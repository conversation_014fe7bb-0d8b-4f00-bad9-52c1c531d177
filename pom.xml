<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>mx.bnext</groupId>
    <artifactId>qms</artifactId>
    <version>*******</version>
    <packaging>war</packaging>
    <!-- context name -->
    <name>app</name>
    <description>Hera Adventurous</description>
    <url>http://bnext.mx/bnext-qms/</url>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <scm>
        <connection>scm:git:http://***********/Bnext/QMS_3.0.0.git</connection>
        <url>http://***********/Bnext/QMS_3.0.0.git</url>
    </scm>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <app.revisionDate>16/05/2025</app.revisionDate>
        <!-- runtime java version -->
        <java.version>22</java.version>
        <maven.build.timestamp.format>dd/MM/yyyy</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <angus-mail.version>2.0.3</angus-mail.version>
        <arial-extension.version>1.0</arial-extension.version>
        <aspectj-maven.version>1.14</aspectj-maven.version>
        <aspectj.version>********</aspectj.version>
        <biweekly.version>0.6.8</biweekly.version>
        <bnext-cipher.version>1.10.0</bnext-cipher.version>
        <bnext-core.version>1.0.3</bnext-core.version>
        <bnext-jdbc.version>1.0.5</bnext-jdbc.version>
        <byte-buddy.version>1.17.1</byte-buddy.version>
        <commons-beanutils.version>1.10.1</commons-beanutils.version>
        <commons-cli.version>1.9.0</commons-cli.version>
        <commons-codec.version>1.18.0</commons-codec.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-digester.version>2.1</commons-digester.version>
        <commons-io.version>2.18.0</commons-io.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-net.version>3.11.1</commons-net.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <commons-text.version>1.13.0</commons-text.version>
        <dojo.version>1.17.3</dojo.version>
        <ehcache.version>3.10.8</ehcache.version>
        <encoder.version>1.3.1</encoder.version>
        <extra-enforcer-rules.version>1.9.0</extra-enforcer-rules.version>
        <frontend-maven-plugin.version>1.15.1</frontend-maven-plugin.version>
        <git-commit-id-maven-plugin.version>9.0.1</git-commit-id-maven-plugin.version>
        <groovy.version>5.0.0-alpha-11</groovy.version>
        <hypersistence-utils.version>3.9.2</hypersistence-utils.version>
        <ignite-ui.version>18.1</ignite-ui.version>
        <jakarta.activation-api.version>2.1.3</jakarta.activation-api.version>
        <jakarta.servlet-api.version>6.1.0</jakarta.servlet-api.version>
        <jakarta.servlet.jsp.jstl-api.version>3.0.2</jakarta.servlet.jsp.jstl-api.version>
        <jakarta.servlet.jsp.jstl.version>3.0.1</jakarta.servlet.jsp.jstl.version>
        <jakarta.transaction-api.version>2.0.1</jakarta.transaction-api.version>
        <jakarta.xml.bind-api.version>4.0.2</jakarta.xml.bind-api.version>
        <jasperreports.version>7.0.1</jasperreports.version>
        <javassist.version>3.30.2-GA</javassist.version>
        <jboss-logging-annotations.version>3.0.3.Final</jboss-logging-annotations.version>
        <jboss-logging.version>3.6.1.Final</jboss-logging.version>
        <jetty-jspc.version>11.0.24</jetty-jspc.version>
        <jfreechart.version>1.5.5</jfreechart.version>
        <jjwt.version>0.12.6</jjwt.version>
        <joda-time.version>2.13.1</joda-time.version>
        <jodconverter-core.version>3.0-beta-4</jodconverter-core.version>
        <jsoup.version>1.18.1</jsoup.version>
        <jsqlparser.version>5.1</jsqlparser.version>
        <licencing-api.version>1.4.67</licencing-api.version>
        <licencing-reader.version>0.0.5</licencing-reader.version>
        <liquibase-slf4j.version>5.1.0</liquibase-slf4j.version>
        <log4j.version>2.24.3</log4j.version>
        <lucene.version>5.5.5</lucene.version>
        <mariadb-java-client.version>3.5.2</mariadb-java-client.version>
        <maven-clean-plugin.version>3.4.1</maven-clean-plugin.version>
        <maven-enforcer-plugin.version>3.5.0</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
        <maven-install-plugin.version>3.1.3</maven-install-plugin.version>
        <maven-replacer-plugin.version>1.4.1</maven-replacer-plugin.version>
        <maven-scm-plugin.version>2.1.0</maven-scm-plugin.version>
        <maven-site-plugin.version>4.0.0-M16</maven-site-plugin.version>
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
        <maven.version>3.6.3</maven.version>
        <mockito.version>5.15.2</mockito.version>
        <modelmapper.version>3.2.2</modelmapper.version>
        <mssql-jdbc.version>12.8.1.jre11</mssql-jdbc.version>
        <net.tascalate.javaflow.api.version>2.7.6</net.tascalate.javaflow.api.version>
        <opencsv.version>5.9</opencsv.version>
        <openpdf.version>1.3.32</openpdf.version>
        <poi.version>5.3.0</poi.version>
        <postgresql.version>42.7.5</postgresql.version>
        <qms-docs.version>3.0.0.2</qms-docs.version>
        <qms-stackedit.version>1.0.1</qms-stackedit.version>
        <snakeyaml.version>2.4</snakeyaml.version>
        <scrimage.version>4.3.0</scrimage.version>
        <spring-ai.version>1.0.0-M5</spring-ai.version>
        <elasticsearch-client.version>8.13.3</elasticsearch-client.version>
        <struts2.version>7.0.0</struts2.version>
        <thumbnailator.version>0.4.20</thumbnailator.version>
        <thymeleaf.version>3.1.3.RELEASE</thymeleaf.version>
        <tomcat.version>10.1.36</tomcat.version>
        <uap-java.version>1.6.1</uap-java.version>
        <waffle.version>3.5.1</waffle.version>
        <app.projectVersion>${project.version}</app.projectVersion>
        <app.buildVersion>${project.version}</app.buildVersion>
        <app.buildNumber>${project.version}</app.buildNumber>
        <app.buildDate>${maven.build.timestamp}</app.buildDate>
        <app.buildDescription>${project.description}</app.buildDescription>
        <app.name>${project.name}</app.name>
        <app.ngappOutputPath>dist</app.ngappOutputPath>
        <aspectj-spring-profile>, aspectj-proxy</aspectj-spring-profile>
        <liquibase-spring-profile/>
        <open-browser-spring-profile/>
        <project.testresult.directory>${project.build.directory}/test-results</project.testresult.directory>
        <junit.itReportFolder>${project.testresult.directory}/integrationTest</junit.itReportFolder>
        <junit.utReportFolder>${project.testresult.directory}/test</junit.utReportFolder>
        <p.excludedGroups>integration</p.excludedGroups>
        <p.Build-Git-Info/>
        <p.Build-MavenSite/>
        <p.Skip-Unit-Test/>
        <p.Skip-Compile/>
        <p.Skip-War/>
        <p.Build-AspectJ-CompileTime/>
        <p.Build-Angular/>
        <p.Run-CheckJavaRules/>
        <p.Build-War/>
        <p.Run-Liquibase/>
        <p.Run-Open-Browser/>
        <p.Run-Integration-Test/>
        <bun.version>v1.2.4</bun.version>
        <node.version>v23.8.0</node.version>
        <npm.version>11.1.0</npm.version>
        <!-- liga del servidor está guardado el caché de NPM y node, es una instancia de nginx -->
        <!-- https://nodejs.org/dist/v20.11.0/win-x64/node.exe-->
        <downloadNodeUrl>http://***********:8182/nodejs/</downloadNodeUrl>
        <!-- https://registry.npmjs.org/npm/-/npm-10.4.0.tgz-->
        <downloadNpmUrl>http://***********:8182/npm/</downloadNpmUrl>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mattbertolini</groupId>
            <artifactId>liquibase-slf4j</artifactId>
            <version>${liquibase-slf4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!-- Permite uso de JSP en spring boot -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-elasticsearch-store-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-ollama-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.victools</groupId>
                    <artifactId>jsonschema-module-jackson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.victools</groupId>
                    <artifactId>jsonschema-generator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.victools</groupId>
                    <artifactId>jsonschema-module-jackson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>${commons-compress.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-tika-document-reader</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.pdfbox</groupId>
                    <artifactId>fontbox</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.pdfbox</groupId>
                    <artifactId>fontbox</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-csv</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk18on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tika</groupId>
                    <artifactId>tika-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.pdfbox</groupId>
                    <artifactId>pdfbox</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- jsoup HTML parser library @ https://jsoup.org/ -->
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${mssql-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>${jfreechart.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>${commons-beanutils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>${commons-cli.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-digester</groupId>
            <artifactId>commons-digester</artifactId>
            <version>${commons-digester.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons-text.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>${jakarta.activation-api.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.transaction</groupId>
            <artifactId>jakarta.transaction-api</artifactId>
            <version>${jakarta.transaction-api.version}</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet.jsp.jstl</groupId>
            <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
            <version>${jakarta.servlet.jsp.jstl-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>jakarta.servlet.jsp.jstl</artifactId>
            <version>${jakarta.servlet.jsp.jstl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-jdbc</artifactId>
            <version>${bnext-jdbc.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-cipher</artifactId>
            <version>${bnext-cipher.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>ignite-ui</artifactId>
            <version>${ignite-ui.version}</version>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>licencing-api</artifactId>
            <version>${licencing-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>licencing-reader</artifactId>
            <version>${licencing-reader.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>mx.bnext</groupId>
                    <artifactId>licencing-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>qms-docs</artifactId>
            <version>${qms-docs.version}</version>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-core</artifactId>
            <version>${bnext-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mx.bnext</groupId>
                    <artifactId>bnext-cipher</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.angus</groupId>
            <artifactId>angus-mail</artifactId>
            <version>${angus-mail.version}</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>${thumbnailator.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.biweekly</groupId>
            <artifactId>biweekly</artifactId>
            <version>${biweekly.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>${ehcache.version}</version>
            <classifier>jakarta</classifier>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>jaxb-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.activation</groupId>
                    <artifactId>jakarta.activation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jfree</groupId>
                    <artifactId>jfreechart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-excel-poi</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-pdf</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-servlets</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-fonts</artifactId>
            <version>${jasperreports.version}</version>
        </dependency>
        <dependency>
            <groupId>net.tascalate.javaflow</groupId>
            <artifactId>net.tascalate.javaflow.api</artifactId>
            <version>${net.tascalate.javaflow.api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>arial-extension</groupId>
            <artifactId>arial-extension</artifactId>
            <version>${arial-extension.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf</artifactId>
            <version>${openpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sksamuel.scrimage</groupId>
            <artifactId>scrimage-core</artifactId>
            <version>${scrimage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.twelvemonkeys.imageio</groupId>
                    <artifactId>imageio-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.twelvemonkeys.imageio</groupId>
                    <artifactId>imageio-jpeg</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.drewnoakes</groupId>
                    <artifactId>metadata-extractor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sksamuel.scrimage</groupId>
            <artifactId>scrimage-webp</artifactId>
            <version>${scrimage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
            <version>${log4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jakarta-web</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-core</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-json-plugin</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-spring-plugin</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>org.artofsolving.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>${jodconverter-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>juh</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>ridl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>unoil</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <!-- Se utiliza en JasperReports -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>${groovy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jcache</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.cache</groupId>
                    <artifactId>cache-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>${lucene.version}</version>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>${hypersistence-utils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>${byte-buddy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
            <version>${jboss-logging.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-annotations</artifactId>
            <version>${jboss-logging-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>org.liquibase.ext</groupId>
            <artifactId>liquibase-modify-column</artifactId>
            <version>${liquibase.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>${opencsv.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>${javassist.version}</version>
        </dependency>
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
            <version>${encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.waffle</groupId>
            <artifactId>waffle-jna-jakarta</artifactId>
            <version>${waffle.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>${mariadb-java-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.waffle</groupId>
                    <artifactId>waffle-jna</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>dojo</artifactId>
            <version>${dojo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet-api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.ua-parser</groupId>
            <artifactId>uap-java</artifactId>
            <version>${uap-java.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>${jakarta.xml.bind-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>qms-stackedit</artifactId>
            <version>${qms-stackedit.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${app.name}</finalName>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>META-INF/context.xml</include>
                    <include>WEB-INF/rewrite.config</include>
                </includes>
            </resource>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/webapp</directory>
                <excludes>
                    <exclude>META-INF/context.xml</exclude>
                    <exclude>WEB-INF/rewrite.config</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <targetPath>webapp</targetPath>
            </resource>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>application.yaml</include>
                </includes>
            </resource>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>application.yaml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin.version}</version>
                <configuration>
                    <fast>true</fast>
                </configuration>
            </plugin>
            <plugin>
                <groupId>cz.habarta.typescript-generator</groupId>
                <artifactId>typescript-generator-maven-plugin</artifactId>
                <version>3.2.1263</version>
                <executions>
                    <execution>
                        <id>generate</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <phase>process-classes</phase>
                    </execution>
                </executions>
                <configuration>
                    <jsonLibrary>jackson2</jsonLibrary>
                    <customTypeMappings>
                        <mapping>java.time.Duration:string</mapping>
                    </customTypeMappings>
                    <classesWithAnnotations>
                        <annotation>qms.util.annotations.TsGenerate</annotation>
                    </classesWithAnnotations>
                    <outputKind>module</outputKind>
                    <outputFile>src/main/ngapp/src/app/qms.types.d.ts</outputFile>
                </configuration>
            </plugin>
            <plugin>
                <!-- https://docs.spring.io/spring-boot/maven-plugin/run.html -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>-Dfile.encoding=${project.build.sourceEncoding}</jvmArguments>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                        <configuration>
                            <additionalProperties>
                                <encoding.source>UTF-8</encoding.source>
                                <encoding.reporting>UTF-8</encoding.reporting>
                                <java.version>${java.version}</java.version>
                            </additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${maven-install-plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-install</id>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <excludedGroups>${p.excludedGroups}</excludedGroups>
                    <trimStackTrace>false</trimStackTrace>
                    <runOrder>alphabetical</runOrder>
                    <reportsDirectory>${junit.utReportFolder}</reportsDirectory>
                    <argLine>-Xmx2048m</argLine>
                    <systemPropertyVariables>
                        <java.awt.headless>true</java.awt.headless>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>${maven-antrun-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <configuration>
                            <target name="copy biome.json">
                                <copy file="src/main/ngapp/biome.json" tofile="biome.json"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>Build-Git-Info</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-Git-Info>,Build-Git-Info</p.Build-Git-Info>
                <!--suppress UnresolvedMavenProperty -->
                <app.buildVersion>@git.commit.id.describe@</app.buildVersion>
                <!--suppress UnresolvedMavenProperty -->
                <app.buildNumber>@git.commit.id.abbrev@</app.buildNumber>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                        <version>${git-commit-id-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>revision</goal>
                                </goals>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <useNativeGit>true</useNativeGit>
                            <offline>true</offline>
                            <abbrevLength>10</abbrevLength>
                            <includeOnlyProperties>
                                <includeOnlyProperty>git.commit.id.describe</includeOnlyProperty>
                                <includeOnlyProperty>git.commit.id.abbrev</includeOnlyProperty>
                            </includeOnlyProperties>
                            <replacementProperties>
                                <replacementProperty>
                                    <property>git.commit.id.describe</property>
                                    <token>^(.*?)(-dirty)?$</token>
                                    <value>$1</value>
                                </replacementProperty>
                            </replacementProperties>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-scm-plugin</artifactId>
                        <version>${maven-scm-plugin.version}</version>
                        <configuration>
                            <connectionType>connection</connectionType>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-MavenSite</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-MavenSite>,Build-MavenSite</p.Build-MavenSite>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-site-plugin</artifactId>
                        <version>${maven-site-plugin.version}</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-Unit-Test</id>
            <properties>
                <p.Skip-Unit-Test>,Skip-Unit-Test</p.Skip-Unit-Test>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-Compile</id>
            <properties>
                <p.Skip-Compile>,Skip-Compile</p.Skip-Compile>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-compile</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <skipMain>true</skipMain>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-War</id>
            <properties>
                <p.Skip-War>,Skip-War</p.Skip-War>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>${maven-war-plugin.version}</version>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>war</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-AspectJ-CompileTime</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-AspectJ-CompileTime>, Build-AspectJ-CompileTime</p.Build-AspectJ-CompileTime>
                <aspectj-spring-profile>, aspectj-compile-time</aspectj-spring-profile>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>dev.aspectj</groupId>
                        <artifactId>aspectj-maven-plugin</artifactId>
                        <version>${aspectj-maven.version}</version>
                        <configuration>
                            <aspectLibraries>
                                <aspectLibrary>
                                    <groupId>org.springframework</groupId>
                                    <artifactId>spring-aspects</artifactId>
                                </aspectLibrary>
                            </aspectLibraries>
                            <ajdtBuildDefFile>src/main/resources/buildaj.properties</ajdtBuildDefFile>
                            <complianceLevel>${java.version}</complianceLevel>
                            <source>${java.version}</source>
                            <target>${java.version}</target>
                            <Xlint>adviceDidNotMatch=error</Xlint>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>org.aspectj</groupId>
                                <artifactId>aspectjtools</artifactId>
                                <version>${aspectj.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-Angular</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-Angular>,Build-Angular</p.Build-Angular>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.code.maven-replacer-plugin</groupId>
                        <artifactId>maven-replacer-plugin</artifactId>
                        <version>${maven-replacer-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>replace-manifest</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>replace</goal>
                                </goals>
                                <configuration>
                                    <file>src/main/ngapp/src/manifest.webmanifest</file>
                                    <regex>true</regex>
                                    <replacements>
                                        <replacement>
                                            <token>"name":.*?".+?"</token>
                                            <value>"name": "Bnext QMS instance for ${app.name}"</value>
                                        </replacement>
                                        <replacement>
                                            <token>"short_name":.*?".+?"</token>
                                            <value>"short_name": "${app.name}"</value>
                                        </replacement>
                                    </replacements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>${frontend-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>default-clean</id>
                                <phase>none</phase>
                            </execution>
                            <execution>
                                <id>install node and npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>Execution [npm install --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>install --progress=false</arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>Execution [npm run biome-lint --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run biome-lint --folderName=${app.name}
                                        --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run biome-check --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run biome-check --folderName=${app.name}
                                        --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run test --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run test-ci --folderName=${app.name}</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run build --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run build --folderName=${app.name} --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <bunVersion>${bun.version}</bunVersion>
                            <nodeVersion>${node.version}</nodeVersion>
                            <npmVersion>${npm.version}</npmVersion>
                            <workingDirectory>${project.basedir}/src/main/ngapp</workingDirectory>
                            <nodeDownloadRoot>${downloadNodeUrl}</nodeDownloadRoot>
                            <npmDownloadRoot>${downloadNpmUrl}</npmDownloadRoot>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-clean-plugin</artifactId>
                        <configuration>
                            <fast>true</fast>
                            <filesets>
                                <fileset>
                                    <directory>src/main/ngapp/node_modules</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/.angular</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/.nx</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/node</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/dist</directory>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Run-CheckJavaRules</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Run-CheckJavaRules>,Run-CheckJavaRules</p.Run-CheckJavaRules>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${maven-enforcer-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>enforce-versions</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>enforce-dependencyConvergence</id>
                                <configuration>
                                    <rules>
                                        <DependencyConvergence/>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>enforce-bytecode-version</id>
                                <configuration>
                                    <rules>
                                        <enforceBytecodeVersion>
                                            <maxJdkVersion>${java.version}</maxJdkVersion>
                                        </enforceBytecodeVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <message>You are running an older version of Maven. Bnext QMS requires at least
                                        Maven ${maven.version}
                                    </message>
                                    <version>[${maven.version},)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <message>
                                        Compilation of Bnext QMS requires JDK ${java.version}.
                                    </message>
                                    <version>[${java.version},)</version>
                                </requireJavaVersion>
                                <bannedDependencies>
                                    <!-- the implementation jakarta.activation:jakarta.activation-api contains the api classes too -->
                                    <excludes>
                                        <exclude>javax.activation:activation</exclude>
                                    </excludes>
                                    <message>the implementation jakarta.activation:jakarta.activation is included and it
                                        contains the API classes too
                                    </message>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>org.codehaus.mojo</groupId>
                                <artifactId>extra-enforcer-rules</artifactId>
                                <version>${extra-enforcer-rules.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <plugin>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-jspc-maven-plugin</artifactId>
                        <version>${jetty-jspc.version}</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.apache.tomcat</groupId>
                                <artifactId>tomcat-jasper</artifactId>
                                <version>${tomcat.version}</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>jspc</id>
                                <goals>
                                    <goal>jspc</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <sourceVersion>${java.version}</sourceVersion>
                            <targetVersion>${java.version}</targetVersion>
                        </configuration>
                    </plugin>
                    <!--
                    <plugin>
                        <groupId>com.diffplug.spotless</groupId>
                        <artifactId>spotless-maven-plugin</artifactId>
                        <version>${spotless.version}</version>
                        <configuration>
                            <java>
                                <googleJavaFormat/>
                            </java>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                    </plugin>
                    -->
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Run-Integration-Test</id>
            <properties>
                <p.Run-Integration-Test>,Run-Integration-Test</p.Run-Integration-Test>
                <p.excludedGroups/>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven-failsafe-plugin.version}</version>
                        <configuration>
                            <runOrder>alphabetical</runOrder>
                            <reportsDirectory>${junit.itReportFolder}</reportsDirectory>
                            <includes>
                                <include>**/*IT*</include>
                                <include>**/*IntTest*</include>
                                <include>**/*CucumberIT*</include>
                            </includes>
                            <excludedGroups>${p.excludedGroups}</excludedGroups>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>-Xmx8192m</argLine>
                            <systemPropertyVariables>
                                <java.awt.headless>true</java.awt.headless>
                            </systemPropertyVariables>
                        </configuration>
                        <executions>
                            <execution>
                                <id>Integration-Test</id>
                                <goals>
                                    <goal>integration-test</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>verify</id>
                                <goals>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-War</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-War>,Build-War</p.Build-War>
            </properties>
            <build>
                <resources>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@infragistics/igniteui-angular/</directory>
                        <targetPath>scss/@infragistics/igniteui-angular</targetPath>
                        <includes>
                            <include>lib/core/styles/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/sass-embedded-win32-x64/dart-sass/</directory>
                        <targetPath>dart-sass</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/sass-embedded-linux-x64/dart-sass/</directory>
                        <targetPath>dart-sass</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/foundation-sites/</directory>
                        <targetPath>scss/foundation-sites</targetPath>
                        <excludes>
                            <exclude>dist/</exclude>
                            <exclude>js/</exclude>
                            <exclude>.github</exclude>
                            <exclude>customizer/</exclude>
                        </excludes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/dist/</directory>
                        <targetPath>${project.build.directory}/classes/static/qms</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@material-design-icons/</directory>
                        <targetPath>scss/material-icons</targetPath>
                        <includes>
                            <include>font/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/igniteui-theming/</directory>
                        <targetPath>scss/igniteui-theming</targetPath>
                        <includes>
                            <include>sass/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/minireset.css/</directory>
                        <targetPath>scss/minireset.css</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@typopro/web-titillium/</directory>
                        <targetPath>scss/@typopro/web-titillium/</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/src</directory>
                        <targetPath>scss</targetPath>
                        <includes>
                            <include>styles.scss</include>
                            <include>materialFonts.scss</include>
                            <include>_foundation-settings.scss</include>
                            <include>foundation.scss</include>
                            <include>componentColors.scss</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/src</directory>
                        <targetPath>scss/src</targetPath>
                        <includes>
                            <include>styles/</include>
                        </includes>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>Run-Liquibase</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Run-Liquibase>,Run-Liquibase</p.Run-Liquibase>
                <liquibase-spring-profile>, liquibase</liquibase-spring-profile>
            </properties>
        </profile>
        <profile>
            <id>Run-Open-Browser</id>
            <properties>
                <p.Run-Open-Browser>,Run-Open-Browser</p.Run-Open-Browser>
                <open-browser-spring-profile>, open-browser</open-browser-spring-profile>
            </properties>
        </profile>
    </profiles>
</project>
