import { Component, type OnD<PERSON>roy, type OnInit } from '@angular/core';
import type { i18n } from 'src/app/core/bnext-core.component';

import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiSelectComponent } from '@/core/multi-select/multi-select.component';
import {
  TransformedFieldsRulesEditorComponent
} from '@/core/transformed-fields-rules-editor/transformed-fields-rules-editor.component';
import { GroupBySectionComponent } from '@/modules/forms/slim-report-base/group-by-section/group-by-section.component';
import { HappyornotPublishTarget } from '@/modules/happyornot/happyornot-target/happyornot-target.enums';
import { AsyncPipe, NgClass } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxButtonGroupComponent,
  IgxCheckboxComponent,
  IgxComboHeaderDirective,
  IgxDividerDirective,
  IgxDragDirective,
  IgxDropDirective,
  IgxForOfDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxListComponent,
  IgxListItemComponent,
  IgxRippleDirective,
  IgxSuffixDirective,
  IgxSwitchComponent
} from '@infragistics/igniteui-angular';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import { SlimReportBaseComponent } from '../slim-report-base/slim-report-base.component';

@Component({
  selector: 'app-slim-report-add',
  templateUrl: '../slim-report-base/slim-report-base.component.html',
  styleUrls: ['../slim-report-base/slim-report-base.component.scss', './slim-report-add.component.scss'],
  imports: [
    NgClass, // <-- Debe ir al inicio
    AsyncPipe,
    BnextTranslatePipe,
    FormsModule,
    GridMultiSelectComponent,
    GroupBySectionComponent,
    IgxBadgeComponent,
    IgxButtonDirective,
    IgxButtonGroupComponent,
    IgxCheckboxComponent,
    IgxComboHeaderDirective,
    IgxDividerDirective,
    IgxDragDirective,
    IgxDropDirective,
    IgxForOfDirective,
    IgxIconComponent,
    IgxInputDirective,
    IgxInputGroupComponent,
    IgxLabelDirective,
    IgxLinearProgressBarComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxRippleDirective,
    IgxSuffixDirective,
    IgxSwitchComponent,
    MultiSelectComponent,
    ReactiveFormsModule,
    TransformedFieldsRulesEditorComponent,
  ]
})
export class SlimReportAddComponent extends SlimReportBaseComponent implements OnInit, OnDestroy, FabButton, i18n {
  /**
   * Utiliza el lenguaje de `SlimReportBaseComponent`
   **/
  override get showGenerateCode() {
    return true;
  }
  override get saveController(): string {
    return `${this.controller}/save`;
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override langReady(): void {
    super.langReady();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }
  protected override loadData(): void {}

  protected readonly EnumPublishTarget = HappyornotPublishTarget;
}
