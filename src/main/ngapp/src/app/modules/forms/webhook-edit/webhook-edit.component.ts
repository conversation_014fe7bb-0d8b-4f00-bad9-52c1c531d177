import { EntityUtil } from '@/core/utils/entity';
import { Component, type OnDestroy, type OnInit, inject } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import type { i18n } from 'src/app/core/bnext-core.component';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { PrintingFormatFieldCode } from '../../printing/printing-format-base/printing-format-base.interfaces';
import { PrintingFormatBaseUtils } from '../../printing/printing-format-base/printing-format-base.utils';
import { WebhookAddComponent } from '../webhook-add/webhook-add.component';
import type { WebhookDTO } from '../webhook-base/webhook-base.interfaces';

@Component({
  selector: 'app-webhook-edit',
  standalone: true,
  imports: [...PrintingFormatBaseUtils.REQUIRED_IMPORTS],
  templateUrl: '../webhook-base/webhook-base.component.html',
  styleUrls: ['../webhook-base/webhook-base.component.scss', './webhook-edit.component.scss']
})
export class WebhookEditComponent extends WebhookAddComponent implements OnInit, OnDestroy, FabButton, i18n {
  sanitizer = inject(DomSanitizer);

  /**
   * Utiliza el lenguaje de `FormPrintingFormatBaseComponent`
   **/
  id: number = null;

  private _htmlId: number = null;

  override get showGenerateCode() {
    return false;
  }
  override get ready(): boolean {
    return typeof this.id === 'number';
  }
  override get htmlId(): number {
    return this._htmlId || -1;
  }

  protected override init(): void {
    this.navLang.getRouteParams(':masterId/:id', ['masterId', 'id'], true, this.activatedRoute).subscribe({
      next: ([masterId, id]) => {
        this.id = +id;
        this.masterId = masterId;
        this.cdr.detectChanges();
        this.setupEditor(); // En edición se renderiza el html del formulario solo si ya se ha asignado ID, por ello, se llama una vez se asinga.
        this.load(+id);
        this.cdr.detectChanges();
      },
      error: () => {
        throw new Error('Not defined  Id');
      }
    });
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override langReady(): void {
    super.langReady();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  override fillLoad(entity: WebhookDTO): PrintingFormatFieldCode {
    this.loading = true;
    this._htmlId = entity.fileId;
    this.stageFieldObjectId = entity.stageFieldObjectId;
    this.stageFieldCode = entity.stageFieldCode;
    const fieldCodes: PrintingFormatFieldCode = {
      fixedFields: EntityUtil.getCodes(entity.fixedFields),
      flexiFields: EntityUtil.getCodes(entity.flexiFields)
    };
    this.mainForm.patchValue(
      {
        code: entity.code,
        description: entity.description,
        details: entity.details,
        stage: entity.stage,
        stageDescription: entity.stageDescription,
        url: entity.url,
        headers: entity.headers,
        masterId: entity.masterId,
        fixedFields: fieldCodes.fixedFields,
        flexiFields: fieldCodes.flexiFields,
        generateCode: false
      },
      { onlySelf: true, emitEvent: false }
    );
    this.updateJson(JSON.parse(entity.json));
    this.loading = false;
    return fieldCodes;
  }

  protected override getEntityData(): WebhookDTO {
    const data = super.getEntityData();
    data.id = this.id;
    return data;
  }
}
