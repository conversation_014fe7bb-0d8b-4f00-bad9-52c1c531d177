import type { FixedFieldType } from '@/modules/printing/printing-format/printing-format.enums';

export type FieldSourceType = 'TRANSFORMED' | 'GHOST' | 'FIXED' | 'FLEXI' | 'SUMMARY_GROUP';
export type TransformedFieldRuleType =
  | 'IS_EMPTY'
  | 'IS_NOT_EMPTY'
  | 'STARTS_WITH'
  | 'CONTAINS'
  | 'ENDS_WITH'
  | 'EQUALS'
  | 'NOT_EQUALS'
  | 'GREATER_THAN'
  | 'LESS_THAN'
  | 'GREATER_THAN_OR_EQUALS'
  | 'LESS_THAN_OR_EQUALS';
export type AggregateFunctionType = 'AVG' | 'SUM' | 'MIN' | 'MAX' | 'STRING_AGG' | 'COUNT';
export type SlimReportValueType = 'string' | 'number' | 'field' | 'date';
export type TransformedFieldRuleValueType = SlimReportValueType | 'same_as_evaluated_field' | 'none';
export type TransformedTypeFinalValueType = 'CUSTOM_TEXT' | 'EVALUATED_FIELD';

export const FieldSource: Record<FieldSourceType, FieldSourceType> = {
  SUMMARY_GROUP: 'SUMMARY_GROUP',
  TRANSFORMED: 'TRANSFORMED',
  GHOST: 'GHOST',
  FIXED: 'FIXED',
  FLEXI: 'FLEXI'
} as const;

export const FieldSourceCodePrefix: Record<FieldSourceType, string> = {
  TRANSFORMED: 't_',
  SUMMARY_GROUP: 'sg_',
  GHOST: 'g_',
  FIXED: 'fi_',
  FLEXI: 'fl_'
} as const;

export const TransformedFieldRuleValueEnabled: Record<TransformedFieldRuleType, TransformedFieldRuleValueType> = {
  CONTAINS: 'string',
  ENDS_WITH: 'string',
  EQUALS: 'same_as_evaluated_field',
  GREATER_THAN: 'number',
  GREATER_THAN_OR_EQUALS: 'number',
  IS_EMPTY: 'none',
  IS_NOT_EMPTY: 'none',
  LESS_THAN: 'number',
  LESS_THAN_OR_EQUALS: 'number',
  NOT_EQUALS: 'same_as_evaluated_field',
  STARTS_WITH: 'string'
} as const;

export const FieldSourceValue: Record<FieldSourceType, number> = {
  TRANSFORMED: 1,
  GHOST: 2,
  FIXED: 3,
  FLEXI: 4,
  SUMMARY_GROUP: 5
} as const;

export const TransformedFieldRuleValue: Record<TransformedFieldRuleType, number> = {
  CONTAINS: 1,
  ENDS_WITH: 2,
  EQUALS: 3,
  GREATER_THAN: 4,
  GREATER_THAN_OR_EQUALS: 5,
  IS_EMPTY: 6,
  IS_NOT_EMPTY: 7,
  LESS_THAN: 8,
  LESS_THAN_OR_EQUALS: 9,
  NOT_EQUALS: 10,
  STARTS_WITH: 11
} as const;

export const TransformedTypeFinalValue: Record<TransformedTypeFinalValueType, number> = {
  CUSTOM_TEXT: 1,
  EVALUATED_FIELD: 2
} as const;


export const FixedFieldCode: Record<FixedFieldType, string> = {
  REQUESTOR: "requestor_name",
  STAGE: "stage_name",
  PROGRESS: "progress_name",
  CODE: "code_name",
  STATUS_PROGRESS: "status_progress_name",
  STATUS: "status_name",
  FILLING_DATE: "filling_date_name",
  BUSINESS_UNIT: "business_unit_name",
  BUSINESS_UNIT_DEPARTMENT: "business_unit_department_name",
  AREA: "area_name",
  LAST_MODIFIED_DATE: "last_modified_date",
  CREATED_DATE: "created_date",
  REQUEST: "request_name",
  OUTSTANDING_SURVEYS: "outstanding_surveys_name",
  AREA_CUSTOM_FIELD1: "area_custom_field1",
  AREA_CUSTOM_FIELD2: "area_custom_field2",
  AREA_CUSTOM_FIELD3: "area_custom_field3",
  AREA_CUSTOM_FIELD4: "area_custom_field4",
  AREA_CUSTOM_FIELD5: "area_custom_field5",
  AREA_CUSTOM_FIELD6: "area_custom_field6",
  AREA_CUSTOM_FIELD7: "area_custom_field7",
  AREA_CUSTOM_FIELD8: "area_custom_field8",
  AREA_CUSTOM_FIELD9: "area_custom_field9",
  AREA_CUSTOM_FIELD10: "area_custom_field10",
  AREA_CUSTOM_FIELD11: "area_custom_field11",
  AREA_CUSTOM_FIELD12: "area_custom_field12",
  AREA_CUSTOM_FIELD13: "area_custom_field13",
  AREA_CUSTOM_FIELD14: "area_custom_field14",
  AREA_CUSTOM_FIELD15: "area_custom_field15",
  AREA_CUSTOM_FIELD16: "area_custom_field16",
  AREA_CUSTOM_FIELD17: "area_custom_field17",
  AREA_CUSTOM_FIELD18: "area_custom_field18",
  AREA_CUSTOM_FIELD19: "area_custom_field19",
  AREA_CUSTOM_FIELD20: "area_custom_field20",
  AREA_CUSTOM_FIELD21: "area_custom_field21",
  AREA_CUSTOM_FIELD22: "area_custom_field22",
  AREA_CUSTOM_FIELD23: "area_custom_field23",
  AREA_CUSTOM_FIELD24: "area_custom_field24",
  AREA_CUSTOM_FIELD25: "area_custom_field25",
  AREA_CUSTOM_FIELD26: "area_custom_field26",
  REGION_DESC: "region_desc",
  REGION_CODE: "region_code"
} as const;
