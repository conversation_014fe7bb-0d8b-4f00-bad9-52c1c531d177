:host ::ng-deep {
  .grid-padding-x.grid-padding-y {
    margin-right: -0.25rem;
    margin-left: -0.25rem;
    margin-top: -0.25rem;
    margin-bottom: -0.25rem;

    > .cell {
      padding-right: 0.25rem;
      padding-left: 0.25rem;
      padding-top: 0.25rem;
      padding-bottom: 0.25rem;
    }
  }
  .is-summary-field-invalid .summary-field-name {
    border: 2px solid red;
  }
}
:host {
  .add-item-container {
    background-color: transparent;
  }
  .summary-field-name {
    font-size: 1rem;
    font-weight: bold;
    width: fit-content;
  }
}
.info-banner {
  background-color: #faebd7;
  padding: 0.5rem;
  margin: 0 0.25rem;
}
