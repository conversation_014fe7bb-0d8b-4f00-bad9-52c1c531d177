import type { ReportColumnRuleDTO, ReportsFieldDTO } from '@/core/grid-report/gird-report.interfaces';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { ConfigApp } from '@/core/local-storage/config-app';
import { hashString, stringToColour } from '@/core/utils/string-util';
import { DEFAULT_RULE_TOKEN } from '@/modules/forms/slim-report-base/slim-report-base.consts';
import {
  FieldSource,
  FieldSourceCodePrefix,
  type FieldSourceType,
  FieldSourceValue,
  type SlimReportValueType,
  TransformedFieldRuleValueEnabled
} from '@/modules/forms/slim-report-base/slim-report-base.enums';
import { getRuleText, getUniqueCode } from '@/modules/forms/slim-report-base/slim-report-base.util';
import { Directive, type OnDestroy, type OnInit, type Signal, computed, inject, signal, viewChild } from '@angular/core';
import { type AbstractControl, type FormArray, FormBuilder, FormControl, type FormGroup, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import type { IChangeCheckboxEventArgs, IComboSelectionChangingEventArgs } from '@infragistics/igniteui-angular';
import { Subject, takeUntil } from 'rxjs';
import { startWith } from 'rxjs/operators';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { RowSelection } from 'src/app/core/grid-base-select/grid-base-select.interfaces';
import type { GridMultiSelectComponent } from 'src/app/core/grid-multi-select/grid-multi-select.component';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { AvatarColumn, TextColumn } from 'src/app/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import type { MultiSelectComponent } from 'src/app/core/multi-select/multi-select.component';
import { AppService } from 'src/app/core/services/app.service';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import type { DialogMessage } from 'src/app/core/services/dialog.service.interfaces';
import { NoticeService } from 'src/app/core/services/notice.service';
import { Deferred } from 'src/app/core/utils/deferred';
import { EntityUtil } from 'src/app/core/utils/entity';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil, stripHtml } from 'src/app/core/utils/form-util';
import type { IMailableUser, IPersistableCodeDescription } from 'src/app/core/utils/interfaces';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { FixedField, type FixedFieldType } from '../../printing/printing-format/printing-format.enums';
import type {
  FormFixedField,
  FormSlimReportDTO,
  FormSlimReportDataSourceDTO,
  ISlimReportOrderColumns,
  ISlimReportsGhostField,
  ISlimReportsTransformedField,
  SurveyDataFieldDTO
} from './slim-report-base.interfaces';

const FIXED_FIELDS_VALUES = Object.keys(FixedField);
type ToFormGroupType<T> = {
  [PropertyKey in keyof T]: AbstractControl<T[PropertyKey]>;
};
@Directive()
export abstract class SlimReportBaseComponent extends BnextCoreComponent implements OnInit, OnDestroy, FabButton, i18n {
  noticeService = inject(NoticeService);
  api = inject(AppService);

  formBuilder = inject(FormBuilder);

  loader = inject(BnextLoaderActivationService);
  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'slim-report-base'
  };

  fixedFieldsData = signal<FormFixedField[]>([]);
  surveyFieldsData = signal([]);
  ghostFieldsValues = signal<ISlimReportsGhostField[]>([]);
  transformedFieldsValues = signal<ISlimReportsTransformedField[]>([]);
  private _wholeAvailableFields: Signal<ReportsFieldDTO[]> = computed(() => {
    return this.refreshAvailableFields();
  }); // <-- Todos los campos disponibles de todas las categorías (fixed, flexi, ghost, etc)
  availableFieldsForTransformation: Signal<ReportsFieldDTO[]> = computed(() => {
    return this._wholeAvailableFields().filter((field) => field.fieldSource !== FieldSource.TRANSFORMED);
  }); // <-- Todos los campos disponibles de todas las categorías excepto TRANSFORMED
  private _fixedFiltersData: FormFixedField[] = [];
  private _surveyFiltersData: SurveyDataFieldDTO[] = [];
  private _fixedFieldsValues: (string | number)[] = []; // <-- Guardar los ids en el ENUM de FixedField
  private _surveyFieldsValues: (string | number)[] = []; // <-- Guardar los nombres de las columnas
  private _fixedFiltersValues: (string | number)[] = []; // <-- Guardar los ids en el ENUM de FixedField
  private _surveyFiltersValues: (string | number)[] = []; // <-- Guardar los nombres de las columnas
  private _transformedFieldValid = false;
  private _ready = false;
  private _surveyFieldsDataStages: string[] = [];
  private _title = null;
  private dragSlimReportSurveyField = '';
  FieldSource = FieldSource;
  $surveyFieldsChangeEvent = new Subject<{
    eventType: 'ADD' | 'REMOVE' | 'EDIT';
    fieldType: FieldSourceType;
    field: ISlimReportsGhostField | ISlimReportsTransformedField | ISlimReportOrderColumns | SurveyDataFieldDTO | FormFixedField;
  }>();

  surveyFieldsDataFilterStage: string = null;

  override get customTagName(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentPath;
  }
  get title(): string {
    return this._title;
  }

  get surveyFieldsDataStages(): string[] {
    return this._surveyFieldsDataStages;
  }
  get showGenerateCode() {
    return false;
  }
  get controller(): string {
    return 'forms/slim-report';
  }
  abstract get saveController(): string;

  get businessUnitAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/business-unit-access/list`;
  }
  get businessUnitDepartmentAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/business-unit-department-access/list`;
  }
  get userAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/user-access/list`;
  }
  get processAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/process-access/list`;
  }
  get fixedFiltersData(): FormFixedField[] {
    return this._fixedFiltersData;
  }
  get surveyFiltersData(): SurveyDataFieldDTO[] {
    return this._surveyFiltersData;
  }
  get isExcelUploadEnabled(): boolean {
    return this.mainForm.controls.excelUploadEnabled.value || false;
  }
  get ready(): boolean {
    // Se sobre escribe en `edit` para validar el `id`
    return this._ready;
  }

  private restrictRecordsByDepartment = 1;
  private validateAccessFormDepartment = 1;

  get surveyFieldControls() {
    return this.mainForm.controls.surveyFields.controls;
  }

  readonly businessUnitAccess = viewChild<GridMultiSelectComponent>('businessUnitAccess');
  readonly businessUnitDepartmentAccess = viewChild<GridMultiSelectComponent>('businessUnitDepartmentAccess');
  readonly userAccess = viewChild<GridMultiSelectComponent>('userAccess');
  readonly processAccess = viewChild<GridMultiSelectComponent>('processAccess');
  readonly surveyFieldsComponent = viewChild<MultiSelectComponent<SurveyDataFieldDTO>>('surveyFields');

  readonly fixedFieldsComponent = viewChild<MultiSelectComponent<FormFixedField>>('fixedFields');

  masterId: string = null;
  showHelp = false;
  busy = true;
  ghostFieldsForm = new UntypedFormGroup({});
  mainForm: FormGroup<{
    code: FormControl<string>;
    description: FormControl<string>;
    details: FormControl<string>;
    fixedFieldsValues: FormControl<(string | number)[]>;
    fixedFiltersValues: FormControl<(string | number)[]>;
    generateCode: FormControl<boolean>;
    surveyFieldsValues: FormControl<(string | number)[]>;
    surveyFiltersValues: FormControl<(string | number)[]>;
    excelUploadEnabled: FormControl<boolean>;
    excelIdFieldValues: FormControl<SurveyDataFieldDTO[]>;
    ghostFields: FormControl<ISlimReportsGhostField[]>;
    restrictRecordsByDepartment: FormControl<boolean>;
    whereFillerUserParticipate: FormControl<boolean>;
    localTimeForDates: FormControl<boolean>;
    surveyFields: FormArray<FormGroup<ToFormGroupType<ISlimReportOrderColumns>>>;
  }>;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo `fabButtons`
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'save',
      text: 'root.common.button.save',
      value: CommonAction.SAVE
    },
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  // SECCIÓN: Permisos
  businessUnitAccessColumns: GridColumn[] = [];
  businessUnitDepartmentAccessColumns: GridColumn[] = [];
  userAccessColumns: GridColumn[] = [];
  processAccessColumns: GridColumn[] = [];

  businessUnitAccessValues: IPersistableCodeDescription[] = [];
  businessUnitDepartmentAccessValues: IPersistableCodeDescription[] = [];
  userAccessValues: IMailableUser[] = [];
  processAccessValues: IPersistableCodeDescription[] = [];

  constructor() {
    super();

    // columnas de permisos
    GridUtil.columns(this.businessUnitAccessColumns, this.businessUnitDepartmentAccessColumns, this.userAccessColumns, this.processAccessColumns)
      .push(
        'code',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .push(
        'description',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .translate(this.$destroy, this.translateService, SlimReportBaseComponent.LANG_CONFIG, 'columns');
    GridUtil.columns(this.userAccessColumns)
      .removeLast()
      .push(
        'account',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .push(
        'description',
        new AvatarColumn({
          autoSize: true,
          fixedFilter: true,
          avatarIdKey: 'id',
          avatarNameKey: 'description',
          showAvatarNameKey: true,
          avatarSecundaryKey: 'correo'
        })
      )
      .translate(this.$destroy, this.translateService, SlimReportBaseComponent.LANG_CONFIG, 'columns');
    this.$surveyFieldsChangeEvent.pipe(takeUntil(this.$destroy)).subscribe((result) => {
      const { eventType, fieldType, field } = result;
      const addFixedOrTransformed = () => {
        const sField = field as ISlimReportsTransformedField;
        this.surveyFieldControls.push(
          this.iSlimReportOrderColumnsToFormGroup(
            {
              id: sField.code,
              description: sField.description,
              alias: '',
              fieldType
            },
            this.surveyFieldControls.length + 1
          )
        );
      };
      const remove = () => {
        let indexFunc: (f: any) => boolean;
        // how to find index to remove
        if (fieldType === 'FIXED') {
          indexFunc = (f) => f.value.id === field.id.toString();
        } else if (fieldType === 'FLEXI') {
          indexFunc = (f) => f.value.id === (field as SurveyDataFieldDTO).name;
        } else if (fieldType === 'TRANSFORMED' || fieldType === 'GHOST') {
          indexFunc = (f) => f.value.id === (field as ISlimReportsGhostField).code;
        }
        // find index
        const idIndex = this.surveyFieldControls.findIndex(indexFunc);
        // do nothing if not found
        if (idIndex === -1) {
          return;
        }
        this.surveyFieldControls.splice(idIndex, 1);
        this.updatePositionAfterRemoveItem();
      };
      const editFixedOrGhost = () => {
        const sField = field as ISlimReportsGhostField;
        // find index for ghost and transformed
        const idx = this.surveyFieldControls.findIndex((f) => f.value.id === sField.code);
        if (idx === -1) {
          console.warn('Field  not found in surveyFieldControls', sField.code);
          return;
        }
        this.surveyFieldControls[idx].patchValue({
          description: sField.description
        });
      };
      const updateMethods = {
        ADD: {
          FIXED: () => {
            const fField = field as FormFixedField;
            this.surveyFieldControls.push(
              this.iSlimReportOrderColumnsToFormGroup({ ...fField, id: fField.id.toString(), fieldType }, this.mainForm.controls.surveyFields.length + 1)
            );
          },
          FLEXI: () => {
            const sField = field as SurveyDataFieldDTO;
            this.surveyFieldControls.push(
              this.iSlimReportOrderColumnsToFormGroup(
                {
                  id: sField.name,
                  description: sField.title,
                  alias: '',
                  fieldType
                },
                this.surveyFieldControls.length + 1
              )
            );
          },
          TRANSFORMED: addFixedOrTransformed,
          GHOST: addFixedOrTransformed
        },
        REMOVE: {
          FIXED: remove,
          FLEXI: remove,
          TRANSFORMED: remove,
          GHOST: remove
        },
        EDIT: {
          FIXED: () => {
            throw new Error(`Unsupported FieldType '${fieldType}' for 'EDIT' event.`);
          },
          FLEXI: () => {
            throw new Error(`Unsupported FieldType '${fieldType}' for 'EDIT' event.`);
          },
          TRANSFORMED: editFixedOrGhost,
          GHOST: editFixedOrGhost
        }
      };
      updateMethods[eventType][fieldType]();
    });
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.navLang
      .getRouteParam(':masterId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([masterId]) => {
        this.activatedRoute.queryParams.subscribe((params) => {
          const { restrictRecordsByDepartment, validateAccessFormDepartment } = params;
          this.restrictRecordsByDepartment = +restrictRecordsByDepartment;
          this.validateAccessFormDepartment = +validateAccessFormDepartment;
          if (Boolean(+validateAccessFormDepartment) || !+restrictRecordsByDepartment) {
            this.mainForm.get('restrictRecordsByDepartment').disable();
          } else {
            this.mainForm.get('restrictRecordsByDepartment').enable();
          }
          this.masterId = masterId;
          // campos del formulario
          this.api.get({ url: `${this.controller}/data-source/${encodeURIComponent(this.masterId)}`, cancelableReq: this.$destroy }).subscribe({
            next: (result: FormSlimReportDataSourceDTO) => {
              if (!result.surveyFields) {
                result.surveyFields = [];
              }
              this.stripFieldsSectionHtml(result.surveyFields);
              this._title = result.title;
              const data = result.surveyFields.sort((a, b) => a.id - b.id);
              for (const f of data) {
                f.title = f.title || this.tag('defaultTitle');
              }
              this.surveyFieldsData.set(data);
              this._surveyFiltersData = data;
              this._surveyFieldsDataStages = Array.from(new Set(result.surveyFields.map((field) => field.fieldStage)));
              // Se cargan campos del ENUM de FixedField's
              this._loadFixedFieldsData(result.areaFields);
              this.busy = false;
              this.cdr.detectChanges();
              this.loadData();
            },
            error: (error) => {
              console.log(error);
              this.busy = false;
            }
          });
        });
      });
  }

  override langReady(): void {
    // form
    this.mainForm = this.formBuilder.group({
      code: new FormControl(),
      description: new FormControl(null, Validators.required),
      details: new FormControl(null, Validators.required),
      fixedFieldsValues: new FormControl(null, Validators.required),
      fixedFiltersValues: new FormControl(),
      generateCode: new FormControl(true),
      excelUploadEnabled: new FormControl(false),
      surveyFieldsValues: new FormControl(null, Validators.required),
      surveyFiltersValues: new FormControl(),
      excelIdFieldValues: new FormControl(null),
      ghostFields: new FormControl(null),
      whereFillerUserParticipate: new FormControl(false),
      localTimeForDates: new FormControl(false),
      restrictRecordsByDepartment: new FormControl(false),
      surveyFields: this.formBuilder.array<FormGroup<ToFormGroupType<ISlimReportOrderColumns>>>([])
    });
    // buttons
    for (const item of this.fabButtons) {
      if (item.text.startsWith('root.')) {
        item.text = this.tag(item.text);
      } else {
        item.text = this.tag(`fixedFields.${item.text}`);
      }
    }
    // Se agregan etiquetas de columnas
    GridUtil.columns(this.businessUnitAccessColumns, this.businessUnitDepartmentAccessColumns, this.userAccessColumns, this.processAccessColumns).translate(
      this.$destroy,
      this.translateService,
      SlimReportBaseComponent.LANG_CONFIG
    );
    this.busy = false;
    this._ready = true;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  private stripFieldsSectionHtml(surveyFields: SurveyDataFieldDTO[]) {
    if (!surveyFields?.length) {
      return;
    }
    const htmlIndex = new Map<string, string>();
    for (const field of surveyFields) {
      const sectionDesc = field.sectionDesc;
      if (sectionDesc === null || sectionDesc === undefined || sectionDesc === '') {
        continue;
      }
      if (htmlIndex.has(sectionDesc)) {
        field.sectionDesc = htmlIndex.get(sectionDesc);
        continue;
      }
      const strippedSectionDesc = stripHtml(sectionDesc);
      htmlIndex.set(sectionDesc, strippedSectionDesc);
      field.sectionDesc = strippedSectionDesc;
    }
  }

  /**
   * Devuelve un ID fijo para cada campo, basado en el nombre del campo y el tipo de sección.
   *
   * Reserva 1024 ids para cara valor de `FieldSourceType`. En caso de que se tengan más de 1024 valores, este metodo no funcionará.
   *
   * @param section
   * @param fieldName
   * @param reserved
   * @private
   */
  private getMadeUpHardcodedId(section: FieldSourceType, fieldName: string | number /* solo los enum tienen número hardcoded */, reserved = 1024): number {
    if (typeof fieldName === 'string') {
      return FieldSourceValue[section] * reserved + hashString(fieldName);
    }
    return FieldSourceValue[section] * reserved + fieldName;
  }

  private getSurveyFieldAnswerType(type: string, answerPartType: number): SlimReportValueType {
    if (type !== 'textField' && type !== 'textFieldArray') {
      return 'string';
    }
    if (!answerPartType) {
      return 'string';
    }
    switch (answerPartType) {
      case 2: // numeric
      case 3: // decimal
      case 5: // currencyConversionUsdToMxn
      case 6: // currency
        return 'number';
      default:
        return 'string';
    }
  }

  refreshAvailableFields(): ReportsFieldDTO[] {
    const uniqueFields = new Map<number, ReportsFieldDTO>();
    // Helper function to add unique fields
    const addUniqueField = (field: ReportsFieldDTO) => {
      if (!uniqueFields.has(field.value)) {
        uniqueFields.set(field.value, field);
      }
    };

    // Fixed fields (campos fijos del enum)
    for (const field of this.fixedFieldsData()) {
      addUniqueField({
        text: field.description,
        value: this.getMadeUpHardcodedId(FieldSource.FIXED, field.id),
        fieldSource: FieldSource.FIXED,
        headerName: this.tag(`fieldHeader.${FieldSource.FIXED}`),
        fieldValueType: 'string',
        fieldCode: String(field.id)
      });
    }

    // Flexi fields (campos configurados en el cuestionario)
    for (const field of this.surveyFieldsData()) {
      addUniqueField({
        text: field.title,
        value: this.getMadeUpHardcodedId(FieldSource.FLEXI, field.name),
        fieldSource: FieldSource.FLEXI,
        headerName: this.tag(`fieldHeader.${FieldSource.FLEXI}`),
        fieldValueType: this.getSurveyFieldAnswerType(field.type, field.answerPartType),
        fieldCode: field.name
      });
    }

    // Ghost fields (campos configurados en la sección de "valores de otros sistemas")
    for (const field of this.ghostFieldsValues()) {
      console.log('ghostField.id', field.code);
      addUniqueField({
        text: field.description,
        value: this.getMadeUpHardcodedId(FieldSource.GHOST, field.name),
        fieldSource: FieldSource.GHOST,
        headerName: this.tag(`fieldHeader.${FieldSource.GHOST}`),
        fieldValueType: 'string',
        fieldCode: field.code
      });
    }

    // Transformed fields (campos configurados en la sección de "valores transformados")
    for (const field of this.transformedFieldsValues()) {
      console.log('transformedField.id', field.code);
      addUniqueField({
        text: field.description,
        value: this.getMadeUpHardcodedId(FieldSource.TRANSFORMED, field.code),
        fieldSource: FieldSource.TRANSFORMED,
        headerName: this.tag(`fieldHeader.${FieldSource.TRANSFORMED}`),
        fieldValueType: 'string',
        fieldCode: field.code
      });
    }
    return Array.from(uniqueFields.values());
  }

  addGhostField(itemName?: string): ISlimReportsGhostField {
    const name = itemName || `ghostFieldTemp${this.ghostFieldsValues().length}`;
    const item: ISlimReportsGhostField = {
      id: -1,
      code: null,
      description: '',
      order: 9999,
      deleted: 0,
      name: name
    };
    this.ghostFieldsValues.update((v) => [...v, item]);
    const control = new UntypedFormControl();
    this.ghostFieldsForm.addControl(name, control);
    this.cdr.detectChanges();
    return item;
  }
  markAsTouched(item: any, controlName: string) {
    const conttrol = this.ghostFieldsForm.controls[controlName];
    conttrol?.setValue(item.value);
    conttrol?.setValidators([Validators.required]);
    conttrol?.markAsTouched();
    conttrol?.updateValueAndValidity();
  }
  filterByStage(newStage?: string): void {
    if (typeof newStage === 'string') {
      this.surveyFieldsDataFilterStage = newStage;
    } else {
      this.surveyFieldsDataFilterStage = null;
    }
  }

  //region <eventos de campos de otros sistemas>
  onChangeGhostDesc(target: any, itemIdx: number) {
    this.onChangeGhostDescAction(target.value, itemIdx);
  }
  onChangeGhostDescAction(value: string, itemIdx: number, isLoad = false) {
    if (!this.ghostFieldsValues()[itemIdx].code) {
      this.ghostFieldsValues.update((values) => [
        ...values.slice(0, itemIdx),
        {
          ...values[itemIdx],
          description: value,
          code: getUniqueCode(value, values, 1, FieldSourceCodePrefix[FieldSourceCodePrefix.GHOST])
        },
        ...values.slice(itemIdx + 1)
      ]);
      this.$surveyFieldsChangeEvent.next({ eventType: 'ADD', fieldType: FieldSource.GHOST, field: this.ghostFieldsValues()[itemIdx] });
    } else {
      this.ghostFieldsValues.update((values) => [
        ...values.slice(0, itemIdx),
        {
          ...values[itemIdx],
          description: value
        },
        ...values.slice(itemIdx + 1)
      ]);
      this.$surveyFieldsChangeEvent.next({ eventType: isLoad ? 'ADD' : 'EDIT', fieldType: FieldSource.GHOST, field: this.ghostFieldsValues()[itemIdx] });
    }
  }
  onExcelUploadEnabledChange(event: any) {
    console.log('Changed switch!', event);
    this.cdr.detectChanges();
  }
  getGhostFormControl(itemName: string): FormControl {
    return <FormControl>this.ghostFieldsForm.get(itemName) || null;
  }
  remove(itemIdx: number, itemName: string): void {
    if (this.ghostFieldsValues().filter((f) => f.deleted === 0).length < 0) {
      return;
    }
    this.$surveyFieldsChangeEvent.next({ eventType: 'REMOVE', fieldType: FieldSource.GHOST, field: this.ghostFieldsValues()[itemIdx] });
    this.ghostFieldsForm.removeControl(itemName);
    this.ghostFieldsValues.update((v) => [...v.slice(0, itemIdx), ...v.slice(itemIdx + 1)]);
  }
  stageColor(stage: string): string {
    return stringToColour(stage);
  }
  //endregion

  //region <eventos de columnas del reporte>
  onChangeSurveyFieldsValues(value: (string | number)[]): void {
    this._surveyFieldsValues = value;
    this.updateFiltersFields(value);
  }
  onChangeFixedFieldsValues(value: (string | number)[]): void {
    this._fixedFieldsValues = value;
    this.updateFixedFields(value);
  }
  onChangeSurveyFiltersValues(value: (string | number)[]): void {
    this._surveyFiltersValues = value;
  }
  onChangeFixedFiltersValues(value: (string | number)[]): void {
    this._fixedFiltersValues = value;
  }
  //endregion

  //region <eventos de permisos>
  onBusinessUnitAccessSelect(args: RowSelection<any[]>): void {
    this.genericOnAccessSelect(args, 'businessUnitAccessValues');
  }
  onBusinessUnitDepartamentAccessSelect(args: RowSelection<any[]>): void {
    this.genericOnAccessSelect(args, 'businessUnitDepartmentAccessValues');
  }
  onProcessAccessSelect(args: RowSelection<any[]>): void {
    this.genericOnAccessSelect(args, 'processAccessValues');
  }
  onUserAccessSelect(args: RowSelection<any[]>): void {
    this.genericOnAccessSelect(args, 'userAccessValues');
  }
  genericOnAccessSelect(
    args: RowSelection<any[]>,
    key: 'userAccessValues' | 'businessUnitAccessValues' | 'processAccessValues' | 'businessUnitDepartmentAccessValues'
  ): void {
    if (!args?.value) {
      return;
    }
    if (Array.isArray(args.value)) {
      this[key] = args.value;
    } else {
      this[key] = [];
    }
  }
  //endregion

  // fab
  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.SAVE:
        this.save();
        break;
      case CommonAction.BACK:
        this.return();
        break;
      default:
        console.warn(`Action is not supported ${item.value}`);
        break;
    }
  }

  protected save(): void {
    const entity: FormSlimReportDTO = this.getEntityData();
    this.refreshTransformedFieldsValidity();
    if (!FormUtil.isValidForms([this.mainForm, this.ghostFieldsForm], null, true).status || !this._transformedFieldValid) {
      this.noticeService.notice(this.tag('requiredNotice'));
      this.cdr.detectChanges();
      return;
    }
    const formReportMaxColumns = ConfigApp.getFormReportMaxColumns();
    if (formReportMaxColumns > 0 && this.surveyFieldControls?.length > formReportMaxColumns) {
      const message = this.tag('maxFormReportColumnsNotice')
        .replace('{maxColumns}', formReportMaxColumns.toString())
        .replace('{currentColumns}', this.surveyFieldControls?.length?.toString() || '0');
      this.dialogService.error(message);
      return;
    }
    // const entity: FormSlimReportDTO = this.getEntityData();
    this.busy = true;
    this.loader.show();
    this.api.post({ url: `${this.saveController}`, cancelableReq: this.$destroy, postBody: entity, handleFailure: false }).subscribe({
      next: (result) => {
        if (this.debug()) {
          console.log('>> save, result: ', result);
        }
        this.busy = false;
        this.loader.hide();
        const dialogConf: DialogMessage = {
          message: this.tag('save-success'),
          rightButton: 'root.common.button.control',
          leftButton: 'root.common.button.create',
          title: 'root.common.error.messageSystem',
          closeOnEscape: false,
          action: new Deferred<DialogResult>()
        };
        this.dialogService.dialogMessage(dialogConf).then(
          () => this.openList(),
          () => this.openCreate()
        );
        this.menuService.refreshLoggedUser();
        this.cdr.detectChanges();
        this.loader.hide();
      },
      error: (error) => {
        this.busy = false;
        this.loader.hide();
        ErrorHandling.notifyError(error, this.navLang);
        this.cdr.detectChanges();
      }
    });
  }

  protected updateFixedFields(fixedFields: (string | number)[]) {
    const fixedFiltersData = [];
    const fixedFiltersNames = [];
    for (const surveyField of this.fixedFieldsData()) {
      if (fixedFields.includes(surveyField.id)) {
        continue;
      }
      fixedFiltersNames.push(surveyField.id);
      fixedFiltersData.push(surveyField);
    }
    const newFixedFiltersValues = [];
    for (const fixedFilterValue of this._fixedFiltersValues) {
      if (!fixedFiltersNames.includes(fixedFilterValue)) {
        continue;
      }
      newFixedFiltersValues.push(fixedFilterValue);
    }
    this._fixedFiltersData = fixedFiltersData;
    this._fixedFiltersValues = newFixedFiltersValues;
    this.cdr.detectChanges();
    this.mainForm.controls.fixedFiltersValues.setValue(this._fixedFiltersValues, { emitEvent: false, onlySelf: true });
  }

  protected updateFiltersFields(surveyFields: (string | number)[]) {
    const surveyFiltersData = [];
    const surveyFiltersNames = [];
    for (const surveyField of this.surveyFieldsData()) {
      if (surveyFields.includes(surveyField.name)) {
        continue;
      }
      surveyFiltersNames.push(surveyField.name);
      surveyFiltersData.push(surveyField);
    }
    const newSurveyFiltersValues = [];
    for (const surveyFilterValue of this._surveyFiltersValues) {
      if (!surveyFiltersNames.includes(surveyFilterValue)) {
        continue;
      }
      newSurveyFiltersValues.push(surveyFilterValue);
    }
    this._surveyFiltersData = surveyFiltersData;
    this._surveyFiltersValues = newSurveyFiltersValues;
    this.cdr.detectChanges();
    this.mainForm.controls.surveyFiltersValues.setValue(this._surveyFiltersValues, {
      emitEvent: false,
      onlySelf: true
    });
  }

  private _loadFixedFieldsData(areaFields: Record<FixedFieldType, string | number> & { fieldsEnabled: boolean; additionalExtraAreaFields: number }): void {
    // campos fijos
    function getAreaIndex(key: string): number {
      return +key.replace('AREA_custom_field', '');
    }
    const _fixedFieldsData = FIXED_FIELDS_VALUES.filter(
      (key) => !key.startsWith('AREA_custom_field') || (areaFields.fieldsEnabled && getAreaIndex(key) <= areaFields.additionalExtraAreaFields + 6)
    ).map((key) => {
      const areaTag = areaFields[key.replace('AREA_', '')];
      return {
        id: FixedField[key],
        description: areaTag || this.tag(`fixedFields.${key}`),
        fixed: true
      };
    });
    this.fixedFieldsData.set(_fixedFieldsData);
    this._fixedFiltersData = _fixedFieldsData;
  }

  protected openCreate(): void {
    const params = `restrictRecordsByDepartment=${this.restrictRecordsByDepartment}&validateAccessFormDepartment=${this.validateAccessFormDepartment}&t=${new Date().getTime()}`;
    const url = `menu/forms/slim-report/${encodeURIComponent(this.masterId)}/add?${params}`;
    this.menuService.navigate(url, Module.FORMULARIE);
  }

  protected openList() {
    this.menuService.navigate(
      `menu/forms/slim-reports/${encodeURIComponent(this.masterId)}` +
        `?restrictRecordsByDepartment=${this.restrictRecordsByDepartment}` +
        `&validateAccessFormDepartment=${this.validateAccessFormDepartment}`,
      Module.FORMULARIE
    );
  }

  protected getEntityData(): FormSlimReportDTO {
    const data = this.mainForm.value;
    const excelIdVaues =
      (
        data.excelIdFieldValues?.reduce((a, b) => {
          return `${a},${b}`;
        }, '') || ','
      ).substring(1) || null;
    const orderColumns = this.mainForm.controls.surveyFields.getRawValue() as ISlimReportOrderColumns[];
    for (const field of this.ghostFieldsValues()) {
      const c = orderColumns.find((f) => f.id === field.code);
      if (c) {
        field.order = c.order;
        field.alias = c.alias;
      } else {
        field.order = null;
        field.alias = null;
      }
    }
    for (const field of this.transformedFieldsValues()) {
      const c = orderColumns.find((f) => f.id === field.code);
      if (c) {
        field.order = c.order;
        field.alias = c.alias;
      } else {
        field.order = null;
        field.alias = null;
      }
    }
    const entity: FormSlimReportDTO = {
      id: -1,
      businessUnitAccessValues: EntityUtil.getIds(this.businessUnitAccess().valueArray || []),
      businessUnitDepartmentAccessValues: EntityUtil.getIds(this.businessUnitDepartmentAccess().valueArray || []),
      code: data.code,
      description: data.description,
      details: data.details,
      documentMasterId: this.masterId,
      excelIdFields: excelIdVaues,
      excelUploadEnabled: data.excelUploadEnabled || false,
      fixedFields: (this._fixedFieldsValues as number[]) || [],
      fixedFilters: (this._fixedFiltersValues as number[]) || [],
      ghostFields: this.ghostFieldsValues() || [],
      transformedFields: this.transformedFieldsValues() || [],
      localTimeForDates: data.localTimeForDates ?? false,
      orderColumns,
      processAccessValues: EntityUtil.getIds(this.processAccess().valueArray || []),
      relatedFields: this.getRelatedFields(),
      restrictRecordsByDepartment: data.restrictRecordsByDepartment || false,
      surveyFields: (this._surveyFieldsValues as string[]) || [],
      surveyFilters: (this._surveyFiltersValues as string[]) || [],
      userAccessValues: EntityUtil.getIds(this.userAccess().valueArray || []),
      whereFillerUserParticipate: data.whereFillerUserParticipate || false
    };
    return entity;
  }

  private getRelatedFields(): string[] {
    if (!this.surveyFieldsData().length || !this._surveyFieldsValues?.length) {
      return [];
    }
    const selectedExternalCatalogs =
      this.surveyFieldsData()
        .filter((field) => this._surveyFieldsValues.includes(field.name))
        .map((field) => field.externalCatalogId)
        .filter((id) => !!id) || [];
    if (!selectedExternalCatalogs?.length) {
      return [];
    }
    return (
      this.surveyFieldsData()
        .filter((field) => selectedExternalCatalogs.indexOf(field.externalCatalogId) !== -1)
        .map((field) => field.name)
        .filter((name) => !!name) || []
    );
  }

  changeGenerateCode(event: IChangeCheckboxEventArgs): void {
    if (event.checked) {
      this.mainForm.controls.code.disable();
    } else {
      this.mainForm.controls.code.enable();
    }
  }

  return(): void {
    this.navLang.back();
  }

  protected loadData(): void {}

  protected ghostCreateHandler(_event) {
    _event.style.visibility = 'hidden';
  }

  protected dragEndHandler(_event: HTMLElement) {
    _event.style.visibility = 'visible';
  }

  protected dragStartHandler(surveyField: string) {
    this.dragSlimReportSurveyField = surveyField;
  }

  protected onEnterHandler(ev): void {
    if (this.dragSlimReportSurveyField === ev.owner.element.nativeElement.id) {
      return;
    }
    const dragIndex = this.surveyFieldControls.findIndex((field) => field.value.id === this.dragSlimReportSurveyField);
    const dropIndex = this.surveyFieldControls.findIndex((field) => field.value.id === ev.owner.element.nativeElement.id);

    this.swapFields(dragIndex, dropIndex);
  }

  private swapFields(dragIndex: number, dropIndex: number) {
    if (dragIndex === -1 || dropIndex === -1 || dragIndex === dropIndex) {
      return;
    }
    const flagDragField = this.surveyFieldControls[dragIndex];
    const flagDropField = this.surveyFieldControls[dropIndex];
    const flagDropFieldOrder = flagDropField.value.order;
    flagDropField.patchValue({ order: flagDragField.value.order });
    this.surveyFieldControls.splice(dragIndex, 1, flagDropField);
    flagDragField.patchValue({ order: flagDropFieldOrder });
    this.surveyFieldControls.splice(dropIndex, 1, flagDragField);
    this.cdr.detectChanges();
  }

  private iSlimReportOrderColumnsToFormGroup(data: Omit<ISlimReportOrderColumns, 'order'>, order: number) {
    return this.formBuilder.group<ToFormGroupType<ISlimReportOrderColumns>>({
      id: new FormControl(data.id),
      fieldType: new FormControl(data.fieldType),
      description: new FormControl(data.description),
      order: new FormControl(order),
      alias: new FormControl(data.alias)
    });
  }

  protected fillSlimReportsColumnsOrder(item: IComboSelectionChangingEventArgs): void {
    for (const value of item.added) {
      if (this.instanceOfFormFixedField(value)) {
        const data: ISlimReportOrderColumns = value as unknown as ISlimReportOrderColumns;
        this.$surveyFieldsChangeEvent.next({ eventType: 'ADD', fieldType: FieldSource.FIXED, field: data });
      } else {
        this.$surveyFieldsChangeEvent.next({ eventType: 'ADD', fieldType: FieldSource.FLEXI, field: value });
      }
    }
    for (const value of item.removed) {
      if (this.instanceOfFormFixedField(value)) {
        this.$surveyFieldsChangeEvent.next({ eventType: 'REMOVE', fieldType: FieldSource.FIXED, field: value });
      } else {
        this.$surveyFieldsChangeEvent.next({ eventType: 'REMOVE', fieldType: FieldSource.FLEXI, field: value });
      }
    }
  }
  private updatePositionAfterRemoveItem(): void {
    for (const item of this.surveyFieldControls) {
      const index = this.surveyFieldControls.indexOf(item);
      item.controls.order.setValue(index + 1);
    }
  }

  private instanceOfFormFixedField(object: any): object is FormFixedField {
    return 'fixed' in object;
  }

  protected fillSlimReportGridOrder(slimReportOrderColumns: ISlimReportOrderColumns[]): void {
    if (slimReportOrderColumns === null || typeof slimReportOrderColumns === 'undefined' || slimReportOrderColumns.length === 0) {
      return;
    }
    const slimReportOrderColumnsFixed: ISlimReportOrderColumns[] = slimReportOrderColumns.filter((f) => f.fieldType === 'FIXED');
    const slimReportOrderColumnsSurvey: ISlimReportOrderColumns[] = slimReportOrderColumns.filter((f) => f.fieldType === 'FLEXI');
    for (const field of slimReportOrderColumnsSurvey) {
      const multiSelectValue = this.surveyFieldsComponent().data.find((f) => f.name === field.description);
      if (multiSelectValue) {
        field.description = multiSelectValue.title;
        field.id = multiSelectValue.name;
      }
    }
    for (const fixed of slimReportOrderColumnsFixed) {
      const multiSelectValue = this.fixedFieldsComponent().data.find((f) => +f.id === +fixed.id);
      if (multiSelectValue) {
        fixed.description = multiSelectValue.description;
      }
    }
    const surveyFieldControls = slimReportOrderColumns.map((slimReportOrderColumn, i) =>
      this.iSlimReportOrderColumnsToFormGroup(slimReportOrderColumn, slimReportOrderColumn.order || i + 1)
    );

    if (slimReportOrderColumns[0].order === null || typeof slimReportOrderColumns[0].order === 'undefined') {
      for (const f of slimReportOrderColumns) {
        const index = slimReportOrderColumns.indexOf(f);
        f.order = index + 1;
      }
    }
    this.surveyFieldControls.push(...surveyFieldControls.sort((a, b) => a.value.order - b.value.order));
  }

  async editAlias(alias: AbstractControl<string>, description: string) {
    const dialogResult = await this.dialogService.input({
      message: `Que alias deseas usar para el campo: ${description}`,
      inputMaxLimit: 50,
      inputValue: alias.value
    });
    alias.setValue(dialogResult.inputValue);
  }
  alias$(surveyField: AbstractControl<unknown>) {
    return surveyField.get('alias').valueChanges.pipe(startWith(surveyField.get('alias').value));
  }

  refreshTransformedFieldsValidity(): void {
    this._transformedFieldValid = true;
    for (const field of this.transformedFieldsValues()) {
      field.valid = field.rules.length > 0;
      for (const rule of field.rules) {
        rule.valid = rule.evaluatedField !== null;
        if (rule.valid && this.isRuleValueNeeded(rule)) {
          rule.valid = this.isValidRuleValue(rule);
        }
        if (rule.valid) {
          rule.valid = !!rule.finalValue;
        }
        field.valid &&= rule.valid;
      }
      this._transformedFieldValid &&= field.valid && !!field.code;
    }
    this.cdr.detectChanges();
  }
  isRuleValueNeeded(rule: ReportColumnRuleDTO): boolean {
    return TransformedFieldRuleValueEnabled[rule.config.type] !== 'none';
  }

  isValidRuleValue(rule: ReportColumnRuleDTO): boolean {
    return getRuleText(rule) !== DEFAULT_RULE_TOKEN;
  }

  addTransformedFields(transformedFields: any[] | null) {
    if (!transformedFields) {
      return;
    }
    this.transformedFieldsValues.update((v) => [...v, ...transformedFields]);
    for (const field of transformedFields) {
      this.$surveyFieldsChangeEvent.next({ eventType: 'ADD', fieldType: FieldSource.TRANSFORMED, field });
    }
  }
}
