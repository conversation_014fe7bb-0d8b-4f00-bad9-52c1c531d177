import type { ReportColumnRuleDTO } from '@/core/grid-report/gird-report.interfaces';
import type { AggregateFunctionType, FieldSourceType } from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { FixedFieldType } from '@/modules/printing/printing-format/printing-format.enums';
import type { ICode, IDescription, IValid, Persistable } from 'src/app/core/utils/interfaces';

export type FieldChangeEventType = 'ADD' | 'REMOVE' | 'EDIT';

export interface FormSlimReportDTO extends Persistable, ICode, IDescription {
  businessUnitAccessValues: number[];
  businessUnitDepartmentAccessValues: number[];
  details: string; // <-- descripción larga
  documentMasterId: string;
  excelIdFields: string;
  excelUploadEnabled: boolean;
  fixedFields: number[]; // <-- Guardar los nombres de las columnas
  fixedFilters: number[]; // <-- Guardar los nombres de las columnas
  ghostFields: ISlimReportsGhostField[];
  localTimeForDates: boolean;
  orderColumns: ISlimReportOrderColumns[];
  processAccessValues: number[];
  profileAccessValues: number[];
  relatedFields: string[];
  restrictRecordsByDepartment: boolean;
  summaryFields: ISlimReportsSummaryGroupField[];
  surveyFields: string[]; // <-- Guardar los ids en el ENUM de FixedField
  surveyFilters: string[]; // <-- Guardar los ids en el ENUM de FixedField
  transformedFields: ISlimReportsTransformedField[];
  userAccessValues: number[];
  whereFillerUserParticipate: boolean;
}
export interface FormFixedField extends Persistable, IDescription, ICode {}
export interface SurveyDataFieldDTO {
  catalogLabel?: string;
  catalogSubType?: string;
  externalCatalogId?: number;
  fieldStage: string;
  sectionDesc: string;
  title: string;
  name: string; // <-- nombre de columna en BD
  answerPartType: number;
  answerType: number;
  type: string;
  id: number; // <-- id de columna en BD
}
export interface FormSlimReportDataSourceDTO {
  areaFields: Record<FixedFieldType, string | number> & {fieldsEnabled: boolean, additionalExtraAreaFields: number};
  title: string;
  surveyId: number;
  surveyFields: SurveyDataFieldDTO[];
}

export interface OrderedCodeField extends ICode {
  alias?: string;
  order: number;
}
export interface ISlimReportOrderColumns {
  id: string;
  description: string;
  alias?: string;
  order: number;
  fieldType: FieldSourceType;
}
export interface ISlimReportsGhostField extends Persistable, ICode, IDescription, OrderedCodeField {
  id: number;
  code: string;
  description: string;
  name?: string;
  deleted: number;
}
export interface ISlimReportsSummaryGroupField extends Persistable, ICode, IDescription, IValid, OrderedCodeField {
  aggregateFunctionForElse: AggregateFunctionType;
  aggregateFunctionForResult: AggregateFunctionType;
  code: string;
  deleted: number;
  description: string;
  groupByColumnName: string;
  groupByFieldName: string;
  groupByColumnNameType?: FieldSourceType;
  groupByFieldNameType?: FieldSourceType;
  groupByColumnNameControlValue?: number;
  groupByFieldNameControlValue?: number;
  groupValueFieldName: string;
  id: number;
  name?: string;
}
export interface ISlimReportsTransformedField extends ISlimReportsGhostField, IValid {
  rules: ReportColumnRuleDTO[];
}
export interface IDeletedResponse {
  deletedQuery: number;
  deletedReport: number;
  deletedSlimReport: number;
}

export interface FieldChangeEvent {
  eventType: FieldChangeEventType;
  fieldType: FieldSourceType;
  field: ISlimReportsGhostField | ISlimReportsTransformedField | ISlimReportsSummaryGroupField | ISlimReportOrderColumns | SurveyDataFieldDTO | FormFixedField;
}
