import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import type { ReportsFieldDTO } from '@/core/grid-report/gird-report.interfaces';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { SelectComponent } from '@/core/select/select.component';
import { FormUtil } from '@/core/utils/form-util';
import { cloneArrayWithModifiedPosition } from '@/core/utils/signal-util';
import type { ITextValue, TextHasValue } from '@/core/utils/text-has-value';
import { SlimReportBaseComponent } from '@/modules/forms/slim-report-base/slim-report-base.component';
import {
  type AggregateFunctionType,
  FieldSourceCodePrefix
} from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { ISlimReportsSummaryGroupField } from '@/modules/forms/slim-report-base/slim-report-base.interfaces';
import { getUniqueCode } from '@/modules/forms/slim-report-base/slim-report-base.util';
import { NgClass } from '@angular/common';
import { Component, type OnDestroy, type OnInit, input, model, output } from '@angular/core';
import {
  type FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import {
  IgxButtonDirective,
  IgxDividerDirective,
  IgxForOfDirective,
  IgxIconComponent,
  IgxListComponent,
  IgxListItemComponent,
  IgxRippleDirective
} from 'igniteui-angular';

@Component({
  selector: 'app-group-by-section',
  templateUrl: './group-by-section.component.html',
  styleUrl: './group-by-section.component.scss',
  imports: [
    NgClass, // <-- Debe ir al inicio
    BnextTranslatePipe,
    FormsModule,
    IgxButtonDirective,
    IgxDividerDirective,
    IgxIconComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxRippleDirective,
    ReactiveFormsModule,
    IgxForOfDirective,
    SelectComponent,
    DropdownSearchComponent
  ]
})
export class GroupBySectionComponent extends BnextCoreComponent implements OnInit, OnDestroy, i18n {

  override get customTagName(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentName;
  }

  override get componentPath(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentPath;
  }

  override langReady() {

  }

  aggregateFunctions: ITextValue<AggregateFunctionType>[] = [
    {text: 'SUM', value: 'SUM'},
    {text: 'AVG', value: 'AVG'},
    {text: 'MAX', value: 'MAX'},
    {text: 'MIN', value: 'MIN'}
  ];
  controlNames:Array<keyof ISlimReportsSummaryGroupField> = [
    'aggregateFunctionForResult', 'groupByColumnName', 'groupByFieldName','groupValueFieldName'
  ] as const;

  onlyNumbersAvailableFields = input<TextHasValue<string>[]>([]);
  surveyAvailableFields = input<ReportsFieldDTO[]>([]);
  surveySelectedFields = input<ReportsFieldDTO[]>([]);
  fieldRemoved = output<ISlimReportsSummaryGroupField>();
  fieldAdded = output<ISlimReportsSummaryGroupField>();
  fieldChanged = output<ISlimReportsSummaryGroupField>();
  summaryFieldValues = model<ISlimReportsSummaryGroupField[]>([]);
  summaryGroupFieldForm = new UntypedFormGroup({});

  fieldId = 0;
  touched = false;

  public addSummaryGroupField(f?: ISlimReportsSummaryGroupField) {
    const name = f?.name || `summaryGroupFieldTemp${this.fieldId++}`;
    const field: ISlimReportsSummaryGroupField = f || {
      id: -1 * (this.fieldId + 1),
      aggregateFunctionForElse: 'STRING_AGG',
      aggregateFunctionForResult: null,
      code: null,
      deleted: 0,
      description: null,
      groupByColumnName: null,
      groupByFieldName: null,
      groupValueFieldName: null,
      name: name,
      order: 9999,
      valid: false
    };
    for (const controlName of this.controlNames) {
      this.addSummaryGroupFieldFormControl(field.id, name, controlName, <string>field[controlName]);
    }
    this.summaryFieldValues.update(v => [...v, field]);
  }

  removeSummaryGroupField(itemIdx: number, itemName: string): void {
    if (this.summaryFieldValues().filter((f) => f.deleted === 0).length < 0) {
      return;
    }
    const id = this.summaryFieldValues()[itemIdx].id;
    this.fieldRemoved.emit(this.summaryFieldValues()[itemIdx]);
    for (const controlName of this.controlNames) {
      this.removeSummaryGroupFieldFormControl(id, itemName, controlName);
    }
    this.summaryFieldValues.update(v => [...v.slice(0, itemIdx), ...v.slice(itemIdx + 1)]);
  }
  private removeSummaryGroupFieldFormControl(id: number, itemName: string, key: keyof ISlimReportsSummaryGroupField): void {
    this.summaryGroupFieldForm.removeControl(`${id}_${itemName}_${key}`);
  }
  public addSummaryGroupFieldFormControl(id: number, itemName: string, key: keyof ISlimReportsSummaryGroupField, value?: string ): FormControl {
    this.summaryGroupFieldForm.addControl(`${id}_${itemName}_${key}`, new UntypedFormControl('', [Validators.required]));
    const fc = <FormControl>this.summaryGroupFieldForm.get(`${id}_${itemName}_${key}`) || null;
    if (fc) {
      fc.setValue(value || null);
    }
    return fc;
  }
  getSummaryGroupFieldFormControl(item: ISlimReportsSummaryGroupField, key: keyof ISlimReportsSummaryGroupField): FormControl {
    let fc = <FormControl>this.summaryGroupFieldForm.get(`${item.id}_${item.name}_${key}`);
    const syncValue = this.controlNames.indexOf(key) !== -1;
    if (syncValue && !fc) {
       fc = this.addSummaryGroupFieldFormControl(item.id, item.name, key);
       // TO-DO: Por performance, refrescar el valor del CONTROL en todos los campos después de ejecutar `removeSummaryGroupField
       fc.setValue(this.getItemValue(item, <keyof ISlimReportsSummaryGroupField>key));
    } else if (syncValue) {
       // TO-DO: Por performance, refrescar el valor del CONTROL en todos los campos después de ejecutar `removeSummaryGroupField`
       fc.setValue(this.getItemValue(item, <keyof ISlimReportsSummaryGroupField>key));
    }
    return fc || null;
  }
  setSummaryGroupFieldFormControlValue(value: string, itemIdx: number, key: keyof ISlimReportsSummaryGroupField): void {
    this.getSummaryGroupFieldFormControl(this.summaryFieldValues()[itemIdx], key)?.setValue(value);
  }

  onChangeAggregateFunctionForResult(value: AggregateFunctionType, itemIdx: number) {
    this.onChangeAction({ aggregateFunctionForResult: value }, itemIdx);
  }
  onChangeGroupByFieldName(item: TextHasValue<any>, itemIdx: number) {
    const field: ReportsFieldDTO = item as ReportsFieldDTO;
    if (!field) {
      this.onChangeAction({ groupByFieldName: null, groupByFieldNameType: null, groupByFieldNameControlValue: null }, itemIdx);
      return;
    }
    this.onChangeAction({
      groupByFieldName: field.fieldCode,
      groupByFieldNameType: field.fieldSource,
      groupByFieldNameControlValue: +item.value
    }, itemIdx);
  }
  onChangeGroupByColumnName(item: TextHasValue<any>, itemIdx: number) {
    const field: ReportsFieldDTO = item as ReportsFieldDTO;
    if (!field || !field.fieldCode) {
      this.onChangeAction({ groupByColumnName: null, groupByColumnNameType: null, groupByColumnNameControlValue: null }, itemIdx);
      return;
    }
    this.onChangeAction({
      groupByColumnName: field.fieldCode,
      groupByColumnNameType: field.fieldSource,
      groupByColumnNameControlValue: +field.value
    }, itemIdx);
  }
  onChangeGroupValueFieldName(item: TextHasValue<string>, itemIdx: number) {
    this.onChangeAction({ groupValueFieldName: item?.value}, itemIdx);
  }
  async onClickChangeDescription(itemIdx: number) {
    const field = this.summaryFieldValues()[itemIdx];
    const dialogResult = await this.dialogService.input({
      message: this.tag('transformedFields.please-capture-field-message'),
      inputMaxLimit: 128,
      inputMinLimit: 5,
      inputValue: field.description || null
    }, 'root.common.button.ok', 'root.common.button.cancel');
    this.onChangeAction({ description: dialogResult.inputValue }, itemIdx, (values) => {
      if (!this.summaryFieldValues()[itemIdx].code) {
        values.code = getUniqueCode(dialogResult.inputValue, this.summaryFieldValues(), 1, FieldSourceCodePrefix[FieldSourceCodePrefix.SUMMARY_GROUP]);
      }
      return values;
    });
  }

  private getItemValue(item: Partial<ISlimReportsSummaryGroupField>, key: keyof ISlimReportsSummaryGroupField): string {
    return item[`${key}ControlValue`] || item[key];
  }

  public onChangeAction(values: Partial<ISlimReportsSummaryGroupField>, itemIdx: number, fn?: (values: Partial<ISlimReportsSummaryGroupField>) => Partial<ISlimReportsSummaryGroupField>) {
    const isNew = !this.summaryFieldValues()[itemIdx].code;
    if (typeof fn === 'function') {
      values = fn(values);
    }
    this.summaryFieldValues.update(a => cloneArrayWithModifiedPosition(a, values, itemIdx));
    Object.keys(values).forEach(key => {
      this.setSummaryGroupFieldFormControlValue(this.getItemValue(values, <keyof ISlimReportsSummaryGroupField>key), itemIdx, <keyof ISlimReportsSummaryGroupField>key);
    })
    this.refreshValidity(itemIdx);
    const field = this.summaryFieldValues()[itemIdx];
    if (field.code) {
      if (isNew) {
        this.fieldRemoved.emit(field);
        this.fieldAdded.emit(field);
      } else {
        this.fieldChanged.emit(field);
      }
    }
  }
  private refreshValidity(itemIdx: number) {
    const field = this.summaryFieldValues()[itemIdx];
    field.valid = !!(
      field.id !== undefined && field.id !== null &&
      field.aggregateFunctionForElse &&
      field.aggregateFunctionForResult &&
      field.code &&
      field.description &&
      field.groupByColumnName &&
      field.groupByFieldName &&
      field.groupValueFieldName &&
      field.name
    );
  }

  public isValid(): boolean {
    this.touched = true;
    return FormUtil.isValid(this.summaryGroupFieldForm, [], true).status && !(
      this.summaryFieldValues().filter(f => !f.valid).length > 0
    );
  }
}
