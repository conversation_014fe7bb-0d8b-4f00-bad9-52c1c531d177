<igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false"
                [indeterminate]="true"></igx-linear-bar>
@if (ready) {
  <div class="grid-container grid-floating-active">
    <div class="grid-x">
      <div class="cell">
        @if (mainForm) {
          <form [formGroup]="mainForm" class="container-form grid-floating-action-buttons">
            @if (!!title) {
              <div class="header grid-container">
                <div class="grid-x">
                  <h3 class="cell igx-card-header__title">{{ title }}</h3>
                </div>
              </div>
            }
            <div class="grid-container">
              <div class="grid-x grid-padding-x">
                <!-- Generales -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.general' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <!-- clave -->
                      <igx-input-group [ngClass]="displayDensityClass" [hidden]="mainForm.value.generateCode"
                                       theme="material">
                        <input #code igxInput id="code" name="code" formControlName="code" type="text"
                               [required]="!mainForm.value.generateCode" maxlength="255" />
                        <label igxLabel for="code">{{ 'i18n.field.code' | translate: this }}</label>
                      </igx-input-group>
                      <igx-input-group [ngClass]="displayDensityClass" [hidden]="!mainForm.value.generateCode"
                                       theme="material">
                        <input #autoCode igxInput id="autoCode" name="autoCode" type="text"
                               [hidden]="!mainForm.value.generateCode" readonly [value]="'SREPORT-####'" />
                        <label igxLabel for="autoCode">{{ 'i18n.field.code' | translate: this }}</label>
                      </igx-input-group>
                      <igx-checkbox
                        [checked]="showGenerateCode"
                        [hidden]="!showGenerateCode"
                        name="generateCode"
                        formControlName="generateCode"
                        (change)="changeGenerateCode($event)"
                      >
                        {{ 'root.common.field.generate-code' | translate: this }}
                      </igx-checkbox>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- titulo -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput id="description" name="description" formControlName="description" type="text"
                               maxlength="255" />
                        <label igxLabel for="description">{{ 'i18n.field.description' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12">
                      <!-- descripción -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput id="details" name="details" formControlName="details" type="text"
                               maxlength="255" />
                        <label igxLabel for="details">{{ 'i18n.field.details' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                  </div>
                </div>
                <!-- columnas -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.columns' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar campos fijos -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #fixedFields
                          [defaultValue]="defaultNumberValue"
                          (change)="onChangeFixedFieldsValues($event)"
                          (selectionChanging)="fillSlimReportsColumnsOrder($event)"
                          displayMainKey="description"
                          formControlName="fixedFieldsValues"
                          name="fixedFieldsValues"
                          valueKey="id"
                          [displayDensity]="displayDensity"
                          [header]="'i18n.fixedFields.header' | translate: this"
                          [help]="'i18n.fixedFields.help' | translate: this"
                          [label]="'i18n.fixedFields.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'i18n.fixedFields.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                          [draggable]="false"
                          [data]="fixedFieldsData()"
                        >
                        </app-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar campos formulario  -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #surveyFields
                          [defaultValue]="defaultStringValue"
                          (change)="onChangeSurveyFieldsValues($event)"
                          (selectionChanging)="fillSlimReportsColumnsOrder($event)"
                          displayMainKey="title"
                          displaySecondaryKey="fieldStage"
                          filterKey="fieldStage"
                          formControlName="surveyFieldsValues"
                          groupKey="sectionDesc"
                          name="surveyFieldsValues"
                          valueKey="name"
                          [filterValue]="surveyFieldsDataFilterStage"
                          [displayDensity]="displayDensity"
                          [header]="'i18n.surveyFields.header' | translate: this"
                          [help]="'i18n.surveyFields.help' | translate: this"
                          [label]="'i18n.surveyFields.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'i18n.surveyFields.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                          [draggable]="false"
                          [data]="surveyFieldsData()"
                        >
                          <div igxComboHeader>
                            <igx-buttongroup class="displayFlex pl-1">
                              <button igxButton="outlined" (click)="filterByStage()">
                                <igx-icon>clear</igx-icon>
                              </button>
                              @for (stage of surveyFieldsDataStages; track stage) {
                                <button igxButton="outlined" [title]="stage" (click)="filterByStage(stage)">
                                  <igx-icon [style.color]="stageColor(stage)">circle</igx-icon>
                                  <span> {{ stage }} </span>
                                </button>
                              }
                            </igx-buttongroup>
                          </div>
                        </app-multi-select>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- orden de columnas -->
                <div class="cell small-12 mb-1 margin-top-3">
                  <div class="slim-reports-grid-order" formArrayName="surveyFields">
                    <!-- Header -->
                    @if (surveyFieldControls.length) {
                      <div class="grid-x slim-reports-grid-order-header">
                        <div class="cell small-1 header-cell">Orden</div>
                        <div class="cell small-4 header-cell">Nombre del Campo</div>
                        <div class="cell small-4 header-cell">Alias</div>
                        <div class="cell small-3 header-cell">Tipo de columna</div>
                      </div>
                    }
                    <!-- Grid Rows -->
                    @for (surveyField of surveyFieldControls; track surveyField.value.id) {
                      <div
                        #dragItem
                        class="item-grid grid-x align-middle"
                        [id]="surveyField.value.id"
                        igxDrag
                        igxDrop
                        draggable="false"
                        (dragStart)="dragStartHandler(surveyField.controls.id.value)"
                        (dragEnd)="dragEndHandler(dragItem)"
                        (enter)="onEnterHandler($event)"
                        (ghostCreate)="ghostCreateHandler(dragItem)"
                        [ghostClass]="'ghost-item'"
                        onmousedown="if (event.preventDefault) event.preventDefault()"
                      >
                        <!-- Order Badge -->
                        <div class="cell small-1 text-center">
                          <igx-badge type="success" [value]="surveyField.value.order" class="badge-dynamic-field"></igx-badge>
                        </div>

                        <!-- Field Name -->
                        <div class="cell small-4 text-ellipsis" [title]="surveyField.value.description">
                          {{ surveyField.value.description }}
                        </div>

                        <!-- Alias with Edit -->
                        <div class="cell small-4 d-flex align-items-center justify-content-between">
                          <span class="text-ellipsis">
                            @if (alias$(surveyField) | async; as alias) {
                              <div class="alias">{{ alias }}</div>
                            } @else {
                              {{ surveyField.value.description }}
                            }
                          </span>
                          <igx-icon
                            class="edit-icon cursor-pointer"
                            (click)="editAlias(surveyField.controls.alias,surveyField.value.description)"
                            (keyup)="editAlias(surveyField.controls.alias,surveyField.value.description)">
                            edit
                          </igx-icon>
                        </div>

                        <!-- Column Type and Drag Handle -->
                        <div class="cell small-2 d-flex align-items-center justify-content-end">
                          <div [className]="'chip chip-' + surveyField.value.fieldType">{{'i18n.fieldHeader.' + surveyField.value.fieldType | translate: this}}</div>
                          <igx-icon class="drag-handle">drag_indicator</igx-icon>
                        </div>
                      </div>
                    }
                  </div>
                </div>
                <!-- campos de calculo grupal -->
                <div class="cell small-12 mb-1 mt-1">
                  <app-group-by-section #summaryFields
                                        (fieldAdded)="refreshSurveyFieldsChangeEvent({ eventType: 'ADD', fieldType: FieldSource.SUMMARY_GROUP, field: $event })"
                                        (fieldRemoved)="refreshSurveyFieldsChangeEvent({ eventType: 'REMOVE', fieldType: FieldSource.SUMMARY_GROUP, field: $event })"
                                        (fieldChanged)="refreshSurveyFieldsChangeEvent({ eventType: 'EDIT', fieldType: FieldSource.SUMMARY_GROUP, field: $event })"
                                        [(summaryFieldValues)]="summaryFieldValues"
                                        [onlyNumbersAvailableFields]="onlyNumbersAvailableFields()"
                                        [surveySelectedFields]="surveySelectedFields()"
                                        [surveyAvailableFields]="surveyAvailableFields()"></app-group-by-section>
                </div>
                <!-- filtros disponibles en el reporte -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.filters' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <h5 class="igx-card-header__subtitle info-banner mb-1 mt-1">
                    {{ 'i18n.filters-help' | translate: this }}
                  </h5>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar los campos fijos para filtrar -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #fixedFilters
                          [defaultValue]="defaultNumberValue"
                          (change)="onChangeFixedFiltersValues($event)"
                          [displayMainKey]="'description'"
                          [formControlName]="'fixedFiltersValues'"
                          [name]="'fixedFiltersValues'"
                          [valueKey]="'id'"
                          [displayDensity]="displayDensity"
                          [header]="'i18n.fixedFilters.header' | translate: this"
                          [help]="'i18n.fixedFilters.help' | translate: this"
                          [label]="'i18n.fixedFilters.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'i18n.fixedFilters.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                          [draggable]="false"
                          [data]="fixedFiltersData"
                        >
                        </app-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar los filtros de campos del formulario  -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #surveyFilters
                          [defaultValue]="defaultStringValue"
                          (change)="onChangeSurveyFiltersValues($event)"
                          [displayMainKey]="'title'"
                          [displaySecondaryKey]="'fieldStage'"
                          [filterKey]="'fieldStage'"
                          [formControlName]="'surveyFiltersValues'"
                          [groupKey]="'sectionDesc'"
                          [name]="'surveyFiltersValues'"
                          [valueKey]="'name'"
                          [filterValue]="surveyFieldsDataFilterStage"
                          [displayDensity]="displayDensity"
                          [header]="'i18n.surveyFilters.header' | translate: this"
                          [help]="'i18n.surveyFilters.help' | translate: this"
                          [label]="'i18n.surveyFilters.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'i18n.surveyFilters.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                          [draggable]="false"
                          [data]="surveyFiltersData"
                        >
                          <div igxComboHeader>
                            <igx-buttongroup class="displayFlex pl-1">
                              <button igxButton="outlined" (click)="filterByStage()">
                                <igx-icon>clear</igx-icon>
                              </button>
                              @for (stage of surveyFieldsDataStages; track stage) {
                                <button igxButton="outlined" [title]="stage" (click)="filterByStage(stage)">
                                  <igx-icon [style.color]="stageColor(stage)">circle</igx-icon>
                                  <span> {{ stage }} </span>
                                </button>
                              }
                            </igx-buttongroup>
                          </div>
                        </app-multi-select>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- valores a otros sistemas -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.other-systems' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x mb-1 mt-1">
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar campos formulario como ids -->
                      <app-multi-select
                        #surveyFields
                        displayMainKey="title"
                        displaySecondaryKey="fieldStage"
                        filterKey="fieldStage"
                        formControlName="excelIdFieldValues"
                        groupKey="sectionDesc"
                        name="excelIdFieldValues"
                        valueKey="name"
                        [filterValue]="surveyFieldsDataFilterStage"
                        [displayDensity]="displayDensity"
                        [header]="'i18n.surveyFields.header' | translate: this"
                        [help]="'i18n.surveyFields.help' | translate: this"
                        [label]="'i18n.field.excelIdFields' | translate: this"
                        [required]="false"
                        [searchPlaceholder]="'i18n.surveyFields.placeholder' | translate: this"
                        [showHelp]="showHelp"
                        [translateGroupKey]="false"
                        [draggable]="false"
                        [data]="surveyFieldsData()"
                        [class.displayNone]="!isExcelUploadEnabled"
                      >
                        <div igxComboHeader>
                          <igx-buttongroup class="displayFlex pl-1">
                            <button igxButton="outlined" (click)="filterByStage()">
                              <igx-icon>clear</igx-icon>
                            </button>
                            @for (stage of surveyFieldsDataStages; track stage) {
                              <button igxButton="outlined" [title]="stage" (click)="filterByStage(stage)">
                                <igx-icon [style.color]="stageColor(stage)">circle</igx-icon>
                                <span> {{ stage }} </span>
                              </button>
                            }
                          </igx-buttongroup>
                        </div>
                      </app-multi-select>
                      <igx-switch
                        formControlName="excelUploadEnabled"
                        [checked]="false"
                        (change)="onExcelUploadEnabledChange($event)"
                      >
                        {{ 'i18n.field.excelUploadEnabled' | translate: this }}
                      </igx-switch>
                    </div>
                    <div class="cell small-12" [class.displayNone]="!isExcelUploadEnabled">
                      <igx-list class="ng-for add-items">
                        <igx-list-item [isHeader]="true" class="add-item-container static-item">
                          <button [igxButton]="'outlined'" class="round" (click)="addGhostField()">
                            <igx-icon family="material">add</igx-icon>
                            <label> {{ 'i18n.field.ghost-field' | translate: this }} </label>
                          </button>
                        </igx-list-item>
                        <igx-list-item
                          #igxListItem
                          *igxFor="let item of ghostFieldsValues(); let rowIndex = index"
                        >
                          @let control = getGhostFormControl(item.name);
                          @if (control) {
                            <div class="grid-x cell grid-padding-x">
                              <div class="cell small-7 margin-fix">
                                <igx-input-group type="border" class="igx-input-group--border" theme="material">
                                  <igx-suffix
                                    (click)="remove(rowIndex, item.name);"
                                  >
                                    <igx-icon>delete</igx-icon>
                                  </igx-suffix>
                                  <input
                                    igxInput
                                    [formControl]="control"
                                    [required]="true"
                                    [placeholder]="'i18n.field.ghost-field' | translate: this"
                                    [attr.title]="'i18n.field.ghost-field' | translate: this"
                                    name="{{ 'ghost-field-name-' + rowIndex }}"
                                    [value]="item.description"
                                    (change)="onChangeGhostDesc($event.target, rowIndex)"
                                    (keyup)="markAsTouched($event.target, item.name)"
                                  />
                                  <label
                                    igxLabel> {{ 'i18n.field.description' | translate: this }} {{ rowIndex + 1 }} </label>
                                </igx-input-group>
                              </div>
                              <div class="cell small-5 margin-fix">
                                <igx-input-group type="border" class="igx-input-group--border" theme="material">
                                  <input
                                    igxInput
                                    [value]="item.code"
                                    readonly
                                    name="{{ 'target-name-' + rowIndex }}"
                                  />
                                  <label igxLabel
                                         for="{{ 'target-name-' + rowIndex }}"> {{ 'i18n.field.code' | translate: this }} {{ rowIndex + 1 }} </label>
                                </igx-input-group>
                              </div>
                            </div>
                          }
                        </igx-list-item>
                      </igx-list>
                    </div>
                  </div>
                </div>
                <!-- valores de campos transformados -->
                <app-transformed-fields-rules-editor
                  (fieldAdded)="refreshSurveyFieldsChangeEvent({ eventType: 'ADD', fieldType: FieldSource.TRANSFORMED, field: $event })"
                  (fieldRemoved)="refreshSurveyFieldsChangeEvent({ eventType: 'REMOVE', fieldType: FieldSource.TRANSFORMED, field: $event })"
                  (fieldChanged)="refreshSurveyFieldsChangeEvent({ eventType: 'EDIT', fieldType: FieldSource.TRANSFORMED, field: $event })"
                  [(transformedFieldsValues)]="transformedFieldsValues"
                  [availableFieldsForTransformation]="availableFieldsForTransformation()"></app-transformed-fields-rules-editor>
                <!-- configuraciones -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.settings' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x mb-1 mt-1">
                    <div class="cell small-6">
                      <igx-checkbox #restrictRecordsByDepartment formControlName="restrictRecordsByDepartment" [checked]="false">
                        {{ 'i18n.restrictRecordsByDepartment' | translate: this }}
                      </igx-checkbox>
                      <igx-icon class="ml-1 cursor-pointer" *ngIf="restrictRecordsByDepartment.disabled"
                                (click)="showRestrictRecordsByDepartmentHelp()"
                                (keyup)="showRestrictRecordsByDepartmentHelp()">help</igx-icon>

                    </div>
                    <div class="cell small-6">
                      <igx-checkbox formControlName="whereFillerUserParticipate" [checked]="false">
                        {{ 'i18n.whereFillerUserParticipate' | translate: this }}
                      </igx-checkbox>
                    </div>
                    <div class="cell small-6">
                      <igx-checkbox formControlName="localTimeForDates" [checked]="false">
                        {{ 'i18n.localTimeForDates' | translate: this }}
                      </igx-checkbox>
                    </div>
                  </div>
                </div>
                <!-- Permisos -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.access' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x mb-1 mt-1">
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar plantas -->
                      <div class="grid-multi-select-container">
                        <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple
                                (click)="businessUnitAccess.openDialog()">
                          <igx-icon family="material">add</igx-icon>
                          <label>{{ 'root.common.field.businessUnit' | translate: this }}</label>
                        </button>
                        <app-grid-multi-select
                          #businessUnitAccess
                          (rowSelectionChange)="onBusinessUnitAccessSelect($event)"
                          id="businessUnitAccess"
                          name="businessUnitAccess"
                          [active]="true"
                          [allowDataGrouping]="false"
                          [allowValueGrouping]="false"
                          [collapsable]="true"
                          [collapsedGrid]="false"
                          [dataColumns]="businessUnitAccessColumns"
                          [dataUrl]="businessUnitAccessController"
                          [hasDelete]="true"
                          [hintLabel]="'i18n.businessUnitAccessHelp' | translate: this"
                          [inputType]="4"
                          [dialogTitle]="'i18n.businessUnitAccess' | translate: this"
                          [label]="'' + businessUnitAccess?.getValueGridCount() + ' ' + ('i18n.businessUnitAccess' | translate: this)"
                          [openDialogButtonLabel]="'root.common.field.businessUnit' | translate: this"
                          [primaryKey]="'id'"
                          [valueColumns]="businessUnitAccessColumns"
                        >
                        </app-grid-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar departamentos -->
                      <div class="grid-multi-select-container">
                        <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple
                                (click)="businessUnitDepartmentAccess.openDialog()">
                          <igx-icon family="material">add</igx-icon>
                          <label>{{ 'root.common.field.businessUnitDepartment' | translate: this }}</label>
                        </button>
                        <app-grid-multi-select
                          #businessUnitDepartmentAccess
                          (rowSelectionChange)="onBusinessUnitDepartamentAccessSelect($event)"
                          id="businessUnitDepartmentAccess"
                          name="businessUnitDepartmentAccess"
                          [active]="true"
                          [allowDataGrouping]="false"
                          [allowValueGrouping]="false"
                          [collapsable]="true"
                          [collapsedGrid]="false"
                          [dataColumns]="businessUnitDepartmentAccessColumns"
                          [dataUrl]="businessUnitDepartmentAccessController"
                          [hasDelete]="true"
                          [hintLabel]="'i18n.businessUnitDepartmentAccessHelp' | translate: this"
                          [inputType]="4"
                          [dialogTitle]="'i18n.businessUnitDepartmentAccess' | translate: this"
                          [label]="'' + businessUnitDepartmentAccess?.getValueGridCount() + ' ' + ('i18n.businessUnitDepartmentAccess' | translate: this)"
                          [openDialogButtonLabel]="'root.common.field.businessUnit' | translate: this"
                          [primaryKey]="'id'"
                          [valueColumns]="businessUnitDepartmentAccessColumns"
                        >
                        </app-grid-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar usuarios -->
                      <div class="grid-multi-select-container">
                        <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple
                                (click)="userAccess.openDialog()">
                          <igx-icon family="material">add</igx-icon>
                          <label>{{ 'root.common.field.user' | translate: this }}</label>
                        </button>
                        <app-grid-multi-select
                          #userAccess
                          (rowSelectionChange)="onUserAccessSelect($event)"
                          id="userAccess"
                          name="userAccess"
                          [active]="true"
                          [allowDataGrouping]="false"
                          [allowValueGrouping]="false"
                          [collapsable]="true"
                          [collapsedGrid]="false"
                          [dataColumns]="userAccessColumns"
                          [dataUrl]="userAccessController"
                          [hasDelete]="true"
                          [hintLabel]="'i18n.userAccessHelp' | translate: this"
                          [inputType]="4"
                          [dialogTitle]="'i18n.userAccess' | translate: this"
                          [label]="'' + userAccess?.getValueGridCount() + ' ' + ('i18n.userAccess' | translate: this)"
                          [openDialogButtonLabel]="'root.common.field.businessUnit' | translate: this"
                          [primaryKey]="'id'"
                          [valueColumns]="userAccessColumns"
                        >
                        </app-grid-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar procesos -->
                      <div class="grid-multi-select-container">
                        <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple
                                (click)="processAccess.openDialog()">
                          <igx-icon family="material">add</igx-icon>
                          <label>{{ 'root.common.field.process' | translate: this }}</label>
                        </button>
                        <app-grid-multi-select
                          #processAccess
                          (rowSelectionChange)="onProcessAccessSelect($event)"
                          id="processAccess"
                          name="processAccess"
                          [active]="true"
                          [allowDataGrouping]="false"
                          [allowValueGrouping]="false"
                          [collapsable]="true"
                          [collapsedGrid]="false"
                          [dataColumns]="processAccessColumns"
                          [dataUrl]="processAccessController"
                          [hasDelete]="true"
                          [hintLabel]="'i18n.processAccessHelp' | translate: this"
                          [inputType]="4"
                          [dialogTitle]="'i18n.processAccess' | translate: this"
                          [label]="'' + processAccess?.getValueGridCount() + ' ' + ('i18n.processAccess' | translate: this)"
                          [openDialogButtonLabel]="'root.common.field.businessUnit' | translate: this"
                          [primaryKey]="'id'"
                          [valueColumns]="processAccessColumns"
                        >
                        </app-grid-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12">
                      <!-- seleccionar procesos -->
                      <div class="grid-multi-select-container">
                        <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple
                                (click)="profileAccess.openDialog()">
                          <igx-icon family="material">add</igx-icon>
                          <label>{{ 'root.common.field.profile' | translate: this }}</label>
                        </button>
                        <app-grid-multi-select
                          #profileAccess
                          (rowSelectionChange)="onProfileAccessSelect($event)"
                          id="profileAccess"
                          name="profileAccess"
                          [active]="true"
                          [allowDataGrouping]="false"
                          [allowValueGrouping]="false"
                          [collapsable]="true"
                          [collapsedGrid]="false"
                          [dataColumns]="profileAccessColumns"
                          [dataUrl]="profileAccessController"
                          [hasDelete]="true"
                          [hintLabel]="'i18n.profileAccessHelp' | translate: this"
                          [inputType]="4"
                          [dialogTitle]="'i18n.profileAccess' | translate: this"
                          [label]="'' + profileAccess?.getValueGridCount() + ' ' + ('i18n.profileAccess' | translate: this)"
                          [openDialogButtonLabel]="'root.common.field.profile' | translate: this"
                          [primaryKey]="'id'"
                          [valueColumns]="profileAccessColumns"
                        >
                        </app-grid-multi-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        }
      </div>
    </div>
  </div>
}
