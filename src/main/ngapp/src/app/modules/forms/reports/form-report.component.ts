import { BnextCoreComponent } from '@/core/bnext-core.component';
import { GridReportComponent } from '@/core/grid-report/grid-report.component';
import { type AfterViewInit, Component, type OnDestroy, inject, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import type { RowType } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { ActionStrip } from 'src/app/core/grid/utils/grid.interfaces';
import type { DialogService } from 'src/app/core/services/dialog.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';

@Component({
  selector: 'app-form-report',
  templateUrl: './form-report.component.html',
  styleUrls: ['./form-report.component.scss'],
  imports: [GridReportComponent, FormsModule, ReactiveFormsModule]
})
export class FormReportComponent extends BnextCoreComponent implements AfterViewInit, OnDestroy {
  private activatedRoute = inject(ActivatedRoute);
  protected baseUrl = 'forms';
  protected primaryKey = 'request_id';
  protected actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.menuAction(row?.data) : []),
      rowIdentifierKey: this.primaryKey
    }
  };
  private defaultMenuActionOptions: DropdownMenuItem[] = [
    {
      text: 'Previsualizar',
      value: CommonAction.OPEN_DETAIL,
      iconName: 'preview'
    },
    {
      text: 'Vistas de impresión',
      value: CommonAction.PRINTING_OPTIONS,
      iconName: 'print'
    }
  ];

  readonly gridReport = viewChild<GridReportComponent>('gridReport');

  private _optionsReady = false;
  private _masterId: string = null;

  protected get moduleName(): string {
    return 'formularie';
  }

  protected get masterId(): string {
    return this._masterId;
  }

  protected get dialogService(): DialogService {
    return this.navLang?.dialogService;
  }

  public ngAfterViewInit() {
    super.ngAfterViewInit();
    this.navLang
      .getRouteParams(':id/:masterId', ['id', 'masterId'], true, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([id, masterId]) => {
        if (masterId) {
          this._masterId = masterId;
          const gridReport = this.gridReport();
          gridReport.masterId = this._masterId;
          gridReport.loadReport(+id, `${this.baseUrl}/reportConfig/${id}/${encodeURIComponent(masterId)}`);
        } else {
          this.gridReport().loadReport(+id, `${this.baseUrl}/reportConfig/${id}`);
        }
      });
  }

  public ngOnDestroy(): void {
    super.ngOnDestroy();
    this.$destroy.next(null);
    this.$destroy.complete();
  }

  protected actionStripOptions(options: CommonAction[]): void {
    this.defaultMenuActionOptions = this.defaultMenuActionOptions.filter((item) => options.includes(item.value as CommonAction));
    this._optionsReady = true;
  }

  private menuAction(row: any): DropdownMenuItem[] {
    if (!this._optionsReady) {
      return [];
    }
    if (row.archived) {
      return [this.defaultMenuActionOptions[0]].map((item) => {
        if (item.value === CommonAction.OPEN_DETAIL) {
          item.modernHref = `legacy/${this.openDetailUrl(row[this.primaryKey], row.outstanding_surveys_id)}`;
        }
        return item;
      });
    }
    return [...this.defaultMenuActionOptions].map((item) => {
      if (item.value === CommonAction.OPEN_DETAIL) {
        item.modernHref = `legacy/${this.openDetailUrl(row[this.primaryKey], row.outstanding_surveys_id)}`;
      }
      return item;
    });
  }

  protected toogleMenu(drop: GridDropDownItem) {
    const requestId = +drop.row[this.primaryKey];
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigateLegacy(this.openDetailUrl(requestId, +drop.row.outstanding_surveys_id), Module.FORMULARIE);
        break;
      case CommonAction.PRINTING_OPTIONS:
        this.loader.show();
        this.menuService.printReportTemplate({ masterId: this.masterId, moduleName: this.moduleName, requestId: requestId });
        break;
    }
  }

  private openDetailUrl(requestId: number, outstandingSurveysId: number) {
    if (requestId) {
      return `v.request.survey.preview.view?task=preview&requestId=${requestId}`;
    }
    return `v.request.survey.preview.view?task=preview&id=O${outstandingSurveysId}`;
  }
}
