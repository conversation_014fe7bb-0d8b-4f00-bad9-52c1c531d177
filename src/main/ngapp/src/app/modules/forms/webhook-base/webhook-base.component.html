<igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
@if (ready) {
  <div class="grid-container grid-floating-active">
    <div class="grid-x">
      <div class="cell">
        @if (mainForm) {
          <form [formGroup]="mainForm" class="container-form grid-floating-action-buttons">
            @if (!!title) {
              <div class="header grid-container">
                <div class="grid-x">
                  <h3 class="cell igx-card-header__title">{{ 'i18n.title' | translate: this }}: {{ title }}</h3>
                </div>
              </div>
            }
            <div class="grid-container">
              <div class="grid-x grid-padding-x">
                <!-- Generales -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.general' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <!-- clave -->
                      <igx-input-group [ngClass]="displayDensityClass" [hidden]="mainForm.value.generateCode" theme="material">
                        <input #code igxInput name="code" formControlName="code" type="text" [required]="!mainForm.value.generateCode" maxlength="255" />
                        <label igxLabel for="code">{{ 'fields.code' | translate: this }}</label>
                      </igx-input-group>
                      <igx-input-group [ngClass]="displayDensityClass" [hidden]="!mainForm.value.generateCode" theme="material">
                        <input #autoCode igxInput name="autoCode" type="text" [hidden]="!mainForm.value.generateCode" readonly [value]="'WEBHOOK-####'" />
                        <label igxLabel for="autoCode">{{ 'fields.code' | translate: this }}</label>
                      </igx-input-group>
                      <igx-checkbox
                        [checked]="showGenerateCode"
                        [hidden]="!showGenerateCode"
                        name="generateCode"
                        formControlName="generateCode"
                        (change)="changeGenerateCode($event)"
                      >
                        {{ 'root.common.field.generate-code' | translate: this }}
                      </igx-checkbox>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- titulo -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="description" formControlName="description" type="text" maxlength="255" />
                        <label igxLabel for="description">{{ 'fields.description' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12">
                      <!-- descripción -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="details" formControlName="details" type="text" maxlength="255" />
                        <label igxLabel for="details">{{ 'fields.details' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- etapa -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="stage" formControlName="stage" type="text" maxlength="255" [readonly]="true"/>
                        <label igxLabel for="stage">{{ 'fields.stage' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- sección -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="stageDescription" formControlName="stageDescription" type="text" maxlength="50" [readonly]="true"/>
                        <label igxLabel for="stageDescription">{{ 'fields.stageDescription' | translate: this }}</label>
                    </igx-input-group>
                    </div>
                    <div class="cell small-12">
                      <!-- masterId -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="masterId" formControlName="masterId" type="text" maxlength="255" [readonly]="true"/>
                        <label igxLabel for="masterId">{{ 'fields.masterId' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12">
                      <!-- url -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <input igxInput name="url" formControlName="url" type="text" maxlength="255" />
                        <label igxLabel for="url">{{ 'fields.url' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                    <div class="cell small-12">
                      <!-- headers -->
                      <igx-input-group theme="material" [ngClass]="displayDensityClass">
                        <textarea igxInput name="headers" formControlName="headers" type="text" maxlength="255"></textarea>
                        <label igxLabel for="headers">{{ 'fields.headers' | translate: this }}</label>
                      </igx-input-group>
                    </div>
                  </div>
                </div>
                <!-- columnas -->
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.variableFields' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar campos fijos -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #fixedFields
                          (change)="onChangeFixedFieldsValues($event)"
                          displayMainKey="description"
                          formControlName="fixedFields"
                          name="fixedFields"
                          [valueKey]="fieldsKey"
                          [data]="fixedFieldsData"
                          [displayDensity]="displayDensity"
                          [header]="'fixedFieldsLabels.header' | translate: this"
                          [help]="'fixedFieldsLabels.help' | translate: this"
                          [label]="'fixedFieldsLabels.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'fixedFieldsLabels.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                        >
                        </app-multi-select>
                      </div>
                    </div>
                    <div class="cell small-12 medium-6">
                      <!-- seleccionar campos formulario  -->
                      <div class="grid-multi-select-container">
                        <app-multi-select
                          #flexiFields
                          (change)="onChangeSurveyFieldsValues($event)"
                          displayMainKey="description"
                          displaySecondaryKey="fieldStage"
                          filterKey="fieldStage"
                          formControlName="flexiFields"
                          groupKey="sectionDesc"
                          name="flexiFields"
                          [valueKey]="fieldsKey"
                          [data]="flexiFieldsData"
                          [displayDensity]="displayDensity"
                          [filterValue]="flexiFieldsDataFilterStage"
                          [header]="'flexiFieldsLabels.header' | translate: this"
                          [help]="'flexiFieldsLabels.help' | translate: this"
                          [label]="'flexiFieldsLabels.label' | translate: this"
                          [required]="false"
                          [searchPlaceholder]="'flexiFieldsLabels.placeholder' | translate: this"
                          [showHelp]="showHelp"
                          [translateGroupKey]="false"
                        >
                          <div igxComboHeader>
                            <igx-buttongroup class="displayFlex pl-1">
                              <button igxButton (click)="filterByStage()">
                                <igx-icon>clear</igx-icon>
                              </button>
                              @for (stage of flexiFieldsDataStages; track stage) {
                                <button igxButton [title]="stage" (click)="filterByStage(stage)">
                                  <igx-icon [style.color]="stageColor(stage)">circle</igx-icon>
                                  <span> {{ stage }} </span>
                                </button>
                              }
                            </igx-buttongroup>
                          </div>
                        </app-multi-select>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="cell small-12 mb-1 mt-1">
                  <h3 class="igx-card-header__title">{{ 'i18n.json' | translate: this }}</h3>
                  <igx-divider></igx-divider>
                  <div class="grid-x grid-padding-x">
                    <div class="cell small-12 medium-6">
                      <div #jsoneditor></div>
                    </div>
                    <div class="cell small-12 medium-6 fields-container">
                      <igx-input-group [ngClass]="displayDensityClass" class="cell small-12 input-textarea" theme="material">
                        <textarea
                          #inputTextarea
                          [required]="true"
                          igxInput
                          class="size"
                          name="json"
                          [readOnly]="true"
                          [value]="textAreaValue"
                        ></textarea>
                      </igx-input-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        }
      </div>
    </div>
  </div>
}
