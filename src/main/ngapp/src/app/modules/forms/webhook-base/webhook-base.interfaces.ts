import type { IPersistableCodeDescription, Persistable } from '@/core/utils/interfaces';
import type { DataSourceDTO, FixedFieldDTO, FlexiFieldDTO } from '@/modules/printing/printing-format-base/printing-format-base.interfaces';

export interface WebhookDTO extends IPersistableCodeDescription {
  details: string; // <-- descripción larga
  masterId: string;
  json: string;
  fileId: number;
  stage: string;
  stageDescription: string;
  stageFieldObjectId: number;
  stageFieldCode: string;
  url: string;
  headers: string;
  fixedFields: FixedFieldDTO[]; // <-- Guardar los ids en el ENUM de FixedField
  flexiFields: FlexiFieldDTO[]; // <-- Guardar los ids de las columnas
  // json ignored!
  fixedFieldsValues?: string;
  flexiFieldsValues?: string;
  surveyFieldsValues?: Persistable[];
}

export interface WebhookDataSourceDTO extends DataSourceDTO {
  load: WebhookDTO;
}
