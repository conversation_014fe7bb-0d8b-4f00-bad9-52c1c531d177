export enum PrintingFormatAction {
  ADD_PRINTING_FORMAT = 'ADD_PRINTING_FORMAT',
  BACK_PRINTING_FORMAT = 'BACK_PRINTING_FORMAT'
}

export type FixedFieldType =
  | 'AREA'
  | 'AREA_CUSTOM_FIELD1'
  | 'AREA_CUSTOM_FIELD10'
  | 'AREA_CUSTOM_FIELD11'
  | 'AREA_CUSTOM_FIELD12'
  | 'AREA_CUSTOM_FIELD13'
  | 'AREA_CUSTOM_FIELD14'
  | 'AREA_CUSTOM_FIELD15'
  | 'AREA_CUSTOM_FIELD16'
  | 'AREA_CUSTOM_FIELD17'
  | 'AREA_CUSTOM_FIELD18'
  | 'AREA_CUSTOM_FIELD19'
  | 'AREA_CUSTOM_FIELD2'
  | 'AREA_CUSTOM_FIELD20'
  | 'AREA_CUSTOM_FIELD21'
  | 'AREA_CUSTOM_FIELD22'
  | 'AREA_CUSTOM_FIELD23'
  | 'AREA_CUSTOM_FIELD24'
  | 'AREA_CUSTOM_FIELD25'
  | 'AREA_CUSTOM_FIELD26'
  | 'AREA_CUSTOM_FIELD3'
  | 'AREA_CUSTOM_FIELD4'
  | 'AREA_CUSTOM_FIELD5'
  | 'AREA_CUSTOM_FIELD6'
  | 'AREA_CUSTOM_FIELD7'
  | 'AREA_CUSTOM_FIELD8'
  | 'AREA_CUSTOM_FIELD9'
  | 'BUSINESS_UNIT'
  | 'BUSINESS_UNIT_DEPARTMENT'
  | 'CODE'
  | 'CREATED_DATE'
  | 'FILLING_DATE'
  | 'LAST_MODIFIED_DATE'
  | 'OUTSTANDING_SURVEYS'
  | 'PROGRESS'
  | 'REGION_CODE'
  | 'REGION_DESC'
  | 'REQUEST'
  | 'REQUESTOR'
  | 'STAGE'
  | 'STATUS'
  | 'STATUS_PROGRESS'
  ;

export type FixedFieldValues =
  | 1 // Solicitante
  | 2 // Etapa
  | 3 // Avance
  | 4 // Clave
  | 5 // Estado + Avance
  | 6 // Estado
  | 7 // Fecha de llenado
  | 8
  | 9
  | 10
  | 11
  | 12
  | 13
  | 14
  | 15
  | 16
  | 17
  | 18
  | 19
  | 20
  | 21
  | 22
  | 23
  | 24
  | 25
  | 26
  | 27
  | 28
  | 29
  | 30
  | 31
  | 32
  | 33
  | 34
  | 35
  | 36
  | 37
  | 38
  | 39
  | 40
  | 41
  | 42
  ;

export const FixedField: Record<FixedFieldType, FixedFieldValues> = {
  REQUESTOR: 1, // Solicitante
  STAGE: 2, // Etapa
  PROGRESS: 3, // Avance
  CODE: 4, // Clave
  STATUS_PROGRESS: 5, // Estado + Avance
  STATUS: 6, // Estado
  FILLING_DATE: 7, // Fecha de llenado
  BUSINESS_UNIT: 8,
  BUSINESS_UNIT_DEPARTMENT: 9,
  AREA: 10,
  LAST_MODIFIED_DATE: 11,
  CREATED_DATE: 12,
  REQUEST: 13,
  OUTSTANDING_SURVEYS: 14,
  AREA_CUSTOM_FIELD1: 15,
  AREA_CUSTOM_FIELD2: 16,
  AREA_CUSTOM_FIELD3: 17,
  AREA_CUSTOM_FIELD4: 18,
  AREA_CUSTOM_FIELD5: 19,
  AREA_CUSTOM_FIELD6: 20,
  AREA_CUSTOM_FIELD7: 21,
  AREA_CUSTOM_FIELD8: 22,
  AREA_CUSTOM_FIELD9: 23,
  AREA_CUSTOM_FIELD10: 24,
  AREA_CUSTOM_FIELD11: 25,
  AREA_CUSTOM_FIELD12: 26,
  AREA_CUSTOM_FIELD13: 27,
  AREA_CUSTOM_FIELD14: 28,
  AREA_CUSTOM_FIELD15: 29,
  AREA_CUSTOM_FIELD16: 30,
  AREA_CUSTOM_FIELD17: 31,
  AREA_CUSTOM_FIELD18: 32,
  AREA_CUSTOM_FIELD19: 33,
  AREA_CUSTOM_FIELD20: 34,
  AREA_CUSTOM_FIELD21: 35,
  AREA_CUSTOM_FIELD22: 36,
  AREA_CUSTOM_FIELD23: 37,
  AREA_CUSTOM_FIELD24: 38,
  AREA_CUSTOM_FIELD25: 39,
  AREA_CUSTOM_FIELD26: 40,
  REGION_DESC: 41,
  REGION_CODE: 42
} as const;
