import type { SafeHtml } from '@angular/platform-browser';
import type { IPersistableCodeDescription, Persistable } from 'src/app/core/utils/interfaces';
import type { WysiwygMention } from 'src/app/devextreme/wysiwyg/wysiwyg.interfaces';
import type { FixedField } from '../printing-format/printing-format.enums';
import type { PrintFormatVariableTypes } from './printing-format-base.enums';

export interface PrintingFormatDTO extends IPersistableCodeDescription {
  details: string; // <-- descripción larga
  masterId: string;
  html: SafeHtml;
  fileId: number;
  fixedFields: FixedFieldDTO[]; // <-- Guardar los ids en el ENUM de FixedField
  flexiFields: FlexiFieldDTO[]; // <-- Guardar los ids de las columnas
  // json ignored!
  fixedFieldsValues?: string;
  flexiFieldsValues?: string;
  surveyFieldsValues?: Persistable[];
}

export interface DataSourceDTO {
  flexiFields: FlexiFieldDTO[]; // <-- SurveyDataFieldDTO
  sectionFields: FlexiFieldDTO[]; // <-- SurveyDataFieldDTO
  title: string;
  message?: string;
}

export interface PrintingFormatFieldCode {
  fixedFields: string[]; // <-- Guardar los ids en el ENUM de FixedField
  flexiFields: string[]; // <-- Guardar los ids de las columnas
}

export interface IAnswerMetadataDTO extends IPersistableCodeDescription {
  // IPersistableCodeDescription contains id, code and description
  order: number;
  fieldStage: string;
  fieldObjectId: number;
  bgColor?: string;
  color?: string;
}

export interface IAnswerMetadataFieldDTO extends IAnswerMetadataDTO {
  enumValue?: FixedField;
}

export interface AnswerMetadataDTO extends Partial<IAnswerMetadataDTO> {
  id: number; // <-- surveyAnswerMetadataId
  code: string;
}

export interface AnswerMetadataFieldDTO extends Partial<IAnswerMetadataFieldDTO> {
  id: number; // <-- surveyAnswerMetadataId
  code: string;
}

export interface FlexiFieldDTO extends AnswerMetadataDTO {}

export interface FixedFieldDTO extends AnswerMetadataFieldDTO {}

export interface PrintingFormatDataSourceDTO extends DataSourceDTO {
  load: PrintingFormatDTO;
}

export type PrintFormatVariable = WysiwygMention<PrintFormatVariableTypes.FIXED | PrintFormatVariableTypes.FLEXI>;
