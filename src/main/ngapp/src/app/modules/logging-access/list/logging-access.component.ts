import { Component, type OnInit, inject, signal, Input } from '@angular/core';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import { GridComponent } from 'src/app/core/grid/grid.component';
import { Session } from 'src/app/core/local-storage/session';

import * as GridUtil from '@/core/grid/utils/grid-util';
import { HttpHeaders } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { takeUntil } from 'rxjs';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { TextColumn, TimestampColumn } from 'src/app/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from 'src/app/core/i18n/bnext-translate.pipe';
import { AppService } from 'src/app/core/services/app.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { LoggingAccessUtil } from '../utils/logging-access-util';

@Component({
  selector: 'app-logging-access',
  imports: [GridComponent, BnextTranslatePipe],
  templateUrl: './logging-access.component.html',
  styleUrl: './logging-access.component.scss'
})
export class LoggingAccessComponent extends BnextCoreComponent implements OnInit, i18n, FabButton {
  activatedRouted = inject(ActivatedRoute);
  appService = inject(AppService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.logging-access',
    componentName: 'list'
  };

  get customTagName(): string {
    return LoggingAccessComponent.LANG_CONFIG.componentName;
  }

  override get componentPath(): string {
    return LoggingAccessComponent.LANG_CONFIG.componentPath;
  }

  private urlsModules = LoggingAccessUtil.UrlDataSource;

  public descriptionLogging = signal('');

  public url = 'logging-access';
  public perPage = Session.getGridSize();
  public columns: GridColumn[] = [];
  public title = 'Bitácora de accesos';
  public description = '';

  fabFloat = true;
  @Input()
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  ngOnInit(): void {
    super.ngOnInit();
  }

  public langReady(): void {
    this.activatedRouted.paramMap.pipe(takeUntil(this.$destroy)).subscribe((params: any) => {
      const { recordId, logElement } = params.params;
      const urlInfo = this.urlsModules[logElement];
      this.url = `logging-access/${recordId}/${logElement}`;
      this.loadLoggingDescription(urlInfo, recordId, logElement);
      GridUtil.columns(this.columns)
        .push(
          'userName',
          new TextColumn({
            autoSize: true
          })
        )
        .push(
          'createdDate',
          new TimestampColumn({
            autoSize: true
          })
        )
        .translate(this.$destroy, this.translateService, LoggingAccessComponent.LANG_CONFIG, 'columns');
    });
  }

  private loadLoggingDescription(url: string, recordId: number, logElement: string): void {
    const isRPCService = url.startsWith('../DPMS');
    let data = null;
    if (isRPCService) {
      data = {
        isRPCService: isRPCService,
        url: url,
        options: {
          headers: new HttpHeaders({ 'Content-Type': 'application/json-rpc' })
        },
        postBody: JSON.stringify({
          id: Math.floor(Math.random() * (1000 - 1) + 1),
          method: 'load',
          params: [+recordId]
        }),
        cancelableReq: this.$destroy
      };
      this.appService.post(data).subscribe((result) => {
        this.descriptionLogging.set(LoggingAccessUtil.describe(result.result, this.tag(`pattern.${logElement}`), null));
      });
    } else {
      data = {
        isRPCService: isRPCService,
        url: url + recordId,
        cancelableReq: this.$destroy
      };
      this.appService.get(data).subscribe((result) => {
        this.descriptionLogging.set(LoggingAccessUtil.describe(result, this.tag(`pattern.${logElement}`), null));
      });
    }
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.BACK:
        this.return();
        break;
      default:
        console.warn(`Action is not supported ${item.value}`);
        break;
    }
  }

  return(): void {
    this.navLang.back();
  }
}
