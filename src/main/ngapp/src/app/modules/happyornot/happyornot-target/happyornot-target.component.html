<igx-dialog
  #publishDialog
  [rightButtonLabel]="disabled ? null : ((id ? 'i18n.SAVE' : 'i18n.SAVE_AND_PUBLISH') | translate: this)"
  leftButtonLabel="{{ 'root.common.button.cancel' | translate: this }}"
  (rightButtonSelect)="publish()"
  (leftButtonSelect)="close()"
  class="dialog-height"
>
  <form [formGroup]="publishTargetForm" [ngClass]="isMobilDevice() ? 'full-wh-small' : 'target-dialog'" class="container-form grid-floating-action-buttons">
    <igx-tabs tabAlignment="justify">
      <igx-tab-item>
        <igx-tab-header>
          <igx-icon igxTabHeaderIcon class="color-black">check_circle</igx-icon>
        </igx-tab-header>
        <igx-tab-content>
          <div class="grid-container">
            <div class="grid-x grid-padding-x grid-padding-y">
              <div class="cell small-12">
                <div class="bg-gray padding-1" [innerHTML]="disabled ? ('publish-info-disabled' | translate: this) : ('publish-info' | translate: this)"></div>
              </div>
              <div class="cell small-12">
                <div class="survey-mode-container">
                  <app-select
                    [hidden]="!happyornotUserSurveyAvaliable"
                    name="publishTarget"
                    valueKey="value"
                    displayKey="text"
                    [required]="true"
                    [label]="'survey-mode' | translate: this"
                    [data]="publishTargets"
                    [value]="publishTarget"
                    (change)="changeSurveyMode($event)"
                  >
                  </app-select>
                  @if (publishTarget && !disabled) {
                    <button [igxButton]="'contained'" class="round" (click)="addPublishTarget(publishTarget)">
                      <igx-icon family="material">add</igx-icon>
                      <label> {{ 'location' | translate: this }} </label>
                    </button>
                  }
                </div>
              </div>
              <igx-list class="cell small-12 publish-target ng-for">
                <div *igxFor="let item of publishTargetNames; let rowIndex = index; scrollOrientation: 'vertical'; containerSize: '500px'; itemSize: '50px'">
                  @if (item.deleted === 0) {
                    <igx-list-item>
                      <div class="displayFlex cell small-12 publish-target margin-fix">
                        @if (item.publishTarget === EnumPublishTarget.USER) {
                          <span> Unsupported </span>
                        }
                        <div class="igx-input-group--border igx-input-group">
                          <div class="igx-input-group__bundle box-shadow-none" [class.disabled]="disabled">
                            @if (item.id && !disabled) {
                              <igx-suffix class="igx-input-group__bundle-main" (click)="toggleStatus(item)">
                                <igx-icon [attr.title]="statusLabel(item) | translate: this" family="material" [style.color]="item.status === 1 ? 'green' : 'red'">
                                  {{ item.status === 1 ? 'mobile_friendly' : 'screen_lock_portrait' }}
                                </igx-icon>
                              </igx-suffix>
                            }
                            @if (!item.id) {
                              <igx-suffix class="igx-input-group__bundle-main">
                                <igx-icon [attr.title]="'status_draft' | translate: this" family="material" style="color: #ff1493">save</igx-icon>
                              </igx-suffix>
                            }
                          </div>
                        </div>
                        @if (item.publishTarget === EnumPublishTarget.ANONYMOUS) {
                          <igx-input-group type="border" class="full-width igx-input-group--border" theme="material">
                            @if (!item.targetName) {
                              <igx-suffix class="error-icon">
                                <igx-icon>error</igx-icon>
                              </igx-suffix>
                            }
                            @if (item.isDeleteAvailable) {
                              <igx-suffix
                                (click)="remove(rowIndex, publishTargetNames, item.id ? 'targetName' + item.id : 'targetNameTemp' + rowIndex); refreshPublishArrayIdx()"
                              >
                                <igx-icon>delete</igx-icon>
                              </igx-suffix>
                            }
                            @if (item.id) {
                              <igx-suffix (click)="copy(itemLink(item))" [class.disabled]="disabled" [attr.title]="'root.common.button.copy' | translate: this">
                                <igx-icon>content_copy</igx-icon>
                              </igx-suffix>
                            }
                            @if (item.id) {
                              <igx-suffix
                                (click)="openDialogQR(itemLink(item), item.id, item.targetName)"
                                [class.disabled]="disabled"
                                [attr.title]="'root.common.button.qr' | translate: this"
                              >
                                <igx-icon>qr_code</igx-icon>
                              </igx-suffix>
                            }
                            @if (item.id) {
                              <igx-suffix (click)="openShare(item)" [class.disabled]="disabled" [attr.title]="'root.common.button.settings' | translate: this">
                                <igx-icon>settings</igx-icon>
                              </igx-suffix>
                            }
                            @if (item.id) {
                              <igx-prefix (click)="openLink(itemLink(item))" [class.disabled]="disabled" [attr.title]="'root.common.button.open' | translate: this">
                                <igx-icon>open_in_new</igx-icon>
                              </igx-prefix>
                            }
                            <input
                              igxInput
                              [required]="true"
                              [placeholder]="'publish-target-placeholder' | translate: this"
                              [attr.title]="'publish-target-placeholder' | translate: this"
                              [value]="item.targetName"
                              name="{{ 'target-name-' + rowIndex }}"
                              [disabled]="disabled"
                              (change)="updateValue($event.target, rowIndex, publishTargetNames, 'targetName')"
                              (keyup)="markAsTouched($event.target, item.id ? 'targetName' + item.id : 'targetNameTemp' + rowIndex)"
                            />
                            <label igxLabel for="{{ 'target-name-' + rowIndex }}"> {{ 'location' | translate: this }} {{ rowIndex + 1 }} </label>
                          </igx-input-group>
                        }
                      </div>
                    </igx-list-item>
                  }
                </div>
              </igx-list>
            </div>
          </div>
        </igx-tab-content>
      </igx-tab-item>

      <igx-tab-item>
        <igx-tab-header>
          <igx-icon igxTabHeaderIcon class="color-black">notifications_active</igx-icon>
        </igx-tab-header>
        <igx-tab-content>
          <div class="grid-container">
            <div class="grid-x grid-padding-x grid-padding-y" id="alertsContainer">
              <div class="cell small-12">
                <div class="bg-gray padding-1">{{ 'publish-alert' | translate: this }}</div>
              </div>
              <div class="cell small-12">
                @if (!disabled) {
                  <button [igxButton]="'contained'" class="round" (click)="addAlert()">
                    <igx-icon family="material">add</igx-icon>
                    <label> {{ 'alert' | translate: this }} </label>
                  </button>
                }
              </div>
              @for (item of publishTargetMails; track item; let idx = $index) {
                @if (item.deleted === 0) {
                  <div class="cell small-12 publish-target">
                    <igx-input-group type="border" class="igx-input-group--border" theme="material">
                      @if (!item.mail) {
                        <igx-suffix class="error-icon">
                          <igx-icon>error</igx-icon>
                        </igx-suffix>
                      }
                      @if (!disabled) {
                        <igx-suffix (click)="remove(idx, publishTargetMails, item.id ? 'alert' + item.id : 'alertTemp' + idx)">
                          <igx-icon>delete</igx-icon>
                        </igx-suffix>
                      }
                      <input
                        type="email"
                        email
                        igxInput
                        [required]="true"
                        [placeholder]="'alert-placeholder' | translate: this"
                        [attr.title]="'alert-placeholder' | translate: this"
                        [formControlName]="item.id ? 'alert' + item.id : 'alertTemp' + idx"
                        name="{{ 'alert-' + idx }}"
                        maxlength="765"
                        [disabled]="disabled"
                        (change)="updateValue($event.target, idx, publishTargetMails, 'mail')"
                      />
                      <label igxLabel for="{{ 'alert-' + idx }}"> {{ 'root.common.button.mail' | translate: this }} {{ idx + 1 }} </label>
                    </igx-input-group>
                  </div>
                }
              }
            </div>
          </div>
        </igx-tab-content>
      </igx-tab-item>
    </igx-tabs>
  </form>
</igx-dialog>
<igx-dialog
  #QRDialog
  title="{{ 'qr-title' | translate: this }}"
  leftButtonLabel="{{ 'root.common.button.close' | translate: this }}"
  rightButtonLabel="{{ 'root.common.button.download' | translate: this }}"
  (leftButtonSelect)="QRDialog.close()"
  (rightButtonSelect)="getRef()"
  (opened)="onDialogQrOpened()"
>
  <div [igxButton]="'contained'" class="file-select-movil">
    <label>{{ 'upload-picture' | translate: this }}</label>
    <igx-icon class="icon-style">attach_file</igx-icon>
    <input type="file" (change)="uploadImage($event)" />
  </div>
  <img style="display: none" src="" #buffer />
  <a #imgToDownload style="display: none" href=""></a>
  <input #checkbox type="checkbox" style="display: none" [(ngModel)]="crisp" />
  <ngx-kjua
    class="center-qr"
    id="QrCode"
    [text]="url || ''"
    [mSize]="15"
    render="image"
    mode="image"
    [image]="imgNativeElement || ''"
    [crisp]="crisp"
    ecLevel="H"
  ></ngx-kjua>
</igx-dialog>

<igx-dialog
  #addTargetsDialog
  title="{{ 'add-multiple-targets' | translate: this }}"
  rightButtonLabel="{{ 'root.common.button.ok' | translate: this }}"
  (rightButtonSelect)="addTargetsDialog.close()"
>
  <app-lined-area
    #linedTextarea
    [areaContent]="publishTargetNames"
    (addRow)="onAddRow($event)"
    (removeRow)="onRemoveRow($event)"
    (rowChanged)="onRowChanged($event)"
  ></app-lined-area>
</igx-dialog>

<igx-dialog
  #shareDialogConfig
  leftButtonLabel="{{ 'cancel' | translate: this }}"
  rightButtonLabel="{{ 'apply' | translate: this }}"
  (leftButtonSelect)="closeShareDialogConfig()"
  (rightButtonSelect)="applyShareDialogConfig()"
>
  <igx-dialog-title>
    <div class="title-container">
      <div class="dialog-title">{{ 'advanced-config' | translate: this }}</div>
    </div>
  </igx-dialog-title>
  <form [formGroup]="sharedFormConfig">
    <igx-input-group>
      <input formControlName="url" igxInput name="URL" [disabled]="true" type="text" />
      <label igxLabel for="fullName">{{ 'link' | translate: this }}</label>
    </igx-input-group>
    <igx-input-group>
      <input formControlName="viewsPerLink" igxInput name="viewsPerLink" type="number" />
      <label igxLabel for="fullName">{{ 'viewsPerLink' | translate: this }}</label>
    </igx-input-group>
    <igx-date-picker #expireDate formControlName="expireDate" name="expireDate" mode="dialog">
      <label igxLabel>{{ 'expireDate' | translate: this }}</label>
    </igx-date-picker>
    <igx-time-picker #expireTime [inputFormat]="'HH:mm'" formControlName="expireTime" name="expireTime" mode="dialog">
      <label igxLabel>{{ 'expireTime' | translate: this }}</label>
    </igx-time-picker>
    <igx-input-group>
      <input formControlName="tagName" igxInput name="alias" type="text" />
      <label igxLabel for="alias">{{ 'alias' | translate: this }}</label>
    </igx-input-group>
  </form>
</igx-dialog>

@if (loading) {
  <app-loader></app-loader>
}
