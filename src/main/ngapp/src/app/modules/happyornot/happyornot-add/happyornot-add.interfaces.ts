import type { DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import type { IAuditableEntity } from '@/core/utils/interfaces';
import type { ITextHasValue } from '@/core/utils/text-has-value';
import type { SafeHtml } from '@angular/platform-browser';
import type { HappyornotFace, HappyornotMessagePosition } from 'src/app/shared/happyornot/happyornot-add.enums';
import type { HappyornotLink } from '../happyornot-target/happyornot-target.interfaces';
import type { HappyornotPublishTarget } from './../happyornot-target/happyornot-target.enums';

export interface HappyOption extends HappyornotData {
  src: string;
  order: number;
  fileId?: number;
  score?: number;
}

export interface HappyornotMailEntity extends IAuditableEntity {
  mail: string;
  userId?: number;
}

export interface HappyornotTargetEntity extends IAuditableEntity {
  type?: HappyornotPublishTarget;
  userId?: number;
  happyornotLinks?: HappyornotLink[];
}

export interface HappyornotSurveySizes {
  panelLogo: string;
  panelLogoLeft: string;
  panelLogoCenter: string;
  panelLogoRight: string;
  panelQuestion: string;
  panelHeader: string;
  panelButtons: string;
  panelButtonsLeft: string;
  panelButtonsCenter: string;
  panelButtonsRight: string;
  panelFooter: string;
}

export interface HappyornotSurveyTarget extends IAuditableEntity {
  targetNames: HappyornotTargetEntity[];
  targetMails: HappyornotMailEntity[];
}

export interface HappyornotSurveyEntity extends IAuditableEntity, HappyornotSurveyTarget, HappyornotCustomSurveys {
  logoFileId: number;
  aspectWidth: number;
  aspectHeight: number;
  waitingTime: number;
  html: SafeHtml;
  header: SafeHtml;
  headerPlain: string;
  footer: SafeHtml;
  footerPlain: string;
  screenBackground: string;
  questionColor: string;
  facesAlignment: string;
  facesDesign: HappyornotFace;
  messagePosition: HappyornotMessagePosition;
  publishTarget: HappyornotPublishTarget;
  sizes: HappyornotSurveySizes;
  negativeReport: boolean;
  timeNegativeReport: string;
  answerPerDay: boolean;
  alertNegativeAnswer: boolean;
  fontStyle: string;
  fontSize: number;
  allowFeedback: boolean;
  feedbackTitle: string;
}

export interface HappyornotSurveyTargetQR extends ITextHasValue<string> {
  id: number;
}

export interface MobileResolutions extends DropdownMenuItem {
  resolution?: {
    w: number;
    h: number;
  };
  deviceFamily: string;
}

export interface HappyornotCustomSurveys extends HappyornotCustomSurveyType, HappyornotCustomSurveyImageType {
  hasCustomsAnswers?: boolean;
}

export enum HappyornotCustomSurveysEnum {
  customAnswer1 = 'customAnswer1',
  customAnswer2 = 'customAnswer2',
  customAnswer3 = 'customAnswer3',
  customAnswer4 = 'customAnswer4',
  customAnswer5 = 'customAnswer5',
  customAnswer6 = 'customAnswer6',
  customAnswer7 = 'customAnswer7',
  customAnswer8 = 'customAnswer8',
  customAnswer9 = 'customAnswer9',
  customAnswer10 = 'customAnswer10'
}

export enum HappyornotCustomsSurveysImages {
  customAnswerName1 = 'customAnswerName1',
  customAnswerName2 = 'customAnswerName2',
  customAnswerName3 = 'customAnswerName3',
  customAnswerName4 = 'customAnswerName4',
  customAnswerName5 = 'customAnswerName5',
  customAnswerName6 = 'customAnswerName6',
  customAnswerName7 = 'customAnswerName7',
  customAnswerName8 = 'customAnswerName8',
  customAnswerName9 = 'customAnswerName9',
  customAnswerName10 = 'customAnswerName10'
}

export interface HappyornotData {
  answerName?: string;
  base64img?: string;
  isCustom?: boolean;
  isNew?: boolean;
}

export type HappyornotCustomSurveyType = Partial<Record<HappyornotCustomSurveysEnum, string>>;

export type HappyornotCustomSurveyImageType = Partial<Record<HappyornotCustomsSurveysImages, string>>;
