import { useAnimation } from '@angular/animations';
import { As<PERSON><PERSON><PERSON><PERSON>, DatePipe, NgClass } from '@angular/common';
import { Component, ElementRef, Input, type OnDestroy, type OnInit, inject, output, viewChild } from '@angular/core';
import {
  type AbstractControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormControl,
  type UntypedFormGroup,
  type ValidationErrors,
  type ValidatorFn,
  Validators
} from '@angular/forms';
import {
  HorizontalAlignment,
  type IBaseChipEventArgs,
  type IDialogCancellableEventArgs,
  IgxButtonDirective,
  IgxCheckboxComponent,
  IgxChipComponent,
  IgxDatePickerComponent,
  IgxDialogActionsDirective,
  IgxDialogComponent,
  IgxHintDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxTimePickerComponent,
  IgxTooltipDirective,
  IgxTooltipTargetDirective,
  PickerInteractionMode,
  type PositionSettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { map, takeUntil, zipWith } from 'rxjs/operators';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import { DropdownSearchComponent } from 'src/app/core/dropdown-search/dropdown-search.component';

import { AppService } from 'src/app/core/services/app.service';

import { AutoresizeDirective } from '@/core/directives/autoresize';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { ConfigApp } from '@/core/local-storage/config-app';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { Session } from '@/core/local-storage/session';
import { NoticeService } from '@/core/services/notice.service';
import { randomUUID } from '@/core/utils/crypto-utils';
import * as DateUtil from '@/core/utils/date-util';
import { ErrorHandling } from '@/core/utils/error-handling';
import { FeatureFlag, isFeatureAvailable } from '@/core/utils/feature-util';
import { FormUtil } from '@/core/utils/form-util';
import { cloneObject } from '@/core/utils/object';
import { stringColors } from '@/core/utils/string-util';
import { slideInTop, slideOutBottom } from '@infragistics/igniteui-angular/animations';
import type { IFileData, IFileUploadAdded } from 'src/app/core/drop-file/drop-file.interfaces';
import { LocalStorageSession } from 'src/app/core/local-storage/local-storage-session';
import { MultiFileUploadComponent } from 'src/app/core/multi-file-upload/multi-file-upload.component';
import type { DataMap } from 'src/app/core/utils/data-map';
import { Deferred } from 'src/app/core/utils/deferred';

import * as DomUtil from '@/core/utils/dom-util';
import { type Observable, firstValueFrom } from 'rxjs';
import type { InvalidFormStatus } from 'src/app/core/utils/form.interfaces';
import type { TextHasValue } from 'src/app/core/utils/text-has-value';
import type { PlannedActivityData, PlannedActivityValue } from 'src/app/shared/activities/core/utils/activity-planner.interfaces';
import type { ClientValue, TextPlannerValue } from 'src/app/shared/planner/planner-task/planner-task.interfaces';
import { ActivitiesSelectComponent } from '../../pendings/activities/activities-select/activities-select.component';
import { ActivityTypes } from '../../pendings/activities/activity-info/activity-info.constant';
import { PlannerStatus } from '../../planner/planner-add/planner-add.interfaces';
import { TimesheetService } from '../../timework/services/timesheet.service';
import type { TimesheetDataSourceDto } from '../timesheet-add/timesheet-datasource';
import { StopwatchType } from '../timesheet-stopwatch/timesheet-stopwatch.enums';
import { PlannedType } from '../timesheet-widget/timesheet-widget.enums';
import type { TimesheetActivityPlanned, TimesheetDto, TimesheetServiceDto } from '../timesheet-widget/timesheet-widget.interfaces';
import { TimesheetWidgetUtils } from './../timesheet-widget/timesheet-widget.utils';
import type { CaptureTimeEvent, ChangePlannerEvent, SetupTimesheetData, TimesheetDeleteEvent, TimesheetEditEvent } from './timesheet-dialog.interfaces';

@Component({
  selector: 'app-timesheet-dialog',
  templateUrl: './timesheet-dialog.component.html',
  styleUrls: ['./timesheet-dialog.component.scss'],
  imports: [
    IgxDialogComponent,
    FormsModule,
    ReactiveFormsModule,
    IgxIconComponent,
    IgxInputGroupComponent,
    IgxPrefixDirective,
    IgxInputDirective,
    IgxIconButtonDirective,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxLinearProgressBarComponent,
    IgxChipComponent,
    IgxTooltipTargetDirective,
    IgxTooltipDirective,
    ActivitiesSelectComponent,
    DropdownSearchComponent,
    IgxLabelDirective,
    NgClass,
    AutoresizeDirective,
    IgxTimePickerComponent,
    IgxHintDirective,
    IgxDatePickerComponent,
    MultiFileUploadComponent,
    IgxDialogActionsDirective,
    IgxCheckboxComponent,
    BnextTranslatePipe,
    AsyncPipe
  ]
})
export class TimesheetDialogComponent extends BnextCoreComponent implements OnDestroy, OnInit {
  service = inject(AppService);
  noticeService = inject(NoticeService);
  datePipe = inject(DatePipe);

  fb = inject(UntypedFormBuilder);

  private timesheetService = inject(TimesheetService);

  private _def: Deferred<TimesheetDto> = null;

  public LangConfig = TimesheetWidgetUtils.LANG_CONFIG;
  public get customTagName(): string {
    return TimesheetWidgetUtils.LANG_CONFIG.componentName;
  }
  public get componentPath(): string {
    return this._componentPath;
  }
  public set componentPath(value: string) {
    this._componentPath = value;
  }
  public get isOpen(): boolean {
    return !!this.saveDialog()?.isOpen;
  }

  private _componentPath = this.LangConfig.componentPath;

  isStopwatchAvailable = isFeatureAvailable(FeatureFlag.TIMESHEET_STOP_WATCH);
  EnumPlannedType = PlannedType;
  busy = false;
  loadingDataSource = false;
  loadedDataSource = false;
  colorCache: DataMap<{
    color: string;
    bgColor: string;
  }> = {};
  timePickerMode: PickerInteractionMode = PickerInteractionMode.DropDown;
  data: TimesheetDto = TimesheetWidgetUtils.getTimesheetInstance();
  fixedTags: string[] = [];
  form: UntypedFormGroup;
  isToday = true;
  todayLabel = '';
  showTags = false;
  dialogOpen = false;
  titleCurrentSelectedDate = '';
  isOutsideTheLimit = false;
  plannedType = PlannedType.UNPLANNED;
  public isTaskExpired: Observable<boolean>;
  public isUpdateTsFromSw = false;
  private dateEditSelected: Date = null;
  private _enableStopwatch = true;
  private formStatus: InvalidFormStatus;
  public addErrorClass: boolean;
  private openedFromTooltip: boolean;
  public departmenName = '';
  public preventPopup = false;
  public popUpFormRemember: UntypedFormGroup;
  private changeFieldMap: DataMap = {};
  public showMoreTags = false;
  public disablePlannedActivityField = false;

  public get enableStopwatch(): boolean {
    if (!this.isStopwatchAvailable) {
      return false;
    }
    return this._enableStopwatch;
  }
  private set enableStopwatch(_enableStopwatch: boolean) {
    this._enableStopwatch = _enableStopwatch;
  }

  // Cliente - Proyecto - Tarea datasources
  clients: ClientValue[] = [];
  planners: TextPlannerValue[] = [];
  tasks: TextPlannerValue[] = [];
  tasksActivities: TextPlannerValue[] = [];
  tasksActivitiesUrl =
    `timesheet/dialog/task-activities/${ActivityTypes.REPORTED.toString()}` ||
    ActivityTypes.RETURNED.toString() ||
    ActivityTypes.IN_PROCESS.toString() ||
    ActivityTypes.IMPLEMENTED.toString();

  filteredPlanners: TextPlannerValue[] = [];
  filteredTasks: TextPlannerValue[] = [];

  public positionSettings: PositionSettings = {
    openAnimation: useAnimation(slideInTop, { params: { duration: '300ms' } }),
    closeAnimation: useAnimation(slideOutBottom, { params: { duration: '300ms' } }),
    horizontalDirection: HorizontalAlignment.Center,
    verticalDirection: VerticalAlignment.Middle,
    horizontalStartPoint: HorizontalAlignment.Center,
    verticalStartPoint: VerticalAlignment.Middle,
    minSize: { height: 300, width: 300 }
  };

  isreadOnlyRecord = false;
  buttonText = '';

  readonly saved = output<TimesheetDto>();

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  hasExternalDataSource = false;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  currentSelectedDate = new Date();

  readonly saveDialog = viewChild<IgxDialogComponent>('saveDialog');
  readonly registry = viewChild('registry', { read: ElementRef });
  readonly clientId = viewChild('clientId', { read: ElementRef });
  readonly plannerId = viewChild<DropdownSearchComponent>('plannerId');
  readonly taskId = viewChild<DropdownSearchComponent>('taskId');
  readonly fileData = viewChild<MultiFileUploadComponent>('fileData');
  readonly butonSave = viewChild<ElementRef>('butonSave');
  readonly startPicker = viewChild<IgxTimePickerComponent>('startPicker');
  readonly endPicker = viewChild<IgxTimePickerComponent>('endPicker');
  readonly tooltip = viewChild<IgxTooltipDirective>('tooltipRef');

  override ngOnInit(): void {
    this.translate
      .getFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.today-label')
      .pipe(takeUntil(this.$destroy))
      .subscribe((tag) => (this.titleCurrentSelectedDate = this.todayLabel = tag));
    this.translate
      .getFrom(TimesheetWidgetUtils.LANG_CONFIG, 'start-stopwatch')
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        this.updateValidationForm();
        this.cdr.detectChanges();
      });
    this.timesheetService.refreshDialogDataSourceCatalogs.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.loadedDataSource = false;
      this.setUp();
    });
    this.timesheetService.timesheetAdd.pipe(takeUntil(this.$destroy)).subscribe((timesheet: TimesheetServiceDto) => {
      this.openNew(
        PlannedType.PLANNED,
        timesheet.pendingRecordId,
        timesheet.comment,
        timesheet.tags,
        this.currentSelectedDate || new Date(),
        timesheet.dataActivityPlanned,
        true,
        undefined,
        timesheet.onOpenedWindow
      ).then(
        (value: TimesheetDto) => {
          // Enviamos el item
          this.timesheetService.pushItemTimesheet(value);
          timesheet.def.resolve(value);
          this.cdr.detectChanges();
        },
        (e) => {
          timesheet.def.reject(e);
          this.cdr.detectChanges();
        }
      );
    });
    this.timesheetService.onCaptureTimesheet.pipe(takeUntil(this.$destroy)).subscribe((event: CaptureTimeEvent) => {
      this.openNew(
        event.plannedType,
        event.pendingRecordId,
        event.comment,
        event.tags,
        this.currentSelectedDate || event.date,
        event.dataActivityPlanned,
        event.enableStopwatch,
        event.disablePlannedActivityField,
        event.onOpenedWindow
      ).then(
        (data) => (event.def ? event.def.resolve(data) : null),
        (data) => (event.def ? event.def.reject(data) : null)
      );
    });
    this.timesheetService.timesheetStartStopwatch.pipe(takeUntil(this.$destroy)).subscribe((args: TimesheetServiceDto) => {
      const dto: TimesheetDto = TimesheetWidgetUtils.getTimesheetInstance(
        args.pendingRecordId,
        TimesheetWidgetUtils.fixDate(DateUtil.safe(new Date())),
        args.dataActivityPlanned,
        false
      );
      dto.tags = args.tags;
      dto.timesheetId = args.timesheetId;
      dto.stopwatchType = args.stopwatchType;
      this.timesheetService.pushItemTimesheet(dto);
    });
    this.timesheetService.updateStopwatchRegistryEmpty.pipe(takeUntil(this.$destroy)).subscribe((dto: TimesheetDto) => {
      if (dto) {
        dto.businessUnitDescription = Session.getBusinessUnitDepartmentName();
        this.openEdit(dto).then((value: TimesheetDto) => {
          this.timesheetService.replaceItem(value);
          dto.def.resolve(value);
          this.cdr.detectChanges();
        });
      }
    });
    this.timesheetService.onOpenEdit.pipe(takeUntil(this.$destroy)).subscribe((event: TimesheetEditEvent) => {
      this.openEdit(event.data, event.isOpenedFromTooltip).then(
        (data) => (event.def ? event.def.resolve(data) : null),
        (data) => (event.def ? event.def.reject(data) : null)
      );
    });
    this.timesheetService.onDeleteRegistry.pipe(takeUntil(this.$destroy)).subscribe((event: TimesheetDeleteEvent) => {
      this.deleteRegistry(event.timesheetId).then(
        (data) => (event.def ? event.def.resolve(data) : null),
        (data) => (event.def ? event.def.reject(data) : null)
      );
    });
    this.timesheetService.onSetupFromDataSource.pipe(takeUntil(this.$destroy)).subscribe((data: SetupTimesheetData) => {
      this.hasExternalDataSource = data.hasExternalDataSource;
      this.setupFromDataSource(data.dataSource);
      this.cdr.detectChanges();
    });
    this.timesheetService.onChangePlannerTask.pipe(takeUntil(this.$destroy)).subscribe((event: ChangePlannerEvent) => {
      this.setNewChangePlannerTask(event.modifiedValue, event.activityPlannedId);
    });
    this.timesheetService.onTimesheetWidgetDateChange.pipe(takeUntil(this.$destroy)).subscribe((date: Date) => {
      this.currentSelectedDate = date;
    });
    const plannedActRequired = this.getPlannedActRequired();
    this.form = this.fb.group({
      uid: new UntypedFormControl(this.data.uid),
      fileData: [[]],
      tags: new UntypedFormControl(this.data.tags),
      registry: new UntypedFormControl(this.data.registry, Validators.required),
      date: new UntypedFormControl(this.data.date, Validators.required),
      start: new UntypedFormControl(this.data.start, []),
      end: new UntypedFormControl(this.data.end, []),
      clientId: new UntypedFormControl(null, Validators.required),
      plannerId: new UntypedFormControl(this.data.plannerId, Validators.required),
      taskId: new UntypedFormControl(this.data.activityId, Validators.required),
      systemLinkValue: new UntypedFormControl(this.data.systemLinkValue),
      plannedActivity: new UntypedFormControl(this.data.activityPlannedId, plannedActRequired)
    });
    this.isTaskExpired = this.form.controls.plannerId.events
      .pipe(map((c) => c.source.value as number))
      .pipe(zipWith(this.form.controls.taskId.events.pipe(map((c) => c.source.value as number)), this.form.controls.date.events.pipe(map((c) => c.source.value as Date))))
      .pipe(takeUntil(this.$destroy))
      .pipe(
        map(([plannerId, taskId, date]) => {
          if (plannerId && taskId) {
            const p = this.tasksActivities?.filter((ta) => ta.plannerId === plannerId)[0];
            if (!p || p.plannerEndDate) {
              return false;
            }
            const selectedDate = date?.getTime();
            if (selectedDate > p.plannerEndDate) {
              return true;
            }
            const ta = this.tasksActivities?.filter((ta) => ta.activityId === taskId)[0];
            if (!ta || !ta.commitmentDate) {
              return false;
            }
            if (selectedDate > ta.commitmentDate) {
              return true;
            }
          }
          return false;
        })
      );
    this.popUpFormRemember = this.fb.group({
      checkboxPopUpRemember: new UntypedFormControl(false)
    });

    if (this.isMobilDevice() && this.isTouchDevice) {
      this.timePickerMode = PickerInteractionMode.Dialog;
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.reject();
    this._def = null;
    this.cdr.detach();
  }

  saveFile(event: IFileUploadAdded) {
    const file: IFileData = event.file;
    file.lastModifiedBy = Session.getUserName();
    file.lastModifiedDate = new Date();
    file.stage = this.data.timesheetId === null ? '0' : '1';
    event.grid?.cdr.detectChanges();
  }

  private updateLastEnd(hasPendingRecord: boolean) {
    // Se cambia validación para utilizar la variable LAST_END_TODAY solo cuando sea hoy y es un registro de actividad
    if (hasPendingRecord && this.isToday) {
      TimesheetWidgetUtils.LAST_END = TimesheetWidgetUtils.LAST_END_TODAY ? TimesheetWidgetUtils.LAST_END_TODAY : TimesheetWidgetUtils.LAST_END;
    }
  }

  clearData(args?: number | TimesheetDto, comment?: string, tags?: string[], date?: Date, dataActivityPlanned?: TimesheetActivityPlanned): void {
    if (typeof args === 'number' || typeof args === 'undefined') {
      const pendingRecordId = args;
      let dateN = new Date();
      if (date) {
        dateN = new Date(date);
      }
      this.isToday = DateUtil.isToday(dateN);
      // Se cambia validación para utilizar la variable LAST_END_TODAY solo cuando sea hoy y es un registro de actividad
      this.updateLastEnd(!!pendingRecordId);
      dateN.setSeconds(0);
      dateN.setMilliseconds(0);
      const lastEnd = TimesheetWidgetUtils.LAST_END || dateN;
      this.currentSelectedDate = dateN;
      this.setTitleCurrentDate();
      this.data = TimesheetWidgetUtils.getTimesheetInstance(pendingRecordId, lastEnd, dataActivityPlanned, true);
      this.updateMissingDataSource(this.data, this.data.plannerStatus === PlannerStatus.INACTIVE);
      if (this.data.plannerId !== null || this.data.activityId !== null || this.data.clientId !== null) {
        // Se pasan los datos del proyecto y tarea configurados en la actividad.
        this.setValuesForm(this.data.clientId, this.data.plannerId, this.data.activityId);
      }
      this.form.patchValue({
        date: this.data.date,
        start: this.data.start,
        end: this.data.end
      });
      if (tags) {
        this.fixedTags = tags;
      } else {
        this.fixedTags = [];
        // Se guarda una copia de los valores de los TAGS correspondientes a registros de actividades
        if (this.data.pendingRecordId !== null) {
          this.updateFixedTags(this.data.tags);
        }
      }
    } else {
      this.data = cloneObject(args);
      let skipInactiveDataSource = false;
      if (this.data.hasEnableStopwatch && this.data.stopwatchType === StopwatchType.AUTOMATIC && this.data.plannerStatus === PlannerStatus.INACTIVE) {
        skipInactiveDataSource = true;
      }
      this.updateMissingDataSource(this.data, skipInactiveDataSource);
      this.currentSelectedDate = new Date(args.date);
      this.setTitleCurrentDate();
      // Se pasan los datos del registro a modificar.
      const activity: PlannedActivityValue = {
        mainSystemLinkValue: this.data.systemLinkValue,
        mainSystemId: this.data.systemLinkId,
        mainSystemLinkLabel: this.data.systemLinkLabel,
        mainSystemUrl: '',
        mainSystemRegExp: '',
        systemLinkAvailable: false,
        pendingRecordId: this.data.pendingRecordId,
        text: this.data.activityPlannedDescription,
        value: this.data.activityPlannedId,
        clientId: this.data.clientId,
        clientDescription: this.data.clientDescription,
        plannerId: this.data.plannerId,
        plannerDescription: this.data.plannerDescription,
        activityId: this.data.activityId,
        activityDescription: this.data.activityDescription,
        plannerStatus: this.data.plannerStatus
      };
      // this.data;
      if (typeof this.data.clientId === 'undefined') {
        this.data.clientId = null;
      }
      if (typeof this.data.plannerId === 'undefined') {
        this.data.plannerId = null;
      }
      if (typeof this.data.activityId === 'undefined') {
        this.data.activityId = null;
      }
      this.form.setValue(
        {
          uid: this.data.uid || this.getUuid(),
          tags: this.data.tags || '',
          registry: this.data.registry || '',
          fileData: this.data.fileData || [],
          date: DateUtil.safe(this.data.date),
          start: DateUtil.safe(this.data.start),
          end: DateUtil.safe(this.data.end),
          clientId: this.data.clientId,
          plannerId: this.data.plannerId,
          taskId: this.data.activityId,
          systemLinkValue: this.data.systemLinkValue || '',
          plannedActivity: activity
        },
        { onlySelf: true, emitEvent: false }
      );
      this.setValuesForm(this.data.clientId, this.data.plannerId, this.data.activityId);
    }
    if (comment) {
      this.data.registry = comment || this.data.registry;
      this.form.patchValue({ registry: this.data.registry });
    }
    // Limpiamos el cache de colores
    this.colorCache = {};
    // Dentro del límite para modificar
    this.isOutsideTheLimit = this.computeIsOutsideTheLimit();
    this.cdr.detectChanges();
    this.checkDisableFields();
  }

  // Método para obtener los catálogos de Cliente - Proyecto - Tarea y Archivos
  private setUp(data?: TimesheetDto): Promise<boolean[]> {
    return Promise.all([this.setUpDataSource(), this.setupFiles(data)]);
  }

  private async setUpDataSource(): Promise<boolean> {
    try {
      const refresh = await firstValueFrom(
        this.service.get({
          url: 'timesheet/dialog/pendingRefresh',
          cancelableReq: this.$destroy
        })
      );
      if (this.loadedDataSource) {
        this.loadedDataSource = !refresh;
      }
      return this.refreshDataSource();
    } catch (error) {
      this.onFailedSetup();
      throw error;
    }
  }

  // Método para obtener los catálogos de Cliente - Proyecto - Tarea
  private async refreshDataSource(): Promise<boolean> {
    if (this.hasExternalDataSource || this.loadingDataSource || this.loadedDataSource) {
      return false;
    }
    this.loadingDataSource = true;
    this.cdr.detectChanges();
    const showimpl = this.navLang.queryParam('impl') || false;
    try {
      const dataSource = await firstValueFrom(
        this.service.get({
          url: `timesheet/dialog/data-source/${showimpl}`,
          cancelableReq: this.$destroy
        })
      );
      this.setupFromDataSource(dataSource);
      return false;
    } catch (error) {
      this.onFailedSetup();
      throw error;
    }
  }

  private async setupFiles(data: TimesheetDto): Promise<boolean> {
    if (!data || !data.timesheetId) {
      return false;
    }
    data.fileData = await this.loadFiles(data);
    data.isLoadedFileData = true;
    return true;
  }

  private async loadFiles(data: TimesheetDto): Promise<IFileData[]> {
    if (!data || !data.timesheetId) {
      return null;
    }
    if (data.isLoadedFileData) {
      return data.fileData;
    }
    try {
      return firstValueFrom(
        this.service.get({
          cancelableReq: this.$destroy,
          url: `timesheet/files/${data.timesheetId}`,
          handleFailure: false
        })
      );
    } catch (error) {
      this.onFailedSetup();
      throw error;
    }
  }

  private onFailedSetup() {
    this.loadingDataSource = false;
    this.busy = false;
    this.cdr.detectChanges();
  }

  public setupFromDataSource(dataSource: TimesheetDataSourceDto): void {
    if (!dataSource) {
      return;
    }
    // Agregamos las tareas
    this.tasks = (dataSource.tasks || []).map((t) => {
      t.uid = this.getUuid();
      return t;
    });
    // Agregamos los proyectos
    this.planners = dataSource.planners || [];
    // Agregamos los clientes
    this.clients = dataSource.clients || [];
    // Agregamos las actividades
    this.tasksActivities = dataSource.tasksActivities || [];
    this.loadedDataSource = true;
    this.loadingDataSource = false;
    this.cdr.detectChanges();
  }

  private getUuid(): string {
    return `${randomUUID()}`;
  }

  // Método para filtrar los proyectos cuando se selecciona un cliente
  onChangeClient(client: TextHasValue) {
    if (!this.planners || client === null) {
      this.filteredPlanners = [];
      this.filteredTasks = [];
      this.form.patchValue({
        plannerId: null,
        taskId: null
      });
      return;
    }
    this.data.clientDescription = client.text;
    this.filteredPlanners = this.filterPlannersByClient(+client?.value);
    // Limpiamos los valores
    const plannerId = this.plannerId();
    if (plannerId?.value !== null && typeof plannerId?.value !== 'undefined') {
      plannerId.clearField();
    }
    const taskId = this.taskId();
    if (taskId?.value !== null && typeof taskId?.value !== 'undefined') {
      taskId.clearField();
    }
  }

  // Método para filtrar las tareas cuando se selecciona un proyecto
  onChangePlanner(planner: TextHasValue) {
    if (!this.tasks || planner === null) {
      this.filteredTasks = [];
      this.form.get('taskId').setValue(null);
      return;
    }
    this.data.plannerDescription = planner.text;
    this.filteredTasks = this.filterTaskByPlanners(+planner.value);
    const taskId = this.taskId();
    if (taskId && taskId.value !== null && typeof taskId.value !== 'undefined') {
      taskId.clearField();
    }
  }

  // Método para setear el valor de system_link_id y poder capturar el campo REQ_ID desde el Alta req-45022
  onChangeTask(task: TextHasValue) {
    if (task === null) {
      return;
    }
    this.data.activityDescription = task.text;
    if (this.plannedType === PlannedType.UNPLANNED) {
      const systemLink = this.filteredTasks.find((item: TextPlannerValue) => {
        return +item.value === +task.value;
      });
      this.data.systemLinkId = systemLink.mainSystemId;
      this.data.systemLinkLabel = systemLink.mainSystemLinkLabel;
    }
  }

  // Método para llenar los combos de Cliente - Proyecto - Tarea en edición
  private setValuesForm(client: number, planner: number, activityId: number): void {
    if ((typeof activityId === 'number' && activityId !== null && activityId > 0) || this.data?.activityId !== null) {
      this.filteredPlanners = this.filterPlannersByClient(client);
      this.filteredTasks = this.filterTaskByPlanners(planner);
      this.cdr.detectChanges();
      this.form.controls.clientId.setValue(client, { onlySelf: true, emitEvent: false });
      this.form.controls.plannerId.setValue(planner, { onlySelf: true, emitEvent: false });
      this.form.controls.taskId.setValue(activityId || this.data.activityId, { onlySelf: true, emitEvent: false });
    } else if (typeof planner === 'number' && planner !== null) {
      this.filteredPlanners = this.filterPlannersByClient(client);
      this.cdr.detectChanges();
      // Seteamos los valores
      this.form.controls.clientId.setValue(client, { onlySelf: true, emitEvent: false });
      this.form.controls.plannerId.setValue(planner, { onlySelf: true, emitEvent: false });
      this.form.controls.taskId.setValue(null, { onlySelf: true, emitEvent: false });
    } else if (typeof client === 'number' && client !== null) {
      // Encontramos el cliente seleccionado
      const clientSelected = this.clients.find((cli: ClientValue) => +cli.value === +client);
      if (!clientSelected) {
        console.error(`Client ${clientSelected.value} not found.`);
        return;
      }
      // Seteamos los valores
      this.form.patchValue({ clientId: clientSelected?.value });
      // Filtramos los proyectos
      this.filteredPlanners = this.planners.filter((pl: TextPlannerValue) => {
        return +pl.clientId === +client;
      });
    } else {
      this.form.patchValue({ systemLinkValue: null });
      // Seteamos los valores
      this.data.taskRecordLocked = false;
      this.data.plannerRecordLocked = false;
      this.data.clientRecordLocked = false;
      setTimeout(() => {
        this.form.patchValue(
          {
            taskId: null,
            plannerId: null,
            clientId: null
          },
          { onlySelf: true, emitEvent: false }
        );
      }, 110);
    }
    this.checkDisableFields();
  }

  isFixedTag(tag: string): boolean {
    if (!tag || !tag.trim().length) {
      return false;
    }
    return !!this.fixedTags.find((t) => `${t}`.trim() === tag.trim());
  }

  hide(): void {
    if (this.data.stopwatchType === StopwatchType.AUTOMATIC && this.data.hasEnableStopwatch) {
      this.noticeService.notice(this.translateService.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.stopwatch-continue'));
    }
    if (!this.checkSafeClose()) {
      this.form.reset();
      this.form.markAsPristine();
      this.form.markAsUntouched();
      this.reject();
      this.saveDialog().close();
      this.dialogOpen = false;
      this.cdr.detectChanges();
    }
  }

  keyUp(event: KeyboardEvent | { code: string }) {
    switch (event.code) {
      case 'Space':
      case 'Comma':
        this.refreshTagColors();
        break;
    }
  }

  chipColor(str: string): string {
    return this.colorCache[str]?.bgColor || '#c6c6c6';
  }

  chipColorBlack(str: string): boolean {
    if (typeof this.colorCache[str] === 'undefined') {
      return true;
    }
    return this.colorCache[str].color === '#000000';
  }

  save(): void {
    this.changeFieldMap = {};
    this.cdr.detectChanges();
    this.form.markAllAsTouched();
    this.form.updateValueAndValidity();
    //Se agrego esta funcionalidad para "gestionar" de manera "manual" cuando un formControl es invalido
    this.formStatus = FormUtil.isValid(this.form, [], true);
    if (!this.formStatus.status) {
      this.setInvalidadControls(this.formStatus);
      return;
    }
    if (this.busy) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.busyMessage'));
      return;
    }
    if (this.fileData()?.uploader?.isUploading) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.uploadingFilesBusy'));
      return;
    }
    if (this.computeIsOutsideTheLimit(this.form.controls.date.value)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.dateOutOfLimit'));
      return;
    }
    this.busy = true;
    let controller = 'timesheet/save';
    if (typeof this.data.pendingRecordId === 'number') {
      controller = `timesheet/save/${this.data.pendingRecordId}`;
    }

    const data: TimesheetDto = this.getEntityDataSave();

    if (this.enableStopwatch) {
      const startDate = DateUtil.safe(data.start);
      const endDate = DateUtil.safe(data.end);
      endDate.setHours(startDate.getHours());
      endDate.setMinutes(startDate.getMinutes() + 5);
      data.end = endDate;
    }
    const modify = typeof data.timesheetId === 'number';
    data.tags = this.tagsArrayFixed(this.data.tags);
    this.service.post({ url: controller, cancelableReq: this.$destroy, postBody: data, options: null, handleFailure: false }).subscribe(
      (result: TimesheetDto) => {
        this.data.hasEnableStopwatch = result.hasEnableStopwatch;
        let message: string;
        if (result.fileData) {
          for (const file of result.fileData) {
            file.code = data.activityPlannedCode;
          }
        }
        if (modify) {
          this.replaceItem(result);
          message = this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.successModify');
          this.noticeService.notice(message);
          this.cdr.detectChanges();
          if (this.openedFromTooltip) {
            this.timesheetService.repaintScheduler(TimesheetWidgetUtils.timesheetDtoToTimesheetRegistry(result));
          }
        } else {
          this.addItem(result);
          message = this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.successCreate');
          this.noticeService.notice(message);
        }
        if (this.data.stopwatchType === StopwatchType.AUTOMATIC) {
          this.timesheetService.updateUIStopwatchService(false, result);
        }
        this.clearBusy();
        this.saveDialog().close();
        this.cdr.detectChanges();
      },
      (error) => {
        if (error.status === 409) {
          if (error.error === 'dates-spliced') {
            ErrorHandling.notifyMessage(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.datesSpliced'), this.navLang);
          } else if (error.error === 'outof-limit') {
            ErrorHandling.notifyMessage(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.dateOutOfLimit'), this.navLang);
          } else if (error.error.stopwatch_running_message === 'stopwatch-dates-spliced') {
            const registryDate = DateUtil.safe(error.error.registry[0].date);
            ErrorHandling.notifyMessage(
              this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', { date: DateUtil.format(registryDate, 'DD/MM/YYYY') }),
              this.navLang
            );
            this.enableStopwatch = true;
          }
          this.busy = false;
          this.cdr.detectChanges();
        } else {
          ErrorHandling.notifyError(error, this.navLang);
          this.reject();
          this.clearBusy();
        }
      }
    );
    this.hasExternalDataSource = false;
    this.cdr.detectChanges();
  }

  //Se agrego esta funcionalidad para "gestionar" de manera "manual" cuando un formControl es invalido
  setInvalidadControls(formStatus: InvalidFormStatus): void {
    for (const controName of formStatus.controls) {
      if (controName === 'plannedActivity') {
        this.addErrorClass = true;
        this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'invalid-form'));
      }
    }
    this.cdr.detectChanges();
  }

  tagsArray(tags: string | string[]): string[] {
    return TimesheetWidgetUtils.getTagsArray(tags);
  }

  tagsArrayFixed(tags: string | string[]): string[] {
    const t: string[] = TimesheetWidgetUtils.getTagsArray(tags);
    const arr = this.fixedTags.concat(t);
    return arr.length ? arr : null;
  }

  fixTags(tags = this.data.tags): string {
    tags = this.form.controls.tags.value;
    return TimesheetWidgetUtils.fixTags(tags);
  }

  endUpdated(evt: string | Date): void {
    this.changeFieldMap.end = true;
    // Fix: Solo se pueden capturar registros en intérvalos de 5 minutos (Como se muestra en el timePicker)
    const fixedEnd = TimesheetWidgetUtils.fixDate(DateUtil.safe(evt));
    const zeroDate = new Date();
    zeroDate.setHours(0, 0, 0, 0);
    if (fixedEnd.getHours() === zeroDate.getHours() && fixedEnd.getMinutes() === zeroDate.getMinutes()) {
      this.enableStopwatch = true;
    } else {
      this.enableStopwatch = false;
    }
    this.updateValidationForm();
    this.data.end = fixedEnd;
    this.form.patchValue({ end: fixedEnd });
    this.cdr.detectChanges();
    // Se actualizan validaciones del formulario para los campos hora inicio y fin
    this.form.get('end').updateValueAndValidity();
    this.form.get('start').updateValueAndValidity();
  }

  updateValidationForm() {
    if (this.enableStopwatch) {
      this.buttonText = this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'start-stopwatch');
    } else {
      this.buttonText = this.translate.instant('root.common.button.save');
    }
    if (!this.form?.controls) {
      return;
    }
    const startControl = this.form.get('start');
    const endControl = this.form.get('end');
    if (this.enableStopwatch) {
      endControl.setValidators([]);
      startControl.setValidators([]);
    } else {
      endControl.setValidators([Validators.required, this.validatorEnd({ ShouldBeGreater: true }, this.form)]);
      startControl.setValidators([Validators.required, this.validatorStart({ ShouldBeLess: true }, this.form)]);
    }
  }

  /** Método que valida horas máximas y mínimas (En este caso solo se valida hora máxima [ShouldBeLess]) */
  public onValidationFailed() {
    this.form.get('end').setErrors({ ShouldBeLess: true });
    this.form.get('end').updateValueAndValidity();
  }

  startUpdated(evt: string | Date): void {
    this.changeFieldMap.start = true;
    // Fix: Solo se pueden capturar registros en intérvalos de 5 minutos (Como se muestra en el timePicker)
    const fixedStart = TimesheetWidgetUtils.fixDate(DateUtil.safe(evt));
    this.data.start = fixedStart;
    this.form.patchValue({ start: fixedStart });
    this.cdr.detectChanges();
    // Se actualizan validaciones del formulario para los campos hora inicio y fin
    this.form.get('end').updateValueAndValidity();
    this.form.get('start').updateValueAndValidity();
  }

  chipRemoved(evt: IBaseChipEventArgs): void {
    if (!this.data.tags || !this.form.controls.tags.value) {
      return;
    }
    this.data.tags = this.data.tags
      .toString()
      .replace(new RegExp(`s*${evt.owner.id}s*`), ' ')
      .replace(/\s+/g, ' ');
    this.form.patchValue({ tags: this.data.tags });
    this.cdr.detectChanges();
  }

  public deleteRegistry(timesheetId: number): Promise<boolean> {
    this.busy = true;
    const d = new Deferred<boolean>();
    this.dialogService
      .confirm(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.confirm-message'))
      .then(() => {
        this.service.post({ url: `timesheet/delete/${timesheetId}`, cancelableReq: this.$destroy, postBody: null, options: null, handleFailure: false }).subscribe(
          (result: boolean) => {
            d.resolve(result);
            this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.deleted-messaje'));
            this.clearBusy();
          },
          (error) => {
            if (error.status === 409 && error.error === 'outof-limit') {
              ErrorHandling.notifyMessage(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.dateOutOfLimit'), this.navLang);
            } else {
              ErrorHandling.notifyError(error, this.navLang);
            }
            d.resolve(false);
            this.clearBusy();
          }
        );
      })
      .catch(() => {
        d.resolve(false);
        this.clearBusy();
      });
    return d.promise;
  }

  public openNew(
    plannedType: PlannedType,
    pendingRecordId?: number,
    comment?: string,
    tags?: string[],
    date?: Date,
    dataActivityPlanned?: TimesheetActivityPlanned,
    enableStopwatch?: boolean,
    disablePlannedActivityField?: boolean,
    onOpenedWindow?: () => void
  ): Promise<TimesheetDto> {
    this.dialogOpen = true;
    this.cdr.detectChanges();
    this.saveDialog().open();
    this._def = new Deferred<TimesheetDto>();
    this.setUp().then(() => {
      this.isToday = DateUtil.isToday(date);
      this.disablePlannedActivityField = disablePlannedActivityField;
      this.showMoreTags = false;
      this.departmenName = Session.getBusinessUnitDepartmentName();
      this.cdr.detectChanges();
      this.enableStopwatch = enableStopwatch;
      this.cdr.detectChanges();
      this.getSystemLinkInfo(dataActivityPlanned);
      this.clearData(pendingRecordId, comment, tags, date, dataActivityPlanned);
      this.updatePlannerType(plannedType, dataActivityPlanned || null);
      // Focus tags
      setTimeout(() => {
        const registry = this.registry();
        DomUtil.adjustTextArea(registry?.nativeElement, 240, 46);
        if ((this.data.clientRecordLocked && this.data.plannerRecordLocked && this.data.taskRecordLocked) || this.isreadOnlyRecord) {
          registry?.nativeElement?.focus();
        } else {
          this.clientId().nativeElement.focus();
        }
        if (typeof onOpenedWindow === 'function') {
          onOpenedWindow();
        }
      }, 100);
    });
    return this._def.promise;
  }

  public disabledFieldsOnStopwatchUpdate() {
    this.updateValidationForm();
    if (!this.isStopwatchAvailable) {
      return;
    }
    switch (this.data.stopwatchType) {
      case StopwatchType.AUTOMATIC:
        this.startPicker().disabled = this.data.hasEnableStopwatch;
        this.endPicker().disabled = false;
        this.isUpdateTsFromSw = true;
        break;
      case StopwatchType.MANUAL:
        this.startPicker().disabled = false;
        this.endPicker().disabled = false;
        this.isUpdateTsFromSw = true;
        break;
    }
  }

  private getSystemLinkInfo(dataActivityPlanned: TimesheetActivityPlanned) {
    if (dataActivityPlanned?.activityPlannedId) {
      const taskActivity = this.tasksActivities.find((task) => task.activityPlannedId === dataActivityPlanned.activityPlannedId);
      if (taskActivity?.systemLinkAvailable) {
        dataActivityPlanned.systemLinkId = taskActivity.mainSystemId;
        dataActivityPlanned.systemLinkLabel = taskActivity.mainSystemLinkLabel;
        dataActivityPlanned.systemLinkValue = taskActivity.mainSystemLinkValue;
      }
    }
  }
  public openEdit(data: TimesheetDto, isOpenedFromTooltip = false): Promise<TimesheetDto> {
    this.dialogOpen = true;
    this.cdr.detectChanges();
    this.saveDialog().open();
    this._def = new Deferred<TimesheetDto>();
    this.setUp(data).then(() => {
      if (data?.registry) {
        data.registry = data.registry.trim();
      }
      this.showMoreTags = false;
      this.departmenName = data.businessUnitDescription || Session.getBusinessUnitDepartmentName();
      this.openedFromTooltip = isOpenedFromTooltip;
      this.enableStopwatch = false;
      this.buttonText = this.translate.instant('root.common.button.save');
      this.dateEditSelected = data.date as Date;
      const plannedType = this.evaluatePlannedType(data);
      this.updatePlannerType(plannedType);
      this.clearData(data);
      this.isreadOnlyRecord = data.recordReadonly ? data.recordReadonly : false;
      this.checkDisableFields();
      setTimeout(() => {
        this.refreshTagColors();
        const registry = this.registry();
        DomUtil.adjustTextArea(registry?.nativeElement, 240, 46);
        if (!this.isreadOnlyRecord) {
          registry?.nativeElement?.focus();
        }
      }, 300);
      this.form.updateValueAndValidity();
    });
    return this._def.promise;
  }

  private updatePlannerType(plannerType: PlannedType, timesheetActivity?: TimesheetActivityPlanned) {
    this.plannedType = plannerType;
    let activity: PlannedActivityValue;
    if (timesheetActivity) {
      activity = {
        mainSystemLinkValue: timesheetActivity.systemLinkValue,
        mainSystemId: timesheetActivity.systemLinkId,
        mainSystemLinkLabel: timesheetActivity.systemLinkLabel,
        mainSystemUrl: '',
        mainSystemRegExp: '',
        systemLinkAvailable: false,
        pendingRecordId: timesheetActivity.pendingRecordId,
        text: timesheetActivity.activityPlannedDescription,
        value: timesheetActivity.activityPlannedId,
        clientId: timesheetActivity.clientId,
        clientDescription: timesheetActivity.clientDescription,
        plannerId: timesheetActivity.plannerId,
        plannerDescription: timesheetActivity.plannerDescription,
        activityId: timesheetActivity.activityId,
        activityDescription: timesheetActivity.activityDescription
      };
    } else {
      activity = null;
    }
    this.form.patchValue({ plannedActivity: activity }, { emitEvent: false });
    const plannedActRequired = this.getPlannedActRequired();
    this.form.controls.plannedActivity.setValidators(plannedActRequired);
    this.cdr.detectChanges();
  }

  private getPlannedActRequired(): (control: AbstractControl) => ValidationErrors | null {
    if (this.plannedType === PlannedType.PLANNED) {
      return Validators.required;
    }
    return undefined;
  }

  private getEntityDataSave(): TimesheetDto {
    const date = DateUtil.safe(this.form.controls.date.value);
    if (date) {
      date.setSeconds(0);
      date.setMilliseconds(0);
    }
    const start = DateUtil.safe(this.form.controls.start.value);
    if (start) {
      start.setSeconds(0);
      start.setMilliseconds(0);
    }
    const end = DateUtil.safe(this.form.controls.end.value);
    if (end) {
      end.setSeconds(0);
      end.setMilliseconds(0);
    }
    let stopwatchLocalTimeStart = new Date();
    if (start.getDate() !== stopwatchLocalTimeStart.getDate()) {
      const dateNotToday = new Date(stopwatchLocalTimeStart.getTime());
      dateNotToday.setDate(start.getDate());
      dateNotToday.setMonth(start.getMonth());
      stopwatchLocalTimeStart = dateNotToday;
    }
    return {
      systemLinkId: this.data.systemLinkId,
      systemLinkValue: this.form.controls.systemLinkValue.value,
      systemLinkLabel: this.data.systemLinkLabel || null,
      timesheetId: this.data.timesheetId,
      clientId: this.form.controls.clientId.value,
      clientDescription: this.data.clientDescription,
      plannerId: this.form.controls.plannerId.value,
      plannerDescription: this.data.plannerDescription,
      activityId: this.form.controls.taskId.value,
      activityDescription: this.data.activityDescription,
      pendingRecordId: this.data.pendingRecordId,
      registry: this.form.controls.registry.value,
      uid: this.form.controls.uid.value,
      date: date,
      start: start,
      end: end,
      tags: this.form.controls.tags.value || '',
      fileIds: this.getFileIds(),
      clientRecordLocked: this.data.clientRecordLocked,
      plannerRecordLocked: this.data.plannerRecordLocked,
      taskRecordLocked: this.data.plannerRecordLocked && this.data.taskRecordLocked,
      workedMinutes: null,
      stopwatchLocalTimeStart: stopwatchLocalTimeStart,
      activityPlannedId: this.data.activityPlannedId,
      activityPlannedCode: this.data.activityPlannedCode,
      activityPlannedDescription: this.data.activityPlannedDescription,
      activityPlannedModule: this.data.activityPlannedModule,
      hasEnableStopwatch: this.enableStopwatch,
      stopwatchType: StopwatchType.MANUAL,
      plannerStatus: this.data.plannerStatus
    };
  }

  private stringColors(str: string): {
    color: string;
    bgColor: string;
  } {
    return stringColors(str, 175);
  }

  refreshTagColors(): void {
    this.data.tags = this.form.controls.tags.value;
    for (const tag of this.tagsArray(this.data.tags)) {
      this.colorCache[tag] = this.stringColors(tag);
    }
  }

  /** Método para validar que la hora fin siempre sea mayor */
  private validatorEnd(error: ValidationErrors, form: UntypedFormGroup): ValidatorFn {
    return (control: AbstractControl): Record<string, any> => {
      if (!control.value) {
        return {};
      }
      if (form) {
        const start = form.get('start');
        // Se muestra error cuando la fecha fin es igual o menor que la de inicio
        if (start.value && control.value && control.value.getTime() <= start.value.getTime()) {
          return error;
        }
      }
      return {};
    };
  }

  /** Método para validar que la hora de inicio siempre sea menor */
  private validatorStart(error: ValidationErrors, form: UntypedFormGroup): ValidatorFn {
    return (control: AbstractControl): Record<string, any> => {
      if (!control.value) {
        return {};
      }
      if (form) {
        const end = form.get('end');
        if (!end.valid) {
          return {};
        }
        // Se muestra error cuando la fecha fin es igual o menor que la de inicio
        if (end.value && control.value && control.value.getTime() >= end.value.getTime()) {
          return error;
        }
      }
      return {};
    };
  }

  private clearBusy(): void {
    this.form.reset();
    this.form.markAsPristine();
    this.form.markAsUntouched();
    this.busy = false;
    this.cdr.detectChanges();
  }

  private reject(): void {
    if (this._def) {
      this._def.reject();
    }
  }

  private addItem(item: TimesheetDto): void {
    TimesheetWidgetUtils.fixItem(item);
    item = this.validDescriptions(item);
    this._def.resolve(item);
    this.saved.emit(item);
  }

  private replaceItem(item: TimesheetDto): void {
    TimesheetWidgetUtils.fixItem(item);
    item = this.validDescriptions(item);
    this._def.resolve(item);
    this.timesheetService.modified(item);
  }

  private validDescriptions(item: TimesheetDto): TimesheetDto {
    if (item.clientDescription === null && item.clientId !== null) {
      const client = this.clients.find((t) => t.value === item.clientId);
      item.clientDescription = client !== null ? client.text.toString() : null;
    }
    if (item.plannerDescription === null && item.plannerId !== null) {
      const planner = this.planners.find((t) => t.value === item.plannerId);
      item.plannerDescription = planner !== null ? planner?.text.toString() : null;
    }
    if (item.activityDescription === null && item.activityId !== null) {
      const task = this.tasks.find((t) => t.value === item.activityId);
      item.activityDescription = task !== null ? task.text.toString() : null;
    }
    return item;
  }

  public selectNow(timePicker: IgxTimePickerComponent) {
    const now = new Date(Date.now());
    if (this.dateEditSelected !== null && this.dateEditSelected < DateUtil.today()) {
      now.setDate(this.dateEditSelected.getDate());
    }
    now.setSeconds(0);
    now.setMilliseconds(0);
    timePicker.value = now;
    timePicker.close();
  }

  toggleTags() {
    this.showTags = !this.showTags;
    this.cdr.detectChanges();
  }

  private computeIsOutsideTheLimit(target: number | string | Date = this.currentSelectedDate): boolean {
    if (target === null || typeof target === 'undefined') {
      return false;
    }
    const targetDate = DateUtil.safe(target);
    if (targetDate === null || typeof targetDate === 'undefined') {
      return false;
    }
    return ConfigApp.getTimeLimitToModifyTimesheet() > targetDate;
  }

  loadToday(): void {
    if (!this.loadedDataSource || this.isreadOnlyRecord) {
      return;
    }
    this.currentSelectedDate = new Date(Date.now());
    this.setTitleCurrentDate();
    const start = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.start));
    const end = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.end));
    this.form.patchValue({
      date: this.currentSelectedDate,
      start: start,
      end: end
    });
    // Dentro del límite para modificar
    this.isOutsideTheLimit = this.computeIsOutsideTheLimit();
    this.form.updateValueAndValidity();
    this.checkDisableFields();
  }

  loadPreviousDay(): void {
    if (!this.loadedDataSource || this.isreadOnlyRecord) {
      return;
    }
    this.currentSelectedDate = DateUtil.subtractDays(this.currentSelectedDate, 1);
    this.setTitleCurrentDate();
    const start = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.start));
    const end = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.end));
    this.form.patchValue({
      date: this.currentSelectedDate,
      start: start,
      end: end
    });
    TimesheetWidgetUtils.LAST_END = end;
    // Dentro del límite para modificar
    this.isOutsideTheLimit = this.computeIsOutsideTheLimit();
    this.form.updateValueAndValidity();
    this.checkDisableFields();
  }

  loadNextDay(): void {
    if (!this.loadedDataSource || this.isreadOnlyRecord) {
      return;
    }
    this.currentSelectedDate = DateUtil.addOneDay(this.currentSelectedDate);
    this.setTitleCurrentDate();
    const start = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.start));
    const end = this.fixDate(this.currentSelectedDate, DateUtil.safe(this.form.value.end));
    this.form.patchValue({
      date: this.currentSelectedDate,
      start: start,
      end: end
    });
    TimesheetWidgetUtils.LAST_END = end;
    // Dentro del límite para modificar
    this.isOutsideTheLimit = this.computeIsOutsideTheLimit();
    this.form.updateValueAndValidity();
    this.checkDisableFields();
  }

  setTitleCurrentDate(): void {
    if (!DateUtil.isToday(this.currentSelectedDate)) {
      this.titleCurrentSelectedDate = this.datePipe.transform(new Date(this.currentSelectedDate), 'dd/MM/yyyy').substring(0, 5);
    } else {
      this.titleCurrentSelectedDate = this.todayLabel;
    }
  }

  /**
   * Método para validar si dateToFix es del mismo día del currentDate (dateValid), en caso de no serlo se corrige
   * Este método sirve para insertar valores correctos en los campos de hora de inicio y fin y calcular correctamente los registros empalmados
   * @param d CurrentDate
   * @param dateToFix Start or End dates
   */
  fixDate(d: Date, dateToFix: Date): Date {
    const dateValid = new Date(DateUtil.safe(d).getTime());
    if (DateUtil.isSameDay(dateValid, dateToFix)) {
      return dateToFix;
    }
    dateValid.setHours(dateToFix.getHours());
    dateValid.setMinutes(dateToFix.getMinutes());
    dateValid.setSeconds(0);
    return new Date(dateValid.getTime());
  }

  /**
   * Metodo para obtener los ID's de "fileData"
   * @returns
   */
  getFileIds(): number[] {
    const fileData = this.fileData();
    if (!fileData.value) {
      return [];
    }
    return fileData.value.map((item) => {
      return item.id;
    });
  }

  /** Método para validar los campos de Cliente - Proyecto - Tarea */
  invalid(fieldName: string): boolean {
    const field = this.form.get(fieldName);
    if (field) {
      let isInvalid = !field.valid && field.touched;
      if (field.status === 'DISABLED') {
        isInvalid = field.invalid && field.touched;
      }
      return isInvalid;
    }
    console.error('Unable to validate field: ', fieldName);
    return false;
  }

  private updateFixedTags(newTags: string | string[]) {
    const arrayTags: string[] = TimesheetWidgetUtils.getTagsArray(newTags);
    this.fixedTags = arrayTags.filter((tag) => TimesheetWidgetUtils.isRemovableTag(tag));
    const tagsClone = arrayTags.filter((tag) => !TimesheetWidgetUtils.isRemovableTag(tag));
    this.data.tags = TimesheetWidgetUtils.fixTags(tagsClone);
    this.form.patchValue({ tags: this.data.tags });
  }

  onPlannedActivityChange(value: PlannedActivityValue) {
    this.addErrorClass = false;
    this.plannedType = this.evaluatePlannedType(value);
    if (value) {
      this.data.plannerStatus = value.plannerStatus;
      if ((value.activityId || value.taskId) && value.plannerStatus === PlannerStatus.ACTIVE) {
        this.data.taskRecordLocked = true;
      } else {
        this.data.taskRecordLocked = false;
      }
      if (value.plannerId && value.plannerStatus === PlannerStatus.ACTIVE) {
        this.data.plannerRecordLocked = true;
      } else {
        this.data.plannerRecordLocked = false;
      }
      if (value.clientId) {
        this.data.clientRecordLocked = true;
      } else {
        this.data.clientRecordLocked = false;
      }
      this.updateMissingDataSource(value, value.plannerStatus === PlannerStatus.INACTIVE);
      if (typeof value?.pendingRecordId === 'undefined') {
        value.pendingRecordId = null;
      }
      this.data.pendingRecordId = +value.pendingRecordId;
      this.data.systemLinkId = value.mainSystemId;
      this.data.activityPlannedId = value.activityPlannedId;
      this.data.activityPlannedDescription = value.activityPlannedDescription;
      if (
        typeof value.mainSystemLinkValue === 'string' &&
        (this.data.systemLinkValue === null ||
          typeof this.data.systemLinkValue === 'undefined' ||
          this.data.systemLinkValue === '' ||
          value.mainSystemLinkValue !== this.data.systemLinkValue)
      ) {
        this.data.systemLinkValue = value.mainSystemLinkValue;
        this.form.patchValue({ systemLinkValue: value.mainSystemLinkValue });
      }
      this.data.systemLinkLabel = value.mainSystemLinkLabel || '';
      const newTags = [value.activityPlannedCode];
      if (this.data?.tags?.length > 0) {
        for (const tag1 of TimesheetWidgetUtils.getTagsArray(this.data.tags).filter((tag) => !TimesheetWidgetUtils.isRemovableTag(tag))) {
          if (tag1 !== value.activityPlannedCode && tag1 !== value.activityPlannedModule.toUpperCase()) {
            newTags.push(tag1);
          }
        }
      }
      this.updateFixedTags(newTags);
      if ((value.activityId !== null && typeof value.activityId !== 'undefined') || (value.value !== null && typeof value.value !== 'undefined')) {
        this.setValuesForm(value.clientId, value.plannerId, this.isNaN(+value.activityId) ? +value.taskId : +value.activityId);
      } else {
        this.setValuesForm(null, null, null);
      }
      this.updateLastEnd(true);
    } else {
      this.fixedTags = [];
      this.data.pendingRecordId = null;
      this.data.clientRecordLocked = false;
      this.data.plannerRecordLocked = false;
      this.data.taskRecordLocked = false;
      this.setValuesForm(null, null, null);
      this.updateLastEnd(false);
    }
  }

  onClearPlannedActivityChange(value: PlannedActivityValue) {
    this.data.activityPlannedId = value?.activityPlannedId;
    this.data.activityPlannedDescription = value?.activityDescription;
    this.data.activityPlannedCode = value?.activityPlannedCode;
    this.data.activityPlannedModule = value?.activityPlannedModule;
    this.data.pendingRecordId = value?.pendingRecordId;
    if (value === null) {
      this.data.clientRecordLocked = false;
      this.data.plannerRecordLocked = false;
      this.data.taskRecordLocked = false;
    }
  }

  private updateMissingDataSource(data: PlannedActivityData, skipInactivePlanners: boolean): void {
    if (data.clientId !== null && typeof data.clientId !== 'undefined') {
      const client = this.clients.find((client) => client.value === data.clientId);
      if (!client) {
        const newData: PlannedActivityData = cloneObject(data);
        newData.activityId = null;
        newData.plannerId = null;
        const newClient = newData as ClientValue;
        newClient.value = data.clientId;
        newClient.text = data.clientDescription;
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newClient['temporalRegistry'] = true;
        this.clients.push(newClient);
        this.clients = this.clients.slice(0, this.clients.length);
        this.cdr.detectChanges();
      }
    }
    if (data.plannerId !== null && typeof data.plannerId !== 'undefined') {
      const planner = this.planners.find((planner) => planner.value === data.plannerId);
      if (!planner && !skipInactivePlanners) {
        const newPlanner: TextPlannerValue = cloneObject(data);
        newPlanner.value = data.plannerId;
        newPlanner.text = data.plannerDescription;
        newPlanner.activityId = null;
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newPlanner['temporalRegistry'] = true;
        this.planners.push(newPlanner);
        if (this.plannerId()) {
          this.filteredPlanners = this.filterPlannersByClient(data.clientId);
        }
        this.cdr.detectChanges();
      }
    }
    const taskId = this.taskId();
    if (data.activityId !== null && typeof data.activityId !== 'undefined') {
      const task = this.tasks.find((task) => task.value === data.activityId);
      if (!task && !skipInactivePlanners) {
        const newTask: TextPlannerValue = cloneObject(data);
        newTask.value = data.activityId;
        newTask.text = data.activityDescription;
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newTask['temporalRegistry'] = true;
        this.tasks.push(newTask);
        if (taskId) {
          this.filteredTasks = this.filterTaskByPlanners(data.plannerId);
        }
        this.cdr.detectChanges();
      }
    }
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    if (data['taskId'] !== null && typeof data['taskId'] !== 'undefined') {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      const task = this.tasks.find((task) => task.value === data['taskId']);
      if (!task) {
        const newTask: TextPlannerValue = cloneObject(data);
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newTask.value = data['taskId'];
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newTask.text = data['taskDescription'];
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        newTask['temporalRegistry'] = true;
        this.tasks.push(newTask);
        if (taskId) {
          this.filteredTasks = this.filterTaskByPlanners(data.plannerId);
        }
        this.cdr.detectChanges();
      }
    }
    if (data.activityPlannedId !== null && typeof data.activityPlannedId !== 'undefined') {
      const plannedData = this.tasksActivities.find((planned) => planned.value === data.activityPlannedId);
      if (!plannedData) {
        const newTaskActivity: PlannedActivityValue = cloneObject(data);
        newTaskActivity.value = data.activityPlannedId;
        newTaskActivity.text = data.activityPlannedDescription;
        this.tasksActivities.push(newTaskActivity);
      }
    }
  }

  reset(e: IDialogCancellableEventArgs) {
    TimesheetWidgetUtils.LAST_END = new Date();
    if (this.checkSafeClose()) {
      e.cancel = true;
    }
    this.updateValidationForm();
    this.popUpFormRemember.reset();
  }

  private checkSafeClose(): boolean {
    /**
     * ToDo: Poner `closeOnOutsideSelect=true`, pero el dialogo para preguntar si desea cerrar TS, debe mostrarse solo cuando
     *       se cierre el dialogo con esa opción. Actualmente se muestra siempre que se cierra el dialogo (la "X" no debe preguntar).
     */
    const localStoragePreventRemenber = LocalStorageSession.getValue(LocalStorageItem.POP_UP_REMEMBER);
    if (localStoragePreventRemenber === 'null' || localStoragePreventRemenber === null || localStoragePreventRemenber === 'false') {
      for (const f in this.form.controls) {
        if (this.changeFieldMap[f] && !this.isMobilDevice()) {
          this.preventPopup = true;
          return true;
        }
      }
    }
    return false;
  }

  public onChangeFields(fieldName: string): void {
    this.changeFieldMap[fieldName] = true;
  }

  public onClosedDialog(): void {
    if (this._def) {
      this._def.resolve(null);
    }
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    this.clients = this.clients.filter((f) => !!f['temporalRegistry'] === false);
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    this.planners = this.planners.filter((f) => !!f['temporalRegistry'] === false);
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    this.tasks = this.tasks.filter((f) => !!f['temporalRegistry'] === false);
    this.hide();
    this.disablePlannedActivityField = false;
    this._enableStopwatch = true;
    this.changeFieldMap = {};
    this.filteredTasks = [];
    this.filteredPlanners = [];
    this.dialogOpen = false;
    this.timesheetService.closedDialog();
  }

  public showMore(index: number): string {
    if (index >= 15) {
      this.showMoreTags = true;
      return 'none';
    }
    this.showMoreTags = false;
    return '';
  }

  private evaluatePlannedType(data: TimesheetDto | PlannedActivityValue): PlannedType {
    if (!data) {
      return PlannedType.UNPLANNED;
    }
    return data.pendingRecordId !== null && typeof data.pendingRecordId !== 'undefined' ? PlannedType.PLANNED : PlannedType.UNPLANNED;
  }

  private checkDisableFields(): void {
    if (this.isreadOnlyRecord || this.isOutsideTheLimit) {
      this.form.disable();
    } else {
      this.form.enable();
      if (this.form?.controls.clientId && this.form.controls.plannerId && this.form.controls.taskId) {
        if (this.data.clientRecordLocked) {
          this.form.controls.clientId.disable();
        } else {
          this.form.controls.clientId.enable();
        }
        if (this.data.plannerRecordLocked) {
          this.form.controls.plannerId.disable();
        } else {
          this.form.controls.plannerId.enable();
        }
        if (this.data.taskRecordLocked) {
          this.form.controls.taskId.disable();
        } else {
          this.form.controls.taskId.enable();
        }
      }
    }
  }

  public setNewChangePlannerTask(modifiedValue: any, activityPlannedId: number): void {
    if (this.tasksActivities) {
      for (const item of this.tasksActivities.filter((f) => f.activityPlannedId === activityPlannedId)) {
        item.clientId = modifiedValue.clientId ?? null;
        item.plannerId = modifiedValue.plannerId ?? null;
        item.activityId = modifiedValue.activityId ?? null;
      }
    }
  }

  private filterPlannersByClient(value: number): TextPlannerValue[] {
    return this.planners.filter((pl: TextPlannerValue) => {
      return +pl.clientId === +value;
    });
  }

  private filterTaskByPlanners(value: number): TextPlannerValue[] {
    return this.tasks.filter((task: TextPlannerValue) => {
      return +task.plannerId === +value;
    });
  }

  public onCancelPrevent(): void {
    LocalStorageSession.setValue(LocalStorageItem.POP_UP_REMEMBER, this.popUpFormRemember.controls.checkboxPopUpRemember.value);
    this.preventPopup = false;
    this.cdr.detectChanges();
  }

  public onClosePrevent(): void {
    LocalStorageSession.setValue(LocalStorageItem.POP_UP_REMEMBER, this.popUpFormRemember.controls.checkboxPopUpRemember.value);
    this.preventPopup = false;
    this.changeFieldMap = {};
    this.hide();
    this.cdr.detectChanges();
  }
}
