import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { Session } from '@/core/local-storage/session';
import { AppService } from '@/core/services/app.service';
import { NoticeService } from '@/core/services/notice.service';
import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import type { TextHasValue } from '@/core/utils/text-has-value';
import { DatePipe } from '@angular/common';
import { type AfterViewInit, Component, Input, type OnDestroy, type OnInit, inject, input, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  type DateRange,
  GlobalPositionStrategy,
  HorizontalAlignment,
  IgxButtonDirective,
  IgxCardActionsComponent,
  IgxCardComponent,
  IgxCardContentDirective,
  IgxCardHeaderComponent,
  IgxCircularProgressBarComponent,
  IgxDateRangePickerComponent,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxPrefixDirective,
  IgxProgressBarGradientDirective,
  type OverlaySettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivitiesTreeScoreComponent } from '../../activities/activities-tree-score/activities-tree-score.component';
import { MenuWidgetService } from '../../menu/menu-widget/menu-widget.service';
import type { SizeWidget, WidgetConfig } from '../widget-panel/widget-panel.interfaces';
import type { DataSourceCommitmentsSummary } from './commitments-summay-widget.interface';

@Component({
  selector: 'app-commitments-summary-widget',
  templateUrl: './commitments-summary-widget.component.html',
  styleUrls: ['./commitments-summary-widget.component.scss'],
  imports: [
    FormsModule,
    IgxCardComponent,
    IgxCardHeaderComponent,
    IgxDateRangePickerComponent,
    IgxCardContentDirective,
    IgxLinearProgressBarComponent,
    IgxIconComponent,
    IgxCircularProgressBarComponent,
    IgxProgressBarGradientDirective,
    IgxCardActionsComponent,
    IgxButtonDirective,
    IgxPrefixDirective,
    DropdownSearchComponent,
    ActivitiesTreeScoreComponent,
    BnextTranslatePipe
  ]
})
export class CommitmentsSummaryWidgetComponent extends BnextCoreComponent implements OnDestroy, OnInit, i18n, AfterViewInit {
  private service = inject(AppService);

  noticeService = inject(NoticeService);
  datePipe = inject(DatePipe);

  private menuWidgetService = inject(MenuWidgetService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.widgets',
    componentName: 'commitments-summary-widget'
  };

  override get componentPath(): string {
    return CommitmentsSummaryWidgetComponent.LANG_CONFIG.componentPath;
  }

  get urlModule(): string {
    return 'activities/activity';
  }

  private restDateFormat = 'YYYY-MM-DD';
  items: DataSourceCommitmentsSummary = null;
  loading = false;
  users: TextHasValue[] = [];
  currentUserId = null;

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'compact';

  range: DateRange = {
    start: DateUtil.today(),
    end: DateUtil.today()
  };
  needPachBrowser = this.isAppleDevice() && this.isIPhoneNotInstallled();

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public size: SizeWidget = {};
  public readonly header = input<string>(undefined);

  readonly userId = viewChild<DropdownSearchComponent>('userId');

  readonly treeScoreDialog = viewChild('treeScoreDialog', { read: ActivitiesTreeScoreComponent });

  minHeightResponsive = this.windowInnerHeight <= 720 ? 535 : 700;

  mode = 'score';
  deviation = 0;

  overlaySettingsMobile: OverlaySettings = {
    modal: true,
    positionStrategy: new GlobalPositionStrategy({ horizontalDirection: HorizontalAlignment.Center, verticalDirection: VerticalAlignment.Middle })
  };

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.menuWidgetService.requestRefreshWidgetSize.pipe(takeUntil(this.$destroy)).subscribe((refreshData) => {
      this.refreshSize(refreshData.widgets);
      this.detectChanges();
    });
    this.menuWidgetService.newPendingsProgress.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.onNewPendingsProgress();
    });
    this.menuWidgetService.refreshPendingsRequest.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.refresh();
    });
    this.refresh();
  }

  override ngAfterViewInit() {
    this.detectChanges();
  }

  private refreshSize(widgets: readonly WidgetConfig[]): void {
    const widget = widgets.find((item) => item.header === this.header());
    if (widget) {
      this.size = widget.sizeWidget;
    }
  }

  public refresh(): void {
    this.currentUserId = Session.getUserId();
    if (this.currentUserId == null || typeof this.currentUserId === 'undefined') {
      return;
    }
    this.loading = true;
    this.getUsersDataSource(this.currentUserId);
    const today = DateUtil.today();
    this.range.start = DateUtil.monday(today);
    this.range.end = DateUtil.sunday(today);

    this.getDataSource(this.currentUserId);
  }

  detectChanges(): void {
    this.updateUserIdFromCurrentUser();
    this.cdr.detectChanges();
  }

  private updateUserIdFromCurrentUser(): void {
    if (!this.users?.length) {
      return;
    }
    const currentUserData = this.users.find((item) => item.value === this.currentUserId);
    if (currentUserData) {
      this.userId().textFormControl.patchValue(currentUserData.text);
    }
  }

  private getUsersDataSource(userId: number): void {
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: `${this.urlModule}/users-commitments-summary/${userId}`
      })
      .subscribe({
        next: (data: TextHasValue[]) => {
          this.users = data;
          this.loading = false;
          this.detectChanges();
        },
        error: (error) => {
          console.log(error);
          this.loading = false;
        }
      });
  }

  private getDataSource(userId: number): void {
    const startDateRange = DateUtil.format(DateUtil.safe(this.range.start), this.restDateFormat);
    const endDateRange = DateUtil.format(DateUtil.safe(this.range.end), this.restDateFormat);

    this.service
      .get({
        cancelableReq: this.$destroy,
        url: `${this.urlModule}/dataSource-commitments-summary/${startDateRange}/${endDateRange}/${userId}`
      })
      .subscribe({
        next: (info: DataSourceCommitmentsSummary) => {
          this.deviation = this.calculateDeviation(info);
          if (info !== null && Object.keys(info).length !== 0) {
            info = this.fixDecimalItems(info);
          }
          this.items = info;
          this.loading = false;
          this.detectChanges();
        },
        error: (error) => {
          console.log(error);
          this.loading = false;
        }
      });
  }

  private fixDecimalItems(info: DataSourceCommitmentsSummary): DataSourceCommitmentsSummary {
    info.progress = NumberUtil.round(info.progress, 2);
    info.score = NumberUtil.round(info.score, 2);
    info.sumActualHoursCancelled = NumberUtil.round(info.sumActualHoursCancelled, 2);
    info.sumActualHoursPlanned = NumberUtil.round(info.sumActualHoursPlanned, 2);
    info.sumActualHoursUnplanned = NumberUtil.round(info.sumActualHoursUnplanned, 2);
    info.sumPlannedHoursCancelled = NumberUtil.round(info.sumPlannedHoursCancelled, 2);
    info.sumPlannedHours = NumberUtil.round(info.sumPlannedHours, 2);
    return info;
  }

  override langReady(): void {}

  onUpdatedRange(): void {
    if (Object.keys(this.range).length !== 0) {
      this.loading = true;
      this.getDataSource(this.currentUserId);
    }
  }

  onChangeUser(user: any): void {
    if (user !== null) {
      this.currentUserId = user.value;
      this.loading = true;
      this.getDataSource(this.currentUserId);
    }
  }

  private onNewPendingsProgress() {
    if (this.currentUserId != null) {
      this.loading = true;
      this.getDataSource(this.currentUserId);
    }
  }

  goToReport(): void {
    const startDateRange = DateUtil.format(DateUtil.safe(this.range.start), this.restDateFormat);
    const endDateRange = DateUtil.format(DateUtil.safe(this.range.end), this.restDateFormat);
    const url = `menu/activities/commitments-summary/${this.currentUserId}/${startDateRange}--${endDateRange}`;
    this.menuService.navigate(url, Module.ACTIVITY);
  }

  openTreeScoreDialog(): void {
    this.mode = ActivitiesTreeScoreComponent.METRIC.SCORE;
    this.treeScoreDialog().openImplementer({
      implementerId: this.currentUserId,
      start: DateUtil.safe(this.range.start),
      end: DateUtil.safe(this.range.end)
    });
  }

  openTreeDeviationScoreDialog(): void {
    this.mode = ActivitiesTreeScoreComponent.METRIC.DEVIATION;
    this.treeScoreDialog().openDeviation({
      implementerId: this.currentUserId,
      start: DateUtil.safe(this.range.start),
      end: DateUtil.safe(this.range.end)
    });
  }

  calculateDeviation(items: DataSourceCommitmentsSummary) {
    if (items.sumActualHoursUnplanned && items.sumPlannedHours && items.sumPlannedHours !== 0) {
      return NumberUtil.round((items.sumActualHoursUnplanned / items.sumPlannedHours) * 100, 2);
    }
    return NumberUtil.round(0, 2);
  }
}
