import type { CommentMultiSaveComponent } from '@/core/comment-multi-save/comment-multi-save.component';
import type { DocumentMultiSelectComponent } from '@/core/document-multi-select/document-multi-select.component';
import type { DynamicFieldsDTO, FieldChangeData, PersistableDynamicEntity } from '@/core/dynamic-field/field-handler/field-handler.interfaces';
import * as DateUtil from '@/core/utils/date-util';
import { ActivityConditionalUtil } from '@/shared/activities/core/utils/activity-conditional-util';
import { type AfterViewInit, Component, HostBinding, Input, type OnDestroy, type OnInit, ViewChild, inject, input, output, viewChild, viewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup, Validators } from '@angular/forms';
import type { DateRangeDescriptor, IChangeCheckboxEventArgs, IgxAvatarComponent, IgxDatePickerComponent, IgxDialogComponent } from '@infragistics/igniteui-angular';

import type { PeriodicityComponent } from '@/core/periodicity/periodicity.component';
import { AppService } from '@/core/services/app.service';
import { NoticeService } from '@/core/services/notice.service';
import type { DataMap } from '@/core/utils/data-map';
import type { TextHasValue, TextLongValue, UserTextHasValue } from '@/core/utils/text-has-value';

import type { FieldHandlerComponent } from '@/core/dynamic-field/field-handler/field-handler.component';
import type { MultiFileUploadComponent } from '@/core/multi-file-upload/multi-file-upload.component';

import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { DocumentSelectComponent } from '@/core/document-select/document-select.component';
import { FieldDisplayComponent } from '@/core/field-display/field-display.component';
import type { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import { Session } from '@/core/local-storage/session';
import type { SystemLinkComponent } from '@/core/system-link/system-link.component';
import * as NumberUtil from '@/core/utils/number-util';
import { cloneObject, equalsObject, keysObject, shallowClone, sortByObject } from '@/core/utils/object';
import { nameInitials, stringToColour, textColor } from '@/core/utils/string-util';
import { calculateDiffDays, getUuid, setConstraintDates, skipFieldByRecurrence } from '@/modules/activities/activities-add-many/activities-add-many.util';
import { WORKFLOW_MENU_NAME } from '@/modules/menu/menu-definition/menu-definition.builder';
import { CommitmentTask, VerificationAvailable } from '@/shared/activities/core/activities-core.enums';
import { ActivityCoreUtil } from '@/shared/activities/core/activities-core.utils';
import {
  ADD_MANY_LANG_CONFIG,
  DEFAULT_FIELD_DEFINITION,
  clearImplementation,
  clearVerification,
  getActivityController,
  getDefaultDisabledFields,
  getDefaultHiddenRules,
  getDefaultValues,
  getEntityData,
  getParticipantColumns,
  isImplementationAvailable,
  isVerificationAvailable,
  updateCatalogTaskData
} from '@/shared/activities/core/utils/activity-util';
import type { PlannerTaskComponent } from '@/shared/planner/planner-task/planner-task.component';
import { DatePipe } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { TranslateDefaultParser } from '@ngx-translate/core';
import { type Observable, Subject, type Subscription, forkJoin } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import type { ComboComponent } from 'src/app/core/combo/combo.component';
import type { ConditionalDictionary, ConditionalEntity, ConditionalFieldData } from 'src/app/core/conditional-field/conditional-field.interfaces';
import { AutoresizeDirective } from 'src/app/core/directives/autoresize';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { FieldType } from 'src/app/core/field-list/field-list.enums';
import type { FieldDefinition } from 'src/app/core/field-list/field-list.interfaces';
import type { RowSelection } from 'src/app/core/grid-base-select/grid-base-select.interfaces';
import { PeriodicityUtil } from 'src/app/core/periodicity/periodicity-util';
import { PeriodicityType } from 'src/app/core/periodicity/utils/periodicity-type';
import { RestApiModule } from 'src/app/core/rest-api.module';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import type { SimpleComboComponent } from 'src/app/core/simple-combo/simple-combo.component';
import { Deferred } from 'src/app/core/utils/deferred';
import type { Entity } from 'src/app/core/utils/entity';
import { EnumUtil } from 'src/app/core/utils/enum-util';
import type { DocumentLinkedSelectorDTO } from 'src/app/core/utils/interfaces';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { DataSourceResult, DisabledDatesHolder } from 'src/app/shared/activities/activities-add-many.interfaces';
import type { ActivityFlagFields, DisabledFields } from 'src/app/shared/activities/activities-rules.interfaces';
import { FindingActivityType, ImplementationAvailable } from 'src/app/shared/activities/core/activities-core.enums';
import type {
  ActivityConstraints,
  ActivityDateHolder,
  ActivityDetails,
  ActivityLinked,
  ActivityTypeFilllTypeDto,
  BusinessUnitDepartmentEntity,
  DefaultHiddenValues,
  DefaultValues,
  DisabledFieldsAsReadonly,
  HiddenRules,
  ParticipantDto,
  PlannerStartDatesEntity
} from 'src/app/shared/activities/core/activities-core.interfaces';
import type {
  ActivityDataSourceDto,
  ActivityEntity,
  ActivityLinkedDataSource,
  ActivityLinkedSource,
  ActivityLoadDto,
  ITypeAttributes
} from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import { sameImplementerVerifierValidator } from 'src/app/shared/activities/core/utils/same-implementer-verifier.directive';
import type { PlannerTaskData, TextPlannerValue } from 'src/app/shared/planner/planner-task/planner-task.interfaces';
import { MenuFavoritesUtils } from '../../menu/menu-definition/menu-favorites-utils';
import type { FavoriteTaskSaveDTO, QualifiedGenericMenuItem } from '../../menu/menu-main/menu-main.interfaces';
import { ActivitiesAssignConstants } from '../../pendings/activities/activities-assign/activities-assign.constants';
import type { ActivityPendingDto } from '../../pendings/activities/activities-pending/activities-pending.interfaces';
import { ActivityInfoConstant } from '../../pendings/activities/activity-info/activity-info.constant';
import type { TimesheetDataSourceDto } from '../../timesheet/timesheet-add/timesheet-datasource';
import { ActivitiesDetailConstant } from '../activities-detail/activities-detail.constant';
import type { ActivitiesHistoryComponent } from '../activities-history/activities-history.component';

let NEXT_ID = 0;

@Component({
  selector: 'app-activities',
  template: '',
  standalone: true
})
export class ActivitiesComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit, DisabledDatesHolder {
  loader = inject(BnextLoaderActivationService);
  datePipe = inject(DatePipe);
  noticeService = inject(NoticeService);

  api = inject(AppService);

  formBuilder = inject(UntypedFormBuilder);

  activatedRoute = inject(ActivatedRoute);

  initSubscription: Subscription;
  subscriptions: Subscription[] = [];

  override get componentPath(): string {
    return 'modules.activities';
  }

  readonly defaultFieldsDef = DEFAULT_FIELD_DEFINITION;
  readonly ignoreKeys: Set<string> = new Set<string>(['uploader', 'item']);
  protected _addSubtaskByDepartment = false;
  protected _participants: ParticipantDto[];
  protected _isLoadingDuplicateActivity = false;
  protected _timesheetDs: TimesheetDataSourceDto;

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @HostBinding('attr.id')
  @Input()
  public id = `activities-${NEXT_ID++}`;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public isNew = true;

  readonly descriptionSize = viewChild('description', { read: AutoresizeDirective });
  readonly sucessSaveDialog = viewChild<IgxDialogComponent>('sucessSaveDialog');
  readonly failSaveDialog = viewChild<IgxDialogComponent>('failSaveDialog');

  readonly files = viewChild<MultiFileUploadComponent>('files');
  readonly fillForm = viewChild<DocumentSelectComponent>('fillForm');

  // TODO: Skipped for migration because:
  //  This query is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @ViewChild('documents')
  public documents: DocumentMultiSelectComponent = null;

  readonly comments = viewChild<CommentMultiSaveComponent>('comments');

  readonly history = viewChild<ActivitiesHistoryComponent>('history');
  readonly dynamic = viewChild<FieldHandlerComponent>('dynamic');
  readonly _implementation = viewChild<IgxDatePickerComponent>('implementation');
  readonly _implementationPeriodicity = viewChild<PeriodicityComponent>('implementationPeriodicity');
  readonly _verification = viewChild<IgxDatePickerComponent>('verification');
  readonly _verificationPeriodicity = viewChild<PeriodicityComponent>('verificationPeriodicity');
  readonly implementationStartDate = viewChild<IgxDatePickerComponent>('implementationStartDate');
  readonly plannerTask = viewChild<PlannerTaskComponent>('plannerTask');
  readonly systemLinks = viewChild<SystemLinkComponent>('systemLinks');

  readonly participants = viewChild<GridMultiSelectComponent>('participants');
  readonly combo = viewChild<ComboComponent>('combo');
  readonly comboPreImplementer = viewChild<ComboComponent>('comboPreImplementer');
  readonly comboVerifier = viewChild<SimpleComboComponent>('comboVerifier');
  readonly avatar = viewChildren<IgxAvatarComponent>('avatar');
  readonly fieldDisplay = viewChildren(FieldDisplayComponent);
  public startDisabledDates: DateRangeDescriptor[] = [];
  public implementationDisabledDates: DateRangeDescriptor[] = [];
  public implementationPeriodicityDisabledDates: DateRangeDescriptor[] = [];
  public verificationDisabledDates: DateRangeDescriptor[] = [];
  public verificationPeriodicityDisabledDates: DateRangeDescriptor[] = [];
  public showHelp = false;
  public savingComment = false;
  public savingDocument = false;
  public commitmentTask = 0;
  public activitiesTitle = '';
  public relationshipTabTitle = 'Subtareas';
  public childsTabTitle = 'Subtareas';
  public implementationDateLabel: string = null;
  public reminderDateLabel: string = null;
  public addSubtaskLabel = 'Subtarea';
  public activitiesSubtitle;
  public module: Module = Module.ACTIVITY;
  public businessUnitId = null;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public linkedSource: ActivityLinkedSource = null;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public linkedDataSource: ActivityLinkedDataSource = null;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public activityDetails: ActivityDetails = null;
  public parser = new TranslateDefaultParser();
  public participantsColumns: GridColumn[] = [];
  public createdBy = null;
  public descriptionReadOnlyLabel = null;
  public LangConfigActivityInfo = ActivityInfoConstant.LANG_CONFIG;
  public defaultHiddenValues: DefaultHiddenValues = {};
  public defaultHiddenRules: HiddenRules = getDefaultHiddenRules();
  public defaultDisabledFields: DisabledFields = getDefaultDisabledFields();
  public defaultValues: DefaultValues = getDefaultValues();
  public hiddenRules: HiddenRules = { ...this.defaultHiddenRules };
  public hiddenFields: string[] = [];
  public conditionalDictionary: ConditionalDictionary = null;
  public conditionalBusyFields = new Set<string>();
  public disabledFields: DisabledFields = { ...this.defaultDisabledFields };
  public constraints: ActivityConstraints = null;
  public updatingHistory = false;
  get isActivityRecurrencesAvailable() {
    return this.module === Module.ACTIVITY;
  }

  get maxOrderSize(): number {
    return ActivitiesAssignConstants.MAX_ORDER_SIZE;
  }

  public requiredRules: ActivityFlagFields = {
    activityOrder: false,
    activityResolutionId: false,
    actualHours: false,
    anticipationAttendDays: false,
    belongSeries: false,
    businessUnitDepartmentId: false,
    businessUnitId: false,
    cancellationReason: false,
    code: false,
    comments: false,
    commitmentTask: false,
    daysToVerify: false,
    description: false,
    documents: false,
    dynamicFields: false,
    enableDeliverySetUp: false,
    files: false,
    fillForm: false,
    fillType: false,
    finishVerificationOn: false,
    followUpImplementationDelay: false,
    mustUpdateImplementationAtReturn: false,
    implementation: false,
    implementationPeriodicity: false,
    implementer: false,
    preImplementer: false,
    isPlanned: true,
    objectiveId: false,
    participants: false,
    plannedHours: false,
    plannedImplementationWeek: false,
    plannedVerificationWeek: false,
    plannerTask: false,
    priority: false,
    reminder: false,
    categoryId: false,
    source: false,
    startVerificationOn: false,
    systemLinks: false,
    taskCategoryId: false,
    taskDeliveryTypeId: false,
    typeId: false,
    verification: false,
    verificationPeriodicity: false,
    verifier: false
  };

  public editableRules: ActivityFlagFields = {
    activityOrder: false,
    activityResolutionId: false,
    actualHours: false,
    anticipationAttendDays: false,
    belongSeries: false,
    businessUnitDepartmentId: true,
    businessUnitId: true,
    cancellationReason: false,
    code: false,
    comments: true,
    commitmentTask: false,
    daysToVerify: true,
    description: true,
    documents: true,
    dynamicFields: false,
    enableDeliverySetUp: false,
    files: true,
    fillForm: true,
    fillType: true,
    finishVerificationOn: true,
    followUpImplementationDelay: false,
    mustUpdateImplementationAtReturn: false,
    implementation: true,
    implementationPeriodicity: true,
    implementer: true,
    preImplementer: true,
    isPlanned: false,
    objectiveId: true,
    participants: true,
    plannedHours: false,
    plannedImplementationWeek: false,
    plannedVerificationWeek: false,
    plannerTask: false,
    priority: true,
    reminder: false,
    categoryId: true,
    source: true,
    startVerificationOn: true,
    systemLinks: false,
    taskCategoryId: false,
    taskDeliveryTypeId: false,
    typeId: true,
    verification: true,
    verificationPeriodicity: true,
    verifier: true
  };

  public saveButton = {
    comments: false,
    description: false,
    implementer: false,
    verifier: false
  };

  private _belongSeries = 0;

  public savedDirectly = {
    implementation: false,
    implementationPeriodicity: false,
    verification: false,
    verificationPeriodicity: false
  };

  private _temporary: {
    startDate: Date;
    implementer?: DataMap<number>;
    preImplementer?: DataMap<number>;
  } = {
    startDate: null,
    implementer: {},
    preImplementer: {}
  };

  _workflowId = 0;

  // TODO: Skipped for migration because:
  //  The input cannot be migrated because the field is overridden by a subclass.
  @Input()
  public skipInit = false;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  isPlannedSwitchAvailable = true;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  isPlannedHoursAvailable = true;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  enableHiddenFields = false;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  enableConditionalFields = true;

  readonly isVerifierRemoved = input(false);

  readonly isHeaderAvailable = input(true);

  readonly isButtonSectionAvailable = input(true);

  readonly descriptionLabel = input<string>(null);

  readonly typeLabel = input<string>(null);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  ownerNamesLabel: string = null;

  readonly businessUnitDepartmentLabel = input<string>(null);

  readonly moduleMenuOptions = input<DropdownMenuItem[]>([]);

  readonly moduleMenuOptionsAvailable = input<string[]>([]);

  readonly descriptionIcon = input('done');

  readonly showDisabledAsReadonly = input<DisabledFieldsAsReadonly>({});

  private _verifiers: TextLongValue[];
  private _verifierId: number | string;
  private _implementers: TextHasValue[];
  private _implementerIds: number[] = [];
  private _preImplementers: TextHasValue[];
  private _preImplementerIds: number[] = [];

  readonly changedBusinessUnitDepartment = output<BusinessUnitDepartmentEntity>();

  readonly changedPlannerStartDates = output<PlannerStartDatesEntity>();

  readonly typesChanged = output<ActivityTypeFilllTypeDto[]>();

  readonly changedBusinessUnitId = output<number>();

  mainForm: UntypedFormGroup;
  priorities: TextHasValue[];
  clients: TextLongValue[] = [];
  planners: TextPlannerValue[] = [];
  tasks: TextPlannerValue[] = [];
  categories: TextHasValue[];
  sources: TextHasValue[];
  objectives: TextHasValue[];
  types: TextHasValue[];
  classifications: TextHasValue[];
  fillTypes: TextHasValue[] = [];
  resolutions: TextLongValue[] = [];
  businessUnitDepartments: BusinessUnitDepartmentEntity[] = [];
  parentActivityCode: string;
  parentPlannerName: string;
  parentActivityProgress: number = null;
  partitions: ActivityPendingDto[];
  events: ActivityPendingDto[];
  siblings: ActivityPendingDto[];
  childs: ActivityPendingDto[];
  plannedImplementations: ActivityPendingDto[];
  hasFillForm = false;
  fillTypesLang: DataMap = {};
  progress: number;
  code: string;
  periodicityShortTag: string;
  busy = true;
  touched = false;

  get wholeImplementers(): TextHasValue[] {
    return this._implementers || [];
  }

  get wholeVerifiers(): TextHasValue[] {
    return this._verifiers || [];
  }

  get implementers(): TextHasValue[] {
    if (this.isVerifierRemoved()) {
      return this._implementers || [];
    }
    let c = this._implementers || [];
    if (this.isNew) {
      if (this.defaultHiddenValues.verifier) {
        c = c.filter((v) => +v.value !== this.defaultHiddenValues.verifier);
      }
    }
    if (this.mainForm?.controls.verifier?.value) {
      c = c.filter((v) => +v.value !== +this.mainForm.controls.verifier.value);
    }
    return c || [];
  }

  set implementers(_implementers: TextHasValue[]) {
    if (_implementers) {
      for (const i of _implementers) {
        i.value = +i.value;
      }
    }
    this._implementers = _implementers;
  }

  get preImplementers(): TextHasValue[] {
    return this._preImplementers || [];
  }

  set preImplementers(_preImplementers: TextHasValue[]) {
    if (_preImplementers) {
      for (const i of _preImplementers) {
        i.value = +i.value;
      }
    }
    this._preImplementers = _preImplementers;
  }

  tempImplementers: TextHasValue[];
  tempPreImplementers: TextHasValue[];

  get verifiers(): TextLongValue[] {
    let c = this._verifiers || [];
    if (this.defaultHiddenValues.implementer) {
      c = c.filter((v) => +v.value !== this.defaultHiddenValues.implementer);
    }
    if (this._implementerIds.length > 0) {
      c = c.filter((v) => this._implementerIds.indexOf(+v.value) === -1);
    }
    return c || [];
  }

  set verifiers(_verifiers: TextLongValue[]) {
    this._verifiers = _verifiers;
  }

  get activity(): ActivityDateHolder {
    return {
      belongSeries: this.belongSeries,
      implementation: DateUtil.safe(this.implementation?.value || null),
      implementationPeriodicity: this.implementationPeriodicity?.value || null,
      verification: DateUtil.safe(this.verification?.value || null),
      verificationPeriodicity: this.verificationPeriodicity?.value || null
    };
  }

  get implementerIds(): number[] {
    return this._implementerIds || [];
  }

  get verifierId(): number | string {
    return this._verifierId;
  }

  get implementer(): DataMap<number> {
    return this._temporary.implementer;
  }

  set implementer(value: DataMap<number>) {
    this._temporary.implementer = value;
  }

  get preImplementerIds(): number[] {
    return this._preImplementerIds || [];
  }

  get preImplementer(): DataMap<number> {
    return this._temporary.preImplementer;
  }

  set preImplementer(value: DataMap<number>) {
    this._temporary.preImplementer = value;
  }

  get temporary(): {
    startDate: Date;
    implementer?: DataMap<number>;
    preImplementer?: DataMap<number>;
  } {
    const implementationStartDate = this.implementationStartDate();
    if (!implementationStartDate) {
      this._temporary.startDate = null;
    } else {
      this._temporary.startDate = DateUtil.safe(implementationStartDate.value);
    }
    return this._temporary;
  }

  temporaryImplementers(): string[] {
    return keysObject(this.implementer) || [];
  }

  temporaryPreImplementers(): string[] {
    return keysObject(this.preImplementer) || [];
  }

  override ngOnInit() {
    super.ngOnInit();
    this.initForm();
    this.initLang();
    this.setParticipantsSelectColumns();
  }

  protected initForm() {
    this.mainForm = this.formBuilder.group({
      actualHours: new UntypedFormControl(),
      anticipationAttendDays: new UntypedFormControl(),
      activityOrder: new UntypedFormControl(null),
      businessUnitDepartmentId: new UntypedFormControl(null, Validators.required),
      cancellationReasonLabel: new UntypedFormControl(),
      code: new UntypedFormControl(),
      comments: new UntypedFormControl(''),
      commitmentTask: new UntypedFormControl(),
      createdDate: new UntypedFormControl(),
      creatorUserName: new UntypedFormControl(),
      daysToVerify: new UntypedFormControl(),
      description: new UntypedFormControl(),
      documents: new UntypedFormControl(''),
      dynamic: new UntypedFormControl(null, this.dynamic()?.getMandatoryValidator()),
      enableDeliverySetUp: new UntypedFormControl(),
      files: new UntypedFormControl([]),
      filesTimesheetActivity: new UntypedFormControl([]),
      fillForm: new UntypedFormControl(),
      fillType: new UntypedFormControl(),
      fillTypeLabel: new UntypedFormControl(),
      followUpImplementationDelay: new UntypedFormControl(true),
      mustUpdateImplementationAtReturn: new UntypedFormControl(true),
      generateCode: new UntypedFormControl(),
      history: new UntypedFormControl(''),
      implementation: new UntypedFormControl(),
      implementationPeriodicity: new UntypedFormControl(),
      implementationStartDate: new UntypedFormControl(),
      implementer: new UntypedFormControl(null),
      preImplementer: new UntypedFormControl(null),
      isPlanned: new UntypedFormControl(false),
      objectiveId: new UntypedFormControl(),
      plannedHours: new UntypedFormControl(null),
      plannedImplementationWeek: new UntypedFormControl(),
      plannedVerificationWeek: new UntypedFormControl(),
      plannerTask: new UntypedFormControl(),
      priority: new UntypedFormControl(),
      reminder: new UntypedFormControl(),
      resolutionId: new UntypedFormControl(),
      categoryId: new UntypedFormControl(),
      source: new UntypedFormControl(),
      statusLabel: new UntypedFormControl(),
      systemLinks: new UntypedFormControl(),
      taskCategoryId: new UntypedFormControl(),
      taskDeliveryTypeId: new UntypedFormControl(),
      typeId: new UntypedFormControl(),
      verification: new UntypedFormControl(),
      verificationPeriodicity: new UntypedFormControl(),
      verifier: new UntypedFormControl(),
      verificationPlannedWeek: new UntypedFormControl()
    });
    if (!this.isVerifierRemoved()) {
      this.mainForm.addValidators(sameImplementerVerifierValidator);
    }
    this.cdr.detectChanges();
  }

  protected initLang() {
    this.translate.getFrom(ActivityInfoConstant.LANG_CONFIG, 'fillType').subscribe((tags) => {
      this.fillTypesLang = tags || {};
    });
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'menuOption');
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'files');
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'cancel-series-dialog-message');
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'failed-auto-generated-quick-save');
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'accept-dialog-message');
    this.lang(ActivitiesDetailConstant.LANG_CONFIG, 'messages');
    if (!this.skipInit) {
      const workflowMenuName = WORKFLOW_MENU_NAME;
      this.navLang.getRouteParam(`${this.controller}${workflowMenuName}/:workflow`, null, false, this.activatedRoute).subscribe({
        next: ([workflow]) => {
          if (!NumberUtil.isInteger(workflow)) {
            console.error('Invalid workflow id');
            return;
          }
          this._workflowId = +workflow;
          this.beginInit();
        },
        error: () => {
          this._workflowId = 0;
          this.beginInit();
        }
      });
    } else {
      this.busy = false;
      this.cdr.detectChanges();
    }
  }

  private beginInit(): void {
    this.initSubscription = this.init().subscribe({
      next: () => {
        this.busy = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.busy = false;
        this.cdr.detectChanges();
        // <-- Para `ActivityDetailsToModule` se inicialza desde `initializeById`
        console.error("ActivitiesComponent wasn't initilized from `beginInit`, consider turning ON `skipInit` flag!!", error);
      }
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.initSubscription) {
      this.initSubscription.unsubscribe();
    }
    if (this.subscriptions?.length) {
      for (const s of this.subscriptions) {
        try {
          if (s) {
            s.unsubscribe();
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit();
    const files = this.files();
    if (files) {
      files.hasDelete = () => this.hasDelete();
      files.detectChanges();
    }
    if (this.documents) {
      this.documents.hasDelete = () => this.hasDelete();
      this.documents.detectChanges();
    }
    const comments = this.comments();
    if (comments) {
      comments.hasDelete = () => this.hasDelete();
      comments.detectChanges();
    }
  }

  protected init(id?: number, typeIdSub?: number, businessUnitDepartmentIdSub?: number, typeController?: string): Observable<DataSourceResult> {
    const subjectResult = new Subject<DataSourceResult>();
    const queryParams = this.navLang.getQueryParams(['typeId', 'impl', 'activityId'], true, this.activatedRoute);
    const initResult = subjectResult.asObservable();
    queryParams.pipe(takeUntil(this.$destroy)).subscribe({
      next: ([typeIdStr, showimpl, activityId]) => {
        const typeId = +typeIdStr || typeIdSub || null;
        let serviceUrl: string;
        const defaultTypeController = typeof typeController === 'undefined' || typeController === 'data-source-type';
        if (typeId !== 0 && NumberUtil.isInteger(typeId) && typeof id === 'undefined' && defaultTypeController) {
          const businessUnitDepartmentId: number = businessUnitDepartmentIdSub || 0;
          serviceUrl = `${this.controller}/init-data/data-source-type/${this._workflowId}/${typeId}/${businessUnitDepartmentId}`;
        } else if (id && showimpl && showimpl === 'true') {
          serviceUrl = `${this.controller}/init-data/data-source/${this._workflowId}/${id}/${showimpl}`;
        } else if (activityId && !id) {
          serviceUrl = `${this.controller}/duplicate-data/data-source/${this._workflowId}/${activityId || ''}`;
        } else if (typeIdSub !== 0 && NumberUtil.isInteger(typeIdSub) && typeController) {
          serviceUrl = `${this.controller}/init-data/${typeController}/${this._workflowId}/${typeIdSub}`;
        } else {
          serviceUrl = `${this.controller}/init-data/data-source/${this._workflowId}/${id || ''}`;
        }
        const initData = this.api.get({ cancelableReq: this.$destroy, url: serviceUrl, handleFailure: false });
        if (id) {
          initData.subscribe({
            next: (response) => this.onLoadedDataSource(response, null, typeId, id, subjectResult),
            error: (error) => {
              console.error(error);
              subjectResult.error(error);
              this.loader.hide();
            }
          });
        } else {
          const tsUrl = `${this.controller}/timesheet/data-source`;
          const tsData = this.api.get({ cancelableReq: this.$destroy, url: tsUrl, handleFailure: false });
          const loaded = forkJoin([initData, tsData]);
          loaded.subscribe({
            next: ([response, tsDto]) => this.onLoadedDataSource(response, tsDto, typeId, id, subjectResult),
            error: (error) => {
              console.error(error);
              subjectResult.error(error);
              this.loader.hide();
            }
          });
        }
      }
    });
    return initResult;
  }

  protected onLoadedDataSource(
    response: ActivityDataSourceDto,
    tsDto: TimesheetDataSourceDto,
    typeId: number,
    id: number,
    subjectResult: Subject<DataSourceResult>
  ): void {
    if (response.load && response.load.actualHours !== null && typeof response.load.actualHours !== 'undefined') {
      response.load.actualHours = NumberUtil.round(response.load.actualHours, 2);
    }
    if (typeof response.load !== 'undefined' && response.load !== null) {
      this.createdBy = response.load.createdBy;
    }
    if (tsDto) {
      this._timesheetDs = tsDto;
    }
    this.setCatalogsDataSource(response, id);
    if (this.activitiesTitle === '') {
      this.translate.get(`${this.typeTag}activity-title`).subscribe((activitiesTitle) => {
        this.activitiesTitle = activitiesTitle;
      });
    }
    if (typeId && !response.typeId) {
      this.noticeService.notice('No fué posible encontrar el tipo de actividad capturada');
    }
    subjectResult.next({
      loaded: true,
      typeId: response.typeId || null,
      typeName: response.typeName || null,
      ds: response
    });
    this.loader.hide();
  }

  public setTypes(ds: ITypeAttributes, triggerEvent = true): void {
    const types: ActivityTypeFilllTypeDto[] = ds.types || [];
    if (ds.typeId && ds.typeName && !types.find((t) => t.value === ds.typeId)) {
      types.push({ value: ds.typeId, text: ds.typeName, fillTypes: ds.typeFillTypes });
    }
    this.types = types;
    if (triggerEvent) {
      this.typesChanged.emit(types);
    }
  }

  public setClassifications() {
    const localClassifications: TextHasValue[] = [
      { value: ActivityCoreUtil.getClassificationValue(CommitmentTask.IMPLEMENTATION), text: EnumUtil.getName(CommitmentTask, CommitmentTask.IMPLEMENTATION) },
      { value: ActivityCoreUtil.getClassificationValue(CommitmentTask.VERIFICATION), text: EnumUtil.getName(CommitmentTask, CommitmentTask.VERIFICATION) },
      { value: ActivityCoreUtil.getClassificationValue(CommitmentTask.PROGRAM), text: EnumUtil.getName(CommitmentTask, CommitmentTask.PROGRAM) }
    ];

    this.classifications = [];
    for (const classification of localClassifications) {
      this.classifications.push({
        value: classification.value,
        text: this.translate.instantFrom(ActivityCoreUtil.LANG_CONFIG, `classifications.${classification.value}`)
      });
    }
  }

  protected configureHiddenFields(enable: boolean) {
    this.enableHiddenFields = enable;
    this.cdr.detectChanges();
  }

  protected configureConditionalFields(enable: boolean) {
    this.enableConditionalFields = enable;
    this.cdr.detectChanges();
  }

  protected setCatalogsDataSource(response: ActivityDataSourceDto, id?: number) {
    this.setCatalogOptions(response);
    this.setCatalogRules(response, id);
  }

  private setCatalogOptions(response: ActivityDataSourceDto) {
    if (this._timesheetDs !== null && typeof this._timesheetDs !== 'undefined') {
      response.clients = this._timesheetDs.clients || [];
      response.planners = this._timesheetDs.planners || [];
      response.tasks = this._timesheetDs.tasks || [];
      response.taskDeliveryTypes = this._timesheetDs.taskDeliveryTypes || [];
      response.taskCategories = this._timesheetDs.taskCategories || [];
    }
    response.implementers ||= [];
    response.verifiers ||= [];
    const loggedUser: UserTextHasValue = {
      text: Session.getUserName(),
      value: +Session.getUserId(),
      businessUnitId: +Session.getBusinessUnitId(),
      businessUnitDepartmentId: +Session.getBusinessUnitDepartmentId()
    };
    if (!response.implementers.find((i) => +i.value === +loggedUser.value)) {
      response.implementers.push(loggedUser);
    }
    if (!response.verifiers.find((i) => +i.value === +loggedUser.value) && !response.disabledFields?.verification) {
      response.verifiers.push(loggedUser);
    }
    this.businessUnitDepartments = response.businessUnitDepartments;
    this.priorities = response.priorities;
    this.clients = response.clients || [];
    this.planners = response.planners || [];
    this.tasks = response.tasks || [];
    this.verifiers = response.verifiers;
    this.implementers = sortByObject(response.implementers, 'text');
    this.preImplementers = response.implementers;
    this.sources = response.sources;
    this.categories = response.categories;
    this.objectives = response.objectives;
    this.fillTypes = response.fillTypes || [];
    this.resolutions = response.resolutions || [];
    if (this.fillTypes?.length > 1) {
      this.requiredRules.fillType = true;
    } else {
      this.requiredRules.fillType = false;
    }
    for (const item of this.fillTypes) {
      item.text = this.fillTypesLang[item.text] || item.text;
    }
    updateCatalogTaskData(this.clients, this.planners, this.tasks);
    this.cdr.detectChanges();
  }

  private setCatalogRules(response: ActivityDataSourceDto, id?: number) {
    const isNew = !id;
    this.isNew = isNew;
    this.setTypes(response);
    this.setClassifications();
    this.setHiddenRules(response.hiddenRules, response.dynamicFields);
    this.setDefaultValues(isNew, response.constraints, response.defaultValues, response.disabledFields);
    this.setConstraintPlannedDates(response.constraints);
    this.setDisabledFields(response.disabledFields);
    this.initializeConditionalData(response.conditionalFields);
  }

  private initializeConditionalData(conditionalFields: ConditionalEntity[]): void {
    const dictionary: ConditionalDictionary = ActivityConditionalUtil.parseDictionary(conditionalFields);
    this.conditionalDictionary = dictionary;
    this.conditionalBusyFields.clear();
  }

  private isAvailableConditional(): boolean {
    if (!this.enableConditionalFields) {
      return false;
    }
    return this.conditionalBusyFields?.size > 0;
  }

  private relaseConditional(fieldName: string): boolean {
    if (!this.conditionalBusyFields) {
      return false;
    }
    return this.conditionalBusyFields.delete(fieldName);
  }

  public checkConditionalFields(): boolean {
    if (this.isAvailableConditional()) {
      return false;
    }
    const dictionary = this.conditionalDictionary;
    if (dictionary == null || typeof dictionary === 'undefined' || !dictionary.sources?.length) {
      return false;
    }
    const data = this.mainForm.value;
    const updatedFieldsLabels = new Set<string>();
    const applied = dictionary.sources.some((source) => this.checkAndApplyConditionalValue(data, source, updatedFieldsLabels));
    this.showUpdatedCondiditionalMessages(updatedFieldsLabels);
    return applied;
  }

  public checkConditionalByFieldName(fieldName: string, value: any): boolean {
    if (this.isAvailableConditional()) {
      this.relaseConditional(fieldName);
      return false;
    }
    if (value === null || typeof value === 'undefined' || value === '') {
      return false;
    }
    const dictionary = this.conditionalDictionary;
    if (dictionary == null || typeof dictionary === 'undefined' || !dictionary.sources?.length) {
      return false;
    }
    if (!dictionary.matchTargetNames.has(fieldName)) {
      return false;
    }
    const matchFieldOrders = new Set(dictionary.entries.filter((entry) => entry.matchFields.has(fieldName)).map((entry) => entry.fieldOrder));
    const data = this.mainForm.value;
    data[fieldName] = value;
    const updatedFieldsLabels = new Set<string>();
    const applied = dictionary.sources
      .filter((source) => matchFieldOrders.has(source.fieldOrder))
      .some((source) => this.checkAndApplyConditionalValue(data, source, updatedFieldsLabels));
    this.showUpdatedCondiditionalMessages(updatedFieldsLabels);
    return applied;
  }

  private showUpdatedCondiditionalMessages(updatedFieldsLabels: Set<string>) {
    if (updatedFieldsLabels?.size > 0) {
      if (updatedFieldsLabels.size === 1) {
        const message = this.translateService
          .instantFrom(this.LangConfigActivityInfo, 'updatedSingleConditionalFields')
          .replace('{fields}', Array.from(updatedFieldsLabels)[0]);
        this.noticeService.notice(message);
      } else {
        const message = this.translateService
          .instantFrom(this.LangConfigActivityInfo, 'updatedMultipleConditionalFields')
          .replace('{fields}', Array.from(updatedFieldsLabels).join(','));
        this.noticeService.notice(message);
      }
    }
  }

  private compareConditionalValue(field: ConditionalEntity, data: any, source: any): boolean {
    const fieldDef = this.defaultFieldsDef[field.fieldName];
    if (!fieldDef) {
      const dynamicEntity: PersistableDynamicEntity = data.dynamic;
      let dynamicValue: string;
      if (dynamicEntity?.dynamicFieldData) {
        dynamicValue = dynamicEntity.dynamicFieldData[field.fieldName];
      } else {
        dynamicValue = null;
      }
      return equalsObject(dynamicValue, source);
    }
    const target = data[field.fieldName];
    switch (fieldDef.fieldType) {
      case FieldType.DOCUMENT_SELECT:
      case FieldType.PLANNER_TASK:
        return equalsObject(target?.value, source?.value);
      case FieldType.INPUT_NUMBER:
      case FieldType.INPUT_DOUBLE:
        return equalsObject(+target, +source);
      default:
        return equalsObject(target, source);
    }
  }

  private checkAndApplyConditionalValue(data: any, source: ConditionalFieldData, updatedFieldsLabels: Set<string>): boolean {
    if (this.isAvailableConditional()) {
      return false;
    }
    const matches = source.matchFields.every((field) => {
      const fieldValue = this.parseConditionalValue(field);
      const result = this.compareConditionalValue(field, data, fieldValue);
      return result;
    });
    if (!matches) {
      return false;
    }
    const applied = this.applyDefinedFieldsConditional(data, source, updatedFieldsLabels);
    const dynamicApplied = this.applyDynamicFieldsConditional(data, source, updatedFieldsLabels);
    return applied || dynamicApplied;
  }

  private fixCatalogDataConditionalFields(field: ConditionalEntity): void {
    const fillForm = this.fillForm();
    switch (field.fieldName) {
      case 'fillForm':
        fillForm.gridSelectValue = +field.value;
        if (!fillForm.data?.length) {
          fillForm.data = [];
        }
        if (fillForm.data.find((item) => item.id === +field.value) === null) {
          fillForm.data.push({
            id: +field.value,
            deleted: 0,
            code: field.code,
            description: field.description
          });
        }
        break;
    }
  }

  private applyDefinedFieldsConditional(data: any, source: ConditionalFieldData, updatedFieldsLabels: Set<string>): boolean {
    const appliedData: DataMap = {};
    let hasChanges = false;
    for (const field1 of source.assignedFields.filter((field) => this.defaultFieldsDef[field.fieldName])) {
      const fieldValue = this.parseConditionalValue(field1);
      if (fieldValue !== null && typeof fieldValue !== 'undefined' && fieldValue !== '') {
        appliedData[field1.fieldName] = fieldValue;
        this.conditionalBusyFields.add(field1.fieldName);
        const label = this.translate.instantFrom(ADD_MANY_LANG_CONFIG, `fieldsLabels.${field1.fieldName}`);
        updatedFieldsLabels.add(label);
        this.fixCatalogDataConditionalFields(field1);
        hasChanges = true;
      }
    }
    if (hasChanges) {
      this.mainForm.patchValue(appliedData);
      this.conditionalBusyFields.clear();
      if (this.debug()) {
        console.log(`Applied conditional data of order ${source.fieldOrder}.`, data);
      }
      return true;
    }
    return false;
  }

  private applyDynamicFieldsConditional(data: any, source: ConditionalFieldData, updatedFieldsLabels: Set<string>): boolean {
    const appliedData: PersistableDynamicEntity = this.mainForm.controls.dynamic.value ?? {
      dynamicFieldData: {}
    };
    let hasChanges = false;
    for (const field1 of source.assignedFields.filter((field) => !this.defaultFieldsDef[field.fieldName])) {
      if (field1.value !== null && typeof field1.value !== 'undefined' && field1.value !== '') {
        appliedData.dynamicFieldData[field1.fieldName] = field1.value;
        const dynamicField = this.dynamic()?.formConfig?.fields?.find((formField) => formField);
        if (dynamicField) {
          updatedFieldsLabels.add(dynamicField.label);
          hasChanges = true;
        }
      }
    }
    if (hasChanges) {
      this.mainForm.patchValue({
        dynamic: appliedData
      });
      if (this.debug()) {
        console.log(`Applied dynamic conditional data of order ${source.fieldOrder}.`, data);
      }
      return true;
    }
    return false;
  }

  private parseConditionalValue(field: ConditionalEntity): any {
    const fieldDef = this.defaultFieldsDef[field.fieldName];
    if (!fieldDef) {
      return field.value;
    }
    switch (fieldDef.fieldType) {
      case FieldType.SWITCH: {
        const itemValue = !!((field.value as unknown as number) === 1 || field.value === 'true' || (field.value as unknown as boolean) === true);
        return itemValue;
      }
      case FieldType.PLANNER_TASK: {
        const taskId = +field.value;
        const task = this.tasks.find((element) => element.value === taskId);
        return task;
      }
      case FieldType.INPUT_NUMBER:
      case FieldType.INPUT_DOUBLE:
      case FieldType.DROPDOWN_SEARCH:
        return +field.value;
      case FieldType.SYSTEM_LINK: {
        const itemId = +field.value;
        const itemCatalog = this.fieldCatalog(fieldDef).find((element) => element.value === itemId);
        return itemCatalog;
      }
      case FieldType.DOCUMENT_SELECT:
        return {
          id: +field.value,
          deleted: 0,
          code: field.code,
          description: field.description
        };
      case FieldType.DROPDOWN_SEARCH_MULTIPLE: {
        const itemIds = field.value.split(',');
        const ids = itemIds.filter((itemId) => itemId != null && typeof itemId !== 'undefined' && itemId !== '').map((itemId) => +itemId);
        return ids;
      }
      default:
        return field.value;
    }
  }

  private fieldCatalog(fieldDef: FieldDefinition): any[] {
    if (this.busy) {
      return null;
    }
    return this[fieldDef.catalogName] || [];
  }

  protected required() {
    return true;
  }

  addFavorite(saveAsSystem = false): void {
    const params: DataMap<string> = {};
    let urlParams = '?favUrl=1';
    for (const fieldName in this.mainForm.controls) {
      if (!this.mainForm.controls.hasOwnProperty(fieldName)) {
        continue;
      }
      const control = this.mainForm.get(fieldName);
      if (!control.valid || !control.value) {
        continue;
      }
      MenuFavoritesUtils.setFavoriteParams(params, control.value, fieldName, this.ignoreKeys);
    }
    if (NumberUtil.isNumber(params.typeId)) {
      urlParams += `&typeId=${params.typeId}`;
    }
    const config: QualifiedGenericMenuItem = {
      moduleName: this.moduleName,
      icon: 'launch',
      urlParams: urlParams,
      params: params,
      type: FavoriteTaskType.MENU_PATH,
      infoMessage: 'Todos los datos configurados serán guardados como una plantilla dentro del acceso directo'
    };
    if (saveAsSystem) {
      config.isSystemGenerated = true;
      config.systemGeneratedName = this.activitiesTitle;
      const saved = new Deferred<FavoriteTaskSaveDTO>();
      config.def = saved;
      saved.promise.then((result) => this.onAutoGeneratedQuickAccessSave(result));
    }
    this.menuService.addQuickAccess(config);
  }

  hideDates(): boolean {
    return false;
  }

  changeType(typeId: TextHasValue) {
    if (!typeId?.value) {
      this.verifiers = [];
      this.implementers = [];
      this.preImplementers = [];
      this.fillTypes = [];
      this.setHiddenRules(this.defaultHiddenRules, null);
      this.setDisabledFields(this.defaultDisabledFields);
      const now = new Date();
      this.setConstraintPlannedDates({
        minPlannedImplementation: now,
        minPlannedVerification: now
      });
      this.dynamic().clear();
      if (this.mainForm.controls.dynamic) {
        this.mainForm.controls.dynamic.reset();
        this.mainForm.controls.dynamic.disable();
      }
      return;
    }
    if (!this._isLoadingDuplicateActivity) {
      if (this.defaultHiddenValues != null && typeof this.defaultHiddenValues.typeId === 'number') {
        this.defaultHiddenValues.typeId = null;
      }
      this.refreshTypeId(typeId?.value || null, null);
    }
  }

  protected refreshTypeId(typeId: number | string, ds?: ActivityDataSourceDto): Promise<boolean[]> {
    const def = new Deferred<boolean>();
    const defDynamic = new Deferred<boolean>();
    this.dynamic()
      .get(typeId)
      .then((count) => {
        if (count === 0) {
          this.mainForm.controls.dynamic.disable();
        } else {
          this.mainForm.controls.dynamic.enable();
          this.refreshRequiredFields();
        }
        defDynamic.resolve(true);
      });
    if (typeof ds === 'undefined' || ds === null) {
      const departmentId = this.mainForm.controls.businessUnitDepartmentId.value || '-1';
      const url = `${this.controller}${this.linkedSource?.typeController || '/data-source-type/'}${this._workflowId}/${typeId}/${departmentId}`;
      this.busy = true;
      this.cdr.detectChanges();
      this.api
        .get({
          url: url,
          handleFailure: false,
          cancelableReq: this.$destroy
        })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (result: ActivityDataSourceDto) => {
            this.refreshTypeDataSource(result);
            def.resolve(true);
            switch (this.module) {
              case Module.PLANNER:
                // Cambio de título para el módulo de proyectos
                forkJoin([this.translate.get('subplanner-title'), this.translate.get('planner-title')]).subscribe(([subplannerTitle, plannerTitle]) => {
                  if (this.hiddenRules.plannerTask) {
                    this.activitiesTitle = plannerTitle || subplannerTitle;
                  } else {
                    this.activitiesTitle = subplannerTitle || plannerTitle;
                  }
                });
                break;
            }
          },
          error: (error) => {
            def.resolve(false);
            console.log(error);
            this.busy = false;
            this.cdr.detectChanges();
          }
        });
    } else {
      this.refreshTypeDataSource(ds);
      def.resolve(true);
      defDynamic.resolve(true);
    }
    return Promise.all([def.promise, defDynamic.promise]);
  }

  private refreshTypeDataSource(ds: ActivityDataSourceDto): void {
    try {
      const isNew = !ds?.load?.id;
      this.isNew = isNew;
      this.evaluateDisableBelongSeries();
      this.setTypes(ds);
      this.setCatalogOptions(ds);
      const hiddenRules: HiddenRules = ds.hiddenRules || this.hiddenRules;
      const disabledFields: DisabledFields = ds.disabledFields || this.disabledFields;
      if (this.defaultHiddenValues?.description) {
        this.mainForm.patchValue({
          description: this.defaultHiddenValues.description
        });
      }
      this.descriptionSize().adjust();
      this.setSelectDefault('fillTypes', this.fillTypes, disabledFields, 'fillType');
      this.setSelectDefault('types', this.types, disabledFields, 'typeId', false);
      if (ds.defaultValues?.businessUnitDepartmentId) {
        this.defaultHiddenValues.businessUnitDepartmentId = ds.defaultValues.businessUnitDepartmentId;
      }
      this.setSelectDefault('businessUnitDepartments', this.businessUnitDepartments, disabledFields, 'businessUnitDepartmentId');
      this.setHiddenRules(hiddenRules, ds.dynamicFields);
      if (!this.touched) {
        this.setDefaultValues(isNew, ds.constraints, ds.defaultValues, ds.disabledFields);
      }
      if (this.defaultHiddenValues?.plannedHours !== null && typeof this.defaultHiddenValues?.plannedHours !== 'undefined') {
        this.mainForm.patchValue({
          plannedHours: this.defaultHiddenValues.plannedHours
        });
      }
      this.setDisabledFields(disabledFields);
      this.initializeConditionalData(ds.conditionalFields);
      this.setConstraintPlannedDates(ds.constraints);
      this.evaluateSameDayMessages();
      this.refreshRequiredFields();
      this.checkConditionalFields();
      this.busy = false;
      this.cdr.detectChanges();
    } catch (e) {
      console.error(e);
    }
  }

  private refreshRequiredFields() {
    for (const key of keysObject(this.mainForm.controls)) {
      if (this.requiredRules.hasOwnProperty(key) && this.requiredRules[key] && !this.isFieldHidden(key) && !this.disabledFields[key]) {
        if (this.mainForm.controls.hasOwnProperty(key) && this.mainForm.controls[key] && !this.mainForm.controls[key].hasValidator(Validators.required)) {
          this.mainForm.controls[key].addValidators(Validators.required);
        }
      } else if (this.mainForm.controls.hasOwnProperty(key) && this.mainForm.controls[key]) {
        this.mainForm.controls[key].removeValidators(Validators.required);
      }
    }
    const dynamic = this.dynamic();
    if (dynamic?.hasFields) {
      this.mainForm.controls.dynamic.addValidators(dynamic?.getMandatoryValidator());
    } else {
      this.mainForm.controls.dynamic.removeValidators(dynamic?.getMandatoryValidator());
    }
    this.mainForm.markAsUntouched();
    this.mainForm.updateValueAndValidity();
  }

  private refreshDepartmentIdDataSource(ds: ActivityDataSourceDto, value: TextHasValue): void {
    this.verifiers = ds.verifiers;
    this.implementers = ds.implementers;
    this.preImplementers = ds.implementers;
    this.setSelectDefault('implementers', this.implementers, ds.disabledFields, 'implementer', false);
    this.setSelectDefault('verifiers', this.verifiers, ds.disabledFields, 'verifier', false);
    this.checkConditionalByFieldName('businessUnitDepartmentId', value?.value);
    this.cdr.detectChanges();
  }

  setSelectDefault(dataArrayPropertyName: string, dataArray: TextHasValue[], disabledFields: DataMap, key: string, clearValue = true): void {
    // Para validación de valores por defecto
    let settedDefaultValue = false;
    if (this.defaultHiddenValues != null && typeof this.defaultHiddenValues[key] === 'number') {
      if (!dataArray) {
        dataArray = [];
      }
      const patch: DataMap = {};
      const value = dataArray.find((f: TextHasValue) => +f.value === this.defaultHiddenValues[key]) || null;
      if (value === null) {
        dataArray.push({
          text: `ID: ${this.defaultHiddenValues[key]}`,
          value: this.defaultHiddenValues[key]
        });
        if (this[dataArrayPropertyName] !== null && typeof this[dataArrayPropertyName] !== 'undefined') {
          this[dataArrayPropertyName] = shallowClone(dataArray);
        }
      } else if (this.mainForm.controls[key].value === value) {
        // El control ya tiene el valor
        return;
      }
      patch[key] = this.defaultHiddenValues[key];
      // Se habilita el campo y se setea valor por defecto.
      this.enableField(key);
      this.mainForm.patchValue(patch, { onlySelf: true, emitEvent: false });
      // Si no se requiere un valor por defecto eliminar las líneas anteriores y la validación con settedDefaultValue
      settedDefaultValue = true;
      this.touched = false;
    }
    if (!dataArray || dataArray.length === 0) {
      console.warn(`No data loaded for field ${key}`);
      return;
    }
    if (dataArray.length === 1) {
      const patch: DataMap = {};
      patch[key] = dataArray[0].value;
      this.enableField(key);
      this.mainForm.patchValue(patch, { onlySelf: true, emitEvent: false });
    } else if (dataArray.length > 1 && clearValue && !settedDefaultValue) {
      const patch: DataMap = {};
      patch[key] = null;
      // Se setea en null, solo si ya tiene un valor
      if (this.mainForm.controls[key].value !== null) {
        this.mainForm.patchValue(patch, { onlySelf: true, emitEvent: false });
      }
      disabledFields[key] = false;
    } else if (!this.iImplementerField(key) && !dataArray.find((item) => +item.value === +this.mainForm.controls[key].value)) {
      const patch: DataMap = {};
      patch[key] = null;
      // Se setea en null, solo si ya tiene un valor
      if (this.mainForm.controls[key].value !== null) {
        this.mainForm.patchValue(patch, { onlySelf: true, emitEvent: false });
      }
      disabledFields[key] = false;
    }
  }

  private iImplementerField(key: string) {
    if (key === null || typeof key === 'undefined' || key === '') {
      return false;
    }
    return key === 'implementer' || key.startsWith('implementer-');
  }

  changeFillType(_fillType: any) {
    // empty
  }

  changeBusinessUnit(_value: TextHasValue) {
    // empty
  }

  changePlanner(_plannerId: TextHasValue) {
    // empty
  }

  onChangeVerifier(value: number | string): void {
    if (value !== null && typeof value !== 'undefined') {
      this._verifierId = value;
      this.checkConditionalByFieldName('verifier', value);
    }
  }

  onChangeCategory(value: number | string): void {
    this.checkConditionalByFieldName('categoryId', value);
  }

  onChangeSource(value: number | string): void {
    this.checkConditionalByFieldName('source', value);
  }

  onChangePriority(value: number | string): void {
    this.checkConditionalByFieldName('priority', value);
  }

  onChangeObjective(value: number | string): void {
    this.checkConditionalByFieldName('objectiveId', value);
  }

  changePlannerTask(value: PlannerTaskData): void {
    this.checkConditionalByFieldName('plannerTask', value);
  }

  changeForm(value: DocumentLinkedSelectorDTO): void {
    this.checkConditionalByFieldName('fillForm', value);
  }

  changePlannedHours(_event, value: number | string): void {
    this.checkConditionalByFieldName('plannedHours', value);
  }

  changeIsPlanned(value: boolean): void {
    this.checkConditionalByFieldName('isPlanned', value);
  }

  changeDynamicData(change: FieldChangeData): void {
    if (this.busy) {
      return;
    }
    const dynamicFieldData = change.entity.dynamicFieldData;
    const fieldName = change.field.name;
    this.checkConditionalByFieldName(fieldName, dynamicFieldData[fieldName]);
  }

  changeImplementer(newValue: (number | string)[] | number | string) {
    if (newValue === null || typeof newValue === 'undefined') {
      this._implementerIds = [];
      this.tempImplementers = [];
    } else {
      let implementers: (number | string)[];
      if (typeof newValue === 'string' || typeof newValue === 'number') {
        implementers = [+newValue];
      } else {
        implementers = newValue;
      }
      if (!equalsObject(this.implementerIds, implementers)) {
        this.tempImplementers = [];
        for (const imp of this.implementers) {
          if (implementers.indexOf(+imp.value) !== -1) {
            this.tempImplementers.push(imp);
          }
        }
        this._implementerIds = implementers.map((id) => +id);
      }
      this.checkConditionalByFieldName('implementer', this._implementerIds);
    }
  }

  changePreImplementer(newValue: (number | string)[] | number | string) {
    if (newValue === null || typeof newValue === 'undefined') {
      this._preImplementerIds = [];
      this.tempPreImplementers = [];
    } else {
      let preImplementers: (number | string)[];
      if (typeof newValue === 'string' || typeof newValue === 'number') {
        preImplementers = [+newValue];
      } else {
        preImplementers = newValue;
      }
      if (!equalsObject(this.preImplementerIds, preImplementers)) {
        this.tempPreImplementers = [];
        for (const imp of this.preImplementers) {
          if (preImplementers.indexOf(+imp.value) !== -1) {
            this.tempPreImplementers.push(imp);
          }
        }
        this._preImplementerIds = preImplementers.map((id) => +id);
      }
      this.checkConditionalByFieldName('preImplementer', this._preImplementerIds);
    }
  }

  changeParentActivityTask(_parentActivityTaskIs: TextHasValue) {
    // empty
  }

  changeBusinessUnitDepartment(value: TextHasValue) {
    const department = value as unknown as BusinessUnitDepartmentEntity;
    if (this._isLoadingDuplicateActivity) {
      return;
    }
    if (this.mainForm.controls.typeId.value) {
      const typeId = this.mainForm.controls.typeId.value;
      let loadDsObs: Observable<any>;
      if (department) {
        const url = `${this.controller}/data-source-type/${this._workflowId}/${typeId}/${department.value}`;
        loadDsObs = this.api.get({ cancelableReq: this.$destroy, url: url });
      } else {
        const url = `${this.controller}/data-source-type/${this._workflowId}/-1/-1`;
        loadDsObs = this.api.get({ cancelableReq: this.$destroy, url: url });
      }
      const subscrition = loadDsObs.subscribe(
        (result: ActivityDataSourceDto) => {
          this.refreshDepartmentIdDataSource(result, value);
          subscrition.unsubscribe();
        },
        (error) => {
          subscrition.unsubscribe();
          console.log(error);
          this.busy = false;
          this.cdr.detectChanges();
        }
      );
      if (department) {
        this.changedBusinessUnitDepartment.emit({
          text: department.text,
          value: +department.value,
          businessUnitId: +department.businessUnitId
        });
      } else {
        this.changedBusinessUnitDepartment.emit(null);
      }
      const businessUnitId = department?.businessUnitId;
      if (businessUnitId !== this.businessUnitId) {
        this.businessUnitId = businessUnitId;
        this.changedBusinessUnitId.emit(this.businessUnitId);
      }
    }
  }

  saveChange(_fieldName: string) {
    // empty
  }

  undoChange(_fieldName: string) {
    // empty
  }

  setAddSubtaskByDepartment(addSubtaskByDepartment: boolean): void {
    this._addSubtaskByDepartment = addSubtaskByDepartment;
  }

  setHiddenRules(hiddenRules: HiddenRules, dsDTO: DynamicFieldsDTO) {
    if (!hiddenRules) {
      return;
    }
    for (const key in this.hiddenRules) {
      if (this.hiddenRules.hasOwnProperty(key) && hiddenRules.hasOwnProperty(key)) {
        if (hiddenRules[key]) {
          if (key === 'dynamicFields' && dsDTO) {
            // Add dynamic fields
            dsDTO.dynamicFields.forEach((item) => {
              this.hiddenRules[item.name] = true;
              const dynamicFieldId = hiddenRules.dynamicFields.find((i) => i === item.id);
              if (dynamicFieldId) {
                this.hiddenRules[item.name] = false;
              }
            });
          } else {
            this.hiddenRules[key] = true;
          }
        } else {
          this.hiddenRules[key] = false;
          this.enableField(key);
        }
      }
    }
    // Populate hiddenFields with only the name
    this.hiddenFields = [];
    for (const [key, value] of Object.entries(this.hiddenRules)) {
      if (value) {
        this.hiddenFields.push(key);
      }
    }
    this.cdr.detectChanges();
  }

  setDisabledFields(disabledFields: DataMap) {
    if (!disabledFields) {
      return;
    }
    for (const key in this.disabledFields) {
      if (this.disabledFields.hasOwnProperty(key) && disabledFields.hasOwnProperty(key)) {
        if (disabledFields[key]) {
          this.disabledFields[key] = true;
        } else {
          this.disabledFields[key] = false;
          this.enableField(key);
        }
      }
    }
  }

  setConstraintPlannedDates(config: ActivityConstraints) {
    if (!config) {
      return;
    }
    this.constraints = config;
    const isPlanner = this.module === Module.PLANNER;
    setConstraintDates(config, this, !isPlanner);
    if (isPlanner) {
      this.implementationDisabledDates = [];
    }
    this.cdr.detectChanges();
  }

  get typeId(): number {
    if (!this.mainForm || !this.mainForm.get('typeId')) {
      return null;
    }
    return +this.mainForm.get('typeId').value;
  }

  get implementation(): IgxDatePickerComponent {
    return this._implementation();
  }

  get verification(): IgxDatePickerComponent {
    return this._verification();
  }

  get implementationPeriodicity(): PeriodicityComponent {
    return this._implementationPeriodicity();
  }

  get verificationPeriodicity(): PeriodicityComponent {
    return this._verificationPeriodicity();
  }

  get belongSeries(): number {
    const value = this._belongSeries;
    if (value === null || typeof value === 'undefined') {
      return 0;
    }
    return value;
  }

  set belongSeries(belongSeries: number) {
    this._belongSeries = belongSeries;
  }

  get typeTag(): string {
    if (this.linkedSource == null || typeof this.linkedSource.type === 'undefined') {
      return '';
    }
    return `${this.moduleName}.${EnumUtil.getName(FindingActivityType, this.linkedSource.type).toLowerCase()}.`;
  }

  get moduleName(): string {
    return EnumUtil.getName(Module, Module[this.module]).toLowerCase();
  }

  /**
   * Se utiliza solamente para diferencias etiquetas ya que en
   * todos los "submodulos" se llaman "actividades" y para proyectos
   * hay que llamarlos "tareas/proyectos"
   */
  get moduleLabelsFrom(): string {
    if (this.module === Module.PLANNER) {
      return String(Module.PLANNER).toLowerCase();
    }
    return String(Module.ACTIVITY).toLowerCase();
  }

  get controller(): string {
    return getActivityController(this.moduleName);
  }

  setDefaultValues(isNew: boolean, constraints: ActivityConstraints, defaultValues?: DefaultValues, disabledFields?: DisabledFields) {
    let today = new Date();
    const implementationStartDate = new Date();
    if (!this.disabledFields.anticipationAttendDays) {
      today = new Date();
      today.setDate(implementationStartDate.getDate() + constraints.anticipationAttendDays);
    }
    this.translate.get('status-draft').subscribe((tag) => {
      this.mainForm.patchValue(
        {
          status: tag,
          generateCode: true,
          implementationStartDate: implementationStartDate,
          daysToVerify: defaultValues?.daysToVerify || null,
          systemLinks: defaultValues?.systemLinks || null
        },
        { onlySelf: true, emitEvent: false }
      );
      this.disableField('code');
      if (this.implementationPeriodicity) {
        this.implementationPeriodicity.startDate = today;
      }
      if (this.verificationPeriodicity) {
        this.verificationPeriodicity.startDate = today;
      }
      if (this.implementation) {
        this.implementation.value = today;
      }
      if (this.verification) {
        this.verification.value = today;
      }
    });
    this.setConstraintPlannedDates({
      minPlannedVerification: today,
      minPlannedImplementation: today
    });
    if (!defaultValues) {
      return;
    }
    this.defaultHiddenValues.implementer = defaultValues.implementer;
    this.defaultHiddenValues.daysToVerify = defaultValues.daysToVerify;
    this.defaultHiddenValues.verifier = defaultValues.verifier;
    this.defaultHiddenValues.objectiveId = defaultValues.objectiveId;
    this.defaultHiddenValues.priority = defaultValues.priority;
    this.defaultHiddenValues.source = defaultValues.source;
    this.defaultHiddenValues.categoryId = defaultValues.categoryId;
    this.defaultHiddenValues.isPlanned = defaultValues.isPlanned;
    this.defaultHiddenValues.plannedHours = defaultValues.plannedHours;
    this.defaultHiddenValues.systemLinks = defaultValues.systemLinks;
    this.defaultHiddenValues.actualHours = defaultValues.actualHours;
    this.defaultHiddenValues.activityResolutionId = defaultValues.activityResolutionId;
    if (isNew) {
      this.setSelectDefault('implementers', this.implementers, disabledFields, 'implementer', false);
      this.setSelectDefault('verifiers', this.verifiers, disabledFields, 'verifier', false);
      this.setSelectDefault('objectives', this.objectives, disabledFields, 'objectiveId', false);
      this.setSelectDefault('priorities', this.priorities, disabledFields, 'priority', false);
      this.setSelectDefault('sources', this.sources, disabledFields, 'source', false);
      this.setSelectDefault('categories', this.categories, disabledFields, 'categoryId', false);
    }
  }

  onSaveAction(): void {
    // empty
  }

  onAutoGeneratedQuickAccessSave(_result: FavoriteTaskSaveDTO): void {
    // empty
  }

  return(): void {
    this.navLang.back();
  }

  closeDialogSave(): void {
    this.sucessSaveDialog().close();
    this.menuService.navigate('menu/activities/add-advanced', this.module);
  }

  closeDialogList(): void {
    this.sucessSaveDialog().close();
    this.menuService.navigate(`menu/activities/mine/${this._workflowId}`, this.module);
  }

  closeDialogSaveFail(): void {
    this.failSaveDialog().close();
  }

  changeGenerateCode(event: IChangeCheckboxEventArgs): void {
    if (this.editableRules.code) {
      return;
    }
    if (event.checked) {
      this.disableField('code');
    } else {
      this.enableField('code');
    }
  }

  protected showDates(): boolean {
    console.log('showDates add!');
    return !this.mainForm.value.type;
  }

  public getEntityData(): ActivityEntity {
    const data: ActivityEntity = this.mainForm.value;
    const participants = this.participants();
    if (participants?.value?.length) {
      data.participants = participants.value.map((participant) => participant.entity_id);
    }
    if (
      (data.businessUnitDepartmentId === null || typeof data.businessUnitDepartmentId === 'undefined') &&
      this.defaultHiddenValues.businessUnitDepartmentId !== null &&
      typeof this.defaultHiddenValues.businessUnitDepartmentId !== 'undefined'
    ) {
      data.businessUnitDepartmentId = this.defaultHiddenValues.businessUnitDepartmentId;
    }
    data.businessUnitId = this.businessUnitId;
    data.anticipationAttendDays = !this.isFieldHidden('anticipationAttendDays') ? this.mainForm.controls.anticipationAttendDays.value : null;
    data.belongSeries = this.belongSeries;
    data.plannerTask = this.plannerTask()?.value;
    data.systemLinks = this.systemLinks()?.value;
    data.implementation = this.mainForm.controls.implementation.value;
    data.verification = this.mainForm.controls.verification.value;
    const linked: ActivityLinked = this.getLinkedData();
    const dynamicData: PersistableDynamicEntity = this.dynamic().getPersistableDynamicEntity(data.typeId);
    const implementationDates = PeriodicityUtil.getDates(data.implementationPeriodicity);
    const implemverificationDates = PeriodicityUtil.getDates(data.verificationPeriodicity);
    data.implementer = this._implementerIds;
    data.preImplementer = this._preImplementerIds;
    if (this.linkedSource?.commitmentTask) {
      data.commitmentTask = this.linkedSource.commitmentTask;
    }
    if (this.linkedSource?.groupName) {
      data.groupName = this.linkedSource.groupName || 'Plan de actividades';
      data.groupId = getUuid();
    }
    if (this.linkedSource?.recurrenceId) {
      data.recurrenceId = this.linkedSource.recurrenceId;
    }
    if (this.linkedSource?.parentPlannerId) {
      data.parentPlannerId = this.linkedSource.parentPlannerId;
    }
    if (this.linkedSource?.parentActivityId) {
      data.parentActivityId = this.linkedSource.parentActivityId;
    }
    if (this.linkedSource?.parentActivityImplementationId) {
      data.parentActivityImplementationId = this.linkedSource.parentActivityImplementationId;
    }
    if (this.linkedSource?.parentTreeActivityId) {
      data.parentTreeActivityId = this.linkedSource.parentTreeActivityId;
    }
    // Se construye objeto de guardado
    const entity: ActivityEntity = getEntityData(
      this.disabledFields,
      data,
      linked,
      dynamicData,
      implementationDates,
      implemverificationDates,
      this.moduleName,
      this.linkedSource
    );
    if (this.isVerifierRemoved() || this.isVerificationAvailable() === VerificationAvailable.UNAVAILABLE) {
      clearVerification(entity);
    }
    if (this.isImplementationAvailable() === ImplementationAvailable.UNAVAILABLE) {
      clearImplementation(entity);
    }
    return entity;
  }

  saveComment(_saveCommentEvt) {
    this.savingComment = true;
  }

  deleteComment(_commentRowData) {
    this.savingComment = true;
  }

  saveDocument(_saveDocumentEvt) {
    this.savingDocument = true;
  }

  deleteDocument(_documentRowData) {
    this.savingDocument = true;
  }

  getIds(data: any[]): Entity[] {
    if (!data) {
      return [];
    }
    return data.map((item) => {
      return { id: item.id };
    });
  }

  getLinkedData(): ActivityLinked {
    return {
      documents: this.editableRules.documents && this.documents ? this.getIds(this.documents?.value) : null,
      files: this.editableRules.files ? this.getIds(this.files()?.value) : null,
      comments: this.editableRules.comments ? this.getIds(this.comments()?.value) : null,
      swapActivities: null
    };
  }

  hasDelete() {
    return true;
  }

  onDescriptionChange(value: string) {
    this.checkConditionalByFieldName('description', value);
  }
  onSelectStartDate(value: Date) {
    if (value) {
      value.setHours(0, 0, 0, 0);
      if (this.editableRules.verification) {
        if (value.getTime() > DateUtil.safe(this.verification.value).getTime()) {
          this.mainForm.patchValue({ verification: value }, { onlySelf: true, emitEvent: false });
        }
        if (this.constraints) {
          this.constraints.minPlannedVerification = value.getTime();
        }
      }
      if (this.editableRules.implementation) {
        if (value.getTime() > DateUtil.safe(this.implementation.value).getTime()) {
          this.mainForm.patchValue({ implementation: value }, { onlySelf: true, emitEvent: false });
        }
        if (this.constraints) {
          this.constraints.minPlannedImplementation = value.getTime();
        }
      }
    }
    this.temporary.startDate = value;
    this.setConstraintPlannedDates(this.constraints);
    this.refreshAnticipationAttendDays();
    this.changedPlannerStartDates.emit({
      implementationStartDate: value,
      implementation: DateUtil.safe(this.implementation?.value || null)
    });
  }

  onSelectImplementation(value: Date): void {
    if (value) {
      value.setHours(0, 0, 0, 0);
      const verificationDate = DateUtil.safe(this.verification?.value);
      if (this.editableRules.verification && verificationDate) {
        if (value.getTime() > verificationDate.getTime()) {
          this.mainForm.patchValue({ verification: value }, { onlySelf: true, emitEvent: false });
        }
        if (this.constraints) {
          this.constraints.minPlannedVerification = value.getTime();
        }
      }
    } else {
      this.constraints.minPlannedVerification = DateUtil.today();
    }
    this.setConstraintPlannedDates(this.constraints);
    this.refreshAnticipationAttendDays();
    this.changedPlannerStartDates.emit({
      implementationStartDate: DateUtil.safe(this.implementationStartDate().value) || null,
      implementation: value
    });
  }

  onSelectVerification(value: Date) {
    if (value) {
      value.setHours(0, 0, 0, 0);
    }
    this.setConstraintPlannedDates(this.constraints);
  }

  disableField(key: string): void {
    if (this.mainForm.controls.hasOwnProperty(key)) {
      this.mainForm.controls[key].disable();
      if (key === 'code') {
        this.mainForm.patchValue({ generateCode: true }, { onlySelf: true, emitEvent: false });
      }
      if (this.requiredRules.hasOwnProperty(key) && this.requiredRules[key]) {
        console.warn(`The required field "${key}" is now disabled.`);
      }
      if (this.disabledFields) {
        this.disabledFields[key] = true;
      }
    }
  }
  enableField(key: string): void {
    if (key === 'code' && this.mainForm.controls.generateCode.value === true) {
      return;
    }
    if (this.mainForm.controls.hasOwnProperty(key)) {
      this.mainForm.controls[key].enable({ onlySelf: true, emitEvent: false });
    }
    if (this.disabledFields) {
      this.disabledFields[key] = false;
    }
  }

  private evaluateSameDayMessages() {
    if (!this.mainForm.value.typeId) {
      return;
    }
    const now = DateUtil.today();
    if (this.hiddenRules.implementation || this.disabledFields.implementation) {
      this.noticeService.notice(this.translate.instant('activity-sameDayImplementation'));
      this.mainForm.patchValue({ implementation: now }, { onlySelf: true, emitEvent: false });
    } else if (!this.implementation.value) {
      this.mainForm.patchValue({ implementation: now }, { onlySelf: true, emitEvent: false });
    }
    if (this.hiddenRules.verification || this.disabledFields.verification) {
      if (this.implementation !== null && now.getTime() < (this.implementation.value as Date).getTime()) {
        this.mainForm.patchValue({ verification: this.implementation.value }, { onlySelf: true, emitEvent: false });
      } else {
        this.mainForm.patchValue({ verification: now }, { onlySelf: true, emitEvent: false });
      }
    } else if (!this.verification.value) {
      this.mainForm.patchValue({ verification: now }, { onlySelf: true, emitEvent: false });
    }
  }

  private evaluateDisableBelongSeries() {
    if (!this.disabledFields.belongSeries) {
      return;
    }
    this.disableBelongSeries();
  }

  private disableBelongSeries() {
    if (this.belongSeries === 0) {
      return;
    }
    this.belongSeries = 1;
    this.setDisabledPeriodicityDefaults();
    this.resetPeriodicity();
  }

  private enableBelongSeriesByRow() {
    if (this.belongSeries === 1) {
      return;
    }
    this.belongSeries = 1;
    this.setEnabledPeriodicityDefaults();
    this.resetPeriodicity();
  }

  protected setDisabledPeriodicityDefaults(): void {
    this.updatePeriodicityDefault('implementationPeriodicity', PeriodicityType.never);
    this.updatePeriodicityDefault('verificationPeriodicity', PeriodicityType.never);
  }

  protected setEnabledPeriodicityDefaults(): void {
    this.updatePeriodicityDefault('implementationPeriodicity', PeriodicityType.weekly);
    this.updatePeriodicityDefault('verificationPeriodicity', PeriodicityType.weekly);
  }

  private updatePeriodicityDefault(key: 'implementationPeriodicity' | 'verificationPeriodicity', type: PeriodicityType): void {
    const periodicityComponent: PeriodicityComponent = this[key];
    periodicityComponent.defaultLoadType = type;
    periodicityComponent.loadDefaultData();
  }

  private resetPeriodicity() {
    if (!this.disabledFields.implementationPeriodicity) {
      this.mainForm.patchValue(
        {
          implementationPeriodicity: null,
          implementationStartDate: null,
          implementation: null
        },
        { onlySelf: true, emitEvent: false }
      );
      this.implementationPeriodicity.loadDefaultData();
    }
    if (!this.disabledFields.verificationPeriodicity) {
      this.mainForm.patchValue(
        {
          verificationPeriodicity: null,
          verification: null
        },
        { onlySelf: true, emitEvent: false }
      );
      this.verificationPeriodicity.loadDefaultData();
    }
  }

  protected isVerificationAvailable(_fieldName?: string): VerificationAvailable {
    return isVerificationAvailable(this.constraints, _fieldName);
  }

  protected isImplementationAvailable(_fieldName?: string): ImplementationAvailable {
    return isImplementationAvailable(this.constraints, _fieldName);
  }

  isFieldHidden(fieldName: string): boolean {
    if (this.isImplementationAvailable(fieldName) === ImplementationAvailable.UNAVAILABLE) {
      // Si no está disponible; quita el campo
      return true;
    }
    if (this.isVerificationAvailable(fieldName) === VerificationAvailable.UNAVAILABLE) {
      // Si no está disponible; quita el campo
      return true;
    }
    if (fieldName === 'verifier') {
      // updatingHistory: El historial detona una renderización sobre los implementadores (ocultar/mostrar).
      // Para mantener consistencia en datos, se ocultan verificadores hasta volver a estar disponibles.
      if (this.isVerifierRemoved() || this.updatingHistory) {
        return true;
      }
    }
    if (fieldName === 'plannerTask' && this.module === Module.PLANNER) {
      return true;
    }
    if (this.disabledFields[fieldName] || skipFieldByRecurrence(fieldName, this.belongSeries, false)) {
      return true;
    }
    if (!this.hiddenRules.hasOwnProperty(fieldName)) {
      console.error('Invalid fieldName found while trying to get hidden rules of [fieldName]: ', fieldName);
      return false;
    }
    if (fieldName === 'isPlanned') {
      // ToDo: Mover este IF al backend
      if (!this.isPlannedSwitchAvailable) {
        return true;
      }
      if (this.enableHiddenFields) {
        return false;
      }
      return this.hiddenRules[fieldName];
    }
    if (fieldName === 'plannedHours') {
      // ToDo: Mover este IF al backend
      if (!this.isPlannedHoursAvailable) {
        return true;
      }
      if (this.enableHiddenFields) {
        return false;
      }
      return this.hiddenRules[fieldName];
    }
    if (fieldName === 'plannedImplementationWeek' || fieldName === 'plannedVerificationWeek') {
      if (this.enableHiddenFields) {
        return false;
      }
      return this.hiddenRules[fieldName];
    }
    if (this.enableHiddenFields) {
      return false;
    }
    return this.hiddenRules[fieldName];
  }

  onToggleBelongSeries(): void {
    if (this.busy) {
      return;
    }
    const belongSeries = this.belongSeries === 1;
    if (belongSeries) {
      this.disableBelongSeries();
    } else {
      this.enableBelongSeriesByRow();
    }
  }

  onChangeImplementationPeriodicity() {
    if (this.busy) {
      return;
    }
    this.updateDataFromPeriodicity();
    this.setConstraintPlannedDates(this.constraints);
  }

  onChangeVerificationPeriodicity() {
    if (this.busy) {
      return;
    }
    this.updateDataFromPeriodicity();
    this.setConstraintPlannedDates(this.constraints);
  }

  private refreshAnticipationAttendDays(): void {
    if (this.busy) {
      return;
    }
    const anticipationAttendDays = calculateDiffDays(this.implementationStartDate().value || null, this.implementation.value || null);
    this.mainForm.patchValue({ anticipationAttendDays: anticipationAttendDays }, { onlySelf: true, emitEvent: false });
  }

  private updateDataFromPeriodicity() {
    const data: DataMap = {};
    // Se colocan los valores por defecto en repetición
    if (this.belongSeries && this.implementationPeriodicity.value && this.implementationPeriodicity.value.vchtipoperiodicidad !== PeriodicityType.never) {
      const anticipationAttendDays = calculateDiffDays(this.implementationPeriodicity.nextDateStart, this.implementationPeriodicity.nextDateEnd);
      data.anticipationAttendDays = anticipationAttendDays;
      data.implementation = this.implementationPeriodicity.nextDateEnd;
      if (this.disabledFields.verificationPeriodicity) {
        this.verificationPeriodicity.value = cloneObject(this.implementationPeriodicity.value);
        data.verification = this.verificationPeriodicity.nextDateEnd;
        if (!this.disabledFields.daysToVerify && !this.mainForm.controls.daysToVerify.value) {
          const daysToVerify = calculateDiffDays(this.verificationPeriodicity.nextDateStart, this.verificationPeriodicity.nextDateEnd);
          data.daysToVerify = daysToVerify;
        }
      }
    } else {
      const now = DateUtil.today();
      this.implementationPeriodicity.value = PeriodicityUtil.neverEntity(null, null, now);
    }
    if (this.belongSeries && this.verificationPeriodicity.value && this.implementationPeriodicity.value.vchtipoperiodicidad !== PeriodicityType.never) {
      data.verification = this.verificationPeriodicity.nextDateStart;
      if (!this.disabledFields.daysToVerify && !this.mainForm.controls.daysToVerify.value) {
        const daysToVerify = calculateDiffDays(this.verificationPeriodicity.nextDateStart, this.verificationPeriodicity.nextDateEnd);
        data.daysToVerify = daysToVerify;
      }
      if (this.disabledFields.implementationPeriodicity) {
        data.anticipationAttendDays = 0;
        this.implementationPeriodicity.value = cloneObject(this.verificationPeriodicity.value);
        data.implementation = this.implementationPeriodicity.nextDateEnd;
      }
    } else {
      const now = DateUtil.today();
      this.verificationPeriodicity.value = PeriodicityUtil.neverEntity(null, null, now);
      data.verification = this.verificationPeriodicity.nextDateEnd;
    }
    this.mainForm.patchValue(data);
  }

  onParticipantsSelect(args: RowSelection<ParticipantDto[]>): void {
    if (args?.value && Array.isArray(args.value)) {
      this._participants = args.value;
    } else {
      this._participants = [];
    }
  }

  setParticipantsSelectColumns(): void {
    this.participantsColumns = getParticipantColumns(this.$destroy, this.translateService, null, this.module);
  }

  onImplementerSingleClear(implementer: any): void {
    this.tempImplementers = this.tempImplementers.filter((imp) => imp !== implementer);
    this._implementerIds = this.tempImplementers.map((imp) => +imp.value);
    this.combo().value = this.implementerIds;
  }

  onPreImplementerSingleClear(preImplementer: any): void {
    this.tempPreImplementers = this.tempPreImplementers.filter((imp) => imp !== preImplementer);
    this._preImplementerIds = this.tempPreImplementers.map((imp) => +imp.value);
    this.comboPreImplementer().value = this.preImplementerIds;
  }

  public avatarString(text: string) {
    return nameInitials(text, '').toUpperCase() || null;
  }

  public avatarSrc(value: number): string {
    return RestApiModule.avatar(value);
  }

  public stringToColour(str: string): string {
    return stringToColour(str);
  }

  public textColor(hexColor): string {
    return textColor(hexColor, 150);
  }

  protected setLoadData(loadData: ActivityLoadDto) {
    const preImplementers = loadData.preImplementerUsers ? loadData.preImplementerUsers.map((u) => u.value) : [];
    this._preImplementerIds = preImplementers;
    this.tempPreImplementers = loadData.preImplementerUsers ? loadData.preImplementerUsers : [];

    const implementers = loadData.implementerUsers ? loadData.implementerUsers.map((u) => u.value) : [];
    this._implementerIds = implementers;
    this.tempImplementers = loadData.implementerUsers ? loadData.implementerUsers : [];

    this._verifierId = loadData.verifier ? loadData.verifier : null;

    const data: DataMap = {
      anticipationAttendDays: loadData.anticipationAttendDays,
      activityOrder: loadData.activityOrder,
      businessUnitDepartmentId: loadData.businessUnitDepartmentId,
      daysToVerify: loadData.daysToVerify,
      description: loadData.description,
      fillForm: loadData.fillForm,
      fillType: loadData.fillType,
      fillTypeLabel: loadData.fillTypeLabel,
      followUpImplementationDelay: loadData.followUpImplementationDelay,
      mustUpdateImplementationAtReturn: loadData.mustUpdateImplementationAtReturn,
      isPlanned: loadData.isPlanned,
      objectiveId: loadData.objectiveId,
      plannedHours: loadData.plannedHours,
      plannerTask: loadData.plannerTask || null,
      priority: loadData.priority,
      categoryId: loadData.categoryId,
      source: loadData.source,
      systemLinks: loadData.systemLinks
    };
    this.mainForm.patchValue(data, { onlySelf: true, emitEvent: false });
    this._isLoadingDuplicateActivity = false;
    this.dynamic()
      .load(loadData.id)
      .then(() => {
        this.setDynamicData(loadData.dynamicTableNameId, loadData.dynamicTableName);
      });
  }

  private setDynamicData(dynamicTableNameId: number, dynamicTableName: string): void {
    const dynamicObject = cloneObject(this.dynamic().data);
    const clonDynamicObject: DataMap<string> = {};
    for (const typeId in dynamicObject) {
      if (dynamicObject.hasOwnProperty(typeId)) {
        const data = dynamicObject[typeId];
        if (typeof data === 'string') {
          const isMarkDownField = data.includes('"text":') && data.includes('"html":');
          if (isMarkDownField) {
            clonDynamicObject[typeId] = JSON.parse(data).text;
          } else {
            clonDynamicObject[typeId] = data;
          }
        } else {
          clonDynamicObject[typeId] = data;
        }
      }
    }
    const dynamicData = {
      dynamicFieldData: clonDynamicObject,
      dynamicTableNameId: dynamicTableNameId,
      dynamicTableName: dynamicTableName
    };
    this.mainForm.patchValue({ dynamic: dynamicData }, { onlySelf: true, emitEvent: false });
  }
}
