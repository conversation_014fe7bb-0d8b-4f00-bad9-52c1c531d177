@if (busy) {
  <ng-template *ngTemplateOutlet="waitingBar"></ng-template>
}
<ng-template #waitingBar>
  <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
</ng-template>
@if (busy) {
  <div class="loader"></div>
}
<igx-tabs
  #tabsDef
  class="elevation-2 tabs-panel"
  [class.tabs-fullscreen]="enablePlanAddFullscreen"
  [class.margin-menu]="!isMenuOpen"
  (selectedIndexChange)="onTabChange($event)"
  (selectedIndexChanging)="onTabChanging($event)"
>
  <igx-tab-item #planningDefinitionTab>
    <igx-tab-header>
      <!-- Caratula -->
      <span igxTabHeaderLabel>{{ 'tab-plan' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll" #planningTab>
      <!-- Participantes, Fechas -->
      <ng-template *ngTemplateOutlet="planningDefinition"></ng-template>
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item>
    <igx-tab-header>
      <!-- backlog -->
      <span igxTabHeaderLabel>{{ 'tab-priorities' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll">
      @if (isBacklogReady) {
        <ng-template *ngTemplateOutlet="backlog"></ng-template>
      } @else {
        <div class="empty-state">{{ 'empty-backlog' | translate: this }}</div>
      }
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item #taskAssignementTab>
    <igx-tab-header>
      <!-- carga pre-asignada -->
      <span igxTabHeaderLabel>{{ 'tab-workload-full' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll">
      @if (isWorkloadTabReady) {
        @if (taskAssignementTab?.selected) {
          <ng-template *ngTemplateOutlet="taskAssignement"></ng-template>
        } @else {
          <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
        }
      } @else {
        <div class="empty-state">{{ 'empty-task-assignement' | translate: this }}</div>
      }
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item #taskSelectedTab>
    <igx-tab-header>
      <!-- carga seleccionada -->
      <span igxTabHeaderLabel>{{ 'tab-workload-selected' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll">
      @if (isWorkloadTabReady) {
        @if (taskSelectedTab?.selected) {
          <ng-template *ngTemplateOutlet="taskSelected"></ng-template>
        } @else {
          <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
        }
      } @else {
        <div class="empty-state">{{ 'empty-task-assignement' | translate: this }}</div>
      }
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item #workloadDailyTab>
    <igx-tab-header>
      <!-- Carga por día -->
      <span igxTabHeaderLabel>{{ 'tab-workload-daily' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll">
      @if (isWorkloadTabReady) {
        @if (workloadDailyTab?.selected && data) {
          <ng-template *ngTemplateOutlet="taskAssignementDaily"></ng-template>
        } @else {
          <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
        }
      } @else {
        <div class="empty-state">{{ 'empty-task-assignement' | translate: this }}</div>
      }
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item #summayTab>
    <igx-tab-header>
      <!-- Carga sin confirmar -->
      <span igxTabHeaderLabel>{{ 'tab-workload-commit' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content class="fancy-scroll">
      @if (isSummaryTabReady) {
        @if (summayTab?.selected) {
          <ng-template *ngTemplateOutlet="summary"></ng-template>
        } @else {
          <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
        }
      } @else {
        <div class="empty-state">{{ 'empty-summary' | translate: this }}</div>
      }
    </igx-tab-content>
  </igx-tab-item>
</igx-tabs>
<!-- backlog -->
<ng-template #backlog>
  <div class="grid-container grid-floating-active full-width-grid-container">
    <app-grid-side-multi-select
      #addPlanActivityList
      (dataGridToggleMaximize)="onToggleMaximize($event)"
      (dataLoaded)="onGridPlanActivityLoad($event)"
      (deleteAllRegistries)="onDeleteAllRegistries()"
      (dynamicFieldsLoaded)="onGridPlanActivityDynamicFieldsLoaded($event)"
      (importedExcel)="onImportedPrioritesData($event)"
      (rowSelectionChange)="onMovePlanActivity($event)"
      (valueGridToggleMaximize)="onToggleMaximize($event)"
      [allowMoving]="true"
      [allowRemoveAll]="true"
      [dataColumns]="addPlanActivityListColumns"
      [dataUrl]="addPlanActivityListUrl"
      [displayDensity]="displayDensity"
      [entityDynamicTypeIds]="activityTypesIds"
      [exportAutoConfig]="exportAutoConfig"
      [exportColumnsOption]="exportColumnsOption"
      [exportRecordsOption]="exportRecordsOption"
      [failedImportLabel]="'failedImportLabel' | translate: this"
      [failedImportMissingKeyLabel]="'failedImportMissingKeyLabel' | translate: this"
      [importColumnIdLabel]="importColumnIdLabel"
      [importConfirmationLabel]="'importConfirmationLabel' | translate: this"
      [importSchema]="addPlanImportSchema"
      [missingImportDataLabel]="'missingImportDataLabel' | translate: this"
      [missingImportDataFromDataSourceLabel]="'missingImportDataFromDataSourceLabel' | translate: this"
      [primaryKey]="gridPrimaryKey"
      [valueColumns]="addPlanActivityListColumns"
      id="addPlanActivityList"
      name="addPlanActivityList"
    >
    </app-grid-side-multi-select>
  </div>
</ng-template>
<!-- caratula de la planeación -->
<ng-template #planningDefinition>
  <div class="grid-container grid-floating-active" #planningContainer>
    <div class="grid-x">
      <div class="cell">
        @if (generalForm && data) {
          <form [formGroup]="generalForm" class="container-form grid-floating-action-buttons">
            <div class="header grid-container">
              <div class="grid-x">
                <div class="cell weekly-selector">
                  <!-- Fechas a planear -->
                  <app-toolbar-weekly-selector
                    #rangeSelector
                    name="rangeSelector"
                    [showSummaryButton]="false"
                    [dateLabelFrom]="'root.common.date.starts' | translate: this"
                    [dateLabelTo]="'root.common.date.ends' | translate: this"
                    [displayFormat]="displayFormat || defaultDisplayFormat"
                    [displayDensity]="EnumDisplayDensity.cosy"
                    [inputDisplayDensity]="'compact'"
                    [inputType]="'line'"
                    formControlName="rangeSelector"
                    (updatedRangeData)="onSelectRangeDate($event, true)"
                    (opened)="onOpenedWeeklySelector()"
                  ></app-toolbar-weekly-selector>
                </div>
              </div>
            </div>
            <div class="grid-container">
              <div class="grid-x grid-padding-x">
                <!-- filtro tipo actividad -->
                <div class="cell small-12 medium-6">
                  @if (tabs().selectedItem === planningDefinitionTab) {
                    <app-combo
                      #typeId
                      name="typeId"
                      valueKey="value"
                      displayKey="text"
                      formControlName="typeId"
                      [displayDensity]="displayDensity"
                      [scrollContainer]="planningTab?.nativeElement"
                      [required]="true"
                      [label]="'activity-type' | translate: this"
                      [data]="types"
                      [input]="{
                        options: types,
                        value: activityTypesIds,
                        resetOnEmpty: true
                      }"
                      (change)="onTypeChanged($event)"
                    >
                    </app-combo>
                  }
                </div>
                <div class="cell small-12 medium-6">
                  <!-- check para utilizar solo equipo seleccionado en prioridades -->
                  <igx-checkbox
                    name="isTeamScoped"
                    formControlName="isTeamScoped"
                    [checked]="isTeamScoped"
                    (change)="isTeamScoped = $event.checked; onTypeChanged(activityTypesIds)"
                  >
                    {{ 'plan-scope' | translate: this }}
                  </igx-checkbox>
                  <div class="igx-input-group__hint fake-hint">
                    <igx-hint>{{ 'plan-scope-hint' | translate: this }}</igx-hint>
                  </div>
                </div>
                <div class="cell small-12 medium-6">
                  <!-- filtro de equipo de trabajo -->
                  <app-dropdown-search
                    name="team"
                    iconName="groups"
                    formControlName="team"
                    [displayDensity]="displayDensity"
                    [scrollIntoView]="false"
                    [invalid]="invalid('team')"
                    [label]="'team' | translate: this"
                    [options]="teams"
                    (changed)="onTeamChanged($event)"
                  >
                  </app-dropdown-search>
                </div>
                <div class="cell small-12 medium-6">
                  <!-- horas por día -->
                  <igx-input-group [ngClass]="displayDensityClass" class="dropdown-search" [type]="'line'" theme="material">
                    <input
                      #textInput
                      igxInput
                      type="number"
                      (change)="onHoursPerDayChange($event.target)"
                      autocomplete="off"
                      name="hoursPerDay"
                      id="hoursPerDay"
                      formControlName="hoursPerDay"
                      required
                    />
                    <label igxLabel #labelInput for="hoursPerDay"> {{ 'hoursPerDay' | translate: this }}</label>
                    <igx-prefix>
                      <igx-icon>hourglass_bottom</igx-icon>
                    </igx-prefix>
                    @if (invalid('hoursPerDay')) {
                      <igx-suffix class="error-icon">
                        <igx-icon>error</igx-icon>
                      </igx-suffix>
                    }
                  </igx-input-group>
                </div>
                <div class="cell small-12">
                  <!-- Equipo de trabajo -->
                  <div class="grid-multi-select-container">
                    <button
                      [ngClass]="displayDensityClass"
                      [igxButton]="'outlined'"
                      type="button"
                      class="inner-icon round"
                      igxRipple
                      (click)="participantsGrid().openDialog()"
                    >
                      <igx-icon family="material">add</igx-icon>
                      <label>{{ 'root.common.field.participant' | translate: this }}</label>
                    </button>
                    <app-grid-multi-select
                      #participantsGridDef
                      id="participantsGrid"
                      name="participantsGrid"
                      class="participants"
                      [displayDensity]="displayDensity"
                      [dataUrl]="controller + '/participants/list'"
                      [inputType]="4"
                      [label]="participantsGrid().getValueGridCount() + ' ' + ('root.common.field.participant' | translate: this) + '(s)'"
                      [primaryKey]="gridPrimaryKey"
                      [hasDelete]="true"
                      [showTopExportButton]="true"
                      [exportAutoConfig]="exportAutoConfig"
                      [exportColumnsOption]="exportColumnsOption"
                      [exportRecordsOption]="exportRecordsOption"
                      [valueColumns]="participantsColumns"
                      [dataColumns]="participantsColumns"
                      [openDialogOnEnter]="false"
                      [valueAutoMinimumHeight]="true"
                      (rowSelectionChange)="onParticipantsSelect($event)"
                      (baseDialogClose)="onTypeChanged(activityTypesIds)"
                      (cellEditValueToTextChange)="onSingleValueCellTextBoxChange($event)"
                    >
                    </app-grid-multi-select>
                  </div>
                  <div class="cell small-12">&nbsp;</div>
                </div>
              </div>
            </div>
          </form>
        }
      </div>
    </div>
  </div>
</ng-template>
<!-- resumen de asignación -->
<!-- Pestaña: Carga sin confirmar-->
<ng-template #summary>
  <div class="summary-tab">
    <igx-checkbox [hidden]="!isPlanNoVerificationAvailable" [checked]="authorAsVerifier" (change)="authorAsVerifier = $event.checked">
      {{ 'author-as-verifier' | translate: this }}
    </igx-checkbox>
    <app-grid
      #summaryGrid
      (cellEdit)="onCellEdit($event)"
      (cellEditValueToTextChange)="onWorkLoadCellEditValueToTextChange($event)"
      (cellSwitchValueChange)="isPlannedSwitchChanged($event)"
      (clearCellClick)="onClearCellEdit($event)"
      (dataLoad)="onSummaryDataLoad($event)"
      (dropdownMenu)="onWorkLoadGridDropDownMenu($event)"
      (rowDragEnd)="onRowDragEnd($event)"
      [autoMinimumWidth]="false"
      [allowFiltering]="false"
      [allowGrouping]="true"
      [allowMoving]="true"
      [allowValueToTextMultipleValues]="!singleImplementer"
      [autoMinimumHeight]="false"
      [columns]="summaryColumns"
      [data]="workLoadBySelectedUserTemp"
      [disabledDynamicSearchLookup]="true"
      [displayDensity]="displayDensity"
      [exportAutoConfig]="exportAutoConfig"
      [exportColumnsOption]="exportColumnsOption"
      [exportRecordsOption]="exportRecordsOption"
      [groupKey]="summaryGroupKey"
      [groupingExpressions]="summaryGroupingExpression"
      [height]="containerSize"
      [id]="'summaryGrid'"
      [name]="'summaryGrid'"
      [paging]="false"
      [persistState]="false"
      [primaryKey]="gridPrimaryKey"
      [showRefresh]="false"
      [showSearch]="false"
      [showTitle]="true"
      [showToolbar]="true"
      [titleLabel]="'commit-workload' | translate: this"
      class="summaryGrid"
    >
    </app-grid>
  </div>
</ng-template>
<!-- asignacion por recurso [empty-state] -->
<!-- carga por día / asignacion por recurso [empty-state] -->
<!-- carga por día / asignacion por recurso -->
<ng-template #taskAssignementDaily>
  <app-activities-calendar
    #calendar
    (appointmentUpdated)="onAppointmentUpdated($event)"
    (toggleFullscreen)="toggleFullscreen()"
    (updateDateRanges)="onUpdateDateRanges($event)"
    (workloadUpdate)="onRefreshWorkloadTab($event)"
    [data]="data"
    [displayDensity]="displayDensity"
    [end]="end"
    [fullScreen]="enablePlanAddFullscreen"
    [hoursPerDay]="hoursPerDay"
    [hasSaveAccess]="hasSaveAccess"
    [hasEditAccess]="hasEditAccess"
    [localLoaderEnabled]="true"
    [schedulerFullScreenWidth]="schedulerFullScreenWidth"
    [schedulerHeight]="schedulerSize"
    [start]="start"
    [workLoadValue]="workLoadValue"
  ></app-activities-calendar>
</ng-template>
<!-- asignacion por recurso -->
<!-- Pestaña: Carga pre-asignada -->
<ng-template #taskAssignement>
  <div class="task-assignement" [class.task-assignement-fullscreen]="enablePlanAddFullscreen" [class.margin-menu]="!isMenuOpen">
    <div class="participants-cards task-assignement-section">
      <!-- Responsables -->
      <h5>{{ 'participants-assign' | translate: this }}</h5>
      <igx-list class="elevation-2" [ngClass]="displayDensityClass">
        <div [style.max-height]="containerSize" [style.overflow]="'hidden'" [style.position]="'relative'" class="virtualized-list-items fancy-scroll">
          <igx-list-item
            #igxListItem
            *igxFor="let participant of participants; let idx = index; scrollOrientation: 'vertical'; containerSize: containerSize; itemSize: itemSize"
          >
            <div
              class="item-container"
              igxDrop
              (enter)="onEnterAllowed($event, participant)"
              (leave)="onLeaveAllowed($event, participant)"
              (dropped)="onDropAllowed($event, participant.userId)"
            >
              <div class="item">
                <ng-template *ngTemplateOutlet="userInfo; context: { $implicit: participant }"> </ng-template>
              </div>
            </div>
          </igx-list-item>
        </div>
      </igx-list>
    </div>
    @if (isParticipantsAssignerReady) {
      <div class="assignment-grid task-assignement-section">
        <div class="assign-title">
          <!-- Prioridades a asignar -->
          <h5>{{ 'priorities-assign' | translate: this }}</h5>
          <div class="task-assignement-rigth-options">
            <igx-checkbox [hidden]="!isMultipleImplementerAvailable" [checked]="singleImplementer" (change)="singleImplementer = $event.checked">
              {{ 'single-implementer' | translate: this }}
            </igx-checkbox>
            <button
              [ngClass]="displayDensityClass"
              [igxIconButton]="'flat'"
              type="button"
              igxRipple
              [igxRippleCentered]="true"
              (click)="toggleFullscreen()"
              (dblclick)="$event.preventDefault()"
            >
              <igx-icon family="material">{{ enablePlanAddFullscreen ? 'close_fullscreen' : 'open_in_full' }}</igx-icon>
            </button>
          </div>
        </div>
        <app-grid
          #workLoadGrid
          class="work-load-grid full-width-grid-container"
          (cellEditValueToTextChange)="onWorkLoadCellEditValueToTextChange($event)"
          (cellEdit)="onCellEdit($event)"
          (clearCellClick)="onClearCellEdit($event)"
          (dropdownMenu)="onWorkLoadGridDropDownMenu($event)"
          (rowDragStart)="onRowDragStart($event)"
          (rowDragEnd)="onRowDragEnd($event)"
          [autoMinimumWidth]="false"
          [allowFiltering]="false"
          [allowGrouping]="false"
          [allowMoving]="true"
          [allowValueToTextMultipleValues]="!singleImplementer"
          [autoMinimumHeight]="false"
          [columns]="addedPlanActivityListColumns"
          [displayDensity]="displayDensity"
          [height]="containerSize"
          [id]="'workLoadGrid'"
          [name]="'workLoadGrid'"
          [paging]="false"
          [persistState]="false"
          [primaryKey]="gridPrimaryKey"
          [rowDraggable]="true"
          [rowSelection]="'multiple'"
          [showRefresh]="false"
          [showSearch]="false"
          [showTitle]="true"
          [disabledDynamicSearchLookup]="true"
          [showToolbar]="false"
          [data]="workLoadValue"
        >
        </app-grid>
      </div>
    } @else {
      <div class="empty-state">{{ 'empty-task-assignement' | translate: this }}</div>
    }
  </div>
</ng-template>
<!-- Pestaña: Carga seleccionada -->
<ng-template #taskSelected>
  <div class="task-selected mt-1">
    <div class="assign-title">
      <h5>{{ 'select-priorities-to-assign' | translate: this }}</h5>
    </div>
    @if (participantsPreWorkLoad.length > 0) {
      <igx-tabs #tabsResponsibles class="elevation-2" (selectedIndexChange)="onUserTabChange($event)">
        @for (participant of participantsPreWorkLoad; track participant) {
          <igx-tab-item>
            <igx-tab-header>
              <igx-avatar
                class="user-avatar"
                [src]="avatarSrc(participant.entity_id)"
                [initials]="avatarInitials(participant.entity_description)"
                [bgColor]="avatarBgColor(participant.entity_description)"
                [color]="avatarColor(participant.entity_description)"
                shape="circle"
                [attr.title]="participant.entity_description"
                size="small"
              >
              </igx-avatar>
              <span igxTabHeaderLabel>&nbsp;</span>
              @if (getCountSelectedRecors(participant.userId) > 0) {
                <igx-badge type="warning" class="text-bold water-mark" [value]="getCountSelectedRecors(participant.userId)"> </igx-badge>
              }
            </igx-tab-header>
            <igx-tab-content class="fancy-scroll">
              <ng-template *ngTemplateOutlet="userData; context: { $implicit: participant }"> </ng-template>
            </igx-tab-content>
          </igx-tab-item>
        }
      </igx-tabs>
    }
  </div>
</ng-template>
<ng-template #userInfo let-untypedParticipant>
  @if (castAsParticipantUser(untypedParticipant); as participant) {
    <div class="item__info ig-typography">
      <igx-avatar
        class="user-avatar"
        size="small"
        [src]="avatarSrc(participant.entity_id)"
        [initials]="avatarInitials(participant.entity_description)"
        [bgColor]="avatarBgColor(participant.entity_description)"
        [color]="avatarColor(participant.entity_description)"
        shape="circle"
      >
      </igx-avatar>
      <div class="user-info">
        <span class="user-name"> {{ participant.entity_description }} </span>
        <div class="user-current-workload">
          @if (pendingHours(participant) > 0) {
            <span class="workload-title">{{ 'current-workload' | translate: this }}</span
            >: <span class="clickable-chip">{{ pendingCount(participant) }}</span> {{ 'task-count-and-sum' | translate: this }}
            <span class="clickable-chip bolded">{{ pendingHours(participant) }}hrs </span>
          } @else {
            <span class="workload-title">{{ 'current-workload' | translate: this }}</span
            >:&nbsp;<span class="clickable-chip">{{ pendingCount(participant) }}</span>
            {{ 'task-count' | translate: this }}
          }
        </div>
        <div class="user-preassigned-workload">
          <span class="workload-title">{{ 'pre-assigned-workload' | translate: this }}</span
          >: <span class="clickable-chip">{{ participant.preAssignedCount ?? 0 }}</span> {{ 'activities-count-and-sum' | translate: this }}
          <span class="clickable-chip bolded">{{ participant.preAssignedHours ?? 0 }}hrs</span>
        </div>
        <div class="user-planned-workload">
          <span class="workload-title">{{ 'commit-workload' | translate: this }}</span
          >: <span class="clickable-chip">{{ participant.localCount ?? 0 }}</span> {{ 'activities-count-and-sum' | translate: this }}
          <span class="clickable-chip bolded">{{ participant.localHours  ?? 0}}hrs</span>
        </div>
      </div>
    </div>
  }
</ng-template>
<ng-template #userData let-untypedParticipant>
  @if (castAsParticipantUser(untypedParticipant); as participant) {
    <div class="participant-selected-workload mt-1">
      @if (currentParticipant === participant.userId) {
        <app-grid
          #toAssignWorkLoadGrid
          (cellEdit)="onCellEdit($event)"
          (dataLoad)="selectRecords($event)"
          (rowSelectionChange)="onSelectedPlanActivity($event, participant.userId)"
          [autoMinimumWidth]="false"
          [allowGrouping]="false"
          [allowMoving]="true"
          [autoMinimumHeight]="false"
          [columns]="toPlanActivityListColumns"
          [data]="dataToAssignWorkload"
          [displayDensity]="displayDensity"
          [height]="'600px'"
          [id]="'toAssignWorkLoadGrid'"
          [name]="'toAssignWorkLoadGrid'"
          [paging]="false"
          [persistState]="false"
          [primaryKey]="gridPrimaryKey"
          [rowSelection]="'multiple'"
          [showRefresh]="false"
          [showSearch]="false"
          [showTitle]="true"
          [showToolbar]="false"
          [sortingExpressions]="toAssignWorkLoadSortingExpression"
          class="work-load-grid full-width-grid-container mt-1 fix-odd-color"
        >
        </app-grid>
      }
    </div>
  }
</ng-template>
@if (openConfigDialog) {
  <igx-dialog
    #configDialog
    [leftButtonLabel]="'dialog-config-close' | translate: this"
    (leftButtonSelect)="configDialog ? configDialog.close() : null; openConfigDialog = false"
    [closeOnOutsideSelect]="true"
    [positionSettings]="positionSettings"
  >
    <igx-dialog-title>
      <div class="title-container">
        <div class="dialog-title">{{ 'dialog-config-title' | translate: this }}</div>
      </div>
    </igx-dialog-title>
    <div class="config-dialog-container">
      <!-- Guardar progreso automáticamente-->
      <igx-checkbox [checked]="persistProgress" (change)="changePersistProgress($event)"> {{ 'dialog-config-persist-progress' | translate: this }} </igx-checkbox>
      <!-- campos disponibles -->
      <app-combo
        name="availableColumnsConfig"
        valueKey="field"
        displayKey="header"
        [displayDensity]="displayDensity"
        [scrollContainer]="configDialog?.element"
        [required]="true"
        [label]="'availableColumnsConfig' | translate: this"
        [data]="availableColumnsData"
        [value]="availableColumnsValue"
        (change)="changeAvailableColumnsConfig($event)"
        (selectionChanging)="onSelectionChanging($event)"
      >
      </app-combo>
    </div>
  </igx-dialog>
}
