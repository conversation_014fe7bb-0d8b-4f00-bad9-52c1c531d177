import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import type { GridColumnState } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { ToolbarSelectorBaseComponent } from '@/core/toolbar-selector/toolbar-selector-base.component';
import { dispatchResize } from '@/core/utils/DeviceUtil';
import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import { DEFAULT_COLOR, nameInitials, stringColors } from '@/core/utils/string-util';
import { DatePipe } from '@angular/common';
import { type AfterViewInit, Component, Input, type OnDestroy, type OnInit, type TemplateRef, ViewContainerRef, inject, viewChild } from '@angular/core';
import type { IFilteringExpression, IFilteringExpressionsTree } from '@infragistics/igniteui-angular';
import {
  type CellType,
  type DateRange,
  type IColumnMovingEndEventArgs,
  type IGridState,
  type IGridStateOptions,
  type IGridToolbarExportEventArgs,
  type IgxCircularProgressBarComponent,
  type IgxExporterOptionsBase,
  IgxGridStateDirective,
  type IgxToggleDirective,
  type ToggleViewEventArgs
} from '@infragistics/igniteui-angular';
import type { IgxGridBaseDirective } from '@infragistics/igniteui-angular/lib/grids/grid-base.directive';
import { Subject } from 'rxjs';
import { first, takeUntil, throttleTime } from 'rxjs/operators';
import { GridComponent } from 'src/app/core/grid/grid.component';
import type { GridIcon } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { GridDataType } from 'src/app/core/grid/utils/grid-data-type';
import type { GridI18n } from 'src/app/core/grid/utils/grid-i18n';
import { GRID_STATE_FEATURES, buildColumnState, getColumnIndexKey, moveColumns, restoreColumnsState } from 'src/app/core/grid/utils/grid-state-util';
import type { GridExportResult } from 'src/app/core/grid/utils/grid.interfaces';
import { GridLocalConfig } from 'src/app/core/indexed-db/grid-local-config';
import { RestApiModule } from 'src/app/core/rest-api.module';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { DateInterval } from 'src/app/core/utils/date-util';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivityStatus } from 'src/app/shared/activities/core/activities-core.enums';
import type { ActivitiesOwnerAvatar } from 'src/app/shared/activities/core/activities-core.interfaces';
import { ActivityCoreUtil } from 'src/app/shared/activities/core/activities-core.utils';
import type { ActivityGridColumn, CacheDashboard, IActivityProgressTreeDto } from '../activities-dashboard-utils/activities-dashboard-utils.interfaces';

@Component({
  selector: 'app-activities-dashboard-base',
  template: '',
  standalone: false
})
export abstract class ActivitiesDashboardBaseComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit, i18n {
  viewContainer = inject(ViewContainerRef);

  datePipe = inject(DatePipe);

  noticeService = inject(NoticeService);
  api = inject(AppService);

  private _onDestroyCalled = false;
  private _localDataLoaded = false;
  private _saveStateDebounce = new Subject<boolean>();
  private _containerSize = '600px';
  private _localdata: IActivityProgressTreeDto[] = [];
  private _columnsStateMap: GridColumnState[] = [];
  protected $cancelableSearch = new Subject<boolean>();

  avatarColumnWidth = '140px';
  dateFilterFormat = 'dd/MM/yyyy HH:mm';
  dateFormat = 'DD/MM/YYYY';
  withDayWeekDateFormat = 'dddd DD/MM/YYYY';
  restDateFormat = 'YYYY-MM-DD';
  timestampFormat = 'dd/MM/yyyy h:mm:ss';
  isLargeScreen = this.isScreenLarge;

  emptyFilteredGridMessage = '';
  emptyGridMessage = '';

  stateOptions: IGridStateOptions = {
    columns: true,
    filtering: true,
    advancedFiltering: false,
    sorting: true,
    groupBy: true,
    paging: true,
    cellSelection: false,
    rowSelection: false,
    columnSelection: false,
    rowPinning: true,
    pinningConfig: true,
    expansion: false,
    rowIslands: false
  };
  override watchResizeEvents = true;

  get urlModule(): string {
    return 'activities/activity';
  }

  lastUpdated = Date.now();
  initialized = false;
  viewInit = false;
  busy = true;
  loading = false;

  page = 1;
  perPage = 100;
  cache: CacheDashboard = this.buildCacheGrid();

  statusNames: DataMap<GridIcon> = {};

  gridI18n: GridI18n = {};

  rowHeight = '25px';

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'compact';

  readonly stateDirective = viewChild(IgxGridStateDirective);

  readonly grid = viewChild<IgxGridBaseDirective>('gridDef');

  readonly rangeSelector = viewChild<ToolbarSelectorBaseComponent>('rangeSelector');

  get containerSize(): string {
    return this._containerSize;
  }

  get locale(): string {
    return this.translate?.currentLang || 'es';
  }

  get isSummaryChecked(): boolean {
    return this.rangeSelector()?.isSummaryChecked;
  }

  get localdata(): IActivityProgressTreeDto[] {
    return this._localdata;
  }

  set localdata(_localdata: IActivityProgressTreeDto[]) {
    // Se reparan fechas para poder usar las propiedades del GRID
    for (const data of _localdata) {
      this.normalizeData(data);
    }
    this._localdata = _localdata || [];
  }

  get rangeDescription(): string {
    const rangeSelector = this.rangeSelector();
    const selectorTitle = `${DateUtil.format(rangeSelector?.range?.start, this.restDateFormat)} -- ${DateUtil.format(rangeSelector?.range?.end, this.restDateFormat)}`;
    return selectorTitle.replace(/\//g, '-');
  }

  get exportTitle(): string {
    return `${this.i18n.title} - ${this.rangeDescription || 'not-loaded'}`.replace(/\//g, '-');
  }

  constructor() {
    super();

    this.subs.push(
      this._saveStateDebounce
        .pipe(throttleTime(300, undefined, { leading: false, trailing: true }), takeUntil(this.$destroy))
        .subscribe(() => this.executeSaveStateValue())
    );
  }

  private setupColumns() {
    for (const column of this.columns) {
      GridUtil.updateMediumScreenColumn(column, false, this.isLargeScreen);
      const columnId = getColumnIndexKey(column);
      this.columnMap[columnId] = column;
    }
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.translateService
      .getFrom(GridComponent.LANG_CONFIG, 'base')
      .pipe(takeUntil(this.$destroy))
      .subscribe((grid) => {
        this.gridI18n = grid;
      });
    this.setupColumns();
    this.restoreGridState().then(
      () => {
        if (this._localDataLoaded || this._onDestroyCalled) {
          return;
        }
        this._localDataLoaded = true;
        this.refreshData();
        dispatchResize();
      },
      (e) => {
        if (this._onDestroyCalled) {
          return;
        }
        console.error(`Failed to restore ${this.tagName} state.`, e);
        this._localDataLoaded = true;
        this.refreshData();
        dispatchResize();
      }
    );
    this.statusNames = ActivityCoreUtil.getLocalizedValuesMap({
      translateService: this.translate,
      subs: this.subs
    });
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
    this._onDestroyCalled = true;
    this.$cancelableSearch.next(true);
    this.$cancelableSearch.complete();
    super.ngOnDestroy();
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.afterWindowResized(null);
    this.detectChanges();
  }

  castCell(cell: CellType) {
    return cell;
  }

  getImplementerAvatarValues(names: string, ids: string): ActivitiesOwnerAvatar[] {
    if (names === null || typeof names === 'undefined' || names === '') {
      return [];
    }
    const arrayKey = `$${names}`;
    if (!this.cache.implementerNames[arrayKey]) {
      this.cache.implementerNames[arrayKey] = [];
      let parsedIds: string[];
      if (typeof ids === 'string') {
        parsedIds = ids.split(/\s*,\s*/g);
      } else {
        parsedIds = [ids];
      }
      for (let name of names.split(/\s*,\s*/g)) {
        const index = names.split(/\s*,\s*/g).indexOf(name);
        if (this.cache.implementerNames[arrayKey].length >= 12) {
          continue;
        }
        if (this.cache.implementerNames[arrayKey].length === 11) {
          this.cache.implementerNames[arrayKey].push({
            initials: '...',
            avatarSrc: null,
            bgColor: DEFAULT_COLOR.bgColor,
            color: DEFAULT_COLOR.color,
            name: 'Para ver el resto de implementadores ir al detalle de la actividad'
          });
          continue;
        }
        name = name.trim();
        if (!this.cache.implementerOwners[name]) {
          const stringColor = stringColors(name);
          this.cache.implementerOwners[name] = {
            initials: nameInitials(name),
            avatarSrc: RestApiModule.avatar(+parsedIds[index]),
            name: name,
            bgColor: stringColor.bgColor,
            color: stringColor.color
          };
        }
        this.cache.implementerNames[arrayKey].push(this.cache.implementerOwners[name]);
      }
    }
    return this.cache.implementerNames[arrayKey] || [];
  }

  public refreshData(): void {
    if (!this.rangeSelector()) {
      return;
    }
    this.$cancelableSearch.next(true);
    this.loading = true;
    this.api
      .get({ cancelableReq: this.$cancelableSearch, url: this.buildRefreshUrl() })
      .pipe(takeUntil(this.$cancelableSearch))
      .subscribe({
        next: (data: IActivityProgressTreeDto[]) => {
          this.cache = this.buildCacheGrid();
          this.localdata = [];
          this.reset();
          this.localdata = data;
          this.loading = false;
          this.detectChanges();
          this.grid().collapseAll();
        },
        error: (error) => {
          console.error(`Failed to load ${this.tagName}.`, error);
          this.cache = this.buildCacheGrid();
          this.localdata = [];
          this.reset();
          this.loading = false;
          this.detectChanges();
        }
      });
  }

  onUpdatedRangeData(range: DateRange): void {
    if (this.debug()) {
      console.log('Updated range', range);
    }
    this.refreshData();
  }

  avatarBackgroundColor(str: string): string {
    if (!str) {
      return null;
    }
    return stringColors(str).bgColor;
  }

  avatarInitialsColor(str: string): string {
    if (!str) {
      return null;
    }
    return stringColors(str).color;
  }

  avatarString(str: string): string {
    if (!str) {
      return null;
    }
    return nameInitials(str);
  }

  onToolbarExportingWrapper(args: IGridToolbarExportEventArgs) {
    const options: IgxExporterOptionsBase = args.options;
    options.fileName = `${this.exportTitle} - ${DateUtil.format(new Date(), 'DD/MM/YYYY H:mm:ss')}`;
  }

  onOpenToolbarConfigUi(event: ToggleViewEventArgs) {
    const toggleRef: IgxToggleDirective = event.owner;
    if (toggleRef?.closed) {
      toggleRef.closed.pipe(first(), takeUntil(this.$destroy)).subscribe(() => this.cdr.detectChanges());
    }
  }

  onToggleSummary(): void {
    this.loading = true;
    this.grid().cdr.detectChanges();
    setTimeout(() => {
      this.reset(true);
      this.detectChanges();
    }, 100);
  }

  onFilteringDone(): void {
    this.grid().notifyChanges();
    this.saveGridState();
  }

  reset(loading = false): void {
    const grid = this.grid();
    grid.expansionStates = new Map<any, boolean>();
    grid.resetCaches();
    grid.reflow();
    if (loading) {
      this.loading = false;
    }
    grid.cdr.detectChanges();
    this.cdr.detectChanges();
  }

  detectChanges(): void {
    this.cdr.detectChanges();
    this.grid().cdr.detectChanges();
  }

  private buildCacheGrid(): CacheDashboard {
    return {
      implementerNames: {},
      implementerOwners: {},
      historyStatuses: {},
      hasHistoryEvent: {},
      historyEvents: {},
      commitmentDates: {}
    };
  }

  private restoreGridState(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this._onDestroyCalled) {
        resolve();
        return;
      }
      GridLocalConfig.getValue(this.tagName).then(
        (value) => {
          if (!value || this._onDestroyCalled) {
            resolve();
            return;
          }
          if (value.resultsPerPage) {
            this.perPage = value.resultsPerPage;
          }
          if (value.currentPage) {
            this.page = value.currentPage;
          }
          if (value.columnsState) {
            this._columnsStateMap = value.columnsState;
            this.setStateGridColumns(value.columnsState);
          }
          if (value.stateSerialized) {
            try {
              const state: IGridState = JSON.parse(value.stateSerialized as string);
              if (state.filtering) {
                // Se valida si hubo algún cambio en el valor de búsqueda de los filtros aplicados y se actualiza si es el caso
                // NOTA: Este bug solo sucede en estos grids por la opción stateOptions.filtering = true;
                for (const f of state.filtering.filteringOperands) {
                  const filter = this.grid().filteringExpressionsTree?.filteringOperands.find((i) => i.fieldName === f.fieldName) as IFilteringExpressionsTree;
                  const operand = (f as IFilteringExpressionsTree).filteringOperands;
                  if (filter && (filter.filteringOperands[0] as IFilteringExpression).searchVal !== (operand[0] as IFilteringExpression).searchVal) {
                    (operand[0] as IFilteringExpression).searchVal = (filter.filteringOperands[0] as IFilteringExpression).searchVal;
                  }
                }
              }
              this.stateDirective().setState(state);
              this.detectChanges();
            } catch (e) {
              if (this._onDestroyCalled) {
                reject(e);
                return;
              }
              console.error(`Failed to restore grid ${this.tagName}. State:`, value);
              console.error(`Failed to restore grid ${this.tagName}. Error:`, e);
              GridLocalConfig.clear(this.tagName);
              reject(e);
            }
          }
          resolve();
        },
        (error) => {
          console.error(`Could not load local ${this.tagName} config.`, error);
          reject(error);
        }
      );
    });
  }

  private setStateGridColumns(columns: GridColumnState[]): void {
    restoreColumnsState(this.grid(), columns, this.columnMap, false, this.isLargeScreen, false);
    this.detectChanges();
  }

  private saveGridState() {
    if (!this.grid() || this._onDestroyCalled) {
      return;
    }
    this._saveStateDebounce.next(true);
  }

  private executeSaveStateValue() {
    const stateSerialized = this.stateDirective().getState(true, GRID_STATE_FEATURES);
    this.saveGridLocalConfig(stateSerialized);
  }
  saveGridLocalConfig(stateSerialized: string | IGridState) {
    if (this._onDestroyCalled || !this.viewInit) {
      return;
    }
    this._columnsStateMap = this.grid().columns.map((c, index) => buildColumnState(c, this.columnMap, index));
    GridLocalConfig.setValue(
      {
        resultsPerPage: this.perPage,
        currentPage: this.page,
        stateSerialized: stateSerialized,
        searchConfig: null,
        groupingExpressions: null,
        columnsState: this._columnsStateMap
      },
      this.tagName
    );
  }

  onCellClickWrapper() {
    this.saveGridState();
  }

  onColumnPinningWrapper() {
    this.saveGridState();
  }

  onSortingDoneWrapper() {
    this.saveGridState();
  }

  onColumnResizedWrapper() {
    this.saveGridState();
  }

  onDoubleClickWrapper() {
    this.saveGridState();
  }

  onColumnMovingEndWrapper($event: IColumnMovingEndEventArgs) {
    const mapColumns = moveColumns($event, this._columnsStateMap, this.grid().columns, this.columnMap);
    this._columnsStateMap = mapColumns;
    this.saveGridState();
  }

  onColumnVisibilityChangedWrapper() {
    dispatchResize();
    this.saveGridState();
  }

  getTemplateRef(_type: GridDataType): TemplateRef<any> {
    return this[`_${_type}`];
  }

  openDetail(cell: CellType): void {
    this.menuService.navigate(`menu/activities/${cell.row.data.activityId}`, Module.ACTIVITY);
  }

  isCompletedProgress(cell: CellType, elem: IgxCircularProgressBarComponent, field: string): boolean {
    elem.animate = false;
    elem.value = cell.row.data[field];
    return cell.row.data[field] >= 100;
  }

  isNotApplyVerified(cell: CellType): boolean {
    return cell.row.data.activityStatus === ActivityStatus.NOT_APPLY_VERIFIED || cell.row.data.activityStatus === ActivityStatus.NOT_APPLY;
  }

  isApply(cell: CellType): boolean {
    return !this.isNotApplyVerified(cell);
  }

  override langReady(): void {
    super.langReady();
    this.emptyFilteredGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_filtered_grid_message');
    this.emptyGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_grid_message');
  }

  onStartedExporting() {
    this.loading = true;
  }

  onEndedExporting(result: GridExportResult) {
    this.loading = false;
    if (result.fileDownloaded) {
      this.refreshData();
    } else if (result.emptySelection) {
      this.dialogService.error(this.gridI18n.empty_export_selection);
    }
  }

  protected override afterWindowResized(event: Event) {
    super.afterWindowResized(event);
    this._containerSize = `${this.windowInnerHeight - this.grid().nativeElement.offsetTop - 20}px`;
    this.autoDefineHeight();
    this.detectChanges();
  }

  private autoDefineHeight(): void {
    this.isLargeScreen = this.isScreenLarge;
    for (const column of this.columns) {
      GridUtil.updateMediumScreenColumn(column, false, this.isLargeScreen);
    }
  }

  protected formatRestDate(value: string | Date) {
    return DateUtil.format(value, this.restDateFormat);
  }

  protected formatBoolean(value: boolean) {
    if (value) {
      return this.gridI18n.yes;
    }
    return this.gridI18n.no;
  }

  protected formatterPercentage(value: number, emptyAsNa = true): string {
    if (value === null || typeof value === 'undefined') {
      if (emptyAsNa) {
        return 'NA';
      }
      return '-';
    }
    return `${this.formatNumber(value)}%`;
  }

  protected formatNumber(value: number): string {
    if (value === null || typeof value === 'undefined') {
      return null;
    }
    return NumberUtil.round(value, 2).toString();
  }

  protected isRowWithCommitmentInverval(anticipationAttendDays: number): boolean {
    return anticipationAttendDays !== null && typeof anticipationAttendDays !== 'undefined' && anticipationAttendDays > 0;
  }

  protected getCommitmentDateString(row: IActivityProgressTreeDto): string {
    const cacheKey = row.activityId;
    if (this.cache.commitmentDates[cacheKey] != null && this.cache.commitmentDates[cacheKey] !== 'undefined') {
      return this.cache.commitmentDates[cacheKey];
    }
    const commitmentDate = DateUtil.safe(row.commitmentDate);
    if (commitmentDate === null || typeof commitmentDate === 'undefined') {
      return null;
    }
    let result: string;
    if (this.isRowWithCommitmentInverval(row.anticipationAttendDays)) {
      const anticipationPeriod = DateUtil.add(commitmentDate, DateInterval.DAY, row.anticipationAttendDays);
      result = `${DateUtil.format(commitmentDate, this.dateFormat)} - ${DateUtil.format(anticipationPeriod, this.dateFormat)}`;
    } else {
      result = DateUtil.format(commitmentDate, this.dateFormat);
    }
    this.cache.commitmentDates[cacheKey] = result;
    return result;
  }

  public abstract columns: ActivityGridColumn[];

  public abstract columnMap: DataMap<ActivityGridColumn>;

  protected abstract buildRefreshUrl(): string;

  protected abstract normalizeData(data: IActivityProgressTreeDto): void;
}
