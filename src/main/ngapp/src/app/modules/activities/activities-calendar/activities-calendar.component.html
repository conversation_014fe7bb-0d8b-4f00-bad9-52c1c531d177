@if (localLoaderEnabled() && busy) {
  <div class="local-overlay">
    <igx-linear-bar class="overlayer" type="success" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
  </div>
}
@if (!!data()) {
  <div class="task-assignement-daily-container">
    <dx-scheduler
      #scheduler
      (onContentReady)="onContentReady($event)"
      (onAppointmentFormOpening)="onAppointmentFormOpening($event)"
      (onAppointmentAdded)="onAppointmentAdded($event)"
      (onAppointmentUpdated)="onAppointmentUpdated($event)"
      (onOptionChanged)="onOptionChanged($event)"
      (onAppointmentUpdating)='onAppointmentUpdating($event)'
      [firstDayOfWeek]="startDayOfWeek"
      [startDayHour]="0"
      [endDayHour]="24"
      [cellDuration]="schedulerCellDuration"
      [currentDate]="start"
      [dataSource]="schedulerDataSource"
      [timeZone]="timeZone"
      [width]="fullScreen() ? schedulerFullScreenWidth() : schedulerWidth"
      [height]="schedulerHeight() + 'px'"
      [showAllDayPanel]="true"
      [groups]="['userId']"
      appointmentTemplate="appointmentTemplate"
      resourceCellTemplate="participantTemplate"
      appointmentTooltipTemplate="tootipTemplate"
      dateCellTemplate="dateCellTemplate"
      [currentView]="currentView"
      [views]="schedulerViews()"
      id="scheduler"
    >
      <dxo-editing [allowAdding]="false" [allowDeleting]="false" [allowUpdating]="true" [allowResizing]="true"
                   [allowDragging]="true"></dxo-editing>
      <dxi-view [name]="'root.common.date.Month' | translate: this" type="timelineMonth"
                [maxAppointmentsPerCell]="maxAppointmentsPerCell" groupOrientation="vertical">
      </dxi-view>
      <dxi-view [name]="'root.common.date.Week' | translate: this" type="timelineWeek"
                [maxAppointmentsPerCell]="maxAppointmentsPerCell" groupOrientation="vertical">
      </dxi-view>
      @if (isAgendaAvailable) {
        <dxi-view [name]="'root.common.date.agenda' | translate: this" type="agenda"
                  [maxAppointmentsPerCell]="maxAppointmentsPerCell" groupOrientation="vertical">
        </dxi-view>
      }
      @if (isCalendarAvailable) {
        <dxi-view [name]="'root.common.date.calendar' | translate: this" type="month" [maxAppointmentsPerCell]="1"
                  groupOrientation="vertical"></dxi-view>
      }
      <dxi-resource fieldExpr="userId" [allowMultiple]="false" [dataSource]="participants"
                    label="Participante"></dxi-resource>
      <div *dxTemplate="let untypedArgs of 'participantTemplate'">
        @if (castAsLocalTemplateData(untypedArgs); as args) {
          <ng-template
            *ngTemplateOutlet="userInfo; context: { $implicit: getParticipant(args.data.userId) }"></ng-template>
          <div class="hours-per-date--maincontainer fancy-scroll">
            <ul class="hours-per-date--container elevation-1 fancy-scroll">
              @for (day of elapsedDays; track day) {
                <li class="ig-typography">
                  <span
                    class="hours-per-date--title"> {{ day | date: taskAssignementDateFormat : this.getTimezone() : this.getLanguage() | titlecase }}
                    : </span>
                  <span class="hours-per-date--hrs"
                        [class.red-when-exceed]="hoursPerDate(day, args) > hoursPerDay()"> {{ hoursPerDate(day, args) }}
                    hrs </span>
                  <span class="hours-per-date--flag"
                        [style.background-color]="hoursPerDateColor(day, args)"> &nbsp; </span>
                </li>
              }
            </ul>
          </div>
        }
      </div>
      <div *dxTemplate="let untypedArgs of 'appointmentTemplate'" [class.completed-task]="isCompletedTask(untypedArgs)">
        @if (castAsLocalTargetedAppointmentInfo(untypedArgs); as args) {
          <div [class.inline-divs]="participants.length > 1">
            <div>
              <span class="hours--flag" [style.color]="args.targetedAppointmentData.textColor"
                    [style.background-color]="args.targetedAppointmentData.bgColor">
                {{ strHoursPerAppointment(args.targetedAppointmentData) }}
              </span>
              <span class="appointment-description">
                {{ args.targetedAppointmentData.code }}:
                <strong> {{ args.targetedAppointmentData.description }}.&nbsp;</strong>
              </span>
            </div>
            <div class="dropdown-appointment-dates">
              {{
                interpolate('appointment-range' | translate: this, {
                  startDate: args.targetedAppointmentData.startDate | date: taskAssignementDateFormat,
                  endDate: applyPatchEndDate(args.targetedAppointmentData.endDate) | date: taskAssignementDateFormat
                })
              }}
            </div>
          </div>
        }
      </div>
      <div *dxTemplate="let untypedArgs of 'dateCellTemplate'">
        @if (castAsLocalDateCellTemplateArgs(untypedArgs); as args) {
          {{ args.date | date: taskAssignementAppointmentDateFormat : this.getTimezone() : this.getLanguage() | titlecase }}
        }
      </div>
      <div *dxTemplate="let untypedArgs of 'tootipTemplate'">
        @if (castAsLocalAppointmentData(untypedArgs); as args) {
          <div class="dx-tooltip-appointment-item">
            <div class="dx-tooltip-appointment-item-marker">
              <div class="dx-tooltip-appointment-item-marker-body"></div>
            </div>
            <div class="dx-tooltip-appointment-item-content">
              <div class="dx-tooltip-appointment-item-content-subject">{{ args.appointmentData.code || '' }}
                - {{ args.appointmentData.text }}
              </div>
              <div class="dx-tooltip-appointment-item-content-date">
                {{
                  interpolate('appointment-range' | translate: this, {
                    startDate: args.appointmentData.startDate | date: taskAssignementDateFormat,
                    endDate: args.appointmentData.endDate | date: taskAssignementDateFormat
                  })
                }}
              </div>
            </div>
            <div class="dx-tooltip-appointment-item-delete-button-container">
              <div
                class="dx-widget dx-button dx-button-mode-text dx-button-normal dx-button-has-icon dx-tooltip-appointment-item-delete-button"
                role="button"
                aria-label="edit"
              >
                <div class="dx-button-content"><i class="dx-icon dx-icon-edit"></i></div>
              </div>
            </div>
          </div>
        }
      </div>
    </dx-scheduler>
  </div>
}
<ng-template #userInfo let-untypedParticipant>
  @if (castAsParticipantUser(untypedParticipant); as participant) {
    <div class="item__info ig-typography">
      <igx-avatar
        class="user-avatar"
        [src]="avatarSrc(participant.entity_id)"
        [initials]="avatarInitials(participant.entity_description)"
        [bgColor]="avatarBgColor(participant.entity_description)"
        [color]="avatarColor(participant.entity_description)"
        size="small"
        shape="circle"
      >
      </igx-avatar>
      <div class="user-info">
        <span class="user-name"> {{ participant.entity_description }} </span>
        <div class="user-current-workload">
          @if (pendingHours(participant) > 0) {
            <span class="workload-title">{{ 'current-workload' | translate: this }}</span
            >: <span
              class="clickable-chip">{{ pendingCount(participant) }}</span> {{ 'task-count-and-sum' | translate: this }}
            <span class="clickable-chip bolded">{{ pendingHours(participant) }}hrs </span>
          } @else {
            <span class="workload-title">{{ 'current-workload' | translate: this }}</span
            >:&nbsp;<span class="clickable-chip">{{ pendingCount(participant) }}</span>
            {{ 'task-count' | translate: this }}
          }
        </div>
        <div class="user-preassigned-workload">
          <span class="workload-title">{{ 'pre-assigned-workload' | translate: this }}</span
          >: <span
          class="clickable-chip">{{ participant.preAssignedCount ?? 0 }}</span> {{ 'activities-count-and-sum' | translate: this }}
          <span class="clickable-chip bolded">{{ participant.preAssignedHours ?? 0 }}hrs</span>
        </div>
        <div class="user-planned-workload">
          <span class="workload-title">{{ 'commit-workload' | translate: this }}</span
          >: <span
          class="clickable-chip">{{ participant.localCount ?? 0 }}</span> {{ 'activities-count-and-sum' | translate: this }}
          <span class="clickable-chip bolded">{{ participant.localHours ?? 0 }}hrs</span>
        </div>
      </div>
    </div>
  }
</ng-template>
@if (!!data() && visibleTooltipConfiguration) {
  <igx-dialog
    [title]="'preferences' | translate: this"
    [isModal]="false"
    [closeOnOutsideSelect]="true"
    [focusTrap]="true"
    [closeOnEscape]="true"
    [isOpen]="true"
    (closed)="hideSettingsDialog()"
    (rightButtonSelect)="hideSettingsDialog()"
    [rightButtonLabel]="'root.common.button.close' | translate: this"
  >
    <div class="grid-container">
      <div class="grid-x grid-padding-x grid-padding-y">
        <div class="cell medium-12">
          <igx-input-group [ngClass]="displayDensityClass" type="border">
            <input id="maxRecords" type="number" igxInput name="maxRecords" [formControl]="maxRecordsControl"
                   (change)="updateAppointmentsPerCell()" />
            <label igxLabel for="maxRecords">{{ 'preferences-detail' | translate: this }}</label>
          </igx-input-group>
        </div>
        @if (showDisplayCompletedActivities()) {
          <div class="cell medium-12">
            <igx-switch
              [ngClass]="displayDensityClass"
              name="displayCompletedActivities"
              [formControl]="displayCompletedActivitiesControl"
              (change)="updateDisplayCompletedActivities()"
            >
              {{ 'display-completed-activities' | translate: this }}
            </igx-switch>
          </div>
        }
      </div>
    </div>
  </igx-dialog>
}
