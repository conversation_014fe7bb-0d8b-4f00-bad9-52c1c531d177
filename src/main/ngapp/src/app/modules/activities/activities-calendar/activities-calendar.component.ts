import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { AppService } from '@/core/services/app.service';
import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import { NoticeService } from '@/core/services/notice.service';
import type { ConfirmableFuture } from '@/core/utils/confirmable-leave.guard';
import type { DataMap, IdMap } from '@/core/utils/data-map';
import * as DateUtil from '@/core/utils/date-util';
import { DateInterval, DayInMonth } from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import { colorGradient, encodeUrlParameter, nameInitials, stringColors } from '@/core/utils/string-util';
import type { ParticipantDto, ParticipantDtoPartial } from '@/shared/activities/core/activities-core.interfaces';
import { getActivityController } from '@/shared/activities/core/utils/activity-util';
import { CommonModule, DatePipe, NgClass, TitleCasePipe } from '@angular/common';
import {
  type AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  HostListener,
  Input,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  type SimpleChanges,
  inject,
  input,
  output,
  viewChild
} from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import {
  type DateRange,
  IgxAvatarComponent,
  IgxDialogComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxSwitchComponent
} from '@infragistics/igniteui-angular';
import { DxTemplateModule } from 'devextreme-angular/core';
import { DxiResourceModule, DxiViewModule, DxoEditingModule } from 'devextreme-angular/ui/nested';
import { DxSchedulerComponent, DxSchedulerModule } from 'devextreme-angular/ui/scheduler';
import dxButton from 'devextreme/ui/button';
import dxCheckBox from 'devextreme/ui/check_box';
import type {
  AllDayPanelMode,
  AppointmentAddedEvent,
  AppointmentFormOpeningEvent,
  AppointmentUpdatedEvent,
  AppointmentUpdatingEvent,
  CellAppointmentsLimit,
  FirstDayOfWeek,
  OptionChangedEvent,
  Orientation,
  ViewType,
  dxSchedulerScrolling
} from 'devextreme/ui/scheduler';
import { takeUntil } from 'rxjs';
import { RestApiModule } from 'src/app/core/rest-api.module';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { IPlannerClientDto } from '../../planner/planner-add/planner-add.interfaces';
import type {
  IConsolidatedActivitiesAddPlanRow,
  LocalAppointmentData,
  LocalDateCellTemplateArgs,
  LocalPlannedActivity,
  LocalTargetedAppointmentInfo,
  LocalTemplateData,
  ParticipantUser,
  PendingCountAndHours,
  UserLocalPlannedActivity,
  UserPendingCountAndHours
} from '../activities-add-plan/activities-add-plan.interfaces';
import type { ActivitiesCalendarData } from './activities-calendar.data';

export type BnextDxSchedulerViewType = 'timelineWeek' | 'timelineMonth' | 'agenda' | 'month';

export type BnextDxSchedulerView =
  | (Record<BnextDxSchedulerViewType, any> | BnextDxSchedulerViewType)[]
  | {
      agendaDuration?: number;
      allDayPanelMode?: AllDayPanelMode;
      appointmentCollectorTemplate?: any;
      appointmentTemplate?: any;
      appointmentTooltipTemplate?: any;
      cellDuration?: number;
      dataCellTemplate?: any;
      dateCellTemplate?: any;
      dropDownAppointmentTemplate?: any;
      endDayHour?: number;
      firstDayOfWeek?: FirstDayOfWeek | undefined;
      groupByDate?: boolean;
      groupOrientation?: Orientation;
      groups?: string[];
      intervalCount?: number;
      maxAppointmentsPerCell?: CellAppointmentsLimit | number;
      name?: string | undefined;
      offset?: number;
      resourceCellTemplate?: any;
      scrolling?: dxSchedulerScrolling;
      startDate?: Date | number | string | undefined;
      startDayHour?: number;
      timeCellTemplate?: any;
      type?: undefined | ViewType;
    }[];
@Component({
  selector: 'app-activities-calendar',
  templateUrl: './activities-calendar.component.html',
  styleUrls: ['./activities-calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    NgClass,
    IgxLinearProgressBarComponent,
    IgxLabelDirective,
    IgxInputGroupComponent,
    FormsModule,
    IgxInputDirective,
    ReactiveFormsModule,
    DxSchedulerModule,
    DxoEditingModule,
    DxiViewModule,
    DxiResourceModule,
    DxTemplateModule,
    IgxAvatarComponent,
    TitleCasePipe,
    DatePipe,
    BnextTranslatePipe,
    IgxSwitchComponent,
    IgxDialogComponent
  ]
})
export class ActivitiesCalendarComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit, i18n, OnChanges {
  loader = inject(BnextLoaderActivationService);
  datePipe = inject(DatePipe);
  noticeService = inject(NoticeService);

  titleService = inject(Title);

  service = inject(AppService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities',
    componentName: 'activities-add-plan'
  };

  private readonly _componentPath = 'modules.activities' as const;
  private readonly _tagName = 'activities-add-plan' as const;

  public readonly hasSaveAccess = input<boolean>(false);

  public readonly hasEditAccess = input<boolean>(false);

  public readonly updateDateRanges = output<DateRange>();

  public readonly changedDisplayCompletedActivities = output<boolean>();

  public readonly changedMaxRecords = output<number>();

  public readonly workloadUpdate = output<() => void>();

  public readonly toggleFullscreen = output<() => void>();

  public readonly appointmentUpdated = output<{
    activityId: number;
    participantUser: ParticipantUser;
  }>();

  private _elapsedDays: Date[] = [];
  private _currentView: BnextDxSchedulerViewType = 'timelineWeek';
  private _schedulerFullWidthPx = 0;

  maxRecordsControl = new UntypedFormControl(50);
  displayCompletedActivitiesControl = new UntypedFormControl(false);
  currentView: BnextDxSchedulerViewType = 'timelineWeek';
  taskAssignementDateFormat = 'EEEE, dd/MMM'; // Ej. Miercoles, 03/Mar
  taskAssignementAppointmentDateFormat = 'EEEE d'; // Ej. Miercoles 3
  private clientsCatalog: IPlannerClientDto[] = [];

  busy = true;
  visibleTooltipConfiguration = false;

  // Implemetiation previus values
  prevStartDate = null;
  prevEndDate = null;
  prevPlannedHours = null;

  clientLabel = 'Cliente';
  responsibleLabel = 'Responsable';
  startDateLabel = 'Fecha inicio';
  endDateLabel = 'Fecha fin';
  estimatedHoursLabel = 'Horas estimadas (planeadas)';
  authorLabel = 'Autor';

  readonly scheduler = viewChild(DxSchedulerComponent);

  public readonly schedulerFullScreenWidth = input(`${this.containerSizeWidth}px`);

  public readonly hoursPerDay = input<number>(undefined);

  public readonly fullScreen = input(false);

  public readonly hideFullScreen = input(false);

  public readonly showDisplayCompletedActivities = input<boolean>(false);

  public readonly displayCompletedActivities = input<boolean>(false);

  public readonly maxRecords = input<number>(50);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public start: Date = null;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public end: Date = null;

  public readonly data = input<ActivitiesCalendarData>(null);

  public readonly workLoadValue = input<IConsolidatedActivitiesAddPlanRow[]>([]);

  public readonly hideBreakDownPendings = input(false);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public breakDownPendings = false;

  public readonly schedulerHeight = input<number>(null);

  public readonly localLoaderEnabled = input(false);

  public readonly schedulerViews = input<BnextDxSchedulerView>(['timelineMonth', 'timelineWeek']);

  private get _module(): Module {
    return Module.ACTIVITY;
  }
  override get componentPath(): string {
    return this._componentPath;
  }
  override get tagName(): string {
    return this._tagName;
  }
  override get customTagName(): string {
    return this._tagName;
  }
  get controller(): string {
    return getActivityController(this._module);
  }
  get maxAppointmentsPerCell(): number {
    return +this.appointmentsPerCell || this.hoursPerDay() || 9;
  }
  get isCalendarAvailable(): boolean {
    return this.schedulerViews().indexOf('month') !== -1;
  }
  get isAgendaAvailable(): boolean {
    return this.schedulerViews().indexOf('agenda') !== -1;
  }
  get schedulerFullWidthPx(): number {
    return +this._schedulerFullWidthPx || 100;
  }
  get schedulerDataSource(): UserLocalPlannedActivity[] {
    return this.data()?.schedulerDataSource || [];
  }
  get participantIds(): number[] {
    return this.data()?.participantIds || [];
  }
  get participants(): ParticipantUser[] {
    return this.data()?.participants || [];
  }
  get participantWorkloadMap(): IdMap<UserPendingCountAndHours> {
    return this.data()?.participantWorkloadMap || {};
  }
  get schedulerDateCache(): IdMap<[start: Date, end: Date]> {
    return this.data()?.schedulerDateCache || {};
  }
  get participantWorkload(): {
    participantIds: number[];
    start: Date;
    end: Date;
    dataSource?: IdMap<UserPendingCountAndHours>;
  } {
    return this.data()?.participantWorkload || null;
  }
  get participantMap(): IdMap<ParticipantUser> {
    return this.data()?.participantMap || {};
  }
  get participantNameMap(): DataMap<string> {
    return this.data()?.participantNameMap || {};
  }
  get appointmentsPerCell(): number {
    return this.data()?.appointmentsPerCell || 9;
  }
  get startDayOfWeek(): FirstDayOfWeek {
    return this.data()?.startDayOfWeek ?? 1;
  }
  get elapsedDays(): Date[] {
    return this._elapsedDays || [];
  }
  get containerSize(): string {
    return `${this.windowInnerHeight - 165}px`;
  }
  get schedulerCellDuration(): number {
    return Math.ceil(24 * 60); // <-- 1 sola celda para todo el día, workaround para no manejar horas
  }
  get schedulerWidthPx(): number {
    return this.containerSizeWidth - this.getRemToPixels(5);
  }
  get schedulerWidth(): string {
    return `${this.schedulerWidthPx}px`;
  }

  @HostListener('window:beforeunload', ['$event'])
  public canLeaveWithoutConfirmation(): ConfirmableFuture {
    // ToDo: Devolver `false` cuando existan cambios sin guardar
    return true;
  }

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.start != null) {
      this.updateRanges(this.start, this.currentView);
    } else {
      this.updateRanges(new Date(), this.currentView);
    }
    this.service
      .get({ url: 'activities/activity/clients-planner-active-by-user', cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe((response: IPlannerClientDto[]) => {
        this.clientsCatalog = response;
      });
  }

  public ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.displayCompletedActivities) {
      this.displayCompletedActivitiesControl.setValue(this.displayCompletedActivities());
    }
    if (changes.maxRecords) {
      this.maxRecordsControl.setValue(this.maxRecords());
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  override langReady(): void {
    this.clientLabel = this.tag('appointmentForm.client');
    this.responsibleLabel = this.tag('appointmentForm.responsible');
    this.startDateLabel = this.tag('appointmentForm.startDate');
    this.endDateLabel = this.tag('appointmentForm.endDate');
    this.estimatedHoursLabel = this.tag('appointmentForm.estimatedHours');
    this.authorLabel = this.tag('appointmentForm.author');
    this.cdr.detectChanges();
  }

  castAsLocalTemplateData(args: LocalTemplateData): LocalTemplateData {
    return args;
  }
  castAsLocalAppointmentData(args: LocalAppointmentData): LocalAppointmentData {
    return args;
  }
  castAsLocalTargetedAppointmentInfo(args: LocalTargetedAppointmentInfo): LocalTargetedAppointmentInfo {
    return args;
  }
  isCompletedTask(args: LocalTargetedAppointmentInfo): boolean {
    return args.targetedAppointmentData.pendingStatus === 5;
  }
  castAsParticipantUser(args: ParticipantUser): ParticipantUser {
    return args;
  }
  castAsLocalDateCellTemplateArgs(args: LocalDateCellTemplateArgs): LocalDateCellTemplateArgs {
    return args;
  }
  avatarInitials(name: string): string {
    return nameInitials(name);
  }
  avatarSrc(value: number): string {
    return RestApiModule.avatar(value);
  }
  avatarBgColor(name: string): string {
    return stringColors(name).bgColor;
  }
  avatarColor(name: string): string {
    return stringColors(name).color;
  }
  pendingCount(_participant: ParticipantDto | ParticipantDtoPartial): number {
    return this.data().pendingCount(_participant);
  }
  pendingHours(_participant: ParticipantDto | ParticipantDtoPartial): number {
    return this.data().pendingHours(_participant);
  }
  hoursPerDateColor(date: Date, args: LocalTemplateData): string {
    return this.data().hoursPerDateColor(this.hoursPerDay(), date, args);
  }
  hoursPerDate(date: Date, args: LocalTemplateData): number {
    return this.data().hoursPerDate(date, args);
  }

  strHoursPerAppointment(appointment: UserLocalPlannedActivity): string {
    if (!appointment.localPlannedHours && !appointment.plannedHours) {
      return '0hrs';
    }
    appointment.localPlannedHours = NumberUtil.round(appointment.localPlannedHours, 2);
    if (appointment.localPlannedHours === appointment.plannedHours) {
      return `${appointment.localPlannedHours}hrs`;
    }
    return `${appointment.localPlannedHours || 0}hrs`; // Horas estimadas --> ${appointment.plannedHours || 0}hrs`;
  }

  private modifyDate(
    fieldNewValue: 'localStartDate' | 'localEndDate',
    newValue: Date,
    row: { localStartDate: Date; localEndDate: Date }
  ): { localStartDate: Date; localEndDate: Date } {
    return this.data().modifyDate(this.start, this.end, fieldNewValue, newValue, row, this.noticeService);
  }

  private updateRanges(d, currentView: string): void {
    const date = new Date(DateUtil.safe(d).getTime());
    switch (currentView) {
      case 'week': {
        const monday = DateUtil.getDayInIsoWeek(0, date); // Lunes
        if (this.start.getTime() !== monday.getTime()) {
          this.start = monday;
          this.generateWeekDays();
        }
        this.end = DateUtil.getDayInIsoWeek(6, date); // Domingo
        break;
      }
      case 'month': {
        const newMothStart = DateUtil.getDayInMonth(DayInMonth.FIRST, date);
        if (this.start !== newMothStart) {
          this.start = newMothStart;
          this.generateWeekDays();
        }
        this.end = DateUtil.getDayInMonth(DayInMonth.LAST, date);
        break;
      }
    }
    let _elapsedDays = DateUtil.elapsedDays(this.end, this.start);
    if (_elapsedDays < 0) {
      return;
    }
    let _tempStart = DateUtil.trunc(this.start, true);
    this._elapsedDays = [_tempStart];
    while (_elapsedDays--) {
      _tempStart = DateUtil.add(_tempStart, DateInterval.DAY, 1);
      this._elapsedDays.push(_tempStart);
    }
    this.updateDateRanges.emit({
      start: this.start,
      end: this.end
    });
  }

  public generateWeekDays(): void {
    this.data().generateWeekDays(this.start);
  }

  public refreshCalendarRange(date: Date = this.start, type: BnextDxSchedulerViewType = this._currentView): void {
    this._refreshCalendar(date, type);
  }

  private _refreshCalendar(date: Date, type: BnextDxSchedulerViewType): void {
    // Se realiza esta validación debido a que el valor del currentView del scheduler cambia por el idioma configurado
    this._currentView = type;
    switch (type) {
      case 'timelineWeek':
        this.updateRanges(date, 'week');
        break;
      case 'month':
      case 'timelineMonth':
        this.updateRanges(date, 'month');
        break;
    }
    const localActivities = this.saveLocalActivities();
    this._refreshWorkloadTab(() => {
      for (const p of this.participants) {
        const userId = p.entity_id;
        const workloadMapElement = this.participantWorkloadMap[userId];
        if (workloadMapElement) {
          workloadMapElement.localActivities = localActivities[userId] || [];
        }
      }
      this.cdr.detectChanges();
      this.loadBreakDownPendings();
    });
  }

  private getNormalizedTabName(value: string): BnextDxSchedulerViewType {
    switch (value) {
      case 'Mes':
      case 'Month':
      case 'timelineMonth':
        return 'timelineMonth';
      case 'Calendario':
      case 'month':
        return 'month';
      default:
        return 'timelineWeek';
    }
  }

  private saveLocalActivities(): LocalPlannedActivity[][] {
    const mapRecovery = [];
    for (const p of this.participants) {
      const userId = p.entity_id;
      mapRecovery[userId] = this.participantWorkloadMap[userId]?.localActivities || [];
    }
    return mapRecovery;
  }
  onOptionChanged(e: OptionChangedEvent) {
    if (this.debug()) {
      console.log('>> onOptionChanged: ', e.name, e);
    }
    this.data().normalizeParticipanWorkloads(this.start, this.end);
    switch (e.name) {
      case 'currentView': {
        // Se realiza esta validación debido a que el valor del currentView del scheduler cambia por el idioma configurado
        const view: BnextDxSchedulerViewType = this.getNormalizedTabName(e.value);
        switch (view) {
          case 'timelineMonth':
          case 'month':
            this._refreshCalendar(this.start, view);
            break;
          default:
            this._refreshCalendar(new Date(), view);
            break;
        }
        break;
      }
      case 'currentDate':
        // ToDo: Agregar validaciones de fechas y ajustes a la construcción de los pendientes al cambiar de semana o mes
        if (!DateUtil.isSameDay(DateUtil.safe(e.value), this.start)) {
          this._refreshCalendar(DateUtil.safe(e.value), this._currentView);
        }
        break;
    }
    this.cdr.detectChanges();
  }
  onAppointmentUpdating(e: AppointmentUpdatingEvent) {
    if (this.debug) {
      console.log('>> onAppointmentUpdating', e);
    }
    if (this.needPatchEndDate(e)) {
      if (this.debug) {
        console.log('>> >> fixing date');
      }
      e.newData.endDate = this.applyPatchEndDate(e.newData.endDate);
    }
  }

  onAppointmentUpdated(e: AppointmentUpdatedEvent) {
    const readOnly = this.isRecordDisabled(e.appointmentData);
    if (readOnly) {
      console.warn('The activity is read only, no changes will be saved', e);
      return;
    }
    if (this.debug()) {
      console.log('>> onAppointmentUpdated: ', e);
    }
    // Se guarda cambios de implementacion plandada.
    let activityId = null;
    if (e.appointmentData.implementationId) {
      activityId = e.appointmentData.implementationId;
      this.updateWorkloadCalendarImplementations(e);
      this.dialogService.input(this.tag('confirm-changes')).then((comment: DialogResult) => {
        const changes = {
          reminder: {
            oldValue: this.prevEndDate,
            value: e.appointmentData.endDate,
            comment: comment.inputValue || '',
            deleteValue: false
          },
          startDate: {
            oldValue: this.prevStartDate,
            value: e.appointmentData.startDate,
            comment: comment.inputValue || '',
            deleteValue: false
          },
          plannedHours: {
            oldValue: this.prevPlannedHours,
            value: e.appointmentData.localPlannedHours,
            comment: comment.inputValue || '',
            deleteValue: false
          }
        };
        this.saveChangeAction(changes, e.appointmentData.implementationId, e);
      });
    } else {
      activityId = e.appointmentData.activityId;
    }
    // guardar cambio de horas planeadas (las horas "planeadas" son las asingadas por implementación, las estimadas son las de la padre)
    const activity = this._getWorkloadActivity(activityId);
    if (activity) {
      activity.localPlannedHours = e.appointmentData.localPlannedHours || e.appointmentData.plannedHours || 0;
    }
    // Se actualiza también el objeto de workLoadMap.
    this.updateWorkLoadMap(e.appointmentData.userId, activityId, e.appointmentData.localPlannedHours, e.appointmentData.plannedHours);
    // guardar nuevas fechas en cache
    this.refreshSchedulerDateCache(activityId, e.appointmentData.startDate, e.appointmentData.endDate);
    // normalizar las actividades de carga seleccionada
    this.data().normalizeParticipanWorkload(this.participantMap[e.appointmentData.userId], this.start, this.end);
    this.appointmentUpdated.emit({
      activityId: activityId,
      participantUser: this.participantMap[e.appointmentData.userId]
    });
    this.cdr.detectChanges();
  }
  onAppointmentAdded(e: AppointmentAddedEvent) {
    if (this.debug()) {
      console.log('>> onAppointmentAdded: ', e);
    }
    this.cdr.detectChanges();
  }

  private updateWorkloadCalendarImplementations(e: AppointmentUpdatedEvent) {
    for (const key in this.participantWorkloadMap[e.appointmentData.userId].pendingCountAndHours) {
      for (const f of this.participantWorkloadMap[e.appointmentData.userId].pendingCountAndHours[key].filter(
        (p) => p.implementationId === e.appointmentData.implementationId
      )) {
        f.pendingEndDate = DateUtil.safe(e.appointmentData.endDate);
        f.pendingDate = DateUtil.safe(e.appointmentData.startDate);
      }
    }
  }

  private getCLientsDataSource(data: UserLocalPlannedActivity): IPlannerClientDto[] {
    if (data.clientId !== null && typeof data.clientId !== 'undefined') {
      const client = this.clientsCatalog.find((client) => client.value === data.clientId);
      if (!client) {
        const clients = [...this.clientsCatalog];
        const newClient: IPlannerClientDto = {
          businessUnitIds: null,
          text: data.clientDescription,
          value: data.clientId
        };
        clients.push(newClient);
        return clients;
      }
      return this.clientsCatalog;
    }
    return this.clientsCatalog;
  }

  /**
   * Documentación del evento
   * https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxScheduler/Configuration/#onAppointmentFormOpening
   * @param data
   */
  onAppointmentFormOpening(data: AppointmentFormOpeningEvent) {
    const appData = data.appointmentData;
    // Mostrar título
    data.popup.option('showTitle', true);
    const readOnly = this.isRecordDisabled(appData);
    const subtitle = !readOnly ? this.tag('appointmentForm.activity-config-subtitle') : this.tag('appointmentForm.activity-view-subtitle');
    const appDataCode = encodeUrlParameter(appData.code);
    const href = appData.implementationId && appData.code ? `<a href="./es/menu/activities/code/${appDataCode}" target="_blank"> ${appData.code} </a>` : null;
    const code = href ? href : appData.code || this.tag('activity-to-define') || 'Por definir';

    const template = `<h6>${code} - ${appData.text} </h6><span class="dx-popup-subtitle"> ${subtitle} </span>`;
    data.popup.option('titleTemplate', template);
    const form = data.form;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _self = this;
    const ref = {
      availableActivities: this._getAvailableActivitiesFor(appData.userId, [appData.activityId])
    };
    const clients = this.getCLientsDataSource(appData as UserLocalPlannedActivity);
    /**
     * Configuración de `form`:
     * https://js.devexpress.com/Documentation/Guide/UI_Components/Form/Configure_Simple_Items/
     *
     * - Campos disponibles:    https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxForm/Item_Types/SimpleItem/#editorType
     * - Campo select:          https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxSelectBox/
     * - Campo fecha:           https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxDateBox/
     */
    form.option('items', [
      {
        label: {
          text: this.clientLabel
        },
        editorType: 'dxSelectBox',
        dataField: 'clientId',
        editorOptions: {
          readOnly: true,
          items: clients,
          displayExpr: 'text',
          valueExpr: 'value'
        }
      },
      {
        label: {
          text: this.responsibleLabel
        },
        editorType: 'dxSelectBox',
        dataField: 'userId',
        editorOptions: {
          readOnly: true,
          items: _self.participants,
          displayExpr: 'entity_description',
          valueExpr: 'entity_id',
          onValueChanged(args) {
            const userId = +args.value;
            const activityId = +form.getEditor('activityId').option().value;
            ref.availableActivities = _self._getAvailableActivitiesFor(userId, [activityId]);
          }
        }
      },
      {
        label: {
          text: this.startDateLabel
        },
        dataField: 'startDate',
        editorType: 'dxDateBox',
        editorOptions: {
          width: '100%',
          type: 'date',
          readOnly: readOnly,
          onValueChanged(args) {
            // Solo actualizar si es interaccion por usuario en caso de que valueChange se detone por update en la UI
            if (args.event) {
              const startDate = args.value;
              const endDate = form.getEditor('endDate').option().value;
              const dates = { localStartDate: startDate, localEndDate: endDate };
              _self.modifyDate('localStartDate', startDate, dates);
              form.updateData('endDate', new Date(dates.localEndDate.getTime()));
              _self.prevStartDate = args.previousValue;
            }
          }
        }
      },
      {
        label: {
          text: this.endDateLabel
        },
        dataField: 'endDate',
        editorType: 'dxDateBox',
        editorOptions: {
          width: '100%',
          type: 'date',
          readOnly: readOnly,
          onValueChanged(args) {
            // Solo actualizar si es interaccion por usuario en caso de que valueChange se detone por update en la UI
            if (args.event) {
              const startDate = form.getEditor('startDate').option().value;
              const endDate = args.value;
              const dates = { localStartDate: startDate, localEndDate: endDate };
              _self.modifyDate('localEndDate', endDate, dates);
              form.updateData('startDate', new Date(dates.localStartDate.getTime()));
              _self.prevEndDate = args.previousValue;
            }
          }
        }
      },
      {
        label: {
          text: this.estimatedHoursLabel
        },
        editorType: 'dxNumberBox', // <-- https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxNumberBox
        dataField: 'localPlannedHours',
        editorOptions: {
          min: 0.1,
          readOnly: readOnly,
          onValueChanged(_args) {
            if (this.debug) {
              console.log('>> localPlannedHours -> ', _args);
            }
            _self.prevPlannedHours = _args.previousValue;
          }
        }
      },
      {
        label: {
          text: this.authorLabel
        },
        editorType: 'dxTextBox',
        dataField: 'createdByUser',
        editorOptions: {
          readOnly: true,
          items: appData,
          displayExpr: 'createdByUser'
        }
      }
    ]);
    this.cdr.detectChanges();
  }

  private refreshSchedulerDateCache(activityId: number, start: string | Date, end: string | Date): void {
    this.data().refreshSchedulerDateCache(activityId, start || this.start, end || this.end);
  }
  private _getWorkloadActivity(activityId: number): IConsolidatedActivitiesAddPlanRow {
    // ToDo: Usar un mapa por performance
    return this.workLoadValue().find((a) => a.entity_id === activityId) || null;
  }
  /**
   * Obtiene el listado de actividades que se le pueden asignar a un usuario
   * (solo excluye las que ya tiene)
   *
   * @param userId
   * @param whitelist : utilizado para INCLUIR una actividad en el listado, en especifico la que ya pertenece al registro
   * @returns
   */
  private _getAvailableActivitiesFor(userId: number, whitelist: number[] = []): IConsolidatedActivitiesAddPlanRow[] {
    return this.workLoadValue().filter((activity) => {
      if (whitelist.indexOf(activity.entity_id) !== -1) {
        return true;
      }
      if (this.participantWorkloadMap[userId].localActivities?.find((a) => a.activityId === activity.entity_id)) {
        // excluye las actividades que ya tiene assignadas
        return false;
      }
      return true;
    });
  }
  public refreshDailyWorkloadTab(): void {
    setTimeout(() => {
      this.cdr.detectChanges();
      this.scheduler()?.instance.repaint();
    }, 100);
  }
  private _refreshWorkloadTab(then: () => void): void {
    this.workloadUpdate.emit(then);
  }
  shouldRefreshWorkloadTab(): boolean {
    return this.data().shouldRefreshWorkloadTab(this.start, this.end);
  }
  public getParticipant(userId: number): ParticipantUser {
    return this.data().getParticipant(userId);
  }
  public setParticipantsDataSource(dataSource: IdMap<UserPendingCountAndHours>): void {
    this.data().setParticipantsDataSource(dataSource, this.start, this.end);
  }

  public loadBreakDownPendings(update = false): void {
    if (update) {
      this.breakDownPendings = !this.breakDownPendings;
    }
    if (this.breakDownPendings) {
      this.data().clearDataSource(); // <-- Se borra y se vuelve a llenar (...)
      for (const p of this.participants) {
        const userId = p.entity_id;
        if (!this.participantWorkloadMap[userId]) {
          console.error(`Invalid \`loadBreakDownPendings\` call!, missing \`_participantWorkloadMap\` for: ${userId}`);
          continue;
        }
        const activityIds = this.workLoadValue().map((row) => row.entity_id);
        const localActivities = this.participantWorkloadMap[userId].localActivities;
        if (localActivities?.length > 0) {
          for (const activity1 of localActivities.filter((activity: LocalPlannedActivity) => activityIds.indexOf(activity.activityId) !== -1)) {
            const cache: [start: Date, end: Date] = this.schedulerDateCache[activity1.activityId] || [this.start, this.end];
            const data = this.data();
            const valueToEvaluate = data.workTimeByDayForResponsibles[userId] ? data.workTimeByDayForResponsibles[userId] : this.hoursPerDay();
            const colorHours: number = activity1.plannedHours > valueToEvaluate ? valueToEvaluate : activity1.plannedHours;
            const bgColor = colorGradient(colorHours / valueToEvaluate);
            const r: UserLocalPlannedActivity = this.data().getUserLocalPlannedActivityInstance(
              cache[0],
              cache[1],
              userId,
              activity1.description,
              activity1.code,
              activity1.activityId,
              activity1.implementationId,
              null,
              activity1.clientId,
              activity1.clientDescription,
              activity1.createdByUser,
              activity1,
              bgColor
            );
            this.schedulerDateCache[activity1.activityId] = cache;
            this.data().pushToDataSource(r);
          }
        }
        if (!this.participantWorkloadMap[userId]?.pendingCountAndHours) {
          continue;
        }

        const stringDates: string[] = Object.keys(this.participantWorkloadMap[userId]?.pendingCountAndHours);
        for (const stringDate of stringDates) {
          const startDate = DateUtil.trunc(DateUtil.parse(stringDate.substring(0, 10), 'YYYY-MM-DD', this.getLanguage()));
          const pendingCountAndHours: PendingCountAndHours[] = this.participantWorkloadMap[userId].pendingCountAndHours[stringDate];
          for (const pending of pendingCountAndHours) {
            let endDate: Date;
            if (pending.pendingEndDate) {
              endDate = DateUtil.trunc(pending.pendingEndDate);
            } else {
              endDate = startDate;
            }
            const r: UserLocalPlannedActivity = this.data().getUserLocalPlannedActivityInstance(
              startDate,
              endDate,
              userId,
              pending.description,
              pending.code,
              pending.activityId,
              pending.implementationId,
              pending.pendingStatus,
              pending.clientId,
              pending.clientName,
              pending.creatorUserName
            );
            r.plannedHours = pending.pendingPlannedHours || 0;
            r.localPlannedHours = pending.pendingPlannedHours || 0;
            r.pendingStatus = pending.pendingStatus;
            r.disabled = this.isRecordDisabled(pending);
            this.data().pushToDataSource(r);
          }
        }
      }
      this.refreshDailyWorkloadTab();
    } else {
      this.data().normalizeSchedulerDataSource(this.workLoadValue(), this.getLanguage(), this.hoursPerDay(), this.start, this.end);
      this.cdr.detectChanges();
      this.refreshDailyWorkloadTab();
    }
  }

  private isRecordDisabled(pending: any) {
    const isEdition = !!pending.implementationId;
    if (isEdition) {
      return !this.hasEditAccess();
    }
    return !this.hasSaveAccess();
  }

  onToggleFullscreen(): void {
    this.toggleFullscreen.emit(() => {});
  }
  onContentReady(_e): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _self = this;
    const container = document.getElementsByClassName('dx-toolbar-after')[0];
    if (container?.classList.contains('options-loaded')) {
      return;
    }
    const options = {
      text: this.translate.instantFrom(ActivitiesCalendarComponent.LANG_CONFIG, 'breakDownTasks'),
      value: this.breakDownPendings,
      onValueChanged() {
        _self.loadBreakDownPendings(true);
      }
    };
    const optionsButton = {
      icon: _self.fullScreen() ? 'collapse' : 'expandform',
      elementAttr: {
        id: 'buttonFullScreen',
        class: 'fullscreen-scheduler-button'
      },
      onClick() {
        _self.onToggleFullscreen();
      }
    };
    if (container) {
      if (!this.hideBreakDownPendings()) {
        const element = document.createElement('div');
        container.prepend(element);
        new dxCheckBox(element, options);
      }
      if (!this.hideFullScreen()) {
        const elementbutton = document.createElement('div');
        container.append(elementbutton);
        new dxButton(elementbutton, optionsButton);
      }
      container.classList.add('options-loaded');
    }
    _self.setPreferencesButton();
  }

  private setPreferencesButton(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const _self = this;
    const containerConfig = document.getElementsByClassName('dx-scheduler-header-panel-empty-cell')[0];
    const existsButton = document.getElementById('buttonSettings');
    if (containerConfig && existsButton === null) {
      const optionsButton = {
        icon: 'preferences',
        elementAttr: {
          id: 'buttonSettings',
          class: 'navigate-icons'
        },
        onClick() {
          _self.openSettings();
        }
      };
      const element = document.createElement('div');
      containerConfig.append(element);
      new dxButton(element, optionsButton);
      this.busy = false;
    } else {
      // Parche: En el repaint, al desglosar pendientes, no se carga inmediatamente la sección donde se requiere el icono de configuración
      setTimeout(() => {
        _self.setPreferencesButton();
      }, 100);
    }
  }

  openSettings() {
    this.visibleTooltipConfiguration = true;
    this.cdr.detectChanges();
  }

  hideSettingsDialog() {
    this.visibleTooltipConfiguration = false;
    this.cdr.detectChanges();
  }

  public updateAppointmentsPerCell(): void {
    const value = this.maxRecordsControl.value;
    if (value) {
      this.data().setAppointmentsPerCell(value);
      this.cdr.detectChanges();
      this.refreshDailyWorkloadTab();
    }
    this.changedMaxRecords.emit(+this.maxRecordsControl.value);
  }

  public updateDisplayCompletedActivities(): void {
    this.changedDisplayCompletedActivities.emit(this.displayCompletedActivitiesControl.value);
  }

  saveChangeAction(changes, entityId, event) {
    const url = `${`${this.controller}/planned-implementation-change`.trim().replace(/\\/, '/').replace(/^\//, '').replace(/\/$/, '')}/${entityId || ''}`;
    this.service
      .post({
        url: url,
        postBody: changes,
        options: {
          headers: {
            'Content-Type': 'application/json'
          }
        },
        cancelableReq: this.$destroy,
        handleFailure: false
      })
      .subscribe(
        (response) => {
          if (response) {
            this.noticeService.notice(this.tag('change-save-success'));
          }
          this.clearPrevValues();
          this.cdr.detectChanges();
        },
        () => {
          this.noticeService.notice(this.tag('change-save-fail'));
          const activity = this._getWorkloadActivity(entityId);
          if (activity) {
            activity.localPlannedHours = this.prevPlannedHours || 0;
          }
          if (this.prevStartDate) {
            event.appointmentData.startDate = this.prevStartDate;
          }
          if (this.prevEndDate) {
            event.appointmentData.endDate = this.prevEndDate;
          }
          if (this.prevPlannedHours) {
            event.appointmentData.localPlannedHours = this.prevPlannedHours;
          }
          this.refreshSchedulerDateCache(entityId, this.prevStartDate, this.prevEndDate);
          this.loadBreakDownPendings();
          this.clearPrevValues();
          this.cdr.detectChanges();
        },
        () => {
          this.cdr.detectChanges();
        }
      );
  }

  clearPrevValues() {
    this.prevEndDate = null;
    this.prevStartDate = null;
    this.prevPlannedHours = null;
  }

  private needPatchEndDate(e: AppointmentUpdatingEvent): boolean {
    // Fix al arrastrar appointment: por algún extraño motivo la fecha fin brinca al siguiente día por 59 minutos al soltar.
    // Fix al redimensionar appointment: por algún extraño motivo la fecha fin se queda en el mismo día pero con 00 minutos
    // al redimensionar.
    const newEndDate = e.newData.endDate;
    return DateUtil.isSameDay(newEndDate, e.oldData.endDate) || (newEndDate.getHours() === 0 && newEndDate.getMinutes() === 59);
  }

  public applyPatchEndDate(date: string | Date): string | Date {
    const elapsedMinutes: string = DateUtil.format(date, 'HH:mm:ss');
    // Fix al arrastrar appointment: por algún extraño motivo la fecha fin brinca al siguiente día por 59 minutos al soltar.
    // Fix al redimensionar appointment: por algún extraño motivo la fecha fin se queda en el mismo día pero con 00 minutos
    // al redimensionar.
    if (elapsedMinutes === '00:59:00' || elapsedMinutes === '00:00:00') {
      if (this.debug) {
        console.log('>> >> >> date fixed');
      }
      return DateUtil.add(DateUtil.trunc(date, true), DateInterval.MINUTE, -1);
    }
    return date;
  }

  private updateWorkLoadMap(userId: number, activityId: number, localPlannedHours: number, plannedHours: number) {
    if (this.participantWorkloadMap[userId]) {
      let updated = false;
      if (this.data().tagName === 'activities-workload') {
        const pendingCountAndHours = this.participantWorkloadMap[userId].pendingCountAndHours;
        for (const date in pendingCountAndHours) {
          const activityWLM = pendingCountAndHours[date].find((item) => item.activityId === activityId);
          if (activityWLM) {
            activityWLM.pendingPlannedHours = localPlannedHours || plannedHours || 0;
            updated = true;
            break;
          }
        }
      } else {
        const activityWLM = this.participantWorkloadMap[userId].localActivities?.find((item) => item.activityId === activityId);
        if (activityWLM) {
          activityWLM.localPlannedHours = localPlannedHours || plannedHours || 0;
          updated = true;
        }
      }
      if (!updated) {
        console.warn('Activity does not exists. Refresh the page manually.');
      }
    } else {
      console.warn('User does not exists. Refresh the page manually.');
    }
  }
}
