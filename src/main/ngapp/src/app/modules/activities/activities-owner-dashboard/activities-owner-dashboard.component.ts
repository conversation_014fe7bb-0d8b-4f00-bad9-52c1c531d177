import type { i18n } from '@/core/bnext-core.component';
import { GridExportComponent } from '@/core/grid/grid-export/grid-export.component';
import { type GridFilterConfig, getFilters } from '@/core/grid/utils/grid-filtering-util';
import type { GridFilter } from '@/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MaximizeGridComponent } from '@/core/maximize-grid/maximize-grid.component';
import { ToolbarMonthlySelectorComponent } from '@/core/toolbar-monthly-selector/toolbar-monthly-selector.component';
import * as DateUtil from '@/core/utils/date-util';
import { DateInterval } from '@/core/utils/date-util';
import { DatePipe, NgClass } from '@angular/common';
import { type AfterViewInit, Component, HostBinding, type OnDestroy, type OnInit, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  type CellType,
  FilteringExpressionsTree,
  FilteringLogic,
  type IFilteringExpressionsTree,
  IgxAvatarComponent,
  IgxCellTemplateDirective,
  IgxCircularProgressBarComponent,
  IgxColumnComponent,
  IgxExcelStyleHeaderIconDirective,
  IgxGridStateDirective,
  IgxGridToolbarActionsComponent,
  IgxGridToolbarComponent,
  IgxGridToolbarHidingComponent,
  IgxGridToolbarPinningComponent,
  IgxGridToolbarTitleComponent,
  IgxHierarchicalGridComponent,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxPaginatorComponent,
  IgxRippleDirective,
  IgxRowIslandComponent,
  IgxStringFilteringOperand,
  IgxSwitchComponent
} from '@infragistics/igniteui-angular';
import { IgxButtonDirective } from 'igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import { GridComponent } from 'src/app/core/grid/grid.component';
import type { GridCellType } from 'src/app/core/grid/utils/grid-cell-type';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import type { GridInfo } from 'src/app/core/grid/utils/grid-data';
import { GridDataType } from 'src/app/core/grid/utils/grid-data-type';
import { Session } from 'src/app/core/local-storage/session';
import type { DataMap } from 'src/app/core/utils/data-map';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivityStatus } from 'src/app/shared/activities/core/activities-core.enums';
import { ActivitiesDashboardBaseComponent } from '../activities-dashboard-base/activities-dashboard-base.component';
import type { CacheDashboard, IActivityProgressTreeDto } from '../activities-dashboard-utils/activities-dashboard-utils.interfaces';
import { ActivitiesTreeScoreComponent } from '../activities-tree-score/activities-tree-score.component';
import type { ActivityGridColumn } from './../activities-dashboard-utils/activities-dashboard-utils.interfaces';
import type { ActivitiesUserRow } from './activities-owner-dashboard.interfaces';

@Component({
  selector: 'app-activities-owner-dashboard',
  templateUrl: './activities-owner-dashboard.component.html',
  styleUrls: ['./activities-owner-dashboard.component.scss', './../../../core/grid/grid.component.scss'],
  imports: [
    ActivitiesTreeScoreComponent,
    BnextTranslatePipe,
    DatePipe,
    FormsModule,
    GridExportComponent,
    IgxAvatarComponent,
    IgxButtonDirective,
    IgxCellTemplateDirective,
    IgxCircularProgressBarComponent,
    IgxColumnComponent,
    IgxExcelStyleHeaderIconDirective,
    IgxGridStateDirective,
    IgxGridToolbarActionsComponent,
    IgxGridToolbarComponent,
    IgxGridToolbarHidingComponent,
    IgxGridToolbarPinningComponent,
    IgxGridToolbarTitleComponent,
    IgxHierarchicalGridComponent,
    IgxIconButtonDirective,
    IgxIconComponent,
    IgxLinearProgressBarComponent,
    IgxPaginatorComponent,
    IgxRippleDirective,
    IgxRowIslandComponent,
    IgxSwitchComponent,
    MaximizeGridComponent,
    NgClass,
    ToolbarMonthlySelectorComponent
  ]
})
export class ActivitiesOwnerDashboardComponent<DataRowType = DataMap<any>> extends ActivitiesDashboardBaseComponent implements OnInit, AfterViewInit, i18n, OnDestroy {
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities',
    componentName: 'activities-owner-dashboard'
  };

  override get componentPath(): string {
    return 'modules.activities';
  }

  override get tagName(): string {
    return 'activities-owner-dashboard';
  }

  LangConfig = ActivitiesOwnerDashboardComponent.LANG_CONFIG;

  readonly treeScoreDialog = viewChild('treeScoreDialog', { read: ActivitiesTreeScoreComponent });

  filteringExpressionsTree: IFilteringExpressionsTree;
  showAllDepartments = false;
  emptyFilteredGridMessage = '';
  emptyGridMessage = '';

  columns: GridColumn[] = [
    {
      header: 'Departamento',
      field: 'bussinessDepartmentName',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '235px',
      type: GridDataType.TEXT,
      hasSummary: true,
      pinned: false
    },
    {
      header: 'Responsable',
      field: 'ownerName',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '160px',
      type: GridDataType.TEXT,
      hasSummary: true,
      pinned: true
    },
    {
      header: 'Horas estimadas planeadas',
      field: 'sumPlannedHours',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas estimadas fuera de plan',
      field: 'sumUnplannedHours',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas estimadas canceladas',
      field: 'sumPlannedHoursCancelled',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas estimadas totales',
      field: 'sumEstimatedHours',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas consumidas planeadas',
      field: 'sumActualHoursPlanned',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas consumidas fuera de plan',
      field: 'sumActualHoursUnplanned',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas consumidas canceladas',
      field: 'sumActualHoursCancelled',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas consumidas totales',
      field: 'sumActualHours',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '200px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Avance',
      field: 'progress',
      filterable: true,
      hidden: true,
      resizable: true,
      sortable: true,

      width: '165px',
      type: GridDataType.PERCENTAGE,
      formatter: (value) => this.formatterPercentage(value),
      hasSummary: false,
      pinned: false
    },
    {
      header: 'Cumplimiento',
      field: 'score',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '165px',
      type: GridDataType.PERCENTAGE,
      formatter: (value) => this.formatterPercentage(value),
      hasSummary: false,
      pinned: false
    },
    {
      header: 'No. planeadas',
      field: 'plannedCount',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '145px',
      type: GridDataType.INTEGER,
      hasSummary: false,
      pinned: false
    },
    {
      header: 'No. fuera de plan',
      field: 'unplannedCount',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '170px',
      type: GridDataType.INTEGER,
      hasSummary: false,
      pinned: false
    },
    {
      header: 'No. canceladas',
      field: 'cancelledCount',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '170px',
      type: GridDataType.INTEGER,
      hasSummary: false,
      pinned: false
    },
    {
      header: 'No. realizadas',
      field: 'completedCount',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '145px',
      type: GridDataType.INTEGER,
      hasSummary: false,
      pinned: false
    }
  ];

  recordColumns: GridColumn[] = [
    {
      header: 'Clave',
      field: 'activityCode',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,
      type: GridDataType.TEXT
    },
    {
      header: 'Descripción',
      field: 'activityDescription',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,
      type: GridDataType.TEXT
    },
    {
      header: 'Avance',
      field: 'progress',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,
      width: '125px',
      type: GridDataType.PERCENTAGE
    },
    {
      header: 'Fecha de implementación',
      field: 'commitmentDateInterval',
      filterable: true,
      hidden: false,
      width: '201px',
      resizable: true,
      sortable: true,
      type: GridDataType.TEXT
    },
    {
      header: 'Fecha de verificación',
      field: 'plannedVerificationDate',
      filterable: true,
      width: '178px',
      resizable: true,
      sortable: true,
      type: GridDataType.DATE,
      formatter: (value) => DateUtil.format(value, this.dateFormat),
      hasSummary: true,
      pinned: false
    },
    {
      header: 'Estado',
      width: '134px',
      field: 'statusName',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      type: GridDataType.TEXT
    },
    {
      header: 'Categoría',
      width: '134px',
      field: 'categoryName',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      type: GridDataType.TEXT
    },
    {
      header: 'Resolución',
      width: '134px',
      field: 'activityResolution',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      type: GridDataType.TEXT
    },
    {
      header: 'Es planeada',
      field: 'isPlanned',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '120px',
      type: GridDataType.BOOLEAN,
      formatter: (value) => this.formatBoolean(value)
    },
    {
      header: 'Horas estimadas',
      field: 'plannedHours',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '100px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Horas consumidas',
      field: 'actualHours',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '140px',
      type: GridDataType.DOUBLE,
      pipeArgs: {
        digitsInfo: '1.0-2'
      },
      formatter: (value) => this.formatNumber(value)
    },
    {
      header: 'Realizada',
      field: 'completed',
      filterable: true,
      hidden: false,
      resizable: true,
      sortable: true,

      width: '120px',
      type: GridDataType.BOOLEAN,
      formatter: (value) => this.formatBoolean(value)
    }
  ];

  public columnMap: DataMap<ActivityGridColumn> = {};

  @HostBinding('class.maximized')
  public maximized = false;

  override langReady(): void {
    for (const e of this.columns) {
      e.header = this.tag(e.field);
    }
    for (const e of this.recordColumns) {
      e.header = this.tag(e.field);
    }
    this.emptyFilteredGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_filtered_grid_message');
    this.emptyGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_grid_message');
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.subs.push(
      this.translateService.getFrom(GridComponent.LANG_CONFIG, 'base').subscribe((grid) => {
        this.gridI18n = grid;
      })
    );
  }

  override ngAfterViewInit() {
    this.filterByDepartment();
    super.ngAfterViewInit();
    this.busy = false;
    this.viewInit = true;
    this.detectChanges();
  }

  protected normalizeData(row: ActivitiesUserRow): void {
    if (row.ownerRecords && row.ownerRecords.length > 0) {
      for (const data of row.ownerRecords) {
        data.commitmentDate = DateUtil.safe(data.commitmentDate);
        data.commitmentDateInterval = this.getCommitmentDateString(data);
        data.statusName = this.statusNames[data.activityStatus]?.label || '-';
      }
    } else {
      row.ownerRecords = [];
    }
  }

  private filterByDepartment(): void {
    const bussinessDepartmentName = Session.getBusinessUnitDepartmentName();
    if (bussinessDepartmentName !== null && typeof bussinessDepartmentName !== 'undefined' && bussinessDepartmentName !== '' && !this.showAllDepartments) {
      const filterExpression = {
        condition: IgxStringFilteringOperand.instance().condition('equals'),
        fieldName: 'bussinessDepartmentName',
        ignoreCase: true,
        searchVal: bussinessDepartmentName
      };
      this.filteringExpressionsTree = new FilteringExpressionsTree(FilteringLogic.Or, 'bussinessDepartmentName');
      this.filteringExpressionsTree.filteringOperands.push(filterExpression);
    }
  }

  private buildCacheGrids(): CacheDashboard {
    return {
      implementerNames: {},
      implementerOwners: {},
      historyStatuses: {},
      hasHistoryEvent: {},
      historyEvents: {},
      commitmentDates: {}
    };
  }

  public refreshData(): void {
    if (this.showAllDepartments) {
      this.grid().clearFilter();
      this.filteringExpressionsTree.filteringOperands = [];
    } else {
      this.filterByDepartment();
    }
    if (!this.rangeSelector()) {
      return;
    }
    if (!this.loading) {
      this.grid().collapseAll();
    }
    this.$cancelableSearch.next(true);
    this.loading = true;
    this.api
      .post({ url: this.buildRefreshUrl(), postBody: this.getFilter(), options: null, handleFailure: true, cancelableReq: this.$cancelableSearch })
      .pipe(takeUntil(this.$cancelableSearch))
      .subscribe({
        next: (gridInfo: GridInfo<IActivityProgressTreeDto>) => {
          this.cache = this.buildCacheGrids();
          this.localdata = [];
          this.reset();
          this.localdata = gridInfo.data || [];
          this.loading = false;
          this.detectChanges();
        },
        error: (error) => {
          console.error(`Failed to load ${this.tagName}.`, error);
          this.cache = this.buildCacheGrids();
          this.localdata = [];
          this.reset();
          this.loading = false;
          this.detectChanges();
        }
      });
  }

  private getFilter(): GridFilter {
    const grid = this.grid();
    const filterBuild: GridFilterConfig<DataRowType> = {
      gridId: 'owner-grid',
      componentName: this.translateService.componentName,
      index: 0,
      dynamicSearchEnabled: false,
      perPage: 0,
      filteringExpressionsTree: this.filteringExpressionsTree,
      sortingExpressions: grid ? grid.sortingExpressions : null,
      groupingExpressions: null,
      allowFiltering: true,
      columnMap: this.columnMap,
      datePipe: this.datePipe,
      dateFormat: this.dateFormat,
      escapeUnderscore: true
    };
    return getFilters(filterBuild);
  }

  protected buildRefreshUrl(): string {
    const rangeSelector = this.rangeSelector();
    const start = this.formatRestDate(rangeSelector?.range?.start);
    const end = this.formatRestDate(rangeSelector?.range?.end);
    return `${this.urlModule}/owner-dashboard/${start}/${end}`;
  }

  openDetail(cell: CellType): void {
    this.menuService.navigate(`menu/activities/${cell.row.data.activityId}`, Module.ACTIVITY);
  }

  isRecordNotApplyVerified(cell: CellType): boolean {
    return cell.row.data.activityStatus === ActivityStatus.NOT_APPLY_VERIFIED;
  }

  isRecordApply(cell: CellType): boolean {
    return !this.isRecordNotApplyVerified(cell);
  }

  hasOnlyCancelledActivities(cell: CellType): boolean {
    return cell.row.data.plannedCount === 0;
  }

  hasPlannedActivities(cell: CellType): boolean {
    return !this.hasOnlyCancelledActivities(cell);
  }

  onToggleMaximize(event) {
    this.maximized = event;
    if (this.maximized) {
      this.menuService.hideFabMenu();
    } else {
      this.menuService.showFabMenu();
    }
    this.cdr.detectChanges();
  }

  openTreeScoreDialog(cell: GridCellType<ActivitiesUserRow>): void {
    this.treeScoreDialog().openImplementer({
      implementerId: cell.row.data.implementerId,
      start: DateUtil.safe(this.rangeSelector().range.start),
      end: DateUtil.safe(this.rangeSelector().range.end)
    });
  }

  changeDateInterval(cell: GridCellType<ActivitiesUserRow>): void {
    this.dialogService.confirm(this.translate.instantFrom(ActivitiesOwnerDashboardComponent.LANG_CONFIG, 'confirmChangeDateRange')).then(() => {
      const commitmentDate = DateUtil.safe(cell.row.data.commitmentDate);
      if (this.isRowWithCommitmentInverval(cell.row.data.anticipationAttendDays)) {
        const anticipationPeriod = DateUtil.add(commitmentDate, DateInterval.DAY, cell.row.data.anticipationAttendDays);
        this.rangeSelector().defineRangeData({ start: DateUtil.trunc(commitmentDate), end: anticipationPeriod });
        this.refreshData();
      } else {
        this.rangeSelector().defineRangeData({ start: DateUtil.trunc(commitmentDate), end: DateUtil.trunc(commitmentDate) });
        this.refreshData();
      }
    });
  }
}
