import type { i18n } from '@/core/bnext-core.component';
import type { CommentEntity, SaveCommentEvt } from '@/core/comment-multi-save/comment-multi-save-interfaces';
import { CommentMultiSaveComponent } from '@/core/comment-multi-save/comment-multi-save.component';
import { type DocumentEntity, DocumentMultiSelectComponent, type SaveDocumentEvt } from '@/core/document-multi-select/document-multi-select.component';
import type { IFileData, IFileUploadAdded } from '@/core/drop-file/drop-file.interfaces';
import { DropdownButtonComponent } from '@/core/dropdown-button/dropdown-button.component';
import type { DropdownButtonItem } from '@/core/dropdown-button/dropdown-button.interfaces';
import { DropdownMenuComponent } from '@/core/dropdown-menu/dropdown-menu.component';
import type { DropdownMenuActivable, DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import { FieldHandlerComponent } from '@/core/dynamic-field/field-handler/field-handler.component';
import type { DynamicFieldsDTO } from '@/core/dynamic-field/field-handler/field-handler.interfaces';
import type { FabButtonMenuItem } from '@/core/fab-button-menu/fab-button-menu.interface';
import { FieldDisplayComponent, type ToggleFieldDisplay } from '@/core/field-display/field-display.component';
import { FieldDisplayType } from '@/core/field-display/field-display.enums';
import type { FieldDisplay, FieldDisplayConfig, FieldDisplayEvt, OptionalEditionField } from '@/core/field-display/field-display.interfaces';
import { GridSelectInputType, type RowSelection } from '@/core/grid-base-select/grid-base-select.interfaces';
import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import { BnextGridWindowPosition } from '@/core/grid/utils/grid.enums';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { ImagePreviewerComponent } from '@/core/image-previewer/image-previewer.component';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { LocalStorageSession } from '@/core/local-storage/local-storage-session';
import { Session } from '@/core/local-storage/session';
import { MultiFileUploadComponent } from '@/core/multi-file-upload/multi-file-upload.component';
import type { PeriodicityComponent } from '@/core/periodicity/periodicity.component';
import { PeriodicityType } from '@/core/periodicity/utils/periodicity-type';
import type { DialogResult } from '@/core/services/custom-dialog.interfaces';
import type { ModifiedFieldResponseDTO } from '@/core/services/field-display-edition.interfaces';
import type { DataMap } from '@/core/utils/data-map';
import * as DateUtil from '@/core/utils/date-util';
import { DateInterval } from '@/core/utils/date-util';
import * as DomUtil from '@/core/utils/dom-util';
import type { UserRefEntity } from '@/core/utils/entity';
import { EnumUtil } from '@/core/utils/enum-util';
import { CommonAction } from '@/core/utils/enums';
import { ErrorHandling } from '@/core/utils/error-handling';
import * as NumberUtil from '@/core/utils/number-util';

import { isObject } from '@/core/utils/object';
import { StringFormat } from '@/core/utils/string-format';
import { encodeUrlParameter } from '@/core/utils/string-util';
import type { ITextHasValue, ITextValue, TextHasUserValue, TextHasValue, TextLongValue } from '@/core/utils/text-has-value';
import { ActivitiesAddManyComponent } from '@/modules/activities/activities-add-many/activities-add-many.component';
import { ActivityAddManyConfiguration } from '@/modules/activities/activities-add-many/activities-add-many.config';
import type { ReportedActivities } from '@/modules/activities/activities-add-many/activities-add-many.interfaces';
import type { ActivityAddPlanSaveDto, IActivitiesAddPlanRow } from '@/modules/activities/activities-add-plan/activities-add-plan.interfaces';
import { ActivitiesAddToModuleComponent } from '@/modules/activities/activities-add-to-module/activities-add-to-module.component';
import { ActivitiesDetailConstant } from '@/modules/activities/activities-detail/activities-detail.constant';
import { ActivitiesHistoryComponent } from '@/modules/activities/activities-history/activities-history.component';
import { ActivitiesRelationshipComponent } from '@/modules/activities/activities-relationship/activities-relationship.component';
import { ActivityService } from '@/modules/activities/activities-service/activities.service';
import { ActivitiesComponent } from '@/modules/activities/activities/activities.component';
import { loadActivityNavigator, saveActivityNavigatorHistory } from '@/modules/activities/consolidated-activities/consolidated-activities.util';
import { ACTIVITY_ACTIONS } from '@/modules/config.pendings.module';
import { Module } from '@/modules/menu/menu-definition/menu-definition.enum';
import { ActivitiesAssignComponent, type ActivitiesAssignDto } from '@/modules/pendings/activities/activities-assign/activities-assign.component';
import { ActivitiesMoveComponent } from '@/modules/pendings/activities/activities-move/activities-move.component';
import type { ActivityHistoryEntity } from '@/modules/pendings/activities/activities-pending/activites-history.interfaces';
import { ImplementAction } from '@/modules/pendings/activities/activities-pending/activities-pending.enums';
import { type ActivityFormEntity, ActivityHistoryType, type ActivityPendingDto } from '@/modules/pendings/activities/activities-pending/activities-pending.interfaces';
import { ActivitiesWidgetComponent } from '@/modules/pendings/activities/activities-widget/activities-widget.component';
import type { ActivityDropdownMenuItem } from '@/modules/pendings/activities/activities-widget/activities-widget.interfaces';
import { ActivityInfoComponent } from '@/modules/pendings/activities/activity-info/activity-info.component';
import { ActivityInfoConstant } from '@/modules/pendings/activities/activity-info/activity-info.constant';
import { SwapActivitiesComponent } from '@/modules/pendings/activities/swap-activities/swap-activities.component';
import type { ToVerifyServiceDTO } from '@/modules/pendings/activities/to-verify/to-verify.interfaces';
import { ToVerifyService } from '@/modules/pendings/activities/to-verify/to-verify.service';
import { PlannerStatus } from '@/modules/planner/planner-add/planner-add.interfaces';
import { VerifierAuthor } from '@/modules/settings/activity-settings/utils/verifier-authors';
import type { TimesheetDataSourceDto } from '@/modules/timesheet/timesheet-add/timesheet-datasource';
import { TimesheetListComponent } from '@/modules/timesheet/timesheet-list/timesheet-list.component';
import { PlannedType } from '@/modules/timesheet/timesheet-widget/timesheet-widget.enums';
import type { StopwatchEndTimeData, TimesheetDto } from '@/modules/timesheet/timesheet-widget/timesheet-widget.interfaces';
import { TimesheetService } from '@/modules/timework/services/timesheet.service';
import type { DataSourceResult } from '@/shared/activities/activities-add-many.interfaces';
import type { DisabledFields, EditionRules } from '@/shared/activities/activities-rules.interfaces';
import { ActivityRole, ActivityStatus, CommitmentTask, FillType, HistoryViewMode } from '@/shared/activities/core/activities-core.enums';
import type {
  ActivitiesOwnerAvatarTooltip,
  ActivityDetails,
  BusinessUnitDepartmentEntity,
  FieldDisplayField,
  HiddenRules,
  ParticipantDto,
  PlannerStartDatesEntity
} from '@/shared/activities/core/activities-core.interfaces';
import { ActivityCoreUtil } from '@/shared/activities/core/activities-core.utils';
import type { DisplayConfig } from '@/shared/activities/core/activities-field-display.interfaces';
import type {
  ActivityDataSourceDto,
  ActivityLinkedDataSource,
  ActivityLinkedSource,
  ActivityLoadDto,
  AttendResult
} from '@/shared/activities/core/utils/activity-base.interfaces';
import { deleteServicesByModule, getActiveImplementationsController, getNormalizedModule } from '@/shared/activities/core/utils/activity-util';
import { ProfileServices } from '@/shared/roles/profiles/utils/profile-services.enums';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
  type AfterViewInit,
  Component,
  type ElementRef,
  Input,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  type QueryList,
  type SimpleChanges,
  ViewChild,
  ViewChildren,
  inject,
  input,
  output,
  viewChild
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import {
  GlobalPositionStrategy,
  HorizontalAlignment,
  type ISlideEventArgs,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxCarouselComponent,
  IgxCircularProgressBarComponent,
  type IgxDatePickerComponent,
  IgxDialogComponent,
  IgxDividerDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxRippleDirective,
  IgxTabContentComponent,
  IgxTabHeaderComponent,
  IgxTabHeaderLabelDirective,
  IgxTabItemComponent,
  IgxTabsComponent,
  type OverlaySettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { type Observable, Subject, type Subscription } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { ActivitiesDetailAction, ActivitiesDetailAddPanelType, ActivitiesDetailUpdateAction } from './activities-detail.enums';

@Component({
  selector: 'app-activities-detail',
  templateUrl: './activities-detail.component.html',
  styleUrls: ['./../activities-add/activities-add.component.scss', './activities-detail.component.scss'],
  animations: [
    trigger('imageZoneAnim', [
      state(
        'open',
        style({
          height: '25rem',
          opacity: 1
        })
      ),
      state(
        'closed',
        style({
          height: '0',
          opacity: 0
        })
      ),
      transition('* => *', [animate('0.5s')])
    ])
  ],
  imports: [
    IgxLinearProgressBarComponent,
    IgxIconButtonDirective,
    ActivityInfoComponent,
    FormsModule,
    ReactiveFormsModule,
    IgxRippleDirective,
    IgxButtonDirective,
    IgxIconComponent,
    DropdownMenuComponent,
    FieldDisplayComponent,
    IgxTabsComponent,
    IgxTabItemComponent,
    IgxTabHeaderComponent,
    IgxTabHeaderLabelDirective,
    IgxTabContentComponent,
    NgClass,
    IgxDividerDirective,
    FieldHandlerComponent,
    IgxBadgeComponent,
    GridMultiSelectComponent,
    ActivitiesWidgetComponent,
    DropdownButtonComponent,
    SwapActivitiesComponent,
    ActivitiesRelationshipComponent,
    DocumentMultiSelectComponent,
    MultiFileUploadComponent,
    ActivitiesHistoryComponent,
    CommentMultiSaveComponent,
    IgxCircularProgressBarComponent,
    TimesheetListComponent,
    NgTemplateOutlet,
    IgxDialogComponent,
    ActivitiesAddManyComponent,
    ActivitiesAddToModuleComponent,
    ActivitiesMoveComponent,
    ImagePreviewerComponent,
    ActivitiesAssignComponent,
    BnextTranslatePipe
  ]
})
export class ActivitiesDetailComponent extends ActivitiesComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges, i18n {
  titleService = inject(Title);
  toVerifyService = inject(ToVerifyService);

  timesheetService = inject(TimesheetService);
  activityService = inject(ActivityService);

  public static LANG_CONFIG: BnextComponentPath = ActivitiesDetailConstant.LANG_CONFIG;

  override get componentPath(): string {
    return ActivitiesDetailComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    return ActivitiesDetailComponent.LANG_CONFIG.componentName;
  }

  override get customTagName(): string {
    return ActivitiesDetailComponent.LANG_CONFIG.componentName;
  }

  initialized = false;
  openAddDialog = false;
  data: ActivityLoadDto;
  dataSource: ActivityDataSourceDto = null;
  EnumGridSelectInputType = GridSelectInputType;
  partitionItems: DropdownButtonItem[] = [
    { value: ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION, text: 'Implementación', iconName: 'add', hidden: true },
    { value: ActivitiesDetailAddPanelType.ADD_VERIFICATION, text: 'Verificación', iconName: 'add', hidden: true },
    { value: ActivitiesDetailAddPanelType.AUTO_ASSIGN, text: 'Autoasignación', iconName: 'assignment_ind', hidden: true },
    { value: ActivitiesDetailAddPanelType.ASSIGN, text: 'Asignación', iconName: 'assignment_late', hidden: true }
  ];
  selectedPartition: string;
  showSidePaneSmall = false;
  historyViewMode = HistoryViewMode.LIST;
  public enableRelationsship = false;
  public timesheetMaximized = false;
  public isIphone = this.isIPhoneDevice();
  public defaultDeleteServices = [];

  private _assignSave = new Subject<ActivitiesAssignDto>();
  private _cancelAddPlanSave = new Subject<any>();
  private _isImplementer = false;
  private _hasChilds = false;
  private _implementer: DataMap<number> = null;
  private _preImplementer: DataMap<number> = null;
  private _isActivitiesPanelOpen = false;
  public openedFromSubtask = false;
  private _currentActivitiesPanel: ActivitiesDetailAddPanelType = null;
  private _addProgramSubtaskSettings: ActivityAddManyConfiguration = new ActivityAddManyConfiguration(
    null, // defaultTypeId
    null, // _defaultTypeController
    null, // defaultGroupName
    null, // _defaultBusinessUnitDepartment
    null, // _customImplementationLabel
    null, //_defaultPlannerStartDatesEntity
    null, // defaultImplementerIds
    true, // isSaveButtonEnabled
    false, // _isParticipantsAvailable
    false, // isGroupsAvailable
    null, // parentPlannerId
    null, // module
    false, // isVerifierRemoved
    false, // isRowModuleReplacementEnabled
    1, // defaultRowModuleReplacementDeep
    true, // isCancelButtonEnabled
    false, // isButtonSectionHidden
    false, // isTabContainer
    null, // linkedSource
    null, // linkedDataSource
    null, // recurrenceId
    null, // parentActivityId
    null, // parentActivityImplementationId
    null, // parentActivityVerificationId
    null, // parentTreeActivityId
    true, // isPopEnabled
    false // isSetupAfterViewInit
  );
  private _addSubtaskSettings: ActivityAddManyConfiguration = new ActivityAddManyConfiguration(
    null, // defaultTypeId
    null, // defaultTypeController
    null, // defaultGroupName
    null, // _defaultBusinessUnitDepartment
    null, // _customImplementationLabel
    null, //_defaultPlannerStartDatesEntity
    null, // defaultImplementerIds
    null, // isSaveButtonEnabled
    null, // _isParticipantsAvailable
    true, // isGroupsAvailable
    null, // parentPlannerId
    null, // module
    false, // isVerifierRemoved
    null, // isRowModuleReplacementEnabled
    null, // defaultRowModuleReplacementDeep
    true, // isCancelButtonEnabled
    false, // isButtonSectionHidden
    false, // isTabContainer
    null, // linkedSource
    null, // linkedDataSource
    null, // recurrenceId
    null, // parentActivityId
    null, // parentActivityImplementationId
    null, // parentActivityVerificationId
    null, // parentTreeActivityId
    true, // isPopEnabled
    false // isSetupAfterViewInit
  );

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public declare id;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public commitmentSubtaskLabel: string = null;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public commitmentSubtaskField: string = null;
  public readonly navigateBack = input(true);
  public readonly showBackButton = input(false);
  public readonly initializedById = input(false);
  public readonly reminderHidden = input(false);
  readonly businessUnitHidden = input(false);
  readonly businessUnitEditable = input(false);
  readonly businessUnitDepartmentEditable = input(false);
  public readonly subtaskAvatarAvailable = input(true);
  public readonly addChildOverride = input<boolean>(null);
  public readonly isMoreChildsAvailable = input<boolean>(undefined); // <-- Hay validaciones contra `undefined`, no inicializar!
  public readonly isRowModuleReplacementEnabled = input(false);
  public readonly customImplementationLabel = input<string>(null);

  public readonly defaultRowModuleReplacementDeep = input(1);

  public readonly redirectLeftButton = input('DEFAULT');

  public readonly redirectRigthButton = input('DEFAULT');

  readonly returned = output<ActivityDataSourceDto>();

  readonly description = viewChild<ElementRef>('description');
  readonly activityInfo = viewChild('activityInfo', { read: ActivityInfoComponent });
  readonly dropdownmenu = viewChild<DropdownMenuComponent>('dropdownmenu');
  readonly _implementationDisplay = viewChild<FieldDisplayComponent>('implementationDisplay');
  readonly _implementationPeriodicityDisplay = viewChild<FieldDisplayComponent>('implementationPeriodicityDisplay');
  readonly _verificationDisplay = viewChild<FieldDisplayComponent>('verificationDisplay');
  readonly _verificationPeriodicityDisplay = viewChild<FieldDisplayComponent>('verificationPeriodicityDisplay');
  readonly subTasksMove = viewChild<ActivitiesMoveComponent>('subTasksMove');

  readonly swap = viewChild<SwapActivitiesComponent>('swap');
  readonly imageEvidenceCarousel = viewChild(IgxCarouselComponent);
  readonly dialogActivitiesMany = viewChild<IgxDialogComponent>('dialogActivitiesMany');

  // TODO: Skipped for migration because:
  //  There are references to this query that cannot be migrated automatically.
  @ViewChildren('activitiesPanel')
  public activitiesPanel: QueryList<ActivitiesAddManyComponent>;

  // TODO: Skipped for migration because:
  //  This query is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @ViewChild('timesheet')
  public timesheet: TimesheetListComponent;

  readonly filesTimesheetActivity = viewChild<MultiFileUploadComponent>('filesTimesheetActivity');
  readonly tabsWrapper = viewChild<IgxTabsComponent>('tabsWrapper');
  readonly previewerImages = viewChild<ImagePreviewerComponent>('previewerImages');
  readonly containerDetail = viewChild<ElementRef>('containerDetail');
  readonly containerForm = viewChild<ElementRef>('containerForm');
  readonly plannerTaskField = viewChild<FieldDisplayComponent>('plannerTaskField');
  readonly assignComponent = viewChild<ActivitiesAssignComponent>('assignActivity');
  readonly activityAddToModule = viewChild<ActivitiesAddToModuleComponent>('activityAddToModule');
  readonly mainActivity = viewChild<FieldDisplayComponent>('mainActivity');

  override watchResizeEvents = true;
  containerDetailNewSize = null;
  moduleTitleKey = 'activity';
  isInnerOverlayShown = false;
  editModeAvailable = false;
  editMode = true;
  openPreviewerImages = false;
  enableSubtaskMove = false;
  implementersHasChanged = false;
  implementersCache: number[] = [];
  preImplementersCache = [];
  recurrenceId: number = null;
  parentActivityImplementationId: number = null;
  parentActivityId: number = null;
  parentTreeActivityId: number = null;
  parentPlannerId: number = null;
  linkedSource: ActivityLinkedSource = null;
  businessUnitDepartment: BusinessUnitDepartmentEntity = null;
  plannerStartDatesEntity: PlannerStartDatesEntity = {
    implementationStartDate: null,
    implementation: null
  };
  showImplConfirm = false;
  confirmLabel = '';
  configFieldDisplay: DisplayConfig = {
    activityOrder: FieldDisplayComponent.getDefaultConfigInstanceNumber('activityOrder', {
      min: 0,
      max: this.maxOrderSize
    }),
    businessUnitDepartmentId: FieldDisplayComponent.getDefaultConfigInstance('businessUnitDepartmentId', FieldDisplayType.SELECT),
    fillType: FieldDisplayComponent.getDefaultConfigInstance('fillType', FieldDisplayType.SELECT),
    daysToVerify: FieldDisplayComponent.getDefaultConfigInstance('daysToVerify', FieldDisplayType.NUMBER),
    cancellationReason: FieldDisplayComponent.getDefaultConfigInstance('cancellationReason', FieldDisplayType.TEXT),
    description: FieldDisplayComponent.getDefaultConfigInstance('description', FieldDisplayType.TEXTAREA),
    implementation: this.defineImplementationConfig(),
    implementationPeriodicity: FieldDisplayComponent.getDefaultConfigInstance('implementationPeriodicity', FieldDisplayType.PERIODICITY),
    implementer: FieldDisplayComponent.getDefaultConfigInstance('implementer', FieldDisplayType.SELECT),
    isPlanned: FieldDisplayComponent.getDefaultConfigInstance('isPlanned', FieldDisplayType.SWITCH),
    followUpImplementationDelay: FieldDisplayComponent.getDefaultConfigInstance('followUpImplementationDelay', FieldDisplayType.SWITCH),
    mustUpdateImplementationAtReturn: FieldDisplayComponent.getDefaultConfigInstance('mustUpdateImplementationAtReturn', FieldDisplayType.SWITCH),
    plannedHours: FieldDisplayComponent.getDefaultConfigInstance('plannedHours', FieldDisplayType.DOUBLE),
    plannerTask: FieldDisplayComponent.getDefaultConfigInstance('plannerTask', FieldDisplayType.PLANNER_TASK),
    systemLinks: FieldDisplayComponent.getDefaultConfigInstance('systemLinks', FieldDisplayType.SYSTEM_LINK),
    objectiveId: FieldDisplayComponent.getDefaultConfigInstance('objectiveId', FieldDisplayType.SELECT, false),
    priority: FieldDisplayComponent.getDefaultConfigInstance('priority', FieldDisplayType.SELECT, false),
    reminder: this.defineReminderConfig(),
    categoryId: FieldDisplayComponent.getDefaultConfigInstance('categoryId', FieldDisplayType.SELECT),
    source: FieldDisplayComponent.getDefaultConfigInstance('source', FieldDisplayType.SELECT, false),
    typeId: FieldDisplayComponent.getDefaultConfigInstance('typeId', FieldDisplayType.SELECT),
    verification: FieldDisplayComponent.getDefaultConfigInstance('verification', FieldDisplayType.DATE),
    verificationPeriodicity: FieldDisplayComponent.getDefaultConfigInstance('verificationPeriodicity', FieldDisplayType.PERIODICITY),
    verifier: FieldDisplayComponent.getDefaultConfigInstance('verifier', FieldDisplayType.SELECT),
    plannedImplementationWeek: FieldDisplayComponent.getDefaultConfigInstance('plannedImplementationWeek', FieldDisplayType.NUMBER),
    plannedVerificationWeek: FieldDisplayComponent.getDefaultConfigInstance('plannedVerificationWeek', FieldDisplayType.NUMBER),
    actualHours: FieldDisplayComponent.getDefaultConfigInstance('actualHours', FieldDisplayType.DOUBLE),
    creatorUserName: FieldDisplayComponent.getDefaultConfigInstance('creatorUserName', FieldDisplayType.TEXT),
    resolutionId: FieldDisplayComponent.getDefaultConfigInstance('resolutionId', FieldDisplayType.SELECT),
    commitmentTask: FieldDisplayComponent.getDefaultConfigInstance('commitmentTask', FieldDisplayType.SELECT),
    createdDate: FieldDisplayComponent.getDefaultConfigInstance('createdDate', FieldDisplayType.DATE_TIME, false),
    preImplementer: FieldDisplayComponent.getDefaultConfigInstance('preImplementer', FieldDisplayType.SELECT),
    verificationPlannedWeek: FieldDisplayComponent.getDefaultConfigInstance('verificationPlannedWeek', FieldDisplayType.TEXT, false)
  };
  // Configuración de renderización de campos de la actividad main
  mainFieldDisplayCreatorUserName = FieldDisplayComponent.getDefaultConfigInstance('creatorUserName', FieldDisplayType.TEXT, false);
  private _mainFieldDisplayCode = FieldDisplayComponent.getDefaultConfigInstance('code', FieldDisplayType.TEXT, false, true);
  // Opciones para actualizar
  updateOptions: DropdownButtonItem[] = [
    { value: ActivitiesDetailUpdateAction.UPDATE_COINCIDENCES, text: 'Si, editar las que coincidan', iconName: 'sync_alt' },
    { value: ActivitiesDetailUpdateAction.UPDATE_ALL, text: 'Si, todas', iconName: 'edit_note' },
    { value: ActivitiesDetailUpdateAction.UPDATE_THIS, text: 'No, solo esta', iconName: 'edit' }
  ];

  updateOptionsPlanner: DropdownButtonItem[] = [{ value: ActivitiesDetailUpdateAction.UPDATE_ALL, text: 'Actualizar', iconName: 'edit_note' }];

  updateOptionsPlannerTask: DropdownButtonItem[] = [
    { value: ActivitiesDetailUpdateAction.UPDATE_ALL, text: 'Si, todas', iconName: 'edit_note' },
    { value: ActivitiesDetailUpdateAction.UPDATE_THIS, text: 'No, solo esta', iconName: 'edit' }
  ];

  defaultMenuOptionsAvailable: string[] = [CommonAction.ADD_DOCUMENT, CommonAction.ADD_COMMENT, CommonAction.COPY_LINK];
  menuActivityNavigatorOptions: DropdownMenuItem[] = [];
  menuOptionsAvailable: string[] = [...this.defaultMenuOptionsAvailable];
  menuOptions: DropdownMenuItem[] = [
    {
      text: 'Agregar participante',
      value: ActivitiesDetailAction.ADD_PARTICIPANT,
      iconName: 'person_add'
    },
    {
      text: 'Agregar responsable',
      value: ActivitiesDetailAction.ADD_RESPONSIBLE,
      iconName: 'person_add'
    },
    {
      text: 'Traer subtarea',
      value: ActivitiesDetailAction.ADD_RELATION,
      iconName: 'content_cut'
    },
    {
      iconName: 'delete',
      text: 'Cancelar serie',
      value: CommonAction.CANCEL_RECURRENCE
    },
    {
      text: 'Registrar timesheet',
      value: CommonAction.CAPTURE_TIME,
      iconName: 'schedule'
    },
    {
      text: 'Compartir',
      value: CommonAction.COPY_LINK,
      iconName: 'share'
    },
    ACTIVITY_ACTIONS[ImplementAction.AUTO_ASSIGN],
    ACTIVITY_ACTIONS[ImplementAction.ASSIGN]
  ];

  private pendingRecordId = 0;

  routeSubscription: Subscription;

  superInitSubscription: Subscription;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo `fabButtons`
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [];
  fabShowBack = true;
  // Dynamic section
  showDynamicFieldLoad = true;

  slides = [];
  documentCollapsed = false;
  filesCollapsed = false;

  isOpenImageZone = false;
  isDialogImageEvidenceViewOpen = false;
  showTimesheetFilesTab = false;
  private _selectedTab = 0;
  showRelationshipTab = false;
  loadRelationshipContent = false;
  loadTimesheetContent = false;
  _relationshipCount = 0;
  fillTypeEnum = FillType;
  _defaultImplementerIds: number[] = null;

  url = 'timesheet/timesheet-activity/';

  private commitmenTaskCreatedLabel: DataMap = {
    [CommitmentTask.PROGRAM]: 'creationDateLabel',
    [CommitmentTask.IMPLEMENTATION]: 'creationDateLabelImp',
    [CommitmentTask.VERIFICATION]: 'creationDateLabelVer'
  };

  EnumCommitment = CommitmentTask;

  public overlaySettings: OverlaySettings;

  get showMainActivity(): boolean {
    return (
      this.module === Module.ACTIVITY &&
      this.data &&
      (this.data.commitmentTask === CommitmentTask.IMPLEMENTATION || this.data.commitmentTask === CommitmentTask.VERIFICATION)
    );
  }

  get showFieldLabelOnly(): boolean {
    return !this.isMobilDevice();
  }

  get mainFieldDisplayCode(): FieldDisplay {
    if (!this._mainFieldDisplayCode.href && this.data?.recurrenceId) {
      if (this.module === Module.PLANNER) {
        this._mainFieldDisplayCode.href = this.getMainActivityHref(this.data?.linkedSource.id);
        return this._mainFieldDisplayCode;
      }
      this._mainFieldDisplayCode.href = this.getMainActivityHref(this.data?.recurrenceId);
    }
    return this._mainFieldDisplayCode;
  }

  get selectedTab(): number {
    return this._selectedTab;
  }

  get codeTagAvailable(): boolean {
    return !this.isPlannerMainActivity;
  }

  get goToMainActivityAvailable(): boolean {
    return !this.isPlannerMainActivity;
  }

  get isPlannerMainActivity(): boolean {
    return this.data?.fillType === this.fillTypeEnum.PLANNER;
  }

  get isPlannerModule(): boolean {
    return this.module === Module.PLANNER;
  }

  private _showCancelationReasons = true;

  get showCancelationReasons(): boolean {
    return this._showCancelationReasons;
  }

  get activityMainClosed(): boolean {
    return this.data?.status === ActivityStatus.MAIN_CLOSED;
  }

  set showCancelationReasons(value: boolean) {
    this._showCancelationReasons = value;
  }

  public get canDeleteOrEditPreImplementer(): boolean {
    const servicesAssign = [ProfileServices.ACTIVITY_CREATOR.toString(), ProfileServices.ACTIVITY_MANAGER.toString()];
    return this.isAdmin || Session.getServices().some((s) => servicesAssign.includes(s));
  }

  override ngOnInit() {
    super.ngOnInit();
    this.updateLabelsByModule();
    this._assignSave.pipe(throttleTime(500), takeUntil(this.$destroy)).subscribe((dtoAssign) => this._assignActivity(dtoAssign));
    for (const item of this.menuOptions) {
      const s = this.translateService.getFrom(BnextTranslateService.BUTTON_COMMON, `${item.value as string}`).subscribe((lbl) => {
        item.text = lbl || item.text;
      });
      if (s) {
        this.subs.push(s);
      }
    }
    for (const item of this.partitionItems) {
      const s = this.translateService.getFrom(BnextTranslateService.BUTTON_COMMON, `${item.value as string}`).subscribe((lbl) => {
        item.text = lbl || item.text;
      });
      if (s) {
        this.subs.push(s);
      }
    }
    if (this.isMobilDevice()) {
      this.overlaySettings = {
        modal: false,
        closeOnOutsideClick: true,
        positionStrategy: new GlobalPositionStrategy({ horizontalDirection: HorizontalAlignment.Center, verticalDirection: VerticalAlignment.Middle })
      };
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.hasAddImplementationAccess || changes.hasAddVerificationAccess) {
      this.refreshPartitionAccess();
      this.cdr.detectChanges();
    }
    if (changes?.initializedById?.currentValue && this.initializedById()) {
      this.initializeById(this.activityDetails);
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.initSubscription) {
      this.initSubscription.unsubscribe();
    }
    if (this.superInitSubscription) {
      this.superInitSubscription.unsubscribe();
    }
    this.cdr.detach();
  }

  get implementation(): IgxDatePickerComponent {
    return this._implementationDisplay()?.datePicker();
  }

  get verification(): IgxDatePickerComponent {
    return this._verificationDisplay()?.datePicker();
  }

  get implementationPeriodicity(): PeriodicityComponent {
    return this._implementationPeriodicityDisplay()?.periodicityComponent();
  }

  get verificationPeriodicity(): PeriodicityComponent {
    return this._verificationPeriodicityDisplay()?.periodicityComponent();
  }

  get implementer(): DataMap<number> {
    return this._implementer;
  }

  set implementer(value: DataMap<number>) {
    this._implementer = value;
  }

  get activityImplementationId(): number {
    if (this.commitmentTask === 1) {
      return this.id;
    }
    if (this.plannedImplementations?.length === 1) {
      return this.plannedImplementations[0].recordId;
    }
    return null;
  }

  get activityVerificationId(): number {
    if (this.commitmentTask === 2) {
      return this.id;
    }
    if (this.data.plannedVerificationActivityId != null) {
      return this.data.plannedVerificationActivityId;
    }
    if (this.childs?.length > 0) {
      return this.childs[0].parentActivityVerificationId;
    }
    return null;
  }

  get showActivityInfo(): boolean {
    return this.data != null && (this.belongSeries === 0 || this.commitmentTask !== CommitmentTask.PROGRAM);
  }

  override langReady() {
    for (const optional of this.configFieldDisplay.implementation.optionalEditionFields) {
      optional.name = this.tag(`optionalEditionFields.${optional.fieldName}.name`);
      optional.tooltip = this.tag(`optionalEditionFields.${optional.fieldName}.tooltip`);
    }
    for (const optional of this.configFieldDisplay.reminder.optionalEditionFields) {
      optional.name = this.tag(`optionalEditionFields.${optional.fieldName}.name`);
      optional.tooltip = this.tag(`optionalEditionFields.${optional.fieldName}.tooltip`);
    }
    this.configFieldDisplay.implementation.optionalDataFailedMessage = this.tag('optionalEditionFields.failedMessage');
    this.configFieldDisplay.reminder.optionalDataFailedMessage = this.tag('optionalEditionFields.failedMessage');
    this.updateLabelsByModule();
    for (const item of this.updateOptions) {
      item.text = this.tag(item.value.toString());
    }
    for (const item of this.updateOptionsPlannerTask) {
      item.text = this.tag(item.value.toString());
    }
  }

  private defineImplementationConfig(): FieldDisplay {
    const config = FieldDisplayComponent.getDefaultConfigInstance('implementation', FieldDisplayType.DATE);
    config.optionalEditionFields = this.defineKeepAttendDays();
    return config;
  }

  private defineKeepAttendDays(): OptionalEditionField[] {
    return [
      {
        fieldName: 'keepAnticipationAttendDays',
        name: 'keepAnticipationAttendDays',
        tooltip: 'keepAnticipationAttendDays',
        fieldType: FieldDisplayType.SWITCH
      }
    ];
  }

  private defineReminderConfig(): FieldDisplay {
    const config = FieldDisplayComponent.getDefaultConfigInstance('reminder', FieldDisplayType.DATE);
    config.optionalEditionFields = this.defineKeepAttendDays();
    return config;
  }

  protected override afterWindowResized(event: Event) {
    super.afterWindowResized(event);
    this.showSidePaneSmall = !this.isScreenFhd;
    this.historyViewMode = this.getHistoryViewMode();
    this.cdr.detectChanges();
  }

  savedImplementation(selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.verification) {
      this.constraints.maxConstraint = true;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = selected.modifiedValue;
      this.setConstraintPlannedDates(this.constraints);
    }
    this.onSavedField(selected);
    this.updateLabelsByCommitmentTask();
  }

  savedReminder(selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.verification) {
      this.constraints.maxConstraint = false;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = selected.modifiedValue;
      this.setConstraintPlannedDates(this.constraints);
    }
  }

  changeConstraints(options): void {
    if (this.editableRules.verification && options.value) {
      this.constraints.maxConstraint = false;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = new Date();
      this.setConstraintPlannedDates(this.constraints);
    } else {
      this.setActivityContraints();
    }
  }

  private setActivityContraints(): void {
    const implementationDate = this.implementation ? this.implementation.value : this._implementationDisplay()?.dateValue;
    this.constraints.maxConstraint = false;
    this.constraints.minPlannedImplementation = new Date();
    this.constraints.minPlannedVerification = DateUtil.safe(implementationDate);
    this.setConstraintPlannedDates(this.constraints);
  }

  private updateSubTaskPanelStatus() {
    const isSubtaskPanelOpen = this.navLang.queryParam('isSubtaskPanelOpen');
    if (isSubtaskPanelOpen !== null && isSubtaskPanelOpen === '1') {
      this.openSubtaskPanel();
    }
  }

  private updateLabelsByCommitmentTask(data = this.data) {
    if (data.commitmentTask) {
      const titleKey = this.getTitleKeyValue(data);
      const relationshipKey = `${titleKey}-relationshipTab`;
      const childsKey = `${titleKey}-childTab`;
      this.translate
        .getTuples([`${titleKey}-title`, `${titleKey}-subtitle`, relationshipKey, childsKey])
        .pipe(takeUntil(this.$destroy))
        .subscribe(([activitiesTitle, activitiesSubtitle, relationshipTabTitle, childsTabTitle]) => {
          if (data.parentActivityDescription) {
            this.activitiesTitle = this.parser.interpolate(activitiesTitle, {
              childCount: this.childs?.length || 0,
              parentDescription: data.parentActivityDescription || '-'
            });
          } else {
            this.activitiesTitle = this.parser.interpolate(activitiesTitle, {
              childCount: this.childs?.length || 0
            });
          }
          this.relationshipTabTitle = relationshipTabTitle.indexOf(relationshipKey) === -1 ? relationshipTabTitle : relationshipKey;
          this.childsTabTitle = childsTabTitle;
          switch (data.commitmentTask) {
            case CommitmentTask.IMPLEMENTATION:
            case CommitmentTask.VERIFICATION:
              if (data.parentActivityDescription) {
                this.activitiesSubtitle = this.parser.interpolate(activitiesSubtitle, {
                  childCount: this.childs?.length || 0,
                  parentDescription: data.parentActivityDescription || '-',
                  commitmentDate: this.datePipe.transform(data.commitmentDate, this.dateFormat),
                  parentActivityCode: this.parentActivityCode || 'INVALID',
                  parentPlannerName: this.parentPlannerName || 'INVALID'
                });
              } else if (this.childs?.length) {
                this.activitiesSubtitle = this.parser.interpolate(activitiesSubtitle, {
                  childCount: this.childs?.length || 0,
                  commitmentDate: this.datePipe.transform(data.commitmentDate, this.dateFormat),
                  parentActivityCode: this.parentActivityCode || 'INVALID',
                  parentPlannerName: this.parentPlannerName || 'INVALID'
                });
              } else {
                this.activitiesSubtitle = this.parser.interpolate(activitiesSubtitle, {
                  commitmentDate: this.datePipe.transform(data.commitmentDate, this.dateFormat),
                  parentActivityCode: this.parentActivityCode || 'INVALID',
                  parentPlannerName: this.parentPlannerName || 'INVALID'
                });
              }
              if (this.belongSeries) {
                this.periodicityShortTag = StringFormat.titleCase(this.datePipe.transform(data.commitmentDate, 'EEEE, dd/MM/yyyy'));
              }
              break;
            default:
              if (this.belongSeries) {
                // Programa de periodicidades
                if (this.implementationPeriodicity?.value?.nextDateEnd && this.verificationPeriodicity?.value?.nextDateStart) {
                  this.activitiesSubtitle = this.parser.interpolate(activitiesSubtitle, {
                    nextImplementation: this.datePipe.transform(this.implementationPeriodicity.value.nextDateEnd, this.dateFormat),
                    nextVerification: this.datePipe.transform(this.verificationPeriodicity.value.nextDateStart, this.dateFormat)
                  });
                } else {
                  this.activitiesSubtitle = this.translate.instant('invalid-implementation-periodicity');
                }
                this.periodicityShortTag = this.translate.instant(
                  `root.core.periodicity.periodicity-tags.types-tags.${this.data.implementationPeriodicity.vchtipoperiodicidad}`
                );
              } else {
                // Actividad padre tipo "PROGRAM" sin periodicidad
                let nextImplementationTemp;
                if (this.childs?.length > 0) {
                  nextImplementationTemp = this.childs.reduce((a, b) => {
                    if (!DateUtil.isGreater(a.commitmentDate)) {
                      return b;
                    }
                    if (!DateUtil.isGreater(b.commitmentDate)) {
                      return a;
                    }
                    return a.commitmentDate < b.commitmentDate ? a : b;
                  })?.commitmentDate;
                  if (nextImplementationTemp && DateUtil.isGreaterOrEqual(nextImplementationTemp)) {
                    this.activitiesSubtitle = this.parser.interpolate(activitiesSubtitle, {
                      childCount: this.childs?.length || 0,
                      nextImplementation: this.datePipe.transform(nextImplementationTemp, this.dateFormat)
                    });
                  }
                } else {
                  this.activitiesSubtitle = activitiesSubtitle;
                }
              }
              break;
          }
        });
    }
  }

  private updateLabelsByModule() {
    switch (this.module) {
      case Module.PLANNER:
        this.commitmentSubtaskField = 'reminder';
        this.translate
          .get('root.common.field')
          .pipe(takeUntil(this.$destroy))
          .subscribe((label) => {
            this.implementationDateLabel = label.startDate;
            this.reminderDateLabel = label.endDate;
          });
        this.translate
          .get(`activities-${this.moduleTitleKey}-commitment-subtask-title`)
          .pipe(takeUntil(this.$destroy))
          .subscribe((commitmentSubtaskLabel) => {
            this.commitmentSubtaskLabel = commitmentSubtaskLabel;
          });
        break;
    }
    if (this.module === Module.PLANNER && !this.isRowModuleReplacementEnabled()) {
      this.addSubtaskLabel = this.translate.instant('planner-task-subtask');
    }
    const data = this.data;
    const status = data?.status;
    let updateData = false;
    if (status) {
      if (this.module === Module.PLANNER) {
        data.statusLabel = this.translate.instantFrom(ActivityCoreUtil.LANG_CONFIG, `${this.module}.${status}`);
      } else {
        data.statusLabel = this.translate.instantFrom(ActivityCoreUtil.LANG_CONFIG, `${Module.ACTIVITY}.${status}`);
      }
      updateData = true;
    }
    if (data?.mainStatus) {
      data.mainStatusLabel = this.translate.instantFrom(ActivityCoreUtil.LANG_CONFIG, `${Module.ACTIVITY}.${data.mainStatus}`);
    }
    if (data?.cancellationReason) {
      data.cancellationReasonLabel = this.translate.instantFrom(ActivityInfoConstant.LANG_CONFIG, `activityCancellationReasons.${data.cancellationReason}`);
      updateData = true;
    }
    const fillType = data?.fillType;
    if (fillType) {
      data.fillTypeLabel = this.translate.instantFrom(ActivityInfoConstant.LANG_CONFIG, `fillType.${FillType[fillType]}`);
      updateData = true;
    }
    if (updateData && this.mainForm) {
      this.mainForm.patchValue(data, {
        onlySelf: true,
        emitEvent: false
      });
      this.patchZonedDates(data);
    }
  }

  savedImplementationPeriodicity(selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.verificationPeriodicity) {
      this.constraints.maxConstraint = true;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = selected.modifiedValue.endDate;
      this.setConstraintPlannedDates(this.constraints);
    }
  }

  savedVerification(selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.implementation) {
      const implementationDate = this.implementation ? this.implementation.value : this._implementationDisplay()?.dateValue;
      this.constraints.maxConstraint = false;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = DateUtil.safe(implementationDate);
      this.setConstraintPlannedDates(this.constraints);
    }
    this.onSavedField(selected);
  }

  savedVerificationPeriodicity(_selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.implementationPeriodicity) {
      this.constraints.maxConstraint = true;
      this.constraints.minPlannedImplementation = new Date();
      this.constraints.minPlannedVerification = this.implementationPeriodicity.value.endDate;
      this.setConstraintPlannedDates(this.constraints);
    }
  }

  savedBusinessUnitDepartment(selected: ModifiedFieldResponseDTO) {
    if (this.editableRules.businessUnitDepartmentId) {
      const department = this.businessUnitDepartments.find((item: TextHasValue) => item && +item.value === +selected.modifiedValue);
      this.businessUnitId = department.businessUnitId;
    }
  }

  saveFile(event: IFileUploadAdded) {
    const file: IFileData = event.file;
    const grid = event.grid;
    this.api
      .post({ url: `${this.controller}/files/save/${this.id}`, cancelableReq: this.$destroy, postBody: file.id, options: null, handleFailure: false })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result)=> {
          this.noticeService.notice(this.tag('files.saved-file-result-true'));
          file.lastModifiedBy = Session.getUserName();
          file.lastModifiedDate = new Date();
          file.stage = result.stage;
          file.activityCommitmentTask = this.data.commitmentTask;
          file.activityId = this.id;
          this.slides = this.getFilesImages();
          if (!this.data.linked.files) {
            this.data.linked.files = [];
          }
          const files = this.files();
          if (files !== null) {
            this.data.linked.files = files.value;
          }
          if (result.history) {
            this.onAddedHistory(result.history);
          }
          if (grid) {
            grid.cdr.detectChanges();
          }
        },
        error: () => {
          this.noticeService.notice(this.tag('files.saved-file-result-false'));
        }
      });
  }

  deleteFile(file: IFileData, url: string) {
    this.api
      .post({
        url: this.controller + url + (file.activityId ? file.activityId : this.id),
        cancelableReq: this.$destroy,
        postBody: file.id,
        options: null,
        handleFailure: false
      })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result) => {
          if (!result) {
            this.files().addValue(file);
            this.noticeService.notice(this.tag('files.file-deleted-result-false'));
          }
          this.noticeService.notice(this.tag('files.file-deleted-result-true'));
          if (result.history) {
            this.onAddedHistory(result.history);
          }
          this.slides = this.getFilesImages();
          this.timesheet?.grid()?.refresh();
          if (this.mainForm.get('filesTimesheetActivity') !== null && this.mainForm.get('filesTimesheetActivity').value.length === 0) {
            this.showTimesheetFilesTab = false;
            this.cdr.detectChanges();
          }
        },
        error: () => {
          this.noticeService.notice(this.tag(`files.file-deleted-result-${false}`));
          this.files().addValue(file);
        }
      });
  }

  saveCommentFile(file: IFileData): void {
    const data = {
      fileId: file.id,
      commentFile: file.commentFile
    };
    const id = file.activityId ? file.activityId : this.id;
    this.api
      .post({ url: `${this.controller}/files/save-comment/${id}`, cancelableReq: this.$destroy, postBody: data, options: null, handleFailure: false })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result) => {
          this.noticeService.notice(this.tag('files.file-comment-result-true'));
          if (result) {
            file.activityId = file.activityId ? file.activityId : this.id;
          }
          this.slides = this.getFilesImages();
        },
        error: () => {
          this.noticeService.notice(this.tag('files.file-comment-result-false'));
          file.commentFile = null;
        }
      });
  }

  protected override init(): Observable<DataSourceResult> {
    console.log('init-edit');
    const subjectResult = new Subject<DataSourceResult>();
    const initResult = subjectResult.asObservable();
    this.routeSubscription = this.navLang.getRouteParam(':id', null, false, this.activatedRoute).subscribe(
      ([id]) => {
        if (id === null) {
          subjectResult.error('Null Id');
          return;
        }
        const module = Module[this.activatedRoute.snapshot.paramMap.get('module')?.toUpperCase()];
        const details: ActivityDetails = {
          activityId: +id,
          module: module
        };
        this.initializeByDetails(details, subjectResult);
      },
      (error) => subjectResult.error(error)
    );
    return initResult;
  }

  public initializeByDetails(details: ActivityDetails, subjectResult: Subject<DataSourceResult> = new Subject<DataSourceResult>()) {
    console.log('init-edit');
    const initResult = subjectResult.asObservable();
    this._initializeByDetails(details, subjectResult);
    this.activatedRoute.queryParams.subscribe((q) => {
      if (q?.pendingRecordId) {
        this.pendingRecordId = q.pendingRecordId;
      }
    });
    if (this.editMode) {
      this.translate
        .get('editable-info')
        .pipe(takeUntil(this.$destroy))
        .subscribe((editableInfo) => this.noticeService.notice(editableInfo));
    }
    return initResult;
  }

  private _initializeByDetails(details: ActivityDetails, subjectResult: Subject<DataSourceResult>) {
    this.initSubscription = this.initializeById(details).subscribe({
      next: (result) => {
        if (!result.ds.load) {
          subjectResult.error(result);
          return;
        }
        this.titleService.setTitle(`${result.ds.load.code}: ${result.ds.load.description}`);
        this.configureHiddenFields(result.ds.load.commitmentTask !== CommitmentTask.PROGRAM);
        this.configureConditionalFields(false);
        const load: ActivityLoadDto = result.ds.load;
        if (result.ds.load.commitmentTask === CommitmentTask.PROGRAM) {
          this.ownerNamesLabel = this.translate.instantFrom(ActivitiesDetailComponent.LANG_CONFIG, 'activity-program-implementer');
        }
        this.descriptionReadOnlyLabel = this.translate.instant(this.commitmenTaskCreatedLabel[result.ds.load.commitmentTask], {
          createdDate: this.datePipe.transform(result.ds.load.createdDate, 'dd/MM/YYYY', this.getTimezone(), this.getLang()).toString(),
          createdTime: this.datePipe.transform(result.ds.load.createdDate, 'HH:mm:ss', this.getTimezone(), this.getLang()).toString(),
          author: result.ds.load.creatorUserName.trim(),
          department: result.ds.load.creatorUserDepartment || this.tag('noDepartment')
        });
        const dropdownmenu = this.dropdownmenu();
        const activityInfo = this.activityInfo();
        if (dropdownmenu && load.availableActions && activityInfo?.pendingMenuActions) {
          const actions = this.getActions(activityInfo.pendingMenuActions, load.availableActions);
          for (const menuOption of actions) {
            if (this.invalidFill(menuOption, load)) {
              continue;
            }
            this.addMenuOption([menuOption]);
            const iconName = (ACTIVITY_ACTIONS[menuOption] || { iconName: 'check' }).iconName;
            dropdownmenu.pushMenuOption({
              text: this.i18n?.menuOption[menuOption] || menuOption,
              value: menuOption,
              iconName: typeof iconName === 'undefined' ? 'check' : iconName // <-- El valor NULL es valido para poder usar un "checkbox"
            });
          }
        }
        this.refreshPartitionAccess();
        subjectResult.next(result);
        this.cdr.detectChanges();
      },
      error: (error) => subjectResult.error(error)
    });
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit();
    if (!this.isMobilDevice()) {
      this.activitiesPanel.changes.pipe(takeUntil(this.$destroy)).subscribe((queryList: QueryList<ActivitiesAddManyComponent>) => {
        if (queryList.length) {
          this.scrollToBottom();
        }
      });
    }
  }

  scrollToBottom() {
    setTimeout(() => {
      document.querySelector('#activities-detail-bottom').scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
    }, 300);
  }

  refreshData(refresh = true): void {
    if (!refresh) {
      console.log('refreshData(false)!');
      return;
    }
    if (!this.id) {
      console.error('Tried to refresh without an activityId');
      return;
    }
    this.busy = true;
    this.cdr.detectChanges();
    this.initializeById(this.activityDetails)
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        this.busy = false;
        this.cdr.detectChanges();
      });
  }

  deleteImplementer(implementerUser: TextHasUserValue): void {
    this.data.implementerUsers = this.data.implementerUsers?.filter((user) => {
      return user.value !== implementerUser.value && user.value !== null;
    });
    this.updateImplementerCache();
  }

  deletePreImplementer(preImplementerUser: TextHasUserValue): void {
    this.data.preImplementerUsers = this.data.preImplementerUsers?.filter((user) => {
      return user.value !== preImplementerUser.value && user.value !== null;
    });
    this.updatePreImplementerCache();
  }

  public initializeById(activityDetails: ActivityDetails): Observable<DataSourceResult> {
    this.setParticipantsSelectColumns();
    this.setActivityDetails(activityDetails);
    this.refreshFieldDisplays(['initialize']);
    const subjectResult = new Subject<DataSourceResult>();
    const initResult = subjectResult.asObservable();
    this.superInitSubscription = super.init(this.id).subscribe({
      next: (result: DataSourceResult) => {
        this.initializeDataSourceResult(result);
        subjectResult.next(result);
        if (this.hasTimesheetAccess) {
          this.loadTimesheetContentTab();
        }
        this.refreshPartitionAccess();
        this.cdr.detectChanges();
      },
      error: (error) => subjectResult.error(error)
    });
    return initResult;
  }

  private initializeActivityNavigator(): void {
    const codes: string[] = loadActivityNavigator([this.code]);
    if (codes.length > 0) {
      this.setMenuActivityNavigatorOptions(codes);
    } else {
      this.api
        .get({ url: `${this.controller}/navigator-codes/${this.typeId}/${this.id}`, handleFailure: false, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (codes) => {
            this.setMenuActivityNavigatorOptions(codes || []);
          },
          error: (error) => {
            console.error(error);
            ErrorHandling.notifyError(error, this.navLang);
          }
        });
    }
    saveActivityNavigatorHistory(this.code);
  }

  private setMenuActivityNavigatorOptions(codes: string[]) {
    const current = LocalStorageSession.getValue(LocalStorageItem.ACTIVITY_NAVIGATOR_HISTORY_LIST) || [];
    this.menuActivityNavigatorOptions = codes.sort().map((code) => {
      const pair = String(code).split('@'); // <-- "clave@módulo"
      return {
        text: pair[0] || code,
        value: code,
        // modernHrefRest: `rest/activities/${pair[1]}/redirect/${pair[0]}`,
        iconName: current.indexOf(pair[0]) === -1 ? 'open_in_browser' : 'history'
      };
    });
  }

  private initializeDataSourceResult(result: DataSourceResult) {
    if (result.ds.load?.verification && result.ds.constraints) {
      result.ds.constraints.maxConstraint = false;
    }
    this.setDataSource(result.ds);
    this.initialized = true;
  }

  private setActivityDetails(details: ActivityDetails) {
    this.activityDetails = details;
    if (details.module && (details.module?.toString() !== 'undefined' || details.module?.toString() !== '')) {
      this.module = Module[details.module.toString().toUpperCase()];
      this.updateLabelsByModule();
    }
    this.id = +details.activityId;
  }
  protected override onLoadedDataSource(
    response: ActivityDataSourceDto,
    tsDto: TimesheetDataSourceDto,
    typeId: number,
    id: number,
    subjectResult: Subject<DataSourceResult>
  ): void {
    super.onLoadedDataSource(response, tsDto, typeId, id, subjectResult);
  }
  protected initializeByDataSource(result: DataSourceResult) {
    const details: ActivityDetails = {
      activityId: result.ds.load.id,
      module: getNormalizedModule(result.ds.load.module || result.ds.load.moduleName)
    };
    this.setActivityDetails(details);
    this.refreshFieldDisplays(['initialize']);
    this.setCatalogsDataSource(result.ds, details.activityId);
    this.cdr.detectChanges();
    this.initializeDataSourceResult(result);
  }

  public addImplementerUser(implementerUser?: TextHasUserValue): void {
    this.data.implementerUsers.push(
      implementerUser || {
        mail: null,
        code: null,
        account: null,
        text: null,
        value: null
      }
    );
    if (!implementerUser) {
      this.noticeService.notice('Capture la información del nuevo responsable');
    }
    this._isImplementer = !!this.data.implementerUsers?.find((user) => +user.value === Session.getUserId());
    this.updateImplementerCache(implementerUser?.value != null);
  }

  public addPreImplementerUser(preImplementerUser?: TextHasUserValue): void {
    if (!this.data.preImplementerUsers) {
      this.data.preImplementerUsers = [];
    }
    this.data.preImplementerUsers.push(
      preImplementerUser || {
        mail: null,
        code: null,
        account: null,
        text: null,
        value: null
      }
    );
    if (!preImplementerUser) {
      this.noticeService.notice('Capture la información del nuevo responsable pre-asignado');
    }
    this.updatePreImplementerCache(preImplementerUser?.value != null);
  }

  private intializeDataFromActivity(name: string, data: TextHasValue[], value: number | TextLongValue | TextHasValue): void {
    if (!value) {
      // Se excluyen los valores inválidos
      return;
    }
    if (typeof value === 'object' && (value as ITextValue)?.value == null) {
      // Se excluyen los valores inválidos de objetos
      return;
    }
    let val = +value;
    if (typeof (value as TextLongValue).text === 'string') {
      val = (value as TextLongValue).value;
    }
    if (name !== 'verifier' && name !== 'implementer' && name !== 'preImplementer') {
      return;
    }
    if (data.some((item) => +item.value === val)) {
      return;
    }
    if (typeof (value as TextLongValue).text === 'string') {
      data.push(value as TextHasValue);
    } else {
      data.push({
        text: this.data[`${name}Name`],
        value: val
      });
    }
  }

  public isRemovableImplementer(userValue: number): boolean {
    //Similar a VALID_EDIT_IMPLEMENTER_STATUS
    const activityStatusRemovable =
      this.data.status === ActivityStatus.REPORTED || this.data.status === ActivityStatus.RETURNED || this.data.status === ActivityStatus.IN_PROCESS;
    return this.data.implementerUsers.length > 1 && userValue && this.data?.commitmentTask === CommitmentTask.IMPLEMENTATION && activityStatusRemovable;
  }

  public getFieldDisplayImplementerInstance(options: TextHasValue[], user: TextHasUserValue, toolTipInfo: Partial<ActivitiesOwnerAvatarTooltip>) {
    if (!this.initialized || !this.configFieldDisplay.implementer) {
      return null;
    }
    let fieldData: TextHasValue[] = options || [];
    this.intializeDataFromActivity('implementer', fieldData, user);
    fieldData = fieldData.filter((d: TextHasValue) => {
      return d.value === user.value || (!this.data.implementerUsers?.find((i) => i.value === d.value) && this.data.verifier !== d.value);
    });
    if (this.implementersCache.length !== 0) {
      for (const value of this.implementersCache) {
        const index = this.implementersCache.indexOf(value);
        if (value === user.value) {
          this.implementersCache.splice(index, 1);
          this.configFieldDisplay.implementer.hasChanged = true;
        }
      }
    } else {
      this.configFieldDisplay.implementer.hasChanged = false;
    }
    this.configFieldDisplay.implementer.tooltipId = user.activityId;

    if (toolTipInfo?.displayTopValue || toolTipInfo?.code) {
      this.configFieldDisplay.implementer.tooltipTopLabel = toolTipInfo.displayTopValue || toolTipInfo.code.replace(/^.+?IMP/, 'IMP').replace(/^.+?VER/, 'VER');
    } else if (toolTipInfo) {
      this.configFieldDisplay.implementer.tooltipTopLabel = this.data.code;
    } else {
      this.configFieldDisplay.implementer.tooltipTopLabel = user.commitmentTask === 1 ? 'IMP' : 'VER';
    }
    if (toolTipInfo?.displayBottomValue) {
      this.configFieldDisplay.implementer.tooltipBottomLabel = toolTipInfo.displayBottomValue;
    }
    return FieldDisplayComponent.getFieldDisplayConfigInstance(this.configFieldDisplay.implementer, fieldData, user);
  }

  public getFieldDisplayPreImplementerInstance(options: TextHasValue[], user: TextLongValue) {
    if (!this.initialized || !this.configFieldDisplay.preImplementer) {
      return null;
    }
    let fieldData: TextHasValue[] = options || [];
    this.intializeDataFromActivity('preImplementer', fieldData, user);
    if (user !== null) {
      fieldData = fieldData.filter((d: TextHasValue) => {
        return d.value === user.value || !this.data.preImplementerUsers?.find((i) => i.value === d.value);
      });
    }
    if (this.preImplementersCache.length !== 0 && user !== null) {
      for (const value of this.preImplementersCache) {
        const index = this.preImplementersCache.indexOf(value);
        if (value === user.value) {
          this.preImplementersCache.splice(index, 1);
          this.configFieldDisplay.preImplementer.hasChanged = true;
        }
      }
    } else {
      this.configFieldDisplay.preImplementer.hasChanged = false;
    }
    return FieldDisplayComponent.getFieldDisplayConfigInstance(this.configFieldDisplay.preImplementer, fieldData, user);
  }

  public getVerifiersFieldDisplayConfigInstance(name: FieldDisplayField, data: TextLongValue[], value?: number | TextLongValue) {
    if (!this.initialized || !this.configFieldDisplay[name]) {
      return null;
    }
    if (typeof value === 'undefined') {
      value = this.data[name.toString()];
    }
    let fieldData: TextLongValue[];
    if (data) {
      fieldData = data;
    } else {
      fieldData = [];
    }
    if (name === 'verifier') {
      if (this.commitmentTask && this.commitmentTask === CommitmentTask.PROGRAM && this.fillTypes?.find((e) => e.value === FillType.FILL_FORM)) {
        this.configFieldDisplay[name].editable = false;
      }
      this.configFieldDisplay[name].hasChanged = this.implementersHasChanged;
      this.implementersHasChanged = false;
      fieldData = fieldData.filter((d: TextHasValue) => {
        return +d.value === value || (!this.data.implementerUsers?.find((i) => i.value === +d.value) && this.data.implementer !== +d.value);
      });
    }
    this.intializeDataFromActivity(name, fieldData, value);
    return FieldDisplayComponent.getFieldDisplayConfigInstance(this.configFieldDisplay[name], fieldData, value);
  }

  public getPeriodicityFieldConfig(name: FieldDisplayField) {
    if (!this.configFieldDisplay[name]) {
      return null;
    }
    return FieldDisplayComponent.getPeriodicityFieldConfigInstance(this.configFieldDisplay[name], this.hasSameDayPeriodicity(name), PeriodicityType.weekly, true);
  }

  private hasSameDayPeriodicity(name: FieldDisplayField) {
    if (name === 'implementationPeriodicity') {
      return this.disabledFields.anticipationAttendDays;
    }
    return false;
  }

  private cancelRecurrence(recurrenceId: number) {
    this.dialogService.confirm(this.tag('cancel-series-dialog-message')).then(() => {
      this.api
        .get({ url: `${this.controller}/cancel-series/${recurrenceId}`, handleFailure: false, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: () => {
            this.noticeService.notice(this.tag('accept-dialog-message'));
          },
          error: (error) => {
            console.error(error);
            ErrorHandling.notifyError(error, this.navLang);
          }
        });
    });
  }

  public getFieldDisplayConfigSelectInstance(name: FieldDisplayField, data: ITextHasValue<string | number>[], editable = false): FieldDisplayConfig {
    if (
      !this.initialized ||
      this.data === null ||
      typeof this.data === 'undefined' ||
      !this.configFieldDisplay[name] ||
      (!this.data[name.toString()] && !this.defaultHiddenValues[name.toString()])
    ) {
      return null;
    }
    const config = this.configFieldDisplay[name];
    config.editable = editable || config.editable || false;
    return FieldDisplayComponent.getFieldDisplayConfigInstance(config, data, this.data[name.toString()]);
  }

  public getPlannerTaskConfig(): FieldDisplayConfig {
    if (!this.initialized) {
      return null;
    }
    const config = this.getFieldDisplayConfigSelectInstance('plannerTask', []);
    if (config) {
      if (!config.multipleData) {
        config.multipleData = {
          clients: [],
          planners: [],
          tasks: []
        };
      }
      config.fieldDisplay.editable = !!this.editableRules.plannerTask;
    } else {
      const config = FieldDisplayComponent.getFieldDisplayConfigInstance(this.configFieldDisplay.plannerTask, [], null);
      config.fieldDisplay.editable = false;
      return config;
    }
    config.fieldDisplay.dataSourceUrl = `${this.controller}/timesheet/data-source`;
    return config;
  }

  public getFillFormConfig(): FieldDisplayConfig {
    if (!this.initialized) {
      return null;
    }
    const fillForm: ActivityFormEntity = this.data.fillForm;
    if (!fillForm) {
      return null;
    }
    const config = FieldDisplayComponent.getDefaultConfigInstance('fillForm', FieldDisplayType.ENTITY);
    const entity: TextHasValue = {
      text: `${fillForm.description}, ${this.translate.instant('document-code')} ${fillForm.code}`,
      value: fillForm.id
    };
    config.hasAction = true;
    return FieldDisplayComponent.getFieldDisplayConfigInstance(config, null, entity);
  }

  public previewFillForm() {
    if (this.data.outstandingSurveyId) {
      const requestUrl = `../view/v-popup-loader.view?url=${encodeUrlParameter('v.request.survey.preview.view')}&id=O${this.data.outstandingSurveyId}&activityId=${this.data.recordId}&requestMode=PREVIEW&fromPath=detail`;
      this.menuService.navigateLegacy(requestUrl);
    } else {
      const fillForm: ActivityFormEntity = this.data.fillForm;
      const viewerUrl = `v-document-viewer.view?id=${fillForm.id}`;
      this.menuService.navigateLegacy(viewerUrl);
    }
    this.returned.emit(this.dataSource);
  }

  public getMainActivityHref(activityId: number): string {
    if (this.module === Module.PLANNER) {
      return this.menuService.buildModernUrl(`planner/edit/${activityId}`, this.translateService.currentLang);
    }
    return this.menuService.buildModernUrl(`activities/${activityId}`, this.translateService.currentLang);
  }

  public goToMainActivity(activityId: number): void {
    this.menuService.navigate(`menu/activities/${activityId}`, this.module);
  }

  public goToLinkedSource(): void {
    switch (this.module) {
      case Module.PLANNER: {
        const plannerId = this.data.linkedSource.id;
        this.menuService.navigate(`menu/planner/edit/${plannerId}`, this.module);
        return;
      }
      case Module.FORMULARIE: {
        const outstandingSurveysId = this.data.linkedSource.id;
        const requestId = this.data.linkedSource.requestId;
        const requestUrl = `v.request.survey.preview.view?id=O${outstandingSurveysId}&requestId=${requestId || ''}`;
        this.menuService.navigateLegacy(requestUrl, this.module);
        break;
      }
      case Module.ACTION: {
        const findingId = this.data.linkedSource.id;
        const findingCode = this.data.linkedSource.code;
        const findingUrl = `v.action.by.type.view?intAccionGenericaId=${findingId}&accion=${findingCode}&id=${findingId}`;
        this.menuService.navigateLegacy(findingUrl, this.module);
        break;
      }
      case Module.AUDIT: {
        const recordId = this.data.linkedSource.id;
        const auditUrl = `v.audit.individual.handle.view?task=edit&id=${recordId}`;
        this.menuService.navigateLegacy(auditUrl, this.module);
        break;
      }
      default:
        this.dialogService.error('Not supported to open linked source.');
        break;
    }
    this.returned.emit(null);
  }

  private setDataSource(result: ActivityDataSourceDto): void {
    this.dataSource = result;
    const data = result.load;
    if (!data) {
      console.error('Failure while initializing the form.');
      return;
    }
    this.setActivityLoad(result.load);
    const hiddenRules: HiddenRules = result.hiddenRules || this.hiddenRules;
    const disabledFields: DisabledFields = result.disabledFields || this.disabledFields;
    this.setCustomDetailRules(data, disabledFields, hiddenRules);
    this.setAddSubtaskByDepartment(result.addSubtaskByDepartment);
    this.setHiddenRules(hiddenRules, result.dynamicFields);
    this.setDisabledFields(disabledFields);
    this.setEditionRules(result.editionRules || null);
    if (result.dynamicFields) {
      this.setDynamicFields(result.typeId, result.dynamicFields || null); // <-- Asegurarse de que esto se llame ANTES que `this.dynamic.load({activityId});`
    }
    this.refreshFieldDisplays();
    this.addMenuOption(data.availableActions);
    this.setConstraintPlannedDates(result.constraints);
    this.enableRelationsship = (this.siblings?.length || 0) + (this.plannedImplementations?.length || 0) > 0;
    this.showRelationshipTab =
      result.load.commitmentTask === CommitmentTask.PROGRAM && !result.constraints.addImplementationOnCreate && !result.constraints.addVerificationOnCreate;
    this.cdr.detectChanges();
    this.setSelectedTabByParam();
    this.initializeActivityNavigator();
    this.defaultDeleteServices = deleteServicesByModule(this.module);
  }

  private addMenuOption(availableActions: string[]) {
    if (availableActions) {
      for (const e of availableActions) {
        if (!this.menuOptionsAvailable.find((opt) => opt === e)) {
          this.menuOptionsAvailable.push(e);
        }
      }
    }
  }

  private setDynamicFields(typeId: number, fieldData: DynamicFieldsDTO) {
    const dynamic = this.dynamic();
    if (dynamic) {
      dynamic.setDynamicFields(typeId, fieldData);
      this.refreshDynamicFieldsDisplay();
    } else if (fieldData.valid) {
      throw Error('INVALID_DYNAMIC_FIELDS_LOADING_ORDER');
    }
  }

  private setCustomDetailRules(data: ActivityLoadDto, disabledFields: DisabledFields, hiddenRules: HiddenRules) {
    const isPlanner = data.module === Module.PLANNER;
    if (isPlanner) {
      // EL módulo de PLANNER tiene bien definidas sus reglas desdel BackEnd en "getHiddenRules", en caso de falla corregirlo allá
      return;
    }
    /**
     * ToDo: Consolidar los HIDDEN_RULES de esta función en el metodo del BACKEND de nombre "getHiddenRules()"
     **/
    if (data.objectiveId == null) {
      hiddenRules.objectiveId = true;
      disabledFields.objectiveId = true;
    } else {
      hiddenRules.objectiveId = false;
      disabledFields.objectiveId = false;
    }
    if (data.priority == null) {
      hiddenRules.priority = true;
      disabledFields.priority = true;
    } else {
      hiddenRules.priority = false;
      disabledFields.priority = false;
    }
    if (data.source == null) {
      hiddenRules.source = true;
      disabledFields.source = true;
    } else {
      hiddenRules.source = false;
      disabledFields.source = false;
    }
    if (data.categoryId == null) {
      hiddenRules.categoryId = true;
      disabledFields.categoryId = true;
    } else {
      hiddenRules.categoryId = false;
      disabledFields.categoryId = false;
    }
    if (data.commitmentTask === CommitmentTask.PROGRAM) {
      if (!data.childs) {
        disabledFields.implementationPeriodicity = false;
        hiddenRules.implementationPeriodicity = false;
      }
      disabledFields.verificationPeriodicity = false;
      hiddenRules.verificationPeriodicity = false;
    } else if (data.commitmentTask === CommitmentTask.VERIFICATION) {
      disabledFields.reminder = true;
      hiddenRules.reminder = true;
      disabledFields.implementation = true;
      hiddenRules.implementation = true;
      disabledFields.verification = false;
      hiddenRules.verification = false;
    }
  }

  private updateImplementerCache(clearEmpty = false): void {
    if (this.data?.implementerUsers) {
      const newConfig: DataMap<number> = {};
      for (const user of this.data.implementerUsers) {
        newConfig[`${user.value}`] = user.value;
      }
      if (this._implementer !== newConfig) {
        this._implementer = newConfig;
        this.implementersHasChanged = true;
      }
      if (clearEmpty) {
        this.data.implementerUsers = this.data.implementerUsers.filter((implementer) => {
          return implementer.value != null;
        });
      }
      this.setDefaultImplementerIds(this.data.implementerUsers.map((a) => a.value));
    } else {
      this._implementer = null;
    }
  }

  private updatePreImplementerCache(clearEmpty = false): void {
    if (this.data?.preImplementerUsers) {
      const newConfig: DataMap<number> = {};
      for (const user of this.data.preImplementerUsers) {
        newConfig[`${user.value}`] = user.value;
      }
      if (this._preImplementer !== newConfig) {
        this._preImplementer = newConfig;
      }
      if (clearEmpty) {
        this.data.preImplementerUsers = this.data.preImplementerUsers.filter((implementer) => {
          return implementer.value != null;
        });
      }
    } else {
      this._preImplementer = null;
    }
  }

  private setActivityLoad(data: ActivityLoadDto): void {
    if (!data) {
      console.error('Failure while updated the data form.');
      return;
    }
    this.id = data.id;
    if (data.participants?.length) {
      this.participants().value = data.participants.map((participant) => ({
        entity_id: participant.userId,
        entity_code: participant.code,
        entity_cuenta: participant.account,
        entity_correo: participant.mail,
        entity_description: participant.description,
        bud_description: participant.businessUnitDepartmentName,
        bu_description: participant.businessUnitName,
        bud_id: participant.businessUnitDepartmentId
      }));
      this.addMenuOption([ActivitiesDetailAction.ADD_PARTICIPANT]);
      const menuOption = this.i18n?.menuOption as DataMap<string>;
      this.dropdownmenu().pushMenuOption({
        text: menuOption?.ADD_PARTICIPANT || 'ADD_PARTICIPANT',
        value: ActivitiesDetailAction.ADD_PARTICIPANT,
        iconName: 'person_add'
      });
    }
    this.module = data.module;
    this.belongSeries = data.belongSeries;
    this.commitmentTask = data.commitmentTask || null;
    switch (this.module) {
      case Module.PLANNER:
        this.parentActivityCode = data.parentPlannerCode || null;
        this.parentPlannerName = data.parentPlannerName || null;
        this.moduleTitleKey = 'planner';
        // Se llenan los datos de las fechas planeadas.
        this.plannerStartDatesEntity = {
          implementationStartDate: new Date(),
          implementation: DateUtil.safe(data.reminder)
        };
        // Se llena el valor del parentPlannerID
        this.parentPlannerId = data.parentPlannerId;
        break;
      default:
        this.parentActivityCode = data.parentActivityDescription || data.parentActivityCode || null;
        this.parentPlannerName = data.parentActivityDescription || data.parentActivityCode || null;
        this.moduleTitleKey = 'activity';
    }
    if (data.parentActivityImplementationId) {
      this.parentActivityProgress = NumberUtil.round(data.parentActivityImplementationProgress, 2) || 0;
    } else if (data.parentActivityId) {
      this.parentActivityProgress = NumberUtil.round(data.parentActivityProgress, 2) || 0;
    } else {
      this.parentActivityProgress = null;
    }
    this.partitions = data.partitions || null;
    this.events = data.events || null;
    this.siblings = data.siblings || null;
    this.childs = data.childs || null;
    if (this.siblings) {
      this.siblings = this.siblings.filter((a) => a.recordId !== this.id);
    }
    switch (data.commitmentTask) {
      case CommitmentTask.IMPLEMENTATION:
      case CommitmentTask.VERIFICATION:
        this.recurrenceId = data.recurrenceId || null;
        break;
      default:
        this.recurrenceId = data.id;
    }
    this.parentActivityId = data.parentActivityId || null;
    this.parentActivityImplementationId = data.parentActivityImplementationId || null;
    this.parentTreeActivityId = data.parentTreeActivityId || null;
    this.businessUnitDepartment = {
      text: data.businessUnitDepartmentName || '-',
      value: data.businessUnitDepartmentId || null,
      businessUnitId: data.businessUnitId || null
    };
    this.businessUnitId = data.businessUnitId || null;
    this.plannedImplementations = data.plannedImplementations || null;
    this.hasFillForm = EnumUtil.enumEquals(FillType, +data.fillType, FillType.FILL_FORM);
    this.requiredRules.fillForm = this.hasFillForm;
    this.mainForm.patchValue(data, {
      onlySelf: true,
      emitEvent: false
    });
    this.patchZonedDates(data);
    this.data = this.parsedDates(data);
    this._isImplementer = !!this.data.implementerUsers?.find((user) => +user.value === Session.getUserId());
    this._hasChilds = +this.data.childs?.length > 0;
    this.progress = NumberUtil.round(data.progress || 0, 2);
    this.code = data.code;
    this.mainForm.patchValue({
      comments: data.linked.comments,
      documents: data.linked.documents,
      files: data.linked.files,
      history: data.history
    });
    this.linkedSource = {
      module: this.module,
      parentCode: this.data.code,
      urlAction: null,
      businessUnitId: this.data.businessUnitId || null,
      businessUnitDepartmentId: this.data.businessUnitId || null,
      plannerId: this.data.plannerTask?.plannerId || null // Actualmente solo se pueden agregar subtareas de PLANNER y ACTIVITY
    };
    this.historyViewMode = this.getHistoryViewMode();
    this.updateImplementerCache();
    this.updateLabelsByCommitmentTask();
    this.updateLabelsByModule();
    this.updateSubTaskPanelStatus();
    if (this.files()) {
      this.slides = this.getFilesImages();
      if (this.slides.length > 0) {
        this.isOpenImageZone = true;
      }
    }
    this.cdr.detectChanges();
    const description = this.description();
    if (description) {
      description.nativeElement.dispatchEvent(new Event('input'));
    }
    this.api
      .get({ url: `${this.controller}/relationship-activity/count/${this.id}`, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe((response: any) => {
        if (response === null) {
          this._relationshipCount = 0;
        }

        if (response.count === null) {
          this._relationshipCount = 0;
        }
        this._relationshipCount = response.count;
        this.cdr.detectChanges();
      });
  }

  private patchZonedDates(load: ActivityLoadDto): void {
    // En este lugar se aplican las fechas zoneadas correspondiente a su timezone(zoneId)
    if (load.plannedImplementationZoned) {
      this.mainForm.patchValue(
        { implementation: load.plannedImplementationZoned },
        {
          onlySelf: true,
          emitEvent: false
        }
      );
    }
    if (load.plannedVerificationZoned) {
      this.mainForm.patchValue(
        { verification: load.plannedVerificationZoned },
        {
          onlySelf: true,
          emitEvent: false
        }
      );
    }
  }

  private parsedDates(data: ActivityLoadDto): ActivityLoadDto {
    if (data.implementationPeriodicity) {
      data.implementationPeriodicity.startDate = new Date(data.implementationPeriodicity.startDate);
      data.implementationPeriodicity.endDate = new Date(data.implementationPeriodicity.endDate);
    }
    if (data.verificationPeriodicity) {
      data.verificationPeriodicity.startDate = new Date(data.verificationPeriodicity.startDate);
      data.verificationPeriodicity.endDate = new Date(data.verificationPeriodicity.endDate);
    }
    if (data.reminder) {
      data.reminder = DateUtil.safe(data.reminder);
    }
    if (data.anticipationAttendDays !== null && typeof data.anticipationAttendDays !== 'undefined' && data.anticipationAttendDays > 0) {
      data.reminder = DateUtil.add(data.commitmentDate, DateInterval.DAY, data.anticipationAttendDays);
    } else {
      data.reminder = data.commitmentDate;
    }
    if (data.implementation) {
      data.implementation = new Date(data.implementation);
    }
    if (data.verification) {
      data.verification = new Date(data.verification);
    }
    return data;
  }

  private getTitleKeyValue(data: ActivityLoadDto, moduleTitleKey = this.moduleTitleKey): string {
    let titleKey = `activities-${moduleTitleKey}`;
    // ToDo: -hasChilds-hasSiblings <-- Hacen falta etiquetas para actividades padre con "siblings"
    if (+data.childs?.length > 0) {
      titleKey += '-hasChilds';
      if (data.parentActivityId && data.parentActivityDescription) {
        titleKey += '-withparent';
      }
    } else if (data.siblings) {
      titleKey += '-hasSiblings';
    }
    titleKey += `-${EnumUtil.getName(CommitmentTask, data.commitmentTask).toLowerCase()}`;
    if (data.belongSeries && moduleTitleKey !== 'planner') {
      // <-- Se excluyen proyectos ya que no tienen repetición
      titleKey += '-belongSeries';
    }
    return titleKey;
  }

  changeType(type: TextHasValue) {
    if (this.busy) {
      console.log('on change event not fired because form is busy.');
      return;
    }
    super.changeType(type);
  }

  /**
   * Solo hay 7 campos que se pueden editar despues del alta:
   * - implementer
   * - verifier
   * - implementation
   * - verification
   * - apply
   * - progress
   * - description
   *
   * Campos que cuentan con boton de "saveButton" el cual
   * se utiliza para confirmar directamente el cambio despues de editar.
   * - description
   * - implementer
   * - verifier
   *
   * Campos que se guardan directamente despues de una confirmación
   * - implementation
   * - implementationPeriodicity
   * - verification
   * - verificationPeriodicity
   *
   * @param {*} editionRules
   * @returns
   * @memberof ActivitiesDetailComponent
   */
  setEditionRules(editionRules: EditionRules) {
    if (!editionRules) {
      console.warn('No edition rules.');
      return;
    }
    editionRules.reminder = editionRules.implementation;
    editionRules.daysToVerify = editionRules.verification;
    for (const key in this.editableRules) {
      if (!this.editableRules.hasOwnProperty(key)) {
        continue;
      }
      if ((this.hiddenRules.hasOwnProperty(key) && this.hiddenRules[key]) || (this.disabledFields.hasOwnProperty(key) && this.disabledFields[key])) {
        // los campos adicionales automaticamente son "no requeridos"
        this.requiredRules[key] = false;
      }
      if (editionRules.hasOwnProperty(key)) {
        this.editableRules[key] = editionRules[key];
        if (editionRules[key] && this.mainForm.controls.hasOwnProperty(key)) {
          // se habilita el botón "editar"
          this.editModeAvailable = true;
        }
      } else if (this.editableRules.hasOwnProperty(key)) {
        this.editableRules[key] = false;
      }
    }
  }

  refreshFieldDisplays(options: ('initialize' | 'refresh')[] = []): void {
    for (const k in this.configFieldDisplay) {
      if (!this.configFieldDisplay.hasOwnProperty(k)) {
        continue;
      }
      const key = k as FieldDisplayField;
      if (options.includes('initialize')) {
        this.initFieldDisplay(this.configFieldDisplay[key]);
      } else {
        this.refreshFieldDisplay(this.configFieldDisplay[key]);
      }
    }
    /**
     * El valor de los campos dinamicos debe refrescarse hasta despues
     * de haber cargado "editionRules" por que necesita el valor
     * de "this.editableRules.dynamicFields"
     *
     **/
    if (options.includes('refresh')) {
      this.refreshDynamicFieldsDisplay();
    }
  }

  private refreshDynamicFieldsDisplay(): void {
    const dynamic = this.dynamic();
    if (!dynamic) {
      return;
    }
    /**
     *
     * El valor de los campos dinamicos debe refrescarse hasta despues
     * de haber cargado "editionRules" por que necesita el valor de "this.editableRules.dynamicFields"
     *
     * Asegurarse tambien que `info` tenga información en `dynamicFields`, ya que
     * se marcó el componente como `offline = true`, ya que la configuración de campos dinamicos se estaba cargando varias veces.
     *
     **/
    if (this.showMainActivity && this.data.recurrenceId) {
      dynamic.load(this.data.recurrenceId);
    } else {
      dynamic.load(this.id);
    }
  }

  saveComment(evt: SaveCommentEvt, activityId = this.id): void {
    const comment = evt.commentEntity;
    this.savingComment = true;
    this.cdr.detectChanges();
    let endPoint = `${this.controller}/comments/save/${activityId}`;
    if (typeof comment.id !== 'undefined' && comment.id !== null) {
      endPoint += `/${comment.id}`;
    }
    this.api
      .post({ url: endPoint, cancelableReq: this.$destroy, postBody: comment.description })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result) => {
          comment.id = result.itemId;
          if (result.createdDate) {
            comment.lastModifiedDate = new Date(result.createdDate);
          }
          comment.stage = result.stage;
          comment.activityCommitmentTask = this.data.commitmentTask;
          comment.activityId = this.id;
          comment.createdBy = Session.getUserId();
          if (!this.data.linked.comments) {
            this.data.linked.comments = [];
          }
          const comments = this.comments();
          if (comments !== null) {
            this.data.linked.comments = comments.value;
          }
          this.noticeService.notice('root.common.message.saved-comment');
          if (result.history) {
            this.onAddedHistory(result.history);
          }
          this.savingComment = false;
          evt.grid.detectChanges();
        },
        error: () => {
          this.savingComment = false;
        }
      });
  }

  onTimesheetDeleted(data: StopwatchEndTimeData): void {
    //Se agrega realiza max para evitar valores negativos en la resta, ya que el valor negativo es invalido, de ser asi, que regrese 0
    //suele suceder cuando es el primer registro y no tiene otros, en ese caso actualHours vale 0
    const actualHours = Math.max(0, (this.data.actualHours || 0.0) - (data.workedMinutes || 0));
    this.data = { ...this.data, ...{ actualHours: NumberUtil.round(actualHours, 2) } };
    this.setActivityLoad(this.data);
    this.getTimesheetActivityFiles(this.id);
    data.hasEnableStopwatch = false;
    data.end = new Date();
    this.api.post({ url: `timesheet/updateStopwatch/${data.timesheetId}`, cancelableReq: this.$destroy, postBody: data }).subscribe({
      next: (response) => {
        if (response) {
          this.timesheetService.updateUIStopwatchService(data.hasEnableStopwatch);
        }
      },
      error: (error) => {
        console.error(error);
      }
    });
  }

  override onParticipantsSelect(args: RowSelection<ParticipantDto[]>): void {
    super.onParticipantsSelect(args);
    if (!args.value) {
      return;
    }
    let removedUserId = '';
    if (args.event?.removed?.length && (this.data.fillType === FillType.PLANNER || this.data.fillType === FillType.PLANNER_TASK)) {
      removedUserId = `/${args.event.removed[0][this.participants().primaryKey()]}`;
    }
    if (removedUserId.length > 0) {
      this.deleteParticipant(removedUserId);
    } else {
      this.saveParticipant();
    }
  }

  private saveParticipant(): void {
    this.api
      .post({
        url: `${this.controller}/participants/save/${this.id}`,
        postBody: this._participants.map((participant) => participant.entity_id),
        cancelableReq: this.$destroy
      })
      .pipe(takeUntil(this.$destroy))
      .subscribe((_result) => {
        this.noticeService.notice(this.tag('messages.add-Participant'));
      });
  }

  private deleteParticipant(removedUserId: string): void {
    let endPoint = '/participants-planner-task/delete/';
    if (this.data.fillType === FillType.PLANNER) {
      endPoint = '/participants-planner/delete/';
    }
    this.api
      .post({
        url: this.controller + endPoint + this.id + removedUserId,
        postBody: this.data?.childs?.length > 0,
        cancelableReq: this.$destroy
      })
      .pipe(takeUntil(this.$destroy))
      .subscribe((_result) => {
        this.noticeService.notice(this.tag('messages.remove-Participant'));
      });
  }

  onSaveFieldDescripion(fieldSaved: ModifiedFieldResponseDTO): void {
    this.onSavedField(fieldSaved);
    this.updatePartitionDescription(fieldSaved);
  }

  onSavedField(fieldSaved: ModifiedFieldResponseDTO) {
    if (this.dataSource.load) {
      if (this.dataSource.load.hasOwnProperty(fieldSaved.fieldName) && typeof this.dataSource.load[fieldSaved.fieldName] === typeof fieldSaved.modifiedValue) {
        this.dataSource.load[fieldSaved.fieldName] = fieldSaved.modifiedValue;
      }
    }
  }

  onSavedPlannerTask(fieldSaved: ModifiedFieldResponseDTO) {
    this.onSavedField(fieldSaved);
    this.timesheetService.setNewChangePlannerTask(fieldSaved.modifiedValue, this.id);
    this.cdr.detectChanges();
  }

  onAddedHistory(history: (ActivityHistoryEntity | DataMap)[]) {
    this.updatingHistory = true;
    if (!history) {
      return;
    }
    const historyValue = this.history();
    const gridSelect = historyValue?.gridSelect();
    if (gridSelect) {
      for (const item of history) {
        gridSelect.addValue(item, false);
      }
      gridSelect.valueGrid().data.sort((a, b) => {
        if (DateUtil.safe(a.lastModifiedDate).getTime() > DateUtil.safe(b.lastModifiedDate).getTime()) {
          return -1;
        }
        if (DateUtil.safe(a.lastModifiedDate).getTime() < DateUtil.safe(b.lastModifiedDate).getTime()) {
          return 1;
        }
        return 0;
      });
      gridSelect.valueGrid().notifyChanges();
    } else {
      if (historyValue.value == null || typeof historyValue.value === 'undefined') {
        historyValue.value = [];
      }
      for (const item of history) {
        historyValue.value.push(item as ActivityHistoryEntity);
      }
      historyValue.value.sort((a, b) => {
        if (DateUtil.safe(a.lastModifiedDate).getTime() > DateUtil.safe(b.lastModifiedDate).getTime()) {
          return -1;
        }
        if (DateUtil.safe(a.lastModifiedDate).getTime() < DateUtil.safe(b.lastModifiedDate).getTime()) {
          return 1;
        }
        return 0;
      });
      historyValue.detectChanges();
    }
    this.containerDetailNewSize = null;
    this.cdr.detectChanges();
    this.updatingHistory = false;
  }

  deleteComment(commentRowData: CommentEntity) {
    this.savingComment = true;
    const id = commentRowData.activityId ? commentRowData.activityId : this.id;
    this.api
      .post({ url: `${this.controller}/comments/delete/${id}`, cancelableReq: this.$destroy, postBody: commentRowData.id, options: null, handleFailure: false })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result) => {
          this.savingComment = false;
          if (!result) {
            this.comments().gridSelect().addValue(commentRowData, false);
            this.noticeService.notice(this.translate.instant('comment-deleted-false'));
          }
          this.noticeService.notice(this.translate.instant('comment-deleted-true'));
          if (result.history) {
            this.onAddedHistory(result.history);
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.noticeService.notice(this.translate.instant('comment-deleted-false'));
          this.comments().gridSelect().addValue(commentRowData, false);
          this.savingComment = false;
          this.cdr.detectChanges();
        }
      });
  }

  saveDocument(event: SaveDocumentEvt) {
    const documentEntity = event.entity;
    this.savingDocument = true;
    this.api
      .post({ url: `${this.controller}/documents/save/${this.id}/${documentEntity.id}`, postBody: null, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (result) => {
          documentEntity.id = result.itemId;
          if (result.createdDate) {
            documentEntity.lastModifiedDate = new Date(result.createdDate);
          }
          documentEntity.stage = result.stage;
          documentEntity.activityCommitmentTask = this.data.commitmentTask;
          documentEntity.activityId = this.id;
          if (!this.data.linked.documents) {
            this.data.linked.documents = [];
          }
          this.data.linked.documents.push(documentEntity);
          this.noticeService.notice('Documento guardado');
          if (result.history) {
            this.onAddedHistory(result.history);
          }
          this.savingDocument = false;
          event.grid.detectChanges();
        },
        error: () => {
          this.savingDocument = false;
        }
      });
  }

  deleteDocument(entity: DocumentEntity) {
    this.activityService
      .deleteDocumentService({
        activityId: entity.activityId,
        documentId: entity.id
      })
      .then((result) => {
        this.savingDocument = false;
        this.noticeService.notice(this.translate.instant('document-deleted-true'));
        if (result.history) {
          this.onAddedHistory(result.history);
        }
      })
      .catch(() => {
        this.noticeService.notice(this.translate.instant('document-deleted-false'));
        this.documents.gridSelect().addValue(document, false);
        this.savingDocument = false;
      });
  }

  openParentActivity(addChild = false) {
    switch (this.module) {
      case Module.PLANNER:
        this.menuService.navigate(`menu/planner/edit/${this.parentPlannerId}`, this.module);
        break;
      default:
        this.menuService.navigate(`menu/activities/${this.parentActivityImplementationId}${addChild ? '?isSubtaskPanelOpen=1' : ''}`, this.module || Module.ACTIVITY);
        break;
    }
  }

  addMoreSiblings() {
    this.dialogService.confirm('Para agregar más subtareas hermanas será enviado a la actividad padre. ¿Desea continuar?').then(() => {
      this.openParentActivity(true);
    });
  }

  moveTaskFrom() {
    this.enableSubtaskMove = true;
    this.cdr.detectChanges();
    this.subTasksMove().openDialog();
  }

  onSubtaskMoved(result: ModifiedFieldResponseDTO) {
    if (this.debug()) {
      console.log('Tasks moved.', result);
    }
    this.enableSubtaskMove = false;
    if (result?.modified) {
      this.onAddedHistory(result.history);
    }
    this.cdr.detectChanges();
  }

  addMoreChilds() {
    if (this.activityImplementationId) {
      this.openSubtaskPanel();
      this.openedFromSubtask = true;
      this.cdr.detectChanges();
    } else if (this.plannedImplementations?.length > 1) {
      // ¿A que implementación a la que desea agregar subtareas?
      this.dialogService.info(
        'Esta verificación cuenta con más de una implementación. Para agregar subtareas debe visitar el detalle de la implementación correspondiente.'
      );
    }
  }

  addMoreImplementations() {
    this.openImplementationPanel();
  }

  addMoreVerifications() {
    this.openVerificationPanel();
  }

  closeActivitiesPanel() {
    const dialogActivitiesMany = this.dialogActivitiesMany();
    if (dialogActivitiesMany) {
      dialogActivitiesMany.close();
    }
    this._currentActivitiesPanel = null;
    this.toggleActivitiesPanel(false);
  }

  private toggleActivitiesPanel(open?: boolean) {
    /**
     * ToDo: Hacer "dettach" del CDR de "activities-detail.component" para
     * evitar que el alta de actividades sufra bajas de performance
     * por culpa de la ventana padre
     **/
    if (typeof open === 'undefined') {
      open = !this._isActivitiesPanelOpen;
    }
    this._isActivitiesPanelOpen = open;
    if (this.isMobilDevice()) {
      this.cdr.detectChanges();
      const activityAddToModule = this.activityAddToModule();
      if (this.isActivitiesPanelOpen && activityAddToModule) {
        const linkedSource: ActivityLinkedSource = this.getLinkedSource();
        const linkedDataSource: ActivityLinkedDataSource = this.getLinkedDataSource();
        activityAddToModule.initialize(linkedSource, linkedDataSource);
      }
      if (this.activitiesPanelSettings) {
        this.activitiesPanelSettings.isPopEnabled = false;
      }
      const dialogActivitiesMany = this.dialogActivitiesMany();
      if (dialogActivitiesMany && open) {
        dialogActivitiesMany.open();
      }
    }
    this.isInnerOverlayShown = open;
  }

  private openSubtaskPanel() {
    if (!this.hasAddSubtaskAccess) {
      return;
    }
    this._currentActivitiesPanel = ActivitiesDetailAddPanelType.ADD_SUBTASK;
    this.toggleActivitiesPanel(true);
  }

  private openImplementationPanel() {
    if (!this.hasAddImplementationAccess) {
      return;
    }
    if (!this._currentActivitiesPanel) {
      this._currentActivitiesPanel = ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION;
      this.toggleActivitiesPanel(true);
    } else {
      this._currentActivitiesPanel = ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION;
      this.activitiesPanel.first.reset();
      this.cdr.detectChanges();
    }
  }

  private openVerificationPanel() {
    if (!this.hasAddVerificationAccess) {
      return;
    }
    if (!this._currentActivitiesPanel) {
      this._currentActivitiesPanel = ActivitiesDetailAddPanelType.ADD_VERIFICATION;
      this.toggleActivitiesPanel(true);
    } else {
      this._currentActivitiesPanel = ActivitiesDetailAddPanelType.ADD_VERIFICATION;
      this.activitiesPanel.first.reset();
      this.cdr.detectChanges();
    }
  }

  onPartitionAction(item: DropdownButtonItem) {
    this.executeActivitiesDetailAction(item.value, item.selected);
  }

  toggleMenu(value: string | number, selected: boolean) {
    this.executeActivitiesDetailAction(value, selected);
  }

  toggleActivityNavigator(value: string | number): void {
    if (!value) {
      return;
    }
    const pair = String(value).split('@'); // <-- "clave@módulo"
    const code = encodeUrlParameter(pair?.[0]);
    const activityUrl = `./../qms/${this.getLang()}/menu/activities/code/${code}`;
    window.location.href = activityUrl;
  }

  get isNextPrevAvailable(): boolean {
    return this.menuActivityNavigatorOptions.length === 0 && false /* ToDo: Hacer que el next/before funcionen */;
  }

  previousCode(): void {
    // ToDo: Soporte a "anterior"
  }

  nextCode(): void {
    // ToDo: Soporte a "siguiente"
  }

  private executeActivitiesDetailAction(value: string | number, selected: boolean) {
    switch (value) {
      case ActivitiesDetailAction.ADD_RESPONSIBLE:
        this.closeAllOpenedFieldsInEdit();
        this.addImplementerUser();
        break;
      case ActivitiesDetailAction.ADD_PARTICIPANT:
        this.participants().openDialog();
        break;
      case ActivitiesDetailAction.ADD_RELATION:
        this.moveTaskFrom();
        break;
      case ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION:
        this.addMoreImplementations();
        break;
      case ActivitiesDetailAddPanelType.ADD_VERIFICATION:
        this.addMoreVerifications();
        break;
      case CommonAction.FAVORITE:
        this.addFavorite();
        break;
      case CommonAction.CANCEL_RECURRENCE:
        this.cancelRecurrence(this.data.id);
        break;
      case CommonAction.ADD_DOCUMENT:
        this.documents.openDialog();
        break;
      case CommonAction.ADD_COMMENT:
        this.comments().openDialog();
        break;
      case CommonAction.COPY_LINK:
        DomUtil.shareLinkOrCopyToClipboard({
          url: `${this.navLang.basePath()}menu/activities/code/${this.data.code}`,
          title: this.titleService.getTitle()
        });
        this.noticeService.notice(this.tag('text-copied-clipboard'));
        break;
      case CommonAction.CAPTURE_TIME: {
        const lastComment = this.getLastComment();
        const plannedType = this.data.pendingRecordId !== null && typeof this.data.pendingRecordId !== 'undefined';
        const plannerRecordLocked = this.evaluatePlannerRecordLocked() && this.data.plannerTask?.plannerStatus === PlannerStatus.ACTIVE;
        const taskRecordLocked = this.evaluateTaskRecordLocked() && this.data.plannerTask?.plannerStatus === PlannerStatus.ACTIVE;
        this.timesheetService
          .openNew(
            plannedType ? PlannedType.PLANNED : PlannedType.UNPLANNED,
            this.data.pendingRecordId,
            lastComment,
            [this.data.code],
            new Date(),
            {
              clientId: this.data.plannerTask?.clientId || null,
              clientDescription: this.data.plannerTask?.clientName || null,
              plannerId: this.data.plannerTask?.plannerId || null,
              plannerDescription: this.data.plannerTask?.plannerName || null,
              activityId: +this.data.plannerTask?.value || null,
              activityDescription: this.data.plannerTask?.text || null,
              clientRecordLocked: !!(this.data.plannerTask && this.data.plannerTask?.clientId !== null),
              plannerRecordLocked: plannerRecordLocked,
              taskRecordLocked: taskRecordLocked,
              pendingRecordId: this.data.pendingRecordId || this.dataSource.load.pendingRecordId,
              activityPlannedId: this.data.recordId || null,
              activityPlannedCode: this.data.code || null,
              activityPlannedDescription: this.data.description || null,
              activityPlannedModule: this.data.module || null,
              hasEnableStopwatch: false,
              plannerStatus: this.data.plannerTask?.plannerStatus
            },
            true
          )
          .then(
            (value: TimesheetDto) => {
              if (value === null) {
                return;
              }
              if (this.debug()) {
                console.log('Opened new timesheet.', value);
              }
              this.data.actualHours += +(value.workedMinutes / 60.0);
              this.data.actualHours = NumberUtil.round(this.data.actualHours, 2);
              this.dataSource.load.actualHours = this.data.actualHours;
              this.mainForm.patchValue({ actualHours: this.dataSource.load.actualHours });
              const grid = this.timesheet?.grid();
              if (grid) {
                grid.refresh();
                const files = value?.fileData;
                if (files?.length > 0) {
                  this.showTimesheetFilesTab = true;
                  this.cdr.detectChanges();
                  this.filesTimesheetActivity().value.push(...files);
                  this.filesTimesheetActivity().valueChanged();
                }
              } else {
                this.cdr.detectChanges();
              }
            },
            (value: TimesheetDto) => {
              if (this.debug()) {
                console.log('Cancelled open new timesheet.', value);
              }
            }
          );
        break;
      }
      case ActivitiesDetailAddPanelType.AUTO_ASSIGN:
      case ActivitiesDetailAddPanelType.ASSIGN: {
        this.openAddDialog = true;
        this.cdr.detectChanges();
        const mapPreImplementers: number[] = this.data.preImplementerUsers?.map((a) => +a.value);
        const assigUsers = ActivitiesAssignComponent.getAssignUsers(
          this.data.defaultVerifierAuthor,
          this.data?.defaultCustomVerifier,
          this.data?.createdBy,
          value,
          mapPreImplementers
        );
        this.assignComponent()?.openDialog({
          addImplementer: assigUsers.addImplementer,
          addVerifier: assigUsers.addVerifier,
          addEstimatedHours: this.data?.plannedHours || null,
          addStartDate: DateUtil.today(),
          addEndDate: DateUtil.tomorrow(),
          addOrder: NumberUtil.isInteger(this.data?.activityOrder) ? this.data.activityOrder : null,
          isPlanned: false
        });
        break;
      }
      case CommonAction.BACK:
        this.navLang.back();
        break;
      case CommonAction.DUPLICATE:
        this.menuService.navigateBlank(`menu/activities/add-advanced?activityId=${this.id}`, this.module);
        break;
      default:
        if (this._isActivitiesPanelOpen) {
          this.closeActivitiesPanel();
        }
        this.activityInfo().attendByValue(value, selected, this.pendingRecordId);
        break;
    }
  }

  private getLastComment(): string {
    let comment = '';
    let historyResults = [];
    let commentsResults = [];
    const history = this.history();
    if (history && history.getValueGridCount() > 0) {
      const gridSelectValue = history.gridSelect();
      if (gridSelectValue) {
        historyResults = gridSelectValue
          .valueGrid()
          .data.filter((h) => h && h.createdBy === Session.getUserId() && h.activityHistoryType === ActivityHistoryType.PROGRESS_CHANGE);
      } else {
        historyResults = history.value.filter((h) => h && h.createdBy === Session.getUserId() && h.activityHistoryType === ActivityHistoryType.PROGRESS_CHANGE);
      }
    }
    const comments = this.comments();
    const gridSelect = comments?.gridSelect();
    if (gridSelect) {
      const valueGrid = comments.gridSelect().valueGrid();
      valueGrid?.data.sort((a, b) => (a.id > b.id ? -1 : 1));
      commentsResults = valueGrid?.data.filter((c) => c?.createdBy === Session.getUserId()) || [];
    }
    const valueComment = commentsResults?.[0] as CommentEntity;
    const historyElement = historyResults?.[0] as ActivityHistoryEntity;
    if (historyResults?.length > 0 && commentsResults?.length > 0) {
      comment = historyElement?.lastModifiedDate?.getTime() > valueComment?.lastModifiedDate?.getTime() ? historyElement?.userComment : valueComment?.description;
    } else {
      comment = historyResults?.length > 0 ? historyElement?.userComment : null;
      comment = commentsResults?.length > 0 ? valueComment?.description : comment;
    }
    return comment;
  }

  toggleMenuActivity(item: ActivityDropdownMenuItem) {
    switch (item.value) {
      case CommonAction.ADD_COMMENT:
        this.dialogService.input(`Agregar comentario a la actividad: ${item.activity.description}`, 'Guardar', 'Cancelar', item.activity.code).then((r: DialogResult) => {
          this.saveComment(
            {
              commentEntity: {
                id: null,
                description: r.inputValue,
                lastModifiedDate: new Date(),
                lastModifiedBy: Session.getUserName(),
                stage: '',
                createdBy: Session.getUserId()
              },
              grid: this.comments().gridSelect().valueGrid()
            },
            item.activity.recordId
          );
          item.activity.lastAction = r.inputValue;
        });
        break;
      default: {
        const data: ToVerifyServiceDTO = {
          verificationId: this.id,
          verifyAttender: item
        };
        this.toVerifyService.attend(data);
        break;
      }
    }
  }

  initFieldDisplay(config: FieldDisplay): void {
    config.url = `${this.controller}/save-field/`;
    config.entityId = this.id;
    this.refreshFieldDisplay(config);
  }

  refreshFieldDisplay(config: FieldDisplay): void {
    if (this.editableRules.hasOwnProperty(config.fieldName)) {
      config.editable = this.editMode && this.editableRules[config.fieldName];
      if (this.debug()) {
        console.log(`-> ${config.fieldName}: ${config.editable}`);
      }
    }
  }

  valueSaved(result: ModifiedFieldResponseDTO) {
    if (result.editionRules) {
      this.setEditionRules(result.editionRules);
    }
    if (result.loggedUserRole) {
      this.data.loggedUserRole = result.loggedUserRole;
    }
    if (result.taskStatus) {
      this.data.taskStatus = result.taskStatus;
    }
    if (result.status) {
      this.data.status = result.status;
    }
  }

  get addSiblingsAvailable(): boolean {
    return this.isParentVerifier && this.parentActivityImplementationId != null;
  }

  get hasAddSubtaskAccess(): boolean {
    const isMoreChildsAvailable = this.isMoreChildsAvailable();
    if (typeof isMoreChildsAvailable !== 'undefined') {
      return isMoreChildsAvailable;
    }
    return (
      this.activityImplementationId &&
      this.linkedSource &&
      (this.isVerifier ||
        this.isTrustedImplementer ||
        this.isAddChildOverride ||
        this.isServiceEditor ||
        this.isAddSubtaskByDepartmentAvailable ||
        this.isSuperVerifier ||
        this.isAdmin)
    );
  }

  private get hasAddImplementationAccess(): boolean {
    return this.menuOptionsAvailable.indexOf(ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION) !== -1;
  }

  private get hasAddVerificationAccess(): boolean {
    return this.menuOptionsAvailable.indexOf(ActivitiesDetailAddPanelType.ADD_VERIFICATION) !== -1;
  }

  get hasAddDocumentAccess(): boolean {
    return this.menuOptionsAvailable.indexOf(CommonAction.ADD_DOCUMENT) !== -1;
  }

  get hasTimesheetAccess(): boolean {
    return this.menuOptionsAvailable.indexOf(CommonAction.CAPTURE_TIME) !== -1;
  }

  get isAddSubtaskByDepartmentAvailable(): boolean {
    return this._addSubtaskByDepartment && +this.businessUnitDepartment.value === Session.getBusinessUnitDepartmentId();
  }

  get isVerifier(): boolean {
    const isVerifier = this.data && EnumUtil.getName(ActivityRole, this.data.loggedUserRole) === 'VERIFIER';
    return isVerifier;
  }

  get isSuperVerifier(): boolean {
    const isVerifier = this.data && EnumUtil.getName(ActivityRole, this.data.loggedUserRole) === 'SUPER_VERIFIER';
    return isVerifier;
  }

  get isAdmin(): boolean {
    const result = this.data && EnumUtil.getName(ActivityRole, this.data.loggedUserRole) === 'ADMIN';
    return result;
  }

  get isCreator(): boolean {
    const createdBy = this.data?.createdBy === Session.getUserId();
    return createdBy;
  }

  get isTrustedImplementer(): boolean {
    return (!this.data?.verifier && this.isImplementer) || (this.isImplementer && this.module === Module.PLANNER && this.hasChilds);
  }

  get isServiceEditor(): boolean {
    const isVerifier = this.data && EnumUtil.getName(ActivityRole, this.data.loggedUserRole) === 'SERVICE';
    return isVerifier;
  }

  get isAddChildOverride(): boolean {
    const addChildOverride = this.addChildOverride();
    if (typeof addChildOverride === 'boolean') {
      return addChildOverride;
    }
    return false;
  }

  get isImplementer(): boolean {
    return this._isImplementer;
  }

  get hasChilds(): boolean {
    return this._hasChilds;
  }

  get isParentVerifier(): boolean {
    const isVerifier = this.data && EnumUtil.getName(ActivityRole, this.data.loggedUserRoleParentActivityId) === 'VERIFIER';
    return isVerifier;
  }

  updateActivity(val: FieldDisplayEvt) {
    if (!this.initialized) {
      return;
    }
    this.setActivityContraints();
    if (val.fieldName != null) {
      const fieldName = val.fieldName;
      const value = val.value;
      this.data[fieldName] = value;
      switch (fieldName) {
        case 'implementation':
          if (DateUtil.safe(value) > this.data.verification) {
            if (!this.hiddenRules.anticipationAttendDays && DateUtil.safe(value) > this.data.reminder) {
              this.showImplConfirm = true;
              this.confirmLabel = this.translate.instant('confirmChangeDatesMessage');
            } else {
              this.showImplConfirm = true;
              this.confirmLabel = this.translate.instant('confirmChangeVerificationMessage');
            }
          } else {
            this.showImplConfirm = false;
          }
          break;
        case 'reminder':
          if (DateUtil.safe(value) > this.data.verification) {
            this.showImplConfirm = true;
            this.confirmLabel = this.translate.instant('confirmChangeVerificationMessage');
          } else {
            this.showImplConfirm = false;
          }
          break;
        case 'verifier':
          if (this.mainForm?.controls.verifier?.value !== value) {
            const data = {};
            data[fieldName] = value;
            this.mainForm.patchValue(data);
          }
          this.implementersCache = [];
          for (const user of this.data.implementerUsers) {
            this.implementersCache.push(user.value);
          }
          break;
      }
      this.cdr.detectChanges();
    }
  }

  updateImplementer(val: FieldDisplayEvt<TextHasUserValue | number>, implementerUser: TextHasUserValue) {
    if (val.fieldName != null && typeof val.oldValue === 'undefined') {
      this.deleteImplementer({
        mail: null,
        code: null,
        account: null,
        text: null,
        value: null
      });
      let value: number;
      if (typeof val === 'number') {
        value = val;
      } else if (isObject(val) && typeof val.value === 'number') {
        value = val.value;
      } else {
        throw Error(`Invalid value at ${val}`);
      }
      if (value && implementerUser.value && implementerUser.value !== value) {
        this.addImplementerUser(val.value as TextHasUserValue);
      }
    }
  }

  undoAddNewPreImplementer(_val: FieldDisplayEvt<TextHasUserValue | number>, _preImplementerUser: FieldDisplayComponent) {
    _preImplementerUser.resetDefaultValues();
    this.cdr.detectChanges();
  }

  updatePreImplementer(val: FieldDisplayEvt<TextHasUserValue | number>, preImplementerUser: TextHasUserValue) {
    if (val.fieldName != null && typeof val.oldValue === 'undefined') {
      this.deletePreImplementer({
        mail: null,
        code: null,
        account: null,
        text: null,
        value: null
      });
      let value: number;
      if (typeof val === 'number') {
        value = val;
      } else if (isObject(val) && typeof val.value === 'number') {
        value = val.value;
      } else {
        throw Error(`Invalid value at ${val}`);
      }
      if (value && preImplementerUser?.value && preImplementerUser.value !== value) {
        this.addPreImplementerUser(val.value as TextHasUserValue);
      }
    }
  }

  savedImplementer(val: ModifiedFieldResponseDTO<UserRefEntity[]>) {
    const prevValues: UserRefEntity[] = val.previousValue;
    if (prevValues?.length) {
      for (const prevValue of prevValues) {
        this.deleteImplementer({
          mail: prevValue.correo,
          code: prevValue.code,
          account: prevValue.account,
          text: prevValue.description,
          value: prevValue.id
        });
      }
    }
    const modifiedValues: UserRefEntity[] = val.modifiedValue;
    for (const modifiedValue of modifiedValues) {
      this.addImplementerUser({
        mail: modifiedValue.correo,
        code: modifiedValue.code,
        account: modifiedValue.account,
        text: modifiedValue.description,
        value: modifiedValue.id
      });
    }
  }

  savedPreImplementer(val: ModifiedFieldResponseDTO<UserRefEntity>) {
    const prevValue = val.previousValue;
    if (prevValue !== null && typeof prevValue !== 'undefined') {
      this.deletePreImplementer({
        mail: prevValue.correo,
        code: prevValue.code,
        account: prevValue.account,
        text: prevValue.description,
        value: prevValue.id
      });
    }
    this.addPreImplementerUser({
      mail: val.modifiedValue.correo,
      code: val.modifiedValue.code,
      account: val.modifiedValue.account,
      text: val.modifiedValue.description,
      value: val.modifiedValue.id
    });
  }

  invalidFill(value: string, load: ActivityLoadDto) {
    if (value === CommonAction.EDIT || value === CommonAction.EDIT_REQUEST) {
      return true;
    }
    if (value !== ImplementAction.FILL || !load.formPending || load.formPending.hasPending) {
      return false;
    }
    if (load.formPending.hasStarted) {
      return true;
    }
    if (load.formPending.isAuthor) {
      return false;
    }
    return true;
  }

  activityUpdated(result: AttendResult) {
    let load = result.load;
    if (!load) {
      if (!result.changes) {
        return;
      }
      load = result.changes as ActivityLoadDto;
    }
    if (result.hiddenRules) {
      this.setHiddenRules(result.hiddenRules, null);
    }
    if (result.disabledFields) {
      this.setDisabledFields(result.disabledFields);
    }
    if (result.editionRules) {
      this.setEditionRules(result.editionRules);
      this.refreshFieldDisplays(['refresh']);
    }
    if (result.implementers) {
      this.implementers = result.implementers;
    }
    if (result.verifiers) {
      this.verifiers = result.verifiers;
    }
    if (result.implementationId && result.implementationId === this.data.id) {
      const implChanges = result.implementationChanges as ActivityLoadDto;
      this.data = { ...this.data, ...implChanges };
    } else {
      this.data = { ...this.data, ...load };
    }
    this.setActivityLoad(this.data);
    this.menuOptionsAvailable = [...this.defaultMenuOptionsAvailable];
    const activityInfo = this.activityInfo();
    if (load.availableActions && activityInfo?.pendingMenuActions) {
      const actions = this.getActions(activityInfo.pendingMenuActions, load.availableActions);
      for (const value of actions) {
        if (this.invalidFill(value, load)) {
          continue;
        }
        this.addMenuOption([value]);
        const dropdownmenu = this.dropdownmenu();
        if (dropdownmenu) {
          const iconName = (ACTIVITY_ACTIONS[value] || { iconName: 'check' }).iconName;
          dropdownmenu.pushMenuOption({
            text: this.i18n?.menuOption[value] || value,
            value: value,
            iconName: typeof iconName === 'undefined' ? 'check' : iconName // <-- El valor NULL es valido para poder usar un "checkbox"
          });
        }
      }
    }
    console.warn('got new update activity', load);
  }

  private getActions(menuActions: DropdownMenuItem[], requiredActions: string[]): string[] {
    const actions = menuActions.filter((action) => requiredActions.indexOf(action.value as string) !== -1).map((action) => action.value as string);
    for (const action1 of requiredActions.filter((action) => actions.indexOf(action) === -1)) {
      actions.push(action1);
    }
    return actions;
  }

  disabledDateMessage(event: FieldDisplayEvt) {
    console.log(event);
    this.noticeService.notice('Para habilitar fechas posteriores debe modificar primero la fecha de verificación');
  }

  menuActivateBy(): DropdownMenuActivable[] {
    let result: DropdownMenuActivable[] = null;
    if (this.documents) {
      if (!result) {
        result = [];
      }
      result.push(this.documents);
    }
    const comments = this.comments();
    if (comments) {
      if (!result) {
        result = [];
      }
      result.push(comments);
    }
    return result;
  }

  return(): void {
    if (this.navigateBack()) {
      if (window.history.length > 1) {
        this.navLang.back();
      } else {
        this.menuService.navigate('pendings');
      }
    }
    this.returned.emit(null);
  }

  get activitiesPanelSettings(): ActivityAddManyConfiguration {
    if (!this.isActivitiesPanelOpen) {
      return null;
    }
    switch (this._currentActivitiesPanel) {
      case ActivitiesDetailAddPanelType.ADD_SUBTASK:
        return this.getSubtaskSettings();
      case ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION:
        return this.getProgramSubtaskSettings(CommitmentTask.IMPLEMENTATION);
      case ActivitiesDetailAddPanelType.ADD_VERIFICATION:
        return this.getProgramSubtaskSettings(CommitmentTask.VERIFICATION);
      default:
        return null;
    }
  }

  private get hasAssignAccess(): boolean {
    const servicesAssign = [ProfileServices.ACTIVITY_CREATOR.toString(), ProfileServices.ACTIVITY_BULK_CREATOR.toString(), ProfileServices.ACTIVITY_MANAGER.toString()];
    return this.isAdmin || Session.getServices().some((s) => servicesAssign.includes(s));
  }

  private refreshPartitionAccess(): void {
    for (const t of this.partitionItems) {
      t.hidden = true;
      if (t.value === ActivitiesDetailAddPanelType.ADD_VERIFICATION && this.hasAddVerificationAccess) {
        t.hidden = false;
      } else if (t.value === ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION && this.hasAddImplementationAccess) {
        t.hidden = false;
      } else if (
        (t.value === ActivitiesDetailAddPanelType.AUTO_ASSIGN || t.value === ActivitiesDetailAddPanelType.ASSIGN) &&
        this.data.status !== ActivityStatus.MAIN_CLOSED &&
        this.hasAssignAccess
      ) {
        t.hidden = false;
      }
    }

    for (const op of this.menuOptionsAvailable) {
      if (
        (op === ActivitiesDetailAddPanelType.AUTO_ASSIGN.toString() || op === ActivitiesDetailAddPanelType.ASSIGN.toString()) &&
        this.data.status !== ActivityStatus.MAIN_CLOSED &&
        !this.hasAssignAccess
      ) {
        let index = this.menuOptionsAvailable.indexOf(ActivitiesDetailAddPanelType.AUTO_ASSIGN);
        this.menuOptionsAvailable.splice(index, 1);
        index = this.menuOptionsAvailable.indexOf(ActivitiesDetailAddPanelType.ASSIGN);
        this.menuOptionsAvailable.splice(index, 1);
      }
    }

    const assignComponent = this.assignComponent();
    if (!this.constraints?.verificationReqOnPlanning && !this.constraints?.addVerificationAvailable && assignComponent) {
      assignComponent.disableVerification();
    }
  }
  getHistoryViewMode(): HistoryViewMode {
    if (this.showSidePaneSmall || (this.data && (this.data?.fillType === FillType.PLANNER || this.data?.fillType === FillType.PLANNER_TASK))) {
      return HistoryViewMode.LIST;
    }
    return HistoryViewMode.GRID;
  }

  private getSubtaskSettings(): ActivityAddManyConfiguration {
    let module = this.module;
    const isRowModuleReplacementEnabled = this.isRowModuleReplacementEnabled();
    if (this.module === Module.PLANNER && !isRowModuleReplacementEnabled) {
      module = Module.ACTIVITY;
    }
    this._addSubtaskSettings.setDefaultBusinessUnitDepartment(this.businessUnitDepartment);
    this._addSubtaskSettings.setCustomImplementationLabel(this.customImplementationLabel());
    this._addSubtaskSettings.setDefaultPlannerStartDatesEntity(module === Module.PLANNER ? this.plannerStartDatesEntity : null);
    this._addSubtaskSettings.setIsSaveButtonEnabled(this.isSaveButtonEnabled);
    this._addSubtaskSettings.setIsParticipantsAvailable(module === Module.PLANNER);
    this._addSubtaskSettings.setModule(module);
    this._addSubtaskSettings.setIsRowModuleReplacementEnabled(isRowModuleReplacementEnabled);
    this._addSubtaskSettings.setDefaultRowModuleReplacementDeep(this.defaultRowModuleReplacementDeep());
    this._addSubtaskSettings.setLinkedSource(this.linkedSource);
    this._addSubtaskSettings.setParentActivityId(this.recurrenceId);
    this._addSubtaskSettings.setParentActivityImplementationId(this.activityImplementationId);
    this._addSubtaskSettings.setParentActivityVerificationId(this.activityVerificationId);
    this._addSubtaskSettings.setParentTreeActivityId(this.parentTreeActivityId || this.recurrenceId);
    return this._addSubtaskSettings;
  }

  private getProgramSubtaskSettings(commitmentTask: CommitmentTask): ActivityAddManyConfiguration {
    const flexibleCommitmentTaskAvailable: CommitmentTask[] = [];
    if (this.hasAddImplementationAccess) {
      flexibleCommitmentTaskAvailable.push(CommitmentTask.IMPLEMENTATION);
    }
    if (this.hasAddVerificationAccess) {
      flexibleCommitmentTaskAvailable.push(CommitmentTask.VERIFICATION);
    }
    if (!flexibleCommitmentTaskAvailable.length) {
      // Para usar `flexibleCommitmentTask = true` se requiere al menos un valor del arreglo
      return null;
    }
    this._addProgramSubtaskSettings.setDefaultTypeControllerImplementation('/data-source/implementation/type/');
    this._addProgramSubtaskSettings.setDefaultTypeControllerVerification('/data-source/verification/type/');
    this._addProgramSubtaskSettings.setCommitmentTask(commitmentTask);
    this._addProgramSubtaskSettings.setIsFlexibleCommitmentTask(true);
    this._addProgramSubtaskSettings.setFlexibleCommitmentTaskAvailable(flexibleCommitmentTaskAvailable);
    this._addProgramSubtaskSettings.setDefaultTypeId(this.data.typeId);
    this._addProgramSubtaskSettings.setDefaultBusinessUnitDepartment(this.businessUnitDepartment);
    this._addProgramSubtaskSettings.setCustomImplementationLabel(this.customImplementationLabel());
    this._addProgramSubtaskSettings.setLinkedSource(this.linkedSource);
    this._addProgramSubtaskSettings.setRecurrenceId(this.recurrenceId);
    this._addProgramSubtaskSettings.setParentTreeActivityId(this.parentTreeActivityId);
    if (this.module === Module.PLANNER && !this.isRowModuleReplacementEnabled()) {
      this._addProgramSubtaskSettings.setModule(Module.ACTIVITY);
    } else {
      this._addProgramSubtaskSettings.setModule(this.module);
    }
    switch (commitmentTask) {
      case CommitmentTask.IMPLEMENTATION:
        this._addProgramSubtaskSettings.setIsVerifierRemoved(true);
        this._addProgramSubtaskSettings.setDefaultTypeController(this._addProgramSubtaskSettings.defaultTypeControllerImplementation);
        break;
      case CommitmentTask.VERIFICATION:
        this._addProgramSubtaskSettings.setIsVerifierRemoved(false);
        this._addProgramSubtaskSettings.setDefaultTypeController(this._addProgramSubtaskSettings.defaultTypeControllerVerification);
        break;
      case CommitmentTask.PROGRAM:
        this._addProgramSubtaskSettings.setIsVerifierRemoved(false);
        this._addProgramSubtaskSettings.setDefaultTypeController(null);
        break;
    }
    return this._addProgramSubtaskSettings;
  }

  get isSaveButtonEnabled(): boolean {
    if (!this._isActivitiesPanelOpen) {
      return false;
    }
    switch (this._currentActivitiesPanel) {
      case ActivitiesDetailAddPanelType.ADD_SUBTASK:
        return this.hasAddSubtaskAccess && this.activityImplementationId && !!this.linkedSource;
      case ActivitiesDetailAddPanelType.ADD_IMPLEMENTATION:
        return this.hasAddImplementationAccess;
      case ActivitiesDetailAddPanelType.ADD_VERIFICATION:
        return this.hasAddVerificationAccess;
      default:
        return false;
    }
  }

  get isActivitiesPanelOpen(): boolean {
    return this._isActivitiesPanelOpen && this._currentActivitiesPanel !== null;
  }

  public setSelectedTabByParam(): void {
    const selectedTab = this.navLang.queryParam('tabSelected');
    if (selectedTab) {
      this.tabsWrapper().selectedIndex = +selectedTab;
    }
  }

  get relationshipCount(): number {
    return this._relationshipCount;
  }

  get isPartitionButtonAvailable(): boolean {
    const isPlanner = this.module === Module.PLANNER;
    if (isPlanner) {
      // EL módulo de PLANNER no tiene particiones
      return false;
    }
    return this.commitmentTask === CommitmentTask.PROGRAM && this.belongSeries === 0;
  }

  public hasDynamicFields(hasDynamicField): void {
    this.showDynamicFieldLoad = hasDynamicField;
    this.cdr.detectChanges();
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.BACK:
        this.return();
        break;
    }
  }

  public getFilesImages(): { id: number; imageUrl: string }[] {
    const filesCarousel: { id: number; imageUrl: string; description: string }[] = [];
    const validFileExtForCarousel = ['JPEG', 'JPG', 'PNG', 'BMP', 'WBMP', 'GIF'];
    const files = this.files();
    if (files) {
      for (const item of files.value) {
        if (item.id && item.extension && validFileExtForCarousel.includes(item.extension.toUpperCase())) {
          const descriptionFile = item.commentFile ? `${item.description} - ${item.commentFile}` : item.description;
          filesCarousel.push({ id: item.id, imageUrl: `./../rest/files/${item.id}`, description: descriptionFile });
        }
      }
    }
    return filesCarousel;
  }

  public showImageEvidences(): void {
    this.isOpenImageZone = !this.isOpenImageZone;
    this.cdr.detectChanges();
  }

  public openImageEvidence(currentSlideIndex: number): void {
    this.openPreviewerImages = true;
    this.cdr.detectChanges();
    const previewerImages = this.previewerImages();
    previewerImages.imageToShow = currentSlideIndex;
    previewerImages.openDialog();
  }

  private _addPlanSave(data: ActivityAddPlanSaveDto): void {
    this._cancelAddPlanSave.next(true);
    this.loader.show();
    this.api
      .post({ url: `${this.controller}/add-plan/save`, cancelableReq: this.$destroy, postBody: data })
      .pipe(takeUntil(this.$destroy))
      .pipe(takeUntil(this._cancelAddPlanSave))
      .subscribe({
        next: (results: ReportedActivities[]) => {
          const verificationReplaceOld = results?.find((r) => r.commitmentTask === CommitmentTask.VERIFICATION && r.replacingOldVerification) || null;
          if (verificationReplaceOld !== null) {
            this.dialogService.info(this.tag('activity-saveMessageNoVerification').replace('{verificationCode}', verificationReplaceOld.verificationCode));
          } else {
            this.dialogService.info(this.tag('activity-saveMessage'));
          }
          this.refreshData();
          this.openAddDialog = false;
          this.assignComponent().onCloseAssign();
          this.loader.hide();
        },
        error: (e) => {
          this.loader.hide();
          this.assignComponent().onCloseAssign();
          this.openAddDialog = false;
          if (e && e.status === 500 && e.error?.includes('[NO_RELATED_IMPLEMENTATIONS]')) {
            this.dialogService.info(this.translate.instant('root.common.error.noRelatedImplementations'));
          } else {
            ErrorHandling.notifyError(e, this.navLang);
          }
        }
      });
  }

  private getConfirmMessageAssign(result: any, userLabel: string): string {
    return this.translate.instant('confirm-same-user-autoassign', {
      count: result.length,
      user: userLabel?.trim?.(),
      start: this.datePipe.transform(result[0].implementation, this.dateFormat, this.getTimezone(), this.getLang()).toString(),
      end: this.datePipe.transform(result[result.length - 1].reminder, this.dateFormat, this.getTimezone(), this.getLang()).toString(),
      newStart: this.datePipe.transform(DateUtil.today(), this.dateFormat, this.getTimezone(), this.getLang()).toString(),
      newEnd: this.datePipe.transform(DateUtil.tomorrow(), this.dateFormat, this.getTimezone(), this.getLang()).toString()
    });
  }

  onAssign(dto: ActivitiesAssignDto) {
    this.userHasAnotherActivity(dto.addImplementer).then((result) => {
      if (!result || result.length === 0) {
        this.assignActivity(dto);
      } else {
        const userLabel = this.implementers.find((u) => u.value === dto.addImplementer);
        let message = '';
        if (!userLabel) {
          const userLabel = this.preImplementers.find((u) => u.value === dto.addImplementer);
          message = this.getConfirmMessageAssign(result, userLabel.text);
        } else {
          message = this.getConfirmMessageAssign(result, userLabel.text);
        }
        this.dialogService.confirm(message).then(
          () => {
            this.assignActivity(dto);
          },
          () => {}
        );
      }
    });
  }

  private assignActivity(dtoAssign: ActivitiesAssignDto): void {
    this._assignSave.next(dtoAssign);
  }

  private _assignActivity(dtoAssign: ActivitiesAssignDto): void {
    const dto = this._getEntityData(this.data);
    this._addPlanSave(
      ActivitiesAssignComponent.getEntityData(
        {
          dto: dto,
          assign: dtoAssign,
          verificationReqOnPlanning: this.constraints?.verificationReqOnPlanning,
          addVerificationAvailable: this.constraints?.addVerificationAvailable
        },
        this.data.recordId
      )
    );
  }

  private userHasAnotherActivity(userId: number): Promise<any> {
    return new Promise<any>((implementations) => {
      const url = getActiveImplementationsController(this.id, userId, this.module);
      this.api
        .get({ url: url, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe((result: any[]) => {
          implementations(result);
        });
    });
  }

  private _getEntityData(activity: ActivityPendingDto): ActivityAddPlanSaveDto {
    const data: ActivityAddPlanSaveDto = {
      activities: []
    };
    const loggedUserId = Session.getUserId();
    const endDate = DateUtil.tomorrow(); // <-- La diferencia entre el inicio y el fin debe ser de al menos 1 día
    //TODO: Quitar valor harcodeado de 9,  agregar campo para configurar horas por día
    if (activity.plannedHours / 9 > 1) {
      endDate.setDate(endDate.getDate() + Math.ceil(activity.plannedHours / 9));
    }
    let localVerifierId: number[];
    if (activity.defaultVerifierAuthor === VerifierAuthor.CUSTOM_USER) {
      localVerifierId = [activity.defaultCustomVerifier];
    } else {
      localVerifierId = loggedUserId !== activity.createdBy ? [activity.createdBy] : null;
    }
    const r: IActivitiesAddPlanRow = {
      entity_id: activity.recordId,
      activityId: activity.recordId,
      activityTypeId: activity.typeId,
      localResponsibleId: Array.from([loggedUserId]),
      localPreResponsibleId: [],
      localPlannedHours: activity.plannedHours,
      localVerifierId: localVerifierId,
      localStartDate: DateUtil.today(),
      localEndDate: endDate,
      entity_verificationReqOnPlanning: this.constraints.verificationReqOnPlanning ? 1 : 0,
      entity_addVerificationAvailable: this.constraints.addVerificationAvailable ? 1 : 0,
      isPlanned: 0,
      localActivityOrder: '0',
      orderSelected: 0
    };
    data.activities.push(r);
    return data;
  }

  private getTimesheetActivityFiles(activityPlannedId: number): void {
    this.api
      .get({ url: `timesheet/timesheet-activity-files/data-source/${activityPlannedId}`, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe((response: IFileData[]) => {
        if (response && response?.length > 0) {
          this.showTimesheetFilesTab = true;
          this.mainForm.patchValue({
            filesTimesheetActivity: response
          });
        } else {
          this.showTimesheetFilesTab = false;
        }
        this.cdr.detectChanges();
      });
  }

  public selectedIndexChange(index: number): void {
    this._selectedTab = index;
  }

  public onSlideEvidenceChange(e: ISlideEventArgs): void {
    this.previewerImages().titleDialog = e.slide.nativeElement.querySelector('.image-evidence').dataset.imagename;
    this.cdr.detectChanges();
  }

  public onSlideClosed() {
    this.openPreviewerImages = false;
    this.cdr.detectChanges();
  }

  // ToDo: Agregar caché! este código se ejecuta demasiado.
  public loadTooltipInfo(userInfo: { userId: number; tooltipId: number }): Partial<ActivitiesOwnerAvatarTooltip> {
    const user = this.data.implementerUsers.find((u) => u.value === userInfo.userId && u.activityId === userInfo.tooltipId);
    if (user?.activityId) {
      const partition = this.partitions?.find((p) => p.id === user.activityId);
      if (partition) {
        return {
          activityId: user.activityId,
          code: partition.code,
          resolution: this.resolutions.find((r) => r.value === partition.resolutionId)?.text || '-',
          userName: user.text.trim(),
          commitmentDate: this.datePipe.transform(partition.commitmentDate, 'dd/MM/yyyy'),
          displayTopValue: `${DateUtil.format(partition.commitmentDate, 'DD-MMM')}`,
          displayBottomValue: `${partition.code.replace(/^.+?(IMP|VER)-[0]*/, '$1-')}`,
          plannedHours: partition.plannedHours || 0,
          actualHours: +partition.actualHours?.toFixed(2) || 0,
          commitmentTask: partition.commitmentTask
        };
      }
    }
    if (this.commitmentTask === CommitmentTask.PROGRAM) {
      if (this.belongSeries === 1) {
        return {};
      }
      return null;
    }
    return {};
  }

  public openDetail(activityId: number, module: string) {
    switch (module) {
      case Module.PLANNER:
        this.menuService.navigate(`menu/planner/task-detail/${activityId}`);
        break;
      default:
        this.menuService.navigate(`menu/activities/${activityId}`);
        break;
    }
  }

  public toggleMode(e: ToggleFieldDisplay) {
    const containerDetail = this.containerDetail();
    if (!this.isMobilDevice() && containerDetail && e.toggledField) {
      const surplusSize = containerDetail.nativeElement.clientHeight - e.toggledField.nativeElement.clientHeight;
      const fieldClientRects = e.toggledField.nativeElement.getClientRects();
      const availableResizeContainer =
        fieldClientRects[0].y + (fieldClientRects[0].height - 125) > this.containerForm().nativeElement.clientHeight ||
        (this.files().value.length > 0 && !this.filesCollapsed);
      if (e.toggle && availableResizeContainer) {
        this.containerDetailNewSize = containerDetail.nativeElement.clientHeight + surplusSize;
      } else {
        this.containerDetailNewSize = null;
      }
      this.cdr.detectChanges();
    }
  }

  undoPlannerTaskChanges() {
    this.plannerTaskField().resetDefaultValues();
  }

  public onOpeningStackEditFrame(event: boolean) {
    const dynamic = this.dynamic();
    if (event && dynamic) {
      //Campos dinamicos
      for (const field1 of dynamic.dynamicFieldRead().filter((field) => field.editMode === true)) {
        field1.editMode = false;
      }
      //Campos configurados(estaticos) de detalle
      this.closeAllOpenedFieldsInEdit();
    }
  }

  private closeAllOpenedFieldsInEdit(): void {
    const fieldDisplay = this.fieldDisplay();
    if (!fieldDisplay) {
      return;
    }
    for (const field1 of fieldDisplay.filter((field) => field.editMode === true)) {
      field1.editMode = false;
    }
  }

  public onToggleMaximizeTimesheet(event: BnextGridWindowPosition) {
    switch (event) {
      case BnextGridWindowPosition.FIXED:
        this.timesheetMaximized = true;
        break;
      case BnextGridWindowPosition.STATIC:
        this.timesheetMaximized = false;
        break;
      default:
        console.warn(`Window position not supported ${event}`);
        break;
    }
    this.cdr.detectChanges();
  }

  showSaveOptions(): boolean {
    if (!this.data) {
      return false;
    }
    if (this.data.commitmentTask) {
      if (this.data.commitmentTask === CommitmentTask.IMPLEMENTATION || this.data.commitmentTask === CommitmentTask.VERIFICATION) {
        return true;
      }
      return (this.data.partitions && this.data.partitions.length > 0) || this.data.implementationCount > 0;
    }
    return false;
  }

  private getLinkedSource(): ActivityLinkedSource {
    return {
      typeId: this.activitiesPanelSettings.defaultTypeId,
      businessUnitDepartmentId: +this.activitiesPanelSettings.defaultBusinessUnitDepartment.value,
      businessUnitId: this.activitiesPanelSettings.defaultBusinessUnitDepartment.businessUnitId,
      module: this.activitiesPanelSettings.module,
      typeControllerImp: this.activitiesPanelSettings.defaultTypeControllerImplementation,
      typeControllerVer: this.activitiesPanelSettings.defaultTypeControllerVerification,
      typeController: this.activitiesPanelSettings.defaultTypeController,
      commitmentTask: this.activitiesPanelSettings.commitmentTask,
      recurrenceId: this.activitiesPanelSettings.recurrenceId,
      groupName: this.activitiesPanelSettings.defaultGroupName,
      parentPlannerId: this.activitiesPanelSettings.parentPlannerId,
      parentActivityId: this.activitiesPanelSettings.parentActivityId,
      parentActivityImplementationId: this.activitiesPanelSettings.parentActivityImplementationId,
      parentTreeActivityId: this.activitiesPanelSettings.parentTreeActivityId
    };
  }

  private getLinkedDataSource(): ActivityLinkedDataSource {
    return {};
  }

  loadRelationContentTab(): void {
    this.loadRelationshipContent = true;
    this.cdr.detectChanges();
  }

  private refreshWorkedMinutes(): void {
    this.api.post({ url: `timesheet/timesheet-worked-time-info/${this.id}`, cancelableReq: this.$destroy, postBody: {} }).subscribe((response) => {
      if (response) {
        const { workedHours } = response;
        this.data.actualHours = NumberUtil.round(workedHours, 2);
        this.mainForm.patchValue({ actualHours: this.data.actualHours });
      }
    });
  }

  loadTimesheetContentTab(): void {
    this.loadTimesheetContent = true;
    this.cdr.detectChanges();
    this.getTimesheetActivityFiles(this.id);
    if (this.timesheet) {
      this.timesheetService.onModified.pipe(takeUntil(this.$destroy)).subscribe(() => {
        this.timesheet
          .grid()
          .refreshAsync()
          .then(() => {
            this.getTimesheetActivityFiles(this.id);
            this.refreshWorkedMinutes();
            this.cdr.detectChanges();
          });
      });
    }
  }

  private evaluatePlannerRecordLocked(): boolean {
    if (!this.data.plannerTask) {
      return false;
    }
    return !!this.data.plannerTask?.plannerId;
  }

  private evaluateTaskRecordLocked(): boolean {
    if (!this.data.plannerTask) {
      return false;
    }
    if (!this.data.plannerTask?.plannerId) {
      return false;
    }
    return !!this.data.plannerTask?.value;
  }

  public onRelationShipAdded(): void {
    this._relationshipCount++;
  }

  public onRelatioShipDeleted(): void {
    this._relationshipCount--;
  }

  private updatePartitionDescription(fieldSaved: ModifiedFieldResponseDTO): void {
    if (this.partitions?.length > 0) {
      const partitionsCopy = [...this.partitions];
      let notRefresh = false;
      switch (fieldSaved.optionalData.opcionSelected) {
        case ActivitiesDetailUpdateAction.UPDATE_ALL:
          for (const partition of partitionsCopy) {
            if (partition.hasOwnProperty(fieldSaved.fieldName)) {
              partition[fieldSaved.fieldName] = fieldSaved.modifiedValue;
            }
          }
          break;
        case ActivitiesDetailUpdateAction.UPDATE_COINCIDENCES:
          for (const partition of partitionsCopy) {
            if (partition.hasOwnProperty(fieldSaved.fieldName) && partition.description === fieldSaved.previousValue) {
              partition[fieldSaved.fieldName] = fieldSaved.modifiedValue;
            }
          }
          break;
        default:
          notRefresh = true;
          console.warn(`${ActivitiesDetailUpdateAction.UPDATE_THIS.toString()} has been executed`);
          break;
      }
      if (notRefresh) {
        return;
      }
      this.partitions = [];
      this.partitions.push(...partitionsCopy);
      this.cdr.detectChanges();
    }
  }

  public onCancelationReasonsChanged(reasons: number[]): void {
    this._showCancelationReasons = reasons.length > 0;
  }

  public getSaveOptions(isClientPlannerTask?: boolean): DropdownButtonItem[] {
    if (this.isPlannerModule) {
      return this.updateOptionsPlanner;
    }
    if (isClientPlannerTask) {
      return this.updateOptionsPlannerTask;
    }
    return this.updateOptions;
  }

  public getSaveOptionsLabel(commitmentTask: number): string {
    if (this.isPlannerModule) {
      return '';
    }
    return this.translate.instant(`plannedHours-update-${commitmentTask}`);
  }

  public getCanEditPreImplementer(preImplementerUser: TextLongValue): boolean {
    if (!this.canDeleteOrEditPreImplementer) {
      return true;
    }
    return preImplementerUser.value === null;
  }

  public getCanDeletePreImplementer(preImplementerUser: TextLongValue): boolean {
    if (!this.canDeleteOrEditPreImplementer) {
      return false;
    }
    return this.data.preImplementerUsers.length > 0 && preImplementerUser.value !== null;
  }

  public onInvalidDate(invalidAgainst: any): void {
    if (invalidAgainst.dateValidatorType === 'less') {
      this.noticeService.notice(this.translate.instant('taskStartDateLessThanProjectStartDate'));
    }
  }

  public setDefaultImplementerIds(_defaultImplementerIds: number[]): void {
    this._defaultImplementerIds = _defaultImplementerIds;
  }
}
