<div
  #mainContent
  class="main"
  [class.hide-header]="hideHeader"
  [class.hide-side-menu]="hideSideMenu"
  igxLayout
  [class.is-desktop]="isDesktop"
  [class.is-wide-screen]="isScreenLarge"
  [class.is-drawer-open]="drawer()?.isOpen"
  (dragenter)="dragEnter($event)"
  (dragleave)="dragLeave($event)"
>
  <igx-nav-drawer id="navigation" class="elevation-2" [pin]="drawerState.pin"  [pinThreshold]="drawerState.pinThreshold" [width]="null" [enableGestures]="false">
    <ng-template igxDrawer>
      <nav aria-label="Site menu">
        <div igxDrawerItem [isHeader]="true" class="drawer-header">
          <!-- LOGO -->
          <a [href]="homeUrl" class="main-logo" (click)="goHome($event, true)">
            <img src="./../view/v-application-logo.view" class="logo" alt="Logo" />
          </a>
          @if (isScreenLarge) {
            <span [igxIconButton]="'flat'" class="navArrow" (click)="toggleAction()">
              <igx-icon family="material">chevron_left</igx-icon>
            </span>
          }
          <!-- SECCIÓN DE BÚSQUEDA -->
          <igx-input-group type="search" [hidden]="mobileShowHelper" [ngClass]="displayDensityClass" theme="material">
            <input #entry class="search-menu-field" igxInput placeholder="{{ 'search_menu' | translate: this }}" (keyup)="onKey()" />
            <input #search igxInput type="hidden" (ngModelChange)="onFilterChange()" [(ngModel)]="searchMenu" />
            <igx-prefix>
              <igx-icon>search</igx-icon>
            </igx-prefix>
            <igx-suffix>
              @if (searchMenu.length > 0) {
                <igx-icon class="prefixIcon" (click)="clearSearch()">clear</igx-icon>
              }
            </igx-suffix>
          </igx-input-group>
        </div>
        <div class="drawer-items" (mouseleave)="mouseleave($event)">
          @for (item of navItems; track item; let i = $index) {
            @if (item.showSettingsLabel) {
              <div class="settings-nav" [hidden]="mobileShowHelper && !settingsModule">
                <span class="settings-span">{{ 'menu.settings' | translate: this }}</span>
              </div>
            }
            @if (item.show) {
              <span
                title="{{ isFormActive(item) ? item.text : ('menu.inactiveFormTitle' | translate: this) }}"
                igxDrawerItem
                [attr.data-cy]="item.breadcrumbsKey"
                class="{{ menuItemClass(item, false) }}"
                igxRipple
                [class.elevation-2]="item.elevation === 2"
                [class.level3-separator]="item.lastCurrentLevel"
                [class.navOpen]="item.open"
                [class.disabled]="!isFormActive(item)"
                (click)="isFormActive(item) ? navigateMenu(item) : $event.stopPropagation()"
                (contextmenu)="menuContext($event, item)"
              >
                <igx-icon family="material" [hidden]="mobileShowHelper && item.open && item.icon !== ''">{{ item.icon }}</igx-icon>
                @if (mobileShowHelper && item.open && item.icon !== '') {
                  <igx-icon family="material">reply</igx-icon>
                }
                <span [class.spanMenuNav]="!(item.level !== 3 || (item.level === 3 && item.icon === ''))" class="max-span-menu">{{ item.text }}</span>
                @if (item.level === 3 && item.icon !== '') {
                  @defer (on immediate) {
                    <app-dropdown-menu
                      (changed)="navigateDropDownMenu($event, item)"
                      (dropdownClosed)="onCloseDropdown($event, item)"
                      [alwaysVisible]="true"
                      [openedAtClickTarget]="false"
                      [class]="'dropMenuNav dropMenuNav_' + item.key + navItems.indexOf(item)"
                      [displayDensity]="displayDensity"
                      [dropdownId]="item.breadcrumbsKey"
                      [enableRightPosition]="true"
                      [iconName]="null"
                      [style.background-color]="'transparent'"
                      [igxButtonType]="'flat'"
                      [menuOptions]="hasItems(item)"
                      [navMenu]="true"
                      [rippleDisabled]="true"
                      [selectable]="false"
                      igxButtonLabel="{{ item.text }}"
                    >
                    </app-dropdown-menu>
                  }
                }
              </span>
            }
          }
          @if (!isScreenLarge) {
            <div class="settings-nav">
              <span class="settings-span"></span>
              <a
                (click)="help()"
                href="javascript: void(0);"
                title="{{ 'help' | translate: this }}"
                class="level0 menu-item igx-nav-drawer__item"
                [class.displayNone]="menuBarHidden.help"
              >
                <igx-icon family="material">help_outline</igx-icon>
                <span>{{ 'menu.help' | translate: this }}</span>
              </a>
              @if (!isAnonymous) {
                <a (click)="profile()" href="javascript: void(0);" title="{{ 'my-profile' | translate: this }}" class="level0 menu-item igx-nav-drawer__item">
                  <igx-icon family="material">account_circle</igx-icon>
                  <span>{{ 'my-profile' | translate: this }}</span>
                </a>
              } @else {
                <a (click)="qr()" href="javascript: void(0);" title="{{ 'my-qr' | translate: this }}" class="toolbar-icon">
                  <igx-icon family="material">qr_code</igx-icon>
                </a>
              }
              <a
                (click)="signOut()"
                href="javascript: void(0);"
                title="{{ 'logout' | translate: this }}"
                class="level0 menu-item igx-nav-drawer__item"
                [class.displayNone]="menuBarHidden.signOut"
              >
                <igx-icon family="material">exit_to_app</igx-icon>
                <span>{{ 'menu.signOut' | translate: this }}</span>
              </a>
            </div>
          }
        </div>
      </nav>
    </ng-template>
    <!-- MENU RAIL -->
    @if (isScreenLarge) {
      <ng-template igxDrawerMini>
        <nav aria-label="Site menu">
          <div igxDrawerItem [isHeader]="true" class="drawer-mini-header">
            <a [href]="homeUrl" class="isotipo-logo" (click)="goHome($event)">
              <img alt="Isotipo logo" width="56px" height="56px" src="./../view/v-isotype-logo.view" />
            </a>
            <span #toggleMenuIcon [igxIconButton]="'flat'" class="navArrow" (click)="toggleAction()">
              <igx-icon family="material">chevron_right</igx-icon>
            </span>
          </div>
          <div class="drawer-items-mini">
            @for (item of navItemsMini; track item; let i = $index) {
              @if (item.showSettingsLabel) {
                <div class="settings-nav" [hidden]="mobileShowHelper && !settingsModule">
                  <span class="settings-span">{{ 'menu.settings' | translate: this }}</span>
                  <igx-divider></igx-divider>
                </div>
              }
              <span class="iconNavRail" igxDrawerItem igxRipple (click)="navigateRail(item)" [active]="isItemSelected(item, selectedItemRail)">
                <igx-icon family="material">{{ item.icon }}</igx-icon>
                <span class="module-text">{{ item.text }}</span>
              </span>
            }
          </div>
        </nav>
      </ng-template>
    }
  </igx-nav-drawer>

  <igx-nav-drawer
    id="navigationRail"
    #drawerRail
    [isOpen]="navNavigationRail"
    class="navigationRailExpanded"
    [pin]="false"
    [pinThreshold]="3000"
    [enableGestures]="false"
    [width]="null"
  >
    <ng-template igxDrawer>
      <nav aria-label="Site menu" (mouseleave)="mouseleave($event)">
        <div class="moduleTitle">
          <span title="{{ selectedItemRail?.text }}">{{ selectedItemRail?.text }}</span>
        </div>
        @for (item of navItemsRail; track item.breadcrumbsKey; let i = $index) {
          @if (item.show && item.level !== 0) {
            <span
              title="{{ isFormActive(item) ? item.text : ('menu.inactiveFormTitle' | translate: this) }}"
              igxDrawerItem
              [attr.data-cy]="item.breadcrumbsKey"
              class="{{ menuItemClass(item, true) }}"
              igxRipple
              [class.elevation-2]="item.elevation === 2"
              [class.level3-separator]="item.lastCurrentLevel"
              [class.display-menu]="item.level === 4"
              [class.navOpen]="item.open"
              [class.disabled]="!isFormActive(item)"
              (click)="isFormActive(item) ? navigateMenu(item) : $event.stopPropagation()"
              (contextmenu)="menuContext($event, item)"
            >
              <igx-icon family="material" [hidden]="mobileShowHelper && item.open && item.icon !== ''">{{ item.icon }}</igx-icon>
              <span class="max-span-menu">{{ item.text }}</span>
            </span>
          }
        }
      </nav>
    </ng-template>
  </igx-nav-drawer>

  <main #main igxFlex [class.hide-side-menu]="hideSideMenu">
    @if (showWarningBanner) {
      @defer (on immediate) {
        <app-menu-warning [type]="warningBannerId" (closed)="onClosedWarningBanner()"></app-menu-warning>
      }
    }
    <igx-navbar class="main-appbar" [class.hide-header]="hideHeader" #mainAppBar>
      <div class="igx-navbar__title" igxNavbarTitle>
        @if (!drawer()?.isOpen && !isScreenLarge && !fabShowBackAvailable) {
          <span [igxIconButton]="'flat'" igxToggleAction="navigation">
            <igx-icon family="material">menu</igx-icon>
          </span>
        }
        @if (!isScreenLarge && fabShowBackAvailable) {
          <span [igxIconButton]="'flat'" (buttonClick)="fabBackAction()">
            <igx-icon family="material">arrow_back</igx-icon>
          </span>
        }
        <div>
          @if (isMobilDevice()) {
            <span>{{ isModuleTitle ? appBarTitle : 'BnextQMS' }}</span>
          } @else {
            {{ appBarTitle }}
          }
        </div>
        @if (appBarSubTitle && isScreenLarge) {
          <div class="igx-navbar__subtitle">{{ appBarSubTitle }}</div>
        }
        <div class="search-activity-section">
          <select #selectModuleSearch class="select-module-search">
            @for (option of moduleSearchItems; track option) {
              <option [selected]="setSelectModuleToSearch(option.value)" [value]="option.value">{{ option.text }}</option>
            }
          </select>
          <div class="search-zone">
            <input type="text" [placeholder]="'search-code' | translate: this" [(ngModel)]="codeToSearch" class="search-text-activity" (keyup)="onKeyUp($event)" />
            <igx-icon class="reset" (click)="goToSearchActivity()">search</igx-icon>
          </div>
        </div>
      </div>
      <a
        (click)="viewfinder()"
        href="javascript: void(0);"
        title="{{ 'menu.viewfinder-button' | translate: this }}"
        class="toolbar-icon hide-for-small-only"
        [class.displayNone]="menuBarHidden.viewfinder || isSmallTouchDevice"
      >
        <igx-icon family="material">chrome_reader_mode</igx-icon>
      </a>
      @if (isScreenLarge && !isAnonymous) {
        <a (click)="profile()" href="javascript: void(0);" title="{{ 'my-profile' | translate: this }}" class="toolbar-icon hide-for-small-only">
          <igx-icon family="material">account_circle</igx-icon>
        </a>
      } @else {
        <a (click)="qr()" href="javascript: void(0);" title="{{ 'my-qr' | translate: this }}" class="toolbar-icon">
          <igx-icon family="material">qr_code</igx-icon>
        </a>
      }
      <a (click)="help()" href="javascript: void(0);" title="{{ 'help' | translate: this }}" class="toolbar-icon hide-for-small-only">
        <igx-icon family="material">help_outline</igx-icon>
      </a>
      @if (isScreenLarge) {
      <a
        (click)="activeDirectoryAdd()"
        href="javascript: void(0);"
        title="{{ 'menu.activeDirectoryAdd' | translate: this }}"
        class="toolbar-icon hide-for-small-only"
        [class.displayNone]="menuBarHidden.activeDirectoryAdd || isSmallTouchDevice"
      >
        <igx-icon family="material">person_add</igx-icon>
      </a>
      }
      @if (isMobilDevice()) {
        <span class="date-subtitle">{{ isModuleTitle ? appBarSubTitle : appBarTitle }}</span>
      }
      <a (click)="goHome($event, false)" [href]="homeUrl" title="{{ 'pendings' | translate: this }}" class="toolbar-icon">
        <igx-icon family="material">notifications</igx-icon>
        @if (pendingCount) {
          <igx-badge type="error" class="badge-style" [value]="pendingCount || null"></igx-badge>
        }
      </a>
      @if (isScreenLarge) {
        <div class="user-card-container">
          <div title="{{ 'logout' | translate: this }}" (click)="signOut()" class="sign-out-button">
            <igx-icon family="material" class="no-hover">exit_to_app</igx-icon>
            <label>{{ 'logout' | translate: this }}</label>
          </div>
          <igx-avatar
            src="./../view/v-application-avatar.view?u={{ avatarSha512 }}"
            [initials]="userInitials"
            class="elevation-2 hover-active"
            size="medium"
            shape="circle"
          >
          </igx-avatar>
          <div class="user-data-container">
            <div>
              <div class="capitalize">{{ userName }}</div>
              <div>{{ userMail }}</div>
            </div>
          </div>
        </div>
      }
    </igx-navbar>
    <div
      class="main-component-content"
      [class.fab-button-float]="fabMenuFloat"
      [ngClass]="{ 'legacy-wrapper': legacyActive.show, 'legacy-wrapper-document': legacyActive.shownDocumentViewer }"
    >
      @defer (on immediate) {
        <app-fab-button-menu
          [isAvailable]="fabMenuIsAvailable"
          [fabOptions]="fabMenuItems"
          [availableFabOptionValues]="fabMenuItemsAvailable"
          (clickedOption)="doFabActionButton($event)"
        ></app-fab-button-menu>
      }
      @if (currentWindow && !legacyActive.show) {
        <div class="fancy-scroll modern-container" [ngClass]="currentWindowCss" (scroll)="onScrollModernContainer($event)" [hidden]="hiddenRoutes">
          <router-outlet></router-outlet>
        </div>
      }
      <div class="legacy-wrapper-document" [class.displayNone]="!legacyActive.shownDocumentViewer" [hidden]="hiddenRoutes">
        <div class="legacy-frame-container-document" [class.displayNone]="!legacyActive.show">
          @if (legacyActive.hasDocumentViewer) {
            <iframe
              #documentViewer
              id="documentViewer"
              name="documentViewer"
              title=""
              [class.displayNone]="!legacyActive.shownDocumentViewer"
              class="legacy-frame-document"
              [src]="'../view/' + documentViewerSrc | safeUrl"
            >
            </iframe>
          }
        </div>
      </div>
      <div class="legacy-wrapper" [class.displayNone]="legacyActive.shownDocumentViewer" [hidden]="hiddenRoutes">
        <div class="legacy-frame-container" [class.displayNone]="!legacyActive.show || legacyActive.shownDocumentViewer">
          <iframe #basefrm id="basefrm" name="basefrm" title="" class="legacy-frame"></iframe>
        </div>
      </div>
      @if (isPendingsAvailable) {
        @defer (on immediate) {
          <app-menu-widget [hidden]="!showPendings" [isHidden]="!showPendings" [shouldRefreshPending]="shouldRefreshPending"></app-menu-widget>
        }
      }
    </div>
  </main>
</div>
@if (about.licenceType !== '-' && 'PRODUCTION' !== about.licenceType) {
  <div class="licenceTypeBanner">
    <span>{{ about.licenceType }}</span>
    @if (config?.systemId) {
      <span> - {{ config.systemId }}</span>
    }
  </div>
}
@if (paramBuilderDialogOpen) {
  <igx-dialog
    #paramBuilderDialog
    title="{{ parameterBuilderTitle }}"
    rightButtonLabel="{{ parameterBuilderRightButton }}"
    leftButtonLabel="{{ parameterBuilderLeftButton }}"
    (rightButtonSelect)="onParameterBuilderRightButtonSelect()"
    (leftButtonSelect)="onParameterBuilderLeftButtonSelect()"
  >
    <div class="grid-container">
      <div class="grid-x">
        @for (parameterBuilder of parameterBuilderArray; track parameterBuilder) {
          <div class="cell">
            <igx-input-group [ngClass]="displayDensityClass" type="border" theme="material">
              <igx-suffix>
                <igx-icon (click)="parameterBuilder.value = ''">clear</igx-icon>
              </igx-suffix>
              <input igxInput name="{{ parameterBuilder.fieldName }}" type="text" [(ngModel)]="parameterBuilder.value" [required]="true" maxlength="4000" />
              <label igxLabel for="{{ parameterBuilder.fieldName }}">{{ parameterBuilder.fieldLabel }}</label>
            </igx-input-group>
          </div>
        }
      </div>
    </div>
  </igx-dialog>
}

@if (showContextualMenu) {
  <div #contextualMenu class="contextual-menu elevation-2">
    <ul>
      <li (click)="removeFavorite()">{{ 'remove' | translate: this }}</li>
      <li (click)="favoriteInfo()">{{ 'edit' | translate: this }}</li>
    </ul>
  </div>
}

@if (igxFavoriteUpdateOpen) {
  <igx-dialog
    #igxFavoriteUpdate
    title="{{ 'updateDescription' | translate: this }}"
    rightButtonLabel="{{ 'update' | translate: this }}"
    leftButtonLabel="{{ 'cancel' | translate: this }}"
    (rightButtonSelect)="updateFavorite()"
    (leftButtonSelect)="cancelUpdateFavorite()"
  >
    <label (click)="navigateToUrl()"
      >URL: <span class="label-link">{{ favoriteUrl }}</span></label
    >
    <br />
    <label>{{ 'activity-type' | translate: this }}: {{ favoriteActivity }}</label>
    <igx-input-group>
      <input igxInput type="text" [(ngModel)]="favoriteDescription" />
    </igx-input-group>
  </igx-dialog>
}

@if (avatarDialogOpen) {
  <igx-dialog
    #avatarDialog
    title="{{ 'update' | translate: this }}"
    leftButtonLabel="{{ 'cancel' | translate: this }}"
    rightButtonLabel="{{ 'go-to-profile' | translate: this }}"
    (leftButtonSelect)="showInformationDialogs()"
    (rightButtonSelect)="goToProfileView()"
  >
    <div>
      {{ 'update-avatar-message' | translate: this }} <span (click)="goToProfileView()" class="clickTextHere"> {{ 'clickTextHere' | translate: this }}</span>
    </div>
  </igx-dialog>
}

@if (hasAccessTimesheet) {
  @defer (on immediate) {
    <app-timesheet-dialog [currentSelectedDate]="currentTimesheetWidgetDate"></app-timesheet-dialog>
  }
}
