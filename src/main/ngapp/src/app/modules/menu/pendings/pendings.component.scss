//Los colores del componente se pueden configurar desde el archivo componentColors.scss en la carpeta src
@use 'src/styles/mediaQueries' as *;
@use 'src/styles/immutable-colors' as *;
@use 'src/styles/colors' as *;
@use 'sass:string';

.max-height-vh {
  height: 100%;
  overflow: hidden;
}

:host ::ng-deep .grid-container {
  max-width: 100%;

  &.search-field-container {
    padding: 0.5rem 1rem;
  }

  &.grid-floating-active {
    margin-bottom: 0;
  }

  &.main-grid-container {
    padding: 0rem;
    width: 100%;
  }
}

.container-form {
  height: 100%;
  background-color: $bg-color;
}

.module-filters {
  padding-top: 0.5rem;

  > div {
    display: inline-block;
    vertical-align: bottom;

    > div {
      margin-right: 0.65rem;
    }
  }
}
.igx-chip-area .badge-style {
  position: absolute;
  margin-top: -1.125rem;
  margin-left: 1.125rem;
  justify-content: flex-start;
}

.chip-pending {
  background-color: #00000074;
  border-radius: 1.5rem;
  font-size: 0.6875rem;
  font-weight: 400;
  color: $bg-color;
  height: 1.5rem;
  min-width: 1.5rem;
  text-align: center;
  line-height: 1.5rem;
  float: left;

  &--count {
    background-color: #ff134a;
  }

  .igx-icon {
    margin-top: 0.188rem;
  }
}

.grid-update-container {
  display: flex;
  flex-grow: 1;
  justify-content: flex-end;
}

.list-container {
  margin-right: 1rem;
  margin-left: 1rem;
  margin-bottom: 0.2rem;
  margin-top: 0.2rem;
  height: auto;
}

.grid-update-last-updated {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 0.5rem;
  font-style: italic;
  font-weight: 300;
  font-size: 0.8em;
  padding-right: 1rem;
  padding-left: 1rem;
  line-height: 0.938rem;
  position: absolute;
  bottom: 0rem;
  > span a.underlined {
    font-weight: 400;
    cursor: pointer;
  }
}

.pending-item {
  height: 4.5rem;

  .pending-loading {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
  }
  .main-action {
    width: 100%;
    margin-bottom: 0.3rem;
  }
  .secondary-action {
    width: 100%;
    white-space: normal;
  }
}
.chip-section {
  padding-top: 0.5rem;
}

.filter-chip {
  padding: 0rem 0.25rem 0.25rem 0rem;
}

:host ::ng-deep .pending-item.igx-list__item-base:hover {
  background: $bg-color;
  --background: #{$bg-color};
}

:host ::ng-deep .item-container {
  max-height: 4.5rem;
  cursor: default;
  .item-buttons {
    padding-right: 0.5rem;
  }
  .item {
    position: relative;
    flex: 1 1 7.5rem !important;

    .comment-box {
      position: absolute;
      width: calc(100% - 14rem);
      height: 4.3rem;
      top: 0.2rem;
      left: 0;
      padding-left: 0.8rem;
      padding-bottom: 0.2rem;

      > igx-input-group {
        background: $bg-color;

        &.igx-input-group,
        > .igx-input-group__bundle,
        > .igx-input-group__bundle > .igx-input-group__bundle-main,
        > .igx-input-group__bundle > .igx-input-group__bundle-main .igx-input-group__textarea {
          height: 100%;
          min-height: 1rem;
        }
        > .igx-input-group__bundle > .igx-input-group__bundle-main {
          padding-right: 13.2rem;
        }
        > .igx-input-group__bundle > .igx-input-group__bundle-main .igx-input-group__textarea {
          resize: none;
        }
      }

      .comment-box-buttons {
        position: absolute;
        bottom: 0.2rem;
        right: 1rem;

        button {
          margin: 0rem 0.25rem 0.25rem 0rem;
        }
      }
    }
  }
  .item-container .item__info a.pending-anchor {
    cursor: default;
  }
  .module-filters > div.search-button,
  .item-container .item__info.item-small {
    display: none;
  }
  .item-container {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: row;

    .item__info.item-large {
      margin-left: 0;
      display: flex;
      flex-direction: row;

      .item-info-code {
        width: 7rem;
        min-width: 7rem;
        line-height: 1.5rem;
        margin-left: 0.8rem;
        display: flex;
        flex-direction: column;
      }
    }
  }
  .item__buttons-container {
    position: absolute;
    display: none;
    height: 4.3rem;
    max-width: 10rem;
    right: 5rem;

    .last-action-description {
      visibility: hidden;
    }
  }
  .item__buttons-container.comment_active {
    display: inline-block;
  }
  &:hover {
    .item__buttons-container {
      display: inline-block;

      .hidden-buttons div {
        display: flex;
        flex-direction: column;

        > button,
        > a.pending-anchor {
          margin: 0.1rem 0.2rem;
          min-height: 1.5rem;
          height: 1.8rem;
          width: 9.8rem;
          text-transform: none;
          font-size: 0.8rem;
        }
      }
      .compact-size .spin-button,
      .spin-button {
        align-items: center;
      }
    }
  }
}

:host ::ng-deep .pending-progress .compact-size {
  .square {
    width: 8rem !important;
    height: 3.8rem !important;
    position: relative;
    margin-top: 0.1rem;
  }

  .progress-input {
    min-height: 3.5rem !important;
    padding-top: 0.25rem;

    .input-value,
    input {
      margin-top: 0;
      height: 3.8rem !important;
      max-width: 100%;
      border-radius: 1rem;
      width: 100%;
    }
    .spin-button {
      position: absolute;
      width: 100%;
      height: 1.5rem;
      width: auto;

      &--up {
        top: 0;
      }

      &--down {
        bottom: 0;
      }
    }
    .side-button {
      position: absolute;
      top: 0.8rem !important;
      cursor: pointer;

      label {
        font-size: 0.5rem;
        font-weight: bold;
        left: 0.313rem;
        position: absolute;
        top: 1rem !important;
      }
      &--left {
        left: 0.625rem !important;
      }
      &--right {
        right: 0px;
      }
    }

    &.fill-form-input {
      > .igx-icon {
        width: 3rem;
        height: 3rem;
        font-size: 3rem;
      }
    }
  }
}
.items-header {
  display: flex;
  align-items: center;
}
.items-title {
  padding-right: 0.5rem;
}

:host ::ng-deep app-dropdown-menu.module-pending-button {
  & + .badge-style {
    position: relative;
    margin-top: -0.938rem;
    margin-left: calc(100% - 0.75rem);
    justify-content: flex-end;
  }
}

.chip-pending {
  border-radius: 1.5rem;
  font-size: 0.6875rem;
  font-weight: 400;
  color: $bg-color;
  height: 1.3rem;
  min-width: 1.3rem;
  text-align: center;
  line-height: 1.3rem;
  float: left;

  .igx-icon {
    margin-top: 1px;
  }
}
.filter-chip {
  padding: 0rem 0.25rem 0.25rem 0rem;
}

:host ::ng-deep .mini-view {
  &.grid-container {
    padding: 0px;
    margin-bottom: 0px;
    height: 100%;
    width: 30rem;
    position: fixed;
    right: 0;
    box-shadow:
      0 1px 0.313rem 0 rgba($fg-color, 0.26),
      0 0.125rem 0.125rem 0 rgba($fg-color, 0.12),
      0 0.188rem 1px -0.125rem rgba($fg-color, 0.08);
    background-color: rgba($fg-color, 0.42);

    app-dropdown-menu.module-pending-button button {
      background-color: rgba($fg-color, 0.42);
    }

    .grid-x > .large-6,
    .grid-x > .large-9 {
      width: 100% !important;
    }

    .container-form {
      height: 100vh;
      background-color: $bg-color;

      .grid-update-last-updated {
        line-height: 1.125rem;
      }
    }

    .list-container {
      padding-top: 0;
    }

    .igx-vhelper--vertical {
      overflow: auto;
    }

    .pendings-content-title {
      padding-bottom: 1rem;
    }

    .igx-input-group__bundle {
      border-radius: 0;
    }

    .module-filters {
      > div {
        display: flex;
        justify-content: center;
      }
    }

    .floating-action-buttons,
    .header {
      display: none;
    }

    .body {
      padding-right: 0;
      padding-left: 0;
    }

    .igx-input-group__bundle-main input {
      font-size: 0.875rem;
      padding-bottom: 0px;
      border-top: 0px;
    }

    .igx-chip {
      margin: 0.25rem;
    }

    .item-container .item__info {
      margin-left: 0.625rem;
      max-width: 16rem;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  &.grid-right-panel {
    padding: 0;
    margin: 0;
    height: 100%;
    width: 30rem;
    position: fixed;
    right: 30rem;
    max-width: none;
    top: 0;
    background-color: $bg-color;
    filter: none;
    border-radius: 0;

    div div.grid-x .cell.container-cell.fancy-scroll {
      max-width: 30rem;
      left: calc(50% - 15rem);
      max-height: calc(100% - 5rem);
      top: 0.75rem;
      padding-bottom: 1rem;
    }

    .right-panel-content .igx-list {
      padding: 0 0.5rem 1.25rem 0.5rem;
    }
  }

  .hide-on-mini-view {
    display: none;
  }
}

:host ::ng-deep .grid-right-panel {
  position: fixed;
  z-index: 4;
  top: 0;
  width: 25%;
  max-width: 30rem;
  right: 0;
  overflow: auto;
  height: 100vh;
  background-color: $bg-color;

  &.grid-right-panel--hidden {
    display: none;
  }

  .close-button {
    min-height: auto;
    right: 0;
    position: absolute;
    background-color: #ececec;
    border-radius: 0;
  }
}

:host ::ng-deep .grid-right-panel {
  .dragging-drop-zone {
    top: 0;
    width: calc(100% - 1rem);
    height: 100%;
  }
}
:host ::ng-deep .grid-right-panel-container {
  padding-bottom: 1rem;
  .before-pending-btn {
    position: relative;
    top: -2.4rem;
  }
  .vertical-navigator {
    position: fixed;
    right: 0.7rem;
    top: 5.5rem;
    z-index: 1;
    > * {
      padding: 0.125rem 0.5rem;
      cursor: pointer;
      background-color: $bg-color;
      line-height: 1.5rem;
      padding: 0rem 0.25rem;
      font-size: 0.75rem;
      border-width: 1px;
      border-style: solid;
      margin-top: -1px;
      color: #004691;
      filter: drop-shadow(-0.35rem 0.45rem 0.125rem #ccca);

      &:hover {
        filter: brightness(1.2) drop-shadow(-0.35rem 0.45rem 0.125rem #ccc);
        transform: scale(1.1) translate(-0.188rem, -1px);
        font-weight: bold;
      }
      span {
        font-style: italic;
      }
    }
  }
  .after-pending-btn {
    position: relative;
    top: -4.75rem;
    float: right;
  }

  .panel-pending-type-name {
    text-transform: uppercase;
    background-color: #efefef;
    font-size: 0.75rem;
    color: #444;
    text-align: center;
    line-height: 1rem;
    border-radius: 2.5rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5rem 0 2.5rem;
  }

  .right-panel-actions {
    padding: 0.5rem 0.5rem 0rem;

    button,
    a.pending-anchor {
      width: calc(100% - 2rem);
      text-align: left;
      align-items: flex-start;
      justify-content: flex-start;
      margin: 0rem 0rem 0.5rem;
      height: auto;
      min-height: auto;
      .igx-icon {
        margin-left: -0.25rem;
        + label {
          margin-left: 0.5rem;
        }
      }
    }

    button label,
    a.pending-anchor label {
      white-space: pre-line;
    }
    a.pending-anchor label {
      margin-top: 0.125rem;
    }

    &.question-mode {
      padding-left: 1.15rem;
    }

    > h5 {
      margin-top: 0rem;
    }
  }
  .right-panel-header {
    padding: 0 1rem;
    height: 3.3rem;
  }
  .expansion-panel {
    igx-expansion-panel-body {
      line-height: inherit;
      letter-spacing: inherit;
      font-size: inherit;
      min-height: 5rem;
      .flip-card-front,
      igx-icon {
        box-sizing: content-box;
      }
      igx-list {
        min-height: inherit;
      }
    }
  }
  .right-panel-content {
    padding-top: 1rem;
  }
  .header {
    padding-top: 1rem;

    &.header-details {
      padding: 0 1rem;

      .pending-description {
        padding-top: 0.2rem;
        padding-right: 0px;
        padding-bottom: 1rem;
        padding-left: 0rem;
      }
    }
  }

  .right-panel-content .igx-list {
    padding-bottom: 1.25rem;
  }
  .right-panel-content div.flip-card-front,
  .right-panel-content div.flip-card-back {
    width: 1.5rem;
    height: 1.5rem;
  }
  .right-panel-content div.field-display-input {
    width: calc(100% - 3.75rem);
  }
}

.search-container {
  position: relative;

  .search-field {
    padding-top: 0.2rem;
  }

  .search-field-large {
    padding-top: 0.2rem;
    min-width: 50%;

    ::ng-deep .igx-input-group__bundle {
      --size: 2.2rem;
    }
  }
}

.title-calendar {
  padding: 1.8rem 1.3rem;
  font-size: 1.25rem;
  font-weight: 600;
  font-style: normal;
  letter-spacing: 0.009375rem;
  text-transform: none;
  line-height: 1.5rem;
  color: rgba($fg-color, 0.87);
}

h5 {
  padding: 1rem 0;
}
:host ::ng-deep .highlight-search {
  background-color: yellow;
  outline: 1px solid #ffbf00;
}
.mini-pendings {
  igx-list .item-buttons,
  h5 {
    display: none;
  }

  .search,
  .grid-container {
    padding: 0;
  }

  .list-container {
    padding-top: 8rem;
  }

  .search.igx-input-group__bundle {
    box-shadow: none;
    border-bottom: 1px solid #0002;
  }

  .grid-floating-active.grid-container {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.pendings-content-header {
  text-align: left;
  .pendings-content-title {
    font-size: 1rem;
    font-weight: bold;
  }
}

.visibility-hidden .igx-chip {
  display: none;
}

::ng-deep app-dropdown-menu.module-pending-button button.igx-icon-button--outlined,
::ng-deep app-dropdown-menu.module-pending-button button.igx-button--outlined {
  text-align: left;
  font-size: 0.875rem;
  text-transform: none;
  justify-content: end;
  font-weight: normal;
  border-radius: unset;
  min-width: 15rem;
}

.container-actions {
  text-align: center;
  width: 100%;
  padding-top: 0.625rem;

  .link {
    font-size: 0.75rem;
    text-decoration: underline;
    cursor: pointer;
  }
}
/* ---------------- Filter Button ---------------------------------------- */
.filter-button {
  igx-icon {
    vertical-align: middle;
  }

  + igx-badge {
    min-width: 0.8rem;
    width: 1rem;
    height: 1rem;
    position: absolute;
    top: -0.4rem;
    right: -0.25rem;
    color: $bg-color;
  }
}
.item-index {
  z-index: 1;
}
/* ---------------- Filtros ---------------------------------------- */
.filter-dropdown {
  padding: 0.5rem;
}
::ng-deep .filter-container {
  max-width: 25rem;
  min-width: 25rem;

  .header-filter-panel.igx-expansion-panel__header.igx-expansion-panel__header--expanded {
    border-radius: 0.3rem;
  }

  .header-filter-panel div.igx-expansion-panel__header-inner {
    padding: 0.5rem 0.8rem 0.4rem 0.8rem;
  }

  .content-item-filters {
    padding: 0.5rem 0.5rem 0.5rem 2.7rem;
    display: flex;
    flex-direction: column;

    .content-filter {
      font-size: 0.8rem;
      flex-flow: row nowrap;
      display: flex;
      flex-grow: 1;

      span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .badge-style {
    background: $bg-color;
    color: $fg-color;
    margin-left: 1rem;
    display: inline-flex;
  }
  .header-filter {
    font-size: 1rem;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
  }
}

::ng-deep app-fill-type .full-height.compact-size .progress-input {
  min-width: 5rem;
}

:host ::ng-deep .igx-list.comment-list {
  .list-header > button {
    padding: 0 0.5rem;
  }

  igx-list-item .igx-list__item-content {
    min-height: 5rem;
    max-height: 15rem;

    .igx-list__item-line-title {
      line-height: 1.2rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 5;
      line-clamp: 5;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }

    .igx-list__item-lines > p {
      margin: 0.5rem 0 0 0;
    }

  }
}

.detail-comment {
  max-width: 70rem;

  > span {
    white-space: pre-wrap;
  }
}

/* responsividad small */
@media print, screen and (max-device-width: $small) {
  @media (hover: none) and (pointer: coarse) {
    .container-form {
      height: 100vh;
    }
    .search-field-large {
      display: none !important;
    }
    .module-filters > div.search-button {
      display: inline-block;
    }
    ::ng-deep .filter-container {
      max-width: 20rem;
      min-width: 18rem;

      .header-filter-panel div.igx-expansion-panel__header-inner {
        padding: 0.4rem 0.6rem 0.3rem 0.6rem;
      }
      .content-item-filters {
        padding: 0.4rem 0.4rem 0.4rem 2.5rem;

        .content-filter {
          font-size: 0.8rem;
          max-width: 13rem;
        }
      }
    }
    .chip-section {
      .igx-chip-area {
        ::ng-deep .igx-chip__content {
          max-width: 8rem;
        }
      }
    }
    .item-container {
      .item__info.item-small {
        margin-left: 0;
        .item-content {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 1rem;
          font-weight: bold;
          max-width: 70vw;
        }
        .item-header {
          justify-content: normal;
        }
      }
      .item__info.item-large,
      .item__buttons-container .hidden-buttons,
      .item__buttons-container .last-action-description {
        display: none;
      }
    }
    .igx-avatar--small {
      width: 2.5rem;
      height: 2.5rem;
      min-width: 2rem;
      margin-right: 1rem;
    }
    .grid-update-container {
      display: none;
    }
    .filter-button {
      + igx-badge {
        transform: translate(100%, -50%);
        top: initial;
        right: initial;
      }
    }
  }
}

/* responsividad extra-small */
@media print, screen and (max-width: $xsmall) {
  .container-form {
    height: 100vh;
  }
  .search-field-large {
    display: none !important;
  }
  .module-filters > div.search-button {
    display: inline-block;
  }
  ::ng-deep .filter-container {
    max-width: 20rem;
    min-width: 17rem;

    .header-filter-panel div.igx-expansion-panel__header-inner {
      padding: 0.4rem 0.6rem 0.3rem 0.5rem;
    }
    .content-item-filters {
      padding: 0.4rem 0.4rem 0.4rem 2.4rem;

      .content-filter {
        font-size: 0.75rem;
        max-width: 12rem;
      }
    }
  }
  .chip-section {
    .igx-chip-area {
      ::ng-deep .igx-chip__content {
        max-width: 8rem;
      }
    }
  }
  .item-container {
    .item__info.item-small {
      margin-left: 0;
      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 1rem;
        font-weight: bold;
        max-width: 70vw;
      }
      .item-header {
        justify-content: normal;
      }
    }
    .item__info.item-large,
    .item__buttons-container .hidden-buttons,
    .item__buttons-container .last-action-description {
      display: none;
    }
  }
  .igx-avatar--small {
    width: 2.5rem;
    height: 2.5rem;
    min-width: 2rem;
    margin-right: 1rem;
  }
  .grid-update-container {
    display: none;
  }
  .filter-button {
    + igx-badge {
      transform: translate(100%, -50%);
      top: initial;
      right: initial;
    }
  }
}

.xsmallResponsive {
  .module-filters > div.search-button {
    display: inline-block;
  }
  .chip-section {
    .igx-chip-area {
      ::ng-deep .igx-chip__content {
        max-width: 8rem;
      }
    }
  }
  .item-container {
    .item__info.item-small {
      margin-left: 0.125rem;
      display: flex;
      width: 100%;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 1rem;
        font-weight: bold;
      }
      .item-header {
        justify-content: normal;
      }
    }
    .item__info.item-large,
    .item__buttons-container .hidden-buttons,
    .item__buttons-container .last-action-description {
      display: none;
    }
  }
  .igx-avatar--small {
    width: 2.5rem;
    height: 2.5rem;
    min-width: 2rem;
    margin-right: 1.5rem;
  }
  .grid-update-container {
    display: none;
  }
}

.smallResponsive {
  .module-filters > div.search-button {
    display: inline-block;
  }
  .chip-section {
    .igx-chip-area {
      flex-wrap: nowrap;
      ::ng-deep .igx-chip__content {
        max-width: 10rem;
      }
    }
  }
  .item-container {
    .item {
      flex: 1 0 25.625rem !important;
    }
    .item__info.item-small {
      margin-left: 0.125rem;
      display: flex;
      width: 100%;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 1rem;
        font-weight: bold;
      }

      .item-header {
        justify-content: normal;
      }
    }
    .item__info.item-large,
    .item__buttons-container .hidden-buttons,
    .item__buttons-container .last-action-description {
      display: none;
    }
  }
  .igx-avatar--small {
    width: 2.5rem;
    height: 2.5rem;
    min-width: 2rem;
    margin-right: 1.25rem;
  }
  .grid-update-container {
    display: none;
  }
}

/* responsividad medium
@media print, screen and (min-width: $medium) */
.mediumResponsive {
  .module-filters > div.search-button,
  .item-container .item__info.item-small {
    display: none;
  }
  .item-container {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: row;

    .item__info.item-large {
      margin-left: 0;
      display: flex;
      flex-direction: row;
      width: 100%;

      .item-info-code {
        width: 7rem;
        min-width: 7rem;
        line-height: 1.5rem;
        margin-left: 0.8rem;
        display: flex;
        flex-direction: column;
      }
    }

    .item__buttons-container {
      position: absolute;
      display: none;
      height: 4.3rem;
      max-width: 10rem;
      right: 5rem;

      .last-action-description {
        visibility: hidden;
      }
    }
    .item__buttons-container.comment_active {
      display: inline-block;
    }
    &:hover {
      .pending-container {
        max-width: 29.375rem !important;
      }
      .item__buttons-container {
        display: inline-block;

        .hidden-buttons div {
          display: flex;
          flex-direction: column;

          > button,
          > a.pending-anchor {
            margin: 0.1rem 0.2rem;
            min-height: 1.5rem;
            height: 1.8rem;
            width: 9.8rem;
            text-transform: none;
            font-size: 0.8rem;
          }
        }
      }
    }
    .pending-container {
      display: flex;
      flex-direction: column;
      width: calc(100% - 8.8rem); // <-- se resta el width del componente de la linea 1097, el margen en 1100 y margen de 1147
      margin-left: 1rem;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        max-width: 100%;
        &.pending-anchor {
          width: auto;
          display: inline-block;
        }
        .item-code {
          font-weight: bold;
        }
      }
      .item-chips {
        .last-action-message.smaller {
          max-width: 8rem;
        }
        .chip-message {
          max-width: 9.5rem;
          &.smaller {
            max-width: 8rem;
          }
        }
        .chip-message.chip-large {
          display: none;
        }
      }
    }
    .item-buttons {
      padding-right: 0.5rem;
    }
    .item-avatar {
      height: 4.3rem;
      padding-top: 1rem;

      .igx-avatar--small {
        width: 2.5rem;
        height: 2.5rem;
        min-width: 2rem;
        margin-right: 1.25rem;
      }
    }
  }
  .grid-update-container {
    display: flex;
  }
}

/* responsividad large 1024px
  Estos estilos se conservan para el panel derecho del detalle de pendientes
*/
@media print, screen and (min-width: $large) {
  :host ::ng-deep .grid-right-panel {
    top: 0;
    width: 35%;
  }

  :host ::ng-deep app-field-display div.field-display-main {
    padding-bottom: 0;
  }

  :host ::ng-deep .mini-view {
    &.grid-left-panel.grid-floating-active.grid-container {
      padding-top: 0;
      padding-left: 0;
      padding-right: 0;
    }
  }
}

.largeResponsive {
  .module-filters > div.search-button,
  .item-container .item__info.item-small {
    display: none;
  }

  .item-container {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: row;

    .item__info.item-large {
      margin-left: 0;
      display: flex;
      flex-direction: row;

      .item-info-code {
        width: 7rem;
        min-width: 7rem;
        line-height: 1.5rem;
        margin-left: 0.8rem;
        display: flex;
        flex-direction: column;
      }
    }

    .item__buttons-container {
      position: relative;
      display: inline-block;
      height: 4.3rem;
      width: 10rem;
      padding: 0 1rem 0 0;
      right: 0.5rem;

      .hidden-buttons,
      .last-action-description {
        visibility: hidden;
      }
    }
    .item__buttons-container .hidden-buttons.comment_active {
      visibility: visible;
    }

    &:hover {
      .item__buttons-container .hidden-buttons {
        visibility: visible;

        > div {
          display: flex;
          flex-direction: column;

          > button,
          > a.pending-anchor {
            margin: 0.1rem 0.2rem;
            min-height: 1.5rem;
            height: 1.8rem;
            width: 100%;
            text-transform: none;
          }
        }
      }
    }
    .pending-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-left: 0;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        max-width: 100%;

        &.pending-anchor {
          width: auto;
          display: inline-block;
        }
        .item-code {
          font-weight: bold;
        }
      }
      .item-chips {
        .last-action-message,
        .chip-message {
          max-width: 11rem;
        }
        .chip-message.chip-large {
          display: inline-block;
          max-width: 10rem;
          min-width: 6rem;
        }
      }
    }
    .item-buttons {
      padding-right: 0.5rem;
    }
    .item-avatar {
      height: 4.3rem;
      padding-top: 1rem;

      .igx-avatar--small {
        width: 2.5rem;
        height: 2.5rem;
        min-width: 2rem;
        margin-right: 1.25rem;
      }
    }
  }
  .grid-update-container {
    display: flex;
  }
}

/* responsividad xlarge 1280px
@media print, screen and (min-width: $xlarge) */
.xlargeResponsive {
  .module-filters > div.search-button,
  .item-container .item__info.item-small {
    display: none;
  }
  .item-container {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: row;

    .item__info.item-large {
      margin-left: 0;
      display: flex;
      flex-direction: row;

      .item-info-code {
        width: 7rem;
        min-width: 7rem;
        line-height: 1.5rem;
        margin-left: 0.8rem;
        display: flex;
        flex-direction: column;
      }
    }
    .item__buttons-container {
      position: relative;
      display: inline-block;
      height: 4.3rem;
      width: 10rem;
      padding: 0 1rem 0 0;
      right: 1.5rem;

      .hidden-buttons,
      .last-action-description {
        visibility: hidden;
      }
    }
    .item__buttons-container .hidden-buttons.comment_active {
      visibility: visible;
    }

    &:hover {
      .item__buttons-container .hidden-buttons {
        visibility: visible;

        > div {
          display: flex;
          flex-direction: column;

          > button,
          > a.pending-anchor {
            margin: 0.1rem 0.2rem;
            min-height: 1.5rem;
            height: 1.8rem;
            width: 100%;
            text-transform: none;
          }
        }
      }
    }
    .pending-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-left: 5rem;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        max-width: 100%;

        &.pending-anchor {
          width: auto;
          display: inline-block;
        }
        .item-code {
          font-weight: bold;
        }
      }
      .item-chips {
        .chip-message,
        .last-action-message {
          max-width: 10rem;
        }
        .chip-message.chip-large {
          display: inline-block;
          max-width: 10rem;
          min-width: 6rem;
        }
      }
    }
    .item-buttons {
      padding-right: 0.5rem;
    }
    .item-avatar {
      height: 4.3rem;
      padding-top: 1rem;

      .igx-avatar--small {
        width: 2.5rem;
        height: 2.5rem;
        min-width: 2rem;
        margin-right: 1.25rem;
      }
    }
  }
  .grid-update-container {
    display: flex;
  }
}

/* responsividad xxlarge 1600px
@media print, screen and (min-width: $xxlarge)*/

.xxlargeResponsive {
  .module-filters > div.search-button,
  .item-container .item__info.item-small {
    display: none;
  }

  .item-container {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: row;

    .item__info.item-large {
      margin-left: 0;
      display: flex;
      flex-direction: row;

      .item-info-code {
        width: 7rem;
        min-width: 7rem;
        line-height: 1.5rem;
        margin-left: 0.8rem;
        display: flex;
        flex-direction: column;
      }
    }

    .item__buttons-container {
      position: relative;
      display: inline-block;
      height: 4.3rem;
      width: 23rem;
      padding: 0 1rem 0 0;

      .hidden-buttons {
        visibility: hidden;
        height: 0;
      }
      .last-action-description {
        margin-top: 2.3rem;
        visibility: visible;
        font-size: 0.7rem;
        text-align: end;
        line-height: 1rem;
      }
    }
    .item__buttons-container .hidden-buttons.comment_active {
      visibility: visible;
    }
    .item__buttons-container .last-action-description.comment_active {
      visibility: hidden;
    }

    &:hover {
      .item__buttons-container {
        .hidden-buttons {
          visibility: visible;
          text-align: center;

          > div {
            display: flex;
            flex-direction: column;
            align-items: center;

            > button,
            > a.pending-anchor {
              margin: 0.1rem 0.2rem;
              min-height: 1.5rem;
              height: 1.8rem;
              width: 12rem;
              text-transform: none;
            }
          }
        }
        .last-action-description {
          visibility: hidden;
        }
      }
    }
    .pending-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-left: 7rem;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        max-width: 100%;

        &.pending-anchor {
          width: auto;
          display: inline-block;
        }
        .item-code {
          font-weight: bold;
        }
      }
      .item-chips {
        .chip-message,
        .last-action-message {
          max-width: 10rem;
        }
        .chip-message.chip-large {
          display: inline-block;
          max-width: 10rem;
          min-width: 6rem;
        }
      }
    }
    .item-buttons {
      padding-right: 0.5rem;
    }
    .comment-box {
      width: calc(100% - 21rem);
    }
    .item-avatar {
      height: 4.3rem;
      padding-top: 1rem;

      .igx-avatar--small {
        width: 2.5rem;
        height: 2.5rem;
        min-width: 2rem;
        margin-right: 1.25rem;
      }
    }
  }
  .grid-update-container {
    display: flex;
  }
}
@media only screen and (min-width: 20rem) and (max-width: 60rem) and (orientation: landscape) {
  .container-form {
    height: 100vw;
  }
  .search-field-large {
    display: none !important;
  }
  .module-filters > div.search-button {
    display: inline-block;
  }
  .chip-section {
    .igx-chip-area {
      flex-wrap: nowrap;
      ::ng-deep .igx-chip__content {
        max-width: 10rem;
      }
    }
  }
  .item-container {
    .item {
      flex: 1 0 25.625rem !important;
    }
    .item__info.item-small {
      margin-left: 0.125rem;
      display: flex;
      width: 100%;

      .item-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 1rem;
        font-weight: bold;
      }

      .item-header {
        justify-content: normal;
      }
    }
    .item__info.item-large,
    .item__buttons-container .hidden-buttons,
    .item__buttons-container .last-action-description {
      display: none;
    }
  }
  .igx-avatar--small {
    width: 2.5rem;
    height: 2.5rem;
    min-width: 2rem;
    margin-right: 1.25rem;
  }
  .grid-update-container {
    display: none;
  }
}

.future {
  color: #2ecc71;
}

.delayed {
  color: #e74c3c;
}

.adjust {
  position: absolute;
  color: #239b56;
}
.calendar-section {
  display: inherit;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
}

.section-calendar {
  display: inherit;
  align-items: center;
}

.text {
  font-style: normal;
  font-size: 1rem;
}

.full-size-footer {
  display: inline-flex;
  flex-direction: row;
  justify-content: space-between;
  width: string.unquote('calc(100% - 2rem)');
}

.info-section {
  width: calc(75%);
}

.header-grouped-pending {
  display: inline-flex !important;
  gap: 0.2rem;
  width: 100%;
}

.header-grouped-pending.desktop-template-header > .description-grouped-pending {
  margin-top: 0rem;
  width: 100%;
  display: flex;
  align-items: center;
  height: 2rem;
  padding-left: 0.5rem;

  igx-icon {
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.3rem;
  }

  > span {
    font-size: 0.8rem;
    font-weight: bold;
    padding-left: 0.4rem;
    width: 100%;

    &.count {
      padding-right: 1.7rem;
      text-align: right;
      padding-top: 0.4rem;

      ::ng-deep .igx-chip__content {
        padding: 0 0.5rem !important;
      }
    }
  }
}

.header-grouped-pending.mobile-template-header > .description-grouped-pending {
  display: flex;
  justify-content: space-between;
  width: inherit;
  padding: 0.25rem 1.25rem;
  border-left: 1px solid #8080802e;
  border-right: 1px solid #8080802e;
  border-bottom: 1px solid #8080802e;
  border-top-width: 1px;
  border-top-style: solid;
  font-size: 0.813rem;
  font-weight: 600;
}

.separatorPending {
  border-bottom: none !important;
  touch-action: none !important;
  padding-bottom: 0;
  padding-top: 2rem;

  .igx-list__item-right,
  .igx-list__item-left {
    visibility: hidden !important;
  }
  .left-panning,
  .right-panning {
    display: none !important;
    touch-action: none !important;
    user-select: none;
  }
}

.normalPending {
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}

.remove-hover-bg-effect {
  --item-background-hover: none;
}

.remove-hover-text-shadow {
  filter: none !important;
}

.list-container-without-inline-margin {
  margin-bottom: 0.2rem;
  margin-top: 0.2rem;
  height: auto;
  box-shadow: none;
  border-bottom: solid 1px rgba($fg-color, 0.25);
  border-top: solid 1px rgba($fg-color, 0.25);
}

.item-index-right {
  right: 1rem;
  left: unset;
}

.list-header {
  width: 100%;
  margin-bottom: -0.2rem;
}

igx-list-item[isHeader='true'] {
  border-bottom: none;
}

igx-list-item > .list-header > button {
  position: relative;
  float: right;
  transform: translateY(50%);
  z-index: 3;
}
.igx-list igx-list-item {
  border: 1px solid #8080802e;
  border-top: none;
  display: flex;
  flex-direction: row;
}

.igx-list igx-list-item:active {
  box-shadow:
    0 calc(var(--ig-elevation-factor, 1) * 3px) calc(var(--ig-elevation-factor, 1) * 5px) calc(var(--ig-elevation-factor, 1) * -1px) rgba(0, 0, 0, 0.075),
    0 calc(var(--ig-elevation-factor, 1) * 6px) calc(var(--ig-elevation-factor, 1) * 10px) 0 #888888,
    0 calc(var(--ig-elevation-factor, 1) * 1px) calc(var(--ig-elevation-factor, 1) * 18px) 0 rgba(0, 0, 0, 0.85);
}

:host ::ng-deep {
  .file-container,
  .list-timesheet-activity {
    .igx-list__item-content .igx-list__item-thumbnail {
      height: 100%;
      cursor: pointer;
    }
  }
}

.list-timesheet-activity {
  transition: 1s;
  height: 30rem;
}

.last-message-tooltip {
  margin-top: 0.5rem;
  font-size: 1rem;
  padding: 1rem;
  overflow-wrap: anywhere;
  min-width: 10rem;
  text-align: center;
  max-width: 40rem;
  width: max-content;
}

.last-message-tooltip::before {
  content: '';
  width: 0;
  height: 0;
  border: 0.5rem solid transparent;
  border-top: 0;
  border-bottom: 0.5rem solid hsla(var(--ig-gray-700), 0.9);
  position: absolute;
  top: 0rem;
  right: 50%;
  transform: translateX(50%);
}

.mobile-option {
  display: flex;
  align-items: center;
}

.pending-title-mobile {
  padding: 1rem 0 0.5rem 0;
  margin: 0;
  font-weight: 700;
  font-size: 1.1rem;
}

.search-field igx-prefix {
  color: $primary-color;
}

.mobile-template {
  display: flex;
  justify-content: space-between;
  padding-right: 1.563rem;
  padding-left: 1.25rem;
  .pending-info {
    display: inherit;
    flex-direction: column;
    gap: 0.7rem;
    font-size: 0.813rem;

    .title-info {
      display: inherit;
      align-items: center;
      gap: 0.5rem;

      .info {
        color: black;
        font-weight: 700;
        font-size: 0.938rem;
      }

      .title-icon {
        color: #2c5eb5;
        font-size: 1.2rem;
        height: 1.2rem;
        width: 1.2rem;
      }
    }
    .subtitle-info {
      font-size: 0.813rem;
    }
  }

  .pending-actions {
    display: inherit;
    align-items: center;

    .igx-icon {
      width: 0.938rem;
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  flex-wrap: wrap;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: break-spaces;
}

.count-activities {
  text-transform: none;
}

.header-grouped-wrapper {
  width: calc(100% - 2rem);
  margin: 0 1rem;
  margin-top: 2.313rem;
}

.item-order {
  color: black;
  font-weight: bold;
  font-size: 0.875em;
  position: absolute;
  right: 0%;
}

.igx-list--empty {
  align-items: stretch;
}

.action-zone {
  min-width: 4rem;
  max-width: 4rem;
}

.igx-list .item-card .item-container .item .item-header {
  width: 100%;
  justify-content: left;
}
