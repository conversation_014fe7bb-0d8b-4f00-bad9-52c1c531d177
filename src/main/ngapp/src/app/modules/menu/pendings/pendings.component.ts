import * as DateUtil from '@/core/utils/date-util';
import { Date<PERSON>ip<PERSON>, KeyValuePipe, Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
  type AfterContentInit,
  type AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  NgZone,
  type OnDestroy,
  type OnInit,
  Output,
  Renderer2,
  ViewChild,
  inject,
  input,
  output,
  viewChild,
  viewChildren
} from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { DomSanitizer, HammerModule, type SafeHtml } from '@angular/platform-browser';
import {
  AutoPositionStrategy,
  ButtonGroupAlignment,
  CloseScrollStrategy,
  GlobalPositionStrategy,
  HorizontalAlignment,
  type IButtonGroupEventArgs,
  type IChipsAreaReorderEventArgs,
  type IExpansionPanelEventArgs,
  type IListItemClickEventArgs,
  type IListItemPanningEventArgs,
  IgxActionStripComponent,
  IgxAvatarComponent,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxButtonGroupComponent,
  IgxCalendarComponent,
  IgxCheckboxComponent,
  IgxChipComponent,
  IgxChipsAreaComponent,
  IgxDataLoadingTemplateDirective,
  IgxDialogComponent,
  IgxDividerDirective,
  IgxDropDownComponent,
  IgxDropDownItemNavigationDirective,
  IgxEmptyListTemplateDirective,
  IgxExpansionPanelBodyComponent,
  IgxExpansionPanelComponent,
  IgxExpansionPanelHeaderComponent,
  IgxExpansionPanelTitleDirective,
  IgxFilterOptions,
  IgxFilterPipe,
  IgxForOfDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxListActionDirective,
  IgxListComponent,
  IgxListItemComponent,
  IgxListItemLeftPanningTemplateDirective,
  IgxListItemRightPanningTemplateDirective,
  IgxListLineTitleDirective,
  IgxListThumbnailDirective,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective,
  IgxToggleActionDirective,
  IgxTooltipDirective,
  IgxTooltipTargetDirective,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { scaleInCenter, scaleOutCenter } from '@infragistics/igniteui-angular/animations';
import { FileUploadModule } from 'ng2-file-upload';
import { Subject, type Subscription, combineLatest, from, takeUntil } from 'rxjs';
import { debounceTime, groupBy, mergeMap, throttleTime, toArray } from 'rxjs/operators';
import type { SaveCommentEvt } from 'src/app/core/comment-multi-save/comment-multi-save-interfaces';
import { DropFileHandlerComponent } from 'src/app/core/drop-file/drop-file.component';
import {
  type FileSelectOptions,
  type IDropFileHandler,
  type IFileData,
  type LinkedFileEvt,
  SUPPORTED_OPEN_BROWSER_CONTENT_TYPES
} from 'src/app/core/drop-file/drop-file.interfaces';
import { DropdownMenuComponent } from 'src/app/core/dropdown-menu/dropdown-menu.component';
import { Session } from 'src/app/core/local-storage/session';
import { MultiFileUploadComponent } from 'src/app/core/multi-file-upload/multi-file-upload.component';
import { RestApiModule } from 'src/app/core/rest-api.module';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import type { DialogInput } from 'src/app/core/services/dialog.service.interfaces';
import type { DataMap } from 'src/app/core/utils/data-map';

import * as DomUtil from '@/core/utils/dom-util';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FeatureFlag, isFeatureAvailable } from 'src/app/core/utils/feature-util';
import { ReplacePipe } from 'src/app/core/utils/replace.pipe';
import { StringFormat } from 'src/app/core/utils/string-format';

import type { i18n } from '@/core/bnext-core.component';
import { BnextCoreComponent } from '@/core/bnext-core.component';
import { CommentMultiSaveComponent } from '@/core/comment-multi-save/comment-multi-save.component';
import { FileDropEnterDirective } from '@/core/directives/file-upload.directive';
import { DocumentMultiSelectComponent, type SaveDocumentEvt } from '@/core/document-multi-select/document-multi-select.component';
import type { DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import { FieldHandlerComponent } from '@/core/dynamic-field/field-handler/field-handler.component';
import { FieldDisplayComponent } from '@/core/field-display/field-display.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { PendingsLocalConfig, type SelectedCalendarDates, type SelectedMenuOptions, type SelectedPendingDTO } from '@/core/indexed-db/pendings-local-config';
import { ConfigPendings } from '@/core/local-storage/config-pendings';
import { AppService } from '@/core/services/app.service';
import { NoticeService } from '@/core/services/notice.service';
import { EnumUtil } from '@/core/utils/enum-util';
import { CommonAction } from '@/core/utils/enums';
import { FileIconPipe } from '@/core/utils/file-icon.pipe';
import { FileSizePipe } from '@/core/utils/file-size.pipe';
import { cloneObject, keysObject } from '@/core/utils/object';
import { encodeUrlParameter } from '@/core/utils/string-util';
import { MODULE_CONFIG, PENDING_TYPE_CONFIG } from '@/modules/config.pendings.module';
import { MODULE_ICON, ModulesAvailable } from '@/modules/menu/menu-definition/menu-definition.const';
import { getPendingRequestChangeInstance } from '@/modules/menu/pendings-request/pendings-request.builder';
import { MenuService } from '@/modules/menu/services/menu.service';
import { PendingsFormsUtil } from '@/modules/pendings/forms/forms.activities.util';
import type { PlannerTaskData } from '@/shared/planner/planner-task/planner-task.interfaces';
import { DoubleTapDirective } from 'src/app/core/directives/doubletap';
import { LongPressDirective } from 'src/app/core/directives/longpress';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { IDocumentEntity } from 'src/app/shared/activities/activities-document.interfaces';
import { FillType, FillTypeSize, HistoryViewMode } from 'src/app/shared/activities/core/activities-core.enums';
import { getNormalizedModule, isActivitySubmodule } from 'src/app/shared/activities/core/utils/activity-util';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import { ActivitiesHistoryComponent } from '../../activities/activities-history/activities-history.component';
import { ActivitiesImplementationComponent } from '../../activities/activities-implementation/activities-implementation.component';
import type { AuditIndividualPendingDto } from '../../audits/core/audits.interfaces';
import { AUDIT_APE, DOCUMENT_APE, FINDING_ACTIVITY_APE } from '../../config.pendings.enum';
import { FormRequestAction, RequestAuthorizeResponse } from '../../forms/utils/form.enums';
import type { ActivityHistoryEntity } from '../../pendings/activities/activities-pending/activites-history.interfaces';
import { ImplementAction, VerifyAction } from '../../pendings/activities/activities-pending/activities-pending.enums';
import type { ActivityPendingDto } from '../../pendings/activities/activities-pending/activities-pending.interfaces';
import { FillTypeComponent } from '../../pendings/activities/fill-type/fill-type.component';
import type { ChangeProgress } from '../../pendings/activities/fill-type/fill-type.interfaces';
import { PendingsActivitiesComponent } from '../../pendings/activities/pendings-activities/pendings.activities.component';
import type { ActivityPickedPending } from '../../pendings/activities/pendings-activities/pendings.activities.interfaces';
import { PendingsActivitiesUtil } from '../../pendings/activities/pendings-activities/pendings.activities.util';
import type { ComplaintPendingDto } from '../../pendings/complaints/complaints-pending.interfaces';
import { PlannerStatus } from '../../planner/planner-add/planner-add.interfaces';
import type { UpdateUIStopwatch } from '../../timesheet/timesheet-widget/timesheet-widget.interfaces';
import { TimesheetWidgetUtils } from '../../timesheet/timesheet-widget/timesheet-widget.utils';
import { TimesheetService } from '../../timework/services/timesheet.service';
import type { SizeWidget, WidgetConfig } from '../../widgets/widget-panel/widget-panel.interfaces';
import type { BasicPendingData } from '../menu-widget/menu-widget.interfaces';
import { MenuWidgetService } from '../menu-widget/menu-widget.service';
import { PendingsRequestAction } from '../pendings-request/pendings-request.enums';
import type { PendingsRequestChange } from '../pendings-request/pendings-request.interfaces';
import { PendingCommentService } from '../services/pending-comment.service';
import type { TimesheetDto } from './../../timesheet/timesheet-widget/timesheet-widget.interfaces';
import { PendingsRequestComponent } from './../pendings-request/pendings-request.component';
import { PendingsUtil } from './pendings-util';
import { DocumentAction, LinkedSelector, PendingsAction } from './pendings.enums';
import {
  type DocumentPickedPending,
  type DocumentRequestPendingDto,
  type FormPendingDto,
  type FormRequestPickedPending,
  type ModuleUnitConfig,
  type PanningActions,
  PanningButton,
  type PendingCodeConfig,
  type PendingComment,
  type PendingDataSource,
  type PendingDto,
  type PendingFeasibleDto,
  type PendingTypeConfig,
  type PickedPending,
  type RighPanelCollapsedStatus
} from './pendings.interfaces';

@Component({
  selector: 'app-pendings',
  templateUrl: './pendings.component.html',
  styleUrls: ['./pendings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    HammerModule,
    IgxIconButtonDirective,
    NgClass,
    NgTemplateOutlet,
    FormsModule,
    ReactiveFormsModule,
    DatePipe,
    IgxLabelDirective,
    IgxAvatarComponent,
    IgxButtonDirective,
    IgxIconComponent,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxPrefixDirective,
    IgxSuffixDirective,
    IgxDropDownComponent,
    IgxCalendarComponent,
    IgxChipsAreaComponent,
    IgxChipComponent,
    IgxLinearProgressBarComponent,
    IgxListActionDirective,
    IgxListComponent,
    IgxListItemComponent,
    IgxListItemLeftPanningTemplateDirective,
    IgxListItemRightPanningTemplateDirective,
    IgxListThumbnailDirective,
    IgxListLineTitleDirective,
    IgxDataLoadingTemplateDirective,
    IgxEmptyListTemplateDirective,
    IgxButtonGroupComponent,
    IgxForOfDirective,
    IgxRippleDirective,
    IgxDividerDirective,
    IgxExpansionPanelComponent,
    IgxExpansionPanelHeaderComponent,
    IgxExpansionPanelTitleDirective,
    IgxExpansionPanelBodyComponent,
    IgxBadgeComponent,
    IgxActionStripComponent,
    IgxDialogComponent,
    IgxTooltipTargetDirective,
    IgxTooltipDirective,
    IgxToggleActionDirective,
    IgxDropDownItemNavigationDirective,
    IgxCheckboxComponent,
    IgxFilterPipe,
    FieldDisplayComponent,
    DropFileHandlerComponent,
    FileUploadModule,
    FileDropEnterDirective,
    FillTypeComponent,
    FieldHandlerComponent,
    ActivitiesHistoryComponent,
    ActivitiesImplementationComponent,
    CommentMultiSaveComponent,
    DocumentMultiSelectComponent,
    MultiFileUploadComponent,
    PendingsRequestComponent,
    DropdownMenuComponent,
    PendingsActivitiesComponent,
    FileIconPipe,
    FileSizePipe,
    ReplacePipe,
    BnextTranslatePipe,
    DoubleTapDirective,
    LongPressDirective
  ]
})
export class PendingsComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit, AfterContentInit, i18n {
  zone = inject(NgZone);
  sanitizer = inject(DomSanitizer);
  commentService = inject(PendingCommentService);
  datePipe = inject(DatePipe);
  keyValuePipe = inject(KeyValuePipe);
  noticeService = inject(NoticeService);
  api = inject(AppService);
  public _scrollingListEvent = new Subject<boolean>();
  public scrollingListEvent = this._scrollingListEvent.asObservable();
  executed = false;
  @HostBinding('hidden')
  isHidden = false;
  // pantalla
  timestampFormat = 'dd/MM/yyyy h:mm:ss';
  timeFormat = 'h:mm:ss';
  dateFormat = 'dd/MM/yyyy';
  panEndTriggeringThreshold = 0.6;
  optionsAlignment = ButtonGroupAlignment.horizontal;
  isLoading = false;
  isRefresingData = false; // Click en icono refresh
  attendedPending = false;
  documentsLoading = false;
  attachmentsLoading = false;
  commentsLoading = false;
  pendingActionLoading = false;
  historyLoading = false;
  addingDocument = false;
  addingFile = false;
  addingComment = false;
  isRightPanelHidden = true;
  isPendingRequestAvailable = isFeatureAvailable(FeatureFlag.APE_PENDING_REQUEST);
  isTimesheetAvailable = Session.hasService(ProfileServices.TS_REGISTER_PLANNED) || Session.hasService(ProfileServices.TS_REGISTER_UNPLANNED);
  chartModuleKey = 'Módulo';
  lastRefreshDate: Date = null;
  deleteDocumentServices = [ProfileServices.NONE];
  isSearchBarHidden = true;
  isFuture = false;
  titleDate = '';
  currentDate = new Date();
  isStopwatchrunning = false;
  stopwatchRunningDate: Date;
  pendingRecordId: number;
  stopwatchType: number;
  pendingListViewGrouped = true;
  pendingListSorted = PendingsAction.SORT_ASC;
  hideFuturePendings = false;
  exactSearch = true;
  inputMaxLimit = 4000; // limite de caracteres
  minHeightResponsive = this.windowInnerHeight <= 720 ? 535 : 700;
  touchPosition = null;
  commentDetail = '';
  rightPanelCollapsed: RighPanelCollapsedStatus = {
    rightPanelActions: true,
    rightPanelHeader: true,
    rightPanelComments: true,
    rightPanelFiles: true,
    rightPanelDocuments: true,
    rightPanelTimesheet: true
  };
  //Rango de tamaños de la descripción del pendiente (para ocultar o reducir tamaño de chips solo para Laptops)
  rangeForChipSmaller = 305;
  rangeForHideChip = 390;
  // Menú de opciones generales
  pendingsDropdownOptions: DropdownMenuItem[] = [
    {
      text: 'Refrescar pendientes',
      value: CommonAction.REFRESH,
      iconName: 'refresh'
    },
    {
      text: 'Mostrar rango de fechas',
      value: PendingsAction.FILTER_RANGE,
      iconName: 'date_range'
    },
    {
      text: 'Agrupar por fecha',
      value: PendingsAction.GROUP_PENDINGS,
      selected: true
    },
    {
      text: 'Mostrar pendientes futuros',
      value: PendingsAction.FILTER_FROM_TOMORROW,
      selected: true
    },
    {
      text: 'Búsqueda exacta',
      value: PendingsAction.EXACT_SEARCH,
      selected: true
    },
    {
      text: 'Ordenar más antiguos primero',
      value: PendingsAction.SORT_ASC,
      iconName: 'north'
    },
    {
      text: 'Ordenar más recientes primero',
      value: PendingsAction.SORT_DESC,
      iconName: 'south'
    }
  ];
  pendingsDropdownOptionsAvailable: string[] = [PendingsAction.GROUP_PENDINGS, PendingsAction.FILTER_FROM_TOMORROW, PendingsAction.EXACT_SEARCH, PendingsAction.SORT_ASC];
  // filtro de texto
  searchValue = '';
  searchModules: string[] = [];
  searchPendingTypes: DropdownMenuItem[] = [];
  // filtro de fecha
  futurePendingCount = 0;
  startDate: Date = null;
  endDate: Date = new Date(Date.now());
  filterCalendarActive = false;
  // filtros del local storage
  filtersApplied = [];
  isClickedTwice: boolean;
  overlaySettings = {
    positionStrategy: new GlobalPositionStrategy({ openAnimation: scaleInCenter, closeAnimation: scaleOutCenter }),
    modal: true,
    closeOnOutsideClick: true
  };
  public overlaySettingsfilter = {
    closeOnOutsideClick: true,
    modal: false,
    scrollStrategy: new CloseScrollStrategy(),
    positionStrategy: new AutoPositionStrategy({
      horizontalDirection: HorizontalAlignment.Right,
      horizontalStartPoint: HorizontalAlignment.Center,
      verticalStartPoint: VerticalAlignment.Bottom
    })
  };
  calendarSearchValue = null;
  // enums
  EnumFillTypeSize = FillTypeSize;
  EnumFillType = FillType;
  EnumHistoryViewMode = HistoryViewMode;
  EnumCommonAction = CommonAction;
  pendingsTemp: PendingDto[] = [];
  clickedPending: PendingDto;
  clickedPendingIdx: number;
  clickedPendingId: number;
  clickedPendingApe: string;
  onCommentAdd: () => void;
  // menus
  pannedPending: PendingDto;
  rightPanningActions: PanningButton[];
  leftPanningActions: PanningButton[];
  menuMainOptions: DropdownMenuItem[] = [
    {
      text: 'Refrescar',
      value: CommonAction.REFRESH,
      iconName: 'refresh'
    }
  ];
  moduleConfig: DataMap<ModuleUnitConfig> = {};
  pendingTypeConfig: PendingCodeConfig = PENDING_TYPE_CONFIG;
  rightPanelButtonBlackList: (CommonAction | ImplementAction | VerifyAction)[] = [
    CommonAction.ADD_ATTACHMENT,
    CommonAction.ADD_DOCUMENT,
    CommonAction.ADD_COMMENT,
    CommonAction.VIEW_ATTACHMENT,
    CommonAction.VIEW_DOCUMENT,
    CommonAction.VIEW_COMMENT,
    CommonAction.HIDE,
    VerifyAction.MULTY_REASSIGN_VERIFY
  ];
  stopwatchItems = [ImplementAction.STOP_STOPWATCH];
  menuOptionsButtonBlackList: (CommonAction | ImplementAction)[] = [
    CommonAction.ADD_DOCUMENT,
    CommonAction.ADD_COMMENT,
    CommonAction.VIEW_ATTACHMENT,
    CommonAction.VIEW_DOCUMENT,
    CommonAction.VIEW_COMMENT,
    CommonAction.OPEN_DETAIL
  ];
  isScrolling = false;
  isPanMoving = false;
  overflow = false;
  formatBetween = 'dd/MM/yy';
  dialogDetailOpen = false;
  pendingVirtualizationHeight = null;
  readonly taskOpenDetail = output<any>();
  @Output() chartData = new EventEmitter<DataMap[]>();
  public readonly onprogress = output<any>();
  readonly showMoreAvailable = input(true);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() miniView = false;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input() shouldRefreshPending = false;
  readonly isPendingsOpen = input(false);
  readonly isPendingsHidden = input(false);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public size: SizeWidget = {};
  public readonly header = input<string>(undefined);

  readonly calendarSearch = viewChild<IgxCalendarComponent>('calendarSearch');
  readonly searchBar = viewChild('searchBarDef', { read: IgxInputDirective });
  readonly dropDownCalendar = viewChild<IgxDropDownComponent>('dropDownCalendar');
  readonly list = viewChild(IgxListComponent);
  readonly commentsDialog = viewChild<IgxDialogComponent>('commentsDialog');
  readonly filesDialog = viewChild<IgxDialogComponent>('filesDialog');

  readonly documents = viewChild<DocumentMultiSelectComponent>('documents');
  readonly comments = viewChild<CommentMultiSaveComponent>('commentsDef');
  readonly files = viewChild<MultiFileUploadComponent>('files');
  readonly pendingVirtualizer = viewChild(IgxForOfDirective);

  // TODO: Skipped for migration because:
  //  This query is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @ViewChild('dropFile', { read: DropFileHandlerComponent })
  public dropFile: IDropFileHandler;
  readonly items = viewChildren(IgxListItemComponent);
  readonly requestChangeDialog = viewChildren<PendingsRequestComponent>('requestChangeDialog');
  readonly chipArea = viewChild('chipArea', { read: ElementRef });
  readonly pendingsDropdown = viewChild<DropdownMenuComponent>('pendingsDropdownDef');
  readonly downloadAnchor = viewChild<ElementRef>('downloadAnchor');
  readonly mainCointainer = viewChild<ElementRef>('mainCointainer');
  readonly activitiesHistory = viewChild<ActivitiesHistoryComponent>('activitiesHistory');
  // componentes por módulo
  readonly pendingsActivities = viewChild<PendingsActivitiesComponent>('activityAttender');
  // Detalle de comentarios
  readonly detailDialog = viewChild('detailCommentDialog', { read: IgxDialogComponent });
  readonly accordion = viewChildren(IgxExpansionPanelComponent);
  private menuWidgetService = inject(MenuWidgetService);

  private render = inject(Renderer2);
  private menu = inject(MenuService);
  private timesheetService = inject(TimesheetService);
  private _onSearchValueChanged = new Subject<string>();
  private _onAddedApeItems = new Subject<boolean>();
  private _changedSearchPendingTypes = new Subject<DropdownMenuItem[]>();
  private _changedSelectedIndex = new Subject<boolean>();
  private _changedSelectedDates = new Subject<SelectedCalendarDates>();
  private _changedSelectedMenuOptions = new Subject<SelectedMenuOptions>();
  // dato
  private pendings: PendingDto[] = [];
  private _lastDataSource: PendingDataSource;

  constructor() {
    super();

    this.subscribeStopwatchService();
    this.scrollingListEvent.pipe(takeUntil(this.$destroy), debounceTime(1000)).subscribe(() => {
      this.isScrolling = false;
      this.cdr.detectChanges();
    });
    this.menuWidgetService.changePendingsLoading(this.isLoading);
  }

  override langReady(): void {
    PendingsActivitiesUtil.setTags(this.translate);
  }

  private _pendingsSource: PendingDataSource;

  get pendingsSource() {
    return this._pendingsSource;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set pendingsSource(pendingsSource: PendingDataSource) {
    this._pendingsSource = pendingsSource;
    if (this.shouldRefreshPending && this.pendings !== undefined && this.pendingsSource !== undefined) {
      this.refreshAll(null, this.pendingsSource);
      this.shouldRefreshPending = false;
    }
  }

  override get componentPath(): string {
    return 'modules.menu';
  }

  get isLoadingLinkedData(): boolean {
    return this.documentsLoading || this.commentsLoading || this.attachmentsLoading || this.pendingActionLoading;
  }

  get containerSize(): string {
    if (this.isSmallTouchDevice) {
      return `${this.windowInnerHeight - 150}px`;
    }
    if (keysObject(this.size).length !== 0) {
      const chipArea = this.chipArea();
      if (chipArea) {
        const heigthArea = chipArea.nativeElement.offsetHeight;
        return `${this.size.height - (165 + heigthArea)}px`;
      }
      if (this.size.responsiveClass === 'xsmallResponsive' || this.size.responsiveClass === 'smallResponsive') {
        return `${this.size.height - 156}px`;
      }
      return `${this.size.height - 160}px`;
    }
    return this.windowInnerHeight < this.minHeightResponsive ? `${this.windowInnerHeight}px` : `${this.windowInnerHeight - 300}px`;
  }

  get itemSize(): string {
    return '68.8px';
  }

  get searchPendingOptions(): IgxFilterOptions {
    return this.filterPendingPendings(this.searchValue);
  }

  get moduleConfigs(): { key: string; value: ModuleUnitConfig }[] {
    return this.keyValuePipe.transform(this.moduleConfig);
  }

  get isFabButtonsLoadingData(): boolean {
    return this.addingDocument || this.addingComment || this.addingFile;
  }

  get itemMaxWidth(): string {
    if (this.size !== null && this.size.responsiveClass === 'xxlargeResponsive') {
      return `${this.size.width - 695}`;
    }
    if (this.size !== null && this.size.responsiveClass === 'xlargeResponsive') {
      return `${this.size.width - 495}`;
    }
    if (this.size !== null && this.size.responsiveClass === 'mediumResponsive') {
      return `${this.size.width - 300}`;
    }
    return `${this.size?.width - 405}`;
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.__init().then(
      () => {
        if (this.debug()) {
          console.log('PendingsComponent init success');
        }
      },
      (e) => {
        console.error('PendingsComponent init error', e);
      }
    );
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.subscribeCommentService();
    const list = this.list();
    if (typeof list !== 'undefined') {
      if (list.isListEmpty && this.pendingsTemp.length === 0) {
        this.showFuturePendings();
      }
    }
    this.detectChanges();
  }

  ngAfterContentInit() {
    super.ngAfterContentInit();
    this.isRightPanelHidden = true;
    this.setTitleDate();
  }

  subscribeCommentService() {
    this.commentService.addCommentDialog.pipe(takeUntil(this.$destroy)).subscribe((commentPending: PendingComment) => {
      const pickedPending: PickedPending = commentPending.pickedPending;
      this.clickedPending = pickedPending.pending;
      this.onCommentAdd = commentPending.onCommentAdd || null;
      this.comments().openTextInput(pickedPending.menuItem, commentPending.message, commentPending.title);
    });
  }

  loadSelectedPending(selected: SelectedPendingDTO) {
    if (selected?.id && this.pendings) {
      let selectedPending: PendingDto = null;
      let selectedPendingIdx: number = null;
      const id = selected.id;
      const type = selected.type;
      this.pendings.find((pending) => {
        if (pending.pendingRecordId === id && pending.pendingTypeCode === type) {
          selectedPending = pending;
          if (ConfigPendings.hasScrollPosition()) {
            selectedPendingIdx = ConfigPendings.getScrollPosition();
          }
          return true;
        }
        return false;
      });
      if (selectedPending) {
        this.setFocus(selectedPending, selectedPendingIdx, false);
        const pendingVirtualizer = this.pendingVirtualizer();
        if (pendingVirtualizer?.igxForOf) {
          pendingVirtualizer.scrollTo(selectedPendingIdx);
        }
        this.cdr.detectChanges();
      } else {
        ConfigPendings.clearScrollPosition();
      }
    } else {
      ConfigPendings.clearScrollPosition();
    }
  }

  //Este metodo sirve para verificar si una lista esta agrupada(si tiene isSeparatorPending, esta agrupada, si no es una lista normal)
  // false: realiza agrupación

  // Método que asigna las fechas almacenadas en el storage al calendario de rango de fechas
  loadCalendarDates(selected: SelectedCalendarDates) {
    if (selected?.filterApplied) {
      if (selected.start !== undefined) {
        this.startDate = selected.start;
      }

      if (selected.end !== undefined) {
        this.endDate = selected.end;
      }
      if (this.startDate !== null && this.endDate !== null) {
        this.calendarSearch().selectDate([this.startDate, this.endDate]);
      }
      this.cdr.detectChanges();
    }
  }

  clearSearchBar(): void {
    this.searchValue = '';
    this.onSearchValueChange('');
    this.cdr.detectChanges();
  }

  detectChanges(): void {
    this.sortPendings(this.pendingListSorted);
    this.isSearchBarHidden = !this.isSmallTouchDevice;
    this.setTitleDate();
    this.cdr.detectChanges();
  }

  onSearchValueChange(searchValue: string) {
    this._onSearchValueChanged.next(searchValue);
  }

  selectSearchPendingTypes(values: (string | number)[]): void {
    if (!values || values.length === 0) {
      return;
    }
    for (const moduleName in this.moduleConfig) {
      if (!this.moduleConfig.hasOwnProperty(moduleName)) {
        continue;
      }
      const module = this.moduleConfig[moduleName];
      if (!module.menuActions || module.menuActions.length === 0) {
        continue;
      }
      // Agregamos los valores seleccionados guardados en LocalStorage
      for (const menuAction of module.menuActions) {
        // Se valida si values es un array, ya que si no lo es se aplican filtros similares (Ejem. ACTIVITY-TO-VERIFY-DELAYED y ACTIVITY-TO-VERIFY)
        if (Array.isArray(values)) {
          if (values.indexOf(menuAction.value) !== -1 && module.selectedMenuActions.indexOf(menuAction.value) === -1) {
            module.selectedMenuActions.push(menuAction.value);
            menuAction.selected = true;
          }
        } else if (values === menuAction.value && module.selectedMenuActions.indexOf(menuAction.value) === -1) {
          module.selectedMenuActions.push(menuAction.value);
          menuAction.selected = true;
        }
        if (menuAction.selected === true && this.searchPendingTypes.indexOf(menuAction) === -1) {
          this.searchPendingTypes.push(menuAction);
        }
      }
    }
  }

  initializeApeLang(): void {
    combineLatest([this.translate.getFrom(PendingsUtil.LANG_CONFIG, 'APE'), this._onAddedApeItems])
      .pipe(takeUntil(this.$destroy))
      .subscribe(([APE]) => {
        this.setMenuActionsLang(APE);
      });
  }

  setMenuActionsLang(ape: any) {
    for (const moduleName in this.moduleConfig) {
      if (!this.moduleConfig.hasOwnProperty(moduleName)) {
        continue;
      }
      const module = this.moduleConfig[moduleName];
      if (!module.menuActions || module.menuActions.length === 0) {
        continue;
      }
      for (const menuAction1 of module.menuActions.filter((menuAction) => menuAction.text !== ape[menuAction.value])) {
        menuAction1.text = ape[menuAction1.value];
      }
    }
    this.cdr.detectChanges();
  }

  updateBarTitle(showNotice = true) {
    const pendingVirtualizer = this.pendingVirtualizer();
    if (pendingVirtualizer?.igxForOf && !this.miniView && showNotice) {
      const correctPendings = pendingVirtualizer.igxForOf.filter((elem) => elem.pendingRecordId);
      const moduleLabel = this.searchValue && this.searchValue.length > 0 ? `${this.searchValue}: ` : '';
      const pageMessages = this.i18n.pageMessages as DataMap<string>;
      const msg = moduleLabel + pageMessages.showing + correctPendings.length + pageMessages.pendings;
      this.noticeService.notice(msg);
    }
    const now = new Date();
    const lang = this.getLang();
    let titleLabel: string;
    let dateFormat: string;
    if (this.isScreenPhone) {
      switch (lang) {
        case 'en':
          dateFormat = 'dddd MMM D';
          break;
        default:
          dateFormat = 'dddd D [{of}] MMMM';
          break;
      }
      titleLabel = DateUtil.format(now, dateFormat, true, lang);
    } else {
      switch (lang) {
        case 'en':
          dateFormat = 'dddd, MMMM D';
          break;
        default:
          dateFormat = 'dddd D [{of}] MMMM';
          break;
      }
      titleLabel = DateUtil.format(now, dateFormat, true, lang);
    }
    this.menuService.changeTitle({
      title: titleLabel.replace('{of}', this.translate.instant('root.common.date.of', true)),
      subtitle: `${StringFormat.titleCase(this.translate.instant('root.common.date.week'))} ${DateUtil.isoWeek(now)}${DateUtil.format(now, '[, ] YYYY', true, lang)}`,
      isModuleTitle: false
    });
    this.resetFocus();
  }

  filterPendingPendings(searchValue: string, includeSearchModules = true, includeSearchPendingTypes = true): IgxFilterOptions {
    const filterOptions = new IgxFilterOptions();
    filterOptions.key = 'description';
    filterOptions.inputValue = searchValue;
    filterOptions.get_value = (item: PendingDto, _key: string) => this.getValueFilterPendings(item, includeSearchModules, includeSearchPendingTypes);
    filterOptions.matchFn = (value: string, inputValue: string): boolean => this.matchFilterPendings(value, inputValue);
    return filterOptions;
  }

  getValueFilterPendings(item: PendingDto, includeSearchModules: boolean, includeSearchPendingTypes: boolean) {
    if (item.hidden) {
      return 'hidden-token';
    }
    if (includeSearchModules && this.searchModules && this.searchModules.length > 0 && this.searchModules.indexOf(item.module.toString().toUpperCase()) === -1) {
      return 'hidden-token';
    }
    const pendingTypeCode = typeof item.pendingTypeCode !== 'undefined' && item.pendingTypeCode !== null ? item.pendingTypeCode.toUpperCase() : null;
    if (
      includeSearchPendingTypes &&
      this.searchPendingTypes &&
      this.searchPendingTypes.length > 0 &&
      pendingTypeCode &&
      !this.searchPendingTypes.some((pendingType) => pendingType.value === pendingTypeCode)
    ) {
      return 'hidden-token';
    }
    if (this.isSearchValueEmpty() && this.startDate == null && this.endDate == null) {
      return 'show-all';
    }
    if (
      this.isSearchValueEmpty() &&
      typeof item.commitmentEndDate !== 'undefined' &&
      item.commitmentEndDate !== null &&
      (this.startDate !== null || this.endDate !== null)
    ) {
      if (DateUtil.safe(item.commitmentEndDate).getTime() === DateUtil.safe(item.commitmentStartDate).getTime()) {
        const s1 = this.datePipe.transform(item.commitmentStartDate, this.dateFormat);
        return `@Compromiso ${s1}@`;
      }
      const s1 = this.datePipe.transform(item.commitmentStartDate, this.dateFormat);
      return `@both ${this.datePipe.transform(item.commitmentEndDate, this.dateFormat)},${s1}`;
    }
    const pendingDetail = this.pendingDetailByType(item, false, true);
    const isToday = this.isPendingDateToday(item.commitmentStartDate) ? 'Hoy' : '';
    const pendingType = this.translate.instant(`APE.${item.pendingTypeCode}`);
    const moduleSearch = this.buildModuleSintaxSearch(item);
    const lastAction = this.lastAction(item);
    const commitmentDate = this.datePipe.transform(item.commitmentStartDate, this.dateFormat);
    return `${pendingDetail}@${lastAction}@${isToday}@${item.code}@Compromiso ${commitmentDate}@${pendingType}${moduleSearch}`;
  }

  matchFilterPendings(value: string, inputValue: string): boolean {
    if (value === 'show-all') {
      return true;
    }
    if (value === 'hidden-token') {
      return false;
    }
    let validDate = true;
    if (this.startDate !== null || this.endDate !== null) {
      let stringDate: string;
      let stringStartDate: string;
      let stringEndDate: string;
      try {
        if (value.includes('@both')) {
          const newValue = value
            .replace(/@both/g, '')
            .split(',')
            .map((s: string) => s.trim());
          stringEndDate = newValue[0];
          stringStartDate = newValue[1];
        } else {
          stringDate = value.match(/.*?@compromiso (.*?)@.*/)[1].trim();
        }
      } catch (_e) {
        stringDate = null;
        stringEndDate = null;
        stringStartDate = null;
      }
      if (stringDate) {
        stringEndDate = null;
        stringStartDate = null;
        const commitmentDate: Date = DateUtil.parse(stringDate, this.dateFormat.toUpperCase(), this.translateService.getLanguage());
        if (commitmentDate !== null && typeof commitmentDate !== 'undefined') {
          if (this.startDate !== null && this.endDate !== null) {
            validDate =
              DateUtil.isSameDay(commitmentDate, this.startDate) ||
              DateUtil.isSameDay(commitmentDate, this.endDate) ||
              (commitmentDate.getTime() >= this.startDate.getTime() && commitmentDate.getTime() <= this.endDate.getTime());
          } else if (this.startDate === null) {
            validDate = DateUtil.isSameDay(commitmentDate, this.endDate) || commitmentDate.getTime() <= this.endDate.getTime();
          } else if (this.endDate === null) {
            validDate = DateUtil.isSameDay(commitmentDate, this.startDate) || commitmentDate.getTime() >= this.startDate.getTime();
          }
        }
      }
      if (stringEndDate && stringStartDate) {
        const commitmentStartDate: Date = DateUtil.parse(stringStartDate, this.dateFormat.toUpperCase(), this.translateService.getLanguage());
        const commitmentEndDate: Date = DateUtil.parse(stringEndDate, this.dateFormat.toUpperCase(), this.translateService.getLanguage());
        if (this.startDate !== null && this.endDate !== null) {
          validDate =
            (commitmentStartDate.getTime() >= this.startDate.getTime() && commitmentEndDate.getTime() <= this.endDate.getTime()) ||
            (commitmentStartDate.getTime() >= this.startDate.getTime() &&
              commitmentStartDate.getTime() < this.endDate.getTime() &&
              commitmentEndDate.getTime() > this.endDate.getTime());
        } else if (this.startDate === null) {
          validDate =
            (commitmentStartDate.getTime() <= this.endDate.getTime() && commitmentEndDate.getTime() <= this.endDate.getTime()) ||
            (this.hideFuturePendings && commitmentStartDate.getTime() <= this.endDate.getTime());
        } else if (this.endDate === null) {
          validDate = commitmentStartDate.getTime() >= this.startDate.getTime() && commitmentEndDate.getTime() >= this.startDate.getTime();
        }
      }
    }
    if (validDate && (inputValue === null || typeof inputValue === 'undefined' || inputValue === '')) {
      return true;
    }
    if (!validDate) {
      return false;
    }
    const inputSearch = inputValue.toLowerCase() || '';
    if (!this.exactSearch && inputSearch.indexOf(',') >= 0) {
      const searchTokens = new Set(inputSearch.split(',').map((s: string) => s.trim()));
      const foundIndex = Array.from(searchTokens).findIndex((token) => value.indexOf(token) > -1);
      return foundIndex >= 0;
    }
    return value.indexOf(inputSearch) > -1;
  }

  /**
   * Al mantener presionado CNTRL y las flechas arriba y abajo
   * cambia de pendiente
   */
  navigateItem(evt: KeyboardEvent): void {
    if (!evt.ctrlKey) {
      return;
    }
    switch (evt.code) {
      case 'ArrowDown':
      case 'Down':
        this.setFocusNext();
        break;
      case 'ArrowUp':
      case 'Up':
        this.setFocusBefore();
        return;
    }
  }

  isSearchValueEmpty(): boolean {
    return !this.searchValue.replace(/[\r\n]/g, '').trim();
  }

  showFuturePendings() {
    this.filterCalendarSearch(PendingsAction.FILTER_FROM_TOMORROW);
  }

  isLastActionAvailable(pending: PendingDto): boolean {
    return PendingsUtil.isLastActionAvailable(pending);
  }

  lastAction(pending: PendingDto, highlightSearch = false): SafeHtml {
    return PendingsUtil.lastAction(this.sanitizer, this.datePipe, this.searchValue, pending, highlightSearch);
  }

  lastActionDescription(pending: PendingDto): SafeHtml {
    return PendingsUtil.lastActionDescription(pending);
  }

  getAuthor(comment: any) {
    return comment.lastModifiedBy;
  }

  itemClicked(args: IListItemClickEventArgs) {
    const pending: PendingDto = this.getPannedPending(args);
    if (pending) {
      pending.isRightPanned = false;
      pending.isLeftPanned = false;
    }
  }

  resetPan(): void {
    const pendingVirtualizer = this.pendingVirtualizer();
    if (pendingVirtualizer?.getScroll() && this.pendingVirtualizationHeight !== '0px' && this.pendingVirtualizationHeight != null) {
      pendingVirtualizer.getScroll().style.height = this.pendingVirtualizationHeight;
      this.cdr.detectChanges();
    }
  }

  panMove(event: any) {
    if (typeof event.additionalEvent === 'undefined') {
      event.preventDefault();
      return;
    }
    if (this.isTouchDevice || 'ontouchMove' in window || event.type === 'panmove') {
      if ((event.additionalEvent === 'panleft' || event.additionalEvent === 'panright') && !this.isScrolling) {
        this.isPanMoving = true;
        const pendingVirtualizer = this.pendingVirtualizer();
        if (pendingVirtualizer?.getScroll() && pendingVirtualizer.getScroll().style.height !== '0px') {
          this.pendingVirtualizationHeight = pendingVirtualizer.getScroll().style.height;
          pendingVirtualizer.getScroll().style.height = '0';
        }
        this.render.listen(this.list().element.nativeElement, 'ontouchMove', (eventS) => {
          eventS.cancelBubble = true;
          eventS.stopPropagation();
          eventS.preventDefault();
        });
        event.preventDefault();
      } else if (!this.isPanMoving) {
        const list = this.list();
        list.allowLeftPanning = false;
        list.allowRightPanning = false;
        if (!this.isScrolling) {
          this.isScrolling = true;
          this.cdr.detectChanges();
        }
        this._scrollingListEvent.next(true);
      }
    }
  }

  movingEvent(e: any) {
    if (this.isPanMoving) {
      return;
    }
    const current = e.touches[0];
    const threshold = 10;
    const touchActionMoveX = current.clientX;
    const touchActionMoveY = current.clientY;
    const touchActionDownX = this.touchPosition ? this.touchPosition.clientX : 0;
    const touchActionDownY = this.touchPosition ? this.touchPosition.clientY : 0;

    this.touchPosition = current;

    if (touchActionMoveX < touchActionDownX - threshold && touchActionMoveY > touchActionDownY - threshold && touchActionMoveY < touchActionDownY + threshold) {
      // Left. If the move left was greater than the threshold and not greater than the threshold up or down
    } else if (touchActionMoveX > touchActionDownX + threshold && touchActionMoveY > touchActionDownY - threshold && touchActionMoveY < touchActionDownY + threshold) {
      // Right. If the move right was greater than the threshold and not greater than the threshold up or down
    } else if (touchActionMoveY < touchActionDownY - threshold && touchActionMoveX > touchActionDownX - threshold && touchActionMoveX < touchActionDownX + threshold) {
      // Up. If the move up was greater than the threshold and not greater than the threshold left or right
      if (!this.isScrolling) {
        this.isScrolling = true;
        this.cdr.detectChanges();
      }
      const list = this.list();
      list.allowLeftPanning = false;
      list.allowRightPanning = false;
    } else if (touchActionMoveY > touchActionDownY + threshold && touchActionMoveX > touchActionDownX - threshold && touchActionMoveX < touchActionDownX + threshold) {
      // Down. If the move down was greater than the threshold and not greater than the threshold left or right
      if (!this.isScrolling) {
        this.isScrolling = true;
        this.cdr.detectChanges();
      }
      const list = this.list();
      list.allowLeftPanning = false;
      list.allowRightPanning = false;
    }
    if (this.isScrolling) {
      this._scrollingListEvent.next(true);
    }
  }

  resetPans() {
    // ToDo: Utilizar una lista separada que solo contemple los items involucrados
    for (const pending of this.pendings) {
      pending.isRightPanned = false;
      pending.isLeftPanned = false;
    }
  }

  endPan() {
    this.resetPan();
    this.isPanMoving = false;
  }

  leftPan(args: IListItemPanningEventArgs) {
    args.keepItem = true;
    const pending: PendingDto = this.getPannedPending(args);
    // toDo: Validar si se utilizará esta funcionalidad más adelante
    if (this.hasPanningOptions(pending)) {
      this.resetPans();
      this.setRightPanningActions(pending);
      pending.isRightPanned = false;
      pending.isLeftPanned = true;
    } else {
      // ----------- hasta aquí ------------------------
      pending.isLeftPanned = true;
      this.cdr.detectChanges();
      setTimeout(() => {
        // Se ejecuta tarea "hide"
        this.toggleMenu(null, pending, CommonAction.HIDE);
      }, 500);
    }
    this.pannedPending = Object.assign({}, pending);
  }

  rightPan(args: IListItemPanningEventArgs) {
    args.keepItem = true;
    const pending: PendingDto = this.getPannedPending(args);
    // toDo: Validar si se utilizará esta funcionalidad más adelante
    if (this.hasPanningOptions(pending)) {
      this.resetPans();
      this.setLeftPanningActions(pending);
      pending.isRightPanned = true;
      pending.isLeftPanned = false;
    } else {
      // ----------- hasta aquí ------------------------
      setTimeout(() => {
        pending.isRightPanned = true;
        // Se ejecuta tarea "atender"
        this.toggleMenu(null, pending, this.pendingValue(pending, 'mainAction'));
      }, 100);
    }
    this.pannedPending = Object.assign({}, pending);
  }

  isPendingDateToday(commitmentDate: number | Date) {
    return DateUtil.isToday(commitmentDate);
  }

  isEqualsToCurrentDate(commitmentDate: number | Date) {
    if (commitmentDate == null || typeof commitmentDate === 'undefined') {
      return false;
    }
    return DateUtil.isSameDay(DateUtil.safe(commitmentDate), this.currentDate);
  }

  isPendingValueAvailable(pending: PendingDto, value: string): boolean {
    return (
      pending &&
      ((pending[value] !== null && typeof pending[value] !== 'undefined' && pending[value] !== 'NO_ACTION') ||
        (!pending[value] && this.isPendingTypeValueAvailable(pending.pendingTypeCode, value)))
    );
  }

  isPendingTypeValueAvailable(pendingTypeCode: string, value: string): boolean {
    return this.pendingTypeConfig[pendingTypeCode]?.[value];
  }

  setActionsLocale() {
    for (const pendingTypeCode in this.pendingTypeConfig) {
      if (this.pendingTypeConfig.hasOwnProperty(pendingTypeCode)) {
        const config = this.pendingTypeConfig[pendingTypeCode].availableMenuActions;
        const items: DropdownMenuItem[] = [...config];
        const pendingTypeAction = `${pendingTypeCode}.action`;
        if (this.translate.containsKeyFrom(PendingsUtil.LANG_CONFIG, pendingTypeAction)) {
          this.translate.getFrom(PendingsUtil.LANG_CONFIG, pendingTypeAction).subscribe((tags) => {
            for (const item of items) {
              if (typeof item !== 'undefined') {
                item.text = tags[item.value] || `${pendingTypeCode}.${item.value}`;
              }
            }
          });
        } else {
          this.translate.getFrom(PendingsUtil.LANG_CONFIG, 'action').subscribe((tags) => {
            for (const item1 of items.filter((item) => !!item)) {
              item1.text = tags[item1.value] || `action.${item1.value}`;
            }
          });
        }
      }
    }
  }

  pendingValue(pending: PendingDto, value: string): any {
    if (!this.isPendingValueAvailable(pending, value)) {
      return null;
    }
    if (pending[value] === null || typeof pending[value] === 'undefined') {
      return this.pendingTypeValue(pending.pendingTypeCode, value);
    }
    return pending[value];
  }

  pendingTypeValue(pendingTypeCode: string, value: string, _isNotFromRightPanel?: boolean): any {
    if (!this.isPendingTypeValueAvailable(pendingTypeCode, value)) {
      return null;
    }
    let menuOptions = this.pendingTypeConfig[pendingTypeCode][value];

    //Esta validacion es para filtrar lo elementos del menu de opciones del pendiente(no del panel derecho)
    if (_isNotFromRightPanel) {
      menuOptions = menuOptions.filter((option) => {
        return !this.menuOptionsButtonBlackList.find((value) => value === option.value);
      });
    }
    return menuOptions;
  }

  getPannedPending(args: IListItemClickEventArgs | IListItemPanningEventArgs): PendingDto {
    if (!args.item.contentElement) {
      return null;
    }
    const idx = +args.item.contentElement.parentElement.getAttribute('data-pending-record-id');
    let pendingRecord;
    for (const pending of this.pendings) {
      if (pending.recordId === idx) {
        pendingRecord = pending;
      }
    }
    return pendingRecord;
  }

  leftPanClick(args: IButtonGroupEventArgs) {
    for (const element of this.list().element.nativeElement.querySelector('igx-virtual-helper').array) {
      console.log(element);
    }
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    console.log(' l-args -> ', args, this.pannedPending, args.button['actionName']);
  }

  rightPanClick(args: IButtonGroupEventArgs) {
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    console.log(' r-args -> ', args, this.pannedPending, args.button['actionName']);
  }

  setRightPanningActions(pending: PendingDto) {
    this.rightPanningActions =
      this.pendingTypeConfig[pending.pendingTypeCode].rightPanningActions
        .map((actionName: string): PanningButton => {
          return this.getPanningButtonInstance(actionName, pending.pendingTypeCode);
        })
        .filter((op) => op != null) || [];
  }

  setLeftPanningActions(pending: PendingDto) {
    this.leftPanningActions =
      this.pendingTypeConfig[pending.pendingTypeCode].leftPanningActions
        .map((actionName: string): PanningButton => {
          return this.getPanningButtonInstance(actionName, pending.pendingTypeCode);
        })
        .filter((op) => op != null) || [];
  }

  getPanningButtonInstance(actionName: string, pendingTypeCode: string): PanningButton {
    if (!this.pendingTypeConfig[pendingTypeCode] || !this.pendingTypeConfig[pendingTypeCode].availablePanningActions) {
      return null;
    }
    const actions: PanningActions = this.pendingTypeConfig[pendingTypeCode].availablePanningActions;
    for (const k in actions) {
      if (!actions.hasOwnProperty(k)) {
        continue;
      }
      const action = actions[k];
      if (action.actionName === actionName) {
        return new PanningButton(actionName, {
          icon: action.icon,
          label: action.label
        });
      }
    }
    return null;
  }

  hasPanningOptions(pending: PendingDto): boolean {
    return !!(
      pending &&
      this.pendingTypeConfig[pending.pendingTypeCode] &&
      this.pendingTypeConfig[pending.pendingTypeCode].rightPanningActions &&
      this.pendingTypeConfig[pending.pendingTypeCode].rightPanningActions.length > 1
    );
  }

  toggleMainMenu(item: DropdownMenuItem) {
    switch (item.value) {
      case CommonAction.REFRESH:
        this.filterCalendarActive = false;
        this.refreshAll(true);
        break;
      case PendingsAction.FILTER_FROM_TOMORROW:
        this.hideFuturePendings = !this.hideFuturePendings;
        this.removeFuturePendings();
        this._changedSelectedMenuOptions.next({
          groupByDate: this.pendingListViewGrouped,
          hideFuturePendings: this.hideFuturePendings,
          order: this.pendingListSorted
        });
        break;
      case PendingsAction.EXACT_SEARCH:
        this.exactSearch = !this.exactSearch;
        PendingsLocalConfig.setExactSearch(this.exactSearch);
        break;
      case PendingsAction.GROUP_PENDINGS:
        this.pendingListViewGrouped = !this.pendingListViewGrouped;
        this.checkPendingContainerSize(true);
        this._changedSelectedMenuOptions.next({
          groupByDate: this.pendingListViewGrouped,
          hideFuturePendings: this.hideFuturePendings,
          order: this.pendingListSorted
        });
        break;
      case PendingsAction.SORT_ASC:
        this.sortPendings(PendingsAction.SORT_DESC);
        this.groupPendingListByDate(true);
        this.addOrderDescMenuOption();
        this._changedSelectedMenuOptions.next({
          groupByDate: this.pendingListViewGrouped,
          hideFuturePendings: this.hideFuturePendings,
          order: PendingsAction.SORT_DESC
        });
        break;
      case PendingsAction.SORT_DESC:
        this.sortPendings(PendingsAction.SORT_ASC);
        this.groupPendingListByDate(true);
        this.addOrderAscMenuOption();
        this._changedSelectedMenuOptions.next({
          groupByDate: this.pendingListViewGrouped,
          hideFuturePendings: this.hideFuturePendings,
          order: PendingsAction.SORT_ASC
        });
        break;
      case PendingsAction.FILTER_RANGE:
        this.openCalendar();
        break;
    }
  }

  openCalendar() {
    this.dropDownCalendar().open(this.overlaySettings);
    this.dropDownCalendar().allowItemsFocus = true;
    if (this.isSmallTouchDevice) {
      this.pendingsDropdown().closeDropdown();
    }
  }

  // Método para refrescar pendientes...
  refreshCountPendings() {
    this.countPendings(this.pendings, true);
  }

  // Método usado despúes de atender pendientes no legacy - También refresca datos de la gráfica
  refreshCountPendingsAndChart(pendingRecordId?: number) {
    this.api.get({ cancelableReq: this.$destroy, url: 'pendings/count' }).subscribe((count: number) => {
      this.menuService.changePendingCount(count);
    });
    this.attendedPending = false;
    this.countPendings(this.pendings, false);
    this.refreshChartDataSource();
    if (pendingRecordId) {
      this.refreshGrouping(pendingRecordId);
    }
  }

  refreshProgressPendings() {
    this.cdr.detectChanges();
  }

  isActionAvailable(pending: PendingDto, action: CommonAction) {
    return typeof pending.availableActions.find((value) => value === action) !== 'undefined';
  }

  addDocument(pending: PendingDto) {
    this.toggleMenu(null, pending, CommonAction.ADD_DOCUMENT);
  }

  public verifyRange(dates: Date | Date[]) {
    let firstDate: Date;
    let lastDate: Date;
    if (Array.isArray(dates)) {
      firstDate = dates[0];
      lastDate = dates[dates.length - 1];
    } else {
      firstDate = dates;
      lastDate = dates;
    }
    if (typeof firstDate === 'undefined') {
      this.calendarSearch().deselectDate(this.startDate);
      this.startDate = null;
      this.endDate = null;
    } else {
      this.startDate = firstDate;
      this.isClickedTwice = false;
      if (this.startDate !== lastDate) {
        this.endDate = lastDate;
        this.isClickedTwice = true;
      }
      if (firstDate === lastDate && !this.isClickedTwice) {
        this.endDate = lastDate;
      }
    }
    this.updateCalendarSearchValue();
  }

  updateCalendarSearchValue(): void {
    const filterDatesNotes = this.i18n.filterDatesNotes as DataMap<string>;
    if (this.startDate == null && this.endDate == null) {
      this.calendarSearchValue = filterDatesNotes.noFilter;
    } else if (this.startDate == null && DateUtil.isToday(this.endDate)) {
      this.calendarSearchValue = filterDatesNotes.beforeToday;
    } else if (this.startDate == null) {
      this.calendarSearchValue = filterDatesNotes.tillDate + this.datePipe.transform(this.endDate, this.dateFormat);
    } else if (this.endDate == null && DateUtil.isTomorrow(this.startDate)) {
      this.calendarSearchValue = filterDatesNotes.afterToday;
    } else if (this.endDate == null) {
      this.calendarSearchValue = filterDatesNotes.filterFromDate + this.datePipe.transform(this.startDate, this.dateFormat);
    } else {
      this.calendarSearchValue =
        filterDatesNotes.fromDate +
        this.datePipe.transform(this.startDate, this.dateFormat) +
        filterDatesNotes.toDate +
        this.datePipe.transform(this.endDate, this.dateFormat);
    }
  }

  public onDoneSelected(dropDownCalendar, cancel = false) {
    if (cancel) {
      this.endDate = DateUtil.todayEndTime();
      this.filterCalendarActive = false;
      this.removeFuturePendings();
    } else {
      if (this.startDate != null && this.endDate != null) {
        // Se realiza el conteo del alert de acuerdo a las fechas del calendarSearch
        this.filterCalendarActive = true;
        this._changedSelectedDates.next({ filterApplied: true, start: this.startDate, end: this.endDate });
        this.titleDate = this.translate.instant('between', {
          startDate: StringFormat.titleCase(this.datePipe.transform(this.startDate, this.formatBetween, this.getTimezone(), this.getLang()).substring(0, 5).toString()),
          endDate: StringFormat.titleCase(this.datePipe.transform(this.endDate, this.formatBetween, this.getTimezone(), this.getLang()).substring(0, 5).toString())
        });
      }
      this.refreshCountPendings();
      this.cdr.detectChanges();
    }
    dropDownCalendar.close();
  }

  public onClosing() {
    if (!this.isClickedTwice) {
      this.calendarSearch().selectDate(this.startDate);
    }
  }

  public onOpening() {
    setTimeout(() => {
      this.searchBar().nativeElement.blur();
      this.dropDownCalendar().element.focus();
    }, 0);
  }

  openDetail(pending: PendingDto, pendingActions: DropdownMenuComponent, $event) {
    if (pending.availableActions.find((name) => name === CommonAction.OPEN_DETAIL)) {
      this.toggleMenu(null, pending, CommonAction.OPEN_DETAIL);
    } else if (pending.availableActions.find((name) => name === CommonAction.ATTEND)) {
      this.toggleMenu(null, pending, CommonAction.ATTEND);
    } else {
      pendingActions.openDropdown($event);
    }
  }

  setHrefLinks(pendings: PendingDto[]) {
    let module: Module;
    for (const pending of pendings) {
      if (pending.availableActions.find((name) => name === CommonAction.OPEN_DETAIL)) {
        let path = '';
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        if (pending['fillType'] === FillType.PLANNER_TASK) {
          path = `menu/planner/task-detail/${pending.recordId}`;
        } else if (this.isActivityPending(pending)) {
          path = this.getActivityHrefLink(pending);
        } else {
          module = EnumUtil.getValue(Module, pending.module, 'string');
          switch (module) {
            case Module.FORMULARIE:
              path = `menu/legacy/${this.getFormHrefLink(pending as FormPendingDto)}`;
              break;
            case Module.DOCUMENT:
              path = `menu/legacy/v.documentos.view?vchState=Edit&intDocumentoId=${pending.recordId}`;
              break;
          }
        }
        pending.href = `../qms/${this.getLang()}/${path}`;
      } else if (pending.availableActions.find((name) => name === CommonAction.ATTEND)) {
        this.legacyTask(pending, true);
      }
    }
  }

  onClickToggleMenu(event, item: DropdownMenuItem, pending: PendingDto, mainAction?: string, comment?: string) {
    if (event?.ctrlKey) {
      return;
    }
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.toggleMenu(item, pending, mainAction, comment);
  }

  onClickAnchor(pending: PendingDto, pendingActions: DropdownMenuComponent, event) {
    if (event?.ctrlKey) {
      return;
    }
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.openDetail(pending, pendingActions, event);
  }

  toggleMenu(item: DropdownMenuItem, pending: PendingDto, mainAction?: string, comment?: string) {
    if (pending.busy) {
      this.noticeService.notice('Espere un momento por favor...');
      return;
    }
    const actionName = mainAction || item?.value || null;
    if (!actionName && (!pending || !item?.selected)) {
      this.clickedPending = null;
      return;
    }
    if (this.clickedPending === null || this.clickedPending.recordId !== pending.recordId) {
      this.clickedPending = pending;
    }
    const documentsService = this.getLinkService(pending, LinkedSelector.DOCUMENTS);
    const filesService = this.getLinkService(pending, LinkedSelector.FILES);
    const commentsService = this.getLinkService(pending, LinkedSelector.COMMENTS);

    switch (actionName) {
      case CommonAction.HIDE:
        this.clickedPending.hidden = true;
        this.refreshCountPendings();
        if (this.isSmallTouchDevice) {
          this.noticeService.notice('Se oculta temporalmente.');
        } else {
          this.noticeService.notice('Se oculta temporalmente, volverá a aparecer cuando recarge la pantalla.');
        }
        break;
      case CommonAction.ADD_DOCUMENT:
      case CommonAction.VIEW_DOCUMENT: {
        if (!documentsService) {
          return;
        }
        pending.busy = true;
        const documents = this.documents();
        documents.value = [];
        const deleteDocumentsServices = this.getLDeleteServices(pending, LinkedSelector.DOCUMENTS);
        if (deleteDocumentsServices && deleteDocumentsServices.length > 0) {
          documents.deleteServices = deleteDocumentsServices;
        }
        this.api.get({ cancelableReq: this.$destroy, url: `${documentsService}/data-source/${pending.recordId}` }).subscribe((values) => {
          if (actionName === CommonAction.VIEW_DOCUMENT && values && values.length === 0) {
            this.noticeService.notice('No hay documentos asociados');
          } else {
            const documentsValue = this.documents();
            documentsValue.dataUrl = `${documentsService.replace(/\/documents/, '')}/linked/documents`;
            documentsValue.value = values || [];
            documentsValue.openDialog();
          }
          pending.busy = false;
        });
        break;
      }
      case CommonAction.VIEW_ATTACHMENT:
        if (!filesService) {
          return;
        }
        pending.busy = true;
        this.files().value = [];
        this.api.get({ cancelableReq: this.$destroy, url: `${filesService}data-source/${pending.recordId}` }).subscribe((values) => {
          if (values && values.length > 0) {
            this.filesDialog().open();
            this.files().value = values;
          } else {
            this.noticeService.notice('No hay archivos asociados');
          }
          pending.busy = false;
        });
        break;
      case CommonAction.VIEW_COMMENT:
        if (!commentsService) {
          return;
        }
        pending.busy = true;
        this.comments().value = [];
        this.api.get({ cancelableReq: this.$destroy, url: `${commentsService}/data-source/${pending.recordId}` }).subscribe((values) => {
          if (values && values.length > 0) {
            this.commentsDialog().open();
            this.comments().value = values;
          } else {
            this.noticeService.notice('No hay comentarios asociados');
          }
          pending.busy = false;
        });
        break;
      case CommonAction.ADD_COMMENT: {
        const saveEntity = {
          commentEntity: {
            id: null,
            description: comment || null,
            lastModifiedDate: new Date(),
            lastModifiedBy: Session.getUserName(),
            stage: ''
          },
          dropdownItem: null,
          grid: null
        };
        if (saveEntity.commentEntity.description === null) {
          const inputDialog: DialogInput = {
            message: 'root.common.message.add-comment',
            inputMaxLimit: 4000
          };
          this.dialogService.input(inputDialog).then((value: DialogResult) => {
            saveEntity.commentEntity.description = value.inputValue;
            this.saveComment(saveEntity);
          });
        } else {
          this.saveComment(saveEntity);
        }
        break;
      }
      case CommonAction.EDIT_REQUEST:
        this.showRequestChangeDialog(pending);
        break;
      case CommonAction.ADD_ATTACHMENT: {
        const selector = EnumUtil.getName(LinkedSelector, LinkedSelector.FILES).toLowerCase();
        if (!this.clickedPending[selector]) {
          this.clickedPending[selector] = [];
        }
        break;
      }
      default:
        if (this.debug()) {
          console.log('toggleMenuByModule --> toggleMenu', item, pending, mainAction);
        }
        // las acciones especificas por módulo se activan por "subscribe"
        this.toggleMenuByModule(item, pending, mainAction, comment);
        this.cdr.detectChanges();
        break;
    }
  }

  showRequestChangeDialog(pending: PendingDto) {
    if (!this.isPendingRequestAvailable) {
      return;
    }
    const requestChangeDialog = this.requestChangeDialog();
    if (requestChangeDialog?.length) {
      const args: PendingsRequestChange = getPendingRequestChangeInstance(pending);
      const s = requestChangeDialog
        .at(0)
        ?.open(args)
        .subscribe(
          (action: PendingsRequestAction) => {
            switch (action) {
              case PendingsRequestAction.BUSY:
                pending.busy = true;
                break;
              case PendingsRequestAction.SAVED:
                pending.hidden = true;
                this.refreshCountPendings();
                break;
              case PendingsRequestAction.ERROR:
              case PendingsRequestAction.CANCEL:
                pending.busy = false;
                pending.hidden = false;
                this.refreshCountPendings();
                break;
            }
            this.cdr.detectChanges();
          },
          (_e) => {}
        );
      this.subs.push(s);
      return;
    }
  }

  toggleMenuByModule(item: DropdownMenuItem, pending: PendingDto, mainAction?: string, comment?: string) {
    const actionName: string = (mainAction || item?.value || '').toString() || null;
    if (!actionName) {
      // eslint-disable-next-line prefer-rest-params
      console.error('Invalid toggleMenuByModule call', arguments);
      return;
    }
    const pickedPending: PickedPending = {
      menuItem: item || {
        value: actionName,
        text: actionName
      },
      pending: pending,
      comment: comment || undefined
    };
    const module = EnumUtil.getValue(Module, pending.module, 'string');
    if (actionName === CommonAction.ATTEND && isActivitySubmodule(module)) {
      this.legacyAttend(pickedPending);
      return;
    }
    if (this.isActivityPending(pending) || getNormalizedModule(module) === Module.ACTIVITY) {
      // AUDIT, ACTION, PLANNER y ACTIVITY
      this.pendingsActivities().pickMenuOption(pickedPending as ActivityPickedPending);
    } else {
      switch (module) {
        case Module.DOCUMENT:
          this.attendDocument(actionName, pickedPending as DocumentPickedPending);
          break;
        case Module.FORMULARIE:
          this.attendFormRequest(actionName, pickedPending as FormRequestPickedPending);
          break;
        default:
          if (actionName === CommonAction.ATTEND) {
            this.legacyAttend(pickedPending);
          } else {
            console.error('Error! el pendiente no está configurado para ser atendido', pending);
          }
          break;
      }
    }
  }

  attendFormRequest(actionType: string, pickedPending: FormRequestPickedPending) {
    const pendingTypeCode = pickedPending.pending.pendingTypeCode;
    const attendService = PENDING_TYPE_CONFIG[pendingTypeCode].attendService;
    switch (actionType) {
      case FormRequestAction.AUTHORIZE:
      case FormRequestAction.REJECT:
        // empty
        break;
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigateLegacy(this.getFormHrefLink(pickedPending.pending as FormPendingDto), Module.FORMULARIE);
        return;
      default:
        this.legacyAttend(pickedPending);
        return;
    }
    const inputMinLimit = 10;
    this.dialogService
      .input({
        message: this.translate.instant(`${pendingTypeCode}.message-${actionType}`),
        inputMaxLimit: 255,
        inputMinLimit: inputMinLimit,
        inputLimitText: this.translate.instant(`${pendingTypeCode}.inputLimitText`),
        dialogLabelInputMessage: this.translate.instant(`${pendingTypeCode}.labelInputLimitText`),
        inputMessage: this.translate.instant(`${pendingTypeCode}.inputMinLimit`).replace('{inputMinLimit}', inputMinLimit)
      })
      .then((result: DialogResult) => {
        pickedPending.pending.busy = true;
        this.cdr.detectChanges();
        const url = `${attendService}/${actionType.toLowerCase()}`;
        this.api
          .post({
            url: url,
            cancelableReq: this.$destroy,
            postBody: {
              text: result.inputValue,
              value: pickedPending.pending.formRequestId
            }
          })
          .subscribe({
            next: (r) => {
              const logicResponse = EnumUtil.getName(RequestAuthorizeResponse, r.logicResponse);
              switch (logicResponse) {
                case 'VERIFY_DEPARTMENT_MANAGER':
                  this.noticeService.notice(this.translate.instant(`${pendingTypeCode}.successVerifyDepartmentManager`));
                  break;
                case 'VERIFY_APPROVED':
                  this.noticeService.notice(this.translate.instant(`${pendingTypeCode}.successVerifyApproved`));
                  break;
                case 'VERIFY_REPEATED_DATA':
                  this.noticeService.notice(this.translate.instant(`${pendingTypeCode}.successVerifyRepeatedData`));
                  break;
                case 'VERIFY_FINAL':
                case 'AUTHORIZED':
                case 'REJECTED':
                  this.noticeService.notice(this.translate.instant(`${pendingTypeCode}.success-${actionType}`));
                  break;
              }
              pickedPending.pending.busy = false;
              pickedPending.pending.hidden = true;
              this.refreshCountPendingsAndChart(pickedPending.pending.pendingRecordId);
              this.cdr.detectChanges();
            },
            error: (_e) => {
              pickedPending.pending.busy = false;
              this.cdr.detectChanges();
            }
          });
      });
  }

  attendDocument(actionName: string, pickedPending: DocumentPickedPending) {
    switch (actionName) {
      case CommonAction.ATTEND:
        this.legacyAttend(pickedPending);
        break;
      case DocumentAction.VIEW_NEW_FILE:
      case DocumentAction.VIEW_NEW_FORM: {
        const pendingRequest = pickedPending.pending as DocumentRequestPendingDto;
        switch (pendingRequest.pendingTypeCode) {
          case DOCUMENT_APE.DOCUMENT_TO_ASSIGN_READER:
          case DOCUMENT_APE.DOCUMENT_TO_READ:
            if (pendingRequest.documentId) {
              this.openViewerByDocumentId(pendingRequest.documentId, pendingRequest.module);
            } else {
              this.openViewerByRequest(pendingRequest.requestId, pendingRequest.module);
            }
            break;
          default:
            this.openViewerByRequest(pendingRequest.requestId, pendingRequest.module);
            break;
        }
        break;
      }
      case DocumentAction.VIEW_LAST_VERSION_FILE:
      case DocumentAction.VIEW_LAST_VERSION_FORM: {
        const pendingFormRequest = pickedPending.pending as DocumentRequestPendingDto;
        this.openViewerByRequest(pendingFormRequest.lastVersionRequestId, pendingFormRequest.module);
        break;
      }
      default:
        console.error('Error! el pendiente no está configurado para ser atendido', pickedPending);
        break;
    }
  }

  openViewerByRequest(requestId: number, module: Module) {
    const link = `v-document-viewer.view?requestId=${requestId}`;
    this.menuService.navigateLegacy(link, module);
  }

  openViewerByDocumentId(documentId: number, module: Module) {
    const link = `v-document-viewer.view?id=${documentId}`;
    this.menuService.navigateLegacy(link, module);
  }

  saveComment(evt: SaveCommentEvt) {
    this.addingComment = true;
    this.cdr.detectChanges();
    const _commentEntity = evt.commentEntity;
    this.clickedPending.busy = true;
    const pending = this.clickedPending;
    if (!pending) {
      console.error('Invalid state, pending record is missing.');
      _commentEntity.stage = 'ERROR-SAVING';
      return;
    }
    const pendingTypeConfig: PendingTypeConfig = this.pendingTypeConfig[pending.pendingTypeCode] || {
      module: pending.module
    };
    const moduleConfig: ModuleUnitConfig = this.moduleConfig[pending.module] || {
      icon: MODULE_ICON[pending.module] || 'help',
      menuActions: [],
      selectedMenuActions: [],
      availableMenuActions: []
    };
    const commentsService = pendingTypeConfig.commentsService || moduleConfig.commentsService || null;
    const id = this.clickedPending.recordId || null;
    if (!id || !commentsService) {
      console.error('Invalid state, pending record is missing data.', id, commentsService);
      _commentEntity.stage = 'ERROR-SAVING';
      return;
    }
    const s = this.api.post({ url: `${commentsService}/save/${id}`, cancelableReq: this.$destroy, postBody: _commentEntity.description }).subscribe((result) => {
      _commentEntity.id = result.itemId;
      if (result.createdDate) {
        _commentEntity.lastModifiedDate = new Date(result.createdDate);
      }
      _commentEntity.stage = result.stage;
      _commentEntity.activityCommitmentTask = pending.commitmentTask;
      if (typeof this.onCommentAdd === 'function') {
        this.onCommentAdd();
        this.onCommentAdd = null;
      } else if (this.onCommentAdd) {
        console.log('this.onCommentAdd: ', this.onCommentAdd);
      }
      this.noticeService.notice('root.common.message.saved-comment');
      pending.comments.push(_commentEntity);
      pending.lastAction = _commentEntity.description;
      this.addingComment = false;
      this.clickedPending.busy = false;
      this.dialogDetailOpen = this.clickedPending.comments !== null && this.clickedPending.comments.filter((c) => c.description.length > 350).length > 0;
      this.cdr.detectChanges();
      s.unsubscribe();
    });
  }

  /**
   *
   * @param widgetRefreshing Tipo de carga de información de los pendientes
   * @param pendingsSource Fuente de datos de los pendientes
   */
  public refreshAll(widgetRefreshing?: boolean, pendingsSource = null) {
    if (this.isLoading) {
      return;
    }
    this.isLoading = true;
    this.menuWidgetService.changePendingsLoading(this.isLoading);
    this.isRefresingData = widgetRefreshing ? widgetRefreshing : false;
    if (pendingsSource != null) {
      this.fillPendings(pendingsSource);
    } else {
      this.api.get({ cancelableReq: this.$destroy, url: 'pendings/data-source', handleFailure: false }).subscribe({
        next: (result: PendingDataSource) => {
          this.fillPendings(result);
        },
        error: (error) => {
          this.isLoading = false;
          ErrorHandling.notifyError(error, this.navLang);
          this.cdr.detectChanges();
        }
      });
    }
  }

  public refreshData(removePending: BasicPendingData) {
    if (this._lastDataSource?.pendings) {
      const length = this._lastDataSource.pendings.length;
      this._lastDataSource.pendings = this._lastDataSource.pendings.filter((p) => {
        return !(p.module === removePending.module && p.recordId === removePending.recordId);
      });
      if (length > this._lastDataSource.pendings.length) {
        if (this._lastDataSource.apeCount[removePending.pendingTypeCode]) {
          this._lastDataSource.apeCount[removePending.pendingTypeCode] -= 1;
        }
        if (this._lastDataSource.moduleCount[removePending.module]) {
          this._lastDataSource.moduleCount[removePending.module].count -= 1;
        }
        this.fillPendings(this._lastDataSource);
      } else {
        /**
         * Si el pendiente "despareció" refrescar todo
         * */
        this.refreshAll();
      }
    } else {
      /**
       * Si no hay pendientes respaldados no queda
       * de otra más que refrescar todo
       * */
      this.refreshAll();
    }
  }

  downloadFile(file: IFileData) {
    if (file.supportedPdfViewer) {
      this.viewFile(file);
    } else {
      this.forceDownloadFile(file);
    }
  }

  isSupportedPdfViewer(file: IFileData) {
    return SUPPORTED_OPEN_BROWSER_CONTENT_TYPES.indexOf(file.contentType) !== -1;
  }

  viewFile(file: IFileData): void {
    const url = `v-document-viewer.view?fileId=${file.id}&documentId=-1&requestId=-1`;
    this.menu.navigateLegacy(url, Module.DOCUMENT);
  }

  viewDocument(document: IDocumentEntity): void {
    this.openViewerByDocumentId(document.id, Module.DOCUMENT);
  }

  forceDownloadFile(file: IFileData) {
    const downloadAnchor = this.downloadAnchor().nativeElement;
    downloadAnchor.href = `${RestApiModule.url()}files/${file.id}`;
    downloadAnchor.click();
    downloadAnchor.href = 'javascript: void(0);';
  }

  isTimeworkAvailableByPending(pending: PendingDto): boolean {
    return pending.workingHoursAvailable && this.isTimesheetAvailable;
  }

  filterByPendingTypeCode(item: DropdownMenuItem): void {
    if (item.selected) {
      if (this.searchPendingTypes.some((pendingType) => pendingType.value === item.value)) {
        return;
      }
      this.searchPendingTypes.push(item);
      this._changedSearchPendingTypes.next(this.searchPendingTypes);
      this.cdr.detectChanges();
      this.updateBarTitle();
    } else {
      this.removeSearchPendingTypeCode(item);
    }
  }

  removeSearchPendingTypeCode(pendingType: DropdownMenuItem) {
    pendingType.selected = false;
    this.searchPendingTypes = this.searchPendingTypes.filter((current) => pendingType.value !== current.value);
    const moduleName = EnumUtil.getName(Module, this.pendingTypeConfig[pendingType.value].module);
    const moduleConfigData = this.moduleConfig[moduleName];
    const idxSelectedItem = moduleConfigData.selectedMenuActions.indexOf(pendingType.value);
    if (idxSelectedItem !== -1) {
      moduleConfigData.selectedMenuActions.splice(idxSelectedItem, 1);
    }
    this._changedSearchPendingTypes.next(this.searchPendingTypes);
    // Actualizamos los filtros aplicados cuando se remueve un chip
    this.filtersApplied = [];
    for (const item of this.searchPendingTypes) {
      this.filtersApplied.push(item.value);
    }
    this.cdr.detectChanges();
    this.updateBarTitle();
  }

  getPendingIconName(pendingTypeCode: any, moduleName?: string): string {
    if (!moduleName && this.pendingTypeConfig[pendingTypeCode]) {
      moduleName = EnumUtil.getName(Module, this.pendingTypeConfig[pendingTypeCode].module);
    }
    if (this.moduleConfig[moduleName]) {
      return this.moduleConfig[moduleName].icon || null;
    }
    return MODULE_ICON[moduleName] || null;
  }

  legacyAttend(pickedPending: PickedPending): void {
    const pending: PendingDto = pickedPending.pending;
    pending.busy = true;
    this.api
      .get({ cancelableReq: this.$destroy, url: `pendings/is-feasible/${pending.pendingTypeCode}/${pending.pendingRecordId}` })
      .subscribe((result: PendingFeasibleDto) => {
        switch (result.valid) {
          case 'VALID':
            this.legacyTask(pending);
            break;
          case 'BUSY':
            this.dialogService.info(`El pendiente de clave ${pending.code} ya está siendo atendido por ${result.blockedBy}.`);
            break;
          default: {
            let message;
            switch (result.status) {
              case 'INACTIVE':
              case 'ATTENDED':
                message = `La tarea con clave ${pending.code} ya fue atendida.`;
                break;
              case 'HISTORY':
                message = '' + 'Usted ya no es el responsable de esta tarea ' + 'debido a que ya fue escalado.';
                break;
            }
            this.noticeService.notice(message);
            pending.hidden = true;
            this.refreshCountPendingsAndChart(pending.pendingRecordId);
            break;
          }
        }
        pending.busy = false;
        this.cdr.detectChanges();
      });
  }

  legacyTask(pending: PendingDto, setHref = false) {
    let baseLink: string;
    if (pending.pendingTypeCode === DOCUMENT_APE.DOCUMENT_TO_RENEW) {
      baseLink = 'v-attender-redirect.view';
    } else {
      baseLink = 'v-attender-wrapper.view';
    }
    if (pending.module === Module.ACTION && !setHref) {
      this.handleRedirectAction(pending);
      return;
    }
    if (pending.module === Module.FORMULARIE && !setHref) {
      this.handleRedirectFormularie(pending as FormPendingDto);
      return;
    }
    if (pending.module === Module.FORMULARIE && setHref) {
      pending.href = `../qms/${this.getLang()}/menu/legacy/${this.surveyFillUrl(pending as FormPendingDto)}`;
      return;
    }
    const module = pending.module.toString().toLowerCase();
    const code = this.camelcase(pending.pendingTypeCode);
    if (pending.module === Module.AUDIT && !setHref) {
      const alreadyHandled = this.handleRedirectAudit(pending);
      if (alreadyHandled) {
        return;
      }
    } else if (pending.module === Module.AUDIT && setHref) {
      if (pending.pendingTypeCode === AUDIT_APE.AUDIT_TO_CONFIRM_DATE) {
        const legacyUrl = 'v.audit.individual.handle.view';
        const baseUrl = `../qms/${this.getLang()}/menu/legacy/${legacyUrl}`;
        pending.href = `${baseUrl}?id=${pending.recordId}&moduleName=${module}&pendingCamelName=${code}&pendingRecordId=${pending.pendingRecordId}&recordId=${pending.recordId}`;
        return;
      }
      if (pending.pendingTypeCode === AUDIT_APE.AUDIT_TO_FILL_BY_LEADER) {
        const legacyUrl = 'v.audit.survey.capture.view';
        const baseUrl = `../qms/${this.getLang()}/menu/legacy/${legacyUrl}`;
        pending.href = `${baseUrl}?id=${pending.recordId}&task=fill&moduleName=${module}&pendingCamelName=${code}&pendingRecordId=${pending.pendingRecordId}&recordId=${pending.recordId}`;
        return;
      }
    }
    const link = `${baseLink}?moduleName=${module}&pendingCamelName=${code}&pendingRecordId=${pending.pendingRecordId}&pendingCode=${encodeUrlParameter(pending.code)}&recordId=${pending.recordId}&trail=param&fromPath=pendings&requestMode=PREVIEW`;
    const text = this.translate.instant(pending.pendingTypeCode);
    if (!setHref) {
      this.loader.show();
      this.menuService.navigateLegacy(link, pending.module, text);
      this.taskOpenDetail.emit(true);
    } else {
      pending.href = `../qms/${this.getLang()}/menu/legacy/${link}`;
    }
  }

  public handleRedirectAction(pending: PendingDto): void {
    this.loader.show();
    const link = `v.action.by.type.view?pendingRecordId=${pending.pendingRecordId}&id=${pending.recordId}&accion${pending.code}&intAccionGenericaId=${pending.recordId}`;
    this.menuService.navigateLegacy(link, pending.module, this.translate.instant(pending.pendingTypeCode));
    this.taskOpenDetail.emit(true);
  }

  public handleRedirectFormularie(pending: FormPendingDto): void {
    this.menuService.navigateLegacy(this.surveyFillUrl(pending), Module.NONE, pending.documentName || '');
  }

  public handleRedirectAudit(pending: PendingDto): boolean {
    switch (pending.pendingTypeCode) {
      case AUDIT_APE.AUDIT_TO_CONFIRM_DATE: {
        this.loader.show();
        const link = `v.audit.individual.handle.view?id=${pending.recordId}&moduleName=${pending.module.toString().toLowerCase()}&pendingCamelName=${this.camelcase(
          pending.pendingTypeCode
        )}&pendingRecordId=${pending.pendingRecordId}&recordId=${pending.recordId}&task=confirm`;
        this.menuService.navigateLegacy(link, pending.module, this.translate.instant(pending.pendingTypeCode));
        this.taskOpenDetail.emit(true);
        return true;
      }
      case AUDIT_APE.AUDIT_TO_FILL_BY_LEADER: {
        this.loader.show();
        const linkToFillByLeader = `v.audit.survey.capture.view?id=${pending.recordId}&task=fill&moduleName=${pending.module
          .toString()
          .toLowerCase()}&pendingCamelName=${this.camelcase(pending.pendingTypeCode)}&pendingRecordId=${pending.pendingRecordId}&recordId=${pending.recordId}`;
        this.menuService.navigateLegacy(linkToFillByLeader, pending.module, this.translate.instant(pending.pendingTypeCode));
        this.taskOpenDetail.emit(true);
        return true;
      }
      default:
        console.warn(`Pending ${pending.pendingTypeCode} use 'v-attender-wrapper' navigation until menuService it be supported`);
        return false;
    }
  }

  commentRequired(progress: ChangeProgress, pending: PendingDto, action: string, rollbackData: string[]): void {
    // Se inicializa el pendiente actual
    if (!pending.commentRequired) {
      pending.commentRequired = {
        required: false,
        action: action,
        control: new UntypedFormControl(),
        value: '',
        rollbackData: []
      };
    }
    if (!progress.apply && pending.commentRequired.action === ImplementAction.PROGRESS) {
      pending.commentRequired.action = ImplementAction.NOT_APPLY;
    } else if (progress.apply && pending.commentRequired.action === ImplementAction.NOT_APPLY) {
      pending.commentRequired.action = ImplementAction.PROGRESS;
    }
    // Se respalda el pendiente actual
    if (!pending.commentRequired.required) {
      pending.commentRequired.rollbackData = [];
      for (const key of rollbackData) {
        pending.commentRequired.rollbackData.push({
          key: key,
          value: pending[key]
        });
      }
    }
    // Se habilita el recuadro de comentarios
    pending.commentRequired.required = true;
    // Se da FOCO al textarea
    if (!progress.keepFocus) {
      DomUtil.focusTextArea(`commentBox${pending.pendingRecordId}`);
    }
    this.cdr.detectChanges();
  }

  public hasEnablestopwtach() {
    this.timesheetService.stopStopwatchFromPendingListService(true);
  }

  commentRequiredSaveTimesheet(event: Event, pending: PendingDto, commentBox): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!pending.commentRequired.value.replace(/\s+/g, '').trim()) {
      pending.commentRequired.control.markAsTouched();
      commentBox.focus();
      commentBox.select();
      this.noticeService.notice(this.translate.instant('comment-required'));
      return;
    }
    if (this.isStopwatchrunning) {
      this.noticeService.notice(
        this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', { date: DateUtil.format(this.stopwatchRunningDate, 'DD/MM/YYYY') })
      );
      return;
    }
    // ToDo: Mover a una sola transaccion
    const plannerTask: Partial<PlannerTaskData> = pending.plannerTask || {};
    const plannerStatusActive = plannerTask.plannerStatus === PlannerStatus.ACTIVE;
    this.timesheetService
      .openNewTimesheet(pending.commentRequired.value, [pending.code], {
        clientId: plannerTask.clientId || null,
        clientDescription: plannerTask.clientName || null,
        plannerId: plannerTask.plannerId || null,
        plannerDescription: plannerTask.plannerName || null,
        activityId: +plannerTask.value || null,
        activityDescription: plannerTask.text || null,
        clientRecordLocked: !!plannerTask.clientId,
        plannerRecordLocked: this.evaluatePlannerRecordLocked(plannerTask) && plannerStatusActive,
        taskRecordLocked: this.evaluateTaskRecordLocked(plannerTask) && plannerStatusActive,
        pendingRecordId: pending.pendingRecordId || null,
        activityPlannedId: pending.recordId || null,
        activityPlannedCode: pending.code || null,
        activityPlannedDescription: pending.description || null,
        activityPlannedModule: pending.module || null,
        hasEnableStopwatch: false,
        plannerStatus: plannerTask.plannerStatus || null
      })
      .then(
        (value: TimesheetDto) => {
          this.commentRequiredSaveAction(pending, value.registry);
          this.cdr.detectChanges();
        },
        (value: TimesheetDto) => {
          if (this.debug()) {
            console.log('Cancelled open new timesheet.', value);
          }
        }
      );
  }

  hasNotApplyAccess(pending: PendingDto): boolean {
    if (pending?.availableActions?.length > 0) {
      return pending.availableActions.indexOf(ImplementAction.NOT_APPLY) !== -1;
    }
    return false;
  }

  commentRequiredSave(event: Event, pending: PendingDto, commentBox): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!pending.commentRequired.value.replace(/\s+/g, '').trim()) {
      pending.commentRequired.control.markAsTouched();
      commentBox.focus();
      commentBox.select();
      this.noticeService.notice(this.translate.instant('comment-required'));
      return;
    }
    this.commentRequiredSaveAction(pending, pending.commentRequired.value);
  }

  commentRequiredCancel(event: Event, pending: PendingDto, _index: number): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!pending.commentRequired.rollbackData) {
      return;
    }
    for (const kv of pending.commentRequired.rollbackData) {
      // Cuando el rollback del progreso es nulo se agrega por defecto 0
      if (pending.commentRequired.action === ImplementAction.PROGRESS && kv.key === 'progress' && !kv.value) {
        pending[kv.key] = 0;
      } else {
        pending[kv.key] = kv.value;
      }
    }
    pending.commentRequired.rollbackData = [];
    pending.commentRequired.required = false;
    this.cdr.detectChanges();
  }

  commentKeyDown(pending: PendingDto, commentBox: any, event: { altKey: boolean; key: string }): void {
    const isEnter = event.key === 'Enter' || event.key === 'NumpadEnter';
    if (event.altKey && isEnter) {
      pending.commentRequired.value += '\r\n';
    } else if (isEnter) {
      if (pending.workingHoursAvailable) {
        this.commentRequiredSaveTimesheet(null, pending, commentBox);
      } else {
        this.commentRequiredSave(null, pending, commentBox);
      }
    }
  }

  camelcase(str: string): string {
    return str
      .toLowerCase()
      .replace(/-(.)/g, (_, x) => x.toUpperCase())
      .replace(/_(.)/g, (_, x) => x.toUpperCase());
  }

  refreshChartDataSource() {
    const chartDataSource = [];
    let countPendings = 0;
    for (const moduleKey in this.moduleConfig) {
      // Solo se contemplan módulos disponibles
      if (!this.moduleConfig.hasOwnProperty(moduleKey) || !this.isModuleAvailable(moduleKey)) {
        continue;
      }
      // Se agrega el porcentaje de las tareas para la barra de porcentaje
      const countTasks = this.moduleConfig[moduleKey].count || 0;
      countPendings += countTasks;
      const value: DataMap = {
        tasks: countTasks,
        tasksPercent: Math.floor((countTasks * 100) / this.pendings.length) || 0,
        moduleKey: moduleKey,
        pendingsType: this.moduleConfig[moduleKey].menuActions
      };
      value[this.chartModuleKey] = this.translate.instant(`root.common.module.names.${moduleKey}`);
      chartDataSource.push(value);
    }
    // Se agrega este item para mostrar o no el emptystate de la gráfica.
    const totalPendings: DataMap = {
      totalPendings: countPendings
    };
    chartDataSource.push(totalPendings);
    this.chartData.emit(chartDataSource);
  }

  resetFocus() {
    if (this.clickedPending !== null && this.clickedPending !== undefined) {
      this.clickedPending.focused = false;
    }
    this.clickedPending = null;
    this.clickedPendingIdx = null;
    this.clickedPendingId = null;
    this.clickedPendingApe = null;
    this.cdr.detectChanges();
  }

  resetCalendarSearch() {
    this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_TODAY);
  }

  filterCalendarSearch(filter = PendingsAction.FILTER_FROM_TOMORROW, date?: Date) {
    let noticeMessage = null;
    if (!this.filterCalendarActive) {
      const noticeMessages = this.i18n.noticeMessages as DataMap<string>;
      const calendarSearch = this.calendarSearch();
      const calendarSearchValue = this.calendarSearch();
      const calendarSearchVal = this.calendarSearch();
      switch (filter) {
        case PendingsAction.FILTER_UNTIL_TODAY:
          this.currentDate = DateUtil.today();
          this.setTitleDate();
          this.startDate = null;
          this.endDate = DateUtil.todayEndTime();
          if (calendarSearch) {
            calendarSearch.selectDate(DateUtil.todayEndTime());
          }
          noticeMessage = noticeMessages?.showingPendings || null;
          // Actualizamos los datos del storage
          this.filterCalendarActive = false;
          this._changedSelectedDates.next({ filterApplied: false, start: this.startDate, end: this.endDate });
          break;
        case PendingsAction.FILTER_FROM_TOMORROW:
          this.currentDate = DateUtil.tomorrow();
          this.setTitleDate();
          this.startDate = DateUtil.tomorrow();
          this.endDate = null;
          if (calendarSearchValue) {
            calendarSearchValue.selectDate(DateUtil.tomorrow());
          }
          noticeMessage = noticeMessages?.pendingsAfterToday || null;
          // Actualizamos los datos del storage
          this.filterCalendarActive = false;
          this._changedSelectedDates.next({ filterApplied: false, start: this.startDate, end: this.endDate });
          break;
        case PendingsAction.FILTER_UNTIL_SOMEDAY:
          this.endDate = date;
          this.startDate = null;
          if (calendarSearchVal) {
            calendarSearchVal.selectDate(date);
          }
          this.filterCalendarActive = false;
          this._changedSelectedDates.next({ filterApplied: false, start: this.startDate, end: this.endDate });
          break;
      }
    }
    this.refreshCountPendings();
    if (this.isPendingsOpen()) {
      this.noticeService.notice(noticeMessage);
    }
    this.cdr.detectChanges();
  }

  setFocus(pending: PendingDto, idx: number, persistFocus = true) {
    ConfigPendings.setScrollPosition(idx);
    if (idx > 0) {
      if (this.clickedPendingIdx === idx) {
        return;
      }
    }
    if (this.clickedPending != null) {
      this.clickedPending.focused = false;
    }
    pending.focused = true;
    if (this.isScreenLarge) {
      this.isRightPanelHidden = false;
    }
    if (this.clickedPending !== pending) {
      this.historyLoading = true;
    } else {
      this.activitiesHistory()?.loadData();
    }
    this.clickedPendingIdx = idx;
    this.setClickedPending(pending, idx, persistFocus);
  }

  public setClickedPending(pending: PendingDto, _idx: number, persistFocus: boolean): void {
    this.clickedPending = pending;
    this.clickedPendingId = pending.pendingRecordId;
    this.clickedPendingApe = pending.pendingTypeCode;
    if (persistFocus) {
      this._changedSelectedIndex.next(true);
    }
    if (!this.isRightPanelHidden) {
      this.loadLinkedData();
    }
  }

  preventRighPanel(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
  }

  setFocusBefore() {
    if (this.clickedPendingIdx - 1 < 0) {
      return;
    }
    this.clickedPendingIdx--;
    const pendingVirtualizer = this.pendingVirtualizer();
    if (pendingVirtualizer?.igxForOf) {
      this.setFocus(pendingVirtualizer.igxForOf[this.clickedPendingIdx], this.clickedPendingIdx);
    }
  }

  setFocusNext() {
    const filteredPendings = this.pendingVirtualizer().igxForOf;
    if (this.clickedPendingIdx + 1 >= filteredPendings.length) {
      return;
    }
    this.clickedPendingIdx++;
    this.setFocus(filteredPendings[this.clickedPendingIdx], this.clickedPendingIdx);
  }

  loadLinkedData() {
    this.loadLinkedSelector(LinkedSelector.FILES);
    this.loadLinkedSelector(LinkedSelector.COMMENTS);
    this.loadLinkedSelector(LinkedSelector.DOCUMENTS);
    this.loadLinkedSelector(LinkedSelector.TIMESHEET);
  }

  loadLinkedSelector(linkedSelector: LinkedSelector): Subscription {
    const selector = EnumUtil.getName(LinkedSelector, linkedSelector).toLowerCase();
    const pendingCondig = this.pendingTypeConfig[this.clickedPending?.pendingTypeCode];
    if (!pendingCondig) {
      console.error(`Pending ${this.clickedPending?.pendingTypeCode} is not configured correctly. Check pendingTypeConfig.`);
      return null;
    }
    const module = this.clickedPending.module;
    const serviceName = pendingCondig[`${selector}Service`] || (this.moduleConfig[module] ? this.moduleConfig[module][`${selector}Service`] : null) || null;
    if (!serviceName || !this.clickedPending) {
      this.executed = true;
      return null;
    }
    this[`${selector}Loading`] = true;
    this.cdr.detectChanges();
    return this.api.get({ cancelableReq: this.$destroy, url: `${serviceName}/data-source/${this.clickedPending.recordId}` }).subscribe((response: []) => {
      if (!this.clickedPending) {
        return;
      }
      this.clickedPending[selector] = response;
      this.clickedPending[selector] = this.clickedPending[selector].sort((a, b) => {
        return !a.lastModifiedDate || !b.lastModifiedDate || a.lastModifiedDate < b.lastModifiedDate ? -1 : a.lastModifiedDate > b.lastModifiedDate ? 1 : 0;
      });
      this[`${selector}Loading`] = false;
      this.executed = true;
      if (selector.toUpperCase() === LinkedSelector.COMMENTS) {
        this.dialogDetailOpen = this.clickedPending[selector] !== null && this.clickedPending[selector].filter((c) => c.description.length > 350).length > 0;
      } else if (selector.toUpperCase() === LinkedSelector.FILES || selector.toUpperCase() === LinkedSelector.TIMESHEET) {
        if (this.clickedPending[selector]?.length > 0) {
          for (const file of this.clickedPending[selector]) {
            file.supportedPdfViewer = this.isSupportedPdfViewer(file);
          }
        }
      }
      this.cdr.detectChanges();
    });
  }

  chartClic(moduleName: string) {
    const newSearchValue = `${this.i18n.moduleOf}`.replace('{ module }', moduleName);
    if (this.searchValue === newSearchValue) {
      this.searchValue = '';
      this.onSearchValueChange('');
    } else {
      this.searchValue = newSearchValue;
    }
    this.cdr.detectChanges();
    this.updateBarTitle();
  }

  onFileSelected(fileList: File[] | FileList, pending: PendingDto, attacher = null, dropFile = this.dropFile) {
    this.addingFile = true;
    this.cdr.detectChanges();
    const options: FileSelectOptions = {
      holder: pending
    };
    dropFile.onFileSelected(fileList, options, attacher);
  }

  onFileFailed() {
    this.addingFile = false;
    this.cdr.detectChanges();
  }

  onFileLinked(e: LinkedFileEvt, pending: PendingDto = e.holder as PendingDto) {
    const filesService = this.getLinkService(pending, LinkedSelector.FILES);
    this.api.post({ url: `${filesService}/save/${pending.recordId}`, cancelableReq: this.$destroy, postBody: e.file.id }).subscribe(
      () => {
        this.noticeService.notice(this.translate.instant('linkedFileSaved'));
        e.file.activityCommitmentTask = this.clickedPending.commitmentTask;
        e.file.supportedPdfViewer = this.isSupportedPdfViewer(e.file);
        this.addingFile = false;
        this.cdr.detectChanges();
      },
      () => {
        this.addingFile = false;
        this.cdr.detectChanges();
      }
    );
    this.cdr.detectChanges();
  }

  deleteLinkDocument(document: IDocumentEntity) {
    this.addingDocument = true;
    this.cdr.detectChanges();
    if (!document) {
      return;
    }
    this.linkDocument(
      {
        entity: document,
        grid: null
      },
      false
    );
  }

  saveLinkDocument(evt: SaveDocumentEvt) {
    this.addingDocument = true;
    this.cdr.detectChanges();
    this.linkDocument(evt);
  }

  pendingDetailByType(pending: any, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    if (pending?.isSeparatorPending) {
      return '';
    }
    switch (pending?.pendingTypeCode) {
      case 'AUDIT-TO-CONFIRM-DATE':
      case 'AUDIT-TO-CONFIRM-CHANGE-BY-LEADER':
      case 'AUDIT-TO-CONFIRM-CHANGE-BY-MANAGER':
      case 'AUDIT-TO-FILL-BY-LEADER':
      case 'AUDIT-TO-FILL-BY-HELPER':
      case 'AUDIT-TO-ACCEPT-RESULT':
        return this.pendingDetailByAuditDto(pending as AuditIndividualPendingDto, highlightSearch, textOnly);
      case 'AUDIT-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FINDING-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FORM-ACTIVITY-TO-VERIFY-DELAYED':
      case 'ACTIVITY-TO-VERIFY-DELAYED':
        return this.pendingDetailByActivityToVerifyDelayedDto(pending as ActivityPendingDto, highlightSearch, textOnly);
      case 'AUDIT-ACTIVITY-TO-VERIFY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FORM-ACTIVITY-TO-VERIFY':
      case 'FORM-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FINDING-ACTIVITY-TO-VERIFY':
      case 'FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'ACTIVITY-TO-VERIFY':
      case 'ACTIVITY-TO-VERIFY-NOT-APPLY':
        return this.pendingDetailByActivityToVerifyDto(pending as ActivityPendingDto, highlightSearch, textOnly);
      case 'AUDIT-ACTIVITY-TO-COMPLETE':
      case 'FINDING-ACTIVITY-TO-COMPLETE':
      case 'FORM-ACTIVITY-TO-COMPLETE':
      case 'ACTIVITY-TO-COMPLETE':
        return this.pendingDetailByActivityToCompleteDto(pending as ActivityPendingDto, highlightSearch, textOnly);
      case 'DOCUMENT-TO-READ': {
        const readDocumentsMessages = this.i18n.documentsMessages as DataMap<string>;
        return this.handleHighlight(
          `${pending.responsibleUserName}${readDocumentsMessages.task.replace('{ description }', pending.description).replace('{ code }', pending.documentCode)}`,
          highlightSearch,
          textOnly
        );
      }
      case 'DOCUMENT-TO-DELIVER-PHYSICAL-COPY': {
        let message: string;
        if (pending.receiptUserName) {
          message = this.translate.instant('detail.DOCUMENT-TO-DELIVER-PHYSICAL-COPY-USER', {
            documentName: pending.documentName,
            userName: pending.receiptUserName
          });
        } else {
          message = this.translate.instant('detail.DOCUMENT-TO-DELIVER-PHYSICAL-COPY-JOB', {
            documentName: pending.documentName,
            jobName: pending.receiptPositionName
          });
        }
        return this.handleHighlight(message, highlightSearch, textOnly);
      }
      case 'DOCUMENT-TO-PICK-UP-PHYSICAL-COPY': {
        const documentsMessages = this.i18n.documentsMessages as DataMap<string>;
        return this.handleHighlight(
          `${documentsMessages.copywith.replace('{ documentName }', pending.documentName).replace('{ createdByUserName }', pending.createdByUserName)}`,
          highlightSearch,
          textOnly
        );
      }
      case 'DOCUMENT-TO-AUTHORIZE-REQUEST':
      case 'DOCUMENT-TO-VERIFY-REQUEST': {
        let docMessage: string;
        if (pending.requestType) {
          docMessage = this.translate.instant(pending.requestType, {
            documentName: pending.documentName,
            currentVersionAuthorName: pending.currentVersionAuthorName
          });
        }
        return this.handleHighlight(docMessage, highlightSearch, textOnly);
      }
      case 'DOCUMENT-TO-RENEW':
        return this.handleHighlight(
          `${this.translate.instant('labels.authorOnly')} ${pending.currentVersionAuthorName} ${pending.description}`,
          highlightSearch,
          textOnly
        );
      case 'FORM-TO-VERIFY-CANCEL-REQUEST':
      case 'FORM-TO-VERIFY-ADJUSTMENT-REQUEST':
      case 'FORM-TO-VERIFY-REOPEN-REQUEST':
      case 'FORM-TO-AUTHORIZE-CANCEL-REQUEST':
      case 'FORM-TO-AUTHORIZE-ADJUSTMENT-REQUEST':
      case 'FORM-TO-AUTHORIZE-REOPEN-REQUEST':
      case 'DOCUMENT-TO-FILL-FORM':
        return this.pendingFormPendingDto(pending as FormPendingDto, highlightSearch, textOnly);
      default:
        return this.handleHighlight(`${pending?.description}`, highlightSearch, textOnly);
    }
  }

  highlight(text: SafeHtml): SafeHtml {
    return this.handleHighlight(text, true, false);
  }

  pendingActivityOwnerValue(pending: ActivityPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    switch (pending?.pendingTypeCode) {
      case 'AUDIT-ACTIVITY-TO-VERIFY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FORM-ACTIVITY-TO-VERIFY':
      case 'FORM-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FORM-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FINDING-ACTIVITY-TO-VERIFY':
      case 'FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FINDING-ACTIVITY-TO-VERIFY-DELAYED':
      case 'ACTIVITY-TO-VERIFY':
      case 'ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'ACTIVITY-TO-VERIFY-DELAYED':
        return this.handleHighlight(
          `${this.translate.instant('labels.implementer')}: ${pending.implementerNames || this.translate.instant('labels.no-owner')}`,
          highlightSearch,
          textOnly
        );
      case 'AUDIT-ACTIVITY-TO-COMPLETE':
      case 'FORM-ACTIVITY-TO-COMPLETE':
      case 'FINDING-ACTIVITY-TO-COMPLETE':
      case 'ACTIVITY-TO-COMPLETE':
        return this.handleHighlight(
          `${this.translate.instant('labels.verifier')}: ${pending.verifierName || this.translate.instant('labels.no-owner')}`,
          highlightSearch,
          textOnly
        );
      default:
        return this.handleHighlight('', highlightSearch, textOnly);
    }
  }

  pendingActivityClient(pending: ActivityPendingDto, highlightSearch: boolean): SafeHtml {
    const fieldLabel = this.translate.instant('labels.client');
    return this.handleHighlight(`${fieldLabel}: ${pending.plannerTask?.clientName || '-'}`, highlightSearch, false);
  }

  pendingActivityPlanner(pending: ActivityPendingDto, highlightSearch: boolean): SafeHtml {
    const fieldLabel = this.translate.instant('labels.planner');
    return this.handleHighlight(`${fieldLabel}: ${pending.plannerTask?.plannerName || '-'}`, highlightSearch, false);
  }

  pendingActivityTask(pending: ActivityPendingDto, highlightSearch: boolean): SafeHtml {
    const fieldLabel = this.translate.instant('labels.task');
    return this.handleHighlight(`${fieldLabel}: ${pending.plannerTask?.text || '-'}`, highlightSearch, false);
  }

  isPendingActivityOwnerAvaliable(pending: ActivityPendingDto): SafeHtml {
    switch (pending?.pendingTypeCode) {
      case 'AUDIT-ACTIVITY-TO-VERIFY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FORM-ACTIVITY-TO-VERIFY':
      case 'FORM-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FORM-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FINDING-ACTIVITY-TO-VERIFY':
      case 'FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FINDING-ACTIVITY-TO-VERIFY-DELAYED':
      case 'ACTIVITY-TO-VERIFY':
      case 'ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'ACTIVITY-TO-VERIFY-DELAYED':
        return true;
      case 'AUDIT-ACTIVITY-TO-COMPLETE':
      case 'FORM-ACTIVITY-TO-COMPLETE':
      case 'FINDING-ACTIVITY-TO-COMPLETE':
      case 'ACTIVITY-TO-COMPLETE':
        return !!pending.verifierName;
      default:
        return false;
    }
  }

  pendingDetailProgress(pending: ActivityPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    if (pending.progress !== null && typeof pending.progress !== 'undefined' && pending.progress > 0) {
      return this.handleHighlight(`${this.translate.instant('labels.progressLabel')} ${pending.progress}%`, highlightSearch, textOnly);
    }
    return this.handleHighlight(this.translate.instant('labels.noProgress'), highlightSearch, textOnly);
  }

  pendingDetailRequester(pending: any, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    if (pending.requestType !== null && typeof pending.requestType !== 'undefined') {
      return this.handleHighlight(`${this.translate.instant('labels.requester')}: ${pending.currentVersionAuthorName}`, highlightSearch, textOnly);
    }
    return this.handleHighlight(`${this.translate.instant('labels.authorOnly')}: ${pending.currentVersionAuthorName}`, highlightSearch, textOnly);
  }

  getActivityHistoryService(pendingTypeCode) {
    return `${this.getModuleService(pendingTypeCode, Module.ACTIVITY)}/history`;
  }

  getActivityService(pendingTypeCode) {
    return this.getModuleService(pendingTypeCode, Module.ACTIVITY);
  }

  onActivityDataLoaded(history: ActivityHistoryEntity[], activity: ActivityPendingDto) {
    activity.history = history;
    this.historyLoading = false;
    this.cdr.detectChanges();
  }

  linkDocument(evt: SaveDocumentEvt, save = true) {
    const documentEntity = evt.entity;
    const documentId = evt.entity.id;
    const pending = this.clickedPending;
    if (!pending) {
      console.error('Invalid state, pending record is missing.');
      return;
    }
    const documentsService = this.getLinkService(pending, LinkedSelector.DOCUMENTS);
    const id = this.clickedPending.recordId || null;
    if (!id || !documentsService) {
      console.error('Invalid state, pending record is missing data.', id, documentsService);
      return;
    }
    if (save) {
      const s = this.api.post({ cancelableReq: this.$destroy, postBody: null, url: `${documentsService}/save/${id}/${documentId}` }).subscribe(() => {
        this.noticeService.notice(this.translate.instant('linkedDocumentSaved'));
        documentEntity.activityCommitmentTask = pending.commitmentTask;
        pending.documents.push(documentEntity as IDocumentEntity);
        s.unsubscribe();
      });
    } else {
      // removing
      const s = this.api.post({ cancelableReq: this.$destroy, postBody: null, url: `${documentsService}/delete/${id}/${documentId}` }).subscribe(() => {
        this.noticeService.notice(this.translate.instant('linkedDocumentRemoved'));
        if (pending.documents) {
          pending.documents = pending.documents.filter((d) => d.id !== documentId);
        }
        s.unsubscribe();
      });
    }
    this.addingDocument = false;
    this.cdr.detectChanges();
  }

  updateProgressBar($event) {
    this.pendingActionLoading = $event;
    this.cdr.detectChanges();
    setTimeout(() => {
      this.onprogress.emit(true);
    }, 50);
  }

  onTapPendingAction(event: any, _pendingActions: DropdownMenuComponent, pending: PendingDto, idx: number) {
    if (event.pointerType !== 'mouse' || this.isScreenSmall) {
      return;
    }
    this.setFocus(pending, idx);
  }

  public changeItemSelection(item: DropdownMenuItem) {
    item.selected = !item.selected;
    this.filterByPendingTypeCode(item);
    this.isSearchBarHidden = !this.isSmallTouchDevice;
  }

  public collapsed(index: number) {
    const accordion = this.accordion();
    if (!accordion) {
      return true;
    }
    return accordion[index]?.collapsed;
  }

  public onInteraction(event: IExpansionPanelEventArgs) {
    const expandedPanels = this.accordion().filter((panel) => !panel.collapsed);
    for (const expandedPanel of expandedPanels) {
      if (expandedPanel.id !== event.owner.id) {
        expandedPanel.collapse();
      }
    }
  }

  isXLargeWidgetSize(): boolean {
    return this.size !== null && this.size.responsiveClass === 'xxlargeResponsive';
  }

  closeRightPanel(): void {
    this.isRightPanelHidden = true;
    this.clickedPending.focused = false;
    this.resetFocus();
    this._changedSelectedIndex.next(true);
    this.cdr.detectChanges();
  }

  public fixScrollPosition(): void {
    if (this.executed || !ConfigPendings.hasScrollPosition()) {
      return;
    }
    const pendingVirtualizer = this.pendingVirtualizer();
    if (typeof pendingVirtualizer !== 'undefined' && typeof pendingVirtualizer.igxForOf !== 'undefined') {
      if (this.clickedPending?.focused) {
        const scrollPosition = ConfigPendings.getScrollPosition();
        pendingVirtualizer.scrollTo(scrollPosition);
      }
    }
  }

  getDateColor(commitmentStartDate: number | Date, commitmentEndDate: number | Date): string {
    if (commitmentStartDate === null || typeof commitmentStartDate === 'undefined' || commitmentEndDate === null || typeof commitmentEndDate === 'undefined') {
      return '';
    }
    let className = '';
    this.isFuture = false;
    const start = DateUtil.safe(commitmentStartDate);
    const end = DateUtil.safe(commitmentEndDate);
    if (!DateUtil.isSameDay(start, end)) {
      if (end < DateUtil.today()) {
        className = 'delayed';
        this.isFuture = false;
      } else if (start >= DateUtil.tomorrow()) {
        className = 'future';
        this.isFuture = true;
      }
    } else {
      if (end < DateUtil.today()) {
        className = 'delayed';
        this.isFuture = false;
      } else if (end >= DateUtil.tomorrow()) {
        className = 'future';
        this.isFuture = true;
      }
    }
    return className;
  }

  getTitle(commitmentDate: number | Date): string {
    if (DateUtil.getTime(commitmentDate) === DateUtil.tomorrow().getTime()) {
      return this.translate.instant('tomorrowActivitie');
    }
    if (DateUtil.getTime(commitmentDate) > DateUtil.tomorrow().getTime()) {
      return this.translate.instant('nearActivitie');
    }
    return this.translate.instant('yesterdayActivitie');
  }

  loadPreviousDay(): void {
    this.currentDate = DateUtil.subtractDays(this.currentDate, 1);
    this.setTitleDate();
    if (!this.pendingListViewGrouped) {
      if (!DateUtil.isToday(this.currentDate)) {
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.currentDate);
      } else {
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.currentDate);
      }
    }
  }

  loadNextDay(): void {
    this.currentDate = DateUtil.addOneDay(this.currentDate);
    this.setTitleDate();
    if (!this.pendingListViewGrouped) {
      if (!DateUtil.isToday(this.currentDate)) {
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.currentDate);
      } else {
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.currentDate);
      }
    }
  }

  loadToday() {
    this.currentDate = DateUtil.today();
    this.setTitleDate();
    if (!this.pendingListViewGrouped) {
      this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, DateUtil.todayEndTime());
    }
  }

  scrollTo(id: string): void {
    this.rightPanelCollapsed[id] = false;
    document.getElementById(id)?.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
  }

  castAsActivityPendingDto(object: any): ActivityPendingDto {
    return object;
  }

  castAsComplaintPendingDto(object: any): ComplaintPendingDto {
    return object;
  }

  castAsFormPending(object: PendingDto): FormPendingDto {
    return object as FormPendingDto;
  }

  public chipsOrderChanged(event: IChipsAreaReorderEventArgs) {
    const newChipList = [];
    for (const chip of event.chipsArray) {
      const chipItem = this.searchPendingTypes.filter((item) => item.value === chip.id)[0];
      newChipList.push(chipItem);
    }
    this.searchPendingTypes = newChipList;
    // Actualizamos los filtros aplicados
    this.filtersApplied = [];
    for (const item of this.searchPendingTypes) {
      this.filtersApplied.push(item.value);
    }
    this._changedSearchPendingTypes.next(this.searchPendingTypes);
    this.reorderPendingsByFilter();
  }

  getPendingsCount() {
    return this.pendings.length;
  }

  clickOnShowDetalil(description: string): void {
    this.dialogDetailOpen = true;
    this.cdr.detectChanges();
    // Para mostrar un dialogo con la información de los comentarios
    if (description) {
      this.commentDetail = description;
      this.detailDialog().open();
    }
  }

  public isTypeOfDate(value: Date | number): boolean {
    return value instanceof Date || typeof value === 'number';
  }

  public isFindingActivityPending(pending: PendingDto): boolean {
    return pending.pendingTypeCode in FINDING_ACTIVITY_APE;
  }

  private async __init(): Promise<void> {
    this.menuWidgetService.requestRefreshWidgetSize.pipe(takeUntil(this.$destroy)).subscribe((refreshData) => {
      this.refreshSize(refreshData.widgets);
    });
    this.menuService.changeAppbarPendingCount.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.refreshChartDataSource();
    });
    this.menuWidgetService.newChartModuleSection.pipe(takeUntil(this.$destroy)).subscribe((moduleName) => {
      this.chartClic(moduleName);
    });
    this.menuWidgetService.refreshPendingsRequest.pipe(takeUntil(this.$destroy)).subscribe(({ refreshPendings, removedPending }) => {
      if (refreshPendings) {
        this.refreshAll();
      } else if (removedPending) {
        // En caso de no refrescar solo quita el pendiente si mandó
        this.refreshData(removedPending);
      }
    });
    if (this.isMobilDevice()) {
      this.menuOptionsButtonBlackList.splice(
        this.menuOptionsButtonBlackList.findIndex((fi) => fi === CommonAction.OPEN_DETAIL),
        1
      );
    }
    // ---------------  GETS LOCAL STORAGE VALUES ---------------------
    await this._loadLocalFilters();
    this.initializeApeLang();
    this._onSearchValueChanged
      .pipe(throttleTime(500, undefined, { leading: false, trailing: true }), takeUntil(this.$destroy))
      .subscribe((value) => this.executeSearchValueChange(value));
    this._changedSearchPendingTypes.pipe(takeUntil(this.$destroy)).subscribe((value) => this.executeChangedSearchPendingTypes(value));
    this._changedSelectedIndex.pipe(takeUntil(this.$destroy)).subscribe(() => this.executeChangeSelectedIndex());
    this._changedSelectedDates.pipe(takeUntil(this.$destroy)).subscribe(() => this.executeChangeSelectedDates());
    this._changedSelectedMenuOptions.pipe(takeUntil(this.$destroy)).subscribe(() => this.executeChangeSelectedMenuOptions());
    this.translate
      .getFrom(PendingsUtil.LANG_CONFIG, 'action')
      .pipe(takeUntil(this.$destroy))
      .subscribe((tags) => {
        for (const item of this.menuMainOptions) {
          item.text = tags[item.value];
        }
      });
    this.translate
      .getFrom(PendingsUtil.LANG_CONFIG, 'pendingsDropdownOptions')
      .pipe(takeUntil(this.$destroy))
      .subscribe((tags) => {
        for (const item of this.pendingsDropdownOptions) {
          item.text = tags[item.value];
        }
      });
    this.translate
      .getFrom(PendingsUtil.LANG_CONFIG, 'filterDatesNotes')
      .pipe(takeUntil(this.$destroy))
      .subscribe((tag) => {
        this.i18n.filterDatesNotes = tag;
        this.updateCalendarSearchValue();
      });
    this.lang(PendingsUtil.LANG_CONFIG, 'noticeMessages');
    this.lang(PendingsUtil.LANG_CONFIG, 'pageMessages');
    this.lang(PendingsUtil.LANG_CONFIG, 'documentsMessages');
    this.lang(PendingsUtil.LANG_CONFIG, 'moduleOf');
    this.setActionsLocale();
    // Se inicializan permisos por módulo
    Session.getLazySession()
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        this.isTimesheetAvailable = Session.hasService(ProfileServices.TS_REGISTER_PLANNED) || Session.hasService(ProfileServices.TS_REGISTER_UNPLANNED);
        for (const moduleKey of Session.getModules()) {
          if (!MODULE_CONFIG[moduleKey]) {
            continue;
          }
          this.moduleConfig[moduleKey] = MODULE_CONFIG[moduleKey];
        }
      });
    this.rightPanelButtonBlackList.push(this.stopwatchItems[0]);
    this.menuOptionsButtonBlackList.push(this.stopwatchItems[0]);
    if (this.isSmallTouchDevice) {
      this.pendingsDropdownOptionsAvailable.push(CommonAction.REFRESH);
      this.pendingsDropdownOptionsAvailable.push(PendingsAction.FILTER_RANGE);
    }
    this.refreshAll();
  }

  private refreshSize(widgets: readonly WidgetConfig[]): void {
    const widget = widgets.find((item) => item.header === this.header());
    if (widget) {
      this.size = widget.sizeWidget;
    }
  }

  private async _loadLocalFilters(): Promise<void> {
    if (this.isPendingsHidden()) {
      return;
    }
    try {
      this.isSearchBarHidden = !this.isSmallTouchDevice;
      const value = await PendingsLocalConfig.initialize();
      if (value) {
        this.selectSearchPendingTypes(value?.searchPendingTypes);
        this.filtersApplied = value?.searchPendingTypes;
        this.exactSearch = value?.exactSearch;
        this.searchValue = value?.searchValue || '';
      } else {
        console.error('Could not load pendings local config, no value.', value);
      }
    } catch (error) {
      console.error('Could not load pendings local config.', error);
    }
  }

  private checkPendingContainerSize(syncGroup = false): void {
    if (this.pendingListViewGrouped) {
      this.groupPendingListByDate(syncGroup);
    } else {
      this.removeFuturePendings();
      this.pendingListViewGrouped = false;
      this.pendingsTemp = this.pendings || [];
      this.cdr.detectChanges();
    }
  }

  //true: no realiza agrupación
  private isRefreshisPendingGroupRequired(): boolean {
    return this.pendingsTemp[0]?.isSeparatorPending;
  }

  private removeFuturePendings(): void {
    if (this.pendingListSorted === PendingsAction.SORT_ASC && this.pendings.length > 0) {
      if (!this.hideFuturePendings) {
        this.endDate = DateUtil.addYears(this.endDate, 1);
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.endDate);
      } else {
        this.endDate = DateUtil.todayEndTime();
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_TODAY);
      }
    } else if (this.pendingListSorted === PendingsAction.SORT_DESC && this.pendings.length > 0) {
      if (!this.hideFuturePendings) {
        this.endDate = DateUtil.addYears(this.endDate, 1);
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_SOMEDAY, this.endDate);
      } else {
        this.endDate = DateUtil.todayEndTime();
        this.filterCalendarSearch(PendingsAction.FILTER_UNTIL_TODAY);
      }
    }
  }

  private sortPendings(sortOption: PendingsAction, sync?: boolean): void {
    if (this.pendings.length > 0) {
      if (this.pendingListSorted !== sortOption) {
        this.pendings.reverse();
        this.pendingListSorted = sortOption;
      }
      // Es necesario sincronizar los registros
      if (sync) {
        if (this.pendingListSorted === PendingsAction.SORT_ASC) {
          this.pendings.sort((a, b) => {
            if (+a.commitmentStartDate < +b.commitmentStartDate) {
              return -1;
            }
            if (+a.commitmentStartDate > +b.commitmentStartDate) {
              return 1;
            }
            return 0;
          });
        } else {
          this.pendings.sort((a, b) => {
            if (+a.commitmentStartDate > +b.commitmentStartDate) {
              return -1;
            }
            if (+a.commitmentStartDate < +b.commitmentStartDate) {
              return 1;
            }
            return 0;
          });
        }
      }
    }
  }

  private groupPendingListByDate(syncGroup = false): void {
    if (!this.isRefreshisPendingGroupRequired() || syncGroup) {
      let pendingsGrouped = [];
      this.removeFuturePendings();
      if (this.pendingListViewGrouped && this.pendings.length > 0) {
        const sourceData = from(this.pendings);
        const pendingsFiltered = sourceData.pipe(
          groupBy((pending) => DateUtil.formatIso(pending.commitmentStartDate)),
          mergeMap((group) => group.pipe(toArray()))
        );

        pendingsGrouped = [];
        let count = 1;
        pendingsFiltered.pipe(takeUntil(this.$destroy)).subscribe((result) => {
          result = result.sort((a, b) => {
            if (+b.activityOrder < 0) {
              return +b.activityOrder - +a.activityOrder;
            }
            return +a.activityOrder - +b.activityOrder;
          });
          const fakePendings: PendingDto = {
            base: null,
            code: DateUtil.isToday(result.map((d) => d.commitmentStartDate)[0])
              ? this.translate.instant('dateTodayWithObj', {
                  date: DateUtil.format(result.map((d) => d.commitmentStartDate)[0], 'D / MMMM / YYYY', false, this.getLang())?.toLowerCase()
                })
              : result.map((d) => d.commitmentStartDate)[0],
            commitmentDate: result.map((d) => d.commitmentDate)[0],
            description: result.map((d) => d.description)[0],
            module: result.map((d) => d.module)[0],
            pendingRecordId: null,
            recordId: result.length,
            systemLinks: null,
            pendingTypeCode: result.map((d) => d.pendingTypeCode)[0],
            isSeparatorPending: true,
            comments: null,
            files: null,
            documents: null,
            dto: null,
            commitmentEndDate: result.map((d) => d.commitmentDate)[0],
            commitmentStartDate: result.map((d) => d.commitmentDate)[0],
            commitmentTask: 0
          };
          result.unshift(fakePendings);
          count--;
          for (const item of result) {
            item.idx = count;
            pendingsGrouped.push(item);
            count++;
          }
          this.pendingsTemp = [];
          this.pendingsTemp = pendingsGrouped;
        });
      } else if (this.pendings.length === 0) {
        this.pendingsTemp = [];
      }
      this.cdr.detectChanges();
    }
  }

  private setSearchPendingsTypes() {
    for (const value of this.filtersApplied) {
      this.selectSearchPendingTypes(value);
    }
  }

  private executeSearchValueChange(searchValue: string) {
    PendingsLocalConfig.setSearchValue(searchValue);
  }

  private executeChangedSearchPendingTypes(items: DropdownMenuItem[]) {
    const values = items.map((item) => item.value);
    PendingsLocalConfig.setSearchPendingTypes(values);
  }

  private executeChangeSelectedIndex() {
    PendingsLocalConfig.setSelected(this.clickedPendingId, this.clickedPendingApe);
  }

  private executeChangeSelectedDates() {
    PendingsLocalConfig.setSelectedCalendarDates(this.filterCalendarActive, this.startDate, this.endDate);
  }

  private executeChangeSelectedMenuOptions() {
    PendingsLocalConfig.setSelectedMenuOptions(this.pendingListViewGrouped, this.hideFuturePendings, this.pendingListSorted);
  }

  private buildModuleSintaxSearch(item: PendingDto): string {
    if (!item.module) {
      console.warn(`Pending ${item.pendingTypeCode} has not configured language.`);
      return '';
    }
    const currentLang = this.getLang();
    if (currentLang.startsWith('es')) {
      return `@Módulo de ${this.translate.instant(`root.common.module.names.${item.module}`)}`;
    }
    if (currentLang.startsWith('en')) {
      return `@${this.translate.instant(`root.common.module.names.${item.module}`)} Module`;
    }
    console.warn(`Search modules by language ${currentLang} is not configurated.`);
    return '';
  }

  /**
   * Método que actualiza el agrupamiento, no vuelve a consultar pendientes.
   * @param pendingRecordId Pendiente atendido
   */
  private refreshGrouping(pendingRecordId: number): void {
    if (this.pendingListViewGrouped) {
      this.pendings = this.pendings.filter((p) => p.pendingRecordId !== pendingRecordId);
      this.groupPendingListByDate(true);
    }
    this.cdr.detectChanges();
  }

  private getFormHrefLink(pending: FormPendingDto): string {
    return `v.request.survey.preview.view?id=O${pending.outstandingSurveysId}&task=preview&requestId=${pending.requestId}`;
  }

  private getActivityHrefLink(p: PendingDto): string {
    const pending = p as ActivityPendingDto;
    if (pending.fillType === FillType.PLANNER_TASK) {
      return `menu/planner/task-detail/${pending.recordId}`;
    }
    if (this.isVerificationPending(pending)) {
      const code = encodeUrlParameter(pending.code);
      return `menu/activities/code/${code}?impl=true`;
    }
    const code = encodeUrlParameter(pending.code);
    return `menu/activities/code/${code}`;
  }

  private isActivityPending(pending: PendingDto): SafeHtml {
    switch (pending.pendingTypeCode) {
      case 'AUDIT-ACTIVITY-TO-VERIFY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-DELAYED':
      case 'AUDIT-ACTIVITY-TO-COMPLETE':
      case 'FORM-ACTIVITY-TO-VERIFY':
      case 'FORM-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FORM-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FORM-ACTIVITY-TO-COMPLETE':
      case 'FINDING-ACTIVITY-TO-VERIFY':
      case 'FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FINDING-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FINDING-ACTIVITY-TO-COMPLETE':
      case 'ACTIVITY-TO-VERIFY':
      case 'ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'ACTIVITY-TO-VERIFY-DELAYED':
      case 'ACTIVITY-TO-COMPLETE':
      case 'PLANNER-ACTIVITY-TO-COMPLETE':
        return true;
      default:
        return false;
    }
  }

  private isVerificationPending(pending: PendingDto): SafeHtml {
    switch (pending.pendingTypeCode) {
      case 'AUDIT-ACTIVITY-TO-VERIFY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'AUDIT-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FORM-ACTIVITY-TO-VERIFY':
      case 'FORM-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FORM-ACTIVITY-TO-VERIFY-DELAYED':
      case 'FINDING-ACTIVITY-TO-VERIFY':
      case 'FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'FINDING-ACTIVITY-TO-VERIFY-DELAYED':
      case 'ACTIVITY-TO-VERIFY':
      case 'ACTIVITY-TO-VERIFY-NOT-APPLY':
      case 'ACTIVITY-TO-VERIFY-DELAYED':
        return true;
      default:
        return false;
    }
  }

  private fillPendings(result: PendingDataSource) {
    if (!result || !result.moduleCount) {
      console.warn("Could not load pending's  data-source");
      this.isLoading = false;
      this.menuWidgetService.changePendingsLoading(this.isLoading);
      return;
    }
    for (const k in this.pendingTypeConfig) {
      if (!this.pendingTypeConfig.hasOwnProperty(k)) {
        continue;
      }
      if (result.apeCount) {
        this.pendingTypeConfig[k].count = result.apeCount[k] || 0;
      } else {
        this.pendingTypeConfig[k].count = 0;
      }
    }
    const typesCodeAvailable = [];
    for (const moduleName in this.moduleConfig) {
      if (!this.moduleConfig.hasOwnProperty(moduleName)) {
        continue;
      }
      if (!result.moduleCount[moduleName] || !this.moduleConfig[moduleName]) {
        continue;
      }
      const menuActionsTemp = [...this.moduleConfig[moduleName].menuActions];
      this.moduleConfig[moduleName].menuActions = [];
      if (result.moduleCount[moduleName].pendingTypeCodes.length > 0) {
        for (const idx in result.moduleCount[moduleName].pendingTypeCodes) {
          if (!result.moduleCount[moduleName].pendingTypeCodes.hasOwnProperty(idx)) {
            continue;
          }
          const pendingTypeCode = result.moduleCount[moduleName].pendingTypeCodes[idx];
          if (menuActionsTemp.find((menuAction) => menuAction.value === pendingTypeCode)) {
            const menuactionS = menuActionsTemp.filter((menuaction) => menuaction.value === pendingTypeCode);
            this.moduleConfig[moduleName].menuActions.push(menuactionS[0]);
            this.moduleConfig[moduleName].availableMenuActions.push(pendingTypeCode);
            if (menuactionS[0].selected === true) {
              typesCodeAvailable.push(menuactionS[0]);
            }
          } else {
            this.moduleConfig[moduleName].menuActions.push({
              text: pendingTypeCode,
              value: pendingTypeCode
            });
          }
        }
      } else if (menuActionsTemp.length > 0) {
        // Limpiamos los valores del menu de acciones (filtros) del módulo para el cual no tenemos pendientes
        for (const idx in menuActionsTemp) {
          if (!menuActionsTemp.hasOwnProperty(idx)) {
            continue;
          }
          const idxSelectedItem = this.searchPendingTypes.indexOf(menuActionsTemp[idx]);
          if (idxSelectedItem !== -1) {
            this.searchPendingTypes.splice(idxSelectedItem, 1);
          }
        }
        this.moduleConfig[moduleName].count = 0;
      }
    }
    // Limpiamos los chips sólo si hay tipos disponibles
    if (typesCodeAvailable.length > 0) {
      this.cleanSearchPendingTypes(typesCodeAvailable);
    }
    if (result.pendings?.length === 0) {
      this.searchPendingTypes = [];
    }
    //TODO: Evaluar en que se requiere
    this._lastDataSource = cloneObject(result);
    this.futurePendingCount = result.futurePendingCount;
    this.setHrefLinks(result.pendings || []);
    this.pendings = result.pendings || [];
    this.pendingsTemp = this.pendings || [];
    this.lastRefreshDate = new Date();
    this.resetCalendarSearch();
    this.refreshChartDataSource();
    this.updateBarTitle();
    PendingsLocalConfig.getValue()
      .then(
        (value) => {
          this.selectSearchPendingTypes(value?.searchPendingTypes);
          this.filtersApplied = value?.searchPendingTypes;
          this.exactSearch = value?.exactSearch;
          this.searchValue = value?.searchValue || '';
          this.isSearchBarHidden = !this.isSmallTouchDevice;
          //Se valida si la lista está agrupada o no
          this.pendingListViewGrouped = value?.selectedMenuOptions?.groupByDate;
          this.hideFuturePendings = value?.selectedMenuOptions?.hideFuturePendings;
          this.pendingListSorted = value?.selectedMenuOptions.order !== null ? value?.selectedMenuOptions?.order : PendingsAction.SORT_ASC;
          this.sortPendings(this.pendingListSorted, true);
          this.checkPendingContainerSize(true);
          this.refreshPendingsDropdown();

          if (!this.clickedPending) {
            this.loadSelectedPending(value?.selected);
          }
          // Cargamos los valores de las fechas seleccionadas (Rango de fechas del calendario)
          this.loadCalendarDates(value.selectedCalendarDates);
          // Validación para saber si hay filtros en el local storage y no se han aplicado
          if (this.filtersApplied?.length > 0 && this.searchPendingTypes?.length === 0) {
            this.setSearchPendingsTypes();
          }
          this.pendingVirtualizer()?.cdr.detectChanges();
          this.fixScrollPosition();
          this.isLoading = false;
          this.menuWidgetService.changePendingsLoading(this.isLoading);
          this.cdr.detectChanges();
        },
        (error) => {
          this.isLoading = false;
          console.error('Could not load pending local config.', error);
        }
      )
      .finally(() => {
        if (this.list().isListEmpty && this.filtersApplied?.length > 0) {
          // si no hay registros visibles se remueven solo los filtros de los tipos de registros que no esten disponibles
          // en el filtro de busqueda
          const keys = Object.keys(this.moduleConfig);
          const allMenuActions = [];
          for (const key of keys) {
            const module = this.moduleConfig[key];
            allMenuActions.push(...module.availableMenuActions);
          }
          this.searchPendingTypes = this.searchPendingTypes.filter((e) => allMenuActions.indexOf(e.value) !== -1);
          this._changedSearchPendingTypes.next(this.searchPendingTypes);
        }
        this.isLoading = false;
        this.menuWidgetService.changePendingsLoading(this.isLoading);
        this.cdr.detectChanges();
      });
    this.cdr.detectChanges();
  }

  private cleanSearchPendingTypes(actualPendingTypeCode: DropdownMenuItem[]): void {
    this.searchPendingTypes = [...actualPendingTypeCode];
  }

  /**
   * Metodo para realizar el conteo de los pendientes y validación de los filtros (Filter menu)
   * NOTA: Se realizó un análisis y siempre se tienen que validar las opciones del menú de filtros, ya que cambian de acuerdo al filtrado de la información.
   * Se cuentan los pendientes y se validan las opciones del menú de filtros al:
   *  - Ocultar un pendiente
   *  - Solicitar cambios en un pendiente
   *	- Al filtrar pendientes hasta hoy
   *	- Al filtrar pendientes futuros
   *  - Al filtrar por rango de fechas
   *	- Al atender pendientes dentro del widget (como en actividades o proyectos)
   * Muestra la alerta en los botones por ModuleName en el menú de filtros.
   *
   * @param pendingsR Listado de pendientes
   * @param fromCountPendings Solo es falso cuando se atiende un pendiente dentro del widget
   */
  private countPendings(pendingsR: PendingDto[], fromCountPendings: boolean): void {
    const filteredPendings = pendingsR;

    if (filteredPendings !== null && filteredPendings !== undefined) {
      if (filteredPendings.length === 0 && !fromCountPendings) {
        // Pendientes atendidos.....
        this.attendedPending = true;
      }
      for (const moduleName in this.moduleConfig) {
        if (!this.moduleConfig.hasOwnProperty(moduleName)) {
          continue;
        }
        let count = 0;
        const filterOptionsMenu = [];
        // Iteramos sobre pendings
        for (const p of filteredPendings) {
          if (p.module === moduleName) {
            count++;
          }
          const pendingTypeCode = p.pendingTypeCode.toUpperCase();
          if (!filterOptionsMenu.some((typeCodes) => typeCodes.typeCode === pendingTypeCode)) {
            filterOptionsMenu.push({
              module: p.module,
              typeCode: pendingTypeCode
            });
          }
        }
        // Agregamos el contador por ModuleName
        this.moduleConfig[moduleName].count = count;

        if (count === 0) {
          // Si ya no tiene pendientes y existe en la búsqueda el nombre del módulo se limpia REQ-44387
          const text: string = this.translate.instant(`root.common.module.names.${moduleName}`);
          if (this.searchValue.includes(text)) {
            this.searchValue = '';
            this.onSearchValueChange('');
          }
        }
        count = 0;
        // Modificamos las acciones de los filtros por ModuleName
        this.moduleConfig[moduleName].availableMenuActions = [];
        for (const p of filterOptionsMenu) {
          if (p.module === moduleName) {
            this.moduleConfig[moduleName].availableMenuActions.push(p.typeCode);
          }
        }

        // Limpiamos los chips seleccionados si ya no hay pendientes filtrados
        for (const searchPT of this.searchPendingTypes) {
          if (!filterOptionsMenu.find((filterT) => filterT.typeCode === searchPT.value)) {
            const idxSelectedItem = this.searchPendingTypes.indexOf(searchPT);
            if (idxSelectedItem !== -1) {
              this.searchPendingTypes.splice(idxSelectedItem, 1);
              this._changedSearchPendingTypes.next(this.searchPendingTypes);
            }
          }
        }

        // Validamos los filtros a mostrar por ModuleName
        for (const element of this.moduleConfig[moduleName].menuActions) {
          element.hidden = !this.moduleConfig[moduleName].availableMenuActions.find((value) => value === element.value);
          // Contamos los pendientes por tipo
          element.suffix = this.countPendingsByType(filteredPendings, element.value.toString());
        }
        this._onAddedApeItems.next(true);
      }
      this.updateBarTitle(false);
    }
    this.cdr.detectChanges();
  }

  private countPendingsByType(filteredPendings: PendingDto[], availableMenuAction: string): string {
    let countP = 0;
    for (const p of filteredPendings) {
      if (p.pendingTypeCode === availableMenuAction) {
        countP++;
      }
    }
    return countP.toString();
  }

  private surveyFillUrl(pending: FormPendingDto): string {
    const requestId = pending.requestId;
    const outstandingSurveyId = pending.outstandingSurveysId;
    return `v.request.survey.fill.view?id=O${outstandingSurveyId}&outstandingSurveyId=${outstandingSurveyId}&requestId=${requestId || -1}&task=fill&requestMode=FILL`;
  }

  private subscribeStopwatchService() {
    if (!isFeatureAvailable(FeatureFlag.TIMESHEET_STOP_WATCH)) {
      return;
    }
    this.subs.push(
      this.timesheetService.hasStopwatchrunning.subscribe((running: TimesheetDto) => {
        this.stopwatchRunningDate = running.date as Date;
        this.isStopwatchrunning = running.hasEnableStopwatch;
        this.pendingRecordId = running.pendingRecordId;
        this.stopwatchType = running.stopwatchType;
        this.clearStopwtachValues(running.hasEnableStopwatch);
        this.resetFocus();
        this.cdr.detectChanges();
      }),
      this.timesheetService.updateUIStopwatch.subscribe((response: UpdateUIStopwatch) => {
        this.clearStopwtachValues(response.update);
        this.cdr.detectChanges();
      })
    );
  }

  private clearStopwtachValues(running: boolean) {
    let index: number;
    let indexMenuOptions: number;
    if (running) {
      if (!this.rightPanelButtonBlackList.includes(ImplementAction.CAPTURE_TIME)) {
        this.rightPanelButtonBlackList.push(ImplementAction.CAPTURE_TIME);
        this.menuOptionsButtonBlackList.push(ImplementAction.CAPTURE_TIME);
      }
      index = this.rightPanelButtonBlackList.findIndex((item) => item === ImplementAction.STOP_STOPWATCH);
      indexMenuOptions = this.menuOptionsButtonBlackList.findIndex((item) => item === ImplementAction.STOP_STOPWATCH);
      this.stopwatchItems.pop();
      this.stopwatchItems.push(ImplementAction.START_STOPWATCH);
    } else {
      if (this.rightPanelButtonBlackList.includes(ImplementAction.CAPTURE_TIME)) {
        this.rightPanelButtonBlackList.splice(
          this.rightPanelButtonBlackList.findIndex((item) => item === ImplementAction.CAPTURE_TIME),
          1
        );
        this.menuOptionsButtonBlackList.splice(
          this.menuOptionsButtonBlackList.findIndex((item) => item === ImplementAction.CAPTURE_TIME),
          1
        );
      }
      index = this.rightPanelButtonBlackList.findIndex((item) => item === ImplementAction.START_STOPWATCH);
      indexMenuOptions = this.menuOptionsButtonBlackList.findIndex((item) => item === ImplementAction.START_STOPWATCH);
      this.stopwatchItems.pop();
      this.stopwatchItems.push(ImplementAction.STOP_STOPWATCH);
      this.isStopwatchrunning = null;
      this.pendingRecordId = null;
      this.isRightPanelHidden = true;
      this.clickedPending = null;
    }
    if (index) {
      this.rightPanelButtonBlackList.splice(index, 1);
      this.menuOptionsButtonBlackList.splice(indexMenuOptions, 1);
    }
    this.rightPanelButtonBlackList.push(this.stopwatchItems[0]);
    this.menuOptionsButtonBlackList.push(this.stopwatchItems[0]);
  }

  private commentRequiredSaveAction(pending: PendingDto, comment: string): void {
    this.toggleMenu(null, pending, pending.commentRequired.action || undefined, comment);
    pending.commentRequired.rollbackData = [];
    pending.commentRequired.required = false;
  }

  private isModuleAvailable(moduleKey: string): boolean {
    const modulesAvailable = ModulesAvailable;
    if (moduleKey === Module.PLANNER && isFeatureAvailable(FeatureFlag.PLANNER_MODULE)) {
      return true;
    }
    if (moduleKey !== Module.PLANNER) {
      return modulesAvailable.includes(moduleKey);
    }
    return false;
  }

  private handleHighlight(text: SafeHtml, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return DomUtil.highlight(this.sanitizer, this.searchValue, text, highlightSearch, textOnly, !this.exactSearch).safeHtml;
  }

  private pendingDetailByAuditDto(pending: AuditIndividualPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return this.handleHighlight(
      `Auditoría el ${this.datePipe.transform(pending.dteStart, this.dateFormat)} a las ${this.datePipe.transform(pending.tmpStart, this.timeFormat)} al departamento ${pending.businessUnitDepartmentDescription} en su ${pending.areaDescription ? 'área' : 'proceso'} de ${pending.areaDescription || pending.departmentProcessDescription}`,
      highlightSearch,
      textOnly
    );
  }

  private pendingDetailByActivityToCompleteDto(pending: ActivityPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return PendingsActivitiesUtil.pendingDetailByActivityToCompleteDto(this.sanitizer, this.searchValue, pending, highlightSearch, textOnly);
  }

  private pendingDetailByActivityToVerifyDelayedDto(pending: ActivityPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return PendingsActivitiesUtil.pendingDetailByActivityToVerifyDelayedDto(this.sanitizer, this.searchValue, pending, highlightSearch, textOnly);
  }

  private pendingDetailByActivityToVerifyDto(pending: ActivityPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return PendingsActivitiesUtil.pendingDetailByActivityToVerifyDto(this.sanitizer, this.searchValue, pending, highlightSearch, textOnly);
  }

  private pendingFormPendingDto(pending: FormPendingDto, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return PendingsFormsUtil.pendingFormPendingDto(this.sanitizer, this.searchValue, pending, highlightSearch, textOnly);
  }

  private getLinkService(pending: PendingDto, linkedSelector: LinkedSelector): string {
    const pendingTypeConfig: PendingTypeConfig = this.pendingTypeConfig[pending.pendingTypeCode] || {
      module: pending.module
    };
    const moduleConfig: ModuleUnitConfig = this.moduleConfig[pending.module] || {
      icon: MODULE_ICON[pending.module] || 'help',
      menuActions: [],
      selectedMenuActions: [],
      availableMenuActions: []
    };
    const selector = EnumUtil.getName(LinkedSelector, linkedSelector).toLowerCase();
    return pendingTypeConfig[`${selector}Service`] || moduleConfig[`${selector}Service`] || null;
  }

  private getModuleService(pendingTypeCode: string, module: Module): string {
    const pendingTypeConfig: PendingTypeConfig = this.pendingTypeConfig[pendingTypeCode] || {
      module: module
    };
    const moduleConfig: ModuleUnitConfig = this.moduleConfig[module] || {
      icon: MODULE_ICON[module] || 'help',
      menuActions: [],
      selectedMenuActions: [],
      availableMenuActions: []
    };
    const service = pendingTypeConfig.moduleService || moduleConfig.moduleService;
    if (service === null || typeof service === 'undefined' || service === '') {
      return '';
    }
    return service;
  }

  private getLDeleteServices(pending: PendingDto, linkedSelector: LinkedSelector): ProfileServices[] {
    const pendingTypeConfig: PendingTypeConfig = this.pendingTypeConfig[pending.pendingTypeCode] || {
      module: pending.module
    };
    const moduleConfig: ModuleUnitConfig = this.moduleConfig[pending.module] || {
      icon: MODULE_ICON[pending.module] || 'help',
      menuActions: [],
      selectedMenuActions: [],
      availableMenuActions: []
    };
    const linkedName = EnumUtil.getName(LinkedSelector, linkedSelector);
    const selector = `delete${linkedName.substring(0, 1)}${linkedName.substring(1).toLowerCase()}Services`;
    return pendingTypeConfig[selector] || moduleConfig[selector] || null;
  }

  private setTitleDate(): void {
    this.formatBetween = 'dd/MM/yy';
    if (this.filterCalendarActive) {
      this.titleDate = this.translate.instant('between', {
        startDate: StringFormat.titleCase(this.datePipe.transform(this.startDate, this.formatBetween, this.getTimezone(), this.getLang()).toString().substring(0, 5)),
        endDate: StringFormat.titleCase(this.datePipe.transform(this.endDate, this.formatBetween, this.getTimezone(), this.getLang()).toString().substring(0, 5))
      });
    } else {
      if (!DateUtil.isToday(this.currentDate)) {
        this.titleDate = this.translate.instant('until', {
          endDate: this.datePipe.transform(new Date(this.currentDate), 'dd/MM/yy').toString().substring(0, 5)
        });
      } else {
        this.titleDate = this.translate.instant('dateToday');
      }
    }
  }

  private addOrderAscMenuOption() {
    this.pendingsDropdownOptionsAvailable = this.pendingsDropdownOptionsAvailable.filter((item) => item !== PendingsAction.SORT_DESC);
    if (this.pendingsDropdownOptionsAvailable.indexOf(PendingsAction.SORT_ASC) === -1) {
      this.pendingsDropdownOptionsAvailable.push(PendingsAction.SORT_ASC);
      this.cdr.detectChanges();
    }
  }

  private addOrderDescMenuOption() {
    this.pendingsDropdownOptionsAvailable = this.pendingsDropdownOptionsAvailable.filter((item) => item !== PendingsAction.SORT_ASC);
    if (this.pendingsDropdownOptionsAvailable.indexOf(PendingsAction.SORT_DESC) === -1) {
      this.pendingsDropdownOptionsAvailable.push(PendingsAction.SORT_DESC);
      this.cdr.detectChanges();
    }
  }

  private refreshPendingsDropdown() {
    this.pendingsDropdownOptions.find((item: DropdownMenuItem) => item.value === PendingsAction.GROUP_PENDINGS).selected = this.pendingListViewGrouped;
    this.pendingsDropdownOptions.find((item: DropdownMenuItem) => item.value === PendingsAction.FILTER_FROM_TOMORROW).selected = !this.hideFuturePendings;
    this.pendingsDropdownOptions.find((item: DropdownMenuItem) => item.value === PendingsAction.EXACT_SEARCH).selected = this.exactSearch;
    if (this.pendingListSorted === PendingsAction.SORT_DESC) {
      this.addOrderDescMenuOption();
    } else {
      this.addOrderAscMenuOption();
    }
  }

  private reorderPendingsByFilter(): void {
    // Solo si hay más de un filtro aplicado y la lista está agrupada
    if (this.filtersApplied?.length > 1 && this.pendingListViewGrouped) {
      const pendingsGrouped = [];
      const sourceData = from(this.pendingsTemp || []);
      sourceData
        .pipe(
          groupBy((pending) => DateUtil.formatIso(pending.commitmentStartDate)),
          mergeMap((group) => group.pipe(toArray()))
        )
        .pipe(takeUntil(this.$destroy))
        .subscribe((result) => {
          pendingsGrouped.push(result.shift());
          for (const filter of this.filtersApplied) {
            const resultFiltered = result.filter((r) => r.pendingTypeCode.toUpperCase() === filter);
            if (resultFiltered) {
              for (const item of resultFiltered) {
                pendingsGrouped.push(item);
              }
            }
          }
          for (const item of result) {
            if (pendingsGrouped.findIndex((p) => p.recordId === item.recordId) === -1) {
              pendingsGrouped.push(item);
            }
          }
          this.pendingsTemp = pendingsGrouped || [];
        });
    }
  }

  private evaluatePlannerRecordLocked(plannerTask: Partial<PlannerTaskData>): boolean {
    return !!plannerTask.plannerId;
  }

  private evaluateTaskRecordLocked(plannerTask: Partial<PlannerTaskData>): boolean {
    return !!(plannerTask.plannerId && plannerTask.value);
  }
}
