import { BnextCoreComponent } from '@/core/bnext-core.component';
import { type ConditionalEntity, type ConditionalFieldData, ConditionalFieldType } from '@/core/conditional-field/conditional-field.interfaces';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import type { LicensesModuleEntity } from '@/core/licenses/utils/bnext-module';
import { LicensesModules } from '@/core/licenses/utils/licenses-modules';
import { Session } from '@/core/local-storage/session';
import { AppService } from '@/core/services/app.service';
import { TimeUnit, type TimeUnitEntity } from '@/core/time.unit';
import { TimeUnits } from '@/core/time.units';
import type { DynamicFieldEntity } from '@/core/utils/dynamic-field-entity';
import { EnumUtil } from '@/core/utils/enum-util';
import { FormUtil } from '@/core/utils/form-util';
import type { TextHasUserValue, TextHasValue, TextLongValue } from '@/core/utils/text-has-value';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, inject, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup, Validators } from '@angular/forms';
import {
  type IChangeCheckboxEventArgs,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxCheckboxComponent,
  IgxDialogActionsDirective,
  IgxDialogComponent,
  IgxDividerDirective,
  IgxDragDirective,
  IgxDropDirective,
  IgxHintDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective,
  IgxSwitchComponent
} from '@infragistics/igniteui-angular';
import { type Observable, Subject, type Subscription, forkJoin, takeUntil } from 'rxjs';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';

import type { Entity } from 'src/app/core/utils/entity';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FeatureFlag, isFeatureAvailable } from 'src/app/core/utils/feature-util';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivitySupportedModules } from 'src/app/shared/activities/activity-supported-modules';
import { FillType } from 'src/app/shared/activities/core/activities-core.enums';
import {
  ACTIVITY_TYPES_COMPONENT_LANG,
  CONDITIONAL_FIELDS_RULES,
  DEFAULT_FIELD_DEFINITION,
  updateCatalogTaskData
} from 'src/app/shared/activities/core/utils/activity-util';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import type { ActivityTypeEntity, SystemLink } from '../utils/activity-type-entity';
import type { ActivityTypeLinked } from '../utils/activity-type-linked';
import { ImplementationType } from '../utils/implementation-types';
import type { ScopeVerifierEntity } from '../utils/scope-verifiers';
import type { VerificationTypeEntity } from '../utils/verification-type';
import { VerificationType, VerificationTypes } from '../utils/verification-types';
import { ScopeVerifiers, ServiceScope } from './../utils/scope-verifiers';
import type { ActivityFeatureFieldNames, ActivityFeatureOptions, DynamicFieldOrder } from './activity-types.interfaces';

import { ConditionalFieldComponent } from '@/core/conditional-field/conditional-field.component';
import { DragBoardComponent, type ListItem, ListItemState } from '@/core/drag-board/drag-board.component';
import { DropdownButtonComponent } from '@/core/dropdown-button/dropdown-button.component';
import type { FieldDefinition } from '@/core/field-list/field-list.interfaces';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiSelectComponent } from '@/core/multi-select/multi-select.component';
import { SelectComponent } from '@/core/select/select.component';
import { UserSelectComponent } from '@/core/user-select/user-select.component';
import { randomUUID } from '@/core/utils/crypto-utils';
import { cloneObject, keysObject } from '@/core/utils/object';
import { NgClass } from '@angular/common';
import type { DropdownButtonItem } from 'src/app/core/dropdown-button/dropdown-button.interfaces';
import { FieldHandlerUtil } from 'src/app/core/dynamic-field/utils/field-handler-util';
import { CommonAction } from 'src/app/core/utils/enums';
import { ActivityConditionalUtil } from 'src/app/shared/activities/core/utils/activity-conditional-util';
import type { IActivityDepartmentItem, IActivityResolutionItem } from '../activity-resolution/activity-resolution.interfaces';
import { type ScopeImplementerEntity, ScopeImplementers } from '../utils/scope-implementers';
import { VerifierAuthor } from '../utils/verifier-authors';

export interface DefaultValue {
  fieldName: string;
  hidden: boolean;
  disabled: boolean;
}

@Component({
  selector: 'app-activity-types',
  templateUrl: './activity-types.component.html',
  styleUrls: ['./activity-types.component.scss'],
  imports: [
    FormsModule,
    NgClass,
    ReactiveFormsModule,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxLabelDirective,
    IgxCheckboxComponent,
    SelectComponent,
    IgxDividerDirective,
    IgxSwitchComponent,
    IgxHintDirective,
    UserSelectComponent,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxIconComponent,
    IgxSuffixDirective,
    IgxPrefixDirective,
    DragBoardComponent,
    MultiSelectComponent,
    IgxDragDirective,
    IgxDropDirective,
    IgxBadgeComponent,
    ConditionalFieldComponent,
    DropdownButtonComponent,
    IgxDialogComponent,
    IgxDialogActionsDirective,
    BnextTranslatePipe
  ]
})
export class ActivityTypesComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy {
  noticeService = inject(NoticeService);
  service = inject(AppService);

  formBuilder = inject(UntypedFormBuilder);

  public static LANG_CONFIG: BnextComponentPath = ACTIVITY_TYPES_COMPONENT_LANG;

  public static readonly IMPLEMENTER_USER = 'implementerUser';
  public static readonly VERIFIER_USER = 'verifierUser';
  public static readonly IMPLEMENTER = 'implementer';
  public static readonly VERIFIER = 'verifier';
  public static readonly SOURCE = 'source';
  public static readonly OBJECTIVE = 'objectiveId';
  public static readonly PRIORITY = 'priority';
  public static readonly RESOLUTION = 'activityResolutionId';
  public static readonly FILL_TYPE = 'fillType';
  public static readonly DEPARTMENT = 'businessUnitDepartmentId';
  public static readonly CATEGORY = 'categoryId';
  public static readonly FILL_FORM = 'fillForm';
  public static readonly PLANNER_TASK = 'plannerTask';
  public static readonly RESOLUTION_FIELD_DEFAULT = 'resolutionDefault';

  private _isAllowedUseOnPlanning = true;
  private _isVerificationAvailable = true;
  private _isImplementationAvailable = true;
  private _isCustomVerifierAvailable = true;
  private _hiddenFieldIndicator: DataMap<string> = {};

  get isAllowedUseOnPlanning(): boolean {
    return this._isAllowedUseOnPlanning;
  }

  get isVerificationAvailable(): boolean {
    return this._isVerificationAvailable;
  }

  get isCustomVerifierAvailable(): boolean {
    return this._isCustomVerifierAvailable;
  }

  get isImplementationAvailable(): boolean {
    return this._isImplementationAvailable;
  }

  get defaultModule(): string {
    return Module.ACTIVITY.toLowerCase();
  }
  get defaultNotifyVerifyTime(): number {
    return null;
  }
  get fillTypeDefaultValue(): string {
    return EnumUtil.getValue(FillType, FillType.PROGRESS);
  }
  override get componentPath(): string {
    return 'modules.settings.activity-settings';
  }

  get titleTag(): string {
    return 'activity-types-add-title';
  }

  get addSubtaskByDepartmentTag(): string {
    return 'activity-type-addSubtaskByDepartment';
  }

  get isActivityRecurrencesAvailable() {
    if (!this.mainForm) {
      return false;
    }
    return this.mainForm.controls.module.value === Module.ACTIVITY.toLowerCase();
  }

  get isActivityStartDateEditable() {
    if (!this.mainForm) {
      return false;
    }
    return this.mainForm.controls.module.value !== Module.PLANNER.toLowerCase();
  }

  get hiddenAddSubtaskByDepartment() {
    if (!this.mainForm) {
      return true;
    }
    return (
      (this.mainForm.controls.module.value !== Module.PLANNER.toLowerCase() && this.mainForm.controls.module.value !== Module.ACTIVITY.toLowerCase()) ||
      this.isHidden('addSubtaskByDepartment')
    );
  }

  get isEdit(): boolean {
    return false;
  }

  get verificationOnCreate(): boolean {
    return true;
  }

  id: number;
  isSystemLinksAvailable = isFeatureAvailable(FeatureFlag.ACTIVITY_TYPE_SYSTEM_LINKS);
  isTurnOffImplementerAvailable = isFeatureAvailable(FeatureFlag.ACTIVITY_NO_IMPLEMENTER);
  isTimesheetAvailable = Session.hasService(ProfileServices.TS_REGISTER_PLANNED) || Session.hasService(ProfileServices.TS_REGISTER_UNPLANNED);
  isPlannerAvailable = isFeatureAvailable(FeatureFlag.PLANNER_MODULE);
  isPeriodicityEnabled = false;
  isFillFormAvailable = false;
  multipleFillTypes = false;
  showDynamicFieldsConfig: DataMap<boolean> = {};
  fillTypePercentageDefaultValue = true;
  scopeVerifierDefaultValue = ServiceScope.BUSINESS_UNIT;
  scopeImplementerDefaultValue = ServiceScope.BUSINESS_UNIT;
  errorMessage = '';
  moduleDisabled = false;
  systemLinks: SystemLink[] = [];
  conditionalFields: ConditionalFieldData[] = [];
  conditionalFieldsIndex: DataMap<FieldDefinition> = {};
  conditionalFieldsRules = CONDITIONAL_FIELDS_RULES;
  hiddenFields: string[] = ['addDeliverySetUp', 'verificationReqOnPlanning'];
  availableVisibilityFieldNames = [
    'showGroupName',
    'showType',
    'showBusinessUnitDepartment',
    'showCode',
    'showDescription',
    'showStartDate',
    'showImplementerUser',
    'showImplementation',
    'showVerifierUser',
    'showVerification',
    'showDaysToVerify',
    'showImplementationPeriodicity',
    'showVerificactionPeriodicity',
    'showSource',
    'showFillType',
    'showFillForm',
    'showObjective',
    'showPriority',
    'showIsPlanned',
    'showPlannedHours',
    'defaultLoggedUserDepartment',
    'showActivityOrder'
  ];
  disableShowSource = true;
  disableShowPriority = true;
  disableActivityObjective = true;
  disableResponsible = true;
  disableVerificationOnCreate = true;
  disableShowImplementation = true;
  disableShowResolution = true;

  hideAllowsPreAssignment = true;

  availableCustomsVisibilityFieldNames = { showImplementation: 'showImplementation' };

  readonly availableVisibilityFieldNamesOrder = cloneObject(this.availableVisibilityFieldNames);
  readonly featureFieldNames: ActivityFeatureFieldNames = {
    defaultValues: null,
    nullify: [],
    turnedOff: [],
    common: ['verificationType', 'showVerification', 'showVerifierUser', 'showDaysToVerify', 'showVerificactionPeriodicity'],
    implementation: ['showImplementerUser', 'showStartDate', 'showImplementation', 'showImplementationPeriodicity'],
    verification: [
      'authorEditVerification',
      'authorEditVerifier',
      'defaultDaysToVerify',
      'defaultVerifierAuthor',
      'notifyVerifyTime',
      'notifyVerifyTimeUnit',
      'scopeVerifier',
      'defaultCustomVerifier',
      'verifierEditApply',
      'verifierEditDynamicFields'
    ]
  };
  readonly defaultFieldsDef = DEFAULT_FIELD_DEFINITION;

  readonly sucessSaveDialog = viewChild<IgxDialogComponent>('sucessSaveDialog');
  readonly failSaveDialog = viewChild<IgxDialogComponent>('failSaveDialog');
  readonly fillTypePercentage = viewChild<IgxSwitchComponent>('fillTypePercentage');
  readonly addImplementationOnCreate = viewChild<IgxSwitchComponent>('addImplementationOnCreate');
  readonly addVerificationOnCreate = viewChild<IgxSwitchComponent>('addVerificationOnCreate');
  readonly verificationReqOnPlanning = viewChild<IgxSwitchComponent>('verificationReqOnPlanning');
  readonly addVerificationAvailable = viewChild<IgxSwitchComponent>('addVerificationAvailable');
  readonly fillTypeForm = viewChild<IgxSwitchComponent>('fillTypeForm');
  readonly fillTypeDone = viewChild<IgxSwitchComponent>('fillTypeDone');
  readonly fillTypePlannerTask = viewChild<IgxSwitchComponent>('fillTypePlannerTask');
  readonly fillTypePlanner = viewChild<IgxSwitchComponent>('fillTypePlanner');
  readonly allowsUseOnPlanning = viewChild<IgxSwitchComponent>('allowsUseOnPlanning');
  readonly saveAsCopy = viewChild<IgxDialogComponent>('dialogSaveAsCopy');

  showHelp = false;
  loadLabelsSucription: Subscription;
  initSubscription: Subscription;
  dynamicFieldOrder: DynamicFieldOrder[];
  dragFieldDynamicId: number;
  redirectOptions: DropdownButtonItem[] = [
    { value: CommonAction.SAVE, text: 'save', iconName: 'save' },
    { value: CommonAction.SAVE_AS_COPY, text: 'save-as-copy', iconName: 'difference' }
  ];
  private copyDescriptionName: string = null;
  public saveAsCopyForm: UntypedFormGroup = null;

  constructor() {
    super();

    this.initConstant();
  }

  moduleLabelOverride: Module = null;
  touched = false;
  mainForm: UntypedFormGroup;
  modules: LicensesModuleEntity[];
  timeUnits: TimeUnitEntity[];
  dynamicFields: DynamicFieldEntity[];
  dynamicFieldsDb = FieldHandlerUtil.defaultDatabaseInstance();
  selectedDynamicFields: DynamicFieldEntity[];
  groupsValues: TextLongValue[];
  preAssignGroupsValues: TextLongValue[];
  verificationTypes: VerificationTypeEntity[];
  scopeVerifiers: ScopeVerifierEntity[];
  scopeImplementers: ScopeImplementerEntity[];
  // ############ Nomeclature #################
  private nomeclatureIcons: { id: string; icon: string }[] = [
    { id: 'textField', icon: 'text_fields' },
    { id: 'sequence', icon: 'pin' },
    { id: 'objective', icon: 'build' },
    { id: 'source', icon: 'label' },
    { id: 'priority', icon: 'priority_high' },
    { id: 'category', icon: 'category' },
    { id: 'businessUnit', icon: 'place' },
    { id: 'businessUnitDepartment', icon: 'place' },
    { id: 'type', icon: 'bloodtype' }
  ];
  nomeclatureValues: ListItem[] = [];
  nomeclatureListChip: ListItem[] = [{ id: 'sequence', text: 'sequence', state: ListItemState.secondary, infinite: false }];
  nomeclatureLockedToMain: 'sequence';
  registryCode = '';
  // -----------------------------------------------------
  implementationTypes: {
    id: number;
    description?: string;
    help?: string;
  }[];
  implementerUser: DefaultValue = {
    fieldName: ActivityTypesComponent.IMPLEMENTER_USER,
    hidden: false,
    disabled: false
  };
  verifierUser: DefaultValue = {
    fieldName: ActivityTypesComponent.VERIFIER_USER,
    hidden: true,
    disabled: false
  };
  source: DefaultValue = {
    fieldName: ActivityTypesComponent.SOURCE,
    hidden: false,
    disabled: false
  };
  objective: DefaultValue = {
    fieldName: ActivityTypesComponent.OBJECTIVE,
    hidden: false,
    disabled: false
  };
  priority: DefaultValue = {
    fieldName: ActivityTypesComponent.PRIORITY,
    hidden: false,
    disabled: false
  };
  resolution: DefaultValue = {
    fieldName: ActivityTypesComponent.RESOLUTION,
    hidden: false,
    disabled: false
  };
  resolutionDefault: DefaultValue = {
    fieldName: ActivityTypesComponent.RESOLUTION_FIELD_DEFAULT,
    hidden: false,
    disabled: true
  };
  catalogs: DataMap<any[]> = {};
  busy = false;

  get hiddenCommitmentDate() {
    return !this.isPeriodicityEnabled && this.mainForm.value.customImplementationDate === 2 && !this.mainForm.value.restrictAnticipationAttend;
  }

  override ngOnInit(): void {
    this.mainForm = this.formBuilder.group({
      allowsPreAssignment: new UntypedFormControl(false),
      code: new UntypedFormControl(),
      generateCode: new UntypedFormControl(true),
      description: new UntypedFormControl(null, Validators.required),
      module: new UntypedFormControl(null, Validators.required),
      verificationType: new UntypedFormControl(null),
      maxOpenTime: new UntypedFormControl(null, Validators.required),
      maxOpenTimeUnit: new UntypedFormControl(null, Validators.required),
      notifyImplementTime: new UntypedFormControl(null, Validators.required),
      notifyImplementTimeUnit: new UntypedFormControl(null, Validators.required),
      notifyVerifyTime: new UntypedFormControl(this.defaultNotifyVerifyTime),
      notifyVerifyTimeUnit: new UntypedFormControl(),
      showGroupName: new UntypedFormControl(),
      defaultCustomVerifier: new UntypedFormControl(),
      showType: new UntypedFormControl(),
      showBusinessUnitDepartment: new UntypedFormControl(),
      showCode: new UntypedFormControl(),
      showDescription: new UntypedFormControl(),
      showStartDate: new UntypedFormControl(),
      showImplementerUser: new UntypedFormControl(),
      showImplementation: new UntypedFormControl(),
      showVerifierUser: new UntypedFormControl(),
      showVerification: new UntypedFormControl(),
      showDaysToVerify: new UntypedFormControl(),
      showFillType: new UntypedFormControl(),
      showFillForm: new UntypedFormControl(),
      showSource: new UntypedFormControl(),
      showObjective: new UntypedFormControl(),
      showPriority: new UntypedFormControl(),
      showImplementationPeriodicity: new UntypedFormControl(),
      showVerificactionPeriodicity: new UntypedFormControl(),
      showIsPlanned: new UntypedFormControl(false),
      showPlannedHours: new UntypedFormControl(false),
      enablePeriodicity: new UntypedFormControl(),
      customImplementationDate: new UntypedFormControl(null),
      authorEditImplementer: new UntypedFormControl(),
      authorEditBusinessUnitDepartment: new UntypedFormControl(),
      followUpImplementationDelay: new UntypedFormControl(true),
      authorEditVerifier: new UntypedFormControl(),
      authorEditImplementation: new UntypedFormControl(),
      authorEditVerification: new UntypedFormControl(),
      authorEditApply: new UntypedFormControl(),
      authorEditDynamicFields: new UntypedFormControl(),
      implementerEditDynamicFields: new UntypedFormControl(),
      implementerEditBusinessUnitDeparment: new UntypedFormControl(),
      preassignedUserEditClientPlannerTask: new UntypedFormControl(),
      dynamicFieldViewName: new UntypedFormControl(),
      verifierEditDynamicFields: new UntypedFormControl(),
      verifierEditBusinessUnitDepartment: new UntypedFormControl(),
      implementerEditApply: new UntypedFormControl(),
      verifierEditApply: new UntypedFormControl(),
      restrictAnticipationAttend: new UntypedFormControl(false),
      dynamicFields: [''],
      resolutionValues: [''],
      categoryValues: [''],
      groupsValues: [''],
      anticipationAttendDays: new UntypedFormControl(0),
      defaultDaysToVerify: new UntypedFormControl(0),
      fillTypes: new UntypedFormControl(this.fillTypeDefaultValue, Validators.required),
      defaultImplementerAuthor: new UntypedFormControl(null),
      defaultVerifierAuthor: new UntypedFormControl(null),
      scopeVerifier: new UntypedFormControl(null),
      scopeImplementer: new UntypedFormControl(null),
      defaultActivitySourceId: new UntypedFormControl(null, Validators.required),
      defaultActivityObjectiveId: new UntypedFormControl(null, Validators.required),
      defaultPriorityId: new UntypedFormControl(null, Validators.required),
      defaultIsPlanned: new UntypedFormControl(true),
      defaultPlannedHours: new UntypedFormControl(null),
      workingHoursAvailable: new UntypedFormControl(),
      addImplementationOnCreate: new UntypedFormControl(true),
      addVerificationOnCreate: new UntypedFormControl(this.verificationOnCreate),
      allowsUseOnPlanning: new UntypedFormControl(false),
      verificationReqOnPlanning: new UntypedFormControl(false),
      addVerificationAvailable: new UntypedFormControl(false),
      projectsAvailable: new UntypedFormControl(),
      addSubtaskByDepartment: new UntypedFormControl(),
      authorEditRecurrence: new UntypedFormControl(),
      authorDeleteRecurrence: new UntypedFormControl(),
      addDeliverySetUp: new UntypedFormControl(),
      defaultLoggedUserDepartment: new UntypedFormControl(false),
      mustUpdateImplementationAtReturn: new UntypedFormControl(true),
      defaultResolutionId: new UntypedFormControl(null),
      departmentsValues: [''],
      showActivityOrder: new UntypedFormControl(false),
      preAssignGroupsValues: ['']
    });
    this.saveAsCopyForm = this.formBuilder.group({
      copyName: new UntypedFormControl(null, Validators.required)
    });
    this.dynamicFieldOrder = [];
    this.initializeRedirectOptions();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.mainFormPatch();
    this.initSubscription = this.init().subscribe(() => {
      this.busy = false;
    });
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.loadLabelsSucription) {
      this.loadLabelsSucription.unsubscribe();
    }
    if (this.initSubscription) {
      this.initSubscription.unsubscribe();
    }
    this.cdr.detach();
  }

  mainFormPatch() {
    this.mainForm.patchValue({
      scopeVerifier: this.scopeVerifierDefaultValue,
      scopeImplementer: this.scopeImplementerDefaultValue,
      fillTypes: this.fillTypeDefaultValue,
      module: this.defaultModule,
      maxOpenTimeUnit: TimeUnit.DAY,
      notifyVerifyTime: this.defaultNotifyVerifyTime,
      notifyImplementTimeUnit: TimeUnit.DAY,
      notifyVerifyTimeUnit: TimeUnit.DAY,
      showGroupName: 1,
      showType: 1,
      showBusinessUnitDepartment: 1,
      showDescription: 1,
      showImplementerUser: 1,
      showVerifierUser: 1,
      showIsPlanned: false,
      showPlannedHours: false,
      showActivityOrder: false
    });
    this.updateFillTypes();
  }

  getCatalogName(fieldName: string): string {
    if (fieldName === null || typeof fieldName === 'undefined' || fieldName === '') {
      return null;
    }
    const fieldDef = this.defaultFieldsDef[fieldName];
    if (fieldDef === null || typeof fieldDef === 'undefined') {
      return null;
    }
    return fieldDef.catalogName;
  }

  getCatalogNames(fieldName: string): string[] {
    if (fieldName === null || typeof fieldName === 'undefined' || fieldName === '') {
      return null;
    }
    const fieldDef = this.defaultFieldsDef[fieldName];
    if (fieldDef === null || typeof fieldDef === 'undefined') {
      return null;
    }
    return fieldDef.catalogNames;
  }

  setCatalogValue(fieldName: string, value: any[]): void {
    const catalogName = this.getCatalogName(fieldName);
    if (catalogName === null || typeof catalogName === 'undefined') {
      this.catalogs[fieldName] = value;
    } else {
      this.catalogs[catalogName] = value;
    }
  }

  initConstant(): void {
    this.setCatalogValue(ActivityTypesComponent.SOURCE, [
      {
        text: 'noSourceLabel',
        value: 0
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.OBJECTIVE, [
      {
        text: 'noObjectiveLabel',
        value: 0
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.PRIORITY, [
      {
        text: 'noPriorityLabel',
        value: 0
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.IMPLEMENTER_USER, [
      {
        text: 'noImplementerLabel',
        value: 0
      },
      {
        text: 'authorImplementerLabel',
        value: 1
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.VERIFIER_USER, [
      {
        text: 'noVerifierLabel',
        value: 0
      },
      {
        text: 'authorVerifierLabel',
        value: 1
      },
      {
        text: 'specificUserLabel',
        value: 2
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.RESOLUTION_FIELD_DEFAULT, [
      {
        text: 'noResolutionLabel',
        value: 0
      }
    ]);
    this.setCatalogValue(ActivityTypesComponent.FILL_TYPE, [
      {
        text: 'noResolutionLabel',
        value: 0
      }
    ]);
    this.translate.getFrom(ACTIVITY_TYPES_COMPONENT_LANG, 'defaultLabels').subscribe((tags) => {
      for (const property in this.catalogs) {
        if (this.catalogs.hasOwnProperty(property)) {
          for (const element of this.catalogs[property]) {
            element.text = tags[element.text];
          }
        }
      }
    });
    this.lang(ActivityTypesComponent.LANG_CONFIG, 'invalidNumber');
    this.defineConditionalFieldsIndex();
  }

  private defineConditionalFieldsIndex() {
    this.conditionalFieldsIndex = {};
    for (const field1 of ActivityConditionalUtil.AVAILABLE_FIELD_NAMES.filter((field) => {
      const allow = !!this.defaultFieldsDef[field];
      if (!allow) {
        console.error(`Missing configuraton for conditional field ${field}.`);
      }
      return allow;
    })) {
      this.conditionalFieldsIndex[field1] = this.defaultFieldsDef[field1];
    }
    if (this.selectedDynamicFields) {
      for (const dynamicField of this.selectedDynamicFields) {
        this.conditionalFieldsIndex[dynamicField.name] = {
          name: dynamicField.name,
          label: dynamicField.label,
          fieldType: null,
          dataType: null,
          required: null,
          isTemporaryValue: null,
          isDynamicField: true,
          dynamicConfig: dynamicField,
          icon: null
        };
      }
    }
  }

  private getInitUrl(): string {
    const initUrl = `${this.controller}/init-data/${this.scopeVerifierDefaultValue}`;
    return initUrl;
  }

  init(): Observable<boolean> {
    this.modules = LicensesModules.getLocalizedValues(
      {
        translateService: this.translateService,
        $destroy: this.$destroy,
        subs: []
      },
      ActivitySupportedModules.VALUES
    );
    this.timeUnits = TimeUnits.getLocalizedValues(this.$destroy, this.translate, ACTIVITY_TYPES_COMPONENT_LANG);
    this.verificationTypes = VerificationTypes.getLocalizedValues({
      translateService: this.translate,
      subs: this.subs
    });
    this.scopeVerifiers = ScopeVerifiers.getLocalizedValues({
      translateService: this.translate,
      subs: this.subs
    });
    this.scopeImplementers = ScopeImplementers.getLocalizedValues({
      translateService: this.translate,
      subs: this.subs
    });
    const implementationTypeLoad = this.translate.getFrom(ActivityTypesComponent.LANG_CONFIG, 'implementationType');
    const initUrl = this.getInitUrl();
    const initData = this.service.get({ cancelableReq: this.$destroy, url: initUrl, handleFailure: false });
    const loaded = forkJoin([implementationTypeLoad, initData]);
    const subjectResult = new Subject<any>();
    const initResult = subjectResult.asObservable();
    this.fillTypePercentage().checked = this.fillTypePercentageDefaultValue;
    this.loadLabelsSucription = loaded.subscribe({
      next: ([labels, result]) => {
        this.implementationTypes = [
          {
            id: 2,
            description: labels[2]
          },
          {
            id: 1,
            description: labels[1]
          }
        ];
        this.dynamicFields = result.dynamicFields || [];
        this.groupsValues = result.groupsValues || [];
        this.preAssignGroupsValues = result.preAssignGroupsValues || [];
        updateCatalogTaskData(result.clients, result.planners, result.tasks);
        this.fillCatalog(result, ActivityTypesComponent.RESOLUTION);
        this.fillCatalog(result, ActivityTypesComponent.CATEGORY);
        this.fillCatalog(result, ActivityTypesComponent.FILL_FORM);
        this.fillCatalog(result, ActivityTypesComponent.PLANNER_TASK);
        this.fillCatalog(result, ActivityTypesComponent.DEPARTMENT);
        this.fillCatalog(result, ActivityTypesComponent.IMPLEMENTER);
        this.fillCatalog(result, ActivityTypesComponent.VERIFIER);
        this.fillCatalog(result, ActivityTypesComponent.SOURCE);
        this.fillCatalog(result, ActivityTypesComponent.OBJECTIVE);
        this.fillCatalog(result, ActivityTypesComponent.PRIORITY);
        if (result.nomeclature && this.controller.includes(this.defaultModule)) {
          const nomeclature = keysObject(JSON.parse(result.nomeclature));
          this.nomeclatureValues.push({ id: 'textField', text: 'textField', state: ListItemState.main, isTextField: true, infinite: true });
          for (const item of nomeclature) {
            this.nomeclatureValues.push({ id: item, text: item, state: ListItemState.main, infinite: false });
          }
          this.fillNomeclatureLabels();
        }
        subjectResult.next(true);
      },
      error: (error) => subjectResult.error(error)
    });
    this.cdr.detectChanges();
    return initResult;
  }

  private refreshInitData(): void {
    this.busy = true;
    const initUrl = this.getInitUrl();
    const initData = this.service.get({ cancelableReq: this.$destroy, url: initUrl, handleFailure: false });
    initData.subscribe({
      next: (result) => {
        this.fillCatalog(result, ActivityTypesComponent.VERIFIER_USER);
        this.cdr.detectChanges();
        this.busy = false;
      },
      error: () => (this.busy = false)
    });
  }

  private fillNomeclatureLabels(): void {
    this.translate.getFrom(ACTIVITY_TYPES_COMPONENT_LANG, 'nomeclatureLabels').subscribe((nomeclatures: DataMap<string>) => {
      for (const item of this.nomeclatureValues) {
        item.text = nomeclatures[item.id];
        item.icon = this.nomeclatureIcons.find((i) => i.id === item.id).icon;
      }
      for (const item of this.nomeclatureListChip) {
        item.text = nomeclatures[item.id];
        item.icon = this.nomeclatureIcons.find((i) => i.id === item.id).icon;
      }
      this.cdr.detectChanges();
    });
  }

  private fixAvailableVisibilityFieldNamesOrder() {
    this.availableVisibilityFieldNames = cloneObject(this.availableVisibilityFieldNamesOrder).filter((fieldName) => {
      return this.availableVisibilityFieldNames.indexOf(fieldName) !== -1;
    });
  }

  protected fillCatalog(source: DataMap<any[]>, fieldName: string): void {
    const catalogNames = this.getCatalogNames(fieldName);
    if (catalogNames?.length > 0) {
      for (const catalogName1 of catalogNames) {
        this.fillCatalogByCatalogName(source, catalogName1);
      }
      return;
    }
    const catalogName = this.getCatalogName(fieldName);
    if (catalogName === null || typeof catalogName === 'undefined') {
      console.error(`Can not set catalogs for ${fieldName}.`);
      return;
    }
    this.fillCatalogByCatalogName(source, catalogName);
  }

  private fillCatalogByCatalogName(source: DataMap<any[]>, catalogName: string): void {
    const values = (source[catalogName] || []).map((a) => {
      if (a.value !== null && typeof a.value !== 'undefined') {
        a.value = +a.value;
      }
      return a;
    });
    if (this.catalogs[catalogName]) {
      this.catalogs[catalogName] = this.catalogs[catalogName].concat(values);
    } else {
      this.catalogs[catalogName] = values;
    }
  }

  toogleDefaultAuthor(fieldName: string, shown: boolean, defaultAuthor1: number | string) {
    const defaultAuthor = +defaultAuthor1;
    if (this.busy) {
      return;
    }
    switch (fieldName) {
      case 'verifierUser':
        this.updateCustomVerifierAvailable(defaultAuthor);
        break;
    }
    const action = !shown || defaultAuthor === 1;
    if (!action) {
      return;
    }
    this.disableResponsible = defaultAuthor !== 1;
    switch (fieldName) {
      case 'implementerUser':
        this.mainForm.patchValue({
          showImplementerUser: false,
          showVerifierUser: true,
          defaultVerifierAuthor: 0
        });
        break;
      case 'verifierUser':
        this.mainForm.patchValue({
          showImplementerUser: true,
          showVerifierUser: false,
          defaultImplementerAuthor: 0
        });
        break;
    }
  }

  protected updateCustomVerifierAvailable(defaultAuthor: number): void {
    if (this.isVerificationAvailable) {
      this._isCustomVerifierAvailable = defaultAuthor === VerifierAuthor.CUSTOM_USER;
    } else {
      this._isCustomVerifierAvailable = false;
    }
  }

  onChangeScopeVerifier(): void {
    if (this.busy) {
      return;
    }
    this.refreshInitData();
  }

  private isMinimumHiddenFieldsReached(fieldName: string, event: IChangeCheckboxEventArgs): boolean {
    if (!event.checked) {
      let shownItems = this.availableVisibilityFieldNames.filter((value) => value !== fieldName && !this.isDisabled(value) && this.mainForm.value[value]).length;
      if (shownItems === 0 && this.selectedDynamicFields) {
        shownItems += this.selectedDynamicFields.filter(
          (value) => this.getDynamicFieldName(value.id) !== fieldName && this.mainForm.value[this.getDynamicFieldName(value.id)]
        ).length;
      }
      if (shownItems === 0) {
        this.translate.getFrom(ActivityTypesComponent.LANG_CONFIG, 'maximum-hidden-fields').subscribe((tag) => this.noticeService.notice(tag));
        return true;
      }
    }
    return false;
  }

  private enableSwitch(fieldName: string) {
    const patch: DataMap = {};
    patch[fieldName] = true;
    this.mainForm.patchValue(patch);
  }

  onChangeVisibleField(fieldName: string, event: IChangeCheckboxEventArgs) {
    const reached = this.isMinimumHiddenFieldsReached(fieldName, event);
    if (reached) {
      this.enableSwitch(fieldName);
    }
  }

  isDisabled(fieldName: string) {
    switch (fieldName) {
      case 'showStartDate': {
        const disable = !this.isPeriodicityEnabled && !this.mainForm.value.restrictAnticipationAttend;
        if (disable) {
          if (this.disableShowImplementation) {
            this.mainForm.patchValue({
              showStartDate: disable
            });
          }
          return disable;
        }
        return disable;
      }
      case 'showImplementation':
        if (this.mainForm.value.customImplementationDate === 1) {
          return false;
        }
        if (this.disableShowImplementation) {
          this.mainForm.patchValue({
            showImplementation: true
          });
          return this.disableShowImplementation;
        }
        return this.mainForm.value.customImplementationDate === 2;
      case 'showVerification':
        if (this.mainForm.value.verificationType === 1) {
          return false;
        }
        if (this.disableVerificationOnCreate) {
          this.mainForm.patchValue({
            showVerification: true
          });
          return this.disableVerificationOnCreate;
        }
        return this.mainForm.value.verificationType === 2;
      case 'showDaysToVerify':
        if (this.mainForm.value.verificationType === 2) {
          return false;
        }
        if (this.disableVerificationOnCreate) {
          this.mainForm.patchValue({
            showDaysToVerify: true
          });
          return this.disableVerificationOnCreate;
        }
        return this.mainForm.value.verificationType === 1;
      case 'showImplementationPeriodicity':
      case 'showVerificactionPeriodicity':
        return !this.isPeriodicityEnabled;
      case 'showFillType':
        return !this.multipleFillTypes;
      case 'showFillForm':
        return !this.isFillFormAvailable;
      case 'showSource':
        if (this.disableShowSource) {
          this.mainForm.patchValue({
            showSource: true
          });
        }
        return this.disableShowSource;
      case 'showPriority':
        if (this.disableShowPriority) {
          this.mainForm.patchValue({
            showPriority: true
          });
        }
        return this.disableShowPriority;
      case 'showObjective':
        if (this.disableActivityObjective) {
          this.mainForm.patchValue({
            showObjective: true
          });
        }
        return this.disableActivityObjective;
      case 'showImplementerUser':
        if (this.disableResponsible) {
          this.mainForm.patchValue({
            showImplementerUser: true
          });
        }
        return this.disableResponsible;
      case 'showVerifierUser':
        if (this.disableVerificationOnCreate) {
          this.mainForm.patchValue({
            showVerifierUser: true
          });
        }
        return this.disableVerificationOnCreate;
    }
    return false;
  }

  private isConditionalFieldsValid(): boolean {
    const valid = this.conditionalFields.every((field) => {
      const valid = field.assignedFields?.length > 0 && field.matchFields?.length > 0;
      if (!valid) {
        field.invalid = true;
        return false;
      }
      field.invalid = false;
      const countNull =
        field.assignedFields?.filter((value) => value.value === null || typeof value.value === 'undefined').length +
        field.matchFields?.filter((value) => value.value === null || typeof value.value === 'undefined').length;
      return countNull === 0;
    });
    return valid;
  }

  saveType(_event: DropdownButtonItem): void {
    switch (_event.value) {
      case CommonAction.SAVE:
        this.save(false);
        break;
      case CommonAction.SAVE_AS_COPY:
        this.onOpenDialogSaveAsCopy();
        break;
      default:
        console.log(`The option ${_event.value} is not recognized`);
        break;
    }
  }

  save(isCopy: boolean): void {
    const isLinksValid = this.isSystemLinksValid();
    const isConditionValid = this.isConditionalFieldsValid();
    if (!FormUtil.isValid(this.mainForm).status || !isLinksValid || !isConditionValid) {
      this.touched = true;
      this.translate.getFrom(ActivityTypesComponent.LANG_CONFIG, 'requiredNotice').subscribe((tag) => this.noticeService.notice(tag));
      this.cdr.detectChanges();
      return;
    }
    const entity: ActivityTypeEntity = this.getEntityData();
    if (isCopy) {
      entity.description = this.copyDescriptionName;
      entity.isCopy = true;
      if (entity.linked.conditionalFields.length > 0) {
        for (const conditional of entity.linked.conditionalFields) {
          conditional.id = null;
        }
      }
    }
    this.busy = true;
    this.loader.show();
    this.service.post({ url: `${this.controller}/save`, cancelableReq: this.$destroy, postBody: entity, options: null, handleFailure: false }).subscribe(
      (_result) => {
        this.loader.hide();
      },
      (result) => {
        this.busy = false;
        this.loader.hide();
        if (result?.error === 'exist_record') {
          this.errorMessage = this.translate.instantFrom(ActivityTypesComponent.LANG_CONFIG, 'dialog-fail-unique-save-message');
          this.failSaveDialog().open();
        } else {
          ErrorHandling.notifyError(result, this.navLang);
        }
        this.cdr.detectChanges();
      },
      () => {
        this.busy = false;
        this.loader.hide();
        this.sucessSaveDialog().open();
        this.cdr.detectChanges();
      }
    );
  }

  return(): void {
    const url = 'menu/settings/activity-settings/activity-types/list';
    this.menuService.navigate(url, Module.CONFIGURATION);
  }

  resetForm(): void {
    this.busy = true;
    this.showDynamicFieldsConfig = {};
    this.selectedDynamicFields = [];
    this.mainForm.reset();
    this.busy = false;
  }

  closeDialogSave(): void {
    this.menuService.navigate('menu/settings/activity-settings/activity-types/add', Module.CONFIGURATION);
  }

  closeDialogList(): void {
    this.sucessSaveDialog().close();
    this.return();
    this.cdr.detectChanges();
  }

  closeDialogSaveFail(): void {
    this.failSaveDialog().close();
    this.cdr.detectChanges();
  }

  changeGenerateCode(event): void {
    if (event.checked) {
      this.mainForm.controls.code.disable();
    } else {
      this.mainForm.controls.code.enable();
    }
  }

  changeModule(val: string | number): void {
    const value = val as string;
    switch (value?.toUpperCase()) {
      case Module.AUDIT:
      case Module.ACTION:
      case Module.MEETING:
        this.mainForm.patchValue({
          authorDeleteRecurrence: false,
          authorEditRecurrence: false,
          enablePeriodicity: false,
          showDaysToVerify: false,
          showImplementationPeriodicity: false,
          showStartDate: false,
          showVerificactionPeriodicity: false
        });
        break;
      case Module.PLANNER:
        this.mainForm.patchValue({
          authorDeleteRecurrence: false,
          authorEditRecurrence: false,
          authorEditImplementer: true,
          authorEditBusinessUnitDepartment: true,
          followUpImplementationDelay: true,
          implementerEditDynamicFields: true,
          authorEditImplementation: true,
          authorEditApply: true,
          authorEditVerifier: true,
          authorEditVerification: true,
          verifierEditApply: true,
          enablePeriodicity: false,
          restrictAnticipationAttend: true,
          verifierEditDynamicFields: false,
          verifierEditBusinessUnitDepartment: false,
          showImplementation: true,
          implementerEditBusinessUnitDeparment: true,
          showVerifierUser: false,
          showVerification: false,
          showDaysToVerify: true,
          showImplementationPeriodicity: false,
          showStartDate: true,
          showVerificactionPeriodicity: false,
          defaultImplementerAuthor: 0,
          defaultVerifierAuthor: 1,
          customImplementationDate: ImplementationType.SPECIFIC_DATE,
          verificationType: VerificationType.SAME_DAY_WITH_TOLERARANCE,
          addDeliverySetUp: false
        });
        this.moduleLabelOverride = Module.PLANNER;
        break;
    }
    this.cdr.detectChanges();
  }

  onChangeCustomImplementationDate(val: number | string) {
    const value = val as number;
    if (this.busy) {
      return;
    }
    if (+value === 2) {
      this.mainForm.patchValue({
        showImplementation: false
      });
    }
  }

  onChangeVerificationType(val: number | string) {
    const value = val as number;
    if (this.busy) {
      return;
    }
    if (+value === 2) {
      this.mainForm.patchValue({
        showVerification: false,
        showDaysToVerify: true
      });
    } else if (+value === 1) {
      this.mainForm.patchValue({
        showVerification: true,
        showDaysToVerify: false
      });
    }
    this.cdr.detectChanges();
  }

  onVerificationOnCreateToggle(event: IChangeCheckboxEventArgs): void {
    let addImplementationOnCreate = this.addImplementationOnCreate().checked;
    if (event.checked) {
      // la implementación debe estar "ON" para que la verificación pueda habilitarse
      addImplementationOnCreate = true;
    }
    this.disableVerificationOnCreate = event.checked;
    this.onActivityFeatureToggle({
      addImplementationOnCreate: addImplementationOnCreate,
      addVerificationOnCreate: event.checked
    });
  }

  onAllowsUseOnPlanningToggle(event: IChangeCheckboxEventArgs): void {
    let addImplementationOnCreate = this.addImplementationOnCreate().checked;
    let addVerificationOnCreate = this.addVerificationOnCreate().checked;
    if (!event.checked) {
      addImplementationOnCreate = true;
      addVerificationOnCreate = true;
    }
    this.onActivityFeatureToggle({
      allowsUseOnPlanning: event.checked,
      addImplementationOnCreate: addImplementationOnCreate,
      addVerificationOnCreate: addVerificationOnCreate
    });
  }

  onVerificationReqOnPlanningToggle(event: IChangeCheckboxEventArgs): void {
    this.onActivityFeatureToggle({
      verificationReqOnPlanning: event.checked
    });
  }

  onVerificationAvailableToggle(event: IChangeCheckboxEventArgs): void {
    this.onActivityFeatureToggle({
      addVerificationAvailable: event.checked
    });
    this.updateCustomVerifierAvailable(+this.mainForm.value.defaultVerifierAuthor);
  }

  get hiddenFieldIndicator(): DataMap<string> {
    return this._hiddenFieldIndicator;
  }

  private get activityFeatureOptions(): ActivityFeatureOptions {
    return {
      addVerificationAvailable: this.addVerificationAvailable().checked,
      addVerificationOnCreate: this.addVerificationOnCreate().checked,
      addImplementationOnCreate: this.addImplementationOnCreate().checked,
      allowsUseOnPlanning: this.allowsUseOnPlanning().checked,
      verificationReqOnPlanning: this.verificationReqOnPlanning().checked
    };
  }

  protected onActivityFeatureToggle(options: ActivityFeatureOptions): void {
    const args: ActivityFeatureOptions = options ? Object.assign(this.activityFeatureOptions, options) : this.activityFeatureOptions;
    const allowsUseOnPlanning = args.allowsUseOnPlanning || (!args.addVerificationOnCreate && !args.addImplementationOnCreate);
    // Normalización de `addVerificationOnCreate` y `addImplementationOnCreate`
    const addVerificationOnCreate = this.addVerificationOnCreate();
    addVerificationOnCreate.checked = !allowsUseOnPlanning && args.addVerificationOnCreate;
    const addImplementationOnCreate = this.addImplementationOnCreate();
    addImplementationOnCreate.checked = !allowsUseOnPlanning && args.addImplementationOnCreate;
    // Banderas para mostrar secciones de IMPLEMENTACIÓN y/o VERIFICACIÓN
    let isImplementationAvailable = addImplementationOnCreate.checked;
    let isVerificationAvailable = args.addVerificationAvailable || args.verificationReqOnPlanning || addVerificationOnCreate.checked;
    if (!this.verificationOnCreate) {
      isImplementationAvailable = args.addImplementationOnCreate;
      isVerificationAvailable = args.addVerificationOnCreate || args.addVerificationAvailable || args.verificationReqOnPlanning;
    }
    // Cuando se deshabilitan `addImplementationOnCreate` siempre aparece `allowsPreAssignment`
    this.hideAllowsPreAssignment = addImplementationOnCreate.checked;
    // Avisos de secciones ocultas
    const allowsUseOnPlanningValue = this.allowsUseOnPlanning();
    if (allowsUseOnPlanning && !allowsUseOnPlanningValue.checked) {
      this.noticeService.notice(this.translate.instantFrom(ActivityTypesComponent.LANG_CONFIG, 'notice-active-allowsUseOnPlanning'));
    } else if (this._isImplementationAvailable !== isImplementationAvailable) {
      this.noticeService.notice(this.translate.instantFrom(ActivityTypesComponent.LANG_CONFIG, `notice-isImplementationAvailable-${isImplementationAvailable}`));
    } else if (this._isVerificationAvailable !== isVerificationAvailable) {
      this.noticeService.notice(this.translate.instantFrom(ActivityTypesComponent.LANG_CONFIG, `notice-isVerificationAvailable-${isVerificationAvailable}`));
    }
    // Normalización de `allowsUseOnPlanning`
    allowsUseOnPlanningValue.checked = allowsUseOnPlanning;
    // flags
    this._isAllowedUseOnPlanning = allowsUseOnPlanning;
    this._isImplementationAvailable = isImplementationAvailable;
    this._isVerificationAvailable = isVerificationAvailable;
    // fieldNames
    const implementation: string[] = this.featureFieldNames.implementation;
    const verification: string[] = this.featureFieldNames.verification;
    const common: string[] = this.featureFieldNames.common;
    // Campos ocultos de PLANEACIÓN
    if (this.isAllowedUseOnPlanning) {
      this.addHiddenFields(['addImplementationOnCreate', 'addVerificationOnCreate']);
      this.removeHiddenFields(['verificationReqOnPlanning']);
      // Los campos de creación de IMPLEMENTACIÓN y VERIFICACIÓN se apagan siempre que SE PUEDA planear
      this.featureFieldNames.turnedOff = ['addImplementationOnCreate', 'addVerificationOnCreate'];
    } else {
      this.removeHiddenFields(['addImplementationOnCreate', 'addVerificationOnCreate']);
      this.addHiddenFields(['verificationReqOnPlanning']);
      // El campo de "verificación al planear" se apaga siempre que NO se pueda planear
      this.featureFieldNames.turnedOff = ['verificationReqOnPlanning'];
    }
    // Campos ocultos de IMPLEMENTACIÓN y VERIFICACIÓN
    switch (`${this.isImplementationAvailable}, ${this.isVerificationAvailable}`) {
      case 'true, true':
        // Con ambas opciones habilitadas se muestran todos los campos
        this.removeHiddenFields(implementation.concat(verification).concat(common));
        this.featureFieldNames.nullify = []; // <-- Sin campos NULOS cuando la verificación está ON
        this.featureFieldNames.defaultValues = null; // <-- Sin valores por defecto cuando la implementación esta ON
        break;
      case 'true, false':
        // Solo deshabilitada la verificación se muestran solo los campos de la implementación
        this.addHiddenFields(verification.concat(common));
        this.removeHiddenFields(implementation);
        this.featureFieldNames.nullify = verification.concat(common);
        this.featureFieldNames.defaultValues = null; // <-- Sin valores por defecto cuando la implementación esta ON
        break;
      case 'false, true':
        // Solo deshabilitada la implementación se muestran solo los campos de la verificación que no chocan con la implementación
        this.addHiddenFields(implementation.concat(common));
        this.removeHiddenFields(verification);
        this.featureFieldNames.nullify = []; // <-- Sin campos NULOS cuando la verificación está ON
        this.featureFieldNames.defaultValues = {
          // implementation fields
          showImplementerUser: 1,
          showImplementationPeriodicity: 0,
          showStartDate: 1,
          showImplementation: 1,
          // verification fields
          showDaysToVerify: 0,
          showVerificactionPeriodicity: 0,
          verificationType: 1, // 1= SPECIFIC_DATE
          showVerifierUser: 1,
          showVerification: 1
        };
        this.mainForm.patchValue({
          // implementation fields
          showImplementerUser: this.featureFieldNames.defaultValues.showImplementerUser,
          showImplementationPeriodicity: this.featureFieldNames.defaultValues.showImplementationPeriodicity,
          showStartDate: this.featureFieldNames.defaultValues.showStartDate,
          showImplementation: this.featureFieldNames.defaultValues.showImplementation,
          // verification fields
          showDaysToVerify: this.featureFieldNames.defaultValues.showDaysToVerify,
          showVerificactionPeriodicity: this.featureFieldNames.defaultValues.showVerificactionPeriodicity,
          verificationType: this.featureFieldNames.defaultValues.verificationType,
          showVerifierUser: this.featureFieldNames.defaultValues.showVerifierUser,
          showVerification: this.featureFieldNames.defaultValues.showVerification
        });
        break;
      case 'false, false':
        // Con ambas opciones deshabilitadas se ocultan todos los campos
        this.addHiddenFields(implementation.concat(verification).concat(common));
        this.featureFieldNames.nullify = verification.concat(common);
        this.featureFieldNames.defaultValues = {
          // implementation fields
          showImplementerUser: 1,
          showImplementationPeriodicity: 0,
          showStartDate: 1,
          showImplementation: 1
          // no verification fields
        };
        this.mainForm.patchValue({
          // implementation fields
          showImplementerUser: this.featureFieldNames.defaultValues.showImplementerUser,
          showImplementationPeriodicity: this.featureFieldNames.defaultValues.showImplementationPeriodicity,
          showStartDate: this.featureFieldNames.defaultValues.showStartDate,
          showImplementation: this.featureFieldNames.defaultValues.showImplementation
          // no verification fields
        });
        break;
    }
    this.fixAvailableVisibilityFieldNamesOrder();
    this.cdr.detectChanges();
  }

  private addHiddenFields(fields: string[]): void {
    let idx;
    for (const fieldName of fields) {
      idx = this.hiddenFields.indexOf(fieldName);
      if (idx === -1) {
        // Se agregan campos al listado de ocultos
        this.hiddenFields.push(fieldName);
      }
      if (fieldName.startsWith('show')) {
        idx = this.availableVisibilityFieldNames.indexOf(fieldName);
        if (idx !== -1) {
          // Se eliminan del listado de visibles
          this.availableVisibilityFieldNames.splice(idx, 1);
        }
      }
    }
  }

  private removeHiddenFields(fields: string[]): void {
    let idx;
    for (const fieldName of fields) {
      idx = this.hiddenFields.indexOf(fieldName);
      if (idx !== -1) {
        // Se eliminan campos del listado de ocultos
        this.hiddenFields.splice(idx, 1);
      }
      if (fieldName.startsWith('show')) {
        idx = this.availableVisibilityFieldNames.indexOf(fieldName);
        if (idx === -1) {
          // Se agregan al listado de visibles
          this.availableVisibilityFieldNames.push(fieldName);
        }
      }
    }
  }

  onImplementationOnCreateToggle(event: IChangeCheckboxEventArgs): void {
    let addVerificationOnCreate = this.addVerificationOnCreate().checked;
    if (!event.checked) {
      // la implementación debe estar "ON" para que la verificación pueda habilitarse, en automatico se marca la verificación "OFF"
      addVerificationOnCreate = false;
    }
    this.disableShowImplementation = event.checked;
    this.onActivityFeatureToggle({
      addImplementationOnCreate: event.checked,
      addVerificationOnCreate: addVerificationOnCreate
    });
  }

  onEnablePeriodicityChange(event: IChangeCheckboxEventArgs): void {
    this.isPeriodicityEnabled = event.checked;
    if (!this.isPeriodicityEnabled) {
      this.mainForm.patchValue({
        showStartDate: false,
        showDaysToVerify: false,
        showImplementationPeriodicity: false,
        showVerificactionPeriodicity: false,
        authorEditRecurrence: false,
        authorDeleteRecurrence: false
      });
    } else {
      this.mainForm.patchValue({
        showImplementationPeriodicity: true,
        showVerificactionPeriodicity: true
      });
    }
  }

  restrictAnticipationAttendChange(event: IChangeCheckboxEventArgs): void {
    if (event.checked) {
      this.mainForm.addControl('anticipationAttendDays', new UntypedFormControl(0));
    } else {
      this.mainForm.removeControl('anticipationAttendDays');
    }
  }
  updateFillTypes() {
    const frm = this.mainForm;
    if (!frm) {
      return;
    }
    let fillTypes = '';
    let showFillForm = this.mainForm.value.showFillForm;
    let showFillType = this.mainForm.value.showFillType;
    let countFils = 0;
    if (this.fillTypePercentage().checked) {
      fillTypes += ',1';
      countFils++;
    }
    if (this.fillTypeForm().checked) {
      fillTypes += ',2';
      countFils++;
      this.isFillFormAvailable = true;
    } else {
      this.isFillFormAvailable = false;
      showFillForm = false;
    }
    if (this.fillTypeDone().checked) {
      fillTypes += ',3';
      countFils++;
    }
    if (this.fillTypePlanner().checked) {
      fillTypes += ',5';
      countFils++;
    }
    if (this.fillTypePlannerTask().checked) {
      fillTypes += ',6';
      countFils++;
    }
    if (fillTypes === '') {
      this.isFillFormAvailable = false;
      showFillType = false;
    }
    this.multipleFillTypes = countFils > 1;
    if (!this.multipleFillTypes) {
      showFillType = false;
    }
    frm.patchValue({
      fillTypes: fillTypes.replace(/^,/, '') || null,
      showFillForm: showFillForm,
      showFillType: showFillType
    });
  }

  protected focusSystemLink(attr: string, idx: number): void {
    const elem = document.querySelector(`[name=system-link-${attr}-${idx}]`) as HTMLInputElement;
    if (!elem) {
      return;
    }
    elem.focus();
  }

  protected isSystemLinksValid(): boolean {
    if (this.systemLinks.length === 0) {
      return true;
    }
    let link;
    for (let i = 0; i < this.systemLinks.length; i++) {
      link = this.systemLinks[i];
      if (!link.url) {
        this.focusSystemLink('url', i);
        return false;
      }
      if (!link.regexp) {
        this.focusSystemLink('regexp', i);
        return false;
      }
      if (!link.label) {
        this.focusSystemLink('label', i);
        return false;
      }
    }
    return true;
  }

  protected getEntityData(): ActivityTypeEntity {
    const data = this.mainForm.value;
    const linked = this.getLinkedData();
    const showDynamicsFields = this.getShowDynamicFields();
    const entity: ActivityTypeEntity = {
      addDeliverySetUp: data.addDeliverySetUp || false,
      addSubtaskByDepartment: data.addSubtaskByDepartment,
      addImplementationOnCreate: data.addImplementationOnCreate,
      addVerificationOnCreate: data.addVerificationOnCreate,
      allowsPreAssignment: data.allowsPreAssignment,
      verificationReqOnPlanning: data.verificationReqOnPlanning,
      addVerificationAvailable: data.addVerificationAvailable,
      allowsUseOnPlanning: data.allowsUseOnPlanning,
      anticipationAttendDays: data.anticipationAttendDays || 0,
      authorDeleteRecurrence: data.authorDeleteRecurrence ? 1 : 0,
      authorEditApply: data.authorEditApply ? 1 : 0,
      authorEditDynamicFields: data.authorEditDynamicFields ? 1 : 0,
      authorEditImplementation: data.authorEditImplementation ? 1 : 0,
      authorEditImplementer: data.authorEditImplementer ? 1 : 0,
      authorEditBusinessUnitDepartment: data.authorEditBusinessUnitDepartment ? 1 : 0,
      followUpImplementationDelay: data.followUpImplementationDelay,
      authorEditRecurrence: data.authorEditRecurrence ? 1 : 0,
      authorEditVerification: data.authorEditVerification ? 1 : 0,
      authorEditVerifier: data.authorEditVerifier ? 1 : 0,
      code: data.code,
      customImplementationDate: data.customImplementationDate,
      defaultActivityObjectiveId: data.defaultActivityObjectiveId || null,
      dynamicFieldViewName: data.dynamicFieldViewName,
      defaultActivitySourceId: data.defaultActivitySourceId || null,
      defaultDaysToVerify: data.defaultDaysToVerify || 0,
      defaultImplementerAuthor: data.defaultImplementerAuthor || 0,
      defaultIsPlanned: data.defaultIsPlanned,
      defaultPlannedHours: data.defaultPlannedHours,
      defaultPriorityId: data.defaultPriorityId || null,
      defaultVerifierAuthor: data.defaultVerifierAuthor || 0,
      description: data.description,
      enablePeriodicity: data.enablePeriodicity ? 1 : 0,
      fillTypes: data.fillTypes || 1,
      implementerEditApply: data.implementerEditApply ? 1 : 0,
      implementerEditDynamicFields: data.implementerEditDynamicFields ? 1 : 0,
      implementerEditBusinessUnitDeparment: data.implementerEditBusinessUnitDeparment ? 1 : 0,
      preassignedUserEditClientPlannerTask: data.preassignedUserEditClientPlannerTask ? 1 : 0,
      linked: linked,
      maxOpenTime: data.maxOpenTime,
      maxOpenTimeUnit: data.maxOpenTimeUnit,
      module: data.module,
      notifyImplementTime: data.notifyImplementTime,
      notifyImplementTimeUnit: data.notifyImplementTimeUnit,
      notifyVerifyTime: data.notifyVerifyTime,
      notifyVerifyTimeUnit: data.notifyVerifyTimeUnit,
      projectsAvailable: data.projectsAvailable,
      restrictAnticipationAttend: data.restrictAnticipationAttend ? 1 : 0,
      scopeVerifier: data.scopeVerifier || this.scopeVerifierDefaultValue,
      scopeImplementer: data.scopeImplementer || this.scopeImplementerDefaultValue,
      showBusinessUnitDepartment: data.showBusinessUnitDepartment ? 1 : 0,
      defaultCustomVerifier: data.defaultCustomVerifier?.value,
      showCode: data.showCode ? 1 : 0,
      showDaysToVerify: data.showDaysToVerify ? 1 : 0,
      showDescription: data.showDescription ? 1 : 0,
      showDynamicFields: showDynamicsFields,
      showFillForm: data.showFillForm ? 1 : 0,
      showFillType: data.showFillType ? 1 : 0,
      showGroupName: data.showGroupName ? 1 : 0,
      showImplementation: data.showImplementation ? 1 : 0,
      showImplementationPeriodicity: data.showImplementationPeriodicity ? 1 : 0,
      showImplementerUser: data.showImplementerUser ? 1 : 0,
      showIsPlanned: data.showIsPlanned,
      showObjective: data.showObjective ? 1 : 0,
      showPlannedHours: data.showPlannedHours,
      showPriority: data.showPriority ? 1 : 0,
      showSource: data.showSource ? 1 : 0,
      showStartDate: data.showStartDate ? 1 : 0,
      showType: data.showType ? 1 : 0,
      showVerificactionPeriodicity: data.showVerificactionPeriodicity ? 1 : 0,
      showVerification: data.showVerification ? 1 : 0,
      showVerifierUser: data.showVerifierUser ? 1 : 0,
      systemLinks: this.systemLinks || [],
      verificationType: data.verificationType,
      verifierEditApply: data.verifierEditApply ? 1 : 0,
      verifierEditDynamicFields: data.verifierEditDynamicFields ? 1 : 0,
      verifierEditBusinessUnitDepartment: data.verifierEditBusinessUnitDepartment ? 1 : 0,
      workingHoursAvailable: data.workingHoursAvailable,
      registryCode: data.registryCode,
      defaultLoggedUserDepartment: data.defaultLoggedUserDepartment,
      mustUpdateImplementationAtReturn: data.mustUpdateImplementationAtReturn,
      defaultResolutionId: data.defaultResolutionId || null,
      showActivityOrder: data.showActivityOrder,
      isCopy: false
    };
    if (!this.isVerificationAvailable || !this.isImplementationAvailable) {
      // Cuando la verificación está apagada; se guardan nulos en todos sus campos asociados
      for (const fieldName of this.featureFieldNames.nullify) {
        entity[fieldName] = null;
      }
    }
    // Los campos listados se enviaran apagados
    for (const fieldName of this.featureFieldNames.turnedOff) {
      entity[fieldName] = false;
    }
    if (typeof this.featureFieldNames.defaultValues === 'object') {
      // Se sobre escriben los valores de "fecha de implementación" y "responsable"
      Object.assign(entity, this.featureFieldNames.defaultValues);
    }
    if (this.registryCode !== '') {
      entity.registryCode = this.registryCode;
    }
    return entity;
  }

  private parseConditionalFormEntities(): ConditionalEntity[] {
    const assigned: ConditionalEntity[] = this.conditionalFields.flatMap((field) =>
      field.assignedFields.map((fieldValue) => ({
        id: fieldValue.id,
        type: ConditionalFieldType.ASSIGNED_FIELD,
        fieldOrder: field.fieldOrder,
        fieldName: fieldValue.fieldName,
        value: fieldValue.value
      }))
    );
    const conditional: ConditionalEntity[] = this.conditionalFields.flatMap((field) =>
      field.matchFields.map((fieldValue) => ({
        id: fieldValue.id,
        type: ConditionalFieldType.MATCH_FIELD,
        fieldOrder: field.fieldOrder,
        fieldName: fieldValue.fieldName,
        value: fieldValue.value
      }))
    );
    return assigned.concat(conditional);
  }

  getCatalog(fieldName: string): any[] {
    const catalogName = this.getCatalogName(fieldName);
    if (catalogName === null || typeof catalogName === 'undefined') {
      return this.catalogs[fieldName] || [];
    }
    return this.catalogs[catalogName] || [];
  }

  getUserCatalog(fieldName: string): TextHasUserValue[] {
    const catalogName = this.getCatalogName(fieldName);
    if (catalogName === null || typeof catalogName === 'undefined') {
      return [];
    }
    return this.catalogs[catalogName] || [];
  }

  getResolutionValues(): IActivityResolutionItem[] {
    const catalogName = this.getCatalogName(ActivityTypesComponent.RESOLUTION);
    if (catalogName === null || typeof catalogName === 'undefined') {
      return [];
    }
    return this.catalogs[catalogName] || [];
  }

  getDepartmentsValues(): IActivityDepartmentItem[] {
    const catalogName = this.getCatalogName(ActivityTypesComponent.DEPARTMENT);
    if (catalogName === null || typeof catalogName === 'undefined') {
      return [];
    }
    return this.catalogs[catalogName] || [];
  }

  getCategoriesValues(): IActivityDepartmentItem[] {
    const catalogName = this.getCatalogName(ActivityTypesComponent.CATEGORY);
    if (catalogName === null || typeof catalogName === 'undefined') {
      return [];
    }
    return this.catalogs[catalogName] || [];
  }

  getIds(data: any[]): Entity[] {
    if (!data) {
      return [];
    }
    return data.map((itemId) => {
      return { id: itemId };
    });
  }

  getDynamicFieldsIdsOrder(): DynamicFieldOrder[] {
    return this.dynamicFieldOrder.map((field) => {
      return {
        id: field.id,
        order: field.order
      };
    });
  }

  protected getLinkedData(): ActivityTypeLinked {
    const data = this.mainForm.value;
    const dynamicFields = this.getDynamicFieldsIdsOrder();
    const resolutionValues = this.getIds(data.resolutionValues);
    const categoryValues = this.getIds(data.categoryValues);
    const groupsValues = this.getIds(data.groupsValues);
    const departmentsValues = this.getIds(data.departmentsValues);
    const conditionalFields: ConditionalEntity[] = this.parseConditionalFormEntities();
    const preAssignGroupsValues = this.getIds(data.preAssignGroupsValues);
    return {
      dynamicFields: dynamicFields,
      resolutionValues: resolutionValues,
      categoryValues: categoryValues,
      groupsValues: groupsValues,
      departmentsValues: departmentsValues,
      conditionalFields: conditionalFields,
      preAssignGroupsValues: preAssignGroupsValues
    };
  }

  protected getShowDynamicFields(): string {
    const data = this.mainForm.value;
    const dynamicFields = this.getIds(data.dynamicFields);
    return dynamicFields
      .filter((value) => data[this.getDynamicFieldName(value.id)])
      .map((value) => value.id)
      .join(',');
  }

  toggleSystemLinkStatus(systemLink: SystemLink): void {
    this.service.get({ cancelableReq: this.$destroy, url: `${this.controller}/toggle-system-link/${systemLink.id}`, handleFailure: false }).subscribe(
      (result) => {
        if (typeof result.newStatus !== 'undefined') {
          systemLink.status = result.newStatus;
        }
        this.noticeService.notice(this.translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, 'accept-dialog-message'));
        this.cdr.detectChanges();
      },
      (error) => {
        console.error(error);
        ErrorHandling.notifyError(error, this.navLang);
      }
    );
  }

  removeSystemLink(idx: number): void {
    this.systemLinks.splice(idx, 1);
    this.cdr.detectChanges();
  }

  addSystemLink(): void {
    if (this.systemLinks.length === 1) {
      // ToDo: Poder agregar multiples ligas
      this.noticeService.notice('Actualmente solo se puede agregar 1 liga');
      return;
    }
    const item: SystemLink = {
      id: -1,
      url: null,
      regexp: null,
      label: null
    };
    this.systemLinks.push(item);
    this.cdr.detectChanges();
  }

  addConditionalField(): void {
    if (this.busy) {
      return;
    }
    this.busy = true;
    this.conditionalFields.push({
      matchFields: [],
      assignedFields: [],
      fieldOrder: this.conditionalFields.length
    });
    this.cdr.detectChanges();
    this.busy = false;
  }

  removeConditionalField(index: number) {
    this.conditionalFields.splice(index, 1);
    this.cdr.detectChanges();
  }

  getDynamicFieldName(id: number) {
    return `dynamicField${id}`;
  }

  onChangeResolutions(values: (string | number)[]): void {
    // Se habilita el campo resolución para capturar valor por defecto
    if (values && values.length > 0) {
      this.resolution.disabled = false;
      // Se extrae la información filtrada del catálogo de resoluciones de las seleccionadas
      const valuesCatalog: TextHasValue[] = this.getResolutionValues()
        .filter((c) => values.includes(c.value) && !c.closeActivityEnabled)
        .map((a) => {
          return { text: a.text, value: +a.value };
        });
      const catalogName = ActivityTypesComponent.RESOLUTION_FIELD_DEFAULT;
      if (this.catalogs[catalogName]) {
        // Agregamos el valor de noResolutionLabel
        valuesCatalog.push(this.catalogs[catalogName].find((c) => +c.value === 0));
        this.catalogs[catalogName] = valuesCatalog;
      } else {
        this.catalogs[catalogName] = valuesCatalog;
      }
    } else {
      this.resolution.disabled = true;
      this.mainForm.patchValue({ defaultResolutionId: null });
    }
  }

  onChangeDynamicFields(values: (string | number)[]) {
    if (!this.dynamicFields) {
      return;
    }
    if (this.selectedDynamicFields) {
      for (const field of this.selectedDynamicFields) {
        this.mainForm.removeControl(this.getDynamicFieldName(field.id));
      }
    }
    if (values) {
      for (const value of values) {
        const fieldName = this.getDynamicFieldName(+value);
        this.mainForm.addControl(fieldName, new UntypedFormControl());
        const data = {};
        data[fieldName] = this.showDynamicFieldsConfig[fieldName];
        this.mainForm.patchValue(data);
      }
      this.selectedDynamicFields = this.dynamicFields.filter((field) => values.indexOf(field.id) !== -1);
      //Este bloque de código sirve para ordenar los campos en el drag and drop zone como vienen desde el backend
      //a traves del parametro values:number[]
      if (this.selectedDynamicFields.length > 0) {
        const tempSelectedDynamicFields = this.selectedDynamicFields;
        this.selectedDynamicFields = [];
        for (const df of values) {
          this.selectedDynamicFields.push(tempSelectedDynamicFields.find((f) => f.id === df));
        }
      }
    } else {
      this.selectedDynamicFields = [];
    }
    for (const dynamicField of this.selectedDynamicFields) {
      const fieldName = this.getDynamicFieldName(dynamicField.id);
      const chipName = `chip${dynamicField.id}`;
      if (typeof this._hiddenFieldIndicator[chipName] === 'undefined') {
        if (this.mainForm.controls[fieldName].value) {
          this._hiddenFieldIndicator[chipName] = 'visibility';
        } else {
          this._hiddenFieldIndicator[chipName] = 'visibility_off';
        }
      }
      if (this.dynamicFieldOrder.findIndex((p) => p.id === dynamicField.id) === -1) {
        this.dynamicFieldOrder.push({
          description: dynamicField.label,
          id: dynamicField.id,
          order: dynamicField?.order ? dynamicField?.order : this.dynamicFieldOrder.length + 1
        });
      }
    }
    this.dynamicFieldOrder.sort((a, b) => a.order - b.order);
    //Esto sirve para remover el control dinamico cuando uno se elimina
    if (this.dynamicFieldOrder.length > this.selectedDynamicFields.length) {
      const dynamicFieldOrderIds = this.dynamicFieldOrder.map((m) => m.id);
      const selectedDynamicFieldsIds = this.selectedDynamicFields.map((m) => m.id);
      for (const id1 of dynamicFieldOrderIds) {
        if (!selectedDynamicFieldsIds.includes(id1)) {
          this.dynamicFieldOrder.splice(
            this.dynamicFieldOrder.findIndex((i) => i.id === id1),
            1
          );
        }
      }
      this.reOrderAfterRemove();
    }
    this.defineConditionalFieldsIndex();
  }

  onChangeShowDynamicField(id: number, event: IChangeCheckboxEventArgs) {
    const field = this.getDynamicFieldName(id);
    const reached = this.isMinimumHiddenFieldsReached(field, event);
    if (reached) {
      this.enableSwitch(field);
      return;
    }
    this.showDynamicFieldsConfig[field] = event.checked;
    const chipName = `chip${id}`;
    if (event.checked) {
      this._hiddenFieldIndicator[chipName] = 'visibility';
    } else {
      this._hiddenFieldIndicator[chipName] = 'visibility_off';
    }
  }

  public validNumber(formName: any, $event) {
    if (!$event.target.validity.valid) {
      this.noticeService.notice(this.i18n.invalidNumber as string);
      this.mainForm.controls[formName].setValue(null);
    }
    return $event.target.validity.valid;
  }

  isHidden(fieldName: string): boolean {
    return this.hiddenFields.indexOf(fieldName) !== -1;
  }

  showGenerateCode() {
    return false;
  }

  get controller(): string {
    return 'activity-types';
  }

  public getCustomHiddenConfigurableTag(control: any): string {
    if (this.moduleLabelOverride == null) {
      return `hiddenConfigurable.${control}`;
    }
    switch (this.moduleLabelOverride) {
      case Module.PLANNER:
        if (this.availableCustomsVisibilityFieldNames[control]) {
          return `hiddenCustomTranslateConfigurable.${this.availableCustomsVisibilityFieldNames[control]}`;
        }
        return `hiddenConfigurable.${control}`;
      case Module.ACTIVITY:
        return `hiddenConfigurable.${control}`;
      default:
        console.log('Missing customs independients translations, check "hiddenCustomTranslateConfigurable"');
        return `hiddenConfigurable.${control}`;
    }
  }

  public onNomeclatureChange(items: ListItem[]): void {
    const nomeclatureKeys: DataMap = {};
    let index = 0;
    for (const item of items) {
      if (item.isTextField) {
        if (item.valueTextField) {
          nomeclatureKeys[index] = item.valueTextField;
          index++;
        }
      } else {
        nomeclatureKeys[index] = `\${${item.id}}`;
        index++;
      }
    }
    this.registryCode = JSON.stringify(nomeclatureKeys);
  }

  public setNomeclatureValues(value: string): void {
    const registryCodes = JSON.parse(value || null);
    if (registryCodes !== null) {
      this.registryCode = value;
      this.nomeclatureValues.push(this.nomeclatureListChip[0]);
      this.nomeclatureListChip = [];
    }
    for (const idx in registryCodes) {
      if (!registryCodes.hasOwnProperty(idx)) {
        continue;
      }
      const key = registryCodes[idx].replace(/\$\{|\}/g, '');
      const item = this.nomeclatureValues.find((i) => i.id === key);
      if (item && this.nomeclatureListChip.findIndex((i) => i.id === item.id) === -1) {
        item.state = ListItemState.secondary;
        this.nomeclatureListChip.push(item);
        this.nomeclatureValues = this.nomeclatureValues.filter((i) => i.id !== key);
      } else if (this.nomeclatureListChip.findIndex((i) => i.id === key) === -1) {
        const itemText = this.nomeclatureValues.find((i) => i.id === 'textField');
        if (!itemText) {
          continue;
        }
        itemText.valueTextField = key;
        itemText.state = ListItemState.main;
        if (itemText.infinite) {
          const clone = cloneObject(itemText);
          clone.infinite = true;
          clone.state = ListItemState.secondary;
          clone.id = randomUUID();
          this.nomeclatureListChip.push(clone);
        } else {
          this.nomeclatureListChip.push(itemText);
        }
        if (itemText.infinite !== true) {
          this.nomeclatureValues = this.nomeclatureValues.filter((i) => i.id !== 'textField');
        }
      }
    }
  }

  public ghostCreateHandler(_event) {
    _event.style.visibility = 'hidden';
  }

  public dragEndHandler(_event: HTMLElement) {
    _event.style.visibility = 'visible';
  }

  public dragStartHandler(idField: number) {
    this.dragFieldDynamicId = idField;
  }

  public onEnterHandler(ev): void {
    if (this.dragFieldDynamicId === +ev.owner.element.nativeElement.id) {
      return;
    }
    const dragIndex = this.dynamicFieldOrder.findIndex((field) => field.id === this.dragFieldDynamicId);
    const dropIndex = this.dynamicFieldOrder.findIndex((field) => field.id === +ev.owner.element.nativeElement.id);
    this.swapFields(dragIndex, dropIndex);
  }

  private swapFields(dragIndex: number, dropIndex: number) {
    const flagDragField = this.dynamicFieldOrder[dragIndex];
    const flagDropField = this.dynamicFieldOrder[dropIndex];
    const flagDropFieldOrder = flagDropField.order;
    flagDropField.order = flagDragField.order;
    this.dynamicFieldOrder.splice(dragIndex, 1, flagDropField);
    flagDragField.order = flagDropFieldOrder;
    this.dynamicFieldOrder.splice(dropIndex, 1, flagDragField);
    this.cdr.detectChanges();
  }

  private reOrderAfterRemove(): void {
    for (const field of this.dynamicFieldOrder) {
      const index = this.dynamicFieldOrder.indexOf(field);
      field.order = index;
    }
    this.cdr.detectChanges();
  }

  public onDefaultActivitiesChange(name: string, event) {
    switch (name) {
      case 'defaultActivitySourceId':
        if (event === null) {
          this.disableShowSource = true;
        } else {
          this.disableShowSource = false;
        }
        break;
      case 'defaultPriorityId':
        if (event === null) {
          this.disableShowPriority = true;
        } else {
          this.disableShowPriority = false;
        }
        break;
      case 'defaultActivityObjectiveId':
        if (event === null) {
          this.disableActivityObjective = true;
        } else {
          this.disableActivityObjective = false;
        }
        break;
      case 'defaultResolutionId':
        if (event === null) {
          this.disableShowResolution = true;
        } else {
          this.disableShowResolution = false;
        }
        break;
    }
  }

  public onOpenDialogSaveAsCopy(): void {
    const saveAsCopy = this.saveAsCopy();
    if (saveAsCopy === null) {
      return;
    }
    saveAsCopy.open();
  }
  public onSaveAsCopy(): void {
    if (!FormUtil.isValid(this.saveAsCopyForm).status) {
      this.saveAsCopyForm.markAllAsTouched();
      this.saveAsCopyForm.updateValueAndValidity();
      return;
    }
    this.copyDescriptionName = this.saveAsCopyForm.get('copyName').value;
    this.saveAsCopyForm.reset();
    this.saveAsCopy().close();
    this.save(true);
  }

  public onBeforeClose(_event): void {
    this.conditionalFields = this.conditionalFields.filter((f: ConditionalFieldData) => f.matchFields.length > 0);
  }

  public getCustomSuccessMessageTag(): string {
    if (this.moduleLabelOverride == null) {
      return 'dialog-save-message';
    }
    switch (this.moduleLabelOverride) {
      case Module.PLANNER:
        return 'dialog-save-planner-message';
      case Module.ACTIVITY:
        return 'dialog-save-message';
      default:
        console.log('Missing customs independients translations, check if the module is supported.');
        return 'dialog-save-message';
    }
  }

  initializeRedirectOptions(): void {
    this.translate
      .getFrom(ACTIVITY_TYPES_COMPONENT_LANG, 'buttonSaveItems')
      .pipe(takeUntil(this.$destroy))
      .subscribe((tags) => {
        for (const item of this.redirectOptions) {
          item.text = tags[item.text] || item.text;
        }
      });
  }
}
