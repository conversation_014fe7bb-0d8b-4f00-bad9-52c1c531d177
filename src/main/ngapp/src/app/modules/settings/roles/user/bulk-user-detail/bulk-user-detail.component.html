<igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
<div class="grid-container grid-floating-active full-width-grid-container">
  <div class="grid-x">
    <div class="cell">
      <div class="container-form grid-floating-action-buttons">
        <div class="header grid-container">
          <div class="grid-x">
            <h3 class="cell igx-card-header__title">{{ 'bulk-user-list-title' | translate: this }}</h3>
          </div>
        </div>
        <div class="grid-container">
          <div class="grid-x grid-padding-x">
            <app-field-display class="cell small-12 medium-3" icon="description" [label]="'field.code' | translate: this" [parameterizedValue]="data?.code">
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="today"
              [label]="'field.startDate' | translate: this"
              [date]="true"
              [dateFormat]="'dd/MM/yyyy HH:mm:ss'"
              [parameterizedValue]="data?.startDate"
            >
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="today"
              [label]="'field.endDate' | translate: this"
              [date]="true"
              [dateFormat]="'dd/MM/yyyy HH:mm:ss'"
              [parameterizedValue]="data?.endDate"
            >
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="schedule"
              [label]="'field.elapsedMinutes' | translate: this"
              [parameterizedValue]="data?.elapsedMinutes"
            >
            </app-field-display>
            <app-field-display class="cell small-12 medium-3" icon="functions" [label]="'field.totalRecords' | translate: this" [parameterizedValue]="data?.totalRecords">
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="functions"
              [label]="'field.activeUsersCount' | translate: this"
              [parameterizedValue]="data?.activeUsersCount"
            >
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="functions"
              [label]="'field.inactiveUsersCount' | translate: this"
              [parameterizedValue]="data?.inactiveUsersCount"
            >
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="functions"
              [label]="'field.requireLicenseCount' | translate: this"
              [parameterizedValue]="data?.requireLicenseCount"
            >
            </app-field-display>
            <app-field-display
              class="cell small-12 medium-3"
              icon="description"
              [isBoolean]="true"
              [label]="'field.hasSourceFile' | translate: this"
              [parameterizedValue]="data?.hasSourceFile"
            >
            </app-field-display>
            <igx-divider></igx-divider>
            <div class="grid-x">
            @if (id) {
              <div class="cell small-12 medium-4 user-detail-grid mt-1">
                <div class="logs-button">
                  <button [igxButton]="'outlined'" igxRipple (click)="onProfileLogClick()">{{ 'log' | translate: this }}</button>
                </div>
                <app-grid
                  #gridProfile
                  [id]="'bulk-user-rows-profile'"
                  name="bulk-user-rows-profile"
                  [url]="'bulk-user/record-profile/' + id"
                  [perPage]="perPage"
                  [autoMinimumWidth]="false"
                  [columns]="columns"
                  [primaryKey]="'id'"
                  [titleLabel]="'record-profile-title' | translate: this"
                  [rowClasses]="rowClasses"
                  [height]="containerSize"
                  [autoMinimumHeight]="false"
                  [allowMaximize]="false"
                  [allowMoving]="false"
                  [allowGrouping]="false"
                  [columnPinning]="false"
                >
                </app-grid>
              </div>
            }
            @if (id) {
              <div class="cell small-12 medium-4 user-detail-grid mt-1">
                <div class="logs-button">
                  <button [igxButton]="'outlined'" igxRipple (click)="onJobLogClick()">{{ 'log' | translate: this }}</button>
                </div>
                <app-grid
                  #gridJob
                  [id]="'bulk-user-rows-job'"
                  name="bulk-user-rows-job"
                  [url]="'bulk-user/record-job/' + id"
                  [perPage]="perPage"
                  [autoMinimumWidth]="false"
                  [columns]="columns"
                  [primaryKey]="'id'"
                  [titleLabel]="'record-job-title' | translate: this"
                  [rowClasses]="rowClasses"
                  [height]="containerSize"
                  [autoMinimumHeight]="false"
                  [allowMaximize]="false"
                  [allowMoving]="false"
                  [allowGrouping]="false"
                  [columnPinning]="false"
                >
                </app-grid>
              </div>
            }
            @if (id) {
              <div class="cell small-12 medium-4 user-detail-grid mt-1">
                <div class="logs-button">
                  <button [igxButton]="'outlined'" igxRipple (click)="onUserLogClick()">{{ 'log' | translate: this }}</button>
                </div>
                <app-grid
                  #gridUser
                  [id]="'bulk-user-rows-user'"
                  name="bulk-user-rows-user'"
                  [url]="'bulk-user/record-user/' + id"
                  [perPage]="perPage"
                  [autoMinimumWidth]="false"
                  [columns]="columns"
                  [primaryKey]="'id'"
                  [titleLabel]="'record-user-title' | translate: this"
                  [rowClasses]="rowClasses"
                  [height]="containerSize"
                  [autoMinimumHeight]="false"
                  [allowMaximize]="false"
                  [allowMoving]="false"
                  [allowGrouping]="false"
                  [columnPinning]="false"
                >
                </app-grid>
              </div>
            }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@if (showBulkUpload) {
  <app-bulk-user-upload #bulkUpload [parent]="this" (closed)="showBulkUpload = false" (fileUploaded)="onFileUploaded($event)"></app-bulk-user-upload>
}
<div class="download-anchor" [hidden]="true">
  <a #downloadAnchor href="javascript: void(0);" target="_blank"></a>
</div>
