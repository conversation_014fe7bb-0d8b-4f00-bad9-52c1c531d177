import * as DomUtil from '@/core/utils/dom-util';
import { NgClass } from '@angular/common';
import { type ComponentRef, computed, signal } from '@angular/core';
import { Component, ElementRef, HostBinding, Injector, Input, ViewContainerRef, inject, output, viewChild } from '@angular/core';
import {
  IgxButtonModule,
  IgxCheckboxModule,
  type IgxDialogComponent,
  IgxDialogModule,
  IgxIconModule,
  IgxInputGroupModule,
  IgxRippleModule
} from '@infragistics/igniteui-angular';
import { Session } from 'src/app/core/local-storage/session';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { OptionsInput } from 'src/app/core/utils/interfaces';
import type { TextLongValue } from 'src/app/core/utils/text-has-value';
import type { IDialogElement } from '../utils/element.interfaces';

import { FormsModule } from '@angular/forms';
import { HammerModule } from '@angular/platform-browser';

import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import type { CustomDialogBase, DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import type { DialogConfiguration } from 'src/app/core/services/dialog.service.interfaces';

import type { InputValidType } from '@/modules/elements/utils/element.types';
import { AutoresizeDirective } from 'src/app/core/directives/autoresize';
import { DropdownSearchComponent } from 'src/app/core/dropdown-search/dropdown-search.component';

let NEXT_NAME = 0;

@Component({
  selector: 'app-dialog-element',
  imports: [
    NgClass,
    FormsModule,
    IgxInputGroupModule,
    IgxDialogModule,
    IgxIconModule,
    IgxRippleModule,
    IgxButtonModule,
    IgxCheckboxModule,
    AutoresizeDirective,
    DropdownSearchComponent,
    FormsModule,
    HammerModule
  ],
  styleUrls: ['./bnext-dialog-element.component.scss'],
  templateUrl: './bnext-dialog-element.component.html'
})
export class BnextDialogElementComponent extends BnextCoreComponent implements IDialogElement {
  private injector = inject(Injector);

  private notice = inject(NoticeService);

  readonly customTemplate = viewChild('customTemplate', { read: ViewContainerRef });
  readonly dialog = viewChild<IgxDialogComponent>('dialog');
  readonly textInput = viewChild('textInput', { read: ElementRef });

  private customComponent: ComponentRef<CustomDialogBase>;
  private _dontShowThisAgain = false;
  private _acceptAllSections = false;
  private _isOpening = false;
  private _isClosed = false;
  private _configuration: DialogConfiguration = null;
  private dialogStatus = signal<'maximized' | 'dialog'>('dialog');
  isMaximized = computed(() => this.dialogStatus() === 'maximized');
  isDialog = computed(() => this.dialogStatus() === 'dialog');

  animateWhenOpen = false;
  _closeOnOutsideSelect = false;
  _closeOnEscape = false;

  @HostBinding('attr.id')
  @Input()
  public id = 'bnextDialog';

  @HostBinding('attr.name')
  @Input()
  public name = `bnextDialog-${NEXT_NAME++}`;

  @Input()
  customContent = false;

  @Input()
  set closeOnOutsideSelect(_closeOnOutsideSelect: boolean) {
    const dialog = this.dialog();
    if (dialog) {
      dialog.closeOnOutsideSelect = _closeOnOutsideSelect;
    }
    this._closeOnOutsideSelect = _closeOnOutsideSelect;
  }

  @Input()
  set closeOnEscape(_closeOnEscape: boolean) {
    this._closeOnEscape = _closeOnEscape;
  }
  get closeOnEscape(): boolean {
    return this._closeOnEscape;
  }
  get closeOnOutsideSelect(): boolean {
    return this._closeOnOutsideSelect;
  }
  get optionsAvailable(): boolean {
    return this.closeOnOutsideSelect || this.maximizeAvailable;
  }
  get dontShowThisAgain() {
    return this._dontShowThisAgain;
  }
  get acceptAllSections() {
    return this._acceptAllSections;
  }

  @Input()
  set dontShowThisAgain(value: any) {
    this._dontShowThisAgain = value;
  }

  @Input()
  set acceptAllSections(value: any) {
    this._acceptAllSections = value;
  }

  @Input()
  dialogTitle: string;

  _dialogInput: boolean;

  @Input()
  public keepAlive = false;

  @Input()
  set dialogInput(value: boolean) {
    this._dialogInput = value;
  }
  get dialogInput(): boolean {
    return this._dialogInput;
  }

  @Input()
  dialogSelect = false;

  @Input()
  dialogSelectOptions: TextLongValue[] = [];

  @Input()
  scrollableAvailable = true;

  @Input()
  selectValue: number = null;

  @Input()
  selectText: string = null;

  @Input()
  inputValue = '';

  @Input()
  inputMaxLimit = 4000;

  @Input()
  inputMinLimit = 0;

  @Input()
  inputType: InputValidType = 'text';

  @Input()
  inputLimitText = `Máximo ${this.inputMaxLimit} caracteres`;

  @Input()
  isPadding = true;

  @Input()
  public dialogMessage = '';

  @Input()
  public isCentered = false;

  @Input()
  dialogScrollableMessage: string;

  @Input()
  dialogSelectLabel = 'default dialog select label';

  @Input()
  dialogSelectHint = 'missing select value';

  @Input()
  dialogInputMessage = 'default dialog input message';

  @Input()
  public dialogEmptyInputMessage = 'root.common.message.input-empty-text';

  @Input()
  rightDialogButton = 'right button';

  @Input()
  leftDialogButton = 'left button';

  @Input()
  dialogId = null;

  @Input()
  acceptAllBox = null;

  @Input()
  dontShowThisAgainLabel = 'default dontShowThisAgain label';

  @Input()
  acceptAllSectionsLabel = 'default acceptAllSections label';

  @Input()
  executeRightButtonOnEnter = false;

  @Input()
  set isOpen(isOpen: boolean) {
    this.cdr.detectChanges();
    const dialog = this.dialog();
    if (isOpen) {
      const element = this.elem.nativeElement as IDialogElement;
      if (this.scrollableAvailable) {
        if (this.dialogMessage.length > 500) {
          element.dialogScrollableMessage = this.dialogMessage;
          element.dialogMessage = null;
        }
        if (this.maximized) {
          this.dialogStatus.set('maximized');
        }
      } else {
        element.dialogScrollableMessage = null;
      }
      if (this._isOpening) {
        return;
      }
      this._isOpening = true;
      this._isClosed = false;
      this.cdr.detectChanges();
      this.dialog().open();
    } else if (dialog?.isOpen) {
      if (this._isClosed) {
        return;
      }
      this._isOpening = false;
      dialog?.close();
      this._isClosed = true;
      this.cdr.detectChanges();
    }
  }
  get isOpen(): boolean {
    return this.dialog()?.isOpen;
  }

  public readonly rightButton = output<DialogResult>();
  public readonly leftButton = output<DialogResult>();

  public readonly open = output<DialogResult>();
  public readonly closed = output<DialogResult>();
  public readonly opening = output<DialogResult>();

  @Input()
  set reset(_dummy: any) {
    this.cdr.detectChanges();
    const element = this.elem.nativeElement as IDialogElement;
    element.isOpen = false;
  }

  get isClosed(): boolean {
    return this._isClosed;
  }

  @Input()
  maximizeAvailable = false;

  @Input()
  maximized = false;

  public onOpening(): void {
    const result = this.getDialogResult();
    if (this.debug()) {
      console.log('Opening dialog element.', result);
    }
    this._isOpening = true;
    this._isClosed = false;
    this.opening.emit(result);
    this.detectChanges();
  }

  public onClosed(): void {
    const result = this.getDialogResult();
    if (this.debug()) {
      console.log('Closed dialog element.', result);
    }
    if (this.isOpen) {
      console.log('Not closed correctly dialog element.', result);
    }
    this._isOpening = false;
    this._isClosed = true;
    if (this.configuration?.closedAction) {
      this.configuration.closedAction(result);
    }
    this.closed.emit(result);
    this.detectChanges();
  }

  public onOpened(): void {
    const result = this.getDialogResult();
    if (this.debug()) {
      console.log('Opened dialog element.', result);
    }
    this._isOpening = false;
    this._isClosed = false;
    this.loader.hide();
    if (this.configuration?.openAction) {
      this.configuration.openAction(result, () => {
        this.animateWhenOpen = false;
      });
    }
    this.open.emit(result);
    this.detectChanges();
  }

  private isValidInput() {
    if (this.dialogInput && !this.inputValue) {
      return false;
    }
    if (this.dialogInput) {
      const value = this.inputValue.replace(/\n|\r|\u200B/g, '').trim();
      if (this.inputMinLimit > 0 && value.length < this.inputMinLimit) {
        return false;
      }
    }
    if (this.dialogSelect && !this.selectValue) {
      return false;
    }
    return true;
  }

  @Input()
  set configuration(config: DialogConfiguration) {
    this.animateWhenOpen = !!config.animateOnResolve;
    const element = this.elem.nativeElement as IDialogElement;
    element.customContent = config.customContent || false;
    if (typeof config.isPadding === 'undefined' || config.isPadding === null) {
      element.isPadding = true;
    } else {
      element.isPadding = config.isPadding;
    }
    if (typeof config.closeOnOutsideSelect === 'undefined' || config.closeOnOutsideSelect === null) {
      element.closeOnOutsideSelect = false;
    } else {
      element.closeOnOutsideSelect = config.closeOnOutsideSelect;
    }
    this.createCustomInstance(config);
    this.retrieveCustomData();
    this._configuration = config;
    element.isOpen = true;
  }

  get configuration(): DialogConfiguration {
    return this._configuration;
  }

  private createCustomInstance(config: DialogConfiguration) {
    this.detectChanges();
    this.customTemplate().clear();
    if (this.customComponent) {
      this.customComponent.destroy();
    }
    this.customComponent = this.customTemplate().createComponent(config.component, { injector: this.injector });
    this.detectChanges();
  }

  private retrieveCustomData() {
    const customInstance = this.customComponent.instance;
    const element = this.elem.nativeElement as IDialogElement;
    element.dialogTitle = customInstance.title;
    element.dialogMessage = customInstance.message;
    element.rightDialogButton = customInstance.rightButton;
    element.leftDialogButton = customInstance.leftButton;
  }

  onClickMaximize(): void {
    const element = this.elem.nativeElement as IDialogElement;
    element.maximized = true;
    this.dialogStatus.set('maximized');
  }

  onClickMinimize(): void {
    const element = this.elem.nativeElement as IDialogElement;
    element.maximized = false;
    this.dialogStatus.set('dialog');
    const textInput = this.textInput();
    if (textInput?.nativeElement) {
      if (this.inputValue !== null && this.inputValue.trim() !== '') {
        DomUtil.adjustTextArea(textInput.nativeElement, 46, 46);
      } else {
        DomUtil.adjustTextArea(textInput.nativeElement, 300, 46);
      }
    }
  }

  onOpen(): void {
    this.open.emit(this.getDialogResult());
  }

  public detectChanges(): void {
    if (this.customComponent?.changeDetectorRef) {
      this.customComponent.changeDetectorRef.detectChanges();
    } else {
      if (this.customComponent) {
        console.log('Custom component not ready.');
      }
    }
    this.cdr.detectChanges();
  }

  onRightButtonSelect(): void {
    if (this.isUserInput && !this.isValidInput()) {
      this.notice.notice(this.dialogEmptyInputMessage);
      return;
    }
    this.showThisAgain();
    this.saveAcceptAllSections();
    const result = this.getDialogResult();
    if (this.configuration?.rightAction) {
      this.configuration.rightAction(result);
    }
    this.rightButton.emit(result);
  }

  onLeftButtonSelect(): void {
    this.showThisAgain();
    this.saveAcceptAllSections();
    const result = this.getDialogResult();
    if (this.configuration?.leftAction) {
      this.configuration.leftAction(result);
    }
    this.leftButton.emit(result);
  }

  getDialogResult(): DialogResult {
    let custom: CustomDialogBase;
    if (this.customComponent) {
      custom = this.customComponent.instance;
    } else {
      custom = null;
    }
    return {
      dialog: this,
      custom: custom,
      inputValue: this.inputValue || null,
      selectValue: this.selectValue || null,
      selectText: this.selectText || null
    };
  }

  inputKeyDown(event: { altKey: boolean; key: string }): void {
    if (!this.executeRightButtonOnEnter) {
      return;
    }
    const isEnter = event.key === 'Enter' || event.key === 'NumpadEnter';
    if (event.altKey && isEnter && this.inputType === 'text') {
      const element = this.elem.nativeElement as IDialogElement;
      element.inputValue += '\r\n';
    } else if (isEnter) {
      this.onRightButtonSelect();
    }
  }

  showThisAgain() {
    if (this.dialogId != null && this.dontShowThisAgain) {
      window.localStorage.setItem(`dialog${Session.getUserId()}${this.dialogId}`, 'true');
    }
    return;
  }

  saveAcceptAllSections() {
    if (this.acceptAllSections) {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      const acceptAll = window.frames['basefrm'].document.getElementById('acceptAllSections');
      const event = new Event('change');
      acceptAll.dispatchEvent(event);
    }
    return;
  }

  public onClear(): void {
    const element = this.elem.nativeElement as IDialogElement;
    element.inputValue = '';
    this.cdr.detectChanges();
  }

  get input(): OptionsInput {
    return {
      options: this.dialogSelectOptions,
      value: this.selectValue
    };
  }

  get isUserInput(): boolean {
    return this.dialogInput || this.dialogSelect;
  }
}
