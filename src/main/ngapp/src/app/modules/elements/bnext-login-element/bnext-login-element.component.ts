import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { setFocus } from '@/core/utils/focus-util';
import type { IBnextLoginElement } from '@/modules/elements/utils/element.interfaces';
import { checkAppRefresh } from '@/shared/updates/sw-utils';
import { type AfterViewInit, Component, HostBinding, Input, type OnDestroy, type OnInit, inject, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, type UntypedFormGroup, Validators } from '@angular/forms';
import { HammerModule, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import {
  IgxButtonModule,
  type IgxDialogComponent,
  IgxDialogModule,
  IgxDividerModule,
  IgxIconModule,
  IgxInputDirective,
  IgxInputGroupModule,
  IgxProgressBarModule,
  IgxRippleModule
} from '@infragistics/igniteui-angular';
import { Subject, type Subscription, timer } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { ConfigApp } from 'src/app/core/local-storage/config-app';
import { OidcProvider } from 'src/app/core/local-storage/config-app.interfaces';
import { LocalStorageSession } from 'src/app/core/local-storage/local-storage-session';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';
import { AuthService } from 'src/app/core/services/auth.service';
import { LoginService } from 'src/app/core/services/login.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil } from 'src/app/core/utils/form-util';
import { LoginUtils } from 'src/app/pages/login/login-utils';

let NEXT_NAME = 0;

const MAXIMUM_FOCUS_ATTEMPTS = 20;
@Component({
  selector: 'app-dialog-login-element',
  standalone: true,
  imports: [
    IgxInputGroupModule,
    IgxIconModule,
    IgxButtonModule,
    IgxRippleModule,
    IgxDividerModule,
    IgxDialogModule,
    IgxProgressBarModule,
    ReactiveFormsModule,
    FormsModule,
    HammerModule
  ],
  styleUrls: ['./bnext-login-element.component.scss'],
  templateUrl: './bnext-login-element.component.html'
})
export class BnextLoginElementComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy, IBnextLoginElement {
  private titleService = inject(Title);
  private authService = inject(AuthService);
  private loginService = inject(LoginService);
  private noticeService = inject(NoticeService);
  private service = inject(AppService);
  private fb = inject(UntypedFormBuilder);
  private router = inject(Router);
  private api = inject(AppService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.elements',
    componentName: 'bnext-login-element'
  };

  private focusTimeout: Subscription;
  private _isOpen = true;
  private _isLogOff: boolean;
  hasUserAccount: boolean;
  userForm: UntypedFormGroup;
  EnumOidcProvider = OidcProvider;
  loginTitle: string;
  righButtonTitle: string;
  leftButtonTitle: string;
  userLabel: string;
  passwordLabel: string;
  userAccount: string;
  usernameLabel: string;
  busy = false;
  pwaInstalled = false;
  externalAuthWindow: Window = null;
  externalAuthOpenTimer: any = null;
  externalAuthMaxRetryDate: Date = null;
  enableRegistration = false;
  oidcEnabled = false;
  enabledLandingPage = false;
  enableLoginForm = true;
  oidcProvider: OidcProvider = null;
  oidcAuthHref: string = null;
  landingPageHref: string = null;
  externalAuthLabel: DataMap<string> = {};
  private _focusAttempts = 0;
  private onReady = new Subject();
  private subscription: Subscription;
  private subscriptionsOnDestroy: Subscription[] = [];

  @HostBinding('attr.id')
  @Input()
  public id = 'bnextLogin';

  @HostBinding('attr.name')
  @Input()
  public name = `bnextLogin-${NEXT_NAME++}`;

  readonly dialog = viewChild<IgxDialogComponent>('dialog');

  readonly userInput = viewChild('userInput', { read: IgxInputDirective });

  readonly passwordInput = viewChild('passwordInput', { read: IgxInputDirective });

  readonly signInButton = output();
  readonly cancelButton = output();

  readonly open = output();

  readonly close = output();
  readonly logged = output<boolean>();

  @Input()
  public config = '';

  @Input()
  set isLogOff(logOff: boolean) {
    this._isLogOff = logOff;
    if (logOff) {
      this.signOut();
    }
  }

  get isLogOff(): boolean {
    return this._isLogOff;
  }

  @Input()
  set isOpen(isOpen: boolean) {
    this.loader.hide();
    this._isOpen = isOpen;
    if (isOpen) {
      this.setupDialog();
    } else {
      this.dialog()?.close();
    }
  }

  get isOpen(): boolean {
    return this._isOpen;
  }

  constructor() {
    super();

    const fb = this.fb;

    const account = this.getUserAccount();
    this.hasUserAccount = account !== null && typeof account !== 'undefined' && account.trim() !== '';
    if (this.hasUserAccount) {
      this.userForm = fb.group({
        password: [null, Validators.required]
      });
    } else {
      this.userForm = fb.group({
        username: [null, Validators.required],
        password: [null, Validators.required]
      });
    }
    this.oidcAuthHref = this.loginService.oidcUrl(ConfigApp.getOidcProvider());
    this.landingPageHref = ConfigApp.getLandingPageUrl();
    this.enableRegistration = ConfigApp.getEnableRegistration();
    this.oidcEnabled = ConfigApp.getOidcEnabled();
    this.enabledLandingPage = ConfigApp.getEnabledLandingPage();
    this.oidcProvider = ConfigApp.getOidcProvider();
    this.enableLoginForm = ConfigApp.getEnabledLoginForm();
    if (this.hasUserAccount) {
      this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'login').subscribe((tag) => (this.loginTitle = tag));
    } else {
      this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'full-login').subscribe((tag) => (this.loginTitle = tag));
    }
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'password').subscribe((tag) => (this.passwordLabel = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'okta-login-title').subscribe((tag) => (this.externalAuthLabel[OidcProvider.OKTA] = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'microsoft-login-title').subscribe((tag) => (this.externalAuthLabel[OidcProvider.MICROSOFT] = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'landing-page-login-title').subscribe((tag) => (this.externalAuthLabel.landingPage = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'username').subscribe((tag) => (this.usernameLabel = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'enter').subscribe((tag) => (this.righButtonTitle = tag));
    this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'logout').subscribe((tag) => (this.leftButtonTitle = tag));
  }

  closeSubscription: Subscription;

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.clearTimeout();
    if (this.closeSubscription) {
      this.closeSubscription.unsubscribe();
    }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.subscriptionsOnDestroy) {
      for (const subs of this.subscriptionsOnDestroy) {
        if (subs) {
          subs.unsubscribe();
        }
      }
    }
    if (this.externalAuthOpenTimer) {
      clearInterval(this.externalAuthOpenTimer);
    }
  }

  private clearTimeout() {
    if (!this.focusTimeout) {
      return;
    }
    this.focusTimeout.unsubscribe();
    this.focusTimeout = null;
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.loginService.loginAvailable(this.$destroy).then(
      (status) => {
        this.oidcEnabled = ConfigApp.getOidcEnabled();
        this.oidcProvider = ConfigApp.getOidcProvider();
        this.enableRegistration = ConfigApp.getEnableRegistration();
        this.oidcAuthHref = this.loginService.oidcUrl(ConfigApp.getOidcProvider());
        this.landingPageHref = ConfigApp.getLandingPageUrl();
        this.enabledLandingPage = ConfigApp.getEnabledLandingPage();
        this.enableLoginForm = ConfigApp.getEnabledLoginForm();
        console.log('login available! status: ', status);
        if (status === 'SSO_ONLY') {
          this.afterSuccessfulLogIn();
        }
        this.checkForUpdates();
      },
      () => {
        this.checkForUpdates();
      }
    );
    this.subscription = this.onReady.pipe(throttleTime(300)).subscribe(() => this.signIn());
    this.setupDialog();
  }

  private checkForUpdates(): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      checkAppRefresh(this.api, this.translateService, this.loader, this.dialogService, this.$destroy).then((shown) => {
        if (!shown) {
          resolve(false);
          return;
        }
        const element = this.elem.nativeElement as IBnextLoginElement;
        element.isOpen = false;
        this.cdr.detectChanges();
        resolve(true);
      });
    });
  }

  onOpen(): void {
    this.loader.show();
    this.open.emit();
    this.userForm.reset();
  }

  onOpened(): void {
    this.loader.hide();
    this.cdr.detectChanges();
    if (this.hasUserAccount) {
      this.focusPasswordInput();
    } else {
      this.focusUserInput();
    }
  }

  private async setupDialog() {
    const hasUpdates = await this.checkForUpdates();
    if (hasUpdates) {
      return;
    }
    this.cdr.detectChanges();
    this.dialogService.close();
    const dialog = this.dialog();
    if (!dialog.isOpen) {
      dialog.open();
    }
  }

  private isElementFocused(element: HTMLInputElement): boolean {
    if (!document.activeElement || !element) {
      return false;
    }
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    const currentFocusName = document.activeElement['name'];
    if (currentFocusName === 'basefrm') {
      return true;
    }
    return currentFocusName === element.name;
  }

  focusPasswordInput() {
    if (!this.isOpen) {
      return;
    }
    const passwordInput = this.passwordInput();
    setFocus(passwordInput);
    this.cdr.detectChanges();
    if (this.isElementFocused(passwordInput?.nativeElement)) {
      this.clearTimeout();
      return;
    }
    this.clearTimeout();
    this.focusTimeout = timer(500)
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        if (this._focusAttempts > MAXIMUM_FOCUS_ATTEMPTS) {
          this.clearTimeout();
          return;
        }
        if (!this.isElementFocused(this.passwordInput()?.nativeElement)) {
          this._focusAttempts++;
          console.warn('Inline Login - Password input not visible, retrying.');
          this.focusPasswordInput();
        } else {
          this._focusAttempts = 0;
        }
      });
  }

  focusUserInput() {
    if (!this.isOpen) {
      return;
    }
    const userInput = this.userInput();
    setFocus(userInput);
    this.cdr.detectChanges();
    if (this.isElementFocused(userInput?.nativeElement)) {
      this.clearTimeout();
      return;
    }
    this.clearTimeout();
    this.focusTimeout = timer(500)
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        if (this._focusAttempts > MAXIMUM_FOCUS_ATTEMPTS) {
          this.clearTimeout();
          return;
        }
        if (!this.isElementFocused(this.userInput()?.nativeElement)) {
          this._focusAttempts++;
          console.warn('Inline Login -User input not visible, retrying.');
          this.focusUserInput();
        } else {
          this._focusAttempts = 0;
        }
      });
  }

  onSignIn(): void {
    this.onReady.next(0);
  }

  private refreshFormValues(): void {
    if (!this.hasUserAccount) {
      const userInput = this.userInput();
      if (this.userForm.get('username') && this.userForm.get('username').value !== userInput?.nativeElement.value) {
        this.userForm.get('username').setValue(userInput?.nativeElement.value);
      }
    }
    const passwordInput = this.passwordInput();
    if (this.userForm.get('password') && this.userForm.get('password').value !== passwordInput?.nativeElement.value) {
      this.userForm.get('password').setValue(passwordInput?.nativeElement.value);
    }
  }

  signIn(token?: string): void {
    if (this.busy) {
      console.warn('Login already in process.');
      return;
    }
    this.refreshFormValues();
    if (!FormUtil.isValid(this.userForm, null, true).status) {
      this.releaseBusy();
      const message = this.translate.instantFrom(BnextLoginElementComponent.LANG_CONFIG, 'badCredentials');
      this.noticeService.notice(message);
      this.checkForUpdates();
      return;
    }
    const data = this.userForm.value;
    let userAccount: string;
    if (this.hasUserAccount) {
      userAccount = this.getUserAccount();
    } else {
      userAccount = data.username;
    }
    const password = data.password;
    this.lockBusy();
    const skipDuplicatedAccessDialog = this.router.url.includes('&nd=1');
    this.loginService.isGeolocationRequired(userAccount, this.$destroy).then((required) => {
      console.log('2. Geolocation required:', required, ConfigApp.getTrackLocation());
      const geolocationRequired = required && ConfigApp.getTrackLocation();
      this.loginService
        .login(geolocationRequired, userAccount, password, token, this.$destroy)
        .then(
          (result) => {
            if (result === true) {
              this.afterSuccessfulLogIn();
            } else {
              this.handleError(result);
            }
          },
          (info) => {
            this.handleError(info, skipDuplicatedAccessDialog);
          }
        )
        .catch((info) => this.handleError(info));
      this.signInButton.emit();
    });
  }

  private afterSuccessfulLogIn(): void {
    this.releaseBusy();
    if (Session.isRenewDataNeeded()) {
      this.loader.show();
      location.replace('../rest/renew-data');
      return;
    }
    this.dialog().close();
    this.loader.hide();
    this.logged.emit(true);
    if (LocalStorageSession.hasValue(LocalStorageItem.FEEDBACK_LOCATION) && LocalStorageSession.getValue(LocalStorageItem.FEEDBACK_LOCATION) === 'true') {
      if (Session.getBrowser() !== null) {
        const title = this.translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, 'geolocation-permission-dennied.title');
        const message = this.translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, `geolocation-permission-dennied.${Session.getBrowser()}`);
        this.dialogService.info(message, 'root.common.button.ok', null, title).then(
          () => {
            LocalStorageSession.setValue(LocalStorageItem.FEEDBACK_LOCATION, false);
            this.showWelcome();
          },
          () => {
            LocalStorageSession.setValue(LocalStorageItem.FEEDBACK_LOCATION, false);
            this.showWelcome();
          }
        );
      } else {
        this.showWelcome();
      }
    } else {
      this.showWelcome();
    }
  }

  redirectExternalUrl(event: MouseEvent, url: string) {
    if (event) {
      if (event.ctrlKey) {
        return;
      }
      event.preventDefault();
      event.stopPropagation();
    }
    this.loader.show();
    this.cdr.detectChanges();
    if (this.pwaInstalled) {
      window.location.href = url;
    } else {
      this.openExternalAuthTab(url);
    }
  }

  openOidcAuth(event: MouseEvent) {
    this.redirectExternalUrl(event, this.oidcAuthHref);
  }

  openLandingPage(event: MouseEvent) {
    this.redirectExternalUrl(event, this.landingPageHref);
  }

  private openExternalAuthTab(url: string): void {
    if (this.externalAuthWindow) {
      return;
    }
    this.externalAuthWindow = window.open(url, '_blank');
    if (window.focus) {
      this.externalAuthWindow.focus();
    }
    this.externalAuthMaxRetryDate = new Date();
    this.externalAuthMaxRetryDate.setMinutes(this.externalAuthMaxRetryDate.getMinutes() + 5);
    this.externalAuthOpenTimer = setInterval(() => {
      if (this.externalAuthWindow?.closed || this.isExternalAuthInMainWindow() || new Date().getTime() > this.externalAuthMaxRetryDate.getTime()) {
        clearInterval(this.externalAuthOpenTimer);
        this.externalAuthWindow.close();
        this.externalAuthWindow = null;
        this.externalAuthOpenTimer = null;
        this.externalAuthMaxRetryDate = null;
        this.loadCurrentUser(url === this.oidcAuthHref);
      }
    }, 200);
  }

  private isExternalAuthInMainWindow(): boolean {
    if (!this.externalAuthWindow) {
      return false;
    }
    try {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      return !!this.externalAuthWindow['isMainWindow'];
    } catch (_e) {
      return false;
    }
  }

  private loadCurrentUser(oidcAuthCallled: boolean): void {
    this.loginService.fillUserData(false, this.$destroy).then(
      () => this.afterSuccessfulLogIn(),
      () => {
        let message: string = null;
        if (oidcAuthCallled) {
          switch (this.oidcProvider) {
            case OidcProvider.OKTA:
              message = this.translate.instantFrom(BnextLoginElementComponent.LANG_CONFIG, 'oktaLoginFailedPage');
              break;
            case OidcProvider.MICROSOFT:
              message = this.translate.instantFrom(BnextLoginElementComponent.LANG_CONFIG, 'microsoftLoginFailedPage');
              break;
          }
        } else {
          message = this.translate.instantFrom(BnextLoginElementComponent.LANG_CONFIG, 'windowsLoginFailedPage');
        }
        this.dialogService.info(message);
        this.loader.hide();
        this.cdr.detectChanges();
      }
    );
  }

  private handleError(info: any, skipDuplicatedAccessDialog = false): any {
    this.loader.hide();
    if (
      info.error &&
      (info.error?.exception === 'UserInactiveSession' ||
        info.error?.exception === 'UserToActivate' ||
        info.error?.exception === 'InternalAuthenticationService' ||
        info.error?.exception === 'BadCredentials')
    ) {
      const errorHandle = ErrorHandling.notifyException(info, this.navLang);
      if (errorHandle) {
        errorHandle.then(
          () => {
            this.releaseBusy();
            this.focusPasswordInput();
          },
          () => {
            this.releaseBusy();
            this.focusPasswordInput();
          }
        );
      }
    } else if (info.error?.exception === 'AlreadyAnonymousSessionStarted') {
      this.releaseBusy();
      this.loader.show();
      this.loginService
        .notifyAlreadySessionStarted(info, this.dialogService, false, this.translateService.instantFrom(LoginUtils.LANG_CONFIG, 'loginError.duplicate-anonymous-session'))
        .then(() => location.replace(`${this.getLang()}`));
    } else if (info.error && (info.error?.exception === 'AlreadySessionStarted' || info.error?.exception === 'ExpiredTokenAndSessionStarted')) {
      this.releaseBusy();
      this.loader.show();
      this.loginService.notifyAlreadySessionStarted(info, this.dialogService, skipDuplicatedAccessDialog).then(
        (localToken) => {
          this.signIn(localToken);
        },
        () => console.error('Failed to login.')
      );
    } else if (info.name === 'TimeoutError') {
      this.dialogService.error(this.translateService.instantFrom(LoginUtils.LANG_CONFIG, 'TimeoutError'));
    } else if (info.error && info.error?.exception === 'UserLocked') {
      this.releaseBusy();
      ErrorHandling.notifyException(info, this.navLang);
    } else {
      this.releaseBusy();
      ErrorHandling.notifyError(info, this.navLang);
    }
  }

  getUserAccount(): string {
    const user = Session.getUserAccount();
    if (user) {
      return user;
    }
    const userAccountDom = document.getElementById('userAccount');
    if (userAccountDom) {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      return userAccountDom['value'] || '';
    }
    this.hasUserAccount = false;
    if (this.userForm && !this.userForm.get('username')) {
      this.userForm = this.fb.group({
        username: [null, Validators.required],
        password: [null, Validators.required]
      });
      this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'full-login').subscribe((tag) => (this.loginTitle = tag));
    }
    return '';
  }

  signOut(): void {
    this.authService.logout().subscribe(() => {
      if (LocalStorageSession.hasValue(LocalStorageItem.LAST_LANG) && LocalStorageSession.getValue(LocalStorageItem.LAST_LANG) !== null) {
        location.replace(`../qms/${LocalStorageSession.getValue(LocalStorageItem.LAST_LANG)}`);
      } else {
        location.replace(`../${this.getLang()}`);
      }
    });
  }

  private showWelcome(): void {
    if (!Session.showWelcome()) {
      return;
    }
    this.titleService.setTitle('Bnext QMS - Bienvenido');
    this.subscriptionsOnDestroy.push(
      this.translate.getFrom(BnextLoginElementComponent.LANG_CONFIG, 'welcome-title').subscribe((title) => {
        let firstName = Session.getUserName().trim();
        if (firstName.indexOf(' ') > 0) {
          firstName = firstName.substring(0, firstName.indexOf(' '));
        }
        const welcomeMessage = ConfigApp.getWelcomeMessage();
        if (welcomeMessage === null || typeof welcomeMessage === 'undefined' || welcomeMessage.trim() === '') {
          this.hideWelcome();
        } else {
          this.dialogService
            .frame(welcomeMessage, this.translate.instantFrom(BnextLoginElementComponent.LANG_CONFIG, 'acknowledge'), null, `${title}, ${firstName}`)
            .then(
              () => {
                this.hideWelcome();
              },
              () => {}
            );
        }
      })
    );
  }
  async hideWelcome(): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      this.service.get({ cancelableReq: this.$destroy, url: 'users/read-welcome' }).subscribe({
        next: () => {
          Session.hideWelcome();
          resolve(true);
        },
        error: () => {
          resolve(true);
        }
      });
    });
  }

  private lockBusy() {
    this.busy = true;
    this.cdr.detectChanges();
  }

  private releaseBusy() {
    this.busy = false;
    this.cdr.detectChanges();
  }

  public onClosed(): void {
    this.close.emit();
  }
}
