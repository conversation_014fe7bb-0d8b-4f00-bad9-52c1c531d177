import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import type { DataMap } from '@/core/utils/data-map';
import { BnextBaseHandlerDirective } from '@/modules/elements/bnext-base-handler/bnext-base-handler.directive';
import type { AfterViewInit, ElementRef, OnDestroy, OnInit } from '@angular/core';
import { CUSTOM_ELEMENTS_SCHEMA, Component, HostBinding, Injector, Input, NgZone, Renderer2, inject, output, viewChild } from '@angular/core';
import { HammerModule } from '@angular/platform-browser';
import { takeUntil } from 'rxjs';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { DialogService } from 'src/app/core/services/dialog.service';
import type { DialogConfiguration, DialogMessage } from 'src/app/core/services/dialog.service.interfaces';
import { type DraggingZoneOptions, DraggingZoneReleaseService } from 'src/app/core/services/dragging-zone-release.service';
import type { ILoaderElement } from '../utils/bnext-config.interfaces';
import { type BnextCoreAliveHandler, BnextCoreAliveKey, type BnextElementHandlerResult, type IDialogElement } from '../utils/element.interfaces';
import { type IBnextCoreElement, buildCoreHandler } from './bnext-core-element.interfaces';

let NEXT_NAME = 0;

@Component({
  selector: 'app-core-element',
  imports: [HammerModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './bnext-core-element.component.html',
  styleUrls: ['./bnext-core-element.component.scss']
})
export class BnextCoreElementComponent
  extends BnextBaseHandlerDirective<BnextCoreAliveHandler, BnextCoreAliveKey>
  implements OnDestroy, AfterViewInit, OnInit, IBnextCoreElement
{
  private zone = inject(NgZone);
  private injector = inject(Injector);
  private _dialogService = inject(DialogService);
  private loaderService = inject(BnextLoaderActivationService);

  draggingZoneReleaseService = inject(DraggingZoneReleaseService);
  render = inject(Renderer2);

  readonly mainContent = viewChild<ElementRef>('mainContent');

  readyHandler: DataMap<BnextElementHandlerResult> = {};

  elementKey = BnextCoreAliveKey;
  aliveHandler: BnextCoreAliveHandler;

  private currentDialogMessage: DialogMessage;

  @HostBinding('attr.id')
  @Input()
  public id = 'bnextElementCore';

  @HostBinding('attr.name')
  @Input()
  public name = `bnextElementCore-${NEXT_NAME++}`;

  @Input()
  set bnextDialog(alive: boolean) {
    this.elementAlive(BnextCoreAliveKey.DIALOG, alive);
  }

  get bnextDialog(): boolean {
    return this.aliveHandler.DIALOG.alive;
  }

  @Input()
  set bnextDialogReady(ready: BnextElementHandlerResult) {
    this.bnextDialog = !!ready;
  }

  get bnextDialogReady(): BnextElementHandlerResult {
    return this.readyHandler[BnextCoreAliveKey.DIALOG];
  }

  @Input()
  set bnextNotice(alive: boolean) {
    this.elementAlive(BnextCoreAliveKey.NOTICE, alive);
  }

  get bnextNotice(): boolean {
    return this.aliveHandler.NOTICE.alive;
  }

  @Input()
  set bnextNoticeReady(ready: BnextElementHandlerResult) {
    this.bnextNotice = !!ready;
  }

  get bnextNoticeReady(): BnextElementHandlerResult {
    return this.readyHandler[BnextCoreAliveKey.NOTICE];
  }

  @Input()
  set bnextOverlay(alive: boolean) {
    this.elementAlive(BnextCoreAliveKey.OVERLAY, alive);
  }

  get bnextOverlay(): boolean {
    return this.aliveHandler.OVERLAY.alive;
  }

  @Input()
  set bnextOverlayReady(ready: BnextElementHandlerResult) {
    this.bnextOverlay = !!ready;
  }

  get bnextOverlayReady(): BnextElementHandlerResult {
    return this.readyHandler[BnextCoreAliveKey.OVERLAY];
  }

  @Input()
  set bnextLoader(alive: boolean) {
    this.elementAlive(BnextCoreAliveKey.LOADER, alive);
  }

  get bnextLoader(): boolean {
    return this.aliveHandler.LOADER.alive;
  }

  @Input()
  set bnextLoaderReady(ready: BnextElementHandlerResult) {
    this.bnextLoader = !!ready;
  }

  get bnextLoaderReady(): BnextElementHandlerResult {
    return this.readyHandler[BnextCoreAliveKey.LOADER];
  }

  get dialogService(): DialogService {
    return this._dialogService;
  }

  readonly ready = output<BnextElementHandlerResult>();

  constructor() {
    super();
    this.aliveHandler = buildCoreHandler(this.zone, this.injector);
    this.draggingZoneReleaseService.draggingZone.pipe(takeUntil(this.$destroy)).subscribe((o) => this.onDraggingZoneReleased(o));
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.enableComponent(BnextCoreAliveKey.LOADER);
    this.enableComponent(BnextCoreAliveKey.NOTICE);
    this.enableComponent(BnextCoreAliveKey.OVERLAY);
    this.enableComponent(BnextCoreAliveKey.DIALOG);
    this.dialogService.subjectConfiguration.pipe(takeUntil(this.$destroy)).subscribe((c) => this.onNextSubjectConfig(c));
    this.dialogService.message.pipe(takeUntil(this.$destroy)).subscribe((m) => this.onNextDialogMessage(m));
    this.loaderService.currentValue.subscribe((activation) => this.onLoaderChange(activation));
    this.cdr.detectChanges();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  private getBnextDialog(): Promise<BnextElementHandlerResult> {
    return this.elementHandler(BnextCoreAliveKey.DIALOG);
  }

  private getBnextLoader(): Promise<BnextElementHandlerResult> {
    return this.elementHandler(BnextCoreAliveKey.LOADER);
  }

  private onDraggingZoneReleased(options: DraggingZoneOptions) {
    if (options.draggingOverlayVisible) {
      this.render.addClass(this.mainContent().nativeElement, 'dragging-zone');
    } else {
      this.render.removeClass(this.mainContent().nativeElement, 'dragging-zone');
    }
  }

  private onNextSubjectConfig(config: DialogConfiguration) {
    this.getBnextDialog().then((element: BnextElementHandlerResult) => {
      const dialog = element.domNode as unknown as IDialogElement;
      dialog.configuration = config;
    });
  }

  private onNextDialogMessage(msg: DialogMessage) {
    if (msg.justClose && !this.aliveHandler[BnextCoreAliveKey.DIALOG].alive) {
      return;
    }
    if (msg.message === null || typeof msg.message === 'undefined' || msg.message === '' || msg.message === '-') {
      return;
    }
    this.getBnextDialog().then(
      (element: BnextElementHandlerResult) => {
        this.onLoadedBnextDialog(element, msg);
      },
      (e) => console.error(e)
    );
  }

  private onLoaderChange(activation: boolean): void {
    this.getBnextLoader().then((element: BnextElementHandlerResult) => {
      if (!this.loaderService.currentValue) {
        console.warn('Invalid loader activation service');
      } else {
        const loader = element.domNode as unknown as ILoaderElement;
        loader.isShown = activation;
      }
    });
  }

  private onLoadedBnextDialog(element: BnextElementHandlerResult, msg: DialogMessage) {
    const dialog = element.domNode as unknown as IDialogElement;
    dialog.dialogScrollableMessage = null;
    dialog.maximizeAvailable = msg.maximizeAvailable || false;
    dialog.maximized = msg.maximized || false;
    dialog.closeOnEscape = msg.closeOnEscape || false;
    if (!msg.message) {
      return;
    }
    if (msg.input) {
      dialog.inputMinLimit = msg.inputMinLimit || 0;
      dialog.inputMaxLimit = msg.inputMaxLimit || 255;
      if (msg.inputLimitText) {
        dialog.inputLimitText = msg.inputLimitText;
      } else {
        this.translate.get('root.common.message.input-limit-text').subscribe((inputLimitText) => {
          dialog.inputLimitText = inputLimitText.replace('{inputMaxLimit}', dialog.inputMaxLimit);
        });
      }
      if (!msg.leftButton) {
        msg.leftButton = 'root.common.button.cancel';
      }
      if (!msg.rightButton) {
        msg.rightButton = 'root.common.button.ok';
      }
    }
    if (msg.input && typeof msg.inputMessage === 'undefined') {
      dialog.dialogInputMessage = '';
    } else if (msg.input) {
      if (msg.inputMessage.startsWith('root.')) {
        this.translate.get(msg.inputMessage).subscribe((inputMessage) => {
          dialog.dialogInputMessage = inputMessage;
        });
      } else {
        dialog.dialogInputMessage = msg.inputMessage;
      }
    }
    if (msg.message.startsWith('root.')) {
      this.translate.get(msg.message).subscribe((message) => {
        dialog.dialogMessage = message;
      });
    } else {
      dialog.dialogMessage = msg.message;
    }
    if (msg.input && msg.inputValue !== null && typeof msg.inputValue !== 'undefined' && msg.inputValue !== '') {
      dialog.inputValue = msg.inputValue;
    }
    if (msg.select) {
      if (msg.selectValue !== null && typeof msg.selectValue !== 'undefined') {
        dialog.selectValue = msg.selectValue;
        dialog.selectText = msg.selectText;
      }
      if (msg.selectLabel !== null && typeof msg.selectLabel !== 'undefined' && msg.selectLabel !== '') {
        dialog.dialogSelectLabel = msg.selectLabel;
      }
      if (msg.selectHint !== null && typeof msg.selectHint !== 'undefined' && msg.selectHint !== '') {
        dialog.dialogSelectHint = msg.selectHint;
      }
      if (Array.isArray(msg.selectOptions)) {
        dialog.dialogSelectOptions = msg.selectOptions;
      } else {
        dialog.dialogSelectOptions = [{ text: 'Missing `dialogSelectOptions`', value: null }];
      }
    } else {
      dialog.dialogSelectLabel = null;
      dialog.selectValue = null;
      dialog.selectText = null;
      dialog.dialogSelectHint = null;
      dialog.dialogSelectOptions = [];
    }
    if (msg.title?.startsWith('root.')) {
      this.translate.get(msg.title).subscribe((title) => {
        dialog.dialogTitle = title;
      });
    } else if (msg.title) {
      dialog.dialogTitle = msg.title;
    } else if (this.isUserInput(msg) && !dialog.dialogInputMessage) {
      dialog.dialogTitle = dialog.dialogMessage;
      dialog.dialogMessage = '';
    } else if (this.isUserInput(msg) && dialog.dialogInputMessage) {
      dialog.dialogTitle = dialog.dialogMessage;
      dialog.dialogMessage = dialog.dialogInputMessage;
      dialog.dialogInputMessage = msg.dialogLabelInputMessage;
    } else {
      dialog.dialogTitle = '';
    }
    if (msg.leftButton?.startsWith('root.')) {
      this.translate.get(msg.leftButton).subscribe((leftButtonLbl) => {
        dialog.leftDialogButton = leftButtonLbl;
      });
    } else {
      dialog.leftDialogButton = msg.leftButton || '';
    }
    dialog.dialogInput = !!msg.input;
    dialog.dialogSelect = !!msg.select;
    dialog.dialogId = msg.dialogId || null;
    dialog.acceptAllBox = msg.acceptAllBox || null;
    dialog.dontShowThisAgainLabel = msg.dontShowThisAgainLabel || '';
    dialog.acceptAllSectionsLabel = msg.acceptAllSectionsLabel || '';
    this.currentDialogMessage = msg;
    if (msg.rightButton?.startsWith('root.')) {
      this.translate.get(msg.rightButton).subscribe((rightButtonLbl) => {
        dialog.rightDialogButton = rightButtonLbl;
      });
    } else {
      dialog.rightDialogButton = msg.rightButton || 'Aceptar';
    }
    if (typeof msg.scrollableAvailable === 'undefined') {
      dialog.scrollableAvailable = true;
    } else {
      dialog.scrollableAvailable = msg.scrollableAvailable;
    }
    if (typeof msg.executeRightButtonOnEnter === 'undefined') {
      dialog.executeRightButtonOnEnter = false;
    } else {
      dialog.executeRightButtonOnEnter = msg.executeRightButtonOnEnter;
    }
    dialog.dialogScrollableMessage = msg.scrollableMessage || null;
    dialog.closeOnOutsideSelect = false;
    dialog.isPadding = true;
    dialog.customContent = false;
    dialog.isOpen = true;
    if (msg.timeoutShown) {
      msg.timeoutShown.next(true);
      msg.timeoutShown.complete();
    }
  }

  onDialogRightButton(event: Event): void {
    if (this.currentDialogMessage) {
      const customEvent = event as CustomEvent<DialogResult>;
      const dialog = customEvent?.detail?.dialog;
      if (dialog) {
        dialog.isOpen = false;
        dialog.reset = true;
      }
      this.currentDialogMessage?.action?.resolve(customEvent?.detail);
      this.currentDialogMessage = null;
    }
  }

  onDialogLefttButton(event: Event): void {
    if (this.currentDialogMessage) {
      const customEvent = event as CustomEvent<DialogResult>;
      const dialog = customEvent?.detail?.dialog;
      if (dialog) {
        dialog.isOpen = false;
        dialog.reset = true;
      }
      this.currentDialogMessage?.action?.reject(customEvent?.detail);
      this.currentDialogMessage = null;
    }
  }

  private isUserInput(dialog: DialogMessage): boolean {
    return dialog.input || dialog.select;
  }
}
