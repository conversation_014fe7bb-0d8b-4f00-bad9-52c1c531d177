import { Component, Input, type OnInit, inject, input, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import {
  DefaultSortingStrategy,
  type IGroupingExpression,
  type ISortingExpression,
  IgxButtonDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { BnextCoreComponent } from '../bnext-core.component';
import { RESERVED_FIELD_NAMES } from '../external-catalog/utils/hierarchy.interfaces';
import { GridComponent } from '../grid/grid.component';
import type { GridColumn } from '../grid/utils/grid-column';
import { CatalogHierarchyColumn, RangeFilterType } from '../grid/utils/grid-external-catalog.interfaces';
import {
  type ActionStrip,
  CurrencyColumn,
  DateColumn,
  DoubleColumn,
  HiddenColumn,
  HtmlColumn,
  IntegerColumn,
  ObjectListColumn,
  TextColumn,
  TextListColumn,
  TimeColumn,
  TimestampColumn
} from '../grid/utils/grid.interfaces';
import type { BnextComponentPath } from '../i18n/bnext-component-path';

import { Session } from '../local-storage/session';
import { ReportColumnType } from '../reports/column-types.enums';

import * as GridUtil from '@/core/grid/utils/grid-util';
import * as DateUtil from '@/core/utils/date-util';
import { TransformedTypeFinalValue } from '@/modules/forms/slim-report-base/slim-report-base.enums';
import { getRuleValue } from '@/modules/forms/slim-report-base/slim-report-base.util';
import { DropdownSearchComponent } from '../dropdown-search/dropdown-search.component';
import { HierarchySearchComponent } from '../external-catalog/hierarchy-search/hierarchy-search.component';
import type { GridDropDownItem } from '../grid/utils/grid-base.interfaces';
import type { GridRowType } from '../grid/utils/grid-cell-type';
import { BnextTranslatePipe } from '../i18n/bnext-translate.pipe';
import type { DataMap } from '../utils/data-map';
import { EnumDisplayDensity } from '../utils/display-density';
import { CommonAction } from '../utils/enums';
import { ErrorHandling } from '../utils/error-handling';
import type { TextHasValue } from '../utils/text-has-value';
import { AppService } from './../services/app.service';
import type { ReportColumnDTO, ReportColumnRuleDTO, ReportDTO, ReportHierarchyDTO, ReportsFieldDTO } from './gird-report.interfaces';

export enum ReportModuleUrlEnum {
  ACTIVITY = 'menu/activities/reports/',
  TIMESHEET = 'menu/timesheet/reports/',
  TIMEWORK = 'menu/timework/reports/',
  FORMULARIE = 'menu/forms/reports/'
}

export type ReportModuleUrlType = Record<ReportModuleUrlEnum, string>;

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IReportModuleUrl extends ReportModuleUrlType {}

export type ReportModuleUrl = keyof IReportModuleUrl;

@Component({
  selector: 'app-grid-report',
  templateUrl: './grid-report.component.html',
  styleUrls: ['./grid-report.component.scss'],
  imports: [
    GridComponent,
    FormsModule,
    ReactiveFormsModule,
    DropdownSearchComponent,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxLinearProgressBarComponent,
    IgxIconComponent,
    HierarchySearchComponent,
    BnextTranslatePipe
  ]
})
export class GridReportComponent extends BnextCoreComponent implements OnInit {
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core.grid-report',
    componentName: 'report'
  };

  private titleService = inject(Title);
  private api = inject(AppService);
  private fb = inject(UntypedFormBuilder);
  private activatedRoute = inject(ActivatedRoute);
  public idReport: number;
  protected readonly EnumDisplayDensity = EnumDisplayDensity;
  protected url: string;
  protected columns: GridColumn[];
  private scripts: string;
  private styles: string;
  protected id = 'grid';
  protected busy = true;
  protected localTimeForDates = true;
  protected groupingExpressions: IGroupingExpression[] = [];
  protected sortingExpressions: ISortingExpression[] = [];
  protected reportsAvailables: TextHasValue[] = [];
  private reportsAvailableMap: DataMap = {};
  protected allowRangeSelectorNoColumns = true;
  protected allowRangeSelector = false;
  protected rangeFilterName: string = null;
  protected rangeSelectorColumns = [];
  protected formReportSelected: UntypedFormGroup;
  protected title = '';

  private LangConfig = GridReportComponent.LANG_CONFIG;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public masterId: string;
  protected noContentErrorMessage = 'Se está publicando una nueva versión del formulario, favor de esperar un momento.';

  public readonly actionStripOptions = output<CommonAction[]>();

  public readonly actionStripAction = output<GridDropDownItem>();

  public readonly reportConfigChanged = output<ReportDTO>();

  public readonly baseUrl = input('');

  public readonly primaryKey = input<string>(null);

  public readonly delayInit = input(false);

  public readonly allowMaximize = input<boolean>(undefined);

  public readonly actionStripConfig = input<ActionStrip>({
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (_row: GridRowType) => [],
      rowIdentifierKey: this.primaryKey()
    }
  });

  readonly grid = viewChild<GridComponent>('grid');

  private readonly defaultStrategy = DefaultSortingStrategy.instance();
  #showActionStrip = false;
  private module: string;
  private moduleUrl: ReportModuleUrl;

  protected get showActionStrip(): boolean {
    return this.#showActionStrip;
  }

  public override ngOnInit() {
    this.loader.show();
    super.ngOnInit();
    this.perPage = Session.getGridSize();
    this.formReportSelected = this.fb.group({
      reportSelected: new UntypedFormControl(null)
    });
    if (!this.delayInit()) {
      this.navLang
        .getRouteParams(':id', null, false, this.activatedRoute)
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: ([id]) => {
            this.loadReport(+id, `${this.baseUrl()}/reportConfig/${id}`);
          },
          error: () => {
            this.busy = false;
            this.cdr.detectChanges();
          }
        });
    }
  }

  public loadReport(reportId: number, url: string): Promise<boolean> {
    this.idReport = reportId;
    this.id = this.LangConfig.componentName + reportId;
    return new Promise<boolean>((resolve) => {
      this.api
        .get({ url: url, handleFailure: true, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (config: ReportDTO) => {
            if (!config) {
              console.error(`Report ${reportId} not found`);
              this.loader.hide();
              this.busy = false;
              resolve(true);
              this.cdr.detectChanges();
              return;
            }
            this.reportConfigChanged.emit(config);
            this.loader.hide();
            const lastModifiedDate = DateUtil.format(config.lastModifiedDate, 'DD-MM-YYYY-h:mm:ss');
            this.id = `${this.LangConfig.componentName}_${reportId}_${lastModifiedDate}`;
            this.scripts = config.scripts;
            this.styles = config.styles;
            this.localTimeForDates = config.localTimeForDates;
            this.allowRangeSelector = config.rangeFilterType !== RangeFilterType.NONE;
            this.rangeFilterName = config.rangeFilterName;
            this.buildColumns(config.reportColumns, config.hierarchies);
            this.titleService.setTitle(config.description);
            this.title = config.description;
            this.url = `${this.baseUrl()}/report/${config.queryId}`;
            this.module = config.module;
            this.moduleUrl = ReportModuleUrlEnum[this.module];
            this.getReportsAvailables(config);
            this.busy = false;
            resolve(true);
            this.cdr.detectChanges();
          },
          error: (error) => {
            console.error(error);
            ErrorHandling.notifyError(error, this.navLang);
            this.busy = false;
            resolve(true);
            this.cdr.detectChanges();
          }
        });
    });
  }

  toogleActionStrip(drop: GridDropDownItem) {
    this.actionStripAction.emit(drop);
  }

  private adjustGridOrders(reportColumns: ReportColumnDTO[]) {
    let maxOrder = 0;
    for (const column of reportColumns) {
      if (column.gridOrder > maxOrder) {
        maxOrder = column.gridOrder;
      }
    }
    for (const column of reportColumns) {
      if (column.gridOrder === null || typeof column.gridOrder === 'undefined') {
        column.gridOrder = maxOrder++;
      }
    }
  }

  buildColumns(reportColumns: ReportColumnDTO[], hierarchies: ReportHierarchyDTO[]) {
    if (!reportColumns || !Array.isArray(reportColumns)) {
      return;
    }
    this.adjustGridOrders(reportColumns);
    reportColumns.sort((a, b) => a.gridOrder - b.gridOrder);
    this.columns = [];
    const hierarchyTypes = this.buildHierarchyColumn(reportColumns, hierarchies);
    const columnsUtils = GridUtil.columns(this.columns);
    for (const element of reportColumns) {
      const type = this.getColumnType(element, hierarchyTypes);
      if (!type) {
        continue;
      }
      columnsUtils.push(element.queryColumnCode, element.description, type);
    }
    this.sortingExpressions = [];
    for (const column1 of reportColumns.filter((column) => column.sortPriority !== null && typeof column.sortPriority !== 'undefined')) {
      this.sortingExpressions.push({
        fieldName: column1.queryColumnCode,
        dir: column1.sortDirection,
        ignoreCase: true,
        strategy: this.defaultStrategy
      });
    }

    this.groupingExpressions = [];
    for (const column1 of reportColumns.filter((column) => column.groupingPriority !== null && typeof column.groupingPriority !== 'undefined')) {
      this.groupingExpressions.push({
        fieldName: column1.queryColumnCode,
        dir: column1.groupingDirection,
        ignoreCase: true,
        strategy: this.defaultStrategy
      });
    }
    this._refreshGroups();
  }

  private buildHierarchyColumn(reportColumns: ReportColumnDTO[], hierarchies: ReportHierarchyDTO[]): DataMap<TextColumn | CatalogHierarchyColumn> {
    const indexHierarchy: DataMap<ReportColumnDTO[]> = {};
    const hierarchyColumns = reportColumns.filter((column) => column.type === ReportColumnType.CATALOG_HIERARCHY && !!column.hierarchyId);
    for (const column of hierarchyColumns) {
      if (!indexHierarchy[column.hierarchyId]) {
        indexHierarchy[column.hierarchyId] = [];
      }
      indexHierarchy[column.hierarchyId].push(column);
    }
    const definedFilters: DataMap<boolean> = {};
    const columnTypes: DataMap<TextColumn | CatalogHierarchyColumn> = {};
    hierarchyColumns.map((column) => {
      if (definedFilters[column.hierarchyCode]) {
        columnTypes[column.queryColumnCode] = new TextColumn({
          searchable: false,
          onlyFilter: column.onlyFilter,
          width: column.width || '200px'
        });
      } else {
        definedFilters[column.hierarchyCode] = true;
        const hierarchyConfig: ReportColumnDTO[] = indexHierarchy[column.hierarchyId];
        const hierarchy = hierarchies.find((hierarchy) => hierarchy.databaseQueryId === column.hierarchyId);
        columnTypes[column.queryColumnCode] = this.getHierarchyColumnType(hierarchy, hierarchyConfig, column.hierarchyCode);
      }
    });
    return columnTypes;
  }

  private getHierarchyColumnType(hierarchy: ReportHierarchyDTO, hiearchyConfig: ReportColumnDTO[], hierarchyCode: string) {
    const hierarchyFields = hierarchy.fields.filter((field) => !field.isEntityKeyId);
    const hierarchyColumns = hiearchyConfig.filter((column) => column.hierarchyCode === hierarchyCode);
    const columnCodes = new Set<string>(hierarchyColumns.map((column) => column.queryColumnCode.replace(`${column.hierarchyCode}_`, '')));
    const allReportFields = hierarchyFields.filter((field) => columnCodes.has(field.column));
    const maxLevelByReport = allReportFields.reduce((max, field) => Math.max(max, field.level), 0);
    const reportFields = hierarchyFields
      .filter((field) => field.level <= maxLevelByReport)
      .filter((field) => {
        if (RESERVED_FIELD_NAMES.includes(field.column)) {
          console.error(`The field ${field.column} is reserved for the hierarchy component`);
          return false;
        }
        return true;
      });
    if (!reportFields.length) {
      return null;
    }
    const hierarchyDescription = hiearchyConfig.find((column) => column.hierarchyCode === hierarchyCode)?.hierarchyDescription;
    const hierarchyUrl = `${this.baseUrl()}/report-hierarchy/${hierarchy.databaseQueryId}/{level}`;
    return new CatalogHierarchyColumn({
      hierarchyTitle: hierarchyDescription || hierarchyCode,
      hierarchyCode: hierarchyCode,
      hidden: true,
      hierarchyUrl: hierarchyUrl,
      hierarchyFields: reportFields
    });
  }

  /**
   * Necesario para que la agrupación se refresque después de agregar cambios
   */
  private _refreshGroups() {
    const grid = this.grid();
    if (grid) {
      grid.groupBy(this.groupingExpressions);
    } else if (this.debug()) {
      console.warn('Grid not available, refreshing groups is not executed.');
    }
  }

  private getReportsFieldValue(field: ReportsFieldDTO, rowData: DataMap<any>): string | number {
    if (!field) {
      return null;
    }
    switch (field.fieldValueType) {
      case 'number':
        return +rowData[field.fieldCode];
      default:
        return rowData[field.fieldCode];
    }
  }

  private isRuleIsTruthy(rule: ReportColumnRuleDTO, rowData: DataMap): boolean {
    if (!rule.config) {
      return false;
    }
    const evaluatedFieldValue = this.getReportsFieldValue(rule.evaluatedField, rowData);
    const ruleFieldValue = this.getReportsFieldValue(rule.config.ruleFieldValue, rowData);
    const ruleValue = getRuleValue(rule, ruleFieldValue);
    switch (rule.config.type) {
      case 'CONTAINS':
        return String(evaluatedFieldValue).indexOf(String(ruleValue)) >= 0;
      case 'EQUALS':
        return String(evaluatedFieldValue).trim() === String(ruleValue).trim();
      case 'ENDS_WITH':
        return String(evaluatedFieldValue).endsWith(String(ruleValue));
      case 'GREATER_THAN':
        return Number(evaluatedFieldValue) > Number(ruleValue);
      case 'GREATER_THAN_OR_EQUALS':
        return Number(evaluatedFieldValue) >= Number(ruleValue);
      case 'IS_EMPTY':
        return !evaluatedFieldValue || String(evaluatedFieldValue).trim() === '';
      case 'IS_NOT_EMPTY':
        return !!evaluatedFieldValue && String(evaluatedFieldValue).trim() !== '';
      case 'LESS_THAN':
        return Number(evaluatedFieldValue) < Number(ruleValue);
      case 'LESS_THAN_OR_EQUALS':
        return Number(evaluatedFieldValue) <= Number(ruleValue);
      case 'NOT_EQUALS':
        return String(evaluatedFieldValue).trim() !== String(ruleValue).trim();
      case 'STARTS_WITH':
        return String(evaluatedFieldValue).startsWith(String(ruleValue));
      default:
        return false;
    }
  }

  getColumnType(column: ReportColumnDTO, hierarchyTypes: DataMap<TextColumn | CatalogHierarchyColumn>) {
    switch (column.type) {
      case ReportColumnType.DATE:
        return new DateColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.HIDDEN:
        return new HiddenColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.TRANSFORMED_FIELD:
        return new TextColumn({
          width: column.width || '150px',
          searchable: false,
          sortable: false,
          onlyFilter: column.onlyFilter || false,
          renderCell: (cell) => {
            return this.renderTransformedFieldValue(column, cell.row.data);
          },
          formatter: (_value, rowData: any) => {
            return this.renderTransformedFieldValue(column, rowData);
          }
        });
      case ReportColumnType.NUMBER:
        return new IntegerColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.DECIMAL:
        return new DoubleColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.PERCENTAGE:
        return new HtmlColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter,
          renderCell: (cell) => {
            return `${+cell.value > 0 ? cell.value : 0}%`;
          }
        });
      case ReportColumnType.CATALOG: {
        const catalog = [];
        if (column.catalogValues?.length) {
          for (const value of column.catalogValues) {
            if (value == null || typeof value === 'undefined') {
              continue;
            }
            if ((typeof value === 'string' && value === '') || (typeof value === 'string' && value === 'undefined')) {
              continue;
            }
            if (typeof value === 'string') {
              const trimValue = `${value}`.trim();
              catalog.push({ text: trimValue, value: trimValue });
            } else {
              catalog.push({ text: value, value: value });
            }
          }
        }
        return new TextListColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter,
          render: {
            valueKey: 'value',
            labelKey: 'text',
            items: catalog
          }
        });
      }
      case ReportColumnType.DATE_TIME:
        return new TimestampColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.WEEK_DAY: {
        const days = DateUtil.getDaysOfWeekISO();
        return new ObjectListColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter,
          render: {
            valueKey: 'value',
            labelKey: 'text',
            items: days
          }
        });
      }
      case ReportColumnType.CURRENCY:
        return new CurrencyColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      case ReportColumnType.SKIP:
        return null;
      case ReportColumnType.CATALOG_HIERARCHY: {
        return hierarchyTypes[column.queryColumnCode];
      }
      case ReportColumnType.TIME:
        return new TimeColumn({
          width: column.width || '150px',
          onlyFilter: column.onlyFilter
        });
      default:
        return new TextColumn({
          width: column.width || '200px',
          onlyFilter: column.onlyFilter || false
        });
    }
  }

  private renderTransformedFieldValue(column: ReportColumnDTO, rowData: any): string | number {
    for (const rule of column.rules) {
      if (!this.isRuleIsTruthy(rule, rowData)) {
        continue;
      }
      if (TransformedTypeFinalValue[rule.config.typeFinalValue] === TransformedTypeFinalValue.EVALUATED_FIELD) {
        return this.getReportsFieldValue(rule.evaluatedField, rowData);
      }
      return rule.finalValue;
    }
    return '';
  }

  valueChanged() {
    this.evalScripts();
    if (this.styles && this.styles !== '') {
      const style = document.createElement('style');
      let reportStyle = this.styles.replace(/(\r\n|\n|\r)/gm, ' ');
      const reportStyleArray = reportStyle.split('}');
      reportStyle = '';
      for (let i = 0; i < reportStyleArray.length - 1; i++) {
        const reportStyleArrayB = reportStyleArray[i].split('{');
        const reportStyleArrayC = reportStyleArrayB[0].split(',');
        if (reportStyleArrayC.length > 0) {
          for (let j = 1; j < reportStyleArrayC.length; j++) {
            reportStyleArrayC[0] += `, #${this.grid().id()} ${reportStyleArrayC[j]}`;
          }
          reportStyleArray[i] = `${reportStyleArrayC[0]} { ${reportStyleArrayB[1]}`;
        }
        reportStyle += ` #${this.grid().id()}${reportStyleArray[i]}} `;
      }
      style.innerText = reportStyle;
      document.getElementsByName(this.grid().name())[0].appendChild(style);
    }
  }

  evalScripts() {
    if (this.scripts && this.scripts !== '') {
      // biome-ignore lint/style/noCommaOperator: TODO: Fix this
      // biome-ignore lint/security/noGlobalEval: TODO: Fix this
      (0, eval)(this.scripts);
    }
  }

  goBack() {
    this.navLang.back();
  }

  private getReportsAvailables(config: ReportDTO) {
    this.api
      .get({
        url: `${this.baseUrl()}/report/reportListAvailables/${this.module}`,
        cancelableReq: this.$destroy
      })
      .subscribe((response) => {
        this.reportsAvailables = [];
        let countPrintingFormats = 0;
        if (response !== null) {
          for (const report of response) {
            if (report.id === this.idReport) {
              countPrintingFormats = report.countPrintFormats;
            }
            this.reportsAvailables.push({ value: report.id, text: report.title });
            this.reportsAvailableMap[report.id] = report;
          }
          this.cdr.detectChanges();
          this.formReportSelected.patchValue({
            reportSelected: this.idReport
          });
          if (config.actionStripOptions?.length > 0) {
            if (!countPrintingFormats) {
              config.actionStripOptions.splice(
                config.actionStripOptions.findIndex((i) => i === CommonAction.PRINTING_OPTIONS),
                1
              );
            }
            this.#showActionStrip = true;
            this.actionStripOptions.emit(config.actionStripOptions);
          }
        }
      });
  }

  changeReport(selectedValue: TextHasValue) {
    if (+selectedValue?.value !== this.idReport) {
      const data = this.reportsAvailableMap[selectedValue.value];
      let url = `${this.moduleUrl + selectedValue.value}`;
      if (typeof data?.documentMasterId === 'string' && data?.documentMasterId !== '') {
        url += `/${encodeURIComponent(data?.documentMasterId)}`;
      }

      this.menuService.navigate(url, Module[this.module]);
    }
  }
}
