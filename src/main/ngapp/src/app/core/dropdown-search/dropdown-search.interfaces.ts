import type { IFocusableElement } from '../utils/form.interfaces';
import type { TextHasValue } from '../utils/text-has-value';
export interface DropdownHasValue extends TextHasValue {
  escapedText?: string; // <-- Contiene el mismo valor que `text` pero sin acentos
}
export interface DropdownPrefixEvent {
  currentValue: number | string;
  event: any;
}
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IDropdownSearchComponent extends IFocusableElement {}
