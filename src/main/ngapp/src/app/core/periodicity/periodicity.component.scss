.periodicity-container ::ng-deep .igx-drop-down__list {
  height: 100%;
}
.form.grid-container {
  max-width: 45rem;
}
.never-selected .hide-on-never {
  display: none;
}

.description textarea[autoresize] {
  min-height: 3.5rem;
}
.disabledDates {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  min-width: 0;
  max-width: 100%;
  color: #ff134a;
  position: relative;
  display: flex;
  justify-content: space-between;
  font-size: 0.75em;
  line-height: 1.16667em;
  padding-top: 0.66667em;
}

.display-form-cell {
  padding: 0;
}

::ng-deep app-periodicity {
  .periodicity-container {
    .display-value .display-form-cell.cell {
      igx-input-group.igx-input-group .igx-input-group__bundle {
        igx-prefix.display-value-prefix {
          margin-right: 0px;
        }
      }
    }
  }
}
