import { type AfterViewInit, Component, HostBinding, HostListener, Input, type OnDestroy, forwardRef, inject, input, output, viewChild } from '@angular/core';
import { type ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import {
  GlobalPositionStrategy,
  type IComboSelectionChangingEventArgs,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxChipComponent,
  IgxChipsAreaComponent,
  IgxComboComponent,
  IgxComboEmptyDirective,
  IgxComboFooterDirective,
  IgxComboHeaderDirective,
  IgxComboItemDirective,
  IgxDialogComponent,
  IgxInputGroupComponent,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import { scaleInCenter, scaleOutCenter } from '@infragistics/igniteui-angular/animations';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import type { GridColumn } from './../grid/utils/grid-column';

import { NgClass } from '@angular/common';
import type { Subscription } from 'rxjs';
import { BnextCoreComponent } from '../bnext-core.component';
import type { DataMap } from '../utils/data-map';
import { FilterGenericPipe } from '../utils/filter-generic';

import { MultiSelectUtil } from './multi-select.utils';

import { stringColors } from '@/core/utils/string-util';
import { NoticeService } from '../services/notice.service';

interface MultiSelectValueTypeAttributes {
  help: string;
  searchKey: string;
}

@Component({
  selector: 'app-multi-select',
  styleUrls: ['./multi-select.component.scss'],
  templateUrl: './multi-select.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MultiSelectComponent),
      multi: true
    }
  ],
  imports: [
    NgClass,
    IgxChipsAreaComponent,
    IgxChipComponent,
    IgxBadgeComponent,
    IgxComboComponent,
    FormsModule,
    IgxComboHeaderDirective,
    IgxComboFooterDirective,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxComboItemDirective,
    IgxComboEmptyDirective,
    FilterGenericPipe
  ]
})
export class MultiSelectComponent<MultiSelectValueType> extends BnextCoreComponent implements ControlValueAccessor, AfterViewInit, OnDestroy {
  noticeService = inject(NoticeService);

  readonly;
  // eslint-disable-next-line @angular-eslint/no-output-native
  change = output<(string | number)[]>();

  readonly selectionChanging = output<IComboSelectionChangingEventArgs>();

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'comfortable';

  public readonly badgeIcon = input<DataMap<string>>({});
  public readonly valueKey = input('id');
  public readonly displayMainKey = input('description');

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public displaySecondaryKey = '';

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public closeLabel = '';
  public readonly groupKey = input('');
  public readonly translateGroupKey = input(true);
  public readonly filterKey = input<string>(this.displayMainKey());
  public readonly filterValue = input<string>(null);
  public readonly name = input('');
  public readonly label = input('');

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public searchPlaceholder = '';
  public readonly help = input('');

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public disabled = false;
  public readonly showHelp = input(false);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public header = null;
  public readonly draggable = input(true);

  public subs: Subscription[] = [];

  get value(): (string | number)[] {
    return this._value;
  }

  get chipValues(): (string | number)[] {
    return this._value;
  }

  get dataMap(): DataMap<MultiSelectValueType> {
    return this._dataMap;
  }

  get grouped(): boolean {
    return typeof this.groupKey() === 'string';
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(newValue: (string | number)[]) {
    if (newValue !== this._value) {
      newValue = newValue || [];
      this._value = newValue;
      this.valueChanged();
    }
  }

  private _data: MultiSelectValueType[] = [];
  private _dataMap: DataMap<MultiSelectValueType> = {};

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set data(value: MultiSelectValueType[]) {
    if (value && value.length > 0) {
      let groupIndex = 1;
      const groupIndexes: DataMap<number> = {};
      const groupKey = this.groupKey();
      const translateGroupKey = this.translateGroupKey();
      const valueKey = this.valueKey();
      const displayMainKey = this.displayMainKey();
      for (const element of value) {
        if (groupKey) {
          const groupLabel = element[groupKey] ? element[groupKey] : 'catalog'; // Valor por defecto
          if (translateGroupKey) {
            const tag = `groupKey.${groupLabel}`;
            const s = this.translate.getFrom(MultiSelectUtil.LANG_CONFIG, tag).subscribe((tag) => {
              element[groupKey] = tag;
            });
            if (s) {
              this.subs.push(s);
            }
          } else {
            if (!groupIndexes[groupLabel]) {
              groupIndexes[groupLabel] = groupIndex++;
            }
            const labelGroupIndex = `${groupIndexes[groupLabel]}. `;
            if (!element[groupKey]?.startsWith?.(labelGroupIndex)) {
              element[groupKey] = `${labelGroupIndex}${groupLabel}`;
            }
          }
        }
        this._dataMap[element[valueKey]] = element;
        // Se pasa el valor del texto a mostrar
        (element as MultiSelectValueTypeAttributes).searchKey = element[displayMainKey];
        if (this.displaySecondaryKey) {
          // Se complementa los valores para filtrar
          (element as MultiSelectValueTypeAttributes).searchKey += `@${element[this.displaySecondaryKey]}`;
        }
      }
    }
    this._data = value;
  }

  get data(): MultiSelectValueType[] {
    return this._data;
  }

  public readonly columns = input<GridColumn[]>([]);

  @HostBinding('class.igx-input-group')
  public defaultClass = true;

  @HostBinding('class.igx-input-group--filled')
  get isFilled() {
    return this.value && this.value.length > 0;
  }

  @HostBinding('class.igx-input-group--empty')
  get isEmpty() {
    return !this.value || this.value.length === 0;
  }

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @Input()
  @HostBinding('class.igx-input-group--required')
  public required = false;

  @HostBinding('class.igx-input-group--focused')
  public isFocused = false;

  @HostBinding('class.igx-input-group--search')
  public isSearch = false;

  readonly dialog = viewChild('dialog', { read: IgxDialogComponent });

  readonly chipsArea = viewChild('chipsArea', { read: IgxChipsAreaComponent });

  readonly inputGroup = viewChild('inputGroup', { read: IgxInputGroupComponent });

  readonly combo = viewChild('combo', { read: IgxComboComponent });

  private _value: (string | number)[] = null;

  displayValue: string;
  emptyCombo = '';

  private _overlaySettings = {
    positionStrategy: new GlobalPositionStrategy({ openAnimation: scaleInCenter, closeAnimation: scaleOutCenter }),
    modal: true,
    closeOnOutsideClick: true
  };

  get overlaySettings() {
    return this._overlaySettings;
  }

  onChange: any = () => {};
  onTouched: any = () => {};

  ngAfterViewInit(): void {
    if (this.emptyCombo === null || typeof this.emptyCombo === 'undefined' || this.emptyCombo === '') {
      this.translate.getFrom(MultiSelectUtil.LANG_CONFIG, 'emptyCombo').subscribe((tag) => {
        this.emptyCombo = tag;
        this.cdr.detectChanges();
      });
    }
    if (this.closeLabel === null || typeof this.closeLabel === 'undefined' || this.closeLabel === '') {
      this.translate.get('root.common.button.close').subscribe((tag) => {
        this.closeLabel = tag;
        this.cdr.detectChanges();
      });
    }
    if (this.searchPlaceholder === null || typeof this.searchPlaceholder === 'undefined' || this.searchPlaceholder === '') {
      this.translate.getFrom(MultiSelectUtil.LANG_CONFIG, 'searchPlaceholder').subscribe((tag) => {
        this.searchPlaceholder = tag;
        this.cdr.detectChanges();
      });
    }
  }

  get hasValues(): boolean {
    return this.value && this.value.length > 0;
  }

  filterItems(item: MultiSelectValueType, value: string, key: string): boolean {
    if (typeof value === 'string') {
      return item[key] === value;
    }
    return true;
  }

  chipText(chipValue: string | number): string {
    return this._dataMap[chipValue]?.[this.displayMainKey()];
  }

  chipColor(str: string | number): string {
    return stringColors(this._dataMap[str]?.[this.filterKey()])?.bgColor;
  }

  chipColorBlack(str: string | number): boolean {
    return stringColors(this._dataMap[str]?.[this.filterKey()])?.color === '#000000';
  }

  writeValue(obj: any): void {
    this.value = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  chipRemoved(chipValue) {
    this.combo().deselect([chipValue]);
    this.chipsArea().cdr.detectChanges();
  }

  chipsOrderChanged(event) {
    const newValueList = [];
    for (const chip of event.chipsArray) {
      const chipItem = this.value.filter((item) => item === chip.id)[0];
      newValueList.push(chipItem);
    }
    this.value = newValueList;
    event.isValid = true;
  }

  isBadgeAvailable(fieldName: string): boolean {
    const badgeIcon = this.badgeIcon();
    return badgeIcon && typeof badgeIcon[fieldName] === 'string';
  }

  onSelectionChange(value: IComboSelectionChangingEventArgs) {
    this.chipsArea().cdr.detectChanges();
    this.selectionChanging.emit(value);
  }

  ngOnDestroy(): void {
    this.chipsArea().cdr.detach();
    this.onChange = null;
    this.onTouched = null;
    this._value = null;
    if (this.subs?.length) {
      for (const s of this.subs) {
        try {
          if (s) {
            s.unsubscribe();
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  }

  clickedOutside() {
    this.combo().close();
  }
  toggleValue() {
    this.combo().toggle();
  }

  @HostListener('keydown.ArrowDown', ['$event'])
  @HostListener('keydown.Alt.ArrowDown', ['$event'])
  onArrowUpDown(evt) {
    evt.preventDefault();
    evt.stopPropagation();
    if (!this.dialog().isOpen) {
      this.toggleValue();
    }
  }

  handleKeyUp(evt) {
    if (evt.key === 'Escape' || evt.key === 'Esc') {
      this.toggleValue();
    }
  }

  handleKeyDown(evt) {
    if (evt.key === 'ArrowUp' || evt.key === 'Up') {
      this.onArrowUpDown(evt);
    }
  }

  castAsString(value: any): string {
    return String(value);
  }

  clear() {
    this.value = null;
  }

  valueChanged() {
    this.onTouched();
    this.change.emit(this.value);
    this.onChange(this.value);
    this.onTouched(this.value);
  }
}
