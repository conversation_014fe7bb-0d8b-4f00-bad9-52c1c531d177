import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from '../test/mock-core.module';
import { MockProviders } from '../test/mock-providers';
import { configureTestSuite } from '../test/utils/configure-test-suite';
import { ConditionalFieldComponent } from './conditional-field.component';

describe('ConditionalFieldComponent', () => {
  let component: ConditionalFieldComponent;
  let fixture: ComponentFixture<ConditionalFieldComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, ConditionalFieldComponent, ConditionalFieldComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
    fixture = TestBed.createComponent(ConditionalFieldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
