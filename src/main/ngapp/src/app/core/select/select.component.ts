import {
  type AfterViewInit,
  Component,
  HostBinding,
  Input,
  type OnChanges,
  type OnD<PERSON>roy,
  type OnInit,
  type SimpleChanges,
  forwardRef,
  input,
  output,
  viewChild
} from '@angular/core';
import {
  type ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  UntypedFormControl
} from '@angular/forms';
import { BnextCoreComponent } from '../bnext-core.component';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import type { DataMap } from '../utils/data-map';
import type { OptionsInput } from '../utils/interfaces';

import { equalsObject } from '@/core/utils/object';
import { sortArrayByAttribute } from '@/core/utils/string-util';
import { NgClass } from '@angular/common';

import {
  ConnectedPositioningStrategy,
  GlobalPositionStrategy,
  HorizontalAlignment,
  IgxHintDirective,
  IgxIconComponent,
  IgxLabelDirective,
  IgxPrefixDirective,
  IgxSelectComponent,
  IgxSelectItemComponent,
  IgxSuffixDirective,
  type OverlaySettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';

import { BnextCloseScrollStrategy } from '../utils/bnext-scroll-strategy';

let NEXT_NAME = 0;

@Component({
  selector: 'app-select',
  styleUrls: ['./select.component.scss'],
  templateUrl: './select.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectComponent),
      multi: true
    }
  ],
  imports: [
    NgClass,
    IgxSelectComponent,
    IgxPrefixDirective,
    IgxIconComponent,
    IgxLabelDirective,
    IgxSelectItemComponent,
    IgxSuffixDirective,
    IgxHintDirective,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class SelectComponent<ValueType = string | number> extends BnextCoreComponent implements ControlValueAccessor, OnDestroy, OnChanges, AfterViewInit, OnInit {
  public static DS_ERROR = 'DataSourceError';
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'select'
  };

  private viewReady = false;

  inputCtrl = input(new UntypedFormControl());

  readonly select = viewChild('select', { read: IgxSelectComponent });

  readonly;
  // eslint-disable-next-line @angular-eslint/no-output-native
  change = output<ValueType>();
  readonly selected = output<ValueType>();
  readonly iconClick = output<boolean>();

  readonly defaultValue = input<ValueType>(null);
  readonly saveButton = input(false);
  public readonly invalid = input(false);
  public readonly valueKey = input('id');

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public icon: string = null;

  public readonly type = input<'line' | 'box' | 'border' | 'search'>('line');
  public readonly displayKey = input('description');

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @Input()
  @HostBinding('attr.name')
  public name = `select-${NEXT_NAME++}`;
  public readonly placeholder = input<string>(undefined);
  public readonly label = input<string>(undefined);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public disabled = false;
  public readonly required = input(false);
  public readonly castDataValueToNumber = input(false);
  public readonly showHelp = input(false);
  public readonly help = input<string>(undefined);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public hint: string;
  readonly sortable = input(true);
  readonly scrollContainer = input<HTMLElement>(undefined);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set input(input: OptionsInput<ValueType>) {
    this.data = input.options;
    this.value = input.value;
  }

  customOverlaySettings: OverlaySettings = null;

  private _value: ValueType = this.defaultValue();

  get value() {
    return this._value;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(val) {
    if (!equalsObject(val, this._value)) {
      this._value = val;
      this.valueChanged();
    }
  }

  private _data = [];
  get data() {
    return this._data;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set data(val: any[]) {
    if (!val || val.length === 0 || equalsObject(this._data, val)) {
      return;
    }
    const displayKey = this.displayKey();
    if (typeof val[0] === 'string') {
      this._data = [];
      for (const str of val as string[]) {
        const newelement: DataMap = {};
        newelement[this.valueKey()] = str;
        newelement[displayKey] = str;
        this._data.push(newelement);
      }
    } else {
      this._data = val;
      // Ordenamiento de los datos
      if (this.sortable()) {
        const lang = this.translate.currentLang;
        sortArrayByAttribute(this._data, displayKey, lang);
      }
      if (this.castDataValueToNumber() && this._data?.length > 0) {
        for (const row of this._data) {
          row[this.valueKey()] = +row[this.valueKey()];
        }
      }
    }
    if (this._data.filter((v) => v === null).length > 0) {
      console.error('Null value found at select.component, check if data-source is correct: ', this._data);
      this._data = [];
      const newelement: DataMap = {};
      newelement[this.valueKey()] = '0';
      newelement[displayKey] = SelectComponent.DS_ERROR;
      this._data.push(newelement);
    }
  }

  onChange: any = () => {};
  onTouched: any = () => {};

  ngOnInit() {
    this.customOverlaySettings = {
      closeOnOutsideClick: true,
      modal: false,
      scrollStrategy: new BnextCloseScrollStrategy(this.scrollContainer()),
      positionStrategy: new ConnectedPositioningStrategy({
        horizontalStartPoint: HorizontalAlignment.Left,
        verticalStartPoint: VerticalAlignment.Bottom
      })
    };
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.onChange = null;
    this.onTouched = null;
    this._value = null;
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.value && this.data.length && changes.value.previousValue !== changes.value.currentValue) {
      this.selectValue(
        this.data.find((item) => item[this.valueKey()] === changes.value.currentValue),
        false
      );
    }
    if (changes.data && !equalsObject(changes.data.previousValue, changes.data.currentValue)) {
      if (this.viewReady) {
        this.cdr.detectChanges();
        this.updateTarget();
      }
    }
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.viewReady = true;
    this.cdr.detectChanges;
    this.updateTarget();
  }

  private updateTarget() {
    if (this.isSmallTouchDevice) {
      this.customOverlaySettings.modal = true;
      this.customOverlaySettings.positionStrategy = new GlobalPositionStrategy();
    } else {
      this.customOverlaySettings.modal = false;
      this.customOverlaySettings.target = this.select()?.element;
    }
  }

  get focusableNative(): HTMLElement {
    return this.elem?.nativeElement;
  }

  onValueSelected(args) {
    if (this.data.length) {
      const item = this.data[args.newSelection.index];
      this.selectValue(item);
    }
  }

  private selectValue(item, emitEvent = true) {
    if (item && item[this.valueKey()] !== null && typeof item[this.valueKey()] !== 'undefined') {
      this.value = item[this.valueKey()];
      if (emitEvent) {
        this.selected.emit(this.value);
      }
    }
  }

  writeValue(obj: any): void {
    this.value = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (isDisabled) {
      this.inputCtrl()?.disable();
    } else {
      this.inputCtrl()?.enable();
    }
  }

  valueChanged() {
    this.change.emit(this.value);
    this.onChange(this.value);
    this.onTouched(this.value);
    if (this.viewReady) {
      this.cdr.detectChanges();
      this.updateTarget();
    }
  }

  public clear(): void {
    if (this.disabled) {
      return;
    }
    this.value = null;
    this.valueChanged();
  }

  public reset(): void {
    this._data = [];
    this._value = null;
    this.valueChanged();
  }
}
