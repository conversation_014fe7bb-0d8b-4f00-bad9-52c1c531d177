import type { ChangeDetectorRef } from '@angular/core';
import type { FileItem, FileUploader } from 'ng2-file-upload';
import type { Entity } from '../utils/entity';

export interface IFileDataTemplate extends Entity {
  code?: string;
  contentSha512?: string;
  contentSize?: number;
  contentType?: string;
  createdBy?: number;
  createdDate?: Date;
  description: string;
  extension?: string;
  id: number;
  lastModifiedBy?: string;
  lastModifiedDate?: Date;
  stage?: string;
  thumbnailSize?: number;
  activityCommitmentTask?: number;
  commentFile?: string;
  activityId?: number;
}

export interface IFileData extends IFileDataTemplate {
  busy?: number;
  hasPdf?: number;
  hasPdfPages?: number;
  isCancel?: boolean;
  isError?: boolean;
  errorMessage?: string;
  item?: FileItem;
  numberPages?: number;
  supportedPdfViewer?: boolean;
  numberSavedPages?: number;
  pdfSha512?: string;
  pdfSize?: number;
  thumbnailSha512?: string;
}
export interface IFileGrid {
  cdr: ChangeDetectorRef;
}
export interface IFileUploadAdded {
  file: IFileData;
  parent: any;
  grid: IFileGrid;
}

export const SUPPORTED_OPEN_BROWSER_CONTENT_TYPES = [
  'image/png',
  'image/gif',
  'image/jpg',
  'image/jpeg',
  'image/bmp',
  'image/tiff',
  'image/tif',
  'application/pdf',
  'text/plain',
  'text/html',
  'application/xml'
];

export interface FileHolder {
  files: IFileData[];
  fileGroup?: boolean;
  busy?: boolean;
  availableLogosFormat?: string[];
}

export interface FileSelectOptions {
  holder: FileHolder;
  successMessage?: string;
  unsupportedFormat?: string;
}

export interface LinkedFileEvt {
  holder: FileHolder;
  file: IFileData;
}

export interface LocalFile {
  name: string;
  size: number;
}
export interface IDropFileHandler {
  uploader: FileUploader;
  onFileSelected: (fileList: File[] | FileList, options: FileSelectOptions, attacher: any) => void;
}
