import type { TextLongValue } from '@/core/utils/text-has-value';

export interface DialogResult {
  dialog: BnextDialogElement;
  custom: CustomDialogBase;
  inputValue: string;
  selectValue: number;
  selectText: string;
}

export interface DialogSelectorItem extends TextLongValue {
  headerName?: string;
}

export interface BnextDialogElement {
  isOpen: boolean;
  reset: boolean;
  inputValue: string;
}
export interface CustomDialogBase {
  leftButton: string;
  rightButton: string;
  message: string;
  title: string;
}
