import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import { keysObject } from '@/core/utils/object';
import { AggregateFunctionType } from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { SafeHtml } from '@angular/platform-browser';
import type {
  CellType,
  DateRange,
  GridType,
  IGridEditDoneEventArgs,
  IGridEditEventArgs,
  IGroupByRecord,
  IGroupingExpression
} from '@infragistics/igniteui-angular';
import type { FileItem } from 'ng2-file-upload';
import type { Subject, Subscription } from 'rxjs';
import type { DropdownMenuItem } from '../../dropdown-menu/dropdown-menu.interfaces';
import type { DynamicFieldDTO } from '../../dynamic-field/dynamic-field.interfaces';
import type { HierarchyValueEntity } from '../../external-catalog/utils/hierarchy.interfaces';
import { FieldHandlerType } from '../../field-display/field-display.enums';
import type { BnextComponentPath } from '../../i18n/bnext-component-path';
import type { BnextTranslateService } from '../../i18n/bnext-translate.service';
import type { GridConfigDTO } from '../../indexed-db/grid-local-config.interfaces';
import { CurrencyUtil } from '../../utils/currency-util';
import type { DataMap } from '../../utils/data-map';
import type { Icon } from '../../utils/interfaces';
import type { TextHasValue } from '../../utils/text-has-value';
import type {
  ColumnPushOrder,
  GridColumnVisibility,
  GridDropDownItem,
  GridIcon,
  GridItemMap
} from './grid-base.interfaces';
import type { GridCellType } from './grid-cell-type';
import { type GridColumn, GridSumSummary } from './grid-column';
import type { GridInfo } from './grid-data';
import { GridDataType } from './grid-data-type';
import type { ColumnRender } from './grid-render';

export function assignRenderConfig(target: any, source: any): any {
  if (typeof source === 'undefined') {
    return target;
  }
  let assignedValue = target;
  if (typeof assignedValue === 'undefined' || assignedValue === null) {
    assignedValue = {};
  }
  const keys = Object.keys(source);
  for (const key of keys) {
    if (typeof source[key] === 'undefined' || source[key] === null) {
      continue;
    }
    assignedValue[key] = source[key];
  }
  return assignedValue;
}

export interface GridBooleanIcon extends Icon {
  value: boolean;
  label: string;
  color?: string;
}

export interface RenderizableSummary<DataRowType = DataMap<any>> {
  column: GridColumn<DataRowType>;
  columnIndex: number;
  summaryResults: any;
}

export interface RenderizableColumn<DataRowType = DataMap<any>> {
  groupColumn?: boolean;
  dayColumn?: GridColumn<DataRowType>;
  value?: any;
  cell?: GridCellType<DataRowType>;
  column: GridColumn<DataRowType>;
  columnIndex: number;
  textWhenEmpty?: string;
  index?: number;
}

export interface RenderizableGroup {
  groupRow: IGroupByRecord;
  column: GridColumn;
}

export interface ICellEditValueToTextChange<DataRowType = DataMap<any>> {
  selected: Set<string | number>;
  cell: GridCellType<DataRowType>;
}

export interface ITextColumn<DataRowType = any> {
  autoSize?: boolean;
  cancel?: boolean;
  cellClasses?: any;
  cellStyles?: any;
  centered?: boolean;
  customCellEditor?: boolean;
  defaultFilterValue?: string | boolean | number | number[]; // number[]: Se usa para las columnas ITEMS y LONG_ITEMS
  editable?: boolean;
  editableClearable?: boolean;
  filterable?: boolean;
  fixedFilter?: boolean; // <-- Permite filtrar aún y cuando la columna no esté visible
  formatter?: (value: any, rowData?: any) => string | number;
  groupable?: boolean;
  groupedValueKey?: string;
  groupedValueFunction?: AggregateFunctionType;
  headerClasses?: string;
  hidden?: boolean;
  isDynamicFieldChild?: boolean;
  localize?: boolean;
  mandatoryField?: boolean;
  onlyFilter?: boolean;
  renderCell?: (cell: GridCellType<DataRowType>) => SafeHtml;
  resizable?: boolean;
  searchable?: boolean;
  showDetail?: boolean;
  sortable?: boolean;
  textWhenEmpty?: string;
  width?: string;
}
export interface GridSearchFilterChips<DataRowType = DataMap<any>> {
  column: GridColumn<DataRowType>;
  value: string | HierarchyValueEntity[];
  iconName?: 'before' | 'after';
}
export interface IObjectListColumn<DataRowType = DataMap<any>> extends ITextColumn {
  render?: IObjectListRenderColumn<DataRowType>;
  asString?: boolean;
}

export interface IDateColumn extends ITextColumn {
  dateFormat?: string;
  render?: IValueToTextRenderColumn;
}
export interface ITimeColumn extends ITextColumn {
  dateFormat?: string;
  render?: IValueToTextRenderColumn;
}

export interface IHtmlColumn<DataRowType = DataMap<any>> extends ITextColumn {
  render?: IValueToTextRenderColumn;
  renderCell: (cell: GridCellType<DataRowType>) => SafeHtml;
}

export interface IValueToTextColumn<T = DataMap<string>, DataRowType = DataMap<any>> extends ITextColumn {
  render: IValueToTextRenderColumn<T, DataRowType>;
  multipleValues?: boolean;
  fieldName: string;
  cellTextBox?: boolean;
  visibleField?: (visible: boolean, cell: GridCellType<DataRowType>) => { visible: boolean };
}

export interface IDynamicColumn extends IMultipleEntityColumn {
  dynamicColumnController: string;
}

export interface IMultipleEntityColumn extends IObjectListColumn {
  itemsController: string;
  typeDropDownValue?: GridDataType;
}

export interface IIntegerColumn extends ITextColumn {
  allowSummaryClick?: boolean;
  hasSummary?: boolean;
  summaries?: any;
  allowCellClick?: boolean;
  maxValue?: number;
  natural?: boolean;
}

export interface IDoubleColumn extends IIntegerColumn {
  decimalCount?: number; // ToDo: Hacer que los decimales funcionen
}
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ICurrencyColumn extends IDoubleColumn {}
export interface IAvatarColumn extends ITextColumn {
  avatarColorized?: boolean;
  avatarRoundShape?: boolean;
  avatarSize?: string;
  avatarIdKey?: string;
  avatarNameKey: string;
  showAvatarNameKey?: boolean;
  avatarSecundaryKey?: string;
  allowManyAvatar?: boolean;
}

export interface ISwitchColumn<DataRowType = DataMap<any>> extends ITextColumn {
  checked: boolean;
  cell?: GridCellType<DataRowType>;
}

export interface ITooltipColumn extends ITextColumn {
  tooltip: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ILongColumn extends IIntegerColumn {}
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IPercentageColumn extends IIntegerColumn {}

export interface IYesNoColumn extends ITextColumn {
  boolean?: boolean;
}

export interface IDropDownColumn<DataRowType = DataMap<any>> extends ITextColumn {
  actionCountLimit?: number;
  alwaysVisible?: boolean;
  render: IDropDownRenderColumn<DataRowType>;
}

export interface IIconBooleanColumn extends ITextColumn {
  render: IIconBooleanRenderColumn;
}

export interface IIconColumn<DataRowType = DataMap<any>> extends ITextColumn {
  render: IIconRenderColumn<DataRowType>;
}

export interface ITimeHumanizeColumn extends ITextColumn {
  locale: string;
}
export interface IGridDayColumn extends ITextColumn {
  day: Date;
  header: string;
}

export interface IIconRenderColumn<DataRowType = DataMap<any>> {
  icons?: GridIcon[];
  isDefaultIconStatus?: boolean;
  isDefaultTextStatus?: boolean;
  callback?: (cell: GridCellType<DataRowType>) => void;
  getCustomIconTitle?: <ColumnType extends GridColumn>(dataRow: any, column: ColumnType, iconValue: number | string, iconLabel: string) => string;
}

export interface IIconBooleanRenderColumn {
  icons?: GridBooleanIcon[];
  isDefaultIconStatus?: boolean;
  isDefaultTextStatus?: boolean;
  callback?: (cell: CellType) => void;
}

export interface ILinkColumn extends ITextColumn {
  linkHref: string;
  linkParams: string[];
  target?: string;
}

export interface IValueToTextRenderColumn<T = DataMap<string>, DataRowType = DataMap<any>> {
  valueToText?: boolean;
  dateToText?: boolean;
  textToHtml?: boolean;
  skipTranslate?: boolean;
  labelValues?: () => T;

  customFunction?: Function;
  blacklistValues?: (cell: GridCellType<DataRowType>) => Set<number | string>;
  iconName?: string; // <-- Icono mostrado en el campo para editar
}

export interface IDropDownRenderColumn<DataRowType = DataMap<any>> {
  menuOptions: (cell: GridCellType<DataRowType>) => DropdownMenuItem[];
  toggleMenuAction?: (drop: GridDropDownItem) => void;
  rowIdentifierKey: string; // <--- El `key` de la llave primaria del renglón
  toggleButtonTitle?: string;
}

export interface IObjectListRenderColumn<DataRowType = DataMap<any>> {
  fieldKey?: string;
  valueKey?: string;
  isDefaultIconStatus?: boolean;
  isDefaultTextStatus?: boolean;
  textWithAction?: boolean;
  callback?: (cell: GridCellType<DataRowType>) => void;
  labelKey?: string;
  skipTranslate?: boolean;
  items?: any[];
  itemsMap?: GridItemMap;
  isDynamicColumn?: boolean;
  warnMissingItems?: boolean;
}

export class TextColumn<DataRowType = any> implements ITextColumn {
  cancel = false;
  cellClasses = '';
  cellStyles = null;
  centered = false;
  defaultFilterValue = null;
  editable = false;
  filterable = true;
  fixedFilter = false;
  groupable = true;
  headerClasses = '';
  hidden = false;
  localize = false;
  resizable = true;
  searchable = true;
  sortable = true;
  textWhenEmpty: string;
  type = GridDataType.TEXT;
  width = '150px';
  formatter = null;
  showDetail = false;
  renderCell = (cell: GridCellType<DataRowType>): SafeHtml => {
    return cell.value;
  };

  public constructor(options?: ITextColumn) {
    if (typeof options === 'undefined') {
      return;
    }
    for (const key of keysObject(options)) {
      if (typeof options[key] !== 'undefined') {
        this[key] = options[key];
      }
    }
    if (typeof options.renderCell !== 'undefined') {
      this.renderCell = options.renderCell;
    }
    if (options.autoSize === true) {
      this.width = null;
    } else {
      this.width = options.width || this.width || '150px';
    }
  }
}
export class HtmlColumn<DataRowType = DataMap<any>> extends TextColumn<DataRowType> implements IHtmlColumn<DataRowType> {
  type = GridDataType.HTML;
  skipExportFormatter = true;
  searchable = false;
  sortable = false;
  filterable = false;
  render = { textToHtml: true };
  renderCell = (cell: GridCellType<DataRowType>): SafeHtml => {
    return cell.value;
  };
  public constructor(options?: IHtmlColumn<DataRowType>) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.renderCell !== 'undefined') {
      this.renderCell = options.renderCell;
    }
    if (typeof options.formatter !== 'undefined') {
      // El formtatter es necesario para que los datos se exporten a EXCEL
      this.formatter = options.formatter;
    }
  }
}

export class DateColumn extends TextColumn implements IDateColumn {
  // Format to do back-end filtering: Since grid-search.component.html:#dateSearch is always a date without time,
  // no need to use the defined dateFormat (could include time) for filtering (grid-filtering.util.ts:193).
  // Back-end specs a date in ES format.
  searchDateFormat = 'DD/MM/YYYY';
  // Format of the date
  dateFormat = 'DD/MM/YYYY';
  type = GridDataType.DATE;
  render = { dateToText: true };
  skipExportFormatter = true;
  formatter = (value: any, _rowData?: any): string | number => {
    if (value === null || typeof value === 'undefined') {
      return null;
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return DateUtil.format(value, this.dateFormat || 'DD/MM/YYYY');
  };
  public constructor(options?: IDateColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.dateFormat !== 'undefined') {
      this.dateFormat = options.dateFormat;
    }
  }
}

export class TimeColumn extends TextColumn implements ITimeColumn {
  dateFormat = 'HH:mm:ss';
  type = GridDataType.TIME;
  render = { dateToText: true };
  skipExportFormatter = false; // Para las columnas de tipo Time se mantiene el formato en el export
  formatter = (value: any, _rowData?: any): string | number => {
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return DateUtil.format(value, this.dateFormat || 'HH:mm:ss');
  };
  public constructor(options?: ITimeColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.dateFormat !== 'undefined') {
      this.dateFormat = options.dateFormat;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class HiddenColumn extends TextColumn implements ITextColumn {
  hidden = true;
}

export class TimestampColumn extends TextColumn implements IDateColumn {
  dateFormat = 'DD/MM/YYYY H:mm:ss';
  type = GridDataType.TIMESTAMP;
  skipExportFormatter = true;
  render = { dateToText: true };
  formatter = (value: any, _rowData?: any): string | number => {
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return DateUtil.format(value, this.dateFormat || 'DD/MM/YYYY H:mm:ss');
  };
  public constructor(options?: IDateColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.dateFormat !== 'undefined') {
      this.dateFormat = options.dateFormat;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class ValueToTextColumn<T = DataMap<string>, DataRowType = DataMap<any>> extends TextColumn implements IValueToTextColumn<T, DataRowType> {
  render: IValueToTextRenderColumn<T, DataRowType>;
  filterable = false;
  groupable = false;
  searchable = false;
  sortable = false;
  multipleValues = false;
  fieldName = null;
  cellTextBox = false;
  customCellEditor = false;

  customFunction: Function = null;

  public constructor(options?: IValueToTextColumn<T, DataRowType>) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    this.fieldName = options.fieldName;
    this.textWhenEmpty = options.textWhenEmpty;
    this.render = assignRenderConfig(this.render, options.render);
    this.multipleValues = options.multipleValues || false;
    this.cellTextBox = options.cellTextBox;
    if (options.render) {
      this.render.valueToText = true;
    }
    if (options.editable) {
      this.customCellEditor = true;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    } else {
      this.formatter = (value: Set<number>, _row): string => {
        if (value && this.fieldName) {
          const labelValues = this.render.labelValues();
          return Array.from(value)
            .map((val: number) => labelValues[this.fieldName][`${val}`])
            .join(',');
        }
        return '';
      };
    }
  }
}

export class YesNoColumn extends TextColumn implements IYesNoColumn {
  type = GridDataType.NUMBER_BOOLEAN;
  boolean = false;

  public constructor(options?: IYesNoColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (options?.boolean) {
      this.type = GridDataType.BOOLEAN;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class IntegerColumn extends TextColumn implements IIntegerColumn {
  allowSummaryClick = false;
  allowCellClick = false;
  hasSummary = false;
  summaries = GridSumSummary;
  type = GridDataType.INTEGER;
  formatter = (value: any, _rowData?: any): string | number => {
    if (NumberUtil.isNumber(value)) {
      if (typeof value === 'string') {
        value = Number(value);
      }
      return `${value}`;
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return null;
  };

  public constructor(options?: IIntegerColumn) {
    if (typeof options !== 'undefined') {
      if (typeof options.natural === 'undefined') {
        options.natural = true;
      }
    }
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.allowSummaryClick !== 'undefined') {
      this.allowSummaryClick = options.allowSummaryClick || this.allowSummaryClick;
    }
    if (typeof options.hasSummary !== 'undefined') {
      this.hasSummary = options.hasSummary || this.hasSummary;
    }
    if (typeof options.summaries !== 'undefined') {
      this.summaries = options.summaries || this.summaries;
    }
    if (typeof options.allowCellClick !== 'undefined') {
      this.allowCellClick = options.allowCellClick || this.allowCellClick;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}
export class PercentageColumn extends IntegerColumn implements IPercentageColumn {
  type = GridDataType.PERCENTAGE;
  formatter = (value: any, _rowData?: any): string | number => {
    if (NumberUtil.isNumber(value)) {
      return `${NumberUtil.round(value, 2)}%`;
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return this.textWhenEmpty || '';
  };
}
export class AvatarColumn extends TextColumn implements IAvatarColumn {
  avatar = true;
  avatarColorized = true;
  avatarRoundShape = true;
  avatarSize = 'small';
  avatarIdKey = null;
  avatarNameKey = null;
  showAvatarNameKey = null;
  avatarSecundaryKey = null;

  formatter = (value: any, rowData?: any): string | number => {
    if (rowData === null || typeof rowData === 'undefined') {
      return value ?? '';
    }
    value = '';
    if (this.avatarNameKey != null && typeof this.avatarNameKey !== 'undefined') {
      const name = rowData[this.avatarNameKey];
      if (name != null && typeof name !== 'undefined') {
        value += name;
      }
    }
    if (this.avatarSecundaryKey !== null && typeof this.avatarSecundaryKey !== 'undefined') {
      if (value !== '') {
        value += ' , ';
      }
      const secondary = rowData[this.avatarSecundaryKey];
      if (secondary != null && typeof secondary !== 'undefined') {
        value += secondary;
      }
    }
    return value;
  };

  public constructor(options: IAvatarColumn) {
    super(options);
    if (typeof options.avatarColorized !== 'undefined') {
      this.avatarColorized = options.avatarColorized || this.avatarColorized;
    }
    if (typeof options.avatarRoundShape !== 'undefined') {
      this.avatarRoundShape = options.avatarRoundShape || this.avatarRoundShape;
    }
    if (typeof options.avatarSize !== 'undefined') {
      this.avatarSize = options.avatarSize || this.avatarSize;
    }
    if (typeof options.avatarIdKey !== 'undefined') {
      this.avatarIdKey = options.avatarIdKey || this.avatarIdKey;
    }
    if (typeof options.avatarNameKey !== 'undefined') {
      this.avatarNameKey = options.avatarNameKey || this.avatarNameKey;
    }
    if (typeof options.showAvatarNameKey !== 'undefined') {
      this.showAvatarNameKey = options.showAvatarNameKey || this.showAvatarNameKey;
    }
    if (typeof options.avatarSecundaryKey !== 'undefined') {
      this.avatarSecundaryKey = options.avatarSecundaryKey || this.avatarSecundaryKey;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class SwitchColumn extends TextColumn implements ISwitchColumn {
  type = GridDataType.NUMBER_BOOLEAN;
  checked = false;
  switchPicker = true;
  public constructor(options: ISwitchColumn) {
    super(options);
    if (typeof options.checked !== 'undefined') {
      this.checked = options.checked || this.checked;
    }
  }
}

export class TooltipColumn extends TextColumn implements ITooltipColumn {
  tooltip = true;
}

export class LongColumn extends IntegerColumn implements ILongColumn {
  type = GridDataType.LONG;

  public constructor(options?: ILongColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class DoubleColumn extends IntegerColumn implements IDoubleColumn {
  decimalCount = 2;
  type = GridDataType.DOUBLE;
  formatter = (value: any, _rowData?: any): string | number => {
    if (NumberUtil.isNumber(value)) {
      if (typeof value === 'string') {
        value = Number(value);
      }
      return Number(NumberUtil.round(value, this.decimalCount).toFixed(this.decimalCount));
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    if (this.textWhenEmpty !== null && typeof this.textWhenEmpty !== 'undefined') {
      return this.textWhenEmpty;
    }
    return null;
  };

  public constructor(options?: IDoubleColumn) {
    if (typeof options !== 'undefined') {
      options.natural = false;
    }
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.decimalCount !== 'undefined') {
      this.decimalCount = options.decimalCount || this.decimalCount;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class CurrencyColumn extends DoubleColumn implements ICurrencyColumn {
  decimalCount = 2;
  type = GridDataType.DOUBLE;
  formatter = (value: any, _rowData?: any): string | number => {
    if (NumberUtil.isNumber(value)) {
      if (typeof value === 'string') {
        value = Number(value);
      }
      const numberValue = CurrencyUtil.formatCurrency(NumberUtil.round(value), true);
      return `\$${numberValue}`;
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    if (this.textWhenEmpty !== null && typeof this.textWhenEmpty !== 'undefined') {
      return this.textWhenEmpty;
    }
    return null;
  };
}

export class IconColumn<DataRowType = DataMap<any>> extends TextColumn implements IIconColumn<DataRowType> {
  render: IIconRenderColumn<DataRowType>;
  type = GridDataType.ICONS;
  centered = true;
  localize = true;
  customCellEditor = true;

  public constructor(options: IIconColumn<DataRowType>) {
    super(options);
    this.render = assignRenderConfig(this.render, options.render);
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class IconBooleanColumn extends TextColumn implements IIconBooleanColumn {
  render: IIconBooleanRenderColumn;
  type = GridDataType.BOOLEAN_ICONS;
  centered = true;
  localize = true;

  public constructor(options: IIconBooleanColumn) {
    super(options);
    this.render = assignRenderConfig(this.render, options.render);
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class DropDownColumn<DataRowType = DataMap<any>> extends TextColumn implements IDropDownColumn<DataRowType> {
  cancel = true;
  filterable = false;
  selectable = false;
  render: IDropDownRenderColumn<DataRowType>;
  searchable = false;
  dropdownActionLimit = 3;
  dropdownAlwaysVisible = true;
  sortable = false;
  type = GridDataType.DROP_DOWN;
  declare width: '115px';

  public constructor(options: IDropDownColumn<DataRowType>) {
    options.groupable = false;
    super(options);
    this.render = assignRenderConfig(this.render, options.render);
    if (typeof options.actionCountLimit !== 'undefined') {
      this.dropdownActionLimit = options.actionCountLimit;
    }
    if (typeof options.alwaysVisible !== 'undefined') {
      this.dropdownAlwaysVisible = options.alwaysVisible;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export class ObjectListColumn<DataRowType = DataMap<any>> extends TextColumn implements IObjectListColumn<DataRowType> {
  render: IObjectListRenderColumn<DataRowType>;
  type = GridDataType.ITEMS;

  public constructor(options: IObjectListColumn<DataRowType>) {
    super(options);
    if (
      options.render !== null &&
      typeof options.render !== 'undefined' &&
      (options.render.skipTranslate === null || typeof options.render.skipTranslate === 'undefined')
    ) {
      options.render.skipTranslate = true;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
    this.render = assignRenderConfig(this.render, options.render);
  }
}

export class TextListColumn<DataRowType = DataMap<any>> extends ObjectListColumn<DataRowType> implements IObjectListColumn<DataRowType> {
  type = GridDataType.TEXT_ITEMS;
}

export class MultipleEntityColumn extends TextColumn implements IMultipleEntityColumn {
  itemsController = null;
  render: IObjectListRenderColumn = {
    valueKey: 'value',
    labelKey: 'text',
    skipTranslate: true,
    items: [],
    itemsMap: {}
  };
  type = GridDataType.LONG_ITEMS;

  public constructor(options: IMultipleEntityColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.typeDropDownValue !== 'undefined') {
      this.type = options.typeDropDownValue || GridDataType.LONG_ITEMS;
    }
    if (typeof options.itemsController !== 'undefined') {
      this.itemsController = options.itemsController || null;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
    if (typeof options.render !== 'undefined') {
      const keys = Object.keys(options.render);
      for (const key of keys) {
        if (typeof options.render[key] !== 'undefined' && options.render[key] !== null) {
          this.render[key] = options.render[key];
        }
      }
    }
    if (!this.itemsController) {
      throw Error(`Invalid MultipleEntityColumn declaration, 'itemsController' value is missing.`);
    }
  }
}

export class DynamicColumn extends MultipleEntityColumn implements IDynamicColumn {
  dynamicColumnController = null;
  public constructor(options: IDynamicColumn) {
    super(options);
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.dynamicColumnController !== 'undefined') {
      this.dynamicColumnController = options.dynamicColumnController || null;
    }
    if (!this.dynamicColumnController) {
      throw Error(`Invalid dynamicColumn declaration, 'dynamicColumnController' value is missing.`);
    }
  }
}

export class TimeHumanizeColumn extends TextColumn implements ITimeHumanizeColumn {
  type = GridDataType.INTEGER;
  locale: string;
  formatter = (value: any, _rowData?: any): string | number => {
    if (value === null || typeof value === 'undefined') {
      return null;
    }
    if (value && value.value !== null && typeof value.value !== 'undefined') {
      return value;
    }
    return DateUtil.formatHumanize(value, this.locale);
  };

  public constructor(options?: ITimeHumanizeColumn) {
    super(options);
    this.locale = options.locale;
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}
/**
 * TODO: Implementar funcionalidad similar al método RenderMenuLink del archivo gridComponentUtil.js
 * para poder configurar múltiples parámetros en la URL y en el texto de ayuda del link y texto a desplegar en la celda.
 */
export class LinkColumn extends TextColumn implements ILinkColumn {
  renderAsLink = true;
  linkHref = '';
  linkParams = [];
  target = '';
  public constructor(options: ILinkColumn) {
    super(options);
    if (typeof options.linkHref !== 'undefined') {
      this.linkHref = options.linkHref;
    }
    if (typeof options.linkParams !== 'undefined') {
      this.linkParams = options.linkParams;
    }
    if (typeof options.target !== 'undefined') {
      this.target = options.target;
    }
  }
}

export class GridDayColumn extends TextColumn implements IGridDayColumn {
  width = '150px';
  header: string;
  day: Date;
  filterable = false;
  hasSummary = false;
  rangeColumn = true;
  hidden = false;
  resizable = true;
  sortable = false;
  pinned = false;
  formatter = (_value: any, _rowData?: any): string | number => '-';

  public constructor(options: IGridDayColumn) {
    super(options);
    this.header = options.header;
    this.day = options.day;
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
}

export type GridColumnBuilder = TextColumn | DateColumn | IconColumn | DropDownColumn | ObjectListColumn | TimestampColumn | HtmlColumn;

export interface IGridColumnUtil {
  push: (
    id: string,
    label?: string | /* LABEL || WIDTH */ GridColumnBuilder /* OPTIONS */,
    options?: string | /* WIDTH */ GridColumnBuilder /* OPTIONS */ | GridColumnVisibility
  ) => IGridColumnUtil;
  /**
   * Se debe llamar antes que `push`, a continuacion cualquier `id` de columna
   * que contenga el arreglo excluido no sera agregado a `columns`.
   */
  excluded: (excluded: string[]) => IGridColumnUtil;
  removeLast: () => IGridColumnUtil;
  order: (order: ColumnPushOrder) => IGridColumnUtil;
  /**
   * Este metodo tiene un memory leak potencial en el arreglo estatico `subscriptionArray`.
   *
   * @deprecated En su lugar, utilizar `GridUtil.translate(...)`
   */
  subs: (subscriptions: Subscription[]) => IGridColumnUtil;
  option: (key: string, value: any) => IGridColumnUtil;
  applyDefaultFormatter: <DataRowType = DataMap<any>>(column: GridColumn<DataRowType>) => void;
  lang: (i18n: DataMap<string | object>) => IGridColumnUtil;
  /**
   * Este metodo tiene un memory leak potencial en el arreglo estatico `subscriptionArray`.
   *
   * @deprecated En su lugar, utilizar `GridUtil.translate(...)`
   */
  langAsync: (translateService: BnextTranslateService, LangConfig: BnextComponentPath, key: string | string[], removeSuperObjects?: boolean) => IGridColumnUtil;
  /**
   * ¡Solo requiere ser llamado 1 vez por arreglo de columnas!
   *
   * Cumple la misma función que `langAsync`, pero sin memoryleak. Traduce
   * etiquetas en base a al ID de cada columna en `GridUtil.columns(...)`.
   */
  translate: (
    $destroy: Subject<any>,
    translateService: BnextTranslateService,
    LangConfig: BnextComponentPath,
    key?: string | string[],
    removeSuperObjects?: boolean
  ) => IGridColumnUtil;
}

export interface ActionStrip {
  dropdownAlwaysVisible: boolean;
  dropdownActionLimit: number;
  render: ColumnRender;
}

export interface GridEditDoneEventArgs<DataRowType = DataMap<any>> extends IGridEditDoneEventArgs, IGridEditEventArgs {
  rowData: DataRowType;
}

export interface GridOrderBy {
  orderByMany: GridOrderBy[];
  orderBy: string;
  direction: number;
  type?: 'HQL' | 'SQL';
}

export interface GridDataLoadedEvent<DataRowType = any> {
  data: DataRowType[];
  grid: GridType;
}

export interface GridDynamicFieldsLoadedEvent {
  dynamicSearchColumns: GridColumn<any>[];
  grid: GridType;
}

export interface GridShowMore {
  parentId: number;
  showMoreAvailable: boolean;
  currentPage: number;
  noopPageIds: number[];
}
export interface GridFilter {
  asumeAlias: boolean;
  columns: DataMap;
  criteria: DataMap;
  direction: number;
  dynamicFieldCriteria: DataMap;
  dynamicSearchEnabled: boolean;
  enableStatistics: boolean;
  field: GridOrderBy;
  filterInPatch: boolean;
  gridId: string;
  gridShowMore: GridShowMore;
  likeCriteria: DataMap;
  likeOrSentence: boolean;
  lowerLimit: DataMap;
  options: DataMap;
  page: number;
  pageSize: number;
  statisticsFields: string[];
  upperLimit: DataMap;
  windowPath: string;
  groupFields?: IGroupingExpression[]; // Campos agrupados - contiene las groupExpressions (Parche)
  searchStringChainIds?: boolean;
  masterId?: string;
}

export interface GridSelectOption {
  value: number;
  label: string;
}

export type GridColumnMap = Record<string, GridColumn>;

export interface FileEntity {
  id?: number;
  description: string;
  lastModifiedBy?: string;
  lastModifiedDate?: Date;
  item?: FileItem;
  isCancel?: boolean;
  isError?: boolean;
  maximized?: boolean;
  stage?: string;
  extension?: string;
  commentFile?: string;
}

export interface CustomCellItem {
  value: number;
  label?: string;
  color: string;
}

export interface GridFiltersExport {
  filter: GridFilter;
  url: string;
}
export interface GridExportResult {
  fileDownloaded: boolean;
  emptySelection: boolean;
}

export interface IDynamicColumnChild extends ITextColumn {
  dynamicFieldType: string | FieldHandlerType;
}
/**
 * Se declara aquí por que solo es para uso interno de `grid.component.ts`
 */
export class DynamicColumnChild extends TextColumn implements IDynamicColumnChild {
  dynamicFieldType = FieldHandlerType.TEXT;
  isDynamicFieldChild = true;
  render: IObjectListRenderColumn = null;
  renderAsMarkdownText = false;
  renderAsLink = false;
  linkHref = null;
  public constructor(args: DynamicFieldDTO, options?: IDynamicColumnChild) {
    super(options);
    if (typeof args === 'undefined') {
      throw Error(`Invalid dynamicColumnChild declaration, 'dynamicFieldType' value is missing.`);
    }
    switch (args.type) {
      case FieldHandlerType.VARCHAR_MAX:
      case FieldHandlerType.HTML:
      case FieldHandlerType.BIG_TEXT:
      case FieldHandlerType.TEXT:
        this.type = GridDataType.TEXT;
        break;
      case FieldHandlerType.CATALOG_MULTIPLE:
        this.type = GridDataType.TEXT_MULTIPLE_ITEMS;
        this.render = this.getRender(args);
        break;
      case FieldHandlerType.CATALOG:
        this.type = GridDataType.TEXT_ITEMS;
        this.render = this.getRender(args);
        break;
      case FieldHandlerType.LINK:
        this.renderAsLink = true;
        this.linkHref = null;
        break;
      case FieldHandlerType.MARKDOWN:
        this.type = GridDataType.TEXT;
        this.renderAsMarkdownText = true;
        break;
      default:
        throw Error(`Unsupported dynamicFieldType: ${options.dynamicFieldType}`);
    }
    if (typeof options === 'undefined') {
      return;
    }
    if (typeof options.formatter !== 'undefined') {
      this.formatter = options.formatter;
    }
  }
  private getRender(field: DynamicFieldDTO): IObjectListRenderColumn {
    const options: TextHasValue[] = field.options?.map((value) => ({ text: value, value: value })) || [];
    const optionsMap: GridItemMap = {};
    for (const option of options) {
      optionsMap[`${option.value}`] = option.text;
    }
    return {
      skipTranslate: true,
      valueKey: 'value',
      labelKey: 'text',
      items: options,
      itemsMap: optionsMap,
      isDynamicColumn: true,
      warnMissingItems: false
    };
  }
}

export interface GridNormalizeConfig<DataRowType = DataMap<any>> {
  gridInfo: GridInfo<DataRowType>;
  columns: GridColumn<DataRowType>[];
  range: DateRange;
  rangeSelectorColumns: GridColumn<DataRowType>[];
  rangeSelectorDataKey: string;
  localTimeForDates: boolean;
  rangeSelectorDataColumnKey: string;
  childDataKey: string;
  allowShowMoreChilds: boolean;
  allowRangeSelectorColumns: boolean;
}

export interface GridImportData<DataRowType = DataMap<any>> {
  keys: string[];
  errors: GridImportedError[];
  rows: DataRowType[];
}

export interface GridImportedError {
  error: string;
  row: number;
  column: string;
  value?: any;
}

export interface AvatarUserInfo {
  avatarName: string;
  avatarId: string;
}

export interface GridStateDTO {
  gridId: string;
  gridConfig: GridConfigDTO;
}
