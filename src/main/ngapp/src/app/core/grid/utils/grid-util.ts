import { GridInfoStatus } from '@/core/grid/utils/grid.enums';
import * as DateUtil from '@/core/utils/date-util';
import { EnumUtil } from '@/core/utils/enum-util';
import { cloneObject, isObject, keysObject } from '@/core/utils/object';
import type { DateRange } from '@infragistics/igniteui-angular';
import type { Observer, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import type { BnextComponentPath } from '../../i18n/bnext-component-path';
import type { BnextTranslateService } from '../../i18n/bnext-translate.service';
import type { DataMap } from '../../utils/data-map';
import { ColumnRenderFilter } from './column-render-filter';
import { ColumnPushOrder, GridColumnVisibility } from './grid-base.interfaces';
import type { GridColumn } from './grid-column';
import { GridDataType } from './grid-data-type';
import { type GridColumnBuilder, type GridNormalizeConfig, type IGridColumnUtil, TextColumn } from './grid.interfaces';

/**
 * Ejemplos de uso:
 *
 *  1) Agregando el valor de i18n, así:
 *
 *          columns(this.columns).lang(this.i18n)
 *                  .push('code')
 *                  .push('description')
 *
 *  2) Enviando la etiqueta directamente, así:
 *
 *          columns(this.columns)
 *                  .push('code', 'Clave')
 *                  .push('description', 'Descripción')
 *
 *  3) Pidiendo las etiquetas asincronamente, así:
 *
 *          columns(this.columns)
 *                  .subs(this.subs)
 *                  .push('code')
 *                  .push('description')
 *                  .langAsync(this.translateService, LangConfig, 'activities.column')
 *
 */
export const MAX_COLUMN_DAYS = 31 as const;
export const GRID_INFO_STATUS_SUCCESS = EnumUtil.getObject(GridInfoStatus, GridInfoStatus.SUCCESS);
export const LANG_CONFIG: BnextComponentPath = {
  componentPath: 'core',
  componentName: 'grid'
};

function defaultItemsFormatter<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, val: any, _rowData: DataRowType): string {
  const columnRender = column.render;
  if (!columnRender) {
    return '';
  }
  if (columnRender.itemsMap) {
    if (typeof val === 'undefined' || val === null || val === '') {
      return '';
    }
    const item = columnRender.itemsMap[val];
    if (item) {
      return item[columnRender.labelKey];
    }
    return val;
  }
  if (columnRender.isEdition) {
    return '';
  }
  return val;
}

function defaultIconsFormatter<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, val: any, rowData: DataRowType): string {
  const columnRender = column.render;
  if (!columnRender) {
    return '';
  }
  if (columnRender.iconsMap) {
    if (typeof val === 'undefined' || val === null || val === '') {
      return '';
    }
    const icon = columnRender.iconsMap[val];
    if (icon) {
      const iconLabel = icon[columnRender.labelKey];
      if (typeof columnRender.getCustomIconTitle === 'function') {
        return columnRender.getCustomIconTitle(rowData, column as GridColumn, val, iconLabel);
      }
      return iconLabel;
    }
  }
  if (columnRender.isEdition) {
    return '';
  }
  return null;
}

function defaultMultipleItemsFormatter<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, val: any, rowData: DataRowType): string {
  if (val == null || typeof val === 'undefined' || val === '') {
    return '';
  }
  const values = val.split(',');
  return values
    .filter((item: any) => item !== null && typeof item !== 'undefined')
    .map((item: string) => defaultItemsFormatter(column, item.trim(), rowData))
    .join(', ');
}

function defaultFormatter<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, val: any, rowData: DataRowType): string {
  switch (column.type) {
    case GridDataType.DROP_DOWN:
      return null;
    case GridDataType.ICONS:
    case GridDataType.BOOLEAN_ICONS:
      return defaultIconsFormatter(column, val, rowData);
    case GridDataType.ITEMS:
    case GridDataType.LONG_ITEMS:
    case GridDataType.TEXT_ITEMS:
      return defaultItemsFormatter(column, val, rowData);
    case GridDataType.TEXT_MULTIPLE_ITEMS:
      return defaultMultipleItemsFormatter(column, val, rowData);
    case GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS:
      return val;
    default:
      if (!column.render) {
        return '';
      }
      if (column.render.itemsMap) {
        return defaultItemsFormatter(column, val, rowData);
      }
      if (column.render.iconsMap) {
        return defaultIconsFormatter(column, val, rowData);
      }
      if (column.render.isEdition) {
        return '';
      }
      return null;
  }
}

function setDefaultData<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>): void {
  if (!column.render && (column.type === GridDataType.NUMBER_BOOLEAN || column.type === GridDataType.BOOLEAN || column.type === GridDataType.DROP_DOWN)) {
    column.render = {};
  }
  const render = column.render;
  if (!render) {
    return;
  }
  column.custom = true;
  if (!column.formatter) {
    column.formatter = (value: any, rowData?: DataRowType) => defaultFormatter(column, value, rowData);
  }
  if (!render.valueKey) {
    render.valueKey = 'value';
  }
  if (!render.labelKey) {
    render.labelKey = 'label';
  }
  if (render.isDefaultIconStatus) {
    if (!render.icons || render.icons.length === 0) {
      render.icons = [
        { value: 1, color: null, label: 'active', name: 'cube_green', family: 'bnext-icons' },
        { value: 0, color: null, label: 'inactive', name: 'cube_gray', family: 'bnext-icons' }
      ];
    }
  }
  if (render.isDefaultTextStatus) {
    if (!render.items || render.items.length === 0) {
      render.items = [
        { value: 1, color: null, label: 'active' },
        { value: 0, color: null, label: 'inactive' }
      ];
    }
  }
  if (render.isEdition) {
    render.action = {};
    render.action.icon = 'edit';
    column.sortable = false;
    column.filterable = false;
    column.disableHiding = true;
  }
  if (render.hasDelete) {
    render.action = {};
    render.action.icon = 'delete';
    column.sortable = false;
    column.filterable = false;
    column.disableHiding = true;
  }
  if (!render.fieldKey) {
    render.fieldKey = column.field;
  }
  if (column.type === GridDataType.NUMBER_BOOLEAN) {
    if (!render.items || render.items.length === 0) {
      const item1 = {};
      item1[column.render.valueKey] = 1;
      item1[column.render.labelKey] = 'yes';
      const item0 = {};
      item0[column.render.valueKey] = 0;
      item0[column.render.labelKey] = 'no';
      render.items = [item1, item0];
    }
  } else if (column.type === GridDataType.BOOLEAN) {
    if (!render.items || render.items.length === 0) {
      const item1 = {};
      item1[column.render.valueKey] = true;
      item1[column.render.labelKey] = 'yes';
      const item0 = {};
      item0[column.render.valueKey] = false;
      item0[column.render.labelKey] = 'no';
      render.items = [item1, item0];
    }
  } else if (column.type === GridDataType.DROP_DOWN) {
    render.isDropDownMenu = true;
  }
  setRenderIconsCache(column);
  setRenderItemsCache(column);
}

export function setRenderItemsCache<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>): void {
  const render = column.render;
  if (!(render.items && render.items.length > 0)) {
    return;
  }
  render.items = [...render.items];
  const itemsMap = {};
  render.itemsMap = itemsMap;
  for (const element of render.items) {
    const itemKey = element[render.valueKey];
    itemsMap[itemKey] = element;
  }
}

export function setRenderIconsCache<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>): void {
  const render = column.render;
  if (!render) {
    return;
  }
  if (render.icons && render.icons.length > 0) {
    render.icons = [...render.icons];
    render.iconsMap = {};
    for (const element of render.icons) {
      render.iconsMap[element[render.valueKey]] = element;
    }
  }
}

function setDefaultStatusTags<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, i18n: DataMap<string>): void {
  const render = column.render;
  if (!render) {
    return;
  }
  if (render.isDefaultIconStatus) {
    render.icons[0][column.render.labelKey] = i18n.active || 'active';
    render.icons[1][column.render.labelKey] = i18n.inactive || 'inactive';
    setRenderIconsCache(column);
  }
  if (render.isDefaultTextStatus) {
    render.items[0][column.render.labelKey] = i18n.active || 'active';
    render.items[1][column.render.labelKey] = i18n.inactive || 'inactive';
    setRenderItemsCache(column);
  }
}

function setDefaultNumberBooleanTags<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, i18n: DataMap<string>): void {
  const render = column.render;
  if (!render) {
    return;
  }
  if (column.type === GridDataType.NUMBER_BOOLEAN || column.type === GridDataType.BOOLEAN) {
    render.items[0][column.render.labelKey] = i18n['1'] || 'yes';
    render.items[1][column.render.labelKey] = i18n['0'] || 'no';
    setRenderItemsCache(column);
  }
}

function normalizeHeader(header: string): string {
  if (header === null || typeof header === 'undefined' || header === '') {
    return header;
  }
  return header.trim();
}

export function columns<DataRowType = DataMap<any>>(
  ...gridDefs: GridColumn<DataRowType>[][]
): IGridColumnUtil {
  let i18nMap = {};
  let subscriptionArray: Subscription[] = null;
  let pushOrder = ColumnPushOrder.LAST;
  let excludedIds = [];
  const setHeaders = (column: GridColumn<DataRowType>, i18n: DataMap<string | DataMap<string>>) => {
    if (typeof i18n[column.field] === 'string') {
      column.header = normalizeHeader((i18n[column.field] as string) || column.field);
      if (column.rangeColumns) {
        for (const c1 of column.rangeColumns) {
          c1.header = normalizeHeader(column.header);
        }
      }
      return;
    }
    if (i18n[column.field] && typeof i18n[column.field] === 'object') {
      column.header = normalizeHeader((i18n[column.field] as DataMap<string>).header || column.field);
      if (column.rangeColumns) {
        for (const c1 of column.rangeColumns) {
          c1.header = normalizeHeader(column.header);
        }
      }
    }
    const render = column.render;
    if (render) {
      if (!column.filters) {
        column.filters = new ColumnRenderFilter(column);
      }
      if (render.items && !render.skipTranslate) {
        for (const item of render.items) {
          const labelKey = item[render.labelKey];
          const langColumn = i18n[column.field] as DataMap<string>;
          const tag = langColumn?.items?.[labelKey] || `${column.field}.items.${labelKey}`;
          item[render.labelKey] = tag;
          const itemKey = item[render.valueKey];
          render.itemsMap[itemKey][render.labelKey] = tag ?? '';
        }
      }
      if (render.icons && render.localize) {
        for (const icon of render.icons) {
          const langColumn = i18n[column.field] as DataMap<string>;
          const labelKeyValue = icon[render.labelKey];
          const tag = langColumn?.icons?.[labelKeyValue] || `${column.field}.icons.${labelKeyValue}`;
          icon[render.labelKey] = tag;
          render.iconsMap[icon[render.valueKey]][render.labelKey] = tag;
        }
      }
      if (render.action) {
        render.action.label = (i18n[column.field] as DataMap<string>)?.action || `${column.field}.action`;
      }
    }
  };
  function isGridColumnVisibility(value: any) {
    switch (value) {
      case GridColumnVisibility.HIDDEN:
      case GridColumnVisibility.VISIBLE:
        return true;
    }
    return false;
  }
  const self: IGridColumnUtil = {
    /**
     * Este metodo tiene un memory leak potencial en el arreglo estatico `subscriptionArray`.
     *
     * @deprecated En su lugar, utilizar `translate`
     *
     **/
    subs: (subscriptions: Subscription[]) => {
      subscriptionArray = subscriptions;
      return self;
    },
    excluded: (_excludedIds: string[]) => {
      excludedIds = _excludedIds;
      return self;
    },
    order: (order: ColumnPushOrder) => {
      pushOrder = order;
      return self;
    },
    removeLast: () => {
      for (const c of gridDefs) {
        if (pushOrder === ColumnPushOrder.LAST) {
          c.splice(c.length - 1, 1);
        } else if (pushOrder === ColumnPushOrder.FIRST) {
          c.splice(0, 1);
        }
      }
      return self;
    },
    push: (
      id: string,
      label?: string | /* LABEL || WIDTH */ GridColumnBuilder /* OPTIONS */,
      options?: string | /* WIDTH */ GridColumnBuilder /* OPTIONS */ | GridColumnVisibility /* HIDDEN */
    ) => {
      if (excludedIds.find((i) => i === id)) {
        return self;
      }
      let labelString = id;
      let width: string = null;
      let hidden = false;
      if ((label && options && typeof label === 'object' && typeof options === 'object') || (typeof label === 'undefined' && i18nMap === null)) {
        /**
         * Si llegaste aquí, es por que "te comiste" la configuración del
         * atributo HEADER de la columna, revisar los ejemplos en este archivo.
         **/
        throw new Error('Invalid configuration, label must be a "string", current: "object"');
      }
      if (typeof label === 'string') {
        if (label.match(/<img\b[^>]*>/gi)) {
          const imgTags = label.match(/<img\b[^>]*>/gi);
          if (imgTags) {
            for (const tag of imgTags) {
              label = (label as string).replace(tag, '');
            }
            labelString = label;
          }
        } else if (label.match(/[0-9].+px/)) {
          width = label;
          if (isObject(i18nMap[id])) {
            labelString = i18nMap[id].header || id;
          } else {
            labelString = i18nMap[id] || id;
          }
        } else {
          labelString = label;
        }
        if (isGridColumnVisibility(options)) {
          hidden = options === GridColumnVisibility.HIDDEN;
          options = null;
        }
      } else if (isObject(label)) {
        if (isObject(i18nMap[id])) {
          labelString = i18nMap[id].header || id;
        } else {
          labelString = i18nMap[id] || id;
        }
        if (typeof options === 'string' && options.match(/[0-9].+px/)) {
          width = options;
        } else if (isGridColumnVisibility(options)) {
          hidden = options === GridColumnVisibility.HIDDEN;
        }
        options = label;
      }
      if (isGridColumnVisibility(options)) {
        hidden = options === GridColumnVisibility.HIDDEN;
      }
      const column: GridColumn = Object.assign(
        {
          type: GridDataType.TEXT,
          field: id,
          header: labelString
        },
        options || new TextColumn({ hidden: hidden })
      );
      if (width) {
        column.width = width;
      }
      setDefaultData(column);
      for (const c of gridDefs) {
        if (pushOrder === ColumnPushOrder.LAST) {
          c.push(cloneObject(column));
        } else if (pushOrder === ColumnPushOrder.FIRST) {
          c.unshift(cloneObject(column));
        }
      }
      return self;
    },
    lang: (i18n: DataMap<string | object>) => {
      i18nMap = i18n;
      return self;
    },
    /**
     * Este metodo tiene un memory leak potencial en el arreglo estatico `subscriptionArray`.
     *
     * @deprecated En su lugar, utilizar `translate`
     * @param translateService
     * @param LangConfig
     * @param key
     * @param removeSuperObjects
     * @returns
     */
    langAsync: (translateService: BnextTranslateService, LangConfig: BnextComponentPath, key: string | string[], removeSuperObjects = false) => {
      self.translate(null, translateService, LangConfig, key, removeSuperObjects);
      return self;
    },
    translate: (
      $destroy: Subject<any>,
      translateService: BnextTranslateService,
      LangConfig: BnextComponentPath,
      key: string | string[] = '',
      removeSuperObjects = false
    ) => {
      const keyObs = translateService.getFrom(LangConfig, key);
      const statusObs = translateService.getFrom(LANG_CONFIG, 'status');
      const numberObs = translateService.getFrom(LANG_CONFIG, 'numberBoolean');
      const statusTranslator: Partial<Observer<any>> = {
        next: (tags) => {
          for (const c of gridDefs) {
            for (const column of c) {
              setDefaultStatusTags(column, tags);
            }
          }
        }
      };
      const numberTranslator: Partial<Observer<any>> = {
        next: (tags) => {
          for (const c of gridDefs) {
            for (const column of c) {
              setDefaultNumberBooleanTags(column, tags);
            }
          }
        }
      };
      const keyTranslator: Partial<Observer<any>> = {
        next: (tags) => {
          if (removeSuperObjects) {
            tags = keysObject(tags)
              .map((key) => {
                const c: DataMap = {};
                c[key.substring((key.lastIndexOf('.') || -1) + 1, key.length)] = tags[key];
                return c;
              })
              .reduce((a, b) => {
                return Object.assign(a, b);
              });
          }
          if (i18nMap && typeof i18nMap === 'object') {
            i18nMap = Object.assign(i18nMap, tags);
          } else {
            i18nMap = tags;
          }
          for (const c of gridDefs) {
            for (const column of c) {
              setHeaders(column, tags);
            }
          }
        }
      };
      let keySubs;
      let statusSubs;
      let numberSubs;
      if ($destroy == null) {
        keySubs = keyObs.subscribe(keyTranslator);
        statusSubs = statusObs.subscribe(statusTranslator);
        numberSubs = numberObs.subscribe(numberTranslator);
      } else {
        keySubs = keyObs.pipe(takeUntil($destroy)).subscribe(keyTranslator);
        statusSubs = statusObs.pipe(takeUntil($destroy)).subscribe(statusTranslator);
        numberSubs = numberObs.pipe(takeUntil($destroy)).subscribe(numberTranslator);
      }
      if (subscriptionArray !== null) {
        if (keySubs) {
          subscriptionArray.push(keySubs);
        }
        if (statusSubs) {
          subscriptionArray.push(statusSubs);
        }
        if (numberSubs) {
          subscriptionArray.push(numberSubs);
        }
      } else if (!$destroy && (!keySubs || !statusSubs || !numberSubs)) {
        console.error('Memory leak detected! missing lang destroyable "observer", please call the "translate(this.$destroy, etc.)".');
      }
      return self;
    },
    option: (key: string, value: any) => {
      if (columns.length === 0) {
        return self;
      }
      columns[columns.length - 1][key] = value;
      return self;
    }
  };
  for (const c of gridDefs) {
    for (const column of c) {
      setDefaultData(column);
    }
  }
  return self;
}

function normalizeRangeValues<DataRowType = DataMap<any>>(
  data: DataRowType[],
  rangeSelectorColumns: GridColumn<DataRowType>[],
  range: DateRange,
  rangeSelectorDataKey: string,
  rangeSelectorDataColumnKey: string
): DataRowType[] {
  if (data === null || typeof data === 'undefined' || data.length === 0) {
    return data;
  }
  let acumulatorRangeDays = DateUtil.safe(range?.start);
  let columnDayCount = 1;
  let fieldKey: string;
  let datesRangeData: any[] = null;
  return data.map((row) => {
    if (Array.isArray(row[rangeSelectorDataKey])) {
      datesRangeData = row[rangeSelectorDataKey];
      acumulatorRangeDays = DateUtil.safe(range?.start);
      columnDayCount = 1;
      while (acumulatorRangeDays <= range?.end && columnDayCount <= MAX_COLUMN_DAYS) {
        const dayRangeDate = new Date(acumulatorRangeDays.getTime());
        for (const rangeColumn of rangeSelectorColumns) {
          fieldKey = `${DateUtil.format(dayRangeDate, 'YYYYMMDD')}_${rangeColumn.field}`; // <--- El formato de las columnas generadas es `YYYYMMDD_field`
          for (const subRow of datesRangeData) {
            if (DateUtil.isSameDay(subRow[rangeSelectorDataColumnKey], dayRangeDate)) {
              row[fieldKey] = subRow[rangeColumn.field] || null;
            } else if (!row[fieldKey]) {
              row[fieldKey] = null;
            }
          }
        }
        acumulatorRangeDays = DateUtil.add(acumulatorRangeDays, 'day', 1);
        columnDayCount++;
      }
    }
    return row;
  });
}

function removeInvalidRowChild<DataRowType = DataMap<any>>(data: DataRowType[], childDataKey: string): DataRowType[] {
  return (
    data
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      .filter((row) => !row['invalidRowChild'])
      .map((row) => {
        if (row[childDataKey]?.length) {
          row[childDataKey] = removeInvalidRowChild(row[childDataKey], childDataKey);
        }
        return row;
      })
  );
}

export function normalizeData<DataRowType = DataMap<any>>({
  gridInfo,
  columns,
  range,
  rangeSelectorColumns,
  rangeSelectorDataKey,
  rangeSelectorDataColumnKey,
  childDataKey,
  allowShowMoreChilds,
  allowRangeSelectorColumns
}: GridNormalizeConfig<DataRowType>): void {
  if (gridInfo.parseFromDynamicResults) {
    const normalColumns = columns.filter((column) => !column.dynamicFieldType).map((column) => column.field);
    const dynamicColumns = columns.filter((column) => !!column.dynamicFieldType);
    const dynamicFieldsColumnsRegex = new RegExp(`(${dynamicColumns.map((column) => `${column.field}\_`).join('|')})`);
    const indexRowColumns: DataMap<string> = {};
    const booleanColumns = columns.filter((column) => column.type === GridDataType.BOOLEAN);
    const data = gridInfo.data.map((row) => normalizeRow(row, normalColumns, dynamicFieldsColumnsRegex, indexRowColumns, booleanColumns, childDataKey));
    gridInfo.data = data;
  }
  normalizeRangeData({
    gridInfo,
    columns,
    range,
    rangeSelectorColumns,
    rangeSelectorDataKey,
    rangeSelectorDataColumnKey,
    childDataKey,
    allowShowMoreChilds,
    allowRangeSelectorColumns
  });
}

export function normalizeRow<DataRowType = DataMap<any>>(
  row: DataRowType,
  normalColumns: string[],
  dynamicFieldsColumnsRegex: RegExp,
  indexRowColumns: DataMap<string>,
  booleanColumns: GridColumn<DataRowType>[],
  childDataKey: string
): DataRowType {
  const dynamicFieldKeys = keysObject(row as unknown as object)
    .filter((columnName) => normalColumns.indexOf(columnName) === -1)
    .map((columnName) => {
      let config: string[];
      if (indexRowColumns[columnName]) {
        config = [indexRowColumns[columnName]];
      } else {
        config = columnName.match(dynamicFieldsColumnsRegex);
      }
      if (config?.length > 0 && config[0]?.length > 0) {
        const lastUnderscore = columnName.lastIndexOf('_');
        const noprmalizedName = columnName.substring(0, lastUnderscore);
        indexRowColumns[columnName] = noprmalizedName;
        config = [noprmalizedName];
      } else {
        indexRowColumns[columnName] = null;
        config = null;
      }
      return {
        columnConfig: config,
        columnName
      };
    })
    .filter((config) => config.columnConfig?.length > 0);
  for (const config of dynamicFieldKeys) {
    const dynamicFieldName = config.columnConfig[0];
    if (!row[dynamicFieldName]) {
      row[dynamicFieldName] = row[config.columnName] || '';
    }
    if (config.columnName !== dynamicFieldName) {
      delete row[config.columnName];
    }
  }
  for (const column of booleanColumns) {
    const columnName = column.field;
    if (row[columnName] === 1) {
      row[columnName] = true;
    } else if (row[columnName] === 0) {
      row[columnName] = false;
    }
  }
  if (row[childDataKey] && row[childDataKey].length > 0) {
    for (const child of row[childDataKey]) {
      normalizeRow(child, normalColumns, dynamicFieldsColumnsRegex, indexRowColumns, booleanColumns, childDataKey);
    }
  }
  return row;
}

function normalizeRangeData<DataRowType = DataMap<any>>({
  gridInfo,
  range,
  rangeSelectorColumns,
  rangeSelectorDataKey,
  rangeSelectorDataColumnKey,
  childDataKey,
  allowShowMoreChilds,
  allowRangeSelectorColumns
}: GridNormalizeConfig<DataRowType>): void {
  if (!allowShowMoreChilds) {
    const data = removeInvalidRowChild(gridInfo.data, childDataKey);
    gridInfo.data = data;
  }
  if (allowRangeSelectorColumns) {
    const data = normalizeRangeValues(gridInfo.data, rangeSelectorColumns, range, rangeSelectorDataKey, rangeSelectorDataColumnKey);
    gridInfo.data = data;
  }
}

export function updateMediumScreenColumn<DataRowType = DataMap<any>>(column: GridColumn<DataRowType>, groupable: boolean, isLargeScreen: boolean): void {
  if (!groupable) {
    column.groupable = false;
  }
  if (!isLargeScreen) {
    column.pinned = false;
  }
}
