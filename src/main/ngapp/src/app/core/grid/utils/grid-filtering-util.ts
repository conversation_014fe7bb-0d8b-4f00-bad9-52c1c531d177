import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import type { DatePipe } from '@angular/common';
import {
  type IFilteringExpression,
  type IFilteringExpressionsTree,
  type IGroupingExpression,
  type ISortingExpression,
  SortingDirection
} from '@infragistics/igniteui-angular';
import type { DataMap } from '../../utils/data-map';
import { EnumUtil } from '../../utils/enum-util';
import type { GridColumn } from './grid-column';
import { GridDataType } from './grid-data-type';
import type { GridFilterType } from './grid-filter-type.enum';
import type { IGridFilteringExpression } from './grid-filtering-expression';
import type { GridFilter, GridOrderBy } from './grid.interfaces';

export interface GridFilterConfig<DataRowType = DataMap> {
  gridId: string;
  componentName: string;
  dynamicSearchEnabled: boolean;
  index: number;
  perPage: number;
  filteringExpressionsTree: IFilteringExpressionsTree;
  sortingExpressions: ISortingExpression[];
  groupingExpressions: IGroupingExpression[];
  allowFiltering: boolean;
  columnMap: DataMap<GridColumn<DataRowType>>;
  datePipe: DatePipe;
  dateFormat: string;
  escapeUnderscore: boolean;
  masterId?: string;
}

/**
 *
 */
function iterateFilters<DataRowType = DataMap>(
  config: GridFilterConfig<DataRowType>,
  filter: GridFilter,
  filters: (IFilteringExpressionsTree | IFilteringExpression)[],
  orLogic = false
) {
  for (const fieldFilter of filters) {
    if ((fieldFilter as IFilteringExpression).condition) {
      setFilterValue(config, fieldFilter as IGridFilteringExpression, filter, orLogic);
    } else if ((fieldFilter as IFilteringExpressionsTree).filteringOperands) {
      iterateFilters(config, filter, (fieldFilter as IFilteringExpressionsTree).filteringOperands, true);
    }
  }
}
function setFilterValue<DataRowType = DataMap>(config: GridFilterConfig<DataRowType>, expression: IGridFilteringExpression, filter: GridFilter, orLogic = false): void {
  const d = new Date();
  let fieldName = expression.fieldName;
  if (config.escapeUnderscore) {
    fieldName = fieldName.replace(/_/g, '#');
  }
  const column = config.columnMap[expression.fieldName];
  const type = column?.type;
  if (column && !column.searchable) {
    console.warn(`searchable disabled for column: ${expression.fieldName}`);
    return;
  }
  if (!expression.condition.iconName) {
    switch (column?.type) {
      case GridDataType.DROP_DOWN: {
        const typeError = `Invalid "${expression.condition.iconName}" filter "${expression.searchVal}" provided for column ${expression.fieldName} of type action with drop down menu.`;
        console.error(typeError);
        return;
      }
      case GridDataType.DATE:
        setDateFilterValue(config, expression, filter, fieldName);
        return;
      case GridDataType.TIMESTAMP:
        setTimestampFilterValue(config, expression, filter, fieldName);
        return;
    }
  }
  let firstDay: Date;
  let lastDay: Date;
  const searchVal = typeof expression.searchVal === 'string' ? expression.searchVal.trim() : expression.searchVal;
  const exprType = expression.condition.iconName.replace('filter_', '');
  switch (exprType) {
    case 'contains': {
      if (type === GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS) {
        filter.searchStringChainIds = true;
      }
      if (
        type !== null &&
        type !== undefined &&
        type !== GridDataType.TEXT &&
        type !== GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS &&
        type !== GridDataType.CATALOG_HIERARCHY
      ) {
        const typeError = `Invalid "contains" filter "${searchVal}" provided for column ${expression.fieldName} of type ${type}`;
        console.error(typeError);
        return;
      }
      if (expression.likeOrSentence) {
        filter.likeOrSentence = true;
      }
      const criteria = getCriteria(filter, fieldName, expression.filterType || type === GridDataType.TEXT ? 'likeCriteria' : 'criteria');
      const criteriaValue = `*${searchVal}*`.replace(/\s*,\s*/g, '*,*');
      const criteriaType = expression.filterType || (type === GridDataType.TEXT ? 'likeCriteria' : 'criteria');
      if (orLogic && criteria) {
        const value = `${criteria},${criteriaValue}`;
        setCriteria(filter, fieldName, value, criteriaType);
      } else {
        setCriteria(filter, fieldName, criteriaValue, criteriaType);
      }
      break;
    }
    case 'does_not_contain':
      setCriteria(filter, `${fieldName}@excludedFromKeyNotLike`, `*${searchVal}*`.replace(/\s*,\s*/g, '*,*'), expression.filterType || 'likeCriteria');
      break;
    case 'starts_with':
      filter.likeCriteria[fieldName] = `${searchVal}*`.replace(/\s*,\s*/g, '*,');
      break;
    case 'ends_with':
      filter.likeCriteria[fieldName] = `*${searchVal}`.replace(/\s*,\s*/g, ',*');
      break;
    case 'equal':
      switch (column?.type) {
        case GridDataType.DATE:
          setDateFilterValue(config, expression, filter, fieldName);
          break;
        case GridDataType.TIMESTAMP:
          setTimestampFilterValue(config, expression, filter, fieldName);
          break;
        case GridDataType.LONG:
          filter.criteria[fieldName] = `${searchVal}L`;
          break;
        case GridDataType.INTEGER:
          filter.criteria[fieldName] = expression.searchVal;
          break;
        case GridDataType.DOUBLE:
          if (expression.likeOrSentence) {
            filter.likeOrSentence = true;
          }
          filter.criteria[fieldName] = `${Math.round(expression.searchVal)}D`;
          break;
        case GridDataType.PERCENTAGE:
          if (expression.likeOrSentence) {
            filter.likeOrSentence = true;
          }
          filter.criteria[fieldName] = `${Math.round(expression.searchVal)}D`;
          break;
        default:
          setCriteria(filter, fieldName, searchVal);
          break;
      }
      break;
    case 'not_equal':
      setCriteria(filter, `${fieldName}@excludedFromKeyNotLike`, expression.searchVal);
      break;
    case 'empty':
      setCriteria(filter, fieldName, '');
      break;
    case 'not_empty':
      setCriteria(filter, fieldName, '<not-empty>');
      break;
    case 'is_null':
      setCriteria(filter, fieldName, '<is-null>');
      break;
    case 'is_not_null':
      setCriteria(filter, fieldName, '<not-null>');
      break;
    case 'in':
    case 'is-in':
      if (expression.searchVal && config.allowFiltering) {
        const searchValue: any[] = Array.from(expression.searchVal);
        for (let i = 0; i < searchValue.length; i++) {
          if (typeof searchValue[i] === 'string' || searchValue[i] instanceof String) {
            searchValue[i] = (searchValue[i] || '').trim().replaceAll(',', '\u2063');
          }
        }
        if (searchValue?.length > 0) {
          let searchCondition: string;
          switch (column.type) {
            case GridDataType.LONG_ITEMS:
              searchCondition = searchValue.map((val) => `${val}L`).join(',');
              break;
            case GridDataType.TEXT_ITEMS:
              fieldName = `${fieldName}@gridDataTypeString`;
              searchCondition = searchValue.join(',');
              break;
            default:
              searchCondition = searchValue.join(',');
              break;
          }
          setCriteria(filter, fieldName, searchCondition, expression.filterType || 'criteria');
        }
      }
      break;
    case 'before': {
      const dateRangeEndSearchVal = DateUtil.format(expression.searchVal, column?.searchDateFormat || column?.dateFormat || config?.dateFormat);
      setCriteria(filter, fieldName, `<${column?.type?.key || 'date'}>${dateRangeEndSearchVal}`, 'upperLimit');
      break;
    }
    case 'after': {
      const dateRangeStartSearchVal = DateUtil.format(expression.searchVal, column?.searchDateFormat || column?.dateFormat || config?.dateFormat);
      setCriteria(filter, fieldName, `<${column?.type?.key || 'date'}>${dateRangeStartSearchVal}`, 'lowerLimit');
      break;
    }
    case 'today':
      filter.lowerLimit[fieldName] = d.getTime();
      filter.upperLimit[fieldName] = d.getTime();
      break;
    case 'yesterday':
      d.setDate(d.getDate() - 1);
      filter.lowerLimit[fieldName] = d.getTime();
      filter.upperLimit[fieldName] = d.getTime();
      break;
    case 'this_month':
      firstDay = new Date(d.getFullYear(), d.getMonth(), 1);
      lastDay = new Date(d.getFullYear(), d.getMonth() + 1, 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    case 'last_month':
      firstDay = new Date(d.getFullYear(), d.getMonth() - 1, 1);
      lastDay = new Date(d.getFullYear(), d.getMonth(), 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    case 'next_month':
      firstDay = new Date(d.getFullYear(), d.getMonth() + 1, 1);
      lastDay = new Date(d.getFullYear(), d.getMonth() + 2, 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    case 'this_year':
      firstDay = new Date(d.getFullYear(), 0, 1);
      lastDay = new Date(d.getFullYear(), 12, 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    case 'last_year':
      firstDay = new Date(d.getFullYear() - 1, 0, 1);
      lastDay = new Date(d.getFullYear() - 1, 12, 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    case 'next_year':
      firstDay = new Date(d.getFullYear() + 1, 0, 1);
      lastDay = new Date(d.getFullYear() + 1, 12, 0);
      filter.lowerLimit[fieldName] = firstDay.getTime();
      filter.upperLimit[fieldName] = lastDay.getTime();
      break;
    default:
      console.error(`Invalid backend filter, exprType "${{ exprType }}" is not supported, expression: `, expression);
      break;
  }
}
/**
 *
 * @param config
 * @param expression
 * @param filter
 * @param fieldName
 */
function setTimestampFilterValue<DataRowType>(config: GridFilterConfig<DataRowType>, expression: IGridFilteringExpression, filter: GridFilter, fieldName: string): void {
  if (!expression.searchVal) {
    return;
  }
  if (expression.condition?.iconName === 'before') {
    const timestampSearchValEnd = config.datePipe.transform(expression.searchVal, config.dateFormat, getTimezone());
    filter.upperLimit[fieldName] = `<timestamp>${timestampSearchValEnd} 23:59:59`;
  } else if (expression.condition?.iconName === 'after') {
    const timestampSearchValStart = config.datePipe.transform(expression.searchVal, config.dateFormat, getTimezone());
    filter.lowerLimit[fieldName] = `<timestamp>${timestampSearchValStart} 00:00:00`;
  } else {
    const timestampSearchValStart = config.datePipe.transform(expression.searchVal?.start, config.dateFormat, getTimezone());
    filter.lowerLimit[fieldName] = `<timestamp>${timestampSearchValStart} 00:00:00`;
    const timestampSearchValEnd = config.datePipe.transform(expression.searchVal?.end, config.dateFormat, getTimezone());
    filter.upperLimit[fieldName] = `<timestamp>${timestampSearchValEnd} 23:59:59`;
  }
}
/**
 *
 * @param config
 * @param expression
 * @param filter
 * @param fieldName
 */
function setDateFilterValue<DataRowType>(config: GridFilterConfig<DataRowType>, expression: IGridFilteringExpression, filter: GridFilter, fieldName: string): void {
  if (!expression.searchVal) {
    return;
  }
  if (expression.condition?.iconName === 'before') {
    const dateRangeValEnd = config.datePipe.transform(expression.searchVal, config.dateFormat, getTimezone());
    filter.upperLimit[fieldName] = `<date>${dateRangeValEnd}`;
  } else if (expression.condition?.iconName === 'after') {
    const dateRangeValStart = config.datePipe.transform(expression.searchVal, config.dateFormat, getTimezone());
    filter.lowerLimit[fieldName] = `<date>${dateRangeValStart}`;
  } else {
    const dateRangeValStart = config.datePipe.transform(expression.searchVal?.start, config.dateFormat, getTimezone());
    filter.lowerLimit[fieldName] = `<date>${dateRangeValStart}`;
    const dateRangeValEnd = config.datePipe.transform(expression.searchVal?.end, config.dateFormat, getTimezone());
    filter.upperLimit[fieldName] = `<date>${dateRangeValEnd}`;
  }
}
/**
 *
 * @param filter
 * @param fieldName
 * @param type
 */
function getCriteria(filter: GridFilter, fieldName: string, type: GridFilterType = 'criteria'): string {
  if (!filter[type]) {
    filter[type] = {};
  }
  return filter[type][fieldName] || '';
}

/**
 * ToDo: implementar en todos los `case's`
 *
 * @param filter
 * @param fieldName
 * @param value
 * @param type
 */
function setCriteria(filter: GridFilter, fieldName: string, value: string, type: GridFilterType = 'criteria'): void {
  if (!filter[type]) {
    filter[type] = {};
  }
  filter[type][fieldName] = value;
}
function getTimezone() {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}
export function getEmpyFilters(gridId_: string, componentName: string, perPage_ = 15, dynamicSearchEnabled = false): GridFilter {
  let gridId = gridId_;
  if (!gridId) {
    gridId = `grid-${gridId}-${componentName}`;
  }
  let perPage = perPage_;
  if (!NumberUtil.isInteger(perPage)) {
    perPage = 15;
  }
  return {
    asumeAlias: false,
    columns: {},
    criteria: {},
    direction: 1,
    dynamicFieldCriteria: {},
    dynamicSearchEnabled: !!dynamicSearchEnabled,
    enableStatistics: false,
    field: null,
    filterInPatch: false,
    gridId: gridId,
    gridShowMore: null,
    likeCriteria: {},
    likeOrSentence: false,
    lowerLimit: {},
    options: {},
    page: 0,
    pageSize: perPage,
    statisticsFields: [],
    upperLimit: {},
    windowPath: gridId
  };
}
export function getFilters<DataRowType = DataMap>(config: GridFilterConfig<DataRowType>): GridFilter {
  const filter: GridFilter = getEmpyFilters(config.gridId, config.componentName, config.perPage, config.dynamicSearchEnabled);
  filter.page = (config.index || 1) - 1;
  if (config.filteringExpressionsTree && config.allowFiltering) {
    iterateFilters(config, filter, config.filteringExpressionsTree.filteringOperands);
  }
  if (config.sortingExpressions?.length > 0) {
    const sortingExpr: ISortingExpression[] = config.sortingExpressions;
    sortingExpr.forEach((sortExpr: ISortingExpression, i: number) => {
      const column = config.columnMap[sortExpr.fieldName];
      if (!column) {
        return;
      }
      const f: GridOrderBy = {
        orderByMany: [],
        orderBy: sortExpr.fieldName,
        direction: EnumUtil.getValue(SortingDirection, sortExpr.dir)
      };
      if (column.dynamicFieldType) {
        f.type = 'SQL';
      }
      if (i === 0 || !filter.field) {
        filter.field = f;
        filter.direction = f.direction || null;
      } else {
        filter.field.orderByMany.push(f);
      }
    });
  }
  // Se agregan las expresiones del agrupamiento (Parche)
  if (config.groupingExpressions?.length > 0) {
    filter.groupFields = config.groupingExpressions;
  }
  filter.masterId = config.masterId || null;
  return filter;
}
