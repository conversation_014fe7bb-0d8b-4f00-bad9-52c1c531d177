@use 'src/styles/mediaQueries' as *;
@use 'src/app/core/grid/grid-toolbar';
@use 'src/styles/menu' as *;
@use 'src/styles/panel-sizes' as *;
@use 'src/styles/immutable-colors' as *;
@use 'sass:string';
:host {
  display: block;

  &.maximized {
    position: fixed;
    top: 0;
    left: 4.25rem;
    width: calc(100vw - $aside-width);
    height: 100vh;
    background: $bg-color;
    z-index: 3;
  }
}

.grid-layout.is-large {
  margin-bottom: 1rem;
}

.igx-dialog__window-content {
  .grid-layout.is-large {
    margin-bottom: 0rem;
  }
}

.resultsText {
  font-size: 0.875rem;
}

.caseSensitiveButton {
  margin-left: 0.625rem;
}

::ng-deep {
  /**
   * ¡Agregar estilos aquí con precaución! Estos le pegan a toda la aplicación.
   **/
  body.main-qms-body > igx-grid-row.igx-grid__tr.igx-grid__tr--ghost {
    z-index: 2 !important; // <-- Estilo de fila flotante al arrastrar desde "Planeación"
  }


  .grid-component-container .dates-range {
    .igx-date-range-picker {
      /* Se oculta borde derecho de fecha inicio */
      igx-date-range-start .igx-input-group__bundle-end,
        /* Se oculta borde izquierdo de fecha fin */
      igx-date-range-end .igx-input-group__bundle-start,
      {
        display: none;
      }

      igx-date-range-start.igx-input-group--border + .igx-date-range-picker__label,
      igx-date-range-start.igx-input-group--box + .igx-date-range-picker__label {
        /* BUG-FIX: Se agregan bordes faltantes */
        border-block-start: 0.0625rem solid var(--date-special-current-border-color);
        border-block-end: 0.0625rem solid var(--date-special-current-border-color);
        margin: 0;
        padding-right: 0.5rem;
      }
      igx-date-range-start.igx-input-group--border.igx-input-group--focused + .igx-date-range-picker__label,
      igx-date-range-start.igx-input-group--box.igx-input-group--focused + .igx-date-range-picker__label {
        /* BUG-FIX: Se agregan bordes faltantes en HOVER */
        border-inline-end-width: 0.125rem;
        border-block-start-width: 0.125rem;
        border-block-end-width: 0.125rem;
        border-inline-end-color: var(--focused-border-color);
        border-block-start-color: var(--focused-border-color);
        border-block-end-color: var(--focused-border-color);
      }

      igx-date-range-start,
      igx-date-range-end {
        /* el ancho máximo de los INPUT es el tamaño de la fecha */
        max-width: 7.85rem;
      }
      igx-date-range-end {
        /* el ancho máximo de los INPUT es el tamaño de la fecha */
        max-width: 5.5rem;
      }
    }
  }


  .grid-component-container igx-grid-toolbar {
    padding: 0.5rem;

    .range-filter-name {
      position: absolute;
    }
  }

  .grid-component-container igx-grid-toolbar-actions {
    gap: 0.5rem;

    igx-grid-toolbar-hiding button span,
    igx-grid-toolbar-pinning button span {
      margin-right: 0.2rem;
    }

    button.igx-icon-button--compact.igx-icon-button--outlined,
    button.igx-button--compact.igx-button--outlined {
      padding: 0.125rem;
    }

    button.igx-icon-button--comfortable,
    button.display-density--comfortable {
      padding: 0.625rem;
    }

    igx-grid-toolbar-pinning,
    igx-grid-toolbar-hiding {
      igx-icon,
      button:active,
      button:hover,
      button {
        background-color: transparent!important;
        border: 0;
        padding: 0;
      }
    }
  }
}

:host ::ng-deep {
  .igx-vhelper--vertical,
  .igx-vhelper--horizontal,
  .igx-grid__scroll,
  .igx-grid__tfoot {
    z-index: auto;
  }


  .igx-grid__tr--odd > .igx-display-container {
    background: rgba($bg-color, 0.5);
  }

  igx-action-strip {
    padding: 0.5rem;
    justify-content: flex-start;
    margin-left: 35%;
    width: auto;
    box-shadow: none;
  }

  .igx-action-strip__actions {
    max-height: 2.35rem !important;
  }


  .igx-grid-thead {
    min-height: 1rem !important; // <-- Encabezado de columnas agrupadas de reportes
  }

  .igx-grid__summaries--body {
    background-color: #f0f8ff;

    .igx-grid-summary {
      position: relative;

      .igx-grid-summary__item {
        &.summary-clickable {
          position: absolute;
          width: 100%;
          left: 0;
          top: 0;
          cursor: pointer;
          padding: 0 0.75rem;

          &:hover {
            background: #ffa500;
          }
        }
      }
    }
  }

  .igx-grid__group-row {
    border-top: 1px dashed #888888;
    border-bottom: 0;
  }

  igx-grid-row,
  igx-tree-grid-row {
    .igx-grid__td {
      > app-combo,
      > app-dropdown-search {
        width: 100%;
        padding: 0 0.5rem 0.5rem 0.5rem;
      }

      > app-combo {
        igx-input-group {
          > .igx-input-group__bundle {
            padding-top: 0;
            border-bottom: 1px solid var(--border-color) !important;
            border-radius: 0;

            > .igx-input-group__bundle-main {
              padding-top: 0;
              padding-left: 0;
            }
          }

          > label {
            position: absolute;
            top: 0.5rem;
          }

          &.igx-input-group--filled .igx-input-group__bundle {
            padding-top: 0.5rem;
          }
        }

        .igx-input-group--filled .igx-input-group__label {
          top: 0.75rem;
          transform: translateY(-50%) scale(0.75);
        }
      }

      .center-column {
        display: block;
        width: 100%;
        text-align: center;

        > * {
          margin: 0.125rem;
        }

        &--many {
          margin-left: 1.25rem;

          > * {
            margin-left: -1.25rem;
            border: 1px solid $bg-color;
          }
        }
      }

      .avatar-with-text {
        display: flex;
      }

      &--tree-cell {
        > div:first-child.igx-grid__tree-grouping-indicator[style='visibility: hidden;'] {
          width: 0;
        }

        > div:first-child:not(.igx-grid__tree-grouping-indicator) {
          + .igx-grid__tree-grouping-indicator {
            &[style='visibility: hidden;'] {
              &::before {
                width: 1rem;
                visibility: visible !important;

                .igx-icon {
                  visibility: hidden;
                }
              }
            }

            &::before {
              content: '';
              font-family: 'Material Icons';
              font-size: 1.5rem;
              display: inline-flex;
              line-height: 100%;
              text-indent: 1rem;
            }
          }

          &.igx-grid__tree-cell--padding-level-1 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(0 70 145 / 50%);
          }

          &.igx-grid__tree-cell--padding-level-2 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(0 70 145 / 25%);
          }

          &.igx-grid__tree-cell--padding-level-3 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(0 145 70 / 50%);
          }

          &.igx-grid__tree-cell--padding-level-4 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(0 145 70 / 25%);
          }

          &.igx-grid__tree-cell--padding-level-5 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(70 0 145 / 50%);
          }

          &.igx-grid__tree-cell--padding-level-6 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(70 0 145 / 25%);
          }

          &.igx-grid__tree-cell--padding-level-7 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(145 0 70 / 50%);
          }

          &.igx-grid__tree-cell--padding-level-8 + .igx-grid__tree-grouping-indicator::before {
            content: 'subdirectory_arrow_right';
            color: rgb(145 0 70 / 25%);
          }
        }
      }

      .clear-cell-button {
        display: none;
        position: absolute;
        right: 0.25rem;
        background: rgba($fg-color, 0.15);
        cursor: pointer;
      }

      &--number .clear-cell-button {
        left: 0.25rem;
        right: unset;
      }
    }

    &:hover .clear-cell-button {
      display: flex;
    }

    .igx-icon {
      cursor: pointer;
    }
  }

  // Solo break-word
  igx-tree-grid .igx-grid__td,
  igx-tree-grid .igx-grid__td--tree-cell {
    > span {
      word-break: break-word;
    }
  }

  // Restringir a 3 lineas máximo
  igx-tree-grid.width-breakword .igx-grid__td,
  igx-tree-grid.width-breakword .igx-grid__td--tree-cell {
    > span {
      text-overflow: ellipsis;
      display: -webkit-box;
      line-clamp: 3;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  // Experimental
  igx-tree-grid-row[data-rowindex]:hover::before {
    content: attr(data-rowindex);
    font-size: 0.625rem;
    border-color: rgb(223 223 223);
    padding: 0.188rem;
    height: 1.5rem;
    top: 0;
    right: 0;
    z-index: 1;
    line-height: 0.938rem;
    min-width: 1.5rem;
    position: absolute;
    text-align: center;
    border-width: 0.188rem;
    margin: 0.375rem;
    border-style: solid;
    border-radius: 1rem;
    background: rgb(255 255 255);
  }

  .no-grouping-expressions-available {
    .igx-grid__td--tree-cell > div:first-child:not(.igx-grid__tree-grouping-indicator) {
      padding-left: 0;

      &::before {
        position: absolute;
        top: 0.25rem;
        left: 1.5rem;
      }
    }
  }

  .grid-search-input-container {
    margin-bottom: 0.7rem;
  }

  .grid-search-fields-container {
    opacity: 0;
    max-height: 1rem;
    display: none;

    &.opened-container {
      display: block;
      opacity: 1;
      max-height: 100%;
      position: fixed;
      top: 0;
      right: 0;
      max-width: $single-column-panel-width;
      background-color: $bg-color !important;
      z-index: 3;
      height: 100vh;
      overflow: auto;
    }

    .grid-search-fields {
      padding: 0 0.5rem;

      div.cell {
        padding: 0.5rem;
      }

      .filters-fields {
        margin: 0.5rem;
      }

      div[field-type='hierarchy'] {
        padding-top: 0rem;
        padding-bottom: 0rem;
      }
    }

    .filter-buttons {
      margin-top: 1.5rem;
      text-align: right;
    }

    .apply-filter-button {
      width: 100%;
    }

    .apply-filters-button {
      position: absolute;
      bottom: 0;
      background-color: $bg-color;
      width: 100%;
      display: flex;
      flex-flow: wrap;
      justify-content: start;

      > * {
        margin: 0.5rem 0;
        margin-left: 1rem;
      }

      > igx-divider {
        margin: 0;
      }
    }

    .close-button {
      min-height: auto;
      right: 0;
      position: fixed;
      background-color: #ececec;
      border-radius: 0;
    }
  }

  .grid-search-filter-chips {
    padding-bottom: 0.5rem;
    padding-top: 0;

    .filter-chips {
      display: flex;
      justify-content: space-around;
      width: 100%;

      .filter-chip .igx-chip__content {
        max-width: 100%;
        display: flex;

        span.detail {
          max-width: 20rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          padding-left: 0.2rem;
        }
      }
    }
  }

  .igx-grid__filtering-cell {
    display: none;
  }

  .is-column-hiding-dropdown-open-500 .igx-drop-down__list.igx-toggle {
    max-height: string.unquote('calc(100vh - 31.25rem)');
  }

  .is-column-hiding-dropdown-open-250 .igx-drop-down__list.igx-toggle {
    max-height: string.unquote('calc(100vh - 15.625rem)');
  }

  .moveGrid .igx-grid__tbody .igx-grid__tbody-content {
    height: fit-content !important;
  }

  .grid-component-container > div > *.can-border-left-color {
    .igx-grid-thead {
      padding-left: 0.5rem;
      position: absolute;
      background-color: var(--header-background);
    }

    .igx-grid__summaries--body,
    .igx-grid__tfoot {
      background-color: var(--header-background);
      padding-left: 0.5rem;
    }

    .igx-grid__tbody {
      margin-top: auto;
    }
  }
}

.moveGrid {
  max-height: 43rem;
  height: fit-content !important;
}

.caseSensitiveIcon {
  width: 1.25rem;
  height: 1.25rem;
  font-size: 1.25rem;
  color: rgba($fg-color, 0.54);
}

.searchButtons {
  margin-left: 0.313rem;
}

.custom-icon-container,
.custom-action-container {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.custom-icon,
.custom-action {
  margin: -0.688rem auto;
}

.grid-update-container {
  display: flex;
  justify-content: space-between;
  flex-grow: 1;

  > * {
    display: flex;
  }
}

.grid-search-input-container,
.grid-filtering-input-container {
  flex-grow: 1;
}

.grid-maximize-action,
.grid-update-action,
.grid-update-last-updated,
.grid-export-action {
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-update-last-updated {
  font-style: italic;
  font-weight: 300;
  font-size: 0.8em;
  padding-right: 0.125rem;
}

.grid-title-mobile {
  margin: 0.75rem 0.75rem 0.75rem 0.3rem;
  font-size: 1.15rem;
}

.pagination-bar {
  width: 100%;
  padding: 0.25rem 0rem;
  float: left;
}

.pagination-bar > div {
  display: flex;
  line-height: 2.25rem;
  border-radius: 2rem;
  padding-left: 0.1rem;
  padding-right: 0.1rem;
  background: #0000000a;
  color: #0000008a;
}

.pagination-page-buttons {
  float: left;
}

.pagination-per-page-buttons {
  float: right;
  padding-left: 1rem;
}

.pagination-per-page-buttons .selected {
  background: #a9a9a97d;
  font-weight: bold;
}

.pagination-page-buttons > span,
.pagination-page-buttons button,
.pagination-per-page-buttons > div {
  display: inline-flex;
  color: $fg-color;
  line-height: 1.5rem;
}

.justify-content-center {
  width: 100%;
  text-align: center;
}

:host ::ng-deep {
  .switch-order {
    &.igx-grid-th,
    &.igx-grid__td--number,
    &.igx-grid-th--number {
      text-align: left;

      .igx-grid-th__icons {
        order: 0;
      }
    }
  }

  .cell-order {
    &.igx-grid__td--number,
    &.igx-grid-th--number {
      text-align: right;
      justify-content: flex-start;
    }
  }

  .igx-column-actions__columns {
    min-height: 6.563rem;
  }

  .clickable-column {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:hover {
      margin: 0.2rem;
      background-color: #f5ee9a;
      border-radius: 3rem;
    }
  }
}

::ng-deep {
  @media print, screen and (min-width: $xlarge) and (max-width: 109.375rem) {
    .igx-grid-toolbar .igx-grid-toolbar__actions {
      app-grid-export .igx-icon-button span:last-of-type,
      igx-grid-toolbar-pinning .igx-icon-button span:last-of-type,
      igx-grid-toolbar-hiding .igx-icon-button span:last-of-type,
      app-grid-export .igx-button span:last-of-type,
      igx-grid-toolbar-pinning .igx-button span:last-of-type,
      igx-grid-toolbar-hiding .igx-button span:last-of-type {
        display: none;
      }
    }
  }
}

:host ::ng-deep {
  /* responsividad small */
  @media print, screen and (max-width: $small) {
    .grid-container.grid-floating-active.full-width-grid-container {
      box-shadow: none !important;
    }
    .grid-search-fields-container {
      &.opened-container {
        max-width: none;
        height: calc(100vh - $mobile-app-bar-height);
        top: $mobile-app-bar-height;
      }
    }
    .grid-search-filter-chips .filter-chips {
      .filter-chip .igx-chip__content {
        > span,
        span.detail {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          padding-left: 0.2rem;
          max-width: 6rem !important;
        }
      }
    }
    .container-form.favorites .igx-grid-toolbar {
      justify-content: center;
      padding: 0;
      flex-flow: column;

      .igx-grid-toolbar__custom-content {
        display: flex;
        flex-wrap: wrap;
        flex-grow: 1;
        justify-content: center;
        align-content: center;
        margin: 0 1rem;

        > button {
          width: 100%;
          margin: 0.2rem;
        }

        [igxButton] :first-of-type {
          margin-left: 0.2rem;
        }
      }
    }

    .mobile-dialog-activites-saved {
      max-height: 15rem !important;
      height: 15rem !important;
    }

    .igx-column-actions {
      max-height: 22.125rem;
      width: 21.875rem !important;
    }
  }
}

.hidden-colums-tree-grid {
  background-color: white;
  width: 21.875rem !important;
  display: flex;
  flex-direction: column;

  .close-hidding-button {
    width: 100%;
  }
}

.igx-column-actions {
  max-height: 22.125rem;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  flex-wrap: wrap;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.group-avatar {
  margin-left: -1.25rem;
}

.avatar-text-container {
  font-weight: 600;
  display: inline-grid;
  align-content: center;
  text-align: left;
  overflow: hidden;
}

.avatar-secundary-text {
  font-weight: 400;
}

.actions-top-zone {
  width: 100%;
  background-color: hsl(0deg 0% 96%);
  border-color: hsl(0deg 0% 74%);
  z-index: 2;
}

.tooltip-main-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cell-tooltip-close-button {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  align-items: center;
  padding-bottom: 0.5rem;
}

.cell-tooltip-close-button > span {
  font-size: 1.4rem;
  font-weight: bold;
}

.showTooltipLabel,
.cell-tooltip-close-button > igx-icon {
  cursor: pointer;
}

.pagination-button {
  outline: none;
  border: none;
  box-shadow: none;
  margin-top: 0;
  height: 1.625rem;
  border-radius: 50%;
}

.title-hidden-tree-grid {
  color: rgb(0, 69, 143);
  padding: 1rem 1rem 0 1rem;
  font-weight: 400;
  font-size: 1rem;
}

::ng-deep {
  .igx-column-actions {
    box-shadow: none !important;
  }

  .igx-grid-toolbar__custom-content {
    justify-content: start;
  }

  .cell-priorityColor > div:first-of-type::before,
  .cell-priorityColor > div:first-of-type::after {
    position: absolute;
    height: 1rem;
    width: 1rem;
    top: 25%;
    bottom: 25%;
  }

  .cell-priorityColor > div:first-of-type::before {
    content: '•';
    left: 0.85rem;
    font-size: 3.55rem;
  }

  .cell-priorityColor > div:first-of-type::after {
    content: '';
    left: 1rem;
    border: solid 0.125rem;
    border-radius: 50%;
    border-color: rgba($bg-color, 0.125);
  }

  .cell-priorityColor > div:first-of-type {
    .igx-group-label__text {
      float: right;
    }
  }
  .grouped-value {
    &.igx-group-label__text {
        font-weight: 600;
        color: $fg-color;
      }
  }
  /**
 * Funcionalidad para hacer más grandes los avatars al pasar el ratón encima de un GRID
 */
  .igx-grid__tr:hover {
    > .igx-display-container {
      z-index: unset;
    }

    + .igx-grid__tr + .igx-grid__tr .igx-grid__td--pinned,
    + .igx-grid__tr .igx-grid__td--pinned {
      z-index: 1; // <-- Necesario para que el renglón que sigue no se empalme con al que se le hace `hover`
    }

    igx-avatar > * {
      position: absolute;
      transition: 0.2s;
      transform: scale(2.25);
      z-index: 1;
      border: 1px solid #555;
      box-shadow: 0 calc(var(--ig-elevation-factor, 1) * 1px) calc(var(--ig-elevation-factor, 1) * 5px) 0 #00000013,
      0 calc(var(--ig-elevation-factor, 1) * 2px) calc(var(--ig-elevation-factor, 1) * 2px) 0 #888,
      0 calc(var(--ig-elevation-factor, 1) * 3px) calc(var(--ig-elevation-factor, 1) * 1px) calc(var(--ig-elevation-factor, 1) * -2px) #000000d9;
    }
  }

  .fix-avatar-hover .igx-grid__tbody .igx-grid__tr:first-child:hover igx-avatar > * {
    transform: scale(2.25) translateY(0.64rem);
  }

  //Cuando es la primer columna no se muestre empalmado a la derecha
  .fix-avatar-hover .igx-grid__tbody .igx-grid__tr:hover igx-display-container igx-grid-cell:first-child div > igx-avatar {
    transform: translateX(0.64rem);
    z-index: 1;
  }

  .fix-avatar-hover.fix-extensive-rows .igx-grid__tbody .igx-grid__tr:last-child {
    margin-bottom: 3.5rem;
  }

  .fix-avatar-hover .igx-grid__tbody .igx-grid__tr:hover {
    overflow: visible !important;
  }

  .fix-avatar-hover .igx-display-container:hover {
    overflow: visible !important;
  }

  .igx-overlay__content--modal:has(igx-circular-bar) {
    overflow: hidden;
  }
}
