@if (columnsSetted) {
  <div class="grid-floating-action-buttons">
    <div class="grid-container bg-color main-grid-component-container">
      @if (topZoneEnable()) {
        <div class="dialog-top-zone elevation-2">
          <ng-content select="[dialogTopZone]" ngProjectAs="[dialogTopZone]"></ng-content>
        </div>
      }
      <div class="grid-x">
        <div class="cell small-12 large-shrink grid-update-container">
          <div>
            <ng-content select="[gridFabButton]"></ng-content>
          </div>
          <div>
            @if (url && showRefresh()) {
              <div class="grid-update-last-updated" [hidden]="!url && !showRefresh() && !showSearch()">
                <span>{{ i18n.last_updated_label }} {{ formatTimestamp(lastUpdated) }}</span>
              </div>
              <div class="grid-update-action" [hidden]="!url && !showRefresh() && !showSearch()">
                <button [igxIconButton]="'flat'" type="button" igxRipple [igxRippleCentered]="true" (click)="refresh()">
                  <igx-icon family="material">refresh</igx-icon>
                </button>
              </div>
            }
            @if (allowClearState()) {
              <div class="grid-maximize-action" [title]="i18n.clearState || 'i18n.clearState'">
                <button [igxIconButton]="'flat'" type="button" igxRipple [igxRippleCentered]="true" (click)="clearState()">
                  <igx-icon family="material">restart_alt</igx-icon>
                </button>
              </div>
            }
            @if (allowMaximize()) {
              <div class="grid-maximize-action">
                <button [igxIconButton]="'flat'" type="button" igxRipple [igxRippleCentered]="true" (click)="onToggleMaximize()">
                  <igx-icon family="material">{{ maximized ? 'close_fullscreen' : 'open_in_full' }}</igx-icon>
                </button>
              </div>
            }
            @if (allowRemoveAll()) {
              <div class="grid-maximize-action">
                <button [igxIconButton]="'flat'" type="button" igxRipple [igxRippleCentered]="true" (click)="deleteAllRegistries.emit(true)">
                  <igx-icon family="material">delete_sweep</igx-icon>
                </button>
              </div>
            }
            @if (showTopExportButton()) {
                @defer (on immediate) {
                  <app-grid-export
                    class="grid-export-action"
                    [allowRangeSelectorColumns]="allowRangeSelectorColumns"
                    [allowShowMoreChilds]="allowShowMoreChilds()"
                    [autoConfig]="exportAutoConfig()"
                    [childDataKey]="childDataKey()"
                    [columnMap]="columnMap"
                    [columns]="columns"
                    [displayDensity]="igxButtonToolbarButtonDensity()"
                    [filters]="getFilters()"
                    [grid]="grid"
                    [inputExport]="exportColumnsOption()"
                    [igxButtonType]="igxButtonToolbarButtonType()"
                    [totalCount]="totalCount"
                    [inputFormat]="exportFormatOption()"
                    [inputRecords]="exportRecordsOption()"
                    [paging]="paging()"
                    [rangeSelectorDataColumnKey]="rangeSelectorDataColumnKey()"
                    [rangeSelectorDataKey]="rangeSelectorDataKey()"
                    [range]="rangeSelector?.range"
                    [showButtonLabel]="showExportButtonLabel()"
                    [titleLabel]="titleLabel()"
                    (startedExporting)="onStartedExporting()"
                    (endedExporting)="onEndedExporting($event)"
                  >
                  </app-grid-export>
                }
            }
            @if (showTopImportButton()) {
              @if (showTopImportButton()) {
                @defer (on immediate) {
                  <app-grid-import
                    [displayDensity]="displayDensity"
                    [importColumnIdLabel]="importColumnIdLabel()"
                    [importSchema]="importSchema()"
                    (importedExcel)="onImportedExcel($event)"
                  ></app-grid-import>
                }
              }
            }
          </div>
        </div>
        @if (!isLargeScreen) {
          <div>
            <h5 class="grid-title-mobile">{{ showTitle() ? titleLabel() || i18n.toolbar_title : '' }}</h5>
          </div>
        }
        @if (searchEnabled && columnsReady) {
          <app-grid-search
            class="grid-x cell small-12"
            [name]="id()"
            [caseSensitive]="searchConfig?.caseSensitive"
            [columns]="columns"
            [displayDensity]="displayDensity"
            [i18n]="i18n"
            [lastSearchInfo]="grid?.lastSearchInfo"
            [persistState]="persistState()"
            [searchBusy]="loading"
            [searchLabel]="searchLabel()"
            [urlAvailable]="!!url"
            (changeFilter)="onChangeFilter($event)"
            (navigatePrev)="onNavigatePrev($event)"
            (navigateNext)="onNavigateNext($event)"
            (clearFilter)="resetGridSearch()"
            (clearSearchGlobalFilters)="onClearSearchGlobalFilters()"
          >
          </app-grid-search>
        }
        @if (actionsTopEnable()) {
          <div class="actions-top-zone elevation-2">
            <ng-content select="[actions-top-zone]" ngProjectAs="[actions-top-zone]"></ng-content>
          </div>
        }
        <div class="cell small-12 grid-layout" [class.is-large]="isLargeScreen">
          <div
            class="grid-component-container elevation-2"
            [class.no-grouping-expressions-available]="!groupIdentLevel1() && groupingExpressions.length === 0"
            [class.is-column-hiding-dropdown-open-250]="isColumnDropdownHeight[250]"
            [class.is-column-hiding-dropdown-open-500]="isColumnDropdownHeight[500]"
          >
            <div>
              @switch (gridType) {
                @case (EnumBnextGridType.DATA_GRID) {
                  <ng-container *ngTemplateOutlet="dataGridTemplate"></ng-container>
                }
                @case (EnumBnextGridType.TREE_GRID) {
                  <ng-container *ngTemplateOutlet="treeGridTemplate"></ng-container>
                }
              }
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- DATA_GRID -->
    <ng-template #dataGridTemplate>
      <igx-grid
        [data]="data"
        [ngClass]="displayDensityClass"
        [emptyFilteredGridMessage]="emptyFilteredGridMessage"
        [emptyGridMessage]="emptyGridMessage"
        [filteringExpressionsTree]="filteringExpressionsTree"
        [groupingExpressions]="groupingExpressions"
        [id]="id()"
        [igxGridState]="stateOptions()"
        [isLoading]="loading"
        [height]="height"
        [sortingExpressions]="sortingExpressions()"
        [class]="gridClass() + (addAvatarFixClass ? ' fix-avatar-hover ' : '')"
        [rowStyles]="rowStyles()"
        [rowClasses]="rowClasses()"
        #dataGrid
        (groupingDone)="onGroupingExpressionsChange($event)"
        (gridScroll)="onGridScroll($event)"
        (mousemove)="onGridMouseMove($event)"
      >
        @if (rowDraggable()) {
          <ng-template igxDragIndicatorIcon>
            <igx-icon>drag_handle</igx-icon>
          </ng-template>
        }
        @if (paging()) {
          <igx-paginator [page]="page" [perPage]="perPage" (pagingDone)="onPagingDoneWrapper($event)">
            <igx-paginator-content>
              <ng-container *ngTemplateOutlet="paginatorContent"></ng-container>
            </igx-paginator-content>
          </igx-paginator>
        }
        @if (showToolbar()) {
          <igx-grid-toolbar #toolbar>
            @if (isLargeScreen && showToolbarTitle()) {
              <igx-grid-toolbar-title>{{ showTitle() ? titleLabel() || i18n.toolbar_title : '' }}</igx-grid-toolbar-title>
            }
            <ng-content select="[customElementToolbar]"></ng-content>
            <ng-container *ngTemplateOutlet="toolbarTransactionalButtons"></ng-container>
            @if (showToolbar()) {
              <igx-grid-toolbar-actions>
                @if (allowAdvancedFiltering()) {
                  <igx-grid-toolbar-advanced-filtering [title]="i18n.export_text"></igx-grid-toolbar-advanced-filtering>
                }
                <ng-content select="[BnextGridToolBarContent]"></ng-content>
                @if (allowRangeSelector()) {
                  @defer (on immediate) {
                    @switch (rangeSelectorType()) {
                      @case ('WeeklySelector') {
                        <app-toolbar-weekly-selector
                          #rangeSelector
                          inputType="border"
                          [displayDensity]="displayDensity"
                          [inputDisplayDensity]="igxButtonToolbarButtonDensity()"
                          [igxButtonType]="igxButtonToolbarButtonType()"
                          [showSummaryButton]="false"
                          [rangeFilterName]="rangeFilterName()"
                          [dateLabelFrom]="rangeFilterFrom()"
                          [dateLabelTo]="rangeFilterTo()"
                          (toggleSummary)="onToggleSummary()"
                          (paramReady)="onRangeParamReady($event)"
                          (updatedRangeData)="onUpdatedRangeData($event)"
                        ></app-toolbar-weekly-selector>
                      }
                      @case ('MonthlySelector') {
                        <app-toolbar-monthly-selector
                          #rangeSelector
                          inputType="border"
                          [displayDensity]="displayDensity"
                          [inputDisplayDensity]="igxButtonToolbarButtonDensity()"
                          [igxButtonType]="igxButtonToolbarButtonType()"
                          [showSummaryButton]="false"
                          [rangeFilterName]="rangeFilterName()"
                          [dateLabelFrom]="rangeFilterFrom()"
                          [dateLabelTo]="rangeFilterTo()"
                          (toggleSummary)="onToggleSummary()"
                          (paramReady)="onRangeParamReady($event)"
                          (updatedRangeData)="onUpdatedRangeData($event)"
                        ></app-toolbar-monthly-selector>
                      }
                      @case ('YearlySelector') {
                        <app-toolbar-yearly-selector
                          #rangeSelector
                          inputType="border"
                          [displayDensity]="displayDensity"
                          [inputDisplayDensity]="igxButtonToolbarButtonDensity()"
                          [igxButtonType]="igxButtonToolbarButtonType()"
                          [showSummaryButton]="false"
                          [rangeFilterName]="rangeFilterName()"
                          [dateLabelFrom]="rangeFilterFrom()"
                          [dateLabelTo]="rangeFilterTo()"
                          (toggleSummary)="onToggleSummary()"
                          (paramReady)="onRangeParamReady($event)"
                          (updatedRangeData)="onUpdatedRangeData($event)"
                        ></app-toolbar-yearly-selector>
                      }
                    }
                  }
                }
                @if (columnHiding() && !isMobilDevice()) {
                  <igx-grid-toolbar-hiding
                    [title]="i18n.column_visible_title"
                    [buttonText]="i18n.hidden_columns"
                    [igxButton]="igxButtonToolbarButtonType()"
                    (opened)="onOpenToolbarConfigUi($event)"
                  ></igx-grid-toolbar-hiding>
                } @else {
                  <button [igxButton]="igxButtonToolbarButtonType()" igxRipple [class]="'display-density--' + igxButtonToolbarButtonDensity()" (click)="openDialogHiddenColumns()">
                    <igx-icon>visibility_off</igx-icon>
                    <span>{{ dataGrid.hiddenColumnsCount }}</span>
                    <span> {{ 'root.common.button.visible' | translate: this }}</span>
                  </button>
                }
                @if (columnPinning) {
                  <igx-grid-toolbar-pinning
                    [title]="i18n.column_pinning_title"
                    [buttonText]="i18n.pinned_columns"
                    [igxButton]="igxButtonToolbarButtonType()"
                    (opened)="onOpenToolbarConfigUi($event)"
                  ></igx-grid-toolbar-pinning>
                }
                @if (exportExcel() || exportCsv()) {
                  @defer (on immediate) {
                    <app-grid-export
                      [allowRangeSelectorColumns]="allowRangeSelectorColumns"
                      [allowShowMoreChilds]="allowShowMoreChilds()"
                      [autoConfig]="exportAutoConfig()"
                      [childDataKey]="childDataKey()"
                      [columnMap]="columnMap"
                      [columns]="columns"
                      [displayDensity]="displayDensity"
                      [inputDisplayDensity]="igxButtonToolbarButtonDensity()"
                      [filters]="getFilters()"
                      [grid]="grid"
                      [inputExport]="exportColumnsOption()"
                      [igxButtonType]="igxButtonToolbarButtonType()"
                      [totalCount]="totalCount"
                      [inputFormat]="exportFormatOption()"
                      [inputRecords]="exportRecordsOption()"
                      [paging]="paging()"
                      [rangeSelectorDataColumnKey]="rangeSelectorDataColumnKey()"
                      [rangeSelectorDataKey]="rangeSelectorDataKey()"
                      [range]="rangeSelector?.range"
                      [showButtonLabel]="showExportButtonLabel()"
                      [titleLabel]="titleLabel()"
                      (startedExporting)="onStartedExporting()"
                      (endedExporting)="onEndedExporting($event)"
                    >
                    </app-grid-export>
                  }
                }
              </igx-grid-toolbar-actions>
            }
          </igx-grid-toolbar>
        }
        @for (col of columns; track col; let i = $index) {
          <ng-container>
            <igx-column
              [cellClasses]="col.cellClasses"
              [cellStyles]="col.cellStyles"
              [dataType]="col.type.dataType"
              [disableHiding]="col.disableHiding"
              [editable]="col.editable"
              [field]="col.field"
              [filterCellTemplate]="emptyFilterTemplate"
              [filterable]="col.filterable"
              [filteringIgnoreCase]="col.filteringIgnoreCase"
              [filters]="col.filters"
              [formatter]="col.formatter"
              [groupable]="allowGrouping() && col.groupable"
              [hasSummary]="col.hasSummary"
              [headerClasses]="col.headerClasses"
              [headerGroupClasses]="col.headerGroupClasses"
              [headerGroupStyles]="col.headerGroupStyles"
              [headerStyles]="col.headerStyles"
              [header]="col.header"
              [hidden]="col.hidden"
              [maxWidth]="col.maxWidth"
              [pipeArgs]="col.pipeArgs"
              [minWidth]="col.minWidth"
              [pinned]="col.pinned"
              [resizable]="col.resizable"
              [searchable]="col.searchable"
              [selectable]="col.selectable"
              [sortable]="col.sortable"
              [sortingIgnoreCase]="col.sortingIgnoreCase"
              [summaries]="col.summaries || null"
              [width]="col.width"
            >
              @if (col.editable && col.customCellEditor) {
                <ng-template igxCellEditor let-cell="cell" let-value>
                  <ng-template
                    *ngTemplateOutlet="
                      customCellEditor;
                      context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, cell: cell, value: value } }
                    "
                  ></ng-template>
                </ng-template>
              }
              <ng-template igxCell let-cell="cell" let-val>
                <ng-template
                  *ngTemplateOutlet="columnContent; context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, cell: cell, value: val } }"
                ></ng-template>
              </ng-template>
              <ng-template igxSummary let-summaryResults>
                <ng-template *ngTemplateOutlet="summaryContent; context: { $implicit: { columnIndex: i, column: col, summaryResults: summaryResults } }"></ng-template>
              </ng-template>
            </igx-column>
          </ng-container>
          <ng-template igxGroupByRow let-groupRow>
            <ng-template
              *ngTemplateOutlet="groupContent; context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, groupRow: groupRow } }"
            ></ng-template>
          </ng-template>
          <ng-template igxRowEditText let-rowChangesCount>
            <ng-template *ngTemplateOutlet="rowEditTextTemplate; context: { $implicit: rowChangesCount }"></ng-template>
          </ng-template>
          <ng-template igxRowEditActions let-endRowEdit>
            <ng-template *ngTemplateOutlet="rowEditTextActions; context: { $implicit: endRowEdit }"></ng-template>
          </ng-template>
        }
        @if (showActionStrip && actionStripConfig) {
          <igx-action-strip #actionstrip class="elevation-2">
            @defer (on immediate) {
              <app-dropdown-menu
                #dropdownmenu
                [alwaysVisible]="!!actionStripConfig.dropdownAlwaysVisible"
                [simplify]="true"
                [simplifiedCount]="actionStripConfig.dropdownActionLimit || 3"
                [selectable]="true"
                [menuOptions]="actionStripConfig.render.menuActionOptions(actionstrip.context)"
                [availableItemValues]="undefined"
                [dropdownId]="'grid-dropdown-menu-' + actionStripConfig.render.rowIdentifierKey"
                [iconsStyle]="'material-icons-filled'"
                [toggleButtonTitle]="actionStripConfig.render.toggleButtonTitle"
                (changed)="toggleMenuActionStrip($event, actionstrip.context, actionStripConfig.render)"
              >
              </app-dropdown-menu>
            }
          </igx-action-strip>
        }
        @for (dayColumn of dayColumns; track dayColumn.header; let i = $index) {
          <igx-column-group
            [cellClasses]="dayColumn.cellClasses"
            [cellStyles]="dayColumn.cellStyles"
            [dataType]="dayColumn.type.dataType"
            [field]="dayColumn.field"
            [filterable]="dayColumn.filterable"
            [formatter]="dayColumn.formatter"
            [hasSummary]="dayColumn.hasSummary"
            [header]="dayColumn.header"
            [hidden]="dayColumn.hidden"
            [pinned]="dayColumn.pinned"
            [pipeArgs]="dayColumn.pipeArgs"
            [resizable]="dayColumn.resizable"
            [sortable]="dayColumn.sortable"
            [width]="dayColumn.width"
          >
            @for (rangeColumn of getRangeSelectorColumns(dayColumn); track rangeColumn; let subIndex = $index) {
              <igx-column
                [cellClasses]="rangeColumn.cellClasses"
                [cellStyles]="rangeColumn.cellStyles"
                [dataType]="rangeColumn.type.dataType"
                [disableHiding]="rangeColumn.disableHiding"
                [editable]="rangeColumn.editable"
                [field]="rangeColumn.field"
                [filterCellTemplate]="emptyFilterTemplate"
                [filterable]="rangeColumn.filterable"
                [filteringIgnoreCase]="rangeColumn.filteringIgnoreCase"
                [filters]="rangeColumn.filters"
                [formatter]="rangeColumn.formatter"
                [groupable]="allowGrouping() && rangeColumn.groupable"
                [hasSummary]="rangeColumn.hasSummary"
                [headerClasses]="rangeColumn.headerClasses"
                [headerGroupClasses]="rangeColumn.headerGroupClasses"
                [headerGroupStyles]="rangeColumn.headerGroupStyles"
                [headerStyles]="rangeColumn.headerStyles"
                [header]="rangeColumn.header"
                [hidden]="rangeColumn.hidden"
                [maxWidth]="rangeColumn.maxWidth"
                [minWidth]="rangeColumn.minWidth"
                [pipeArgs]="rangeColumn.pipeArgs"
                [pinned]="rangeColumn.pinned"
                [resizable]="rangeColumn.resizable"
                [searchable]="rangeColumn.searchable"
                [selectable]="rangeColumn.selectable"
                [sortable]="rangeColumn.sortable"
                [sortingIgnoreCase]="rangeColumn.sortingIgnoreCase"
                [summaries]="rangeColumn.summaries || null"
                [width]="rangeColumn.width"
              >
                @if (rangeColumn.editable && rangeColumn.customCellEditor) {
                  <ng-template igxCellEditor let-cell="cell" let-value>
                    <ng-template
                      *ngTemplateOutlet="
                        customCellEditor;
                        context: {
                          $implicit: { dayColumn: dayColumn, textWhenEmpty: rangeColumn.textWhenEmpty, columnIndex: null, column: rangeColumn, cell: cell, value: value }
                        }
                      "
                    ></ng-template>
                  </ng-template>
                }
                <ng-template igxCell let-cell="cell" let-value>
                  <ng-template
                    *ngTemplateOutlet="
                      columnContent;
                      context: {
                        $implicit: { dayColumn: dayColumn, textWhenEmpty: rangeColumn.textWhenEmpty, columnIndex: null, column: rangeColumn, cell: cell, value: value }
                      }
                    "
                  ></ng-template>
                </ng-template>
                <ng-template igxSummary let-summaryResults>
                  <ng-template
                    *ngTemplateOutlet="
                      summaryContent;
                      context: { $implicit: { dayColumn: dayColumn, columnIndex: null, column: rangeColumn, summaryResults: summaryResults } }
                    "
                  ></ng-template>
                </ng-template>
              </igx-column>
            }
          </igx-column-group>
        }
      </igx-grid>
      <igx-dialog [leftButtonLabel]="'root.common.button.close' | translate: this" (leftButtonSelect)="dialogHiddingColumns().close()">
        <div>
          <igx-column-actions igxColumnHiding [grid]="dataGrid"></igx-column-actions>
        </div>
      </igx-dialog>
    </ng-template>
    <!-- TREE_GRID -->
    <ng-template #treeGridTemplate>
      <igx-tree-grid
        #treeGrid
        [childDataKey]="childDataKey()"
        [class]="gridClass() + (addAvatarFixClass ? ' fix-avatar-hover ' : '')"
        [data]="data ?? [] | treeGridGrouping: groupingExpressions : groupKey : childDataKey() : treeGrid"
        [ngClass]="displayDensityClass"
        [emptyFilteredGridMessage]="emptyFilteredGridMessage"
        [emptyGridMessage]="emptyGridMessage"
        [filteringExpressionsTree]="filteringExpressionsTree"
        [hasChildrenKey]="childDataKey()"
        [height]="height"
        [id]="id()"
        [igxGridState]="stateOptions()"
        [isLoading]="loading"
        [rowClasses]="rowClasses()"
        [rowStyles]="rowStyles()"
        [sortStrategy]="sorting"
        [sortingExpressions]="sortingExpressions()"
        (gridScroll)="onGridScroll($event)"
        (mousemove)="onGridMouseMove($event)"
      >
        @if (rowDraggable()) {
          <ng-template igxDragIndicatorIcon>
            <igx-icon>drag_handle</igx-icon>
          </ng-template>
        }
        @if (paging()) {
          <igx-paginator [page]="page" [perPage]="perPage" (pagingDone)="onPagingDoneWrapper($event)">
            <igx-paginator-content>
              <ng-container *ngTemplateOutlet="paginatorContent"></ng-container>
            </igx-paginator-content>
          </igx-paginator>
        }
        <!-- ToDo: Utilizar <igx-action-strip> https://www.infragistics.com/products/ignite-ui-angular/angular/components/treegrid/row-actions -->
        @if (showToolbar()) {
          <igx-grid-toolbar #toolbar>
            @if (isLargeScreen) {
              <igx-grid-toolbar-title>{{ showTitle() ? titleLabel() || i18n.toolbar_title : '' }}</igx-grid-toolbar-title>
            }
            <ng-container *ngTemplateOutlet="toolbarTransactionalButtons"></ng-container>
            @if (showToolbar()) {
              <igx-grid-toolbar-actions>
                @if (allowAdvancedFiltering()) {
                  <igx-grid-toolbar-advanced-filtering [igxButton]="igxButtonToolbarButtonType()" [title]="i18n.export_text"></igx-grid-toolbar-advanced-filtering>
                }
                <ng-content select="[BnextTreeGridToolBarContent]"></ng-content>
                @if (columnHiding() && !isMobilDevice()) {
                  <button
                    [igxButton]="igxButtonToolbarButtonType()"
                    igxRipple
                    [class]="'display-density--' + igxButtonToolbarButtonDensity()"
                    [igxToggleAction]="toggleRefHidingTreeGrid"
                    (click)="openHiddenColumnsTreeGrid()"
                  >
                    <igx-icon>visibility_off</igx-icon>
                    <!--Se le resta 1 para no contar la columna que viene que se le agrego por defecto-->
                    <span>{{ treeGrid.hiddenColumnsCount - 1 }}</span>
                    <span> {{ 'root.common.button.visible' | translate: this }}</span>
                  </button>
                } @else {
                  @if (columnHiding()) {
                    <button [igxButton]="igxButtonToolbarButtonType()" igxRipple [class]="'display-density--' + igxButtonToolbarButtonDensity()" (click)="openDialogHiddenColumns()">
                      <igx-icon>visibility_off</igx-icon>
                      <span>{{ treeGrid.hiddenColumnsCount - 1 }}</span>
                      <span> {{ 'root.common.button.visible' | translate: this }}</span>
                    </button>
                  }
                }
                <div igxToggle #toggleRefHidingTreeGrid="toggle" class="elevation-2 hidden-colums-tree-grid">
                  <span class="title-hidden-tree-grid">{{ i18n.column_visible_title }}</span>
                  <igx-column-actions igxColumnHiding [grid]="treeGrid"></igx-column-actions>
                </div>
                @if (columnPinning) {
                  <igx-grid-toolbar-pinning
                    [title]="i18n.column_pinning_title"
                    [buttonText]="i18n.pinned_columns"
                    [overlaySettings]="getOverlaySettings()"
                    (opened)="onOpenToolbarConfigUi($event)"
                  ></igx-grid-toolbar-pinning>
                }
                @if (exportExcel() || exportCsv()) {
                  @defer (on immediate) {
                    <app-grid-export
                      [allowRangeSelectorColumns]="allowRangeSelectorColumns"
                      [allowShowMoreChilds]="allowShowMoreChilds()"
                      [autoConfig]="exportAutoConfig()"
                      [childDataKey]="childDataKey()"
                      [columnMap]="columnMap"
                      [columns]="columns"
                      [displayDensity]="igxButtonToolbarButtonDensity()"
                      [grid]="grid"
                      [inputExport]="exportColumnsOption()"
                      [totalCount]="totalCount"
                      [inputFormat]="exportFormatOption()"
                      [inputRecords]="exportRecordsOption()"
                      [igxButtonType]="igxButtonToolbarButtonType()"
                      [paging]="paging()"
                      [rangeSelectorDataColumnKey]="rangeSelectorDataColumnKey()"
                      [rangeSelectorDataKey]="rangeSelectorDataKey()"
                      [range]="rangeSelector?.range"
                      [showButtonLabel]="showExportButtonLabel()"
                      [titleLabel]="titleLabel()"
                      [filters]="getFilters()"
                      (startedExporting)="onStartedExporting()"
                      (endedExporting)="onEndedExporting($event)"
                    >
                    </app-grid-export>
                  }
                }
              </igx-grid-toolbar-actions>
            }
          </igx-grid-toolbar>
        }
        <!-- solo igx-tree-grid tiene atributos "igx-tree-grid-group-area" y columna "groupKey" -->
        <!-- area para agrupar exclusiva de igx-tree-grid -->
        @if (isLargeScreen && allowGrouping()) {
          <igx-tree-grid-group-by-area
            [grid]="treeGrid"
            [(expressions)]="groupingExpressions"
            (expressionsChange)="onExpressionsChange($event)"
            [hideGroupedColumns]="true"
          >
          </igx-tree-grid-group-by-area>
        }
        <!-- columna predefinida para agrupar -->
        <igx-column
          [hidden]="groupingExpressions?.length === 0"
          [header]="groupHeaderText"
          [field]="groupKey"
          [width]="'180px'"
          [sortable]="false"
          [resizable]="true"
          [disableHiding]="true"
        >
          <ng-template igxCell let-cell="cell" let-val>
            <ng-template
              *ngTemplateOutlet="
                columnContent;
                context: {
                  $implicit: {
                    groupColumn: true,
                    columnIndex: -1,
                    textWhenEmpty: columnMap[groupKey]?.textWhenEmpty,
                    column: columnMap[groupKey] || null,
                    cell: cell,
                    value: val
                  }
                }
              "
            ></ng-template>
          </ng-template>
        </igx-column>
        <!-- columnas configuradas -->
        @for (col of columns; track col; let i = $index) {
          <ng-container>
            <igx-column
              [cellClasses]="col.cellClasses"
              [cellStyles]="col.cellStyles"
              [headerStyles]="col.headerStyles"
              [headerGroupClasses]="col.headerGroupClasses"
              [headerGroupStyles]="col.headerGroupStyles"
              [dataType]="col.type.dataType"
              [disableHiding]="col.disableHiding"
              [editable]="col.editable"
              [field]="col.field"
              [filterCellTemplate]="emptyFilterTemplate"
              [filterable]="col.filterable"
              [filteringIgnoreCase]="col.filteringIgnoreCase"
              [filters]="col.filters"
              [formatter]="col.formatter"
              [groupable]="allowGrouping() && col.groupable"
              [hasSummary]="col.hasSummary"
              [headerClasses]="col.headerClasses"
              [header]="col.header"
              [hidden]="col.hidden"
              [maxWidth]="col.maxWidth"
              [pipeArgs]="col.pipeArgs"
              [minWidth]="col.minWidth"
              [pinned]="col.pinned"
              [resizable]="col.resizable"
              [selectable]="col.selectable"
              [searchable]="col.searchable"
              [sortable]="col.sortable"
              [sortingIgnoreCase]="col.sortingIgnoreCase"
              [summaries]="col.summaries || null"
              [width]="col.width"
            >
              @if (col.editable && col.customCellEditor) {
                <ng-template igxCellEditor let-cell="cell" let-value>
                  <ng-template
                    *ngTemplateOutlet="
                      customCellEditor;
                      context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, cell: cell, value: value } }
                    "
                  ></ng-template>
                </ng-template>
              }
              <ng-template igxCell let-cell="cell" let-val>
                <ng-template
                  *ngTemplateOutlet="columnContent; context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, cell: cell, value: val } }"
                ></ng-template>
              </ng-template>
              <ng-template igxSummary let-summaryResults>
                <ng-template *ngTemplateOutlet="summaryContent; context: { $implicit: { columnIndex: i, column: col, summaryResults: summaryResults } }"></ng-template>
              </ng-template>
            </igx-column>
          </ng-container>
          <ng-template igxGroupByRow let-groupRow>
            <ng-template
              *ngTemplateOutlet="groupContent; context: { $implicit: { textWhenEmpty: col.textWhenEmpty, columnIndex: i, column: col, groupRow: groupRow } }"
            ></ng-template>
          </ng-template>
          <ng-template igxRowEditText let-rowChangesCount>
            <ng-template *ngTemplateOutlet="rowEditTextTemplate; context: { $implicit: rowChangesCount }"></ng-template>
          </ng-template>
          <ng-template igxRowEditActions let-endRowEdit>
            <ng-template *ngTemplateOutlet="rowEditTextActions; context: { $implicit: endRowEdit }"></ng-template>
          </ng-template>
          @if (showActionStrip || isMobilDevice()) {
            <igx-action-strip #actionstrip class="elevation-2">
              @if (showSubActionStrip(actionstrip.context) && actionstrip.context?.data?.entity_id) {
                @defer (on immediate) {
                  <app-dropdown-menu
                    #dropdownmenu
                    [alwaysVisible]="!!actionStripConfig.dropdownAlwaysVisible"
                    [simplify]="true"
                    [simplifiedCount]="actionStripConfig.dropdownActionLimit || 3"
                    [selectable]="true"
                    [menuOptions]="actionStripConfig.render.menuActionOptions(actionstrip.context)"
                    [availableItemValues]="undefined"
                    [dropdownId]="'grid-dropdown-menu-' + actionStripConfig.render.rowIdentifierKey"
                    [iconsStyle]="'material-icons-filled'"
                    (changed)="toggleMenuActionStrip($event, actionstrip.context, actionStripConfig.render)"
                  >
                  </app-dropdown-menu>
                }
              }
            </igx-action-strip>
          }
        }
        @for (dayColumn of dayColumns; track dayColumn; let i = $index) {
          <igx-column-group
            [cellClasses]="dayColumn.cellClasses"
            [cellStyles]="dayColumn.cellStyles"
            [dataType]="dayColumn.type.dataType"
            [field]="dayColumn.field"
            [filterable]="dayColumn.filterable"
            [formatter]="dayColumn.formatter"
            [hasSummary]="dayColumn.hasSummary"
            [header]="dayColumn.header"
            [hidden]="dayColumn.hidden"
            [pinned]="dayColumn.pinned"
            [pipeArgs]="dayColumn.pipeArgs"
            [resizable]="dayColumn.resizable"
            [sortable]="dayColumn.sortable"
            [width]="dayColumn.width"
          >
            @for (rangeColumn of getRangeSelectorColumns(dayColumn); track rangeColumn; let subIndex = $index) {
              <igx-column
                [cellClasses]="rangeColumn.cellClasses"
                [cellStyles]="rangeColumn.cellStyles"
                [dataType]="rangeColumn.type.dataType"
                [disableHiding]="rangeColumn.disableHiding"
                [editable]="rangeColumn.editable"
                [field]="rangeColumn.field"
                [filterCellTemplate]="emptyFilterTemplate"
                [filterable]="rangeColumn.filterable"
                [filteringIgnoreCase]="rangeColumn.filteringIgnoreCase"
                [filters]="rangeColumn.filters"
                [formatter]="rangeColumn.formatter"
                [groupable]="allowGrouping() && rangeColumn.groupable"
                [hasSummary]="rangeColumn.hasSummary"
                [headerClasses]="rangeColumn.headerClasses"
                [headerGroupClasses]="rangeColumn.headerGroupClasses"
                [headerGroupStyles]="rangeColumn.headerGroupStyles"
                [headerStyles]="rangeColumn.headerStyles"
                [header]="rangeColumn.header"
                [hidden]="rangeColumn.hidden"
                [pipeArgs]="rangeColumn.pipeArgs"
                [maxWidth]="rangeColumn.maxWidth"
                [minWidth]="rangeColumn.minWidth"
                [pinned]="rangeColumn.pinned"
                [resizable]="rangeColumn.resizable"
                [searchable]="rangeColumn.searchable"
                [selectable]="rangeColumn.selectable"
                [sortable]="rangeColumn.sortable"
                [sortingIgnoreCase]="rangeColumn.sortingIgnoreCase"
                [summaries]="rangeColumn.summaries || null"
                [width]="rangeColumn.width"
              >
                @if (rangeColumn.editable && rangeColumn.customCellEditor) {
                  <ng-template igxCellEditor let-cell="cell" let-value>
                    <ng-template
                      *ngTemplateOutlet="
                        customCellEditor;
                        context: {
                          $implicit: { dayColumn: dayColumn, textWhenEmpty: rangeColumn.textWhenEmpty, columnIndex: null, column: rangeColumn, cell: cell, value: value }
                        }
                      "
                    ></ng-template>
                  </ng-template>
                }
                <ng-template igxCell let-cell="cell" let-value>
                  <ng-template
                    *ngTemplateOutlet="
                      columnContent;
                      context: {
                        $implicit: { dayColumn: dayColumn, textWhenEmpty: rangeColumn.textWhenEmpty, columnIndex: null, column: rangeColumn, cell: cell, value: value }
                      }
                    "
                  ></ng-template>
                </ng-template>
                <ng-template igxSummary let-summaryResults>
                  <ng-template
                    *ngTemplateOutlet="
                      summaryContent;
                      context: { $implicit: { dayColumn: dayColumn, columnIndex: null, column: rangeColumn, summaryResults: summaryResults } }
                    "
                  ></ng-template>
                </ng-template>
              </igx-column>
            }
          </igx-column-group>
        }
      </igx-tree-grid>
      @if (isMobilDevice()) {
        <igx-dialog [leftButtonLabel]="'root.common.button.close' | translate: this" (leftButtonSelect)="dialogHiddingColumns().close()">
          <div>
            <igx-column-actions igxColumnHiding [grid]="treeGrid"></igx-column-actions>
          </div>
        </igx-dialog>
      }
    </ng-template>
    <!-- Código reutilizable entre DATA_GRID y TREE_GRID -->
    <ng-template #emptyFilterTemplate igxFilterCellTemplate let-column="column"></ng-template>
    <ng-template #defaultLoadingGrid>
      <div class="igx-grid__loading">
        <igx-circular-bar [indeterminate]="true"></igx-circular-bar>
      </div>
    </ng-template>
    <ng-template #rowEditTextTemplate let-untypedRowChangesCount>
      @if (castAsNumber(untypedRowChangesCount); as rowChangesCount) {
        <ng-template igxRowEditText let-rowChangesCount> {{ i18n.row_edit_text.replace('\{\{rowChangesCount\}\}', '' + rowChangesCount) }} </ng-template>
      }
    </ng-template>
    <ng-template #rowEditTextActions let-endRowEdit>
      <button [igxButton]="'outlined'" igxRowEditTabStop [ngClass]="displayDensityClass" (click)="endRowEdit(false)">{{ i18n.row_edit_cancel }}</button>
      <button [igxButton]="'outlined'" igxRowEditTabStop [ngClass]="displayDensityClass" (click)="endRowEdit(true)">{{ i18n.row_edit_done }}</button>
    </ng-template>
    <ng-template #customCellEditor let-context>
      @if (context.column.render?.valueToText) {
        <ng-template *ngTemplateOutlet="valueToTextEditor; context: { $implicit: context }"></ng-template>
      } @else {
        @if (context.column.render.iconsMap && context.column.render.iconsMap[context.value]; as icon) {
          <ng-template
            *ngTemplateOutlet="customIconContainer; context: { $implicit: { cell: context.cell, icon: icon, column: context.column, value: context.value } }"
          ></ng-template>
        }
      }
    </ng-template>
    <ng-template #valueToTextEditor let-untypedRenderizableColumn>
      @if (castAsRenderizableColumn(untypedRenderizableColumn); as context) {
        @if (context.column.multipleValues && allowValueToTextMultipleValues()) {
          @defer (on immediate) {
            <app-combo
              [name]="context.column.field"
              [label]="context.column.header"
              [optionMap]="context.column.render.labelValues()[context.column.field]"
              [value]="context.cell.value"
              (changeEnds)="onCellEditValueToTextMultipleChange($event, context)"
            ></app-combo>
          }
        } @else {
          @if (context.column.cellTextBox) {
            <igx-input-group [ngClass]="displayDensityClass">
              <input
                igxInput
                type="number"
                [value]="context.column.render.labelValues()[context.column.field]"
                (change)="onSingleValueCellTextChange($event, context)"
                min="1"
                max="12"
              />
            </igx-input-group>
          } @else {
            @if (toggleVisibleFieldValueText(context)) {
              @defer (on immediate) {
                <app-dropdown-search
                  [name]="context.column.field"
                  [displayDensity]="displayDensity"
                  [iconName]="context.column.render.iconName || null"
                  [scrollIntoView]="false"
                  [label]="context.column.header"
                  [value]="context.cell.value && context.cell.value.length ? context.cell.value[0] : null"
                  [blacklistValues]="context.column.render.blacklistValues ? context.column.render.blacklistValues(context.cell) : null"
                  [optionMap]="context.column.render.labelValues()[context.column.field]"
                  (changed)="onCellEditValueToTextSingleChange($event, context.cell)"
                >
                </app-dropdown-search>
              }
            }
          }
        }
      }
    </ng-template>
    <ng-template #summaryContent let-untypedRenderizableSummary>
      @if (castAsRenderizableSummary(untypedRenderizableSummary); as context) {
        @if (context.summaryResults?.length > 0) {
          <div class="igx-grid-summary__item" [class.summary-clickable]="context.column.allowSummaryClick" style="height: 24px" (click)="onSummaryClick(context)">
            <span class="igx-grid-summary__label" title="{{ context.summaryResults[0].label }}">{{ context.summaryResults[0].label }}</span>
            <span class="igx-grid-summary__result" title="{{ context.summaryResults[0].summaryResult }}">{{ context.summaryResults[0].summaryResult }}</span>
          </div>
        } @else {
          <div class="igx-grid-summary__item" [class.summary-clickable]="context.column.allowSummaryClick" style="height: 24px" (click)="onSummaryClick(context)">
            <span class="igx-grid-summary__label" title="{{ context.column.header }}">{{ context.column.header }}</span>
            <span class="igx-grid-summary__result" title="NA">NA</span>
          </div>
        }
      }
    </ng-template>
    <ng-template #columnContent let-untypedRenderizableColumn>
      @let context = castAsRenderizableColumn(untypedRenderizableColumn);
      @let render = context?.column?.render;
      @let column = context?.column;
      @if (context) {
        @if (context.cell.row.data['invalidRowChild']) {
          @if (context.columnIndex === 0) {
            @switch (context.cell.row.data['invalidRowChild']) {
              @case (EnumInvalidRowChildType.EMPTY_CHILDS) {
                {{ i18n.row_empty_childs }}
              }
              @case (EnumInvalidRowChildType.SHOW_MORE_CHILDS) {
                <button [igxButton]="'outlined'" [ngClass]="displayDensityClass" (click)="showMoreChilds(context, context.cell.row.data['invalidRowChildParentId'])">
                  {{ customMoreChildsName() !== null ? customMoreChildsName() : i18n.row_show_more_childs }}
                </button>
              }
            }
          }
        } @else {
          <ng-container [ngTemplateOutlet]="context.column?.avatar || context.column?.hasImage  ? avatarFormat : context.column?.render ? renderFormat : defaultFormat"></ng-container>
        }
        <ng-template #avatarFormat>
          <div
            class="center-column"
            [class.avatar-with-text]="(context.column?.showAvatarNameKey || context.column?.avatarSecundaryKey) && !context.column?.allowManyAvatar"
          >
            @if (!context.column?.allowManyAvatar) {
              <igx-avatar
                [shape]="avatarRoundShape(context.column)"
                [size]="context.column.avatarSize || 'small'"
                [attr.title]="getAvatarName(context)"
                [bgColor]="context.column.avatarColorized ? getAvatarBgColor(context) : null"
                [color]="context.column.avatarColorized ? getAvatarColor(context) : null"
                [src]="getAvatarSrc(context)"
                [initials]="getAvatarInitials(context)"
              >
              </igx-avatar>
            }
            @if ((context.column?.showAvatarNameKey || context.column?.avatarSecundaryKey) && !context.column?.allowManyAvatar) {
              <div class="avatar-text-container">
                @if (context.column?.showAvatarNameKey) {
                  <div class="avatar-text">{{ getAvatarName(context) }}</div>
                }
                @if (context.column?.avatarSecundaryKey) {
                  <div class="avatar-text avatar-secundary-text">
                    {{ getAvatarProperty(context, context.column.avatarSecundaryKey) }}
                  </div>
                }
              </div>
            }
            @if (context.column?.allowManyAvatar) {
              <div>
                @for (avatar of getAvatarInfo(context); track avatar) {
                  <igx-avatar
                    class="group-avatar"
                    [shape]="avatarRoundShape(context.column)"
                    [size]="context.column.avatarSize || 'small'"
                    [attr.title]="getAvatarName(context)"
                    [bgColor]="context.column.avatarColorized ? getAvatarBgColor(context) : null"
                    [color]="context.column.avatarColorized ? getAvatarColor(context) : null"
                    [src]="getAvatarSrcById(avatar.avatarId)"
                    [initials]="getAvatarInitialsByName(avatar.avatarName)"
                  >
                  </igx-avatar>
                }
              </div>
            }
          </div>
        </ng-template>
        <ng-template #renderFormat>
          @if (context.column.render; as render) {
            <div [class.justify-content-center]="context.column?.centered" class="full-width">
              @if (render.textWithAction; as action) {
                <div class="custom-action-container action-cell">
                  <button
                    [igxButton]="'flat'"
                    type="button"
                    igxRipple
                    [igxRippleCentered]="true"
                    [ngClass]="displayDensityClass"
                    (click)="render.callback(context.cell, $event)"
                  >
                    <ng-container [ngTemplateOutlet]="defaultFormatter"></ng-container>
                  </button>
                </div>
              } @else if (render.itemsMap) {
                <div>
                  <ng-container [ngTemplateOutlet]="defaultFormatter"></ng-container>
                </div>
              } @else if (render.action) {
                <div class="custom-action-container action-cell">
                  @if (isIPhoneDevice()) {
                    <button
                      [igxButton]="'flat'"
                      type="button"
                      igxRipple
                      [igxRippleCentered]="true"
                      class="round inner-icon no-label gray"
                      [hidden]="!render.isRendered || !render.isRendered(context.cell)"
                      [ngClass]="displayDensityClass"
                      (click)="render.callback(context.cell, $event)"
                    >
                      <igx-icon class="custom-action" [style.color]="render.action.color" family="material" [title]="render.action.label"
                        >{{ render.action.icon }}
                      </igx-icon>
                    </button>
                  } @else {
                    <button
                      [igxIconButton]="'flat'"
                      type="button"
                      igxRipple
                      [igxRippleCentered]="true"
                      class="round inner-icon no-label gray"
                      [hidden]="!render.isRendered || !render.isRendered(context.cell)"
                      [ngClass]="displayDensityClass"
                      (click)="render.callback(context.cell, $event)"
                    >
                      <igx-icon class="custom-action" [style.color]="render.action.color" family="material" [title]="render.action.label"
                        >{{ render.action.icon }}
                      </igx-icon>
                    </button>
                  }
                </div>
              } @else if (render.iconsMap && render.iconsMap[context.value]) {
                <ng-template
                  *ngTemplateOutlet="
                    customIconContainer;
                    context: { $implicit: { cell: context.cell, icon: render.iconsMap[context.value], column: context.column, value: context.value } }
                  "
                ></ng-template>
              } @else if (render.valueToText) {
                <div class="value-to-text-container value-to-text-cell">
                  <span
                    class="text-ellipsis"
                    title="{{
                      getValueToText(
                        context.column.field,
                        context.value,
                        context.cell,
                        render.labelValues,
                        render.customFunction,
                        context.column.textWhenEmpty,
                        context.column.allowEmptyValue
                      )
                    }}"
                    [innerHTML]="
                      highlightValueToText(
                        context.column.field,
                        context.value,
                        context.cell,
                        render.labelValues,
                        render.customFunction,
                        context.column.textWhenEmpty,
                        null,
                        context.column.allowEmptyValue
                      )
                    "
                  ></span>
                </div>
              } @else if (render.dateToText) {
                <div class="value-to-text-container date-to-text-cell">
                  <span
                    class="text-ellipsis"
                    title="{{ highlightDate(context.value, context.cell, context.column, true) }}"
                    [innerHTML]="highlightDate(context.value, context.cell, context.column)"
                  ></span>
                </div>
              } @else if (render.textToHtml) {
                <div class="value-to-text-container text-to-html-cell">
                  <span class="text-ellipsis" [innerHTML]="highlightText(context, false, true)"></span>
                </div>
              } @else if (render.isDropDownMenu) {
                <div class="dropdown-menu--container drop-down-menu-cell">
                  @defer (on immediate) {
                    <app-dropdown-menu
                      #dropdownmenu
                      [alwaysVisible]="!!context.column.dropdownAlwaysVisible"
                      [displayDensity]="displayDensity"
                      [simplify]="true"
                      [simplifiedCount]="context.column.dropdownActionLimit || 3"
                      [selectable]="true"
                      [menuOptions]="render.menuOptions(context.cell)"
                      [toggleButtonTitle]="render.toggleButtonTitle"
                      [availableItemValues]="undefined"
                      [dropdownId]="rowId(context.cell, context.column, render)"
                      (changed)="toggleMenu($event, context.cell, render)"
                    >
                    </app-dropdown-menu>
                  }
                </div>
              } @else {
                <ng-container [ngTemplateOutlet]="defaultFormatter"></ng-container>
              }
            </div>
            @if (isClearCellAvailable(context) && toggleVisibleFieldValueText(context)) {
              <div class="clear-cell-button" (click)="onClearCellClick(context)">
                <igx-icon>clear</igx-icon>
              </div>
            }
          }
        </ng-template>
        <ng-template #defaultFormat>
          @if (context.column?.tooltip) {
            @if (rowTooltipEnabled()) {
              <div>
                <span class="showTooltipLabel" (click)="showTooltipCell(context)" [id]="getCellTooltipId(context)">{{ tooltipColumnMessage }}</span>
                <ng-template *ngTemplateOutlet="tooltip; context: { $implicit: context.cell.row }"></ng-template>
              </div>
            }
          } @else {
            @if (('' + context.value).includes('[GPS]')) {
              <div>
                <span class="showTooltipLabel" (click)="getMapsUrl(context)">
                  <igx-icon family="material">location_on</igx-icon>
                </span>
              </div>
            } @else {
              <ng-container
                [ngTemplateOutlet]="
                  context.column?.renderAsLink ? linkFormat : context.column?.renderAsMarkdownText ? mardownTextFormat : context.column?.formatter ? formatter : defaultText
                "
              ></ng-container>
            }
          }
        </ng-template>
        <ng-template #defaultFormatter>
          <ng-container
            [ngTemplateOutlet]="
              context.column?.renderAsLink ? linkFormat : context.column?.renderAsMarkdownText ? mardownTextFormat : context.column?.formatter ? formatter : defaultText
            "
          ></ng-container>
        </ng-template>
        <ng-template #linkFormat>
          <a
            [target]="context.column?.target"
            [class.text-ellipsis]="!context.column?.linkHref"
            class="clickable-href link-cell"
            href="{{ renderLink(context.column?.linkHref, context.column?.linkParams, context.cell.row.data) }}"
          >
            {{ context.value }}
          </a>
        </ng-template>
        <ng-template #mardownTextFormat>
          <div [innerHTML]="markdownTextFormatter(context.value)"></div>
        </ng-template>
        <ng-template #formatter>
          @if (column?.allowCellClick) {
            @if (context.value) {
              <div class="clickable-column formatter-click-enabled-cell" (click)="onCellClick(context)">
                <span [innerHTML]="highlightDefaultFormatter(context)"></span>
              </div>
            } @else {
              <span class="text-ellipsis default-text-cell" [innerHTML]="getTreeGridDefaultValue(context)"></span>
              @if (column?.showDetail && context.value?.length > 250) {
                <igx-icon (click)="showDetail(context)">open_in_full</igx-icon>
              }
            }
          } @else {
            @let itemsMap = render?.itemsMap;
            @if (itemsMap) {
              @if (column?.switchPicker) {
                <div>
                  <igx-switch
                    [checked]="context.value === null || context.value === undefined ? column?.checked : evalValue(context.value)"
                    (change)="onSwitchColumnChange($event, context.cell)"
                  ></igx-switch>
                </div>
              } @else {
                @let valItem = itemsMap?.[context.value];
                @let val = valItem?.[render.labelKey] ?? column.textWhenEmpty ?? context.value ?? '';
                <span class="igx-group-label__text">{{ val }}</span>
              }
            } @else {
              @if (column?.switchPicker) {
                <div>
                  <igx-switch
                    [checked]="context.value === null || context.value === undefined ? column?.checked : evalValue(context.value)"
                    (change)="onSwitchColumnChange($event, context.cell)"
                  ></igx-switch>
                </div>
              } @else {
                <span class="formatter-click-disabled-cell" [innerHTML]="highlightDefaultFormatter(context)"></span>
              }
            }
          }
        </ng-template>
        <ng-template #defaultText>
          <span class="text-ellipsis default-text-cell" [innerHTML]="getTreeGridDefaultValue(context)"></span>
          @if (context.column?.showDetail && context.value?.length > 250) {
            <igx-icon (click)="showDetail(context)">open_in_full</igx-icon>
          }
        </ng-template>
        <ng-template #custom>
          <ng-container [ngTemplateOutlet]="context.column?.avatar ? avatarFormat : context.column?.render ? renderFormat : defaultFormat"></ng-container>
        </ng-template>
      }
    </ng-template>
    <ng-template #groupContent let-untypedRenderizableGroup>
      @if (castAsRenderizableGroup(untypedRenderizableGroup); as renderizableGroup) {
        @if (columnMap[renderizableGroup.groupRow.expression?.fieldName]; as column) {
          <div class="igx-group-label">
            <igx-icon family="material" class="igx-group-label__icon">group_work</igx-icon>
            <span class="igx-group-label__column-name"> {{ grid?.getColumnByName(renderizableGroup.groupRow.expression.fieldName).header }} : </span>
            <div>
              @switch (column.type.key) {
                @case ('text') {
                  <div>
                    <span class="igx-group-label__text">
                      @if (column.render?.valueToText) {
                        {{
                          getValueToText(
                            column.field,
                            renderizableGroup.groupRow.value,
                            null,
                            column.render.labelValues,
                            column.render.customFunction,
                            column.textWhenEmpty,
                            column.allowEmptyValue
                          )
                        }}
                      } @else {
                        {{ renderizableGroup.groupRow.value ?? column.textWhenEmpty ?? '' }}
                      }
                    </span>
                  </div>
                }
                @case ('items') {
                  <div>
                    @if (column.render; as render) {
                      @if (render?.itemsMap) {
                        <span class="igx-group-label__text">{{ getItemMapToText(render, renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? renderizableGroup.groupRow.value ?? '' }}</span>
                      } @else {
                        <span class="igx-group-label__text">{{ column.formatter(renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? '' }}</span>
                      }
                    } @else {
                      <span class="igx-group-label__text">{{ column.formatter(renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? '' }}</span>
                    }
                  </div>
                }
                @case ('icons') {
                  <div>
                    @if (column.render; as render) {
                      @if (render.iconsMap[renderizableGroup.groupRow.value]; as icon) {
                        <span class="igx-group-label__text">{{ icon[render.labelKey] ?? column.textWhenEmpty ?? renderizableGroup.groupRow.value ?? '' }}</span>
                      } @else {
                        <span class="igx-group-label__text">{{ column.formatter(renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? '' }}</span>
                      }
                    } @else {
                      <span class="igx-group-label__text">{{ column.formatter(renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? '' }}</span>
                    }
                  </div>
                }
                @default {
                  <div>
                    @if (isDate(renderizableGroup.groupRow.value)) {
                      <span class="igx-group-label__text"> {{ column.formatter(renderizableGroup.groupRow.value) ?? column.textWhenEmpty ?? '' }}</span>
                    } @else {
                      <span class="igx-group-label__text">{{ renderizableGroup.groupRow.value ?? column.textWhenEmpty ?? '' }}</span>
                    }
                  </div>
                }
              }
            </div>
            @if (allowShowGroupByCount()) {
              <igx-badge [value]="renderizableGroup.groupRow.records.length" class="igx-group-label__count-badge"></igx-badge>
            }
          </div>
        } @else {
          Grouping by "{{ renderizableGroup.groupRow.column?.header }}" and "{{ renderizableGroup.column?.header }}".
        }
      }
    </ng-template>
    <ng-template #toolbarTransactionalButtons>
      @if (transactional()) {
        <button [ngClass]="displayDensityClass" [igxButton]="igxButtonToolbarButtonType()" igxRipple [igxRippleCentered]="true" [disabled]="!hasTransactions" (click)="commit()">
          <igx-icon family="material">save</igx-icon>
          {{ i18n.transaction_commit }}
        </button>
      }
      @if (transactional()) {
        <button [igxButton]="igxButtonToolbarButtonType()" igxRipple [igxRippleCentered]="true" [disabled]="!undoEnabled" [ngClass]="displayDensityClass" (click)="undo()">
          <igx-icon family="material">undo</igx-icon>
          {{ i18n.transaction_undo }}
        </button>
      }
      @if (transactional()) {
        <button [igxButton]="igxButtonToolbarButtonType()" igxRipple [igxRippleCentered]="true" [disabled]="!redoEnabled" [ngClass]="displayDensityClass" (click)="redo()">
          <igx-icon family="material">redo</igx-icon>
          {{ i18n.transaction_redo }}
        </button>
      }
    </ng-template>
    <ng-template #paginatorContent>
      <div class="pagination-bar">
        <div class="pagination-page-buttons">
          <button [disabled]="isFirstPage" (click)="firstPage()" class="pagination-button">
            <igx-icon family="material">first_page</igx-icon>
          </button>
          <button [disabled]="isFirstPage" (click)="previousPage()" class="pagination-button">
            <igx-icon family="material">chevron_left</igx-icon>
          </button>
          <span>{{ page }} {{ i18n.pageOf }} {{ totalPages }}</span>
          <button [disabled]="isLastPage" (click)="nextPage()" class="pagination-button">
            <igx-icon family="material">chevron_right</igx-icon>
          </button>
          <button [disabled]="isLastPage" (click)="lastPage()" class="pagination-button">
            <igx-icon family="material">last_page</igx-icon>
          </button>
        </div>
        @if (isLargeScreen) {
          <div class="pagination-per-page-buttons">
            <div class="pagination-per-page-label">{{ i18n.records_by_page }}</div>
            <div class="pagination-per-page-button">
              @for (val of perPageValues; track val) {
                <button
                  [class.selected]="val === perPage"
                  [igxRippleCentered]="true"
                  [igxIconButton]="'flat'"
                  igxRipple
                  [ngClass]="displayDensityClass"
                  (click)="setPerPage(val)"
                >
                  {{ val }}
                </button>
              }
            </div>
          </div>
        }
      </div>
    </ng-template>
    <ng-template #customIconContainer let-context>
      <div class="custom-icon-container icons-map-cell">
        @if (context.column.render?.callback || context.column.editable) {
          <button
            [igxIconButton]="'flat'"
            type="button"
            igxRipple
            [igxRippleCentered]="true"
            class="grid-button"
            [ngClass]="displayDensityClass"
            (click)="onIconCellClick(context.cell)"
          >
            <ng-template *ngTemplateOutlet="iconContentTemp; context: { $implicit: context }"></ng-template>
          </button>
        } @else {
          <ng-template *ngTemplateOutlet="iconContentTemp; context: { $implicit: context }"></ng-template>
        }
      </div>
    </ng-template>
    <ng-template #iconContentTemp let-context>
      @if (context.icon.family !== 'material') {
        <igx-icon
          class="custom-icon"
          [style.color]="context.icon.color"
          [family]="context.icon.family"
          [title]="getIconTitle(context.cell, context.icon, context.column, context.value)"
          [name]="context.icon.name"
        >
          {{ context.icon.name }}
        </igx-icon>
      }
      @if (context.icon.family === 'material') {
        <igx-icon
          class="custom-icon"
          [style.color]="context.icon.color"
          family="material"
          [title]="getIconTitle(context.cell, context.icon, context.column, context.value)"
        >
          {{ context.icon.name }}
        </igx-icon>
      }
    </ng-template>
    <ng-template #tooltip let-row>
      <dx-tooltip position="bottom" (onHiding)="hideTooltip()" [visible]="isVisibleTooltipCell(row)" [target]="cellTooltipTargetId">
        <div class="tooltip-main-wrapper">
          <div class="cell-tooltip-close-button">
            <span>{{ tooltipTitle }}</span>
            <igx-icon (click)="hideTooltip()">close</igx-icon>
          </div>
          <ng-container *ngTemplateOutlet="tooltipTemplate(); context: { $implicit: row }"></ng-container>
        </div>
      </dx-tooltip>
    </ng-template>
  </div>
}
