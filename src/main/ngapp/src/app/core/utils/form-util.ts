import { type AbstractControl, type FormGroup, type UntypedFormGroup, Validators } from '@angular/forms';
import type { DataMap } from './data-map';
import type { InvalidFormStatus } from './form.interfaces';

export interface InvalidForms {
  status: boolean;
  forms: InvalidFormStatus[];
}
export interface IFormUtil {
  markAsTouched: (value: any, form: FormGroup, controlName: string)  => void;
  isValidForms: (form: UntypedFormGroup[], excludedFields?: string[], updateValidity?: boolean) => InvalidForms;
  isValid: (form: UntypedFormGroup, excludedFields?: string[], updateValidity?: boolean) => InvalidFormStatus;
  isFieldValid: (fieldName: string, form: UntypedFormGroup, touched: boolean) => boolean;
  invalidFields: (form: UntypedFormGroup, excludedFields?: string[]) => string[];
  setControlValue: (key: string, value: any, form: UntypedFormGroup) => void;
  isControlValid: (key: string, form: UntypedFormGroup) => boolean;
}

const DOM_PARSER = new DOMParser();
export const FormUtil: IFormUtil = {
  isValidForms: (forms: UntypedFormGroup[], excludedFields?: string[], updateValidity = false) => {
    const isValid: InvalidForms = {
      status: true,
      forms: []
    };
    for (const form of forms) {
      isValid.forms.push(FormUtil.isValid(form, excludedFields, updateValidity));
      if (isValid.status && !isValid.forms[isValid.forms.length - 1].status) {
        isValid.status = false;
      }
    }
    return isValid;
  },
  markAsTouched: (value: any, form: FormGroup, controlName: string)  => {
    const conttrol = form.controls[controlName];
    conttrol?.setValue(value);
    conttrol?.setValidators([Validators.required]);
    conttrol?.markAsTouched();
    conttrol?.updateValueAndValidity();
  },
  isValid: (form: UntypedFormGroup, excludedFields?: string[], updateValidity = false) => {
    const formStatus: InvalidFormStatus = { status: false, controls: [] };
    if (updateValidity) {
      for (const k in form.controls) {
        if (!form.controls.hasOwnProperty(k) || (excludedFields && excludedFields.indexOf(k) !== -1)) {
          continue;
        }
        form.get(k).updateValueAndValidity();
      }
    }
    if (!form.valid) {
      for (const k in form.controls) {
        if (!form.controls.hasOwnProperty(k) || (excludedFields && excludedFields.indexOf(k) !== -1)) {
          continue;
        }
        const control: AbstractControl = form.get(k);
        if (!control.valid) {
          console.log(`-> ${k}: ${control.valid}`);
          control.markAllAsTouched();
          control.updateValueAndValidity();
          formStatus.controls.push(k);
        }
      }
      formStatus.status = false;
      return formStatus;
    }
    formStatus.status = true;
    return formStatus;
  },
  isFieldValid: (fieldName: string, form: UntypedFormGroup, touched = false) => {
    if (!form) {
      return false;
    }
    const field = form.get(fieldName);
    if (field) {
      return !field.valid && (touched || field.touched);
    }
    console.error('Unable to validate field: ', fieldName);
    return false;
  },
  invalidFields: (form: UntypedFormGroup, excludedFields?: string[]) => {
    if (!form.valid) {
      const invalid = [];
      for (const k in form.controls) {
        if (!form.controls.hasOwnProperty(k) || (excludedFields && excludedFields.indexOf(k) !== -1)) {
          continue;
        }
        const control = form.get(k);
        if (!control.valid) {
          invalid.push(k);
        }
      }
      return invalid;
    }
    return [];
  },
  setControlValue: (key: string, value: any, form: UntypedFormGroup) => {
    const patch: DataMap = {};
    patch[key] = value;
    form.patchValue(patch);
  },
  isControlValid: (key: string, form: UntypedFormGroup) => {
    if (!form.valid) {
      const control: AbstractControl = form.get(key);
      if (!control.valid) {
        console.log(`-> ${key}: ${control.valid}`);
        control.markAllAsTouched();
        control.updateValueAndValidity();
      }
      return false;
    }
    return true;
  }
};
export function stripHtml(html: string): string {
  if (html === null || html === undefined || html === '') {
    return html;
  }
  const doc = DOM_PARSER.parseFromString(html, 'text/html');
  return doc.body.textContent || '';
}
