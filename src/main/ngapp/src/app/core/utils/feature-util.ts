/* eslint-disable max-len */
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ConfigApp } from '../local-storage/config-app';
import type { DataMap } from './data-map';

export enum FeatureFlag {
  ACTIVITY_ADD_GROUP = 'ACTIVITY_ADD_GROUP', // <-- Funcionalidad agregar grupos de actividades
  ACTIVITY_ALLOW_PAST_DATES = 'ACTIVITY_ALLOW_PAST_DATES', // Funcionalidad para permitir crear actividades con fechas en el pasado
  ACTIVITY_NO_IMPLEMENTER = 'ACTIVITY_NO_IMPLEMENTER', // <--Funcionalidad para solicitar cambios a actividades desde la pantalla de pendientes
  ACTIVITY_PLAN = 'ACTIVITY_PLAN', // <--Funcionalidad para asignación de actividades a modo de planeación con visibilidad de cargas de trabajo por recurso
  ACTIVITY_PLAN_MULTIPLE_IMPLEMENTERS = 'ACTIVITY_PLAN_MULTIPLE_IMPLEMENTERS', // <--Funcionalidad para asignación de actividades a modo de planeación con visibilidad de cargas de trabajo por recurso
  ACTIVITY_PLAN_NO_VERIFICATION = 'ACTIVITY_PLAN_NO_VERIFICATION', // <--Funcionalidad para asignar responsables, sin verificadores desde planeación
  ACTIVITY_STEAL_FROM_PARENT = 'ACTIVITY_STEAL_FROM_PARENT', // <--Funcionalidad para mover SUBTAREAS de un padre a otro
  ACTIVITY_TYPE_SYSTEM_LINKS = 'ACTIVITY_TYPE_SYSTEM_LINKS', // <--Funcionalidad con la intención es utilizarlo como se usa en GITEA, cualquier cosa que diga "gato + número" es interpretado como una liga al reqtracker
  ACTIVITY_WORKLOAD = 'ACTIVITY_WORKLOAD', // <--Funcionalidad para consultar el calendario de pendientes por persona
  APE_PENDING_REQUEST = 'APE_PENDING_REQUEST', // <--Funcionalidad para editar el tamaño de los widgets en la pantalla de pendientes
  APE_PENDING_WIDGET_RESIZE = 'APE_PENDING_WIDGET_RESIZE', // <--Funcionalidad para asignar encuestas directamente a usuarios del sistema
  COMPLAINT_REPORTS = 'COMPLAINT_REPORTS',
  HAPPYORNOT_DASHBOARD_DATEFILTERS = 'HAPPYORNOT_DASHBOARD_DATEFILTERS', // <--Funcionalidad para filtrar los resultados del dashboard por fecha
  HAPPYORNOT_USER_SURVEY = 'HAPPYORNOT_USER_SURVEY', // <--Funcionalidad para filtrar los resultados del dashboard por fecha
  MENU_ENABLE_GESTURES = 'MENU_ENABLE_GESTURES', // <--Funcionalidad para habilitar la funcionalidad de gestos del menú para dispositivos móviles
  PLANNER_DRAFT_MODE = 'PLANNER_DRAFT_MODE', // <--Funcionalidad para guardar proyectos en modo "borrador" y liberarlos despúes
  SAVE_REPPLICA_CONNECTION = 'SAVE_REPPLICA_CONNECTION', // <-- Boton de "Guardar" en la pantalla de conexión a réplica
  PLANNER_MODULE = 'PLANNER_MODULE',
  FORM_MIGRATIONS_UI = 'FORM_MIGRATIONS_UI', // <--- Pantalla de migraciones de campos entre versiones de formularios
  SLIM_REPORTS_ADI = 'SLIM_REPORTS_ADI', // <--- Desarrollo para OXXO de nombre "Poliza ADI"
  SLIM_REPORTS = 'SLIM_REPORTS', // <--- Pantalla de alta de reportes de formularios
  TIMESHEET_SCHEDULER = 'TIMESHEET_SCHEDULER', // <--Funcionalidad para abrir un calendario similar al del timesheet de Bnext
  TIMESHEET_SCHEDULER_PICKER = 'TIMESHEET_SCHEDULER_PICKER', // <--Funcionalidad modificar horas de TS desde el calendario
  TIMESHEET_STOP_WATCH = 'TIMESHEET_STOP_WATCH', // <--Funcionalidad para iniciar reloj contabilizando tiempo
  USER_MAIN_GROUP = 'USER_MAIN_GROUP' // <--Funcionalidad para guardar un grupo predeterminado por usuario, se inactivo en favor de "grupos por depto"
}

export type FeatureMap = Record<FeatureFlag, boolean>;

export const FEATURES: FeatureMap = {
  ACTIVITY_ADD_GROUP: false,
  ACTIVITY_ALLOW_PAST_DATES: false,
  ACTIVITY_NO_IMPLEMENTER: true,
  ACTIVITY_PLAN: true,
  ACTIVITY_PLAN_MULTIPLE_IMPLEMENTERS: true,
  ACTIVITY_PLAN_NO_VERIFICATION: false,
  ACTIVITY_STEAL_FROM_PARENT: true,
  ACTIVITY_TYPE_SYSTEM_LINKS: true,
  ACTIVITY_WORKLOAD: true,
  APE_PENDING_REQUEST: false,
  APE_PENDING_WIDGET_RESIZE: true,
  COMPLAINT_REPORTS: false,
  FORM_MIGRATIONS_UI: false,
  SAVE_REPPLICA_CONNECTION: false,
  HAPPYORNOT_DASHBOARD_DATEFILTERS: true,
  HAPPYORNOT_USER_SURVEY: false,
  MENU_ENABLE_GESTURES: true,
  PLANNER_DRAFT_MODE: false,
  PLANNER_MODULE: true,
  SLIM_REPORTS_ADI: true,
  SLIM_REPORTS: true,
  TIMESHEET_SCHEDULER: true,
  TIMESHEET_SCHEDULER_PICKER: true,
  TIMESHEET_STOP_WATCH: true,
  USER_MAIN_GROUP: false
};

export const disabledModules: Module[] = [Module.MEETING, Module.FIVES, Module.PROJECT, Module.POLL, Module.METER, Module.MEETING];
export const excludedServices: DataMap<FeatureFlag> = {
  intBReporteReunion: null,
  intBReporteEncuesta: null,
  intBReporteIndicador: null,
  intBReporteCuestionario: null,
  intBReporteProyecto: null,
  activityMaxOpenTimeIgnore: null,
  catalogsMeetings: null,
  catalogsSurveys: null,
  catalogsMeters: null,
  catalogsComplaints: null,
  catalogsProjects: null,
  catalogsFiveS: null,
  tsEditBusinessUnit: null,
  tsEditBusinessUnitDep: null,
  honSurveyFiller: FeatureFlag.HAPPYORNOT_USER_SURVEY,
  plannerActivityModifyVerifier: null,
  plannerActivityModifyVerification: null,
  plannerActivitySuperVerifier: null,
  plannerActivityMaxOpenTimeIgnore: null,
  plannerActivityModifyRecurrence: null,
  plannerActivityDeleteRecurrence: null,
  honSurveyViewAll: null
};

export function isFeatureAvailable(featureFlag: FeatureFlag): boolean {
  if (typeof featureFlag === 'undefined') {
    return true;
  }
  return FEATURES[featureFlag];
}

export function isModuleAvailable(module: Module | string): boolean {
  if (!module) {
    return false;
  }
  const upperModule = module.toUpperCase();
  return !disabledModules.find((m) => m === upperModule);
}

export function isItemEnabledByApp(enabledByApp: boolean, enabledByAppField: string, path: string): boolean {
  if (!enabledByApp) {
    return true;
  }
  if (!enabledByAppField) {
    console.error(`Missing enabledByAppField config for ${path}`);
    return false;
  }
  const config = ConfigApp.getValue();
  if (!config) {
    return true;
  }
  return config[enabledByAppField] === 1;
}

export function isServiceAvailable(service: string): boolean {
  if (typeof service === 'undefined') {
    return true;
  }
  const excluded = excludedServices[service];
  if (typeof excluded === 'undefined') {
    return true;
  }
  if (excluded === null) {
    return false;
  }
  for (const featureName in FEATURES) {
    if (excluded === featureName && !isFeatureAvailable(featureName)) {
      return false;
    }
  }
  return true;
}
