export enum OidcProvider {
  OKTA = 1,
  MICROSOFT = 2
}

export interface ConfigAppEntity {
  systemId: string;
  welcomeMessage: string;
  enableTimework: number;
  trackLocation: number;
  enableRegistration: number;
  regionCatalogEnabled: number;
  exchangeRateConversorEnabled: number;
  zoneCatalogEnabled: number;
  formReportMaxColumns: number;
  timeworkUrl: string;
  oidcEnabled: number;
  oidcProvider: number;
  timeworkMails: string;
  timeLimitToModifyTimesheet: Date;
  welcomeBgId: number;
  systemTimezone: string;
  enabledLandingPage: number;
  landingPageUrl: string;
  enableLoginForm: number;
  aiEnabled: boolean;
}
