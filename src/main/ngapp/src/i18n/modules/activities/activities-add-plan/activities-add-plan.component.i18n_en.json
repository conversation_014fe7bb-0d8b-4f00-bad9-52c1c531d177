{"tab-plan": "Planner", "tab-priorities": "Priorities", "tab-workload-full": "Pre-assigned workload", "tab-workload-selected": "Selected workload", "tab-workload-daily": "Workload by day ", "tab-workload-commit": "Workload to commit", "commit-workload": "Uncommited workload", "current-workload": "Current workload", "pre-assigned-workload": "Pre-assigned workload", "activities-count-and-sum": "activities, adding up", "appointment-range": "Starts ${startDate}, ending ${endDate}", "task-count-and-sum": "tasks, adding up", "participants-assign": "Your team", "task-count": "tasks", "empty-task-assignement": "A date range and a team should be selected before opening this tab", "empty-backlog": "You must select at least one activity type in planning tab and add participants", "failedImportLabel": "The \"ID\" column is a required value, please check the data and try again.", "failedImportMissingKeyLabel": "There are {numberRecords} invalid ID(s) in the selected Excel, please check the data and try again.", "importConfirmationLabel": "{numberRecords} record(s) have been readed. Do you want to import these record(s)?", "missingImportDataLabel": "There are {numberRecords} invalid ID(s) in the selected Excel, please check the following data and try again.", "missingImportDataFromDataSourceLabel": "There are {numberRecords} invalid record(s) in the selected Excel, please check the data in the 'Records to select' section and try again.", "failedImportMissingResponsibleLabel": " There are {numberRecords} invalid responsible(s) in the selected Excel, please check the data and try again.", "invalidResponsibleDataLabel-one": "The selected responsible isn´t part from selected group from planning, it´s not possible import the information.", "invalidResponsibleDataLabel": "The selected responsibles aren´t part from selected group from planning, it´s not possible import the information.", "missingResponsibleDataLabel": " The \"Responsible\" is a required value, please check the data and try again.", "importedExcelSuccess": "{numberRecords} imported record(s).", "rowNumbers": "Number of the row(s)", "rowIds": "ID(s)", "missingStartDateLabel": "The \"Starts\" column is a required value, please check the data and try again.", "failedImportStartDateLabel": "There are {numberRecords} invalid in \"Starts\" column in the selected Excel, please check the data and try again.", "missingEndDateLabel": "The \"Ends\" column is a required value, please check the data and try again.", "failedImportEndDateLabel": "There are {numberRecords} invalid  in \"Ends\" column in the selected Excel, please check the data and try again.", "missingPlannedHoursLabel": "The \"Estimated hours\" column is a required value, please check the data and try again.", "failedImportPlannedHoursLabel": " There are {numberRecords} invalid in \"Estimated hours\" column in the selected Excel, please check the data and try again.", "failedImportMissingVerifierLabel": " There are {numberRecords} invalid verifier(s) in the selected Excel, please check the data and try again.", "invalidVerifierDataLabel-one": "The selected verifier isn´t part from selected group from planning, it´s not possible import the information.", "invalidVerifierDataLabel": "The selected verifiers aren´t part from selected group from planning, it´s not possible import the information.", "missingVerifierDataLabel": " The \"Verifier\" is a required value, please check the data and try again.", "readonlyVerifierDataLabel": "The activity does not allow you to select values for the \"Verifier\" column. There are invalid {numberRecords} records(s) in the selected Excel.", "empty-summary": "Workloads should be assing before going to this tab", "single-implementer": "Only one responsible per activity", "plan-scope": "Only selected participants are planned", "plan-scope-hint": "When checked, activities without a pre-assigned person will be excluded.", "author-as-verifier": "Automatically send activities to verify to author", "team": "Team", "dialog-config-close": "Close", "dialog-config-title": "Configuration", "dialog-config-persist-progress": "Save progress automatically", "priorities-assign": "Priorities pre-assigned", "hoursPerDay": "Estimaded hours per day", "activity-type": "Activity type", "availableColumnsConfig": "Activity available fields", "breakDownTasks": "Break down tasks", "preferences": "Configuration", "preferences-detail": "Choose the maximum number of records to display", "display-completed-activities": "Display completed activities", "select-priorities-to-assign": "Select pre-assignment to plan dates.", "success-save-preworkloadChanges": "Pre-asignement saved.", "i18n": {"confirm-dates-restart": "All the activities dates will be setted the same as the \"PLANNER\" tab. Do you really want to continue?", "confirm-changes": "You're modifiying an activity already planned. Capture the change reason to continue.", "save-success": "The load assignment was saved successfully, which screen do you want to go now?", "change-save-success": "Changes have been saved", "change-save-fail": "There was an error with the field.", "invalidNumber": "The max value for the field is \"999\"", "change-is-single-checked-true": "It is only possible to configure one person in charge per activity", "change-is-single-checked-false": "You can now configure multiple assignees per activity", "change-author-as-verifier-to-true": "A verification will be created to the creator of all activities", "change-author-as-verifier-to-false": "Activities will be created without verification", "activity-to-define": "To define", "assigned-fill-required": "It is necessary to select at least one record to authorize.", "appointmentForm": {"activity-config-subtitle": "In this section you can modify the planned dates and hours of the activity.", "activity-view-subtitle": "In this section you can view the planned dates and hours of the activity.", "client": "Client", "responsible": "Responsible", "startDate": "Start", "endDate": "End", "estimatedHours": "Estimated hours (planned)", "author": "Author"}, "menuLabels": {"save": "Save", "refresh": "Refresh dates", "addParticipant": "Add participant", "addComment": "Add comment", "addDocument": "Add document", "back": "Back", "addFavorite": "Add to favorites", "settings": "Settings"}, "week": "Week {number} of {year}"}}