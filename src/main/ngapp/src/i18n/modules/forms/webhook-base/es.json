{"fixedFieldsLabels": {"header": "Campos fijos", "placeholder": "Seleccione campos fijos", "help": "Los campos mostrados son fijos. No son editables, pertenecen a la versión actual del formulario.", "label": "Clic para seleccionar los campos fijos que desea mostrar"}, "flexiFieldsLabels": {"header": "Etapas del formulario", "placeholder": "Seleccione campos del formulario", "help": "Los campos mostrados pertenecen al formulario y son llenados por el usuario.", "label": "Clic para seleccionar los campos del formulario que desea mostrar"}, "fields": {"status": "Estado", "code": "Clave", "description": "<PERSON><PERSON><PERSON><PERSON>", "details": "Detalle", "documentVersion": "Versión del formulario", "createdByUserName": "<PERSON><PERSON><PERSON>", "createdDate": "Creación", "lastModifiedDate": "Última modificación", "stage": "Etapa", "stageDescription": "Sección o firma", "masterId": "MasterId", "url": "Url", "headers": "Headers (fila por header de la forma CABECERA: VALOR)"}, "i18n": {"ADD_PRINTING_FORMAT": "Webhook", "BACK_PRINTING_FORMAT": "Regresar", "title": "Webhook", "title-list": "Webhook", "general": "Generales", "json": "JSON (propiedad data obligatoria, no borrar)", "variableFields": "Campos variables para el formato", "no-wysiwyg-content": "Utilice el botón \"Editar\" para agregar contenido", "save-success": "El formato se guardó exitosamente.", "status-dialog-message": "¿Está seguro que desea cambiar el estado de este formato?", "accept-dialog-message": "El estado del formato fue modificado correctamente", "html-format-is-required": "El formato de impresión es obligatorio", "printing-format-deleted-success": "Se elimino el formato correctamente.", "printing-format-deleted-failed": "No se pudo eliminar el formato.", "fixedFields": {"REQUESTOR": "Solicitante", "STAGE": "Etapa", "PROGRESS": "Avance", "CODE": "Clave", "STATUS_PROGRESS": "Estado + Avance", "LAST_MODIFIED_DATE": "Ultima fecha de modificación", "CREATED_DATE": "Fecha de modificación", "STATUS": "Estado", "FILLING_DATE": "<PERSON><PERSON>", "BUSINESS_UNIT": "{Facility}", "BUSINESS_UNIT_DEPARTMENT": "{Department}", "AREA": "{Area}", "REQUEST": "Razón", "OUTSTANDING_SURVEYS": "Descripción", "AREA_CUSTOM_FIELD1": "Campo personalizado de área 1", "AREA_CUSTOM_FIELD2": "Campo personalizado de área 2", "AREA_CUSTOM_FIELD3": "Campo personalizado de área 3", "AREA_CUSTOM_FIELD4": "Campo personalizado de área 4", "AREA_CUSTOM_FIELD5": "Campo personalizado de área 5", "AREA_CUSTOM_FIELD6": "Campo personalizado de área 6", "AREA_CUSTOM_FIELD7": "Campo personalizado de área 7", "AREA_CUSTOM_FIELD8": "Campo personalizado de área 8", "AREA_CUSTOM_FIELD9": "Campo personalizado de área 9", "AREA_CUSTOM_FIELD10": "Campo personalizado de área 10", "AREA_CUSTOM_FIELD11": "Campo personalizado de área 11", "AREA_CUSTOM_FIELD12": "Campo personalizado de área 12", "AREA_CUSTOM_FIELD13": "Campo personalizado de área 13", "AREA_CUSTOM_FIELD14": "Campo personalizado de área 14", "AREA_CUSTOM_FIELD15": "Campo personalizado de área 15", "AREA_CUSTOM_FIELD16": "Campo personalizado de área 16", "AREA_CUSTOM_FIELD17": "Campo personalizado de área 17", "AREA_CUSTOM_FIELD18": "Campo personalizado de área 18", "AREA_CUSTOM_FIELD19": "Campo personalizado de área 19", "AREA_CUSTOM_FIELD20": "Campo personalizado de área 20", "AREA_CUSTOM_FIELD21": "Campo personalizado de área 21", "AREA_CUSTOM_FIELD22": "Campo personalizado de área 22", "AREA_CUSTOM_FIELD23": "Campo personalizado de área 23", "AREA_CUSTOM_FIELD24": "Campo personalizado de área 24", "AREA_CUSTOM_FIELD25": "Campo personalizado de área 25", "AREA_CUSTOM_FIELD26": "Campo personalizado de área 26", "REGION_DESC": "Nombre región", "REGION_CODE": "Código regi<PERSON>"}}}