{"i18n": {"titleLabel": "Upload / Download", "titleLabelRule": "Rules", "column": {"bulkDate": "Date and Time", "bulkDescription": "Description", "bulkUserName": "User", "originalFileName": "Uploaded File", "resultFileName": "Result File", "errorCount": "Error Count"}, "columns2": {"createdAt": "Change Date", "newFieldDescription": "Name", "oldFieldDescription": "Previous Name", "previousRuleCount": "Previous Rules Count", "currentRuleCount": "Previous Rules Count", "equalRulesCount": "Equal Rules Count", "ruleDiffCount": "Change Count", "createdBy": "Author", "changeType": "Change Type", "create": "Create", "update": "Update", "delete": "Delete"}, "newRule": "New rule", "oldRule": "Previous rule"}}