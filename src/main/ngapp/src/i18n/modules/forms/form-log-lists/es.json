{"i18n": {"titleLabel": "Carga / Descarga", "titleLabelRule": "Reg<PERSON>", "column": {"bulkDate": "<PERSON><PERSON> y hora", "bulkDescription": "Descripción", "bulkUserName": "Usuario", "originalFileName": "Archivo cargado", "resultFileName": "Archivo resultado", "errorCount": "Cantidad de errores"}, "column2": {"createdAt": "<PERSON><PERSON> de camb<PERSON>", "newFieldDescription": "Nombre", "oldFieldDescription": "Nombre previo", "previousRuleCount": "Conteo de reglas previas", "currentRuleCount": "<PERSON><PERSON><PERSON> de reglas finales", "equalRulesCount": "Conteo de reglas iguales", "ruleDiffCount": "Conteo de cambios", "createdBy": "Autor", "changeType": "Tipo de cambio", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizado", "delete": "<PERSON><PERSON><PERSON>"}, "newRule": "Nueva regla", "oldRule": "Regla previa"}}