// TypeScript config file that matches all source files in the project. This file is read by
// IDEs and TSLint. For IDEs it ensures that `experimentalDecorator` warnings are not showing up.
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "",
    "checkJs": true,
    "declaration": false,
    "experimentalDecorators": true,
    "esModuleInterop": true,
	"isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,
    "importHelpers": true,
    "strictNullChecks": false,
    "strictPropertyInitialization": false,
    "noImplicitAny": false,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "outDir": "./dist/out-tsc",
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "module": "ES2022",
    "target": "ES2022",
    "lib": ["ES2022", "dom"],
    "paths": {
      "igniteui-angular": ["./node_modules/@infragistics/igniteui-angular"],
      "igniteui-dockmanager": ["./node_modules/@infragistics/igniteui-dockmanager"],
      "igniteui-dockmanager/*": ["./node_modules/@infragistics/igniteui-dockmanager/*"],
      "@/core/*": ["./src/app/core/*"],
      "@/app/*": ["./src/app/*"],
      "@/modules/*": ["./src/app/modules/*"],
      "@/shared/*": ["./src/app/shared/*"]
    },
    "typeRoots": ["node_modules/@types"]
  },
  "angularCompilerOptions": {
    "defaultCategory": "error",
    "enableI18nLegacyMessageIdFormat": false,
    "fullTemplateTypeCheck": true,
    "strictInjectionParameters": true,
    "uninvokedFunctionInEventBinding": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  },
  "exclude": [
    // IDEs should not type-check the different node_modules directories of the different packages.
    // This would cause the IDEs to be slower and also linters would check the node_modules.
    ".history/",
    "./cypress.config.ts",
    ".angular/",
    ".nx/",
    ".vscode/",
    "node_modules/",
    "dist/",
    "node/",
    "test/",
    "**/.angular/",
    "cypress/",
    "nbproject/"
  ]
}
