package ape.mail.core;

import Framework.Config.MailSender;
import Framework.Config.Utilities;
import ape.mail.dto.BnextSmtpTransport;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.annotation.Nonnull;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMultipart;
import mx.bnext.core.daemon.BnextThread;
import mx.bnext.core.daemon.util.IBnextThread;
import org.glowroot.agent.api.Instrumentation;
import qms.framework.daemon.BnextDaemonUtil;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;

public class MailTask extends BnextThread implements IBnextThread {

    private static final Class<MailTask> CLAZZ = MailTask.class;
    private static final Integer MAX_RETRY_ATTEMPTS = 5;

    @Nonnull
    private final String configsHash;
    @Nonnull
    private final MailTaskConfiguration config;
    @Nonnull
    private final String id;
    
    private Integer attempt = 0;
    
    public MailTask(@Nonnull MailTaskConfiguration config) {
        this.config = config;
        this.configsHash = String.valueOf(config.hashCode());
        this.id = UUID.randomUUID().toString();
    }
    
    @Override
    public void submitQueue() {
        getLogger().trace("[{}][{}]-- Queued for mail daemon", getLogParams());
    }

    @Override
    @Instrumentation.Transaction(
            timer = "background thread",
            traceHeadline = "MailTask {{this.id}}",
            transactionName = "MailTask",
            transactionType = "Background",
            alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
    )
    public void run() {
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        try {
            //Parche para utilizar javax.mail del JAR en lugar del JDK
            Thread.currentThread().setContextClassLoader(javax.mail.Message.class.getClassLoader());
            final Object[] logParams = getLogParams();
            getLogger().trace("[{}][{}]-- Starting mail daemon", logParams);
            getLogger().trace("[{}][{}]-- Preparing session", logParams);
            final Session session = Utilities.getSMTPSession(null);
            getLogger().trace("[{}][{}]-- Building message", logParams);
            final List<Message> messages = buildMail(config, session);
            getLogger().trace("[{}][{}]-- Message ready", logParams);
            dispatch(messages);
        } finally {
            MeasureTime.stop(tStart, "[" + getId() + "][" + configsHash + "]-- Mail daemon elapsed time");
        }
    }

    private void dispatch(final List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        try {
            final Object[] logParams = getLogParams();
            getLogger().trace("[{}][{}]-- Preparing transportation", logParams);
            getLogger().trace("[{}][{}]-- Sending email", logParams);
            messages.stream()
                    .filter(Objects::nonNull)
                    .forEach(mailMessage -> {
                        dispatchMessage(mailMessage);
                        getLogger().trace("[{}][{}]-- Email sent", logParams);
                    });
        } catch (Exception ex) {
            printRecipients(ex);
        }
    }

    private Object[] getLogParams() {
        return new Object[]{
            getId(), configsHash
        };
    }

    private void dispatchMessage(final Message message) {
        try (final BnextSmtpTransport transport = SmtpTransport.getTransport(Thread.currentThread().getName())) {
            final ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
            if (transport == null || transport.getSmtpTransport() == null) {
                getLogger().trace("It was not possible to define a transport, no emails are being sent");
                return;
            }
            MeasureTime.stop(tStart, "[" + getId() + "][" + configsHash + "]-- Mail daemon connecting to smtp server");
            transport.getSmtpTransport().sendMessage(message, message.getAllRecipients());
            if (getLogger().isDebugEnabled()) {
                getLogger().debug("[{}][{}]-- Email sent to {} ", new Object[]{
                    getId(),
                    configsHash,
                    message.getAllRecipients()
                });
            }
            if (getLogger().isTraceEnabled()) {
                getLogger().trace("[{}][{}]-- There are {} email recipients", new Object[]{
                    getId(),
                    configsHash,
                    message.getAllRecipients().length,});
            }
            MeasureTime.stop(tStart, "[" + getId() + "][" + configsHash + "]-- Mail daemon dispatching message");
        } catch (final MessagingException ex) {
            if (attempt <= MAX_RETRY_ATTEMPTS) {
                attempt = attempt + 1;
                getLogger().debug("[{}][{}]-- [1] Error sending mail {}, trying again ", new Object[]{
                    getId(),
                    configsHash,
                    message,
                    ex
                });
                final MailSender sender = BnextDaemonUtil.getRunningInstance(MailSender.class);
                sender.runAsyncOrTimeout(this);
            } else {
                getLogger().error("[{}][{}]-- [1] Error sending mail {} ", new Object[]{
                    getId(),
                    configsHash,
                    message,
                    ex
                });
                getLogger().error("[2] Error sending mail ", ex);
                
            }
        } catch (final IllegalStateException | ExecutionException ex) {
            getLogger().error("[{}][{}]-- [1] Execution error sending mail {} ", new Object[] {
                getId(),
                configsHash,
                message,
                ex
            });
            getLogger().error("[2] Error sending mail ", ex);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    

    @Override
    public void interrupt() {
    }

    private void printRecipients(final Exception ex) {
        try {
            getLogger().error("[{}][{}]-- [1] Error sending mail {} {} ", new Object[]{
                getId(),
                configsHash,
                config,
                config.getRecipients(),
                ex
            });
        } catch (Exception e) {
            getLogger().error("[{}][{}]-- [2] Error sending mail {} ", new Object[]{
                getId(),
                configsHash,
                config,
                e
            });
        }
    }

    private List<Message> buildMail(final MailTaskConfiguration config, final Session session) {
        final ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        String subject = MailTaskUtil.getSubject(config.getSubject());
        if (!MailTaskUtil.isMailingAvailableByType(config.getType())) {
            return null;
        }
        if (config.isInvidualSend()
                && (config.getRecipient() == null || config.getRecipient().getEmail() == null || config.getRecipient().getEmail().isEmpty())) {
            return null;
        } else if (config.getRecipient() != null && config.getRecipient().getName() == null) {
            config.getRecipient().setName(config.getRecipient().getEmail());
        }
        final String template = MailTaskUtil.buildMail(config, config.getTokenManager());
        if (template == null) {
            MeasureTime.stop(tStart, "[" + getId() + "][" + configsHash + "]-- Time to build empty mail");
            return null;
        }
        try {
            final List<Message> messages = new ArrayList<>(2);
            final Message mainMessage = MailTaskUtil.buildMessage(session, subject, config);
            if (mainMessage == null) {
                getLogger().error("[{}][{}]-- Error al enviar el correo. No se pudo construir el mensaje.", new Object[]{
                    getId(), configsHash
                });
                return null;
            }
            // Create an alternative Multipart
            final Multipart mailMultiPart = new MimeMultipart("mixed");
            // Creates regular body
            MimeBodyPart bodyPart = new MimeBodyPart();
            bodyPart.setContent(MailTaskUtil.encodeUTF8(template), "text/html; charset=utf-8");
            bodyPart.setHeader("Content-Type", "text/html; charset=UTF-8");
            mailMultiPart.addBodyPart(bodyPart);
            if (template.contains("cid:mailLogo")) {
                // Logo Multipart
                bodyPart = new MimeBodyPart();
                File mailLogo = Utilities.getMailLogo();
                DataSource logo = new FileDataSource(mailLogo);
                DataHandler handler = new DataHandler(logo);
                bodyPart.setDataHandler(handler);
                bodyPart.setFileName(mailLogo.getName());
                bodyPart.setDisposition(MimeBodyPart.INLINE + "; name=\"" + mailLogo.getName() + "\"");
                bodyPart.addHeader("Content-ID", "<mailLogo>");
                bodyPart.addHeader("Content-Type", "image/png; name=\"" + mailLogo.getName() + "\"");
                bodyPart.addHeader("Content-Transfer-Encoding", "base64");
                mailMultiPart.addBodyPart(bodyPart);
            }
            //Se agrega evento simple de calendario
            MailTaskUtil.attachCalendar(mailMultiPart, config.getTitle(), subject, config.getStartDate(), config.getEndDate());
            //Se agregan archivos adjuntos a partir de file_id
            MailTaskUtil.attachFileContent(mailMultiPart, config.getAttachmentFileIds());
            MailTaskUtil.attachFiles(mailMultiPart, config.getAttachmentFiles());
            mainMessage.setContent(mailMultiPart);
            mainMessage.saveChanges();

            /*justo aquí envía los correos*/
            messages.add(mainMessage);
            if (config.getEvent() != null) {
                final Message request = MailTaskUtil.buildMessage(session, subject, config);
                if (request != null) {
                    final Multipart requestMultipart = new MimeMultipart("alternative");
                    // Add part two, the calendar
                    final BodyPart calendarPart = MailTaskUtil.getMeetingPart(config.getEvent());
                    requestMultipart.addBodyPart(calendarPart);
                    request.setContent(requestMultipart);
                    request.saveChanges();
                    messages.add(request);
                } else {
                    getLogger().error("[{}][{}]-- Error al enviar el correo. No se pudo construir el mensaje de evento.", new Object[]{
                        getId(), configsHash
                    });
                }
            }
            return messages;
        } catch (final MessagingException | UnsupportedEncodingException | NumberFormatException e) {
            getLogger().error("--Error al enviar el correo.", new Object[]{
                getId(), configsHash, e
            });
            return null;
        } finally {
            MeasureTime.stop(tStart, "[" + getId() + "][" + configsHash + "]-- Build mail elapsed time");
        }
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.config);
        return hash;
    }

    @Override
    @SuppressWarnings("AccessingNonPublicFieldOfAnotherObject")
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MailTask other = (MailTask) obj;
        return Objects.equals(this.config, other.config);
    }

    @Nonnull
    @Override
    public String getId() {
        return id;
    }

    @Override
    public String toString() {
        return "MailTask{" + "id=" + getId() + ",config=" + config + '}';
    }


}
