package ape.pending.util;

import ape.pending.dto.ColumnDTO;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class AttenderHQLBuilder {

    private static final Pattern
        SELECT_PATTERN = Pattern.compile("^\\s*?select\\s.+?\\sfrom\\s", Pattern.CASE_INSENSITIVE),
        WHERES_PATTERN = Pattern.compile("\\swhere\\s", Pattern.CASE_INSENSITIVE);
    ;

    private final StringBuilder selectSb;
    private final StringBuilder leftJoinSb;
    private String lastBuildedQuery;

    public AttenderHQLBuilder(int hqlSize) {
        this.selectSb = new StringBuilder(hqlSize);
        this.leftJoinSb = new StringBuilder();
    }

    public AttenderHQLBuilder append(String str) {
        selectSb.append(str);
        return this;
    }

    public AttenderHQLBuilder append(
            final String tableAlias,
            final ColumnDTO column
    ) {
        selectSb.append(SqlQueryParser.COMMA);
        boolean tableAliasNone = tableAlias.trim().equals("NONE");
        if (tableAliasNone) {
            selectSb
                .append(column.getValue())
                .append(SqlQueryParser.AS)
                .append(column.getValueAlias());
        } else {
            if (column.getJoinField() == null) {
                selectSb.append(tableAlias);
            } else {
                selectSb.append(column.getJoinFieldAlias());
                leftJoinSb
                    .append(SqlQueryParser.LEFT_JOIN)
                    .append(tableAlias)
                    .append(SqlQueryParser.DOT)
                    .append(column.getJoinField())
                    .append(SqlQueryParser.SPACE)
                    .append(column.getJoinFieldAlias())
                ;
            }
            selectSb
                .append(SqlQueryParser.DOT)
                .append(column.getValue())
                .append(SqlQueryParser.AS)
                .append(column.getValueAlias());
        }
        return this;
    }

    public String getColumnsString() {
        return selectSb.toString().trim();
    }

    public String getBuildedQuery(String pendingHql) {
        final String select = SELECT_PATTERN.matcher(pendingHql).replaceFirst(selectSb.toString());
        leftJoinSb.append(SqlQueryParser.WHERE);
        final String hql = WHERES_PATTERN.matcher(select).replaceFirst(leftJoinSb.toString());
        lastBuildedQuery = hql;
        clear();
        return hql;
    }

    private void clear() {
        leftJoinSb.setLength(0);
        selectSb.setLength(0);
    }

    public String getLastBuildedQuery() {
        return lastBuildedQuery;
    }



}
