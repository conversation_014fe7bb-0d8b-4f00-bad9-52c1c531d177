/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.pending.util;

import DPMS.Mapping.IMailableUser;
import DPMS.Mapping.Persistable;
import Framework.Config.Utilities;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingDataSource;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.ConditionFieldValue;
import ape.pending.dto.IPendingDto;
import ape.pending.dto.PendingCountAndHours;
import ape.pending.dto.UserPendingCountAndHours;
import bnext.exception.ExplicitRollback;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import qms.access.dto.LoggedUser;
import qms.access.util.ProfileServicesUtil;
import qms.activity.pending.ActivityPendingDataSource;
import qms.audit.pending.AuditPendingDataSource;
import qms.complaint.pending.ComplainPendingDataSource;
import qms.configuration.pending.ConfigurationPendingDataSource;
import qms.device.pending.DevicePendingDataSource;
import qms.document.pending.DocumentPendingDataSource;
import qms.finding.pending.FindingPendingDataSource;
import qms.form.pending.FormPendingDataSource;
import qms.form.pending.FormRequestPendingDataSource;
import qms.form.pending.OutstandingSurveyPendingDataSource;

/**
 *
 * <AUTHOR>
 */
public class PendingRecordWorkload {

    private static void dataSourcePendingCountAll
            (PendingDataSource comparator,
             IPendingRecordDAO dao,
             Date start,
             Date end
            ) {
        if ((start != null && end == null) || (start == null && end != null)) {
            throw new ExplicitRollback(
                    "Missing date filter while getting pendings data source, start: [" + start + "], end: [" + end + "]"
            );
        }
        List<UserPendingCountAndHours> pendingCountAndHours = getPendingCountAndHours(
                new IMailableUser[] { comparator.getUser() },
                dao,
                false,
                start,
                end
        );
        // Se llenan los contadores de `comparator`
        pendingCountAndHours.stream()
                .filter(p -> p.getUserId().equals(comparator.getUser().getId()))
                .map(UserPendingCountAndHours::getPendingCountAndHours).forEach(m ->
            m.forEach((dte, c) ->
                c.forEach(r -> 
                    comparator.addApeCount(r.getModule(), r.getApe().getCode(), r.getPendingCount(), r.getPendingPlannedHours())
                )
            )
        );
    }
    
    public static PendingDataSource dataSourcePendingAll(IMailableUser user, IPendingRecordDAO dao) {
        return PendingRecordWorkload.dataSourcePendingAll(user, dao, /* countOnly */false , /*start*/null, /*end*/null);
    }
    
    private static boolean hasAccess(IMailableUser user, Module module) {
        if (user instanceof LoggedUser) {
            return ProfileServicesUtil.hasModuleAccess(((LoggedUser)user).getServices(), module);
        }
        return true;
    }
    
    public static PendingDataSource dataSourcePendingAll(IMailableUser user, IPendingRecordDAO dao, boolean countOnly, Date start, Date end) {
        if ((start != null && end == null) || (start == null && end != null)) {
            throw new ExplicitRollback("Missing date filter while getting pendings data source, start: [" + start + "], end: [" + end + "]");
        }
        final PendingDataSource comparator = new PendingDataSource(user);
        if (countOnly) {
            dataSourcePendingCountAll(comparator, dao, start, end);
        } else {
            final Collection<IPendingDto> pendings = new TreeSet<>(comparator);
            if (hasAccess(user, Module.ACTIVITY)) {
                // pendientes de actividades
                pendings.addAll(ActivityPendingDataSource.getPendingRecords(dao, mx.bnext.access.Module.ACTIVITY, user));
            }
            if (hasAccess(user, Module.PLANNER)) {
                // pendientes de planner
                pendings.addAll(ActivityPendingDataSource.getPendingRecords(dao, mx.bnext.access.Module.PLANNER, user));
            }
            if (hasAccess(user, Module.AUDIT)) {
                // pendientes de auditorias - main
                pendings.addAll(AuditPendingDataSource.getPendingRecords(dao));
                // pendientes de auditorias - submodulo de actividades
                pendings.addAll(ActivityPendingDataSource.getPendingRecords(dao, mx.bnext.access.Module.AUDIT, user));
            }
            if (hasAccess(user, Module.CONFIGURATION)) {
                // pendientes de configuración
                pendings.addAll(ConfigurationPendingDataSource.getPendingRecords(dao));
            }
            if (hasAccess(user, Module.DEVICE)) {
                // pendientes de equipos
                pendings.addAll(DevicePendingDataSource.getPendingRecords(dao));
            }
            if (hasAccess(user, Module.DOCUMENT)) {
                // pendientes de documentos
                pendings.addAll(DocumentPendingDataSource.getPendingRecords(dao, user));
            }
            if (hasAccess(user, Module.FORMULARIE)) {
                // pendientes de formularios - actividades
                pendings.addAll(ActivityPendingDataSource.getPendingRecords(dao, mx.bnext.access.Module.FORMULARIE, user));
                // pendientes de formularios - request
                pendings.addAll(FormPendingDataSource.getPendingRecords(dao));
                // pendientes de formularios - outstanding - reopen/cancel/adjustment
                pendings.addAll(OutstandingSurveyPendingDataSource.getPendingRecords(dao));
                // pendientes de formularios - outstanding
                pendings.addAll(FormRequestPendingDataSource.getPendingRecords(dao));
            }
            if (hasAccess(user, Module.ACTION)) {
                // pendientes de hallazgos - main
                pendings.addAll(FindingPendingDataSource.getPendingRecords(dao));
                // pendientes de hallazgos - submodulo de actividades
                pendings.addAll(ActivityPendingDataSource.getPendingRecords(dao, mx.bnext.access.Module.ACTION, user));
            }
            if (hasAccess(user, Module.COMPLAINT)) {
                // pendientes de quejas
                pendings.addAll(ComplainPendingDataSource.getPendingRecords(dao));
            }
            comparator.setPendings(pendings);
        }
        return comparator;
    }

    public static List<UserPendingCountAndHours> getPendingCountAndHours(
            IMailableUser[] users,
            IPendingRecordDAO dao,
            boolean includeCompleted,
            Date start,
            Date end
    ) {
        if ((start != null && end == null) || (start == null && end != null)) {
            throw new ExplicitRollback(
                    "Missing date filter while getting pendings data source, start: [" + start + "], end: [" + end + "]"
            );
        }
        Long[] userIds = Arrays.stream(users).map(Persistable::getId).toArray(Long[]::new);
        final List<Map<String, Object>> result = new ArrayList<>();
        // Se sobreescriben contadores del módulo de ACTIVITY
        result.addAll(ActivityPendingDataSource.getPendingCountAndHours(userIds, dao, Module.ACTIVITY, includeCompleted, start, end));
        // Se sobreescriben contadores del módulo de PLANNER, submódulo ACTIVITY
        result.addAll(ActivityPendingDataSource.getPendingCountAndHours(userIds, dao, Module.PLANNER, includeCompleted, start, end));
        // Se sobreescriben contadores del módulo de AUDIT, submódulo ACTIVITY
        result.addAll(ActivityPendingDataSource.getPendingCountAndHours(userIds, dao, Module.AUDIT, includeCompleted, start, end));
        // Se sobreescriben contadores del módulo de FORMULARIE, submódulo ACTIVITY
        result.addAll(ActivityPendingDataSource.getPendingCountAndHours(userIds, dao, Module.FORMULARIE, includeCompleted, start, end));
        // Se sobreescriben contadores del módulo de ACTION, submódulo ACTIVITY
        result.addAll(ActivityPendingDataSource.getPendingCountAndHours(userIds, dao,Module.ACTION, includeCompleted, start, end));
        // Otros módulos
        result.addAll(overridePendingCountAndHours(userIds, dao, includeCompleted, start, end));
         
        List<UserPendingCountAndHours> pendingCountAndHours = mapUserPendingCountAndHours(
                result,
                Collections.singletonList(APE.PLANNER_ACTIVITY_TO_COMPLETE)
        );
        // Se llenan valores de correo y nombre del usuario
        for (IMailableUser user : users) {
            pendingCountAndHours.forEach(p -> {
                if (user.getId().equals(p.getUserId())) {
                    p.setUser(user);
                }
            });
        }
        return pendingCountAndHours;
    }
    
    private static List<Map<String, Object>> overridePendingCountAndHours(
            Long[] userIds,
            IPendingRecordDAO dao,
            boolean includeCompleted,
            Date start,
            Date end
    ) {
        // pendientes de hallazgos - main
        final Class<? extends BaseAPE> findingBase = FindingPendingDataSource.getBaseEntity(Module.ACTION);
        final IPendingOperation[] findingOperations = FindingPendingDataSource.getPendingOperations(dao);
        final List<Map<String, Object>> result = new ArrayList<>(
                getBasePendingCountAndHours(userIds, dao, start, end, findingBase, findingOperations)
        );
        // pendientes de auditorías - main      
        final Class<? extends BaseAPE> auditBase = AuditPendingDataSource.getBaseEntity(Module.AUDIT);
        final IPendingOperation[] auditOperations = AuditPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, auditBase, auditOperations));
        // pendientes de formularios - main 
        final Class<? extends BaseAPE> formBase = FormPendingDataSource.getBaseEntity();
        final IPendingOperation[] formOperations = FormPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, formBase, formOperations));
        // pendientes de formularios - outstandingsurveys 
        final Class<? extends BaseAPE> formOut = OutstandingSurveyPendingDataSource.getBaseEntity();
        final IPendingOperation[] formOutOperations = OutstandingSurveyPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, formOut, formOutOperations));
        // pendientes de formularios - formRequest 
        final Class<? extends BaseAPE> formReq = FormRequestPendingDataSource.getBaseEntity();
        final IPendingOperation[] formReqOperations = FormRequestPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, formReq, formReqOperations));
        // pendientes de equipos - main 
        result.addAll(DevicePendingDataSource.getDevicePendingCountAndHours(userIds, dao, start, end));
        // pendientes de documentos - main 
        result.addAll(DocumentPendingDataSource.getDocumentPendingCountAndHours(userIds, dao, start, end));
        // pendientes de configuración - main 
        final Class<? extends BaseAPE> confBase = ConfigurationPendingDataSource.getBaseEntity(Module.CONFIGURATION);
        final IPendingOperation[] confOperations = ConfigurationPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, confBase, confOperations));
        // pendientes de quejas - main 
        final Class<? extends BaseAPE> complainBase = ComplainPendingDataSource.getBaseEntity(Module.COMPLAINT);
        final IPendingOperation[] complainOperations = ComplainPendingDataSource.getPendingOperations(dao);
        result.addAll(getBasePendingCountAndHours(userIds, dao, start, end, complainBase, complainOperations));
        
        return result;
    }
    
    private static List<Map<String, Object>> getBasePendingCountAndHours(
        Long[] userIds, 
        IPendingRecordDAO dao, 
        Date start, 
        Date end,
        final Class<? extends BaseAPE> entityClass,
        final IPendingOperation[] operations
    ) {
        final List<ConditionFieldValue> conditions = new ArrayList<>(2);
        if (start != null && end != null) {
            conditions.add(new ConditionFieldValue("record", "trunc", "commitmentDate", "start", ConditionFieldValue.ConditionOperator.GREATER_OR_EQUALS, Utilities.truncDate(start)));
            conditions.add(new ConditionFieldValue("record", "trunc", "commitmentDate", "end", ConditionFieldValue.ConditionOperator.SMALLER_OR_EQUALS, Utilities.truncDate(end)));
        }
        final List<Map<String, Object>> results = dao.getActivePendingRecordCount(
                userIds,
                entityClass,
                BaseSummaryFieldBuilder.columns(
                    "code = pendingTypeCode@ptype",
                    "trunc(record.commitmentDate) = pendingDate@NONE",
                    "record.status = pendingStatus@NONE",
                    "trunc(record.reminder) = pendingEndDate@NONE",
                    "module = moduleKey@ptype",
                    "code",
                    "description"
                ),
                conditions, /* conditions */
                null, /* joins */
                operations
        );
        return results;
    }
    
    private static List<UserPendingCountAndHours> mapUserPendingCountAndHours(
            List<Map<String, Object>> results,
            List<APE> zeroHours
    ) {
        final List<UserPendingCountAndHours> result = results.stream()
                .map((Map<String, Object> m) -> new UserPendingCountAndHours(
                    (Long) m.get("pendingRecordUserId"), new LinkedHashMap<>()
                ))
                .distinct()
                .collect(Collectors.toList());
        result.forEach(u -> populatePendingCountAndHours(results, zeroHours, u));
        return result;
    }

    private static void populatePendingCountAndHours(
            List<Map<String, Object>> results,
            List<APE> zeroHours,
            UserPendingCountAndHours u
    ) {
        List<Map<String, Object>> filtered = results.stream()
                .filter(p -> Objects.equals(u.getUserId(), p.get("pendingRecordUserId")))
                .collect(Collectors.toList());
        Map<Date, List<PendingCountAndHours>> pendingsByDate = u.getPendingCountAndHours();
        Map<Date, ArrayList<PendingCountAndHours>> pendingDate = filtered.stream()
                .map(p -> Utilities.truncDate((Date) p.get("pendingDate")))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toMap(Utilities::truncDate, p -> new ArrayList<>()));
        pendingsByDate.putAll(pendingDate);
        pendingsByDate.forEach((key, value) -> addPendingCountAndHours(key, value, zeroHours, filtered));
    }

    private static void addPendingCountAndHours(
            Date key,
            List<PendingCountAndHours> value,
            List<APE> zeroHours,
            List<Map<String, Object>> filtered
    ) {
        List<PendingCountAndHours> pendingDate = filtered.stream()
                .filter(m -> Utilities.isSameDay(key, (Date) m.get("pendingDate")))
                .map((Map<String, Object> m) -> getPendingCountAndHours(m, zeroHours))
                .collect(Collectors.toList());
        value.addAll(pendingDate);
    }

    private static PendingCountAndHours getPendingCountAndHours(Map<String, Object> record, List<APE> zeroHours) {
        Double plannedHours = (Double) record.get("plannedHours");
        APE ape = APE.fromCode((String) record.get("pendingTypeCode"));
        if (ape != null && zeroHours.contains(ape)) {
            plannedHours = 0.0;
        }
        return new PendingCountAndHours(
                (String) record.get("pendingTypeCode"),
                (String) record.get("moduleKey"),
                (Long) record.get("pendingCount"),
                plannedHours,
                (Integer) record.get("pendingStatus"),
                (Long) record.get("pendingRecordUserId"),
                (Date) record.get("pendingDate"),
                (Date) record.get("pendingEndDate"),
                (String) record.get("code"),
                (String) record.get("description"),
                (Long) record.get("activityId"),
                (Long) record.get("implementationId"),
                (Long) record.get("createdBy"),
                (String) record.get("creatorUserName"),
                (Long) record.get("clientId"),
                (String) record.get("clientName")
        );
    }
}