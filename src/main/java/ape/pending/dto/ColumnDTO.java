package ape.pending.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ColumnDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    private String joinField;
    private String joinFieldAlias;
    private String value;
    private String valueAlias;
    private String tableAlias; // <-- opcional, por defecto vale "entity" o "subEntity"
    private Map localeKeys;

    public ColumnDTO(String joinField, String value, String valueAlias) {
        this.joinField = joinField;
        this.joinFieldAlias = joinField + new Date().getTime();
        this.value = value;
        this.valueAlias = valueAlias;
    }

    public ColumnDTO(String value, String valueAlias) {
        this.value = value;
        this.valueAlias = valueAlias;
    }

    public ColumnDTO(String value) {
        this.value = value;
        this.valueAlias = value;
    }

    public ColumnDTO(String value, String valueAlias, Map localeKeys) {
        this.value = value;
        this.valueAlias = value;
        this.localeKeys = localeKeys;
    }
    
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueAlias() {
        return valueAlias;
    }

    public void setValueAlias(String valueAlias) {
        this.valueAlias = valueAlias;
    }

    public Map getLocaleKeys() {
        return localeKeys;
    }

    public void setLocaleKeys(Map localeKeys) {
        this.localeKeys = localeKeys;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.joinField);
        hash = 29 * hash + Objects.hashCode(this.joinFieldAlias);
        hash = 29 * hash + Objects.hashCode(this.value);
        hash = 29 * hash + Objects.hashCode(this.valueAlias);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ColumnDTO other = (ColumnDTO) obj;
        if (!Objects.equals(this.joinField, other.joinField)) {
            return false;
        }
        if (!Objects.equals(this.joinFieldAlias, other.joinFieldAlias)) {
            return false;
        }
        if (!Objects.equals(this.value, other.value)) {
            return false;
        }
        return Objects.equals(this.valueAlias, other.valueAlias);
    }

    @Override
    public String toString() {
        return "ColumnDTO{" + "column=" + value + ", alias=" + valueAlias + '}';
    }

    public String getJoinField() {
        return joinField;
    }

    public void setJoinField(String joinField) {
        this.joinField = joinField;
    }

    public String getJoinFieldAlias() {
        return joinFieldAlias;
    }

    public void setJoinFieldAlias(String joinFieldAlias) {
        this.joinFieldAlias = joinFieldAlias;
    }

    public String getTableAlias() {
        return tableAlias;
    }

    public void setTableAlias(String tableAlias) {
        this.tableAlias = tableAlias;
    }

    
}
