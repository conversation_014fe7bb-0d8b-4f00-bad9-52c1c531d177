
package ape.pending.dao;

import DPMS.DAOInterface.IFlowDAO;
import DPMS.Mapping.IUser;
import DPMS.Mapping.Request;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import DPMS.Mapping.Workflow;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.IUntypedDAO;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPending;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingHelper;
import ape.pending.dto.ChangeRequestDto;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.ConditionFieldValue;
import ape.pending.dto.PendingFeasibleDto;
import ape.pending.dto.ReassignDTO;
import ape.pending.entities.ChangeRequest;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.ApeUtil;
import ape.pending.util.AttenderHQLBuilder;
import ape.pending.util.RecordRowsCache;
import ape.pending.util.RecordRowsType;
import ape.pending.util.SqlQueryParser;
import bnext.exception.ExplicitRollback;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.configuration.core.UserPositionService;
import qms.escalation.listener.OnPendingReassigned;
import qms.framework.core.EntityModelCache;
import qms.framework.entity.Owner;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.framework.util.PaginateUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
@Lazy
@Repository(value = "PendingRecordDAO")
@Scope(value = "singleton")
@Language(module = "ape.pending.dao.PendingRecordDAO")
public class PendingRecordDAO extends GenericDAOImpl<PendingRecord, Serializable> implements IPendingRecordDAO {
    
    private void loadLocalizedFields(final Map record, final Set<ColumnDTO> localized) {
        if (localized.isEmpty()) {
            return;
        }
        localized.forEach(field -> {
            final String aliasField = field.getValueAlias();
            final Object recordValue = record.get(aliasField);
            if (recordValue != null) {
                final Object keyLang = field.getLocaleKeys().get(recordValue);
                if (keyLang != null) {
                    final String langKey = keyLang.toString();
                    record.put(aliasField, getTag(langKey));
                } else {
                    getLogger().error("Missing key '{}' for field with alias '{}' ", recordValue, field.getValueAlias());
                }
            }
        });

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean saveScalable(Long pendingRecordId, Integer onOff) {
        int i = HQL_updateByQuery(""
            + " UPDATE " + PendingRecord.class.getCanonicalName() + " c "
            + " SET c.scalable = " + onOff + ", c.lastUpdated = CURRENT_DATE()"
            + " WHERE c.id = :pendingRecordId", "pendingRecordId", pendingRecordId
        );
        return i > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ReassignDTO save(Long pendingRecordId, Long ownerNewId) {
        PendingRecord previowsPendingRecord = HQLT_findById(PendingRecord.class, pendingRecordId);
        Long owner = previowsPendingRecord.getOwner(), superOwner = previowsPendingRecord.getSuperOwnerId(), reassignedFrom = previowsPendingRecord.getReassignedFrom(), currentOwnerId = owner;
        if(reassignedFrom != null) {
            currentOwnerId = reassignedFrom;
        }
        if(superOwner != null) {
            currentOwnerId = superOwner;
        }
        HQL_updateByQuery(""
            + " UPDATE " + PendingRecord.class.getCanonicalName() + " c "
            + " SET c.status = " + PendingRecord.STATUS.HISTORY.getValue()
                + ", c.lastUpdated = CURRENT_DATE()"
            + " WHERE c.id = :pendingRecordId", "pendingRecordId", pendingRecordId
        );
        getEntityManager().detach(previowsPendingRecord);
        previowsPendingRecord.setSuperOwnerId(ownerNewId);
        Date now = getNowInstance(), commitment = truncDate(previowsPendingRecord.getCommitmentDate());
        if (commitment == null || commitment.before(now)) {
            previowsPendingRecord.setCommitmentDate(now);
        }
        Date reminder = truncDate(previowsPendingRecord.getReminder());
        if (reminder == null || reminder.before(now)) {
            previowsPendingRecord.setReminder(now);
        }
        previowsPendingRecord.setUnattendedDays(0);
        previowsPendingRecord.setReassignedFrom(currentOwnerId);
        previowsPendingRecord.setStatus(PendingRecord.STATUS.REASSIGNED.getValue());
        getAspectJAutoProxy().savePendingRecord(previowsPendingRecord);
        
        Map<String, Object> typeData = HQL_findSimpleMap(""
            + " SELECT new map(c.code as code, c.id as type)"
            + " FROM " + PendingType.class.getCanonicalName() + " c"
            + " WHERE c.id = :previowsPendingRecord ", 
            ImmutableMap.of("previowsPendingRecord", previowsPendingRecord.getTypeId()),
            true,
            CacheRegion.CATALOGS_SYSTEM,
            0
        );
        APE ape;
        String typeCode = typeData.get("code").toString();
        if(typeCode.startsWith("ACC-")) {
            ape = APE.valueOf(typeCode.substring(4).replace("-", "_"));
        } else {
            ape = APE.valueOf(typeCode.replace("-", "_"));
        }
        try {
            IPendingOperation pending = ape.getOperationClass().getConstructor(IUntypedDAO.class).newInstance(this);
            pending.updateCountersByType(Long.valueOf(typeData.get("type").toString()), SecurityUtils.getLoggedUser(), ownerNewId);
        } catch (Exception e) {}

        ReassignDTO dto = new ReassignDTO();
        dto.setRecordId(previowsPendingRecord.getRecordId());
        dto.setApe(ape);
        return dto;
    }
    private Date truncDate(Date d) {
        if (d == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    private Date getNowInstance() {
        return truncDate(new Date());
    }

    @Override
    @OnPendingReassigned
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PendingRecord savePendingRecord(PendingRecord pendingRecord){
        pendingRecord = makePersistent(pendingRecord);
        return pendingRecord;
    }
    
    
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPendingRecordDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IPendingRecordDAO.class);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void normalize(ReassignDTO dto, ILoggedUser loggedUser, Long pendingRecordId){
        try{
            IPending pending = dto.getApe().getPendingClass().getConstructor(IUntypedDAO.class).newInstance(this); 
            pending.normalize(loggedUser, pendingRecordId);
        }catch(NoSuchMethodException | SecurityException | InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException e){
            getLogger().error("Error en el metodo normalize", e);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getPendingRecords(final APE ape, final RecordRowsCache useCache, final ILoggedUser user) {
        List<ColumnDTO> fields = new ArrayList<>(7);
        final IPendingOperation operation = ApeUtil.getOperationInstance(ape, this);
        String owner;
        if (
            ApeOperationType.WEAK.equals(operation.getOperationType())
            || ApeOperationType.SOFT.equals(operation.getOperationType())
        ) {
            fields.addAll(Arrays.asList(operation.getBaseSummaryFields()).stream().map((ColumnDTO dto) -> {
                dto.setTableAlias("subEntity");
              return dto;  
            }).collect(Collectors.toList()));
            fields.addAll(Arrays.asList(operation.getModuleSummaryFields()));
            owner = "subEntity";
        } else {
            fields.addAll(Arrays.asList(operation.getBaseSummaryFields()));
            owner = "entity";
        }
        List<Map<String, Object>> records = getLimitedPendingRecords(
            new Long[] { user.getId() }, 
            operation.getEntityBase(), 
            null,
            owner,
            fields, 
            Utilities.getSettings().getPendingMaxCount(), 
            useCache, 
            operation
        );
        for (final Map<String, Object> record : records) {
            loadLocalizedFields(record, operation.getLocalizedFields());
            String str = operation.getSummaryTemplate().getTemplate();
            for (int i = 0; i < operation.getSummaryTemplate().getCount(); i++) {
                str = StringSubstitutor.replace(str, record);
            }
            record.put("text", str);
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getPendingRecords(
            final IUser user,
            final Class<? extends BaseAPE> entityClass, 
            final List<ColumnDTO> fields,
            final IPendingOperation ... pendings
    ) {
        return getPendingRecords(user, entityClass, null, fields, RecordRowsCache.DISABLED, pendings);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getPendingRecords(
            final IUser user,
            final Class<? extends BaseAPE> entityClass, 
            final String owner,
            final List<ColumnDTO> fields,
            final RecordRowsCache useCache,
            final IPendingOperation ... pendings
    ) {
        if (user == null || user.getId() == null || user.getId() == 0) {
            getLogger().error("User is a required parameter for loading pendings. Invalid value {}", user);
            return Utilities.EMPTY_LIST;
        }
        return getLimitedPendingRecords(new Long[] {user.getId()}, entityClass, owner, null, fields, null, useCache, pendings);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<Map<String, Object>> getPendingRecords(
            IUser user,
            Class<? extends BaseAPE> entityClass, 
            String owner,
            List<ColumnDTO> fields,
            String join,
            RecordRowsCache useCache,
            IPendingOperation ... pendings
    ) {
        if (user == null || user.getId() == null || user.getId() == 0) {
            getLogger().error("User is a required parameter for lading pendings with join {}. Invalid value {}", new Object[]{join, user});
            return Utilities.EMPTY_LIST;
        }
        return getLimitedPendingRecords(new Long[] {user.getId()}, entityClass, owner, join, fields, null, useCache, pendings);
    }

    private List<Map<String, Object>> getLimitedPendingRecords(
        Long[] userIds,
        Class<? extends BaseAPE> entityClass,
        String owner,
        String join,
        List<ColumnDTO> fields,
        Integer maxResults,
        RecordRowsCache useCache,
        IPendingOperation ... pendings
    ) {
        if (RecordRowsCache.ENABLED.equals(useCache)) {
            final Map<String, Object> paramsApe = new HashMap<>();
            final String hqlApe = getLimitedPendingRecordsQuery(
                    userIds,
                    entityClass,
                    owner,
                    null, 
                    null,
                    RecordRowsType.ONLY_APE, 
                    null,
                    paramsApe,
                    pendings
            );
            if (hqlApe == null || hqlApe.isEmpty()) {
                getLogger().error("Invalid HQL query for loading pendings for class {}", entityClass);
                return Utilities.EMPTY_LIST;
            }
            final List<Map<String, Object>> resultsApe = HQL_findByQueryLimit(hqlApe, paramsApe, maxResults, false, null, null);
            if (resultsApe != null && !resultsApe.isEmpty()) {
                final Map<String, Object> paramsEntity = new HashMap<>();
                final List<Long> recordIds = resultsApe.stream()
                        .map((record) -> (Long)record.get("recordId"))
                        .collect(Collectors.toList());
                final Integer recordsSize;
                if (maxResults != null && maxResults > 0) {
                   recordsSize = maxResults;
                } else {
                    recordsSize = recordIds.size();
                }
                final List<Map<String, Object>> resultsEntity = new ArrayList<>(recordsSize);
                final Double numberPages = PaginateUtil.numberPages(recordsSize);
                for (Integer currentPage = 0; currentPage < numberPages; currentPage++) {
                    final List<Long> subRecordIds = PaginateUtil.definePagedRecordIds(currentPage, recordIds);
                    final String hqlEntity = getLimitedPendingRecordsQuery(
                            userIds,
                            entityClass,
                            owner,
                            join,
                            fields,
                            RecordRowsType.ONLY_ENTITY,
                            subRecordIds,
                            paramsEntity,
                            pendings
                    );
                    final List<Map<String, Object>> pageResults = HQL_findByQueryLimit(hqlEntity, paramsEntity, maxResults, true, null, null);
                    if (pageResults != null && !pageResults.isEmpty()) {
                        resultsEntity.addAll(pageResults);
                    }
                }                
                if (!resultsEntity.isEmpty()) {
                    final Map<Long, Map<String, Object>> indexEntity = new HashMap<>();
                    resultsEntity.stream().forEach((entity) -> indexEntity.put((Long)entity.get("recordId"), entity));
                    resultsApe.stream().forEach((result) -> {
                        final Long recordId = (Long)result.get("recordId");
                        if (recordId != null && indexEntity.containsKey((recordId))) {
                            result.putAll(indexEntity.get(recordId));
                        } else {
                            String pendingTypeCodes = getPendingTypeCodes(pendings);
                            getLogger().error(
                                    "Missing pending details from cache for id {} and pending type(s) {}.",
                                    new Object[]{ recordId, pendingTypeCodes }
                            );
                        }
                    });
                } else {
                    String pendingTypeCodes = getPendingTypeCodes(pendings);
                    getLogger().error("Missing pending details from cache for all records for pending type(s) {}.", pendingTypeCodes);
                }
            }
            return resultsApe;
        } else {
            final Map<String, Object> params = new HashMap<>();
            final String hql = getLimitedPendingRecordsQuery(
                    userIds, 
                    entityClass, 
                    owner,
                    join, 
                    fields, 
                    RecordRowsType.CROSS_APE_ENTITY, 
                    null,
                    params,
                    pendings
            );
            if (hql == null || hql.isEmpty()) {
                getLogger().error("Invalid HQL query for loading pendings for class {}", entityClass);
                return Utilities.EMPTY_LIST;
            }
            final List<Map<String, Object>> results = HQL_findByQueryLimit(hql, params, maxResults, false, null, null);
            return results;            
        }
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Integer getPendingCount(final IUser user, final IPendingOperation... pendings) {
        final List<IPendingOperation> operations = Arrays.asList(pendings);
        final String pendingCodes = "'" + operations.stream()
                .map((pending) -> pending.getApe().getCode())
                .collect(Collectors.joining("', '")) + "'";
        if (user == null || user.getId() == null || user.getId() == 0) {
            getLogger().error(
                    "User is a required parameter for counting pendings. Invalid value {} for {}",
                    user, pendingCodes
            );
            return 0;
        }
        return operations.stream().map((operation) -> operation.getCountByUser(user.getId())).reduce(0, Integer::sum);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getActivePendingRecordCount(
        final Long[] userIds,
        final Class<? extends BaseAPE> entityClass,
        final List<ColumnDTO> fields,
        final List<ConditionFieldValue> conditions,
        final String joins,
        final IPendingOperation ... pendings
    ) {
        return getPendingRecordCount(userIds, entityClass, fields, conditions, false, joins, pendings);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getPendingRecordCount(
            final Long[] userIds,
            final Class<? extends BaseAPE> entityClass,
            final List<ColumnDTO> fields,
            final List<ConditionFieldValue> conditions,
            final boolean includeCompleted,
            final String joins,
            final IPendingOperation ... pendings
    ) {
        final StringBuilder conditionBuilder = new StringBuilder(conditions.size() * 30);
        final Map<String, Object> params = new HashMap<>(conditions.size());
        buildCondition(conditions, conditionBuilder, params, true);
        String hql = getPendingRecordCountQuery(
                userIds,
                entityClass,
                fields,
                joins,
                conditionBuilder.substring(SqlQueryParser.AND.length()),
                includeCompleted,
                params,
                pendings
        );
        return HQL_findByQuery(hql, params);
    }

    private void buildCondition(
            List<ConditionFieldValue> conditions,
            StringBuilder conditionBuilder,
            Map<String, Object> params,
            boolean andOperator
    ) {
        String tableAliasTemp;
        for (ConditionFieldValue field : conditions) {
            if (field.getTableAlias() == null) {
                tableAliasTemp = "entity";
            } else {
                tableAliasTemp = field.getTableAlias();
            }
            conditionBuilder
                    .append(andOperator ? SqlQueryParser.AND : SqlQueryParser.OR);
            if (field.getFieldName() != null) {
                if (field.getMethodWrap() != null) {
                    conditionBuilder
                            .append(field.getMethodWrap()).append("(");
                }
                conditionBuilder
                        .append(tableAliasTemp)
                        .append(SqlQueryParser.DOT)
                        .append(field.getFieldName());
                if (field.getMethodWrap() != null) {
                    conditionBuilder
                            .append(")");
                }
            }
            if (ConditionFieldValue.ConditionOperator.SUBCONDITION.equals(field.getConditionOperator())) {
                List<ConditionFieldValue> subConditions = (List<ConditionFieldValue>) field.getValue();
                conditionBuilder.append(" (").append(" 1 = 0 ");
                buildCondition(subConditions, conditionBuilder, params, false);
                conditionBuilder.append(" )");
            } else if (ConditionFieldValue.ConditionOperator.BETWEEN_COLUMNS.equals(field.getConditionOperator())) {
                conditionBuilder
                        .append(" :")
                        .append(tableAliasTemp)
                        .append("_")
                        .append(field.getFieldAlias())
                        .append(" ")
                        .append(field.getBetweenColumns());
                params.put(tableAliasTemp + "_" + field.getFieldAlias(), field.getValue());
            } else if (ConditionFieldValue.ConditionOperator.BETWEEN_VALUES.equals(field.getConditionOperator())) {
                conditionBuilder
                        .append(" ")
                        .append(field.getConditionOperator().getKey())
                        .append(" :")
                        .append(tableAliasTemp)
                        .append("_")
                        .append(field.getFieldAlias())
                        .append("start")
                        .append(SqlQueryParser.AND)
                        .append(" :")
                        .append(tableAliasTemp)
                        .append("_")
                        .append(field.getFieldAlias())
                        .append("end");
                final List<Object> values = (List<Object>) field.getValue();
                params.put(tableAliasTemp + "_" + field.getFieldAlias() + "start", values.get(0));
                params.put(tableAliasTemp + "_" + field.getFieldAlias() + "end", values.get(1));

            } else {
                conditionBuilder
                        .append(" ")
                        .append(field.getConditionOperator().getKey())
                        .append(" :")
                        .append(tableAliasTemp)
                        .append("_")
                        .append(field.getFieldAlias());
                params.put(tableAliasTemp + "_" + field.getFieldAlias(), field.getValue());
            }
        }
    }

    private String getLimitedPendingRecordsQuery(
        Long[] userIds,
        Class<? extends BaseAPE> entityClass,
        String owner,
        String join,
        List<ColumnDTO> fields,
        RecordRowsType type,
        List<Long> recordIds,
        Map<String, Object> params,
        final IPendingOperation ... pendings
    ) {
        String select;
        String endOfHql;
        switch (type) {
            case CROSS_APE_ENTITY:
                select =  ""
                    + " SELECT new map("
                        + " record.recordId as recordId"
                        + ",record.base as base"
                        + ",record.id as pendingRecordId"
                        + ",record.status AS status"
                        + ",record.attendedOn AS attendedBy"
                        + ",record.superOwnerId AS superOwnerId"
                        + ",record.owner AS ownerId"
                        + ",record.creationDate AS creationDate"
                        + ",record.commitmentDate AS commitmentDate"
                        + ",record.deadline AS deadline"
                        + ",record.noticeDate AS noticeDate"
                        + ",record.reminder AS reminder"
                        + ",record.owner as pendingRecordUserId" // <-- ToDo: Probablemente venga equivocado para registros escalados
                        + ",ptype.id AS pendingTypeId"
                        + ",ptype.code AS pendingTypeCode"
                        + ",ptype.module AS module";
                endOfHql = ""
                        + " ORDER BY record.commitmentDate DESC,  entity.id DESC";
                break;
            case ONLY_ENTITY:
                select =  ""
                    + " SELECT new map("
                        +  owner + ".id as recordId";
                endOfHql = ""
                        + " ORDER BY " +  owner + ".id DESC";
                break;
            case ONLY_APE:
                select =  ""
                    + " SELECT new map("
                        + " record.recordId as recordId"
                        + ",record.base as base"
                        + ",record.id as pendingRecordId"
                        + ",record.status AS status"
                        + ",record.attendedOn AS attendedBy"
                        + ",record.superOwnerId AS superOwnerId"
                        + ",record.owner AS ownerId"
                        + ",record.creationDate AS creationDate"
                        + ",record.commitmentDate AS commitmentDate"
                        + ",record.deadline AS deadline"
                        + ",record.noticeDate AS noticeDate"
                        + ",record.reminder AS reminder"
                        + ",record.owner as pendingRecordUserId" // <-- ToDo: Probablemente venga equivocado para registros escalados
                        + ",ptype.id AS pendingTypeId"
                        + ",ptype.code AS pendingTypeCode"
                        + ",ptype.module AS module";
                endOfHql = ""
                        + " ORDER BY record.commitmentDate DESC";
                break;
            default:
                select = "";
                endOfHql = "";
                break;
        }
        return getLimitedPendingRecordsQuery(
                userIds,
                entityClass,
                owner,
                select, 
                join, 
                endOfHql, 
                fields, 
                type, 
                recordIds,
                false,
                params,
                pendings
        );
    }

    private String getPendingRecordCountQuery(
        final Long[] userIds,
        final Class<? extends BaseAPE> entityClass,
        final List<ColumnDTO> groupByFields,
        final String join,
        final String WHERE,
        final boolean includeCompleted,
        final Map<String, Object> params,
        final IPendingOperation ... pendings
    ) {
        if (groupByFields.isEmpty()) {
            throw new ExplicitRollback("GroupBy should `group` by at least one column, currently there's none");
        }
        StringBuilder endOfHql = new StringBuilder(groupByFields.size() * 30);
        StringBuilder select = new StringBuilder(200);
        select.append(" "
            + " SELECT new map("
                + " count(*) as pendingCount"
                + ",record.owner as pendingRecordUserId"); // <-- ToDo: Probablemente venga equivocado para registros escalados
        Class<? extends BaseAPE> entityPendingClass = entityClass;
        final boolean isSoft = EntityModelCache.isSoftBaseAPE(entityClass);
        // Se incluye suma de las horas si existe el campo
        if (isSoft) {
            entityPendingClass = EntityModelCache.getSoftBaseConfig(entityClass).getStrongEntityAPE();
        }
        if (EntityModelCache.isPlannedHours(entityPendingClass)) {
            if (isSoft) {
                select.append(", sum(subEntity.plannedHours) as plannedHours ");
            } else {
                select.append(", sum(entity.plannedHours) as plannedHours ");
            }
        }
        // Construcción de campos `GROUP BY`
        endOfHql.append(" "
            + " GROUP BY "
                + " record.owner ");
        groupByFields.forEach(field -> {
            endOfHql.append(", ");
            if (field.getTableAlias() == null) {
                endOfHql.append("entity.").append(field.getValue());
            } else {
                boolean tableAliasNone = field.getTableAlias().trim().equals("NONE");
                if (tableAliasNone) {
                    endOfHql.append(field.getValue());
                } else {
                    endOfHql.append(field.getTableAlias()).append(SqlQueryParser.DOT).append(field.getValue());
                }
            }
        });
        String hql = getLimitedPendingRecordsQuery(
                userIds, 
                entityClass, 
                null,
                select.toString(), 
                join,
                endOfHql.toString(),
                groupByFields, 
                RecordRowsType.CROSS_APE_ENTITY,
                null,
                includeCompleted,
                params,
                pendings
        );
        if (WHERE != null) {
            return hql.replaceFirst(SqlQueryParser.WHERE, SqlQueryParser.WHERE + " " + WHERE + " AND ");
        }
        return hql;
    }

    private String getLimitedPendingRecordsQuery(
        Long[] userIds,
        Class<? extends BaseAPE> entityClass,
        String owner,
        String select,
        String join,
        String endOfHql,
        List<ColumnDTO> fields,
        RecordRowsType type,
        List<Long> recordIds,
        boolean includeCompleted,
        Map<String, Object> params,
        final IPendingOperation ... pendings
    ) {
        final AttenderHQLBuilder builder = new AttenderHQLBuilder(200).append(select);
        if (fields != null) {
            fields.forEach(field -> {
                if (field.getTableAlias() == null) {
                    builder.append("entity", field);
                } else {
                    builder.append(field.getTableAlias(), field);
                }
            });
        }
        builder.append(") FROM ");
        StringBuilder hql = new StringBuilder(
            builder.getBuildedQuery(
                PendingHelper.getLightActiveRecordsHQL(
                        entityClass,
                        owner,
                        userIds, 
                        type, 
                        recordIds,
                        includeCompleted,
                        true,
                        params,
                        pendings
                )
            )
        );
        if (hql.length() == 0) {
            getLogger().error("Could not build HQL query for loading pendings for class {}", entityClass);
            return "";
        }
        hql.append(endOfHql);
        if (join != null) {
            return hql.toString().replaceFirst(SqlQueryParser.WHERE, join + SqlQueryParser.WHERE);
        }
        return hql.toString();
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PendingFeasibleDto isFeasible(String pendingTypeCode, Long pendingRecordId, Long loggedUserId) { 
        PendingFeasibleDto result = new PendingFeasibleDto(PENDING_FEASIBLE_RESULT.INVALID);
        APE ape = APE.fromCode(pendingTypeCode);
        if (ape == null) {
            throw new RuntimeException(""
                + " Invalid pendingTypeCode '" + pendingTypeCode + "',"
                + " value must exist as a 'code' inside APE.java."
            );
        }
        if  (APE.CONFIGURATION_TO_ACTIVE_USER.equals(ape)) {
            final Integer countPending = HQL_findSimpleInteger(""
                    + " SELECT COUNT(c.id)"
                    + " FROM " + UserRef.class.getCanonicalName() + " c"
                    + " WHERE " + UserPositionService.USER_TO_ACTIVATE_FILTER
                    + " AND c.id = :id", "id", pendingRecordId
            );
            if (countPending.equals(0)) {
                result.setStatus(PendingRecord.STATUS.ATTENDED);
            } else {
                result.setValid(PENDING_FEASIBLE_RESULT.VALID);
            }
            return result;
        }
        Integer pendingDocumentCount = HQL_findSimpleInteger(""
            + " SELECT COUNT(1)"
            + " FROM " + PendingRecord.class.getCanonicalName() + " rec "
            + " JOIN rec.pendingType c "
            + " WHERE rec.id = :pendingRecordId"
            + " and rec.module = '" + Module.DOCUMENT.name() + "'",
            "pendingRecordId", 
            pendingRecordId
        );
        if (pendingDocumentCount > 0) {
            // Validate request lock
            Long blockedBy = HQL_findSimpleLong(""
                + " SELECT req.blockedBy FROM " + PendingRecord.class.getCanonicalName() + " c "
                + " , " + Request.class.getCanonicalName() + " req "
                + " WHERE c.id = :pendingRecordId"
                + " AND req.id = c.recordId "
                + " AND req.isBusy = 1 ",
                "pendingRecordId",
                pendingRecordId
            );
            if (blockedBy != 0L && !blockedBy.equals(loggedUserId)) {
                result.setBlockedBy(
                    HQL_findSimpleString(""
                        + " SELECT c.description FROM " + User.class.getCanonicalName() + " c "
                        + " WHERE c.id = :blockedBy",
                        "blockedBy", 
                        blockedBy
                    ).trim()
                );
                result.setValid(PENDING_FEASIBLE_RESULT.BUSY);
                return result;
            }
        }
        Integer status = HQL_findSimpleInteger(""
            + " SELECT c.status"
            + " FROM " + PendingRecord.class.getCanonicalName() + " c "
            + " WHERE c.id = :pendingRecordId",
            "pendingRecordId", 
            pendingRecordId
        );
        PendingRecord.STATUS s = PendingRecord.STATUS.fromValue(status);
        if (s == null) {
            return result;
        }
        result.setStatus(s);
        switch (s) {
            case ACTIVE:
            case ESCALATED:
            case REASSIGNED:
                result.setValid(PENDING_FEASIBLE_RESULT.VALID);
        }
        return result;
    }

    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getRecordRows(
            SortedPagedFilter filter,
            Module module
    ) {
        GridInfo result = null;
        List<APE> apes = Lists.reverse(PendingHelper.getTypesByModule(module));
        List<Class> valid = new ArrayList<>();
        for (APE ape : apes) {
            IPendingOperation operation = ApeUtil.getOperationInstance(ape, this);
            if (valid.contains(operation.getEntityBase())) {
                continue;
            }
            valid.add(operation.getEntityBase());
            RecordRowsType type = RecordRowsType.CROSS_APE_ENTITY;
            try {
                if (result == null) {
                    
                    result = getUntypedDAO().getRecordRowsWithNewTx(null, filter, operation.getEntityBase(), null, type);
                    if (result.getData().isEmpty()) {
                        result.setData(new ArrayList<>());
                    }
                } else {
                    GridInfo temp = getUntypedDAO().getRecordRowsWithNewTx(null, filter, operation.getEntityBase(), null, type);
                    if (!temp.getData().isEmpty()) {
                        result.getData().addAll(temp.getData());
                        result.setCount(result.getCount() + temp.getCount());
                    }
                }
            } catch (Exception e){
                getLogger().error("Error en el metodo getRecordRows", e);
            }
        } 
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean saveChangeRequest(
        Module module, ChangeRequestDto changes, PendingRecord record, List<Owner> autorizators, ILoggedUser loggedUser
    ) {
        IFlowDAO flow = getBean(IFlowDAO.class);

        // Se inhabilita el pendiente
        HQL_updateByQuery(""
            + " UPDATE " + PendingRecord.class.getCanonicalName() + " p "
            + " SET p.status = " + PendingRecord.STATUS.CHANGE_REQUEST.getValue()
            + " WHERE p.id = :recordId", "recordId", record.getId()
        );
        // Inicia proceso
        Workflow workflow = flow.save(
            "CR-" + Utilities.todayDateBy("yyyyMMdd-") + loggedUser.getAccount().toUpperCase(),
            loggedUser.getBusinessUnitId(), null, module, loggedUser, autorizators
        );
        ChangeRequest request = new ChangeRequest(record, changes);
        changes.getChanges().stream().forEach(change -> {
            change.setId(-1L);
            change.setChangeRequest(request);
        });
        request.setId(-1L);
        request.setWorkflowId(workflow.getId());
        request.setDeleted(0);
        request.setCode(workflow.getCode());
        request.setStatus(ChangeRequest.STATUS.ACTIVE.getValue());
        return makePersistent(request, loggedUser.getId()) != null;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public int saveHistory(Settings settings) throws QMSException {
        int movedRecords = SQL_updateByQuery(""
            + " INSERT INTO ape_pending_record_historic "
            + " SELECT * FROM ape_pending_record "
            + " WHERE"
                + " STATUS = " + PendingRecord.STATUS.ATTENDED.getValue()
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM tag_registry "
                + " )"
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM ape_pending_record_historic "
                + " )"
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM timesheet "
                + " )"
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM ape_change_request "
                + " )"
                + " AND attended_on < DATEADD(month, "
                    + "-" + settings.getPendingHistory()
                    + ", CAST(GETDATE() AS DATE)"
                + " ) ",
           Utilities.EMPTY_MAP,
           settings.getConnQueryTimeout(),
           Arrays.asList("ape_pending_record_historic")
        );
        SQL_updateByQuery(""
            + " DELETE FROM ape_pending_record "
            + " WHERE"
                + " STATUS = " + PendingRecord.STATUS.ATTENDED.getValue()
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM tag_registry "
                + " )"
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM timesheet "
                + " )"
                + " AND pending_record_id NOT IN ("
                        + " SELECT pending_record_id FROM ape_change_request "
                + " )"
                + " AND attended_on < DATEADD(month, "
                    + "-" + settings.getPendingHistory()
                    + ", CAST(GETDATE() AS DATE)"
                + " ) ",
            Utilities.EMPTY_MAP,
            settings.getConnQueryTimeout(),
            Arrays.asList("ape_pending_record")
        );
        return movedRecords;
    }
    
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = {Exception.class})
    public void rebuildIndex(Settings settings, int movedRecords) throws QMSException {
        if (movedRecords > 0 && Objects.equals(settings.getIndexRebuild(), 1)) {
            String SQL = "";
            Integer edition = SQL_findSimpleInteger("SELECT CAST(SERVERPROPERTY('EngineEdition') AS INT)");
            if (edition == 3) {
                final Integer timeoutMinutes = getQuerySystemTimeoutMinutes(settings);
                SQL = " PARTITION = ALL "
                    +   " WITH ("
                    +   " PAD_INDEX = OFF"
                    +   ",STATISTICS_NORECOMPUTE = OFF"
                    +   ",SORT_IN_TEMPDB = OFF"
                    +   ",ONLINE = ON (WAIT_AT_LOW_PRIORITY(MAX_DURATION = " + ( timeoutMinutes )
                    +   " MINUTES, ABORT_AFTER_WAIT = SELF ))"
                    +   ",ALLOW_ROW_LOCKS = ON"
                    +   ",ALLOW_PAGE_LOCKS = ON"
                +   ")";
            }
            SQL_execute(""
                + " ALTER INDEX ALL ON ape_pending_record REBUILD "
                + SQL, settings.getConnQueryTimeout());
        }
    }
    private Integer getQuerySystemTimeoutMinutes(Settings settings) {
        final Integer connQueryTimeout;
        if (settings.getConnQueryTimeout() != null) {
            connQueryTimeout = settings.getConnQueryTimeout();
        } else {
            connQueryTimeout = 0;
        }
        final Integer timeoutMinutes;
        if (connQueryTimeout > 0) {
            timeoutMinutes = (int) Math.ceil(connQueryTimeout / 60.0);
        } else {
            timeoutMinutes = 2;
        }
        return timeoutMinutes;
    }

    private String getPendingTypeCodes(IPendingOperation[] pendings) {
        if (pendings == null || pendings.length == 0) {
            return "";
        }
        final String result = Arrays.asList(pendings).stream()
                .map((pending) -> pending.getApe().getCode())
                .collect(Collectors.joining (","));
        return result;
    }

}
