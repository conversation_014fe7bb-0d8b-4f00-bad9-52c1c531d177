package ape.pending.entities;

import Framework.Config.DomainObject;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "ape_change_request_field")
public class ChangeRequestField extends DomainObject {

    private static final long serialVersionUID = -1504321047106252995L;

    private String fieldName;
    private String fieldValueOld;
    private String fieldValueNew;
    private String fieldValueNewReadOnly;
    private String fieldType;

    private ChangeRequest changeRequest;


    public ChangeRequestField() { }

    @Id
    @Column(name = "change_request_field_id", precision = 19, scale = 0)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "field_name")
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    @Column(name = "field_value_old")
    public String getFieldValueOld() {
        return fieldValueOld;
    }

    public void setFieldValueOld(String fieldValueOld) {
        this.fieldValueOld = fieldValueOld;
    }

    @Column(name = "field_value_new")
    public String getFieldValueNew() {
        return fieldValueNew;
    }

    public void setFieldValueNew(String fieldValueNew) {
        this.fieldValueNew = fieldValueNew;
    }

    @Column(name = "field_value_new_readonly")
    public String getFieldValueNewReadOnly() {
        return fieldValueNewReadOnly;
    }

    public void setFieldValueNewReadOnly(String fieldValueNewReadOnly) {
        this.fieldValueNewReadOnly = fieldValueNewReadOnly;
    }

    @Column(name = "field_type")
    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    @JoinColumn(name = "change_request_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public ChangeRequest getChangeRequest() {
        return changeRequest;
    }

    public void setChangeRequest(ChangeRequest changeRequest) {
        this.changeRequest = changeRequest;
    }

}
