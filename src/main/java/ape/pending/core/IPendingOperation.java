package ape.pending.core;

import DPMS.Mapping.IBusinessUnit;
import DPMS.Mapping.IBusinessUnitDepartment;
import DPMS.Mapping.IUser;
import ape.mail.core.MailHelper;
import ape.mail.dto.MailColumn;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.PendingConditional;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.regex.Pattern;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.escalation.dto.IEscalableDTO;
import qms.framework.mail.MailDTO;

/**
 *
 * <AUTHOR>
 */
public interface IPendingOperation extends IPendingQueries {

    Pattern PARAM_PATTERN_STATUS = Pattern.compile(":status");
    Pattern PARAM_PATTERN_TYPE = Pattern.compile(":type");
    Pattern PARAM_PATTERN_OWNER = Pattern.compile(":owner");
    Pattern PARAM_PATTERN_SCOPE = Pattern.compile(":scope");
    Pattern PARAM_PATTERN_RECORD_ID = Pattern.compile(":recordId");

    APE getApe();

    Long getTypeId();
        
    /**
     * Se realizan todas las operaciones de recalculo a un pendiente especifico:
     * 1) Mantiene correcta la fecha de compromiso, solo se utiliza cuando se recalculan pendientes
     * 2) Mantiene correcta la fecha de aviso, solo se utiliza cuando se recalculan pendientes
     * 3) Mantiene correcta la fecha de recordatorio, solo se utiliza cuando se envían correos diarios
     * 4) Incrementa contador de escalamiento
     * 5) Marca pendientes como escaladados
     * 6) Marca pendientes como atendidos
     * 7) Da de alta pendientes activos considerando si el mismo fue escalado
     * 8) Actualiza usuarios relacionados al pendiente reasignados
     * 9) Atiende dependencias
     * 
     * @param loggedUser
     * @param trigger
     * @param recordId
     */
    void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, Long... recordId);
    
    void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, PendingConditional conditional, Long... recordId);

    /**
     * Se recalculan solo los pendientes atendidos y activos:
     * 1) Marca pendientes como atendidos
     * 2) Da de alta pendientes activos considerando si el mismo fue escalado
     * 
     * @param loggedUser
     * @param trigger
     */
    void recalculateActiveAndAttended(@Nonnull ISecurityUser loggedUser, Class trigger);
    
    Integer updateCounterByUsers(Set<Long> users, @Nonnull ISecurityUser loggedUser);

    void updateCountersByType(Long type, @Nonnull ISecurityUser loggedUser, Long ownerNewId);

<<<<<<< HEAD
=======
    @Nonnull
>>>>>>> bnext/3.1.X
    Integer getCountByUser(Long user);
    
    void dailyCheck(Class trigger, @Nonnull ILoggedUser loggedUser);

    Module getImplementedModule();
    
    boolean hasImplementedModule();

    List<String> getStack();

    void setStack(List<String> stack);
    List<IUser> getPossibleResponsibleList(Long recordId);
    List<IUser> getPossibleResponsibleList(
            Long recordId,
            IBusinessUnitDepartment businessUnitDepartment
    );
    List<IUser> getPossibleResponsibleList(Long recordId, IBusinessUnit businessUnit);
    List<IUser> getPossibleResponsibleList(Long recordId, Long currentUserId);
    List<IUser> getPossibleResponsibleList(
            Long recordId,
            Long currentUserId,
            IBusinessUnitDepartment businessUnitDepartment
    );
    List<IUser> getPossibleResponsibleList(
            Long recordId,
            Long currentUserId,
            IBusinessUnit businessUnit
    );

    List<MailDTO> getReminderMail();

    String getPendingSql();
    
    String getPendingSql(String appendEndHql);

    Class<? extends BaseAPE> getEntityBase();

    LinkedHashMap<String, MailColumn> getDailyMailColumns();

    void setExtraFieldsMap(LinkedHashMap<String, MailColumn> listExtraFields);

    List<String> getExtraMails();

    void setExtraMails(List<String> extraUsers);
    
    void setExtraMails(String... extraUsers);
    
    Set<Long> updateRecord(Class trigger, PendingConditional conditional, @Nonnull ISecurityUser loggedUser, Long... recordId);
    
    Set<Long> updateRecord(Long recordId, PendingConditional conditional, Class trigger, @Nonnull ISecurityUser loggedUser);
    
<<<<<<< HEAD
    List<ColumnDTO> getBaseSummaryFields();
    
    List<ColumnDTO> getModuleSummaryFields();
=======
    ColumnDTO[] getBaseSummaryFields();
    
    ColumnDTO[] getModuleSummaryFields();
>>>>>>> bnext/3.1.X
    
    SummaryTemplate getSummaryTemplate();
    
    ApeOperationType getOperationType();

    Set<ColumnDTO> getLocalizedFields();

    boolean isPendingActive(Long recordId);
    
    int updateActive(String base, Map<String, Object> fields, Long... recordId);

    Long getPendingRecordIdFromRecordId(@Nonnull ISecurityUser loggedUser, Long recordId, String... typeCodes);
    
    void handleResultRecords(Set<IEscalableDTO> records);
    
    String getPathDailyMailTemplate();
    
    String getDailyMailQuery();
    
    MailHelper getMailHelper();
    
    void setDailyMailQuery(String dailyMailQuery);
    
    void setMailHelper(MailHelper mailHelper);

    String getNameEscalateMailOverride(PendingMailerType type);

    /**
     * Variables disponibles:
     *   ${counter}
     * 
     * @param type
     * @return
     */
    String getSubjectEscalateMailOverride(PendingMailerType type);
    
    /**
     * Variables disponibles:
     *   ${entityCode}
     *   ${entityDescription}
     *   ${subEntityCode}
     *   ${subEntityDescription}
     *   ${entityTypeName}
     *   ${entityDepartmentName}
     *   ${entityBusinessUnitName}
     *   ${entitySourceCode}
     *   ${entitySourceName}
     *   ${commitmentDate}
     *   ${recipientName}
     *   ${recipientCode}
     * 
     * @param type
     * @param record
     * @return 
     */
    String getSubjectEscalateMailOverride(PendingMailerType type, IEscalableDTO record);
    
    /**
     * Variables disponibles:
     *   ${entityCode}
     *   ${entityDescription}
     *   ${subEntityCode}
     *   ${subEntityDescription}
     *   ${entityTypeName}
     *   ${entityDepartmentName}
     *   ${entityBusinessUnitName}
     *   ${entitySourceCode}
     *   ${entitySourceName}
     *   ${commitmentDate}
     *   ${recipientName}
     *   ${recipientCode}
     * 
     * @param type
     * @param record
     * @return 
     */
    String getMessageTitleEscalateMailOverride(PendingMailerType type, IEscalableDTO record);
    
    PendingMailerMode getPendingMailerMode(PendingMailerType type);

    /**
     * Variables disponibles:
     * ${entityId}
     * ${recordId}
     * ${recipientId}
     * ${entityCode}
     * ${subEntityCode}
     *
     * @return 
     */
    String getMailRecordUrl();
   
    IEscalableDTO getEscalableRecord(
            final Object[] temp,
            final LinkedHashMap<String, MailColumn> extraColumns,
            final ResourceBundle tags,
            final IPendingOperation pendingOperation,
            final ILoggedUser loggedUser
    );
    
    ResourceBundle getEscalationMailerTags();
    
    boolean getRemoveRows();
    
    void execute(Long record, Class trigger, @Nonnull ISecurityUser loggedUser);

}
