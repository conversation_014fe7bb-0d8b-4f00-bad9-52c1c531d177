/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package mx.bnext.qms.configuration.rest;

import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.dto.PrintingParsedHtmlDTO;
import mx.bnext.qms.configuration.util.PrintingFormatHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.custom.dao.IPrintingFormatDAO;
import qms.custom.dto.FormatDataSourceDTO;
import qms.custom.entity.PrintingFormat;
import qms.framework.util.CacheRegion;
import qms.util.GridFilter;
import qms.util.ModuleUtil;
import qms.util.QMSException;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.InputStream;
import java.nio.file.Path;
import javax.servlet.ServletOutputStream;
import mx.bnext.core.file.FileHandler;
import mx.bnext.core.file.TempPath;
import org.apache.commons.io.IOUtils;
import qms.framework.file.FileManager;

import qms.framework.rest.SecurityUtils;

import qms.framework.util.BusinessUnitUtil;
import qms.framework.util.CsvParser;
import qms.framework.util.DepartmentUnitUtil;
import qms.util.FilePathUtils;

/**
 *
 * <AUTHOR>
 */
@Lazy
@RestController
@RequestMapping("printing-format")
public class PrintingFormatController extends Loggable {

    @Autowired
    @Qualifier("PrintingFormatDAO")
    private IPrintingFormatDAO dao;

    private static final String HAS_REPORTS_VIEW_ACCESS = "hasAnyAuthority('IS_ADMIN', 'REPORT_FORM', 'FORMULARIO_CONTROL', 'FILL_OUT_HISTORY', 'FILL_FORM')";

    @PostMapping()
    @RequestMapping({"list/{moduleName}", "list/{moduleName}/{masterId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> printReportList(
        @RequestBody GridFilter filter,
        @PathVariable(value = "moduleName", required = true) String moduleName,
        @PathVariable(value = "masterId", required = false) String masterId
    ) {
        final Module module = ModuleUtil.fromKey(moduleName);
        if (module == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        return dao.list(filter, module, masterId);
    }
    
    @GetMapping()
    @RequestMapping({"base/{moduleName}/data-source/{masterId}", "base/{moduleName}/data-source/{masterId}/{printingFormatId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormatDataSourceDTO> printReportDataSource(
        @PathVariable(value = "moduleName", required = true) String moduleName,
        @PathVariable(value = "masterId", required = true) String masterId,
        @PathVariable(value = "printingFormatId", required = false) Long printingFormatId
    ) {
        final Module module = ModuleUtil.fromKey(moduleName);
        if (module == null) {
            return new ResponseEntity(
                    FormatDataSourceDTO.instance("Invalid module `" + moduleName + "`"), HttpStatus.CONFLICT
            );
        }
        return new ResponseEntity(
            dao.getDataSource(module, masterId, printingFormatId, SecurityUtils.getLoggedUser()),
            HttpStatus.OK
        );
    }
    
    @PostMapping()
    @RequestMapping({"base/{moduleName}/save"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GenericSaveHandle> savePrintReport(
        @RequestBody PrintingFormat entity,
        @PathVariable(value = "moduleName", required = true) String moduleName
    ) throws IOException, QMSException {
        final Module module = ModuleUtil.fromKey(moduleName);
        if (module == null) {
            return new ResponseEntity(
                GenericSaveHandle.instance("Invalid module `" + moduleName + "`"), HttpStatus.CONFLICT
            );
        }
        return dao.save(module, entity, SecurityUtils.getLoggedUser());
    }

    @GetMapping()
    @RequestMapping({"delete/{moduleName}/{printingFormatId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GenericSaveHandle> deletePrintReport(
        @PathVariable(value = "moduleName", required = true) String moduleName,
        @PathVariable(value = "printingFormatId", required = true) Long printingFormatId
    ) throws IOException, QMSException {
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + PrintingFormat.class.getCanonicalName() + " p "
                + " SET p.deleted = 1"
                + " WHERE p.id = :printingFormatId ", "printingFormatId", printingFormatId
            ) > 0
        ) {
            return new ResponseEntity(
                GenericSaveHandle.success("success"), HttpStatus.OK
            );
        }
        return new ResponseEntity(
                GenericSaveHandle.instance("Invalid module `" + moduleName + "`"), HttpStatus.CONFLICT
        );
    }

    @PostMapping()
    @RequestMapping({"toggle-status/{moduleName}/{printingFormatId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> toogleStatus(
        @PathVariable(value = "moduleName") String moduleName,
        @PathVariable(value = "printingFormatId") Long printingFormatId
    ) {
        final ResponseEntity<Map<String, Object>> result = dao.toggleStatus(
                PrintingFormat.class, 
                printingFormatId,
                true, 
                CacheRegion.PRINTING_FORMAT,
                0, 
                SecurityUtils.getLoggedUser()
        );
        if (result != null && Objects.equals(result.getStatusCode(), HttpStatus.CREATED)) {
            final String documentMasterId = dao.getDocumentMasterId(printingFormatId);
            dao.updateCountEntity(documentMasterId, SecurityUtils.getLoggedUser());
        }
        return result;
    }
    
    @GetMapping()
    @RequestMapping({"available/{moduleName}/{masterId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<TextLongValue>> printAvailableFormats(
        @PathVariable(value = "moduleName", required = true) String moduleName,
        @PathVariable(value = "masterId", required = true) String masterId
    ) throws IOException, QMSException {
        final Module module = ModuleUtil.fromKey(moduleName);
        if (module == null) {
            return new ResponseEntity(
                GenericSaveHandle.instance("Invalid module `" + moduleName + "`"), HttpStatus.CONFLICT
            );
        }
        final List<TextLongValue> printFormats = dao.HQL_findByQuery(""
            + " SELECT new " + TextLongValue.class.getCanonicalName() + "("
                + " p.description"
                + ",p.id"
            + " )"
            + " FROM " + PrintingFormat.class.getCanonicalName() + " p "
            + " WHERE "
                + " p.deleted = 0"
                + " AND p.status = 1"
                + " AND p.masterId = :masterId "
                + " AND p.moduleName = :moduleName ", ImmutableMap.of("masterId", masterId, "moduleName", moduleName
            ), true, CacheRegion.PRINTING_FORMAT, 0
        );
        return new ResponseEntity(printFormats, HttpStatus.OK);
    }

    @GetMapping()
    @RequestMapping({"download/{moduleName}/{printingFormatId}/{requestId}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public ResponseEntity<Void> downloadPrintFormat(
            final HttpServletResponse response,
            final @PathVariable(value = "moduleName", required = true) String moduleName,
            final @PathVariable(value = "printingFormatId", required = true) Long printingFormatId,
            final @PathVariable(value = "requestId", required = true) Long requestId
    ) throws IOException, QMSException {
        final PrintingParsedHtmlDTO record = dao.getPrintingFormatRecord(printingFormatId, requestId);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        // Se agrega helper PrintingFormatHelper  para no hacer operaciones de lectura 
        // como parte de una solo transacción y tener bloqueda la conexión a la base de datos
        // Solo las operaciones de escritura se dejan en PrintingFormatDAO
        final PrintingFormatHelper helper = new PrintingFormatHelper(dao);
        final boolean refreshRequested = helper.needsRefreshRecord(record);
        final Long printingRecordFileId;
        if (refreshRequested) {
            printingRecordFileId = helper.refreshFormat(record, loggedUser, false);
            if (printingRecordFileId == null) {
                return new ResponseEntity("unavailable_format", HttpStatus.CONFLICT);
            }
        } else {
            printingRecordFileId = record.getParsedFileId();
        }
        final boolean downloaded = helper.downloadFile(printingRecordFileId, record.getPrintingFormatId(), record.getOutstandingSurveysId(), response, loggedUser);
        if (!downloaded) {
            return new ResponseEntity("failed_write_html", HttpStatus.CONFLICT);
        }
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }
    
    /**
     * Used in Spring ACCESS for example: 
     * 
     *      @PrintingFormatController.canAccessPrintReportList(#moduleName)
     * 
     * @param moduleName
     * @return 
     */
    @SuppressWarnings("unused")
    public boolean canAccessPrintReportList(String moduleName) {
        Module module = ModuleUtil.fromKey(moduleName);
        if (module == null) {
            return false;
        }
        // ToDo: revisar que el usuario tenga permiso en el módulo de ver el listado de vistas de impresión
        return true;
    }
    
    @GetMapping()
    @RequestMapping({"download/usuariosCsv"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public ResponseEntity<Void> downloadUsuariosCsv(
            final HttpServletResponse response
    ) {
        try {
            String relativePath = new FilePathUtils().getFilePathFromProyectFiles("files/", "User.csv");
            File file = new File(relativePath);
            // Ruta del archivo CSV original
            String inputFilePath = file.getPath();
            // Ruta del archivo CSV modificado que se va a generar
            final FileManager fileManager = new FileManager();
            try (final TempPath tempFile = new TempPath(FileHandler.createTempFile("User_temp.csv", fileManager.getTempFolder()))) {
                final Path tempPath = tempFile.getPath();
                File outputFilePath = tempPath.toFile();
                try (BufferedReader br = new BufferedReader(new FileReader(inputFilePath));
                        BufferedWriter bw = new BufferedWriter(new FileWriter(outputFilePath))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        // Reemplazar el texto que desees en cada línea   
                        String modifiedLine = BusinessUnitUtil.interpolate(line);
                        modifiedLine = DepartmentUnitUtil.interpolate(modifiedLine);

                        // Escribir la línea modificada en el nuevo archivo CSV 
                        bw.write(modifiedLine);
                        bw.newLine();
                    }
                } catch (IOException e) {
                    Loggable.getLogger(PrintingFormatController.class).error("Errorcreating User_temp> ", e);
                }
                
                try (final ServletOutputStream output = response.getOutputStream()) {
                    final String filename = "User.csv";
                    fileManager.writeHeadersCacheShort(
                            response,
                            filename,
                            CsvParser.CSV_CONTENT_TYPE,
                            null
                    );
                    try (final InputStream input = new FileInputStream(outputFilePath)) {
                        IOUtils.copy(input, output);
                        output.flush();
                        return new ResponseEntity<>(HttpStatus.ACCEPTED);
                    }
                }
            }
        } catch (final IOException ex) {
            Loggable.getLogger(PrintingFormatController.class).error("Errorcreating User_temp> ", ex);
            return new ResponseEntity("Error " + ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
