package mx.bnext.qms.configuration.rest;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IProfileDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Profile;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import com.sun.star.auth.InvalidArgumentException;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import mx.bnext.core.util.Loggable;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@RestController
@RequestMapping("rest/roles/profiles")
public class ProfileController extends Loggable {

    @PostMapping("save")
    @PreAuthorize("hasAnyAuthority('IS_ADMIN', 'SPECIAL_ACCESS_EDIT_PROFILE')")
    public ResponseEntity<String> save(
        final @RequestBody Profile profile,
        final HttpServletRequest request
    ) throws InvalidArgumentException, QMSException {
        IProfileDAO dao = Utilities.getBean(IProfileDAO.class);
        final boolean isNew = profile.getId() == null || profile.getId() == -1L;
        if (isNew) {
            profile.setId(-1L);
            profile.setIntBReporteReunion(0);
            if (profile.getCode() == null || profile.getCode().isEmpty()) {
                ICodeSequenceDAO sq = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
                profile.setCode("PEF-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE));
            }
        } else {
            profile.setCode(dao.HQL_findSimpleString(""
                + " SELECT p.code FROM " + Profile.class.getCanonicalName() + " p WHERE p.id = " + profile.getId()
            ));
        }
        dao.setValidServiceValues(profile, SecurityUtils.getLoggedUserId());
        try {
            final Profile savedEntity = dao.makePersistent(profile, SecurityUtils.getLoggedUserId());
            if (savedEntity != null) {
                closeUsersSession(profile.getId());
                return new ResponseEntity<>(HttpStatus.CREATED);
            } else {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: " +Utilities.getSerializedObj(profile));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity<>("exist_record", HttpStatus.CONFLICT);
            }
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @GetMapping(value = "/{id}")
    @PreAuthorize("hasAnyAuthority('IS_ADMIN', 'ADMON_ACCESOS', 'USUARIO_CORPORATIVO', 'SPECIAL_ACCESS_EDIT_PROFILE')")
    public ResponseEntity load(@PathVariable(value = "id", required = true) Long id) {
        IProfileDAO dao = Utilities.getBean(IProfileDAO.class);
        final Object profile = dao.HQL_findSimpleObject(""
            + " SELECT c "
            + " FROM " + Profile.class.getCanonicalName() + " c "
            + " WHERE c.id = :id", "id", id);
        return ResponseEntity.ok(profile);
    }
    
    private void closeUsersSession(Long profileId) {
        IProfileDAO dao = Utilities.getBean(IProfileDAO.class);
        List<Long> usersIds = dao.HQL_findByQuery(""
                + "SELECT u.id FROM " + User.class.getCanonicalName() + " u "
                        + "LEFT JOIN u.puestos p "
                        + "LEFT JOIN p.perfil pro "
                        + "WHERE pro.id = " + profileId);
        
        for (Long userId : usersIds) {
            final UserLogin loginInfo = new UserLogin(userId);
            if (loginInfo.hasActiveSession()) {
                loginInfo.removeSession(true);
                getLogger().trace("The session of user with ID: {} has been closed, cause: Services profiles has been updated", userId);
            }
        }
    }
}