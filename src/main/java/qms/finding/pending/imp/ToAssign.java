package qms.finding.pending.imp;

import DPMS.Mapping.Action;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.finding.pending.FindingPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToAssign extends FindingPendingOperations {

    private static final String TO_ASSIGN_BY_DEPARTMENT_MANAGER = ""
            + " FROM " + Action.class.getCanonicalName() + " fnd "
            + " JOIN fnd.department dep"
            + " WHERE "
            + " fnd.status = " + Action.STATUS.REPORTED.getValue() + " "
            + " AND fnd.deleted = 0";
    private static final String TO_ASSIGN_BY_MODULE_MANAGER = ""
            + " FROM " + Action.class.getCanonicalName() + " fnd "
            + " , " + User.class.getCanonicalName() + " usr "
            + " JOIN usr.puestos pst "
            + " JOIN pst.perfil prf "
            + " JOIN fnd.department dep"
            + " LEFT JOIN pst.une bu"
            + " LEFT JOIN pst.corp org"
            + " WHERE "
            + " fnd.status = " + Action.STATUS.REPORTED.getValue()+ " "
            + " AND prf.intBEncargadoAccion = 1 "
            + " AND (("
                + " prf.intBUsuarioPlanta = 1"
                + " AND bu.id = dep.businessUnitId "
            + " ) OR ("
                + " prf.intBUsuarioCorporativo = 1"
                + " AND org.id = dep.organizationalUnitId"
            + " )) "
            + " AND fnd.deleted = 0 "
            + " AND usr.status = 1 "
            + " AND usr.deleted = 0 ";

    public ToAssign(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS);
        setPendingType(getType(APE.FINDING_TO_ASSIGN));
        setScope(PendingRecord.Scope.USER);
        setReminder("dayadd(1, fnd.fechaCreacion)");
        if (Utilities.getSettings().getActionManagerWhoAccept().equals(1)) {
            setQuery(TO_ASSIGN_BY_MODULE_MANAGER);
            setOwnerField("usr.id");
            setOwnerFieldFilter(BY_MODULE_MANAGER);
        } else {
            setQuery(TO_ASSIGN_BY_DEPARTMENT_MANAGER);
            setOwnerField("dep.attendantId");
            setOwnerFieldFilter(BY_DEPARTMENT_MANAGER);
        }
        setModuleKey(MODULE);
        setBase(Action.class);
        setOperationType(ApeOperationType.STRONG);
    }

}
