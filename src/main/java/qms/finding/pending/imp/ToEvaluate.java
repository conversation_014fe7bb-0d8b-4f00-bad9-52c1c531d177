package qms.finding.pending.imp;

import DPMS.Mapping.Action;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.entities.PendingRecord;
import qms.finding.pending.FindingPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToEvaluate extends FindingPendingOperations {

    private static final String TO_EVALUATE_BY_DEPARTMENT_MANAGER = ""
            + " FROM " + Action.class.getCanonicalName() + " fnd "
            + " JOIN fnd.department dep"
            + " WHERE "
            + " fnd.status = " + Action.STATUS.IMPLEMENTED.getValue() + " "
            + " AND fnd.deleted = 0";
    private static final String TO_EVALUATE_BY_MODULE_MANAGER = ""
            + " FROM " + Action.class.getCanonicalName() + " fnd "
            + " CROSS JOIN " + User.class.getCanonicalName() + " usr "
            + " JOIN usr.puestos pst "
            + " JOIN pst.perfil prf "
            + " JOIN fnd.department dep"
            + " LEFT JOIN pst.une bu"
            + " LEFT JOIN pst.corp org"
            + " WHERE "
            + " fnd.status = " + Action.STATUS.IMPLEMENTED.getValue() + " "
            + " AND prf.intBEncargadoAccion = 1 "
            + " AND (("
                + " prf.intBUsuarioPlanta = 1"
                + " AND bu.id = dep.businessUnitId "
            + " ) OR ("
                + " prf.intBUsuarioCorporativo = 1"
                + " AND org.id = dep.organizationalUnitId"
            + " )) "
            + " AND fnd.deleted = 0";
    
    public ToEvaluate(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS);
        setScope(PendingRecord.Scope.USER);
        if (Utilities.getSettings().getActionManagerWhoEvaluate().equals(1)) {
            setQuery(TO_EVALUATE_BY_MODULE_MANAGER);
            setOwnerField("usr.id");
            setOwnerFieldFilter(BY_MODULE_MANAGER);
        } else {
            setQuery(TO_EVALUATE_BY_DEPARTMENT_MANAGER);
            setOwnerField("dep.attendantId");
            setOwnerFieldFilter(BY_DEPARTMENT_MANAGER);
        }
        setPendingType(getType(APE.FINDING_TO_EVALUATE));
        setModuleKey(MODULE);
        setBase(Action.class);
        setDependencies(
            ToAddPlan.class
        );
    }

}
