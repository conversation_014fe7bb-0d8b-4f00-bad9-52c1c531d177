package qms.finding.pending;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingInitializable;
import ape.pending.core.IPendingOperation;
import ape.pending.core.RuntimePending;
import ape.pending.dto.ColumnDTO;
import java.util.List;
import java.util.Set;
import mx.bnext.access.Module;
import qms.escalation.dto.EscalableDTO;
import qms.escalation.dto.IEscalableDTO;

/**
 *
 * <AUTHOR>
 */
public abstract class FindingPendingOperations extends RuntimePending implements IPendingOperation, IPendingInitializable {

    protected static final String MODULE = Module.ACTION.getKey();
    protected static final String ALIAS = "fnd";
    protected static final String BY_OWNER = " AND fnd.responsableId = :userId ";

    protected final String BY_DEPARTMENT_MANAGER = " AND fnd.department.attendantId = :userId ";
    protected final String BY_MODULE_MANAGER = " AND usr.id = :userId ";
    public static final List<ColumnDTO> BASE_SUMMARY_FIELDS = BaseSummaryFieldBuilder.columns(
        "id = findingId",
        "code = findingCode",
        "status = findingStatus",
        "description = findingDescription"
    );

    public FindingPendingOperations(IUntypedDAO dao) {
        super(dao);
        setSummaryTemplate("${findingCode}, ${findingDescription}");
        setBaseSummaryFields(BASE_SUMMARY_FIELDS);
    }
    
    @Override
    public void handleResultRecords(Set<IEscalableDTO> records) {}
    
    @Override
    public String getPathDailyMailTemplate() {
        return EscalableDTO.class.getPackage().getName() + ".LegacySourcePendingMailerTemplate";
    }
}
