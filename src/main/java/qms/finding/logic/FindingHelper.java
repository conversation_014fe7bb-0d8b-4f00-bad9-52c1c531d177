package qms.finding.logic;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Action;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.IAddressableUser;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.PendingHelper;
import ape.pending.util.ApeSelectType;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import static isoblock.common.Properties.AGG_ANALIZADAYENIMPLEMENTACION;
import static isoblock.common.Properties.AGG_APROBADA;
import static isoblock.common.Properties.AGG_ASIGNADA;
import static isoblock.common.Properties.AGG_CERRADA;
import static isoblock.common.Properties.AGG_IMPLEMENTADAYVERIFICADA;
import static isoblock.common.Properties.AGG_REPORTADA;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import qms.access.dto.LoggedUser;
import qms.access.util.AddressableUserHelper;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.entity.Activity;
import qms.activity.logic.ActivityHelper;
import qms.activity.pending.ActivityPendingFilter;
import qms.activity.util.CommitmentTask;
import qms.activity.util.IActivity;
import qms.finding.entity.FindingActivity;
import qms.finding.entity.FindingActivityType;
import qms.finding.pending.imp.ToAssign;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
public class FindingHelper extends PendingHelper {
    
    private final IUserDAO userDao;
    private final ActivityHelper actHelper;

    public FindingHelper(IUntypedDAO dao) {
        super(dao);
        actHelper = new ActivityHelper(dao); 
        this.userDao = dao.getBean(IUserDAO.class);
    }
    
    public BusinessUnitDepartment getDepartment(Action ent) {
        return userDao.HQLT_findById(BusinessUnitDepartment.class, ent.getUbicacionId());
    }

    /**
     * return the current department manager
     *
     * @param ent finding in which we want to know who is the department manager
     * @return Mail of department manager
     */
    public IAddressableUser getDepartmentManager(Action ent) {
        BusinessUnitDepartment dept = getDepartment(ent);
        UserRef manager = new UserRef(dept.getAttendant());
        IAddressableUser user = getAddressableUser(manager);
        if (user == null) {
            getLogger().error("No fue posible obtener el usuario encargado del departamento");
        }
        return user;
    }
    
    private IAddressableUser getAddressableUser(UserRef user) {
        if (Utilities.isValidAddressableUser(user)) {
            return user;
        }
        if (user.getId() == null) {
            return null;
        }
        final AddressableUserHelper userHelper = new AddressableUserHelper(dao);
        return userHelper.findUser(user.getId());
    }

    /**
     * return the current owner
     *
     * @param ent finding in which we want to know who is the owner
     * @return Mail of finding owner
     */
    public IAddressableUser getOwner(Action ent) {
        IAddressableUser user = getAddressableUser(
            new UserRef(ent.getResponsableId())
        );
        if (user == null) {
            getLogger().error("No fue posible obtener el usuario OWNER");
        }
        return user;
    }

    /**
     * return the author
     *
     * @param ent finding in which we want to know who is the author
     * @return Mail of finding owner
     */
    public IAddressableUser getAuthor(Action ent) {
        IAddressableUser user = getAddressableUser(
            new UserRef(ent.getAutorId())
        );
        if (user == null) {
            getLogger().error("No fue posible obtener el usuario OWNER");
        }
        return user;
    }

    /**
     * return a list of module managers in business unit
     *
     * @param ent finding in we which we want to know who is module manager in business unit
     * @return List of module managers
     */
    public Set<IAddressableUser> getModuleManagers(Action ent) {
        BusinessUnitDepartment dept = getDepartment(ent);
        Set<IAddressableUser> moduleManagers = new HashSet<>();
        moduleManagers.addAll(userDao.getFindingManagerInBusinessUnit(dept.getBusinessUnitId()));
        return moduleManagers;
    }
    /**
     * return a list of module managers in business unit
     *
     * @return List of module managers
     */
    public Set<UserRef> getModuleManagers() {
        Set<UserRef> moduleManagers = new HashSet<>();
        moduleManagers.addAll(userDao.getFindingManagers());
        return moduleManagers;
    }

    /**
     * return a list of participants in finding
     *
     * @param ent finding in we which we want to know who is participant
     * @return List of participant
     */
    public Set<IAddressableUser> getParticipants(Action ent) {
        Set<IAddressableUser> participants = new HashSet<>();
        participants.add(getDepartmentManager(ent));
        if(areAcceptedByModuleManager() || areEvaluatedByModuleManager()){
            participants.addAll(getModuleManagers(ent));
        }
        if (ent.getResponsableId() != null) {   
            participants.add(getOwner(ent));
        }
        participants.add(getAuthor(ent));
        participants.addAll(getActivitiesOwner(ent));
        participants.addAll(getActivitiesVerifier(ent));
        return participants.stream().filter(u -> Utilities.isValidAddressableUser(u)).collect(Collectors.toSet());
    }
    
    public Set<IAddressableUser> getParticipants(Action ent, Long activityId) {
        Set<IAddressableUser> participants = new HashSet<>();
        participants.add(getDepartmentManager(ent));
        if(areAcceptedByModuleManager() || areEvaluatedByModuleManager()){
            participants.addAll(getModuleManagers(ent));
        }
        if (ent.getResponsableId() != null) {   
            participants.add(getOwner(ent));
        }
        participants.add(getAuthor(ent));
        participants.addAll(getActivitiesOwnerById(activityId));
        participants.addAll(getActivitiesVerifierById(activityId));
        return participants.stream().filter(u -> Utilities.isValidAddressableUser(u)).collect(Collectors.toSet());
    }
    
    public Set<IAddressableUser> getParticipantsEvaluateAnalysisCause(Action action) {
        if (areAcceptedByModuleManager()) {
            return getModuleManagers(action);
        } else {
            Set<IAddressableUser> participants = new HashSet<>();
            participants.add(getDepartmentManager(action));
            return participants;
        }
    }

    /**
     * return the current activities owners
     *
     * @param ent finding in which we want to know who is the activities owner
     * @return List of mail of finding owner
     */
    public Set<UserRef> getActivitiesOwner(Action ent) {
        Set<UserRef> users = new HashSet();
        List<Long> activitiesId = getActivityId(ent.getId());
        for (Long activityId : activitiesId) {
            IActivity activity = dao.getBean(IActivityDAO.class).findPlainActivity(activityId);
            if (activity == null) {
                return new HashSet<>();
            }
          users.addAll(actHelper.getImplementers(activity));
        }
        return users;
    }
    
    public Set<UserRef> getActivitiesOwnerById(Long activityId) {
        IActivity activity = dao.getBean(IActivityDAO.class).findPlainActivity(activityId);
        if (activity == null) {
            return new HashSet<>();
        }
        return actHelper.getImplementers(activity);
    }

    /**
     * return the current activities owners
     *
     * @param ent finding in which we want to know who is the activities owner
     * @return List of mail of finding owner
     */
    public Set<UserRef> getActivitiesVerifier(Action ent) {
        Set<UserRef> users = new HashSet();
        List<Long> activitiesId = getActivityId(ent.getId());
        for (Long activityId : activitiesId) {
            IActivity activity = dao.getBean(IActivityDAO.class).findPlainActivity(activityId);
            if (activity == null) {
                return new HashSet<>();
            }
          users.addAll(actHelper.getVerifiers(activity));
        }
        return users;
    }
    
    public Set<UserRef> getActivitiesVerifierById(Long activityId) {
        IActivity activity = dao.getBean(IActivityDAO.class).findPlainActivity(activityId);
        if (activity == null) {
            return new HashSet<>();
        }
        return actHelper.getVerifiers(activity);
    }


    /**
     * return a list of Action which are not analyzed
     *
     * @param activity Activity to get Owner
     * @return List ofAction which are not assigned
     */
    public Set<UserRef> getActivityImplementers(IActivity activity) {
        return actHelper.getImplementers(activity);
    }

    public Boolean areAcceptedByModuleManager() {
        return Utilities.getSettings().getActionManagerWhoAccept().equals(1);
    }
    public Boolean areEvaluatedByModuleManager() {
        return Utilities.getSettings().getActionManagerWhoEvaluate().equals(1);
    }

    /**
     * Used to get the list of finding to assign
     *
     * @param loggedUser Logged user
     * @return List of finding to assign
     */
    public List<Action> getFindingsToAssign(LoggedUser loggedUser) {
        return userDao.HQL_findByQuery(new ToAssign(userDao).queryByUser(ApeSelectType.BY_ENTITY, loggedUser.getId()).toString());
    }
    /**
     * Used to get the list of finding to assign
     *
     * @param loggedUser Logged user
     * @return List of finding to assign
     */
    public List<Action> getFindingsToStartImplementation(LoggedUser loggedUser) {
        String query = new ToAssign(userDao).queryByUser(ApeSelectType.BY_ENTITY, loggedUser.getId()).toString();
        Map params = new HashMap();
        params.put("status", Action.STATUS.ASSIGNED.getValue());
        return userDao.HQL_findByQuery(query, params);
    }
    
    /**
     * Used to get the list of finding to analyze
     * 
     * @param loggedUser Logged user
     * @return List of finding to analyze
     */
    public List<Action> getFindingsToAnalyze(LoggedUser loggedUser) {
        String query = new ToAssign(userDao).queryByUser(ApeSelectType.BY_ENTITY, loggedUser.getId()).toString();
        Map params = new HashMap();
        params.put("status", Action.STATUS.ASSIGNED.getValue());
        return userDao.HQL_findByQuery(query, params);
    }
    
    /**
     * Used to get the list of finding to add plan
     *
     * @param loggedUser Logged user
     * @return List of finding to add plan
     */
    public List<Action> getFindingsToAddPlan(LoggedUser loggedUser) {
        String query = new ToAssign(userDao).queryByUser(ApeSelectType.BY_ENTITY, loggedUser.getId()).toString();
        Map params = new HashMap();
        params.put("status", Action.STATUS.IMPLEMENTED.getValue());
        return userDao.HQL_findByQuery(query, params);
    }
    
    public Action getFindingByReference(Long activityId) {
        String query = ""
                + " SELECT fnd"
                + " FROM " + Action.class.getCanonicalName() + " fnd "
                + " WHERE EXISTS ("
                + "     SELECT 1"
                + "     FROM " + FindingActivity.class.getCanonicalName() + " fa"
                + "     WHERE fa.id.activityId = :activityId"
                + "     AND fa.id.findingId = fnd.id"
                + " )";
        Action finding = (Action) userDao.HQL_findSimpleObject(query, "activityId", activityId);
        return finding;
    }
    
    public FindingActivity getFindingActivity(Long activityId) {
        return (FindingActivity) userDao.HQL_findSimpleObject(""
            + " SELECT c"
            + " FROM " + FindingActivity.class.getCanonicalName() + " c "
            + " WHERE c.id.activityId = " + activityId
        );
    }
    
    public List<Long> getActivityId(Long findingId) {
        return userDao.HQL_findByQuery(""
            + " SELECT c.id.activityId"
            + " FROM " + FindingActivity.class.getCanonicalName() + " c "
            + " WHERE c.id.findingId = " + findingId
            + " AND c.id.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
        );
    }
    
    public Long getFindingIdByReference(Long activityId) {
        String query = ""
                + " SELECT fnd.id"
                + " FROM " + Action.class.getCanonicalName() + " fnd "
                + " WHERE EXISTS ("
                + "     SELECT 1"
                + "     FROM " + FindingActivity.class.getCanonicalName() + " fa"
                + "     WHERE fa.id.activityId = :activityId"
                + "     AND fa.id.findingId = fnd.id"
                + "     AND exists ("
                + "         SELECT 1"
                + "         FROM " + Activity.class.getCanonicalName() + " act"
                + "         WHERE fa.id.activityId = act.id"
                + "         AND act.deleted = 0"
                + "     )"
                + " )"
                + " AND fnd.deleted = 0";
        return userDao.HQL_findSimpleLong(query, "activityId", activityId);
    }
    
    public Long getCountLeftActivitiesByReference(Long activityId) {
        Long findingId = getFindingIdByReference(activityId);
        return getCountLeftActivities(findingId);
    }
    
    public Long getCountLeftActivities(Long findingId) {
        String query = ""
                + " SELECT COUNT(act) "
                + " FROM "
                  + Activity.class.getCanonicalName() + " act "
                + " WHERE act.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND act.status NOT IN ( " 
                    + Activity.STATUS.VERIFIED.getValue() 
                    + ","  + Activity.STATUS.NOT_APPLY_VERIFIED.getValue() 
                    + ","  + Activity.STATUS.UNDONE.getValue() // <-- Las actividades de HALLAZGOS no aceptan recursividad y se quedan en 8
                    + ","  + Activity.STATUS.UNDONE_VERIFIED.getValue() 
                + ")"
                + " AND EXISTS ("
                + "     SELECT 1 "
                + "     FROM " + FindingActivity.class.getCanonicalName() + " fa"
                + "     WHERE fa.id.activityId = act.id"
                + "     AND fa.id.findingId = :findingId"
                + " )"
                + " AND act.deleted = 0";
        return userDao.HQL_findSimpleLong(query, "findingId", findingId);
    }

    public Long getCountPlannedActivitiesByReference(Long activityId) {
        Long findingId = getFindingIdByReference(activityId);
        
        String query = ""
                + " SELECT a.requireAnalysis "
                + " FROM " + Action.class.getCanonicalName() + " a "
                + " WHERE a.id = :id";
        Integer requireAnalysis = userDao.HQL_findSimpleInteger(query, "id", findingId);

        if (requireAnalysis == 0) {
            return 1L;
        }
        
        return getCountPlannedActivities(findingId);
    }
    
    public Long getCountPlannedActivities(Long findingId) {
        Map params = new HashMap(2);
        params.put("findingId", findingId);
        params.put("type", FindingActivityType.AAT.getValue());
        String query = ""
                + " SELECT COUNT(act) "
                + " FROM "
                  + Activity.class.getCanonicalName() + " act "
                + " WHERE EXISTS ("
                + "     SELECT 1 "
                + "     FROM " + FindingActivity.class.getCanonicalName() + " fa"
                + "     WHERE fa.id.activityId = act.id"
                + "     AND fa.id.findingId = :findingId"
                + "     AND fa.id.type = :type"
                + " )"
                + " AND act.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND act.deleted = 0";
        return userDao.HQL_findSimpleLong(query, params);
    }
    
    public String hasAccessToFinding(LoggedUser loggedUser) {
        String userId = loggedUser.getId().toString();
        return  " "
            + "  c.status != " + AGG_CERRADA 
            + " AND ("
                +  new ActivityPendingFilter(userDao).getMyFindingActivities(loggedUser.getId())
                + " OR ("
                + "     c.department.attendantId = " + userId + " AND c.status IN (" + AGG_REPORTADA + "," + AGG_IMPLEMENTADAYVERIFICADA + "," + AGG_APROBADA + ")"
                + " )"
                + " OR ("
                + "     c.attendant.id = " + userId + " AND c.status IN (" + AGG_ASIGNADA + "," + AGG_ANALIZADAYENIMPLEMENTACION + ")"
                + " )"
            + " )";
    }

    public boolean hasActivityACI(long findingId) {
        String query = ""
                + " SELECT count(c)"
                + " FROM " + FindingActivity.class.getCanonicalName() + " c "
                + " WHERE c.id.findingId = :id"
                + " AND c.id.type = " + FindingActivityType.ACI.getValue();
        return userDao.HQL_findSimpleLong(query, "id", findingId) > 0;
    }
    public boolean hasActivityAAT(long findingId) {
        String query = ""
                + " SELECT count(c)"
                + " FROM " + FindingActivity.class.getCanonicalName() + " c "
                + " WHERE c.id.findingId = :id"
                + " AND c.id.type = " + FindingActivityType.AAT.getValue();
        return userDao.HQL_findSimpleLong(query, "id", findingId) > 0;
    }

    public Boolean isActivityParticipant(Long findingId, Long loggedUserId) {
        String query = ""
                + " SELECT c.id.activityId"
                + " FROM " + FindingActivity.class.getCanonicalName() + " c "
                + " WHERE c.id.findingId = :id";
        List<Long> activityIds =  userDao.HQL_findByQuery(query, "id", findingId);     
        for(Long activityId : activityIds) {
            if(actHelper.isActivityParticipant(activityId, loggedUserId)) {
                return true;
            }
        }
        return false;
    }

    public FindingActivityType getFindingActivityType(final Long activityId) {
        final FindingActivity findingActivity = getFindingActivity(activityId);
        if (findingActivity == null) {
            return null;
        }
        Integer type = findingActivity.getId().getType();
        return FindingActivityType.getFindingActivityType(type);
    }
    
    public String getDepartmentDescription(Action finding) {
        return userDao.HQL_findSimpleString(""
                + " SELECT c.description"
                + " FROM " + BusinessUnitDepartmentRef.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", finding.getUbicacionId());
    }
    
    public boolean isRequireAnalysisOn() { 
        return  Utilities.getSettings().getRequireAnalysisCause() == 1;
    }
    
    public boolean isActionManagerWhoAccept() {
        return Utilities.getSettings().getActionManagerWhoAccept() == 1;
    }
    
    public Action getAction(Long id) {
        return userDao.HQLT_findById(Action.class, id);
    }
    
    public Boolean isVerifyToEvaluate(Long id) {
        String sQuery = ""
                + " Select a.id "
                + " From " + Action.class.getCanonicalName() + " c "
                + " CROSS JOIN " + FindingActivity.class.getCanonicalName() + " fa "
                + " CROSS JOIN " + Activity.class.getCanonicalName() + " a "
                + " Where c.id = fa.id.findingId "
                + "   and fa.id.activityId = a.id "
                + "   and fa.id.type = " + FindingActivityType.ACI.getValue()
                + "   and a.status = " + Activity.STATUS.VERIFIED.getValue()
                + "   and c.id = " + id;

        Long activityId = (Long) userDao.HQL_findSimpleObject(sQuery);

        sQuery = ""
                + "SELECT a.requireAnalysis "
                + " FROM " + Action.class.getCanonicalName() + " a "
                + " WHERE a.id = " + id;
        
        Integer requireAnalysis = userDao.HQL_findSimpleInteger(sQuery);
        return activityId != null && requireAnalysis == 0;
    }
}
