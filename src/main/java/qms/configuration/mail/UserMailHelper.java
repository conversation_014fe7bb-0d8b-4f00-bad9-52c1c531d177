package qms.configuration.mail;

import DPMS.Mapping.Position;
import DPMS.Mapping.Profile;
import DPMS.Mapping.User;
import Framework.Config.Mail;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import bnext.reference.UserRef;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import qms.configuration.entity.RelationshipUserPosition;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class UserMailHelper extends MailHelper {

    private static final String ESCALATION_MANAGER_LIST = ""
            + "SELECT user "
            + " FROM " + UserRef.class.getCanonicalName() + " user "
            + " , " + RelationshipUserPosition.class.getCanonicalName() + " userPosition "
            + " , " + Position.class.getCanonicalName() + " position "
            + " , " + Profile.class.getCanonicalName() + " profile "
            + " WHERE user.id = userPosition.id.userId "
            + "   AND userPosition.id.positionId = position.id "
            + "   AND position.perfil.id = profile.id "
            + "   AND user.status = " + User.STATUS.ACTIVE.getValue()
            + "   AND profile.escalationManager = 1";

    public UserMailHelper(IUntypedDAO dao) {
        super(dao);
    }

    public Set<Mail> getMails(Long id) {
        List<UserRef> users = new ArrayList<>();
        if (isUserWithOutPosition(id)) {
            users = dao.HQL_findByQuery(ESCALATION_MANAGER_LIST);
        }
        return toMailSet(new HashSet<>(users));
    }

    private Boolean isUserWithOutPosition(Long id) {
        String hql = " "
                + " SELECT COUNT(*)"
                + " FROM " + RelationshipUserPosition.class.getCanonicalName() + " userPosition "
                + " WHERE userPosition.id.userId = " + id;
        Long countPosition = dao.HQL_findSimpleLong(hql);
        return Objects.equals(countPosition, 0L);
    }

}
