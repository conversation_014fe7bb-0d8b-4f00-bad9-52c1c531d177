package qms.configuration.pending;

import DPMS.Mapping.User;
import DPMS.Mapping.UserSimple;
import Framework.Config.Utilities;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import bnext.reference.UserRef;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import qms.access.dto.AddressableUserDTO;
import qms.access.dto.ILoggedUser;
import qms.access.util.AddressableUserHelper;
import qms.configuration.dto.ConfigurationPendingDto;
import qms.configuration.pending.imp.ToAssignJob;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
public class ConfigurationPendingDataSource {
        
    public static Collection<IPendingDto> getPendingRecords(IPendingRecordDAO dao) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return Utilities.EMPTY_LIST;
        }
        final Collection<IPendingDto> pendings = new ArrayList<>();
        if (
            loggedUser.isAdmin() || (
                loggedUser.getServices() != null
                && loggedUser.getServices().contains(ProfileServices.ADMON_ACCESOS)
            )
        ) {
            final Date now = Utilities.getNow();
            final String base = UserRef.class.getCanonicalName();
            AddressableUserHelper helper = new AddressableUserHelper(dao);
            final List<AddressableUserDTO> users = helper.getUsersToActivate();
            users.forEach(pending -> {
                ConfigurationPendingDto value = new ConfigurationPendingDto();
                // valores propios de actividades
                value.setStatus(User.STATUS.TO_ACTIVATE.getValue());
                // valores requeridos para pendientes
                value.setPendingRecordId(pending.getId());
                value.setPendingRecordUserId(loggedUser.getId());
                value.setRecordId(pending.getId());
                value.setBase(base);
                value.setCode(pending.getCode());
                value.setPendingTypeCode(APE.CONFIGURATION_TO_ACTIVE_USER.getCode());
                value.setModule(mx.bnext.access.Module.CONFIGURATION);
                value.setDescription(pending.getDescription());
                value.setCommitmentDate(now);
                value.setCommitmentStartDate(value.getCommitmentDate());
                value.setCommitmentEndDate(value.getCommitmentDate());
                value.setAvailableActions(
                    PendingUtil.commonActions(
                        CommonAction.ATTEND
                    )
                );
                pendings.add(value);
            });
        }
        final Class<? extends BaseAPE> base = UserSimple.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToAssignJob(dao)
        };
        final Integer countPendings = dao.getPendingCount(loggedUser, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return pendings;
        }
        dao.getPendingRecords(
                loggedUser,
                base,
                ConfigurationPendingOperations.BASE_SUMMARY_FIELDS,
                operations
        ).forEach(pending -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            ConfigurationPendingDto value = new ConfigurationPendingDto();
            // valores propios de actividades
            value.setStatus((Integer) pending.get("userStatus"));
            // valores requeridos para pendientes
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setBase((String) pending.get("base"));
            value.setCode((String) pending.get("userCode"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.CONFIGURATION);
            value.setDescription((String) pending.get("userName"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            value.setAvailableActions(
                PendingUtil.commonActions(
                    CommonAction.ATTEND
                )
            );
            pendings.add(value);
        });
        return pendings;
    }
    
    public static IPendingOperation[] getPendingOperations(IPendingRecordDAO dao) {
        return new IPendingOperation[] {
            new ToAssignJob(dao)
        };
    }
    
    public static Class<? extends BaseAPE> getBaseEntity(Module module) {
        return UserSimple.class;
    }
}
