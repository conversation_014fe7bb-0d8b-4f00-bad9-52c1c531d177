package qms.configuration.rest;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.rest.FilesController;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.excel.AreaBulkManager;
import qms.framework.bulk.entity.BulkLog;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.DataExchangeParseError;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("area")
public class AreaController extends FilesController {

    private static final Logger LOGGER = Loggable.getLogger(AreaController.class);
    private final String HAS_ORG_ACCESS_ACCESS = "hasAnyAuthority('IS_ADMIN')";

    @Autowired
    @Qualifier("HibernateDAO_Area")
    private IAreaDAO dao;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @PostMapping()
    @RequestMapping({"list"})
    @PreAuthorize(HAS_ORG_ACCESS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo userGroupList(@RequestBody GridFilter filter) {

        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
        }
        return dao.HQL_getRows(""
            + " SELECT new map( "
                + " entity.id AS entity_id "
                + ",entity.code AS entity_code "
                + ",entity.description AS entity_description "
                + ",entity.status AS entity_status "
                + ",entity.departmentId AS department_id "
                + ",department.description AS department_description "
                + ",entity.buildingId AS building_id "
                + ",building.description AS building_description "
                + ",entity.attendantId AS attendant_id "
                + ",attendant.description AS attendant_description "
                + ",entity.customField1 AS entity_customField1 "
                + ",entity.customField2 AS entity_customField2 "
                + ",entity.customField3 AS entity_customField3 "
                + ",entity.customField4 AS entity_customField4 "
                + ",entity.customField5 AS entity_customField5 "
                + ",entity.customField6 AS entity_customField6 "
                + ",entity.customField7 AS entity_customField7 "
                + ",entity.customField8 AS entity_customField8 "
                + ",entity.customField9 AS entity_customField9 "
                + ",entity.customField10 AS entity_customField10 "
                + ",entity.customField11 AS entity_customField11 "
                + ",entity.customField12 AS entity_customField12 "
                + ",entity.customField13 AS entity_customField13 "
                + ",entity.customField14 AS entity_customField14 "
                + ",entity.customField15 AS entity_customField15 "
                + ",entity.customField16 AS entity_customField16 "
                + ",entity.customField17 AS entity_customField17 "
                + ",entity.customField18 AS entity_customField18 "
                + ",entity.customField19 AS entity_customField19 "
                + ",entity.customField20 AS entity_customField20 "
                + ",entity.customField21 AS entity_customField21 "
                + ",entity.customField22 AS entity_customField22 "
                + ",entity.customField23 AS entity_customField23 "
                + ",entity.customField24 AS entity_customField24 "
                + ",entity.customField25 AS entity_customField25 "
                + ",entity.customField26 AS entity_customField26 "
            + " )"
            + " FROM " + Area.class.getCanonicalName() + " entity "
            + " LEFT JOIN entity.attendant attendant "
            + " LEFT JOIN entity.building building "
            + " LEFT JOIN entity.department department "
            + " WHERE "
                + " entity.deleted = 0"
            , filter
        );
    }

    @GetMapping("custom-config")
    public ResponseEntity<Map<String,Object>> getCustomConfig() {
        Settings s = Utilities.getSettings();
        Map<String,Object> settings = new HashMap<>();
        settings.put("fieldsEnabled",s.getIsAreaCustomFieldsEnabled());
        settings.put("areaField1",s.getLabelAreaField1());
        settings.put("areaField2",s.getLabelAreaField2());
        settings.put("areaField3",s.getLabelAreaField3());
        settings.put("areaField4",s.getLabelAreaField4());
        settings.put("areaField5",s.getLabelAreaField5());
        settings.put("areaField6",s.getLabelAreaField6());

        settings.put("additionalExtraAreaFields", s.getAdditionalExtraAreaFields());
        settings.put("areaField7", s.getLabelAreaField7());
        settings.put("areaField8", s.getLabelAreaField8());
        settings.put("areaField9", s.getLabelAreaField9());
        settings.put("areaField10", s.getLabelAreaField10());
        settings.put("areaField11", s.getLabelAreaField11());
        settings.put("areaField12", s.getLabelAreaField12());
        settings.put("areaField13", s.getLabelAreaField13());
        settings.put("areaField14", s.getLabelAreaField14());
        settings.put("areaField15", s.getLabelAreaField15());
        settings.put("areaField16", s.getLabelAreaField16());
        settings.put("areaField17", s.getLabelAreaField17());
        settings.put("areaField18", s.getLabelAreaField18());
        settings.put("areaField19", s.getLabelAreaField19());
        settings.put("areaField20", s.getLabelAreaField20());
        settings.put("areaField21", s.getLabelAreaField21());
        settings.put("areaField22", s.getLabelAreaField22());
        settings.put("areaField23", s.getLabelAreaField23());
        settings.put("areaField24", s.getLabelAreaField24());
        settings.put("areaField25", s.getLabelAreaField25());
        settings.put("areaField26", s.getLabelAreaField26());

        return ResponseEntity.ok(settings);
    }

    @PostMapping("{id}/toggle-status")
    @PreAuthorize(HAS_ORG_ACCESS_ACCESS)
    public ResponseEntity<Map<String, Object>> toggleStatus(@PathVariable Long id) {
        return dao.toggleStatus(id, SecurityUtils.getLoggedUser());
    }

    @GetMapping("bulk-template-list-download")
    @PreAuthorize(HAS_ORG_ACCESS_ACCESS)
    public ResponseEntity<InputStreamResource> bulkTemplateListDownload() throws IOException {
        AreaBulkManager areaBulkManager = new AreaBulkManager(Utilities.getApplicationName(), "area", Utilities.getUntypedDAO(), Utilities.getLocale(), transactionManager);
        Path report = areaBulkManager.writeReport();
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,  "attachment; filename=" + report.getFileName())
                .contentLength(report.toFile().length())
                .body(new InputStreamResource(Files.newInputStream(report)));
    }

    @PostMapping("bulk-template-list-update")
    @PreAuthorize(HAS_ORG_ACCESS_ACCESS)
    public ResponseEntity<Map<String, Object>> bulkTemplateListUpdate(
            @RequestParam("file") MultipartFile multipart
    ) throws IOException, QMSException, DataExchangeParseError, SQLException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final AreaBulkManager areaBulkManager = new AreaBulkManager(
                Utilities.getApplicationName(),
                "area",
                Utilities.getUntypedDAO(),
                Utilities.getLocale(),
                transactionManager
        );
        final IBulkLog log = areaBulkManager.importReport(multipart, loggedUser);
        if (log == null) {
            return ResponseEntity.status(500).body(ImmutableMap.of("errorMessage", "bulk-upload-error"));
        }
        final  Map<String, Object> result = new HashMap<>(5);
        result.put("id", log.getId());
        result.put("description", log.getDescription());
        result.put("lastModifiedDate", log.getLastModifiedDate());
        result.put("sourceFileId", log.getSourceFileId());
        result.put("resultFileId", log.getResultFileId());
        return ResponseEntity.ok()
                .body(result);
    }

    @GetMapping("bulk-template-list-update/{bulkLogId}")
    @PreAuthorize(HAS_ORG_ACCESS_ACCESS)
    public ResponseEntity<Void> downloadBulkResult(
            final HttpServletResponse response,
            final @PathVariable(value = "bulkLogId", required = true) Long bulkLogId
    ) throws IllegalStateException, IOException, QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Long sourceFileId = dao.HQL_findLong(" "
                + " SELECT c.sourceFileId "
                + " FROM " + BulkLog.class.getCanonicalName() + " c"
                + " WHERE c.id = :bulkLogId", ImmutableMap.of("bulkLogId", bulkLogId), false, null, 0
        );
        return FileManager.download(response, sourceFileId, loggedUser);
    }


}
