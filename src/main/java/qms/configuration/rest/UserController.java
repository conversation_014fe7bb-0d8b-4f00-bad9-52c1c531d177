package qms.configuration.rest;

import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import DPMS.DAO.HibernateDAO_Request;
import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.DAOInterface.IUserIdNameDAO;
import DPMS.Mapping.BusinessUnitSettings;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.IUser;
import DPMS.Mapping.IUserLdap;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import DPMS.Mapping.UserWithTeam;
import Framework.Config.Utilities;
import bnext.dto.ProfileSchemaDTO;
import bnext.dto.ProfileScopeDTO;
import bnext.dto.ProfileServiceDTO;
import bnext.exception.MakePersistentException;
import bnext.licensing.LicenseUtil;
import bnext.login.Login;
import bnext.login.dto.ClientBrowserDTO;
import bnext.login.dto.ScreenResolutionDTO;
import bnext.login.util.ClientBrowserUtils;
import bnext.reference.UserHerarchy;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import mx.bnext.core.file.util.UuidUtils;
import mx.bnext.core.security.CryptoUtils;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.access.dto.MailUserScheduleDTO;
import qms.access.dto.MailUserTypeDTO;
import qms.access.logic.SessionHelper;
import qms.access.util.MailSettingsHandler;
import qms.access.util.MailSettingsUtil;
import qms.configuration.DAOInterface.IPingDAO;
import qms.configuration.dto.GridStateDTO;
import qms.configuration.dto.UserDataDto;
import qms.configuration.dto.UserDetailDto;
import qms.configuration.entity.AdLocation;
import qms.configuration.entity.GridStateByUser;
import qms.configuration.util.UserLayoutHelper;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.entity.Owner;
import qms.framework.entity.OwnerTeam;
import qms.framework.entity.OwnerTeamUser;
import qms.framework.entity.UserGroup;
import qms.framework.file.BnextSpringConfiguration;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;
import qms.framework.util.AreaUnitUtil;
import qms.framework.util.BusinessUnitUtil;
import qms.framework.util.DepartmentUnitUtil;
import qms.framework.util.MeasureTime;
import qms.util.AvatarUtils;
import qms.util.BindUtil;
import qms.util.EntityCommon;
import qms.util.GridFilter;
import qms.util.QMSException;
import qms.util.UserUtil;
import qms.util.dto.FormApproverAnalystByDepartment;

/**
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("rest/users")
public class UserController {

    private static final Logger LOGGER = Loggable.getLogger(UserController.class);
    private final String HAS_USER_TEAM_SAVE_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ADMON_ACCESOS', 'SPECIAL_ACCESS_EDIT_PROFILE'"
            + ")";
    private final String HAS_USER_GROUP_SAVE_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ADMON_ACCESOS', 'SPECIAL_ACCESS_EDIT_PROFILE'"
            + ")";
    private final String HAS_USER_SETTINGS_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ADMON_ACCESOS', 'ESCALATION_MANAGER'"
            + ")";
    private final String HAS_USER_HERIARCHY_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ESCALATION_MANAGER')";

    private final String HAS_ADMIN_ACESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ADMON_ACCESOS'"
            + ")";

    private final String HAS_CHANGE_DEFINED_USER_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_OUT_HISTORY', 'FILL_FORM'"
            + ")";

    private final String HAS_PAPERBIN_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ADMON_ACCESOS', 'SPECIAL_ACCESS_EDIT_PROFILE'"
            + ")";

    @Autowired
    @Qualifier("HibernateDAO_User")
    private IUserDAO dao;

    @Autowired
    @Qualifier("HibernateDAO_User_IdName")
    private IUserIdNameDAO mailDao;

    private String getAvatarSha512(final UserDetailDto details) {
        final Long avatarId = details.getAvatarId();
        if (avatarId != null && avatarId > 0) {
            String sha512 = dao.HQL_findSimpleString(""
                    + " SELECT c.contentSha512"
                    + " FROM " + FilesLite.class.getCanonicalName() + " c"
                    + " WHERE c.id = :id", "id", avatarId);
            if (sha512 != null && !sha512.isEmpty()) {
                return sha512;
            }
        }
        final String avatarSrc = AvatarUtils.getAvatarImageSrc(details.getUsername());
        try {
            return CryptoUtils.sha512(avatarSrc);
        } catch (IOException ex) {
            LOGGER.error("Failed generating sha512 for generated avatar of user {}", new Object[]{
                    details.getAccount(),
                    ex
            });
            return UuidUtils.toString(UUID.randomUUID()).replaceAll("-", "");
        }
    }

    @GetMapping(value = "/me")
    public ResponseEntity me(final HttpServletRequest request) throws InvalidArgumentException {
        final Long userId = SecurityUtils.getLoggedUserId();
        if (userId == null) {
            LOGGER.error("User not authenticated {}", userId);
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
        final UserDetailDto details = dao.HQLT_findSimple(UserDetailDto.class, ""
                        + " SELECT new " + UserDetailDto.class.getCanonicalName() + "("
                        + " u.id"                            // long
                        + ", u.code"                         // String
                        + ", u.description"                  // String
                        + ", u.cuenta"                       // String
                        + ", u.contacto"                     // String
                        + ", u.correo"                       // String
                        + ", u.lang"                         // String
                        + ", u.locale"                       // String
                        + ", u.showWelcomeDialog"            // int
                        + ", u.gridSize"                     // int
                        + ", u.floatingGridSize"             // int
                        + ", u.askToRenewLocation"           // boolean
                        + ", u.askToRenewTimezone"           // boolean
                        + ", u.avatarId"                     // long
                        + ", bu.id"                          // long
                        + ", bu.description"                 // String
                        + ", dep.id"                         // long
                        + ", dep.description"                // String
                        + ", u.askToRenewPassword"           // boolean
                        + ", u.isAway"                       // boolean
                + ", u.isAnonymous"                  // boolean
                        + ", u.legacyOpenFilters"            // int
                        + ", u.timezone"                     // String
                        + ", u.widgetConfigurationLayoutId"  // long
                        + ", u.widgetConfigurationHiddenId"  // long
                        + ", u.myActivitiesTabSelected"      // String
                        + " )"
                        + " FROM " + User.class.getCanonicalName() + " u "
                        + " LEFT JOIN u.businessUnit bu "
                        + " LEFT JOIN u.businessUnitDepartment dep"
                        + " WHERE u.id = :userId",
                "userId", userId
        );
        if (details == null) {
            LOGGER.error("User not found {}", userId);
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        if (details.getLanguage() == null || details.getLanguage().isEmpty()) {
            details.setLanguage(Utilities.getSettings().getLang());
        }
        if (details.getCountry() == null || details.getCountry().isEmpty()) {
            details.setCountry(Utilities.getSettings().getLocale());
        }
        final UserLayoutHelper helper = new UserLayoutHelper(dao);
        if (details.getWidgetConfigurationLayoutId() != null) {
            details.setWidgetConfigurationLayout(helper.load(details.getWidgetConfigurationLayoutId(), userId));
        }
        if (details.getWidgetConfigurationHiddenId() != null) {
            details.setWidgetConfigurationHidden(helper.load(details.getWidgetConfigurationHiddenId(), userId));
        }
        if (details.getTimezone() == null || details.getTimezone().isEmpty()) {
            details.setTimezone(Utilities.getSettings().getTimeZone().trim());
        }
        final String avatarSha512 = getAvatarSha512(details);
        details.setAvatarSha512(avatarSha512);
        final String renewPassword = SecurityUtils.getRenewPasswordAttribute(request);
        details.setAskToRenewPassword(Boolean.TRUE.toString().equals(renewPassword));
        details.setOidcLogin(SecurityUtils.isOidcLogin());
        details.setModules(SecurityUtils.getLoggedUserModules());
        final List<String> loggedUserAuthorities = SecurityUtils.getLoggedUserAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        details.setServices(loggedUserAuthorities);
        final ClientBrowserDTO browser = ClientBrowserUtils.parseBrowser(request, true);
        details.setBrowser(browser.getBrowser());
        details.setBrowserOs(browser.getBrowserOs());
        details.setBrowserOsVersion(browser.getBrowserOsVersion());
        details.setBusinessUnitLabels(
                BusinessUnitUtil.labelMap(
                        BusinessUnitUtil.fromKey(details.getLanguage())
                )
        );
        details.getBusinessUnitLabels().putAll(
                DepartmentUnitUtil.labelMap(
                        DepartmentUnitUtil.fromKey(details.getLanguage())
                )
        );
        details.getBusinessUnitLabels().putAll(
                AreaUnitUtil.labelMap(
                        AreaUnitUtil.fromKey(details.getLanguage())
                )
        );
        final UserLogin userLogin = SessionHelper.getUserLoginInfo(request.getSession());
        if (userLogin != null) {
            details.setProvidedLocation(userLogin.getProvidedLocation());
            details.setProvidedScreenResolution(userLogin.getProvidedScreenResolution());
        } else {
            details.setProvidedLocation(false);
            details.setProvidedScreenResolution(false);
        }
        details.setActivityWorkflows(SecurityUtils.getLoggedUserActivityWorkflows());
        final Settings settings = Utilities.getSettings();
        details.setMaximumFileSize(BnextSpringConfiguration.getUploadFileMaxSizeBytes());
        if (settings.getNewsBySociety() == 1) {
            Boolean active = dao.HQL_findSimpleInteger(""
                            + " SELECT "
                            + " bus.news"
                            + " FROM " + BusinessUnitSettings.class.getCanonicalName() + " bus "
                            + " WHERE bus.businessUnitId = :buId",
                    "buId", details.getBusinessUnitId()
            ) == 1;
            details.setNewsActive(active);
        } else {
            details.setNewsActive(true);
        }
        details.setShowManuals(Utilities.getSettings().getShowManuals());
        return ResponseEntity.ok(details);
    }

    @GetMapping(value = "/license-schemas")
    @PreAuthorize("hasAnyAuthority('IS_ADMIN', 'ADMON_ACCESOS', 'USUARIO_CORPORATIVO', 'SPECIAL_ACCESS_EDIT_PROFILE')")
    public List<ProfileSchemaDTO> allLicenseSchemas() {
        return LicenseUtil.getLicenseSchemas();
    }

    @GetMapping(value = "/profile-scopes")
    @PreAuthorize("hasAnyAuthority('IS_ADMIN', 'ADMON_ACCESOS', 'USUARIO_CORPORATIVO', 'SPECIAL_ACCESS_EDIT_PROFILE')")
    public List<ProfileScopeDTO> allProfileScopes() {
        return LicenseUtil.getProfileScopes();
    }

    @GetMapping(value = "/profile-services")
    @PreAuthorize("hasAnyAuthority('IS_ADMIN', 'ADMON_ACCESOS', 'USUARIO_CORPORATIVO', 'SPECIAL_ACCESS_EDIT_PROFILE')")
    public List<ProfileServiceDTO> allProfileServices() {
        return LicenseUtil.getServices();
    }

    /**
     * Desmarca al usuario de estar "ausente"
     *
     * @return
     */
    @GetMapping(value = "/not-away")
    public ResponseEntity freeApproverReplacements() {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        final List<FormApproverAnalystByDepartment> departmentRegistries = UserUtil.getFormApproverSubstitutes(loggedUserId);
        // Libera pendientes de autorización de formularios
        if (!dao.notAwayAnymore(loggedUserId)) {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        dao.refreshFormApproversPendings(loggedUserId, departmentRegistries);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/ping")
    public Date ping() {
        ElapsedDataDTO tStart = MeasureTime.start(Loggable.LOGGER.PING, UserController.class);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        Loggable.getLogger(Loggable.LOGGER.PING, UserController.class).info(
                ">> ping: {} - {} - {}",
                loggedUser.getId(),
                loggedUser.getDescription(),
                new Date()
        );
        IPingDAO pingDao = Utilities.getBean(IPingDAO.class);
        pingDao.ping(loggedUser);

        MeasureTime.stop(tStart, "Elapsed time loading ping");
        return new Date();
    }

    @GetMapping(value = "/read-welcome")
    public ResponseEntity readWelcome() {
        dao.readShowWelcomeDialog(SecurityUtils.getLoggedUserId());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/hasIncompleteForm/{surveyId}")
    public ResponseEntity hasIncompleteForm(
            final @PathVariable(value = "surveyId", required = false) Long surveyId) {
        final IRequestDAO daoRequest = Utilities.getBean(IRequestDAO.class);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Map<String, Object> results = daoRequest.HQL_findSimpleMap(
                HibernateDAO_Request.REQUEST_DRAFTS_QUERY,
                ImmutableMap.of(
                        "surveysId", surveyId,
                        "creatorUserId", loggedUser.getId()
                )
        );
        return new ResponseEntity<>(results, HttpStatus.OK);
    }

    @GetMapping(value = "/destroyFillForm/{outstandingSurveyId}/{requestId}")
    public ResponseEntity destroyFillForm(
            final @PathVariable(value = "outstandingSurveyId", required = false) Long outstandingSurveyId,
            final @PathVariable(value = "requestId", required = false) Long requestId
    ) {
        IRequestDAO daoRequest = Utilities.getBean(IRequestDAO.class);
        if (daoRequest.destroyRequest(requestId, null)) {
            IOutstandingSurveysDAO daoSurveys = Utilities.getBean(IOutstandingSurveysDAO.class);
            if (daoSurveys.destroyOutstandingSurvey(requestId, outstandingSurveyId, null, false, null) == 1) {
                return new ResponseEntity<>(HttpStatus.OK);
            }
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @GetMapping(value = "/user-data")
    public ResponseEntity getLdapUserData() {
        String loggedUserAccount = SecurityUtils.getLoggedUserAccount();
        Map<String, Object> userAtributes = ActiveDirectoryUtil.searchForUserAttributes(
                loggedUserAccount,
                "samaccountname",       // cuenta
                "mail",                 // correo electrónico
                "displayname",          // nombre completo
                "telephonenumber",      // conmutador
                "othertelephone",       // extensión
                "mobile",               // celular
                "streetaddress",        // dirección
                "l",                    // ciudad
                "st",                   // estado/provicia
                "postalcode"            // código postal
        );
        if (userAtributes == null) {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        List<Map> locations = dao.HQL_findByQuery(""
                + " SELECT new map("
                + " c.id AS id,"
                + " c.description AS description,"
                + " c.address AS address,"
                + " c.postalCode AS postalCode,"
                + " c.city AS city,"
                + " c.stateProvince AS stateProvince,"
                + " c.phone AS phone"
                + " )"
                + " FROM " + AdLocation.class.getCanonicalName() + " c "
                + " WHERE"
                + " c.deleted = 0 "
                + " AND c.status = 1 "
        );
        userAtributes.put("locations", locations);
        return new ResponseEntity<>(userAtributes, HttpStatus.OK);
    }

    @PostMapping("update-user-data")
    public ResponseEntity updateUserLocation(@RequestBody UserDataDto data) {
        String loggedUserAccount = SecurityUtils.getLoggedUserAccount();
        Map<String, String> userData = new HashMap<>(4);
        userData.put("streetaddress", data.getStreetAddress());
        userData.put("l", data.getL());
        userData.put("st", data.getSt());
        userData.put("postalcode", data.getPostalCode());
        userData.put("telephonenumber", data.getTelephoneNumber());
        userData.put("othertelephone", data.getOtherTelephone());
        userData.put("mobile", data.getMobile());
        if (ActiveDirectoryUtil.updateAttribute(loggedUserAccount, userData)) {
            IUserLdap user = Login.getUserData(loggedUserAccount);
            if (dao.updateUserLdap(user)) {
                LOGGER.debug("User DB-DATA could not be updated from LDAP, {}", loggedUserAccount);
            }
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("location")
    public ResponseEntity updateLocation(@RequestBody GeolocationCoordinates data, HttpServletRequest request) {
        try {
            final boolean updated = SessionHelper.updateLocation(data, SecurityUtils.getLoggedUser(), request.getSession());
            if (updated) {
                return new ResponseEntity<>(HttpStatus.OK);
            } else {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }
        } catch(Exception e) {
            LOGGER.error("Error updating location", e);
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PostMapping("screen-resolution")
    public ResponseEntity updateScreenResolution(@RequestBody ScreenResolutionDTO data, HttpServletRequest request) {
        try {
            final boolean updated = SessionHelper.updateScreenResolution(data, SecurityUtils.getLoggedUser(), request.getSession());
            if (updated) {
                return new ResponseEntity<>(HttpStatus.OK);
            } else {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }
        } catch(Exception e) {
            LOGGER.error("Error updating location", e);
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PostMapping({"user-team/add-global-users/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity addGlobalTeam(
            @PathVariable(value = "ownerId", required = true) Long ownerId,
            @RequestBody List<Long> userIds
    ) {
        return this.addTeam(ownerId, null, userIds, OwnerTeam.VALUE_TYPE.GLOBAL_USERS);
    }

    @PostMapping({"user-team/add-main-users/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity addMainTeam(
            @PathVariable(value = "ownerId", required = true) Long ownerId,
            @RequestBody List<Long> userIds
    ) {
        return this.addTeam(ownerId, null, userIds, OwnerTeam.VALUE_TYPE.MAIN_USERS);
    }

    @PostMapping({"user-team/add-groups/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity addGroupTeam(
            @PathVariable(value = "ownerId", required = true) Long ownerId,
            @RequestBody List<Long> groupIds
    ) {
        return this.addTeam(ownerId, null, groupIds, OwnerTeam.VALUE_TYPE.GROUP);
    }

    @PostMapping({"user-team/add-group-users/{groupId}/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity addGroupTeam(
            @PathVariable(value = "groupId", required = true) Long groupId,
            @PathVariable(value = "ownerId", required = true) Long ownerId,
            @RequestBody List<Long> userIds
    ) {
        return this.addTeam(ownerId, groupId, userIds, OwnerTeam.VALUE_TYPE.GROUP_USERS);
    }

    private ResponseEntity addTeam(
            Long ownerId,
            Long groupId,
            List<Long> ids,
            OwnerTeam.VALUE_TYPE type
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final OwnerTeam owner = dao.HQLT_findById(OwnerTeam.class, ownerId);
        final Set errors = new HashSet<>(ids.size());
        switch (type) {
            case GLOBAL_USERS:
                final Set<OwnerTeamUser> globalUsersValues = new HashSet<>(ids.size());
                dao.HQL_updateByQuery(""
                        + " DELETE FROM " + OwnerTeamUser.class.getCanonicalName() + " ou "
                        + " WHERE "
                        + " ou.type = " + OwnerTeamUser.TYPE.GLOBAL.getValue()
                        + " AND ou.owner.id = " + ownerId
                );
                ids.forEach((userId) -> {
                    globalUsersValues.add(new OwnerTeamUser(-1L, userId, null, owner, OwnerTeamUser.TYPE.GLOBAL));
                });
                owner.setUsers(globalUsersValues);
                break;
            case GROUP_USERS:
                final Set<OwnerTeamUser> groupUsersValues = new HashSet<>(ids.size());
                dao.HQL_updateByQuery(""
                        + " DELETE FROM " + OwnerTeamUser.class.getCanonicalName() + " ou "
                        + " WHERE "
                        + " ou.type = " + OwnerTeamUser.TYPE.BY_GROUP.getValue()
                        + " AND ou.groupId = " + groupId
                        + " AND ou.owner.id = " + ownerId
                );
                errors.addAll(dao.HQL_findByQuery(""
                                + " SELECT new map("
                                + " ou.userId AS userId "
                                + ",ou.user.description AS userName "
                                + ",ou.owner.description AS ownerName "
                                + " )"
                                + " FROM " + OwnerTeamUser.class.getCanonicalName() + " ou "
                                + " WHERE "
                                + " ou.groupId = " + groupId
                                + (!ids.isEmpty() ? " AND ou.userId IN ( :usersIds )" : ""),
                        ImmutableMap.of("usersIds", ids))
                );
                List<Long> occupied = (List<Long>) errors.stream().map((e) -> (Long) (((Map) e).get("userId"))).collect(Collectors.toList());
                // Se excluyen los que rompen la llave "idx_owneruser_group"
                ids = ids.stream().filter((id) -> !occupied.contains(id)).collect(Collectors.toList());
                ids.forEach((userId) -> {
                    groupUsersValues.add(new OwnerTeamUser(-1L, userId, groupId, owner, OwnerTeamUser.TYPE.BY_GROUP));
                });
                owner.setUsers(groupUsersValues);
                break;
            case MAIN_USERS:
                dao.HQL_updateByQuery(""
                        + " UPDATE " + User.class.getCanonicalName() + " ou "
                        + " SET ou.mainTeamOwner = null"
                        + " ,ou.lastModifiedDate = CURRENT_TIMESTAMP "
                        + " ,ou.lastModifiedBy = " + SecurityUtils.getLoggedUserId()
                        + " WHERE ou.mainTeamOwner.id = " + ownerId
                );
                final Set<UserWithTeam> mainUsersValues = new HashSet<>(ids.size());
                ids.forEach((userId) -> {
                    mainUsersValues.add(new UserWithTeam(userId, owner));
                });
                owner.setMainUsers(mainUsersValues);
                break;
            case GROUP:
                dao.SQL_updateByQuery(""
                                + " DELETE FROM user_group_owner "
                                + " WHERE owner_id = :ownerId",
                        ImmutableMap.of("ownerId", ownerId),
                        0,
                        Arrays.asList("user_group_owner")
                );
                final Set<UserGroup> groupValues = new HashSet<>(
                        dao.HQL_findByQuery(""
                                + " SELECT c"
                                + " FROM " + UserGroup.class.getCanonicalName() + " c"
                                + " WHERE " + BindUtil.parseFilterList("c.id", ids))
                );
                owner.setGroups(groupValues);
                break;
        }
        if (dao.makePersistent(owner, loggedUser.getId()) == null) {
            return ResponseEntity.ok(ImmutableMap.of(
                    "status", "failure",
                    "id", null,
                    "savedIds", null,
                    "code", null
            ));
        }
        return ResponseEntity.ok(ImmutableMap.of(
                "status", "success",
                "id", owner.getId(),
                "errors", errors,
                "savedIds", ids,
                "code", owner.getCode()
        ));
    }

    @PostMapping({"user-team/save-description/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity saveDescription(
            final @PathVariable(value = "ownerId", required = true) Long ownerId,
            final @RequestBody String description
    ) {
        if (description == null || description.trim().isEmpty()) {
            return new ResponseEntity(ImmutableMap.of(
                    "status", "empty"
            ), HttpStatus.CONFLICT);
        }
        final OwnerTeam owner = new OwnerTeam(ownerId, description);
        // Se requiere para que no valide por clave repetida
        owner.setCode(null);
        final Map<String, Boolean> isRepeated = isRepeatedOwnerTeam(owner);
        if (isRepeated != null) {
            return new ResponseEntity(isRepeated, HttpStatus.ALREADY_REPORTED);
        }
        final Map<String, Object> params = new HashMap<>();
        params.put("description", description);
        if (dao.HQL_updateByQuery(
                OwnerTeam.class,
                params,
                SecurityUtils.getLoggedUserId(),
                ownerId,
                false,
                null,
                0,
                null
        ) == 1) {
            return new ResponseEntity(ImmutableMap.of(
                    "status", "success",
                    "id", ownerId
            ), HttpStatus.CREATED);

        }
        return new ResponseEntity(ImmutableMap.of(
                "status", "fail"
        ), HttpStatus.CONFLICT);
    }

    @PostMapping({"user-team/save"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity saveTeam(
            final @RequestBody OwnerTeam owner
    ) throws QMSException {
        if (owner == null || owner.getDescription() == null) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Map<String, Boolean> isRepeated = isRepeatedOwnerTeam(owner);
        if (isRepeated != null) {
            return new ResponseEntity(isRepeated, HttpStatus.ALREADY_REPORTED);
        }
        final boolean isNew = owner.getId() == null || owner.getId().equals(-1L);
        if (owner.getType() == null) {
            owner.setType(Owner.TYPE.TEAM_BY_GROUP.getValue());
        }
        if (isNew && owner.getCode() == null) {
            owner.setCode(
                    EntityCommon.getNextCode(owner)
            );
        }
        if (owner.getMainUsers() != null) {
            owner.getMainUsers().forEach((u) -> {
                u.setMainTeamOwner(owner);
            });
        }
        if (dao.makePersistent(owner, loggedUser.getId()) == null) {
            return new ResponseEntity(ImmutableMap.of(
                    "status", "fail"
            ), HttpStatus.CONFLICT);
        }
        return new ResponseEntity(ImmutableMap.of(
                "status", "success",
                "id", owner.getId(),
                "code", owner.getCode()
        ), HttpStatus.CREATED);
    }

    private Map<String, Boolean> isRepeatedOwnerTeam(OwnerTeam owner) {
        return isRepeatedOwnerTeam(owner.getId(), owner.getCode(), owner.getDescription());
    }

    private Map<String, Boolean> isRepeatedOwnerTeam(Long id, String code, String description) {
        final Map<String, Boolean> repeated = new HashMap<>(2);
        final boolean isNew = id == null || id.equals(-1L);
        final StringBuilder hql = new StringBuilder(800);
        if (description != null) {
            // Si la descripción está repetida, agrega "description = true"
            hql.append(" "
                    + " SELECT count(c) "
                    + " FROM ").append(OwnerTeam.class.getCanonicalName()).append(" c "
                    + " WHERE lower(c.description) = :description");
            final Map<String, Object> params = new HashMap<>(2);
            if (!isNew) {
                hql.append(" AND c.id != :id");
                params.put("id", id);
            }
            params.put("description", description.trim().toLowerCase());
            final Integer descriptionCount = dao.HQL_findSimpleInteger(hql.toString(), params);
            if (descriptionCount > 0) {
                repeated.put("description", true);
            }
        }
        if (code != null) {
            // Si la clave está repetida, agrega "code = true"
            final StringBuilder hqlCode = new StringBuilder(800);
            hqlCode.append(" "
                    + " SELECT count(c) "
                    + " FROM ").append(OwnerTeam.class.getCanonicalName()).append(" c "
                    + " WHERE lower(c.code) = :code");
            final Map<String, Object> params = new HashMap<>(2);
            if (!isNew) {
                hqlCode.append(" AND c.id != :id");
                params.put("id", id);
            }
            params.put("code", code.trim().toLowerCase());
            final Integer codeCount = dao.HQL_findSimpleInteger(hqlCode.toString(), params);
            if (codeCount > 0) {
                repeated.put("code", true);
            }
        }
        if (Boolean.TRUE.equals(repeated.get("description")) || Boolean.TRUE.equals(repeated.get("code"))) {
            return repeated;
        }
        return null;
    }

    @GetMapping("user-team/toggle-status/{id}")
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    public ResponseEntity toogleTeamStatus(
            @PathVariable(value = "id", required = true) Long id
    ) {
        Integer status = dao.HQL_findSimpleInteger(""
                + " SELECT t.status FROM " + OwnerTeam.class.getCanonicalName() + " t WHERE t.id = " + id
        );
        status = status == 1 ? 0 : 1;
        if (
                dao.HQL_updateByQuery(""
                        + " UPDATE " + OwnerTeam.class.getCanonicalName() + " t "
                        + " SET t.status = " + status
                        + " ,t.lastModifiedDate = CURRENT_TIMESTAMP "
                        + " ,t.lastModifiedBy = " + SecurityUtils.getLoggedUserId()
                        + " WHERE t.id = " + id
                ) == 1
        ) {
            return new ResponseEntity<>(
                    ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }


    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    @GetMapping({"user-team/load/{ownerId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity loadTeam(
            @PathVariable(value = "ownerId", required = true) Long ownerId
    ) {
        // ToDo: Validar permisos
        OwnerTeam result = dao.HQLT_findById(OwnerTeam.class, ownerId);
        if (result == null) {
            throw new RuntimeException("Not found " + ownerId);
        }
        return new ResponseEntity(Utilities.getSerializedObj(result), HttpStatus.ACCEPTED);
    }

    @PostMapping({"user-team/list"})
    @PreAuthorize(HAS_USER_TEAM_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo userTeamList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.description");
        }
        return dao.HQL_getRows(""
                        + " SELECT new map( "
                        + " entity.id AS id "
                        + ",entity.code AS code "
                        + ",entity.description AS description "
                        + ",entity.type AS type "
                        + ",entity.status AS status "
                        + ",entity.createdDate AS createdDate "
                        + ",entity.lastModifiedDate AS lastModifiedDate "
                        + " )"
                        + " FROM " + OwnerTeam.class.getCanonicalName() + " entity "
                        + " WHERE "
                        + " entity.deleted = 0"
                        + " AND entity.type IN (" + Owner.TYPE.TEAM_BY_GROUP.getValue() + "," + Owner.TYPE.GLOBAL_TEAM.getValue() + ")"
                , filter
        );
    }

    @PostMapping({"user-group/save"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_USER_GROUP_SAVE_ACCESS)
    public ResponseEntity saveGroup(
            final @RequestBody UserGroup entity
    ) throws QMSException {
        if (entity == null || entity.getDescription() == null) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();

        final boolean isNew = entity.getId() == null || entity.getId().equals(-1L);
        if (isNew) {
            final Map<String, Boolean> isRepeated = isRepeatedGroup(entity);
            if (isRepeated != null) {
                return new ResponseEntity(isRepeated, HttpStatus.ALREADY_REPORTED);
            } else {
                if (entity.getCode() == null) {
                    entity.setCode(
                            EntityCommon.getNextCode(entity)
                    );
                }
            }
        }
        if (dao.makePersistent(entity, loggedUser.getId()) == null) {
            return new ResponseEntity(ImmutableMap.of(
                    "status", "fail"
            ), HttpStatus.CONFLICT);
        }
        return new ResponseEntity(ImmutableMap.of(
                "status", "success",
                "id", entity.getId(),
                "code", entity.getCode()
        ), HttpStatus.CREATED);
    }

    private Map<String, Boolean> isRepeatedGroup(UserGroup owner) {
        return isRepeatedGroup(owner.getId(), owner.getCode(), owner.getDescription());
    }

    private Map<String, Boolean> isRepeatedGroup(Long id, String code, String description) {

        final Map<String, Boolean> repeated = new HashMap<>(2);
        final boolean isNew = id == null || id.equals(-1L);
        StringBuilder hql = new StringBuilder(800);
        if (description != null) {
            // Si la descripción está repetida, agrega "description = true"
            hql.append(""
                    + " SELECT count(c) "
                    + " FROM ").append(UserGroup.class.getCanonicalName()).append(" c "
                    + " WHERE lower(c.description) = :description");
            if (!isNew) {
                hql.append(" AND c.id != ").append(id);
            }
            if (dao.HQL_findSimpleInteger(hql.toString(), ImmutableMap.of(
                    "description", description.trim().toLowerCase()
            )) > 0) {
                repeated.put("description", true);
            }
        }
        if (code != null) {
            // Si la clave está repetida, agrega "code = true"
            hql = new StringBuilder(800);
            hql.append(""
                    + " SELECT count(c) "
                    + " FROM ").append(UserGroup.class.getCanonicalName()).append(" c "
                    + " WHERE lower(c.code) = :code");
            if (!isNew) {
                hql.append(" AND c.id != ").append(id);
            }
            if (dao.HQL_findSimpleInteger(hql.toString(), "code", code.trim().toLowerCase()) > 0) {
                repeated.put("code", true);
            }
        }
        if (Boolean.TRUE.equals(repeated.get("description")) || Boolean.TRUE.equals(repeated.get("code"))) {
            return repeated;
        }
        return null;
    }

    @GetMapping("user-group/toggle-status/{id}")
    @PreAuthorize(HAS_USER_GROUP_SAVE_ACCESS)
    public ResponseEntity toogleGrupStatus(
            @PathVariable(value = "id", required = true) Long id
    ) {
        Integer status = dao.HQL_findSimpleInteger(""
                + " SELECT t.status FROM " + UserGroup.class.getCanonicalName() + " t WHERE t.id = " + id
        );
        status = status == 1 ? 0 : 1;
        if (
                dao.HQL_updateByQuery(""
                        + " UPDATE " + UserGroup.class.getCanonicalName() + " t "
                        + " SET t.status = " + status
                        + " ,t.lastModifiedDate = CURRENT_TIMESTAMP "
                        + " ,t.lastModifiedBy = " + SecurityUtils.getLoggedUserId()
                        + " WHERE t.id = " + id
                ) == 1
        ) {
            return new ResponseEntity<>(
                    ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }


    @PreAuthorize(HAS_USER_GROUP_SAVE_ACCESS)
    @GetMapping({"user-group/load/{userGroupId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity loadUserGroup(
            @PathVariable(value = "userGroupId", required = true) Long userGroupId
    ) {
        // ToDo: Validar permisos
        UserGroup result = dao.HQLT_findById(UserGroup.class, userGroupId);
        if (result == null) {
            throw new RuntimeException("Not found " + userGroupId);
        }
        return new ResponseEntity(Utilities.getSerializedObj(result), HttpStatus.ACCEPTED);
    }

    @PostMapping({"user-group/list"})
    @PreAuthorize(HAS_USER_GROUP_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo userGroupList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.description");
        }
        return dao.HQL_getRows(""
                        + " SELECT new map( "
                        + " entity.id AS id "
                        + ",entity.code AS code "
                        + ",entity.description AS description "
                        + ",entity.status AS status "
                        + ",entity.createdDate AS createdDate "
                        + ",entity.lastModifiedDate AS lastModifiedDate "
                        + " )"
                        + " FROM " + UserGroup.class.getCanonicalName() + " entity "
                        + " WHERE "
                        + " entity.deleted = 0"
                , filter
        );
    }

    @PreAuthorize(HAS_ADMIN_ACESS)
    @PostMapping("licensing-history")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> licensingHistory(@RequestBody GridFilter filter) {
        final ILicenseUserDAO licenseDao = Utilities.getBean(ILicenseUserDAO.class);
        return licenseDao.getLicensingHistoryRows(filter);
    }

    @GetMapping("check-if-has-avatar")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long checkIfHasAvatar() {
        return dao.HQL_findSimpleLong(""
                        + " SELECT u.avatarId "
                        + " FROM " + User.class.getCanonicalName() + " u "
                        + " WHERE u.id = :userId",
                "userId", SecurityUtils.getLoggedUserId());
    }

    private Long getWidgetConfigurationLayoutId(final Long userId) {
        final Long layoutId = dao.HQL_findLong(""
                        + " SELECT"
                        + " u.widgetConfigurationLayoutId"
                        + " FROM " + User.class.getCanonicalName() + " u "
                        + " WHERE u.id = :userId",
                "userId",
                userId
        );
        if (layoutId == null) {
            return -1l;
        }
        return layoutId;
    }

    private Long getWidgetConfigurationHiddenId(final Long userId) {
        final Long hiddenId = dao.HQL_findLong(""
                        + " SELECT"
                        + " u.widgetConfigurationHiddenId"
                        + " FROM " + User.class.getCanonicalName() + " u "
                        + " WHERE u.id = :userId",
                "userId",
                userId
        );
        if (hiddenId == null) {
            return -1l;
        }
        return hiddenId;
    }

    @PostMapping("save-widgets-layout")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveWidgetLayoutConfiguration(
            final @RequestBody String layout
    ) {
        final UserLayoutHelper helper = new UserLayoutHelper(dao);
        final Long userId = SecurityUtils.getLoggedUserId();
        final Long layoutId = getWidgetConfigurationLayoutId(userId);
        final Long savedLayoutId = helper.persist(layoutId, userId, layout);
        if (savedLayoutId != null && savedLayoutId > 0) {
            if (Objects.equals(layoutId, -1l)) {
                dao.HQL_updateByQuery(""
                                + " UPDATE " + User.class.getCanonicalName() + " u "
                                + " SET u.widgetConfigurationLayoutId = :layoutId "
                                + " WHERE u.id = :userId ",
                        ImmutableMap.of(
                                "layoutId", savedLayoutId,
                                "userId", userId
                        )
                );
            }
            return new ResponseEntity<>(HttpStatus.OK);
        } else if (Objects.equals(savedLayoutId, 0l)) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PostMapping("save-widgets-hidden")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveWidgetHiddenConfiguration(
            final @RequestBody String widgetHidden
    ) {
        final UserLayoutHelper helper = new UserLayoutHelper(dao);
        final Long userId = SecurityUtils.getLoggedUserId();
        final Long hiddenId = getWidgetConfigurationHiddenId(userId);
        final Long savedHiddenId = helper.persist(hiddenId, userId, widgetHidden);
        if (savedHiddenId != null && savedHiddenId > 0) {
            if (Objects.equals(hiddenId, -1l)) {
                dao.HQL_updateByQuery(""
                                + " UPDATE " + User.class.getCanonicalName() + " u "
                                + " SET u.widgetConfigurationHiddenId = :hiddenId "
                                + " WHERE u.id = :userId ",
                        ImmutableMap.of(
                                "hiddenId", savedHiddenId,
                                "userId", userId
                        )
                );
            }
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PostMapping("save-my-activities-tab-selected")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveMyActivitiesTabSelected(final @RequestBody String tabSelected) {
        if (tabSelected != null && !tabSelected.trim().isEmpty()) {
            final Long userId = SecurityUtils.getLoggedUserId();
            dao.HQL_updateByQuery(""
                            + " UPDATE " + User.class.getCanonicalName() + " u "
                            + " SET u.myActivitiesTabSelected = :tabSelected "
                            + " WHERE u.id = :userId ",
                    ImmutableMap.of(
                            "tabSelected", tabSelected,
                            "userId", userId
                    )
            );
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("grid-state/sync/{gridId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GridStateByUser> syncGridState(
            @PathVariable(value = "gridId", required = true) String gridId,
            @RequestBody GridStateDTO state
    ) throws InvalidArgumentException, MakePersistentException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final GridStateByUser result = dao.syncGridState(gridId, state, loggedUser);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }


    @GetMapping("grid-state/get/{gridId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GridStateDTO> getGridState(
            @PathVariable(value = "gridId", required = true) String gridId
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final GridStateDTO result = dao.getMyGridState(gridId, loggedUser);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PreAuthorize(HAS_USER_SETTINGS_ACCESS)
    @PostMapping("name/{userId}")
    public ResponseEntity<Map<String, String>> getName(
            final @PathVariable(value = "userId", required = true) Long userId
    ) {
        final String name = dao.HQL_findSimpleString(""
                        + " SELECT"
                        + " u.description"
                        + " FROM " + User.class.getCanonicalName() + " u "
                        + " WHERE u.id = :userId",
                "userId",
                userId
        );
        return new ResponseEntity<>(ImmutableMap.of("name", name), HttpStatus.OK);
    }

    @PreAuthorize(HAS_USER_SETTINGS_ACCESS)
    @PostMapping("mail-settings/types/{userId}")
    public GridInfo<MailUserTypeDTO> getMailSettingsTypes(
            final @PathVariable(value = "userId", required = true) Long userId
    ) {
        final List<String> modules = SecurityUtils.getLoggedUserModules().stream()
                .map((module) -> module.getKey())
                .collect(Collectors.toList());
        return MailSettingsUtil.getMailSettingsTypesByUser(userId, modules);
    }

    @PreAuthorize(HAS_USER_SETTINGS_ACCESS)
    @PostMapping("mail-settings/schedules/{userId}")
    public GridInfo<MailUserScheduleDTO> getMailSettingsSchedules(
            final @PathVariable(value = "userId", required = true) Long userId
    ) {
        final List<String> modules = SecurityUtils.getLoggedUserModules().stream()
                .map((module) -> module.getKey())
                .collect(Collectors.toList());
        return MailSettingsUtil.getMailSettingsSchedulesByUser(userId, modules);
    }

    @PreAuthorize(HAS_USER_SETTINGS_ACCESS)
    @PostMapping("mail-settings/types/update/{userId}")
    public ResponseEntity<List<Long>> updateMailSettingsTypes(
            final @PathVariable(value = "userId", required = true) Long userId,
            final @RequestBody List<MailUserTypeDTO> types
    ) {
        final List<Long> result = mailDao.updateMailSettingsTypeByUser(userId, types, SecurityUtils.getLoggedUserId());
        if (result != null && result.size() == types.size()) {
            MailSettingsHandler.reset();
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @PreAuthorize(HAS_USER_SETTINGS_ACCESS)
    @PostMapping("mail-settings/schedules/update/{userId}")
    public ResponseEntity<List<Long>> updateSchedulesMailSettings(
            final @PathVariable(value = "userId", required = true) Long userId,
            final @RequestBody List<MailUserScheduleDTO> schedules
    ) {
        final List<Long> result = mailDao.updateMailSettingsScheduleByUser(userId, schedules, SecurityUtils.getLoggedUserId());
        if (result != null && result.size() == schedules.size()) {
            MailSettingsHandler.reset();
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }


    @GetMapping({"heriarchy", "heriarchy/{userId}"})
    @PreAuthorize(HAS_USER_HERIARCHY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<UserHerarchy>> usersHeriarchy(
            final @PathVariable(value = "userId", required = false) Long userId
    ) {
        final Boolean hasUserId = userId != null && userId > 0;
        List<UserHerarchy> users = null;

        if (hasUserId) {
            final Map<String, Object> params = new HashMap<>(1);
            params.put("id", userId);
            users = dao.HQL_findByQuery(""
                    + " SELECT c"
                    + " FROM " + UserHerarchy.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id AND c.status = 1", params);
            setSurbordinatesRecursivity(users, userId);
        } else {
            users = dao.HQL_findByQuery(""
                    + " SELECT c"
                    + " FROM " + UserHerarchy.class.getCanonicalName() + " c"
                    + " WHERE c.status = 1"
            );
            Long firstNodeId = users.get(0).getId();
            setSurbordinatesRecursivity(users.stream().filter(f -> f.getId().equals(firstNodeId)).collect(Collectors.toList()), firstNodeId);
        }

        return new ResponseEntity(users, HttpStatus.OK);
    }

    private void setSurbordinatesRecursivity(List<UserHerarchy> users, Long directBoss) {
        users.forEach(user -> {
            List<UserHerarchy> subordinates = dao.HQL_findByQuery(""
                            + " SELECT c"
                            + " FROM " + UserHerarchy.class.getCanonicalName() + " c "
                            + " WHERE c.bossId = :id AND c.status = :active AND c.id != :bossId",
                    ImmutableMap.of("id", user.getId(),
                            "bossId", directBoss,
                            "active", User.STATUS.ACTIVE.getValue()));
            user.setChildren(subordinates);
            if (!subordinates.isEmpty()) {
                setSurbordinatesRecursivity(subordinates, directBoss);
            }
        });
    }


    @GetMapping("get-all-users")
    @PreAuthorize(HAS_USER_HERIARCHY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity getAllUsers() {
        return new ResponseEntity(dao.getUsersAll(), HttpStatus.OK);
    }

    @GetMapping("get-all-users-to-define-form/{currentUserId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_CHANGE_DEFINED_USER_ACCESS)
    public GridInfo getAllUsersToDefineForm(@RequestBody GridFilter filter, @PathVariable(name = "currentUserId", required = true) Long currentUserId) {
        if (currentUserId != null && currentUserId > 0) {
            filter.getCriteria().put("<condition>", "c.id != " + currentUserId);
        }
        return dao.getAllUsersToDefineForm(filter, SecurityUtils.getLoggedUser());
    }


    @GetMapping("deleted-users")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_PAPERBIN_ACCESS)
    public GridInfo<IUser> deletedUsers(@RequestBody GridFilter filter) {
        return dao.deletedUsers(filter);
    }

    @PostMapping("restore-user")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_PAPERBIN_ACCESS)
    public ResponseEntity<Boolean> restoreUser(@RequestBody Long userId) {
        return new ResponseEntity<>(dao.restoreUser(userId, SecurityUtils.getLoggedUserId()), HttpStatus.OK);
    }
}
