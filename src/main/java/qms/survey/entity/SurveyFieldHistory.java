package qms.survey.entity;

import Framework.Config.CompositeStandardEntity;
import bnext.aspect.IExcludeLogging;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedCompositeEntity;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "survey_field_history")
public class SurveyFieldHistory extends CompositeStandardEntity<SurveyFieldHistoryPK> 
        implements Serializable, IExcludeLogging, ILinkedCompositeEntity<SurveyFieldHistoryPK> {

    private static final long serialVersionUID = 1L;
    
    private SurveyFieldHistoryPK id;

    public SurveyFieldHistory() {
    }

    public SurveyFieldHistory(SurveyFieldHistoryPK id) {
        this.id = id;
    }

    public SurveyFieldHistory(Long commentId, Long fieldId, Long outstandingSurveysId, Long requestId) {
        this.id = new SurveyFieldHistoryPK(commentId, fieldId, outstandingSurveysId, requestId);
    }

    @Override
    public SurveyFieldHistoryPK identifuerValue() {
        return id;
    }

    @Override
    public void setId(SurveyFieldHistoryPK surveyFieldHistoryPK) {
        this.id = surveyFieldHistoryPK;
    }

    @EmbeddedId
    @Override
    public SurveyFieldHistoryPK getId() {
        return id;
    }
    
    @Override
    public int hashCode() {
        int hash = 3;
        hash = 43 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyFieldHistory other = (SurveyFieldHistory) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "SurveyFieldHistory{" + "id=" + id + '}';
    }

}
