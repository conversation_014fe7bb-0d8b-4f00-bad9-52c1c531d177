package qms.meeting.logic;

import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.Mapping.Periodicity;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.PendingHelper;
import com.sun.star.auth.InvalidArgumentException;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.SerializationUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.meeting.dto.RecurrenceMeetingDTO;
import qms.meeting.entity.Meeting;
import qms.meeting.entity.MeetingComment;
import qms.meeting.entity.MeetingFile;
import qms.meeting.entity.MeetingParticipant;

/**
 *
 * <AUTHOR>
 */
public class RecurringMeetingHelper extends PendingHelper {

    public RecurringMeetingHelper(IUntypedDAO dao) {
        super(dao);
    }

    public void schedulingMeetings(Long loggedUserId) {
        List<RecurrenceMeetingDTO> recurrences = getRecurrenceMeetings();
        if (recurrences.isEmpty()) {
            getLogger().warn("No meetings to schedule");
            return;
        }
        IPeriodicityDAO periodicityDao = dao.getBean(IPeriodicityDAO.class);
        Date now = Utilities.truncDate(new Date());
        for (RecurrenceMeetingDTO recurrence : recurrences) {
            Date next = Utilities.truncDate(periodicityDao.getNextOccurence(recurrence.getLastRecurrence(), recurrence.getPeriodicityId()));
            if (next.equals(recurrence.getLastRecurrence()) || next.after(now)) {
                continue;
            }
            if (next.before(now)) {
                next = now;
            }
            generateNextRecurrence(recurrence, next, loggedUserId);
        }

    }

    private void generateNextRecurrence(RecurrenceMeetingDTO recurrence, Date next,  Long loggedUserId) {
        try {
            Meeting nextMeeting = createNextRecurrence(recurrence, next);
            dao.makePersistent(nextMeeting, loggedUserId);
            cloneFiles(nextMeeting, loggedUserId);
            cloneComments(nextMeeting, loggedUserId);
            cloneParticipants(nextMeeting, loggedUserId);
            updateNextDate(nextMeeting.getRecurrenceId(), nextMeeting.getFinishOn());
        } catch (InvalidArgumentException ex) {
            getLogger().error("Fail while schedule recurrence for meeting {}", recurrence, ex);
        }
    }
    
    private void updateNextDate(Long meetingId, Date finishOn){
        IPeriodicityDAO periodicty = dao.getBean(IPeriodicityDAO.class);
        Meeting recurringMeeting = dao.HQLT_findById(Meeting.class, meetingId);
        if(recurringMeeting == null || recurringMeeting.getPeriodicity() == null) {
            getLogger().error("Missing recurrence meeting id, meetingId: {}", meetingId);
            return;
        }
        Date nextDate = Utilities.truncDate(periodicty.getNextOccurence(finishOn, recurringMeeting.getPeriodicity().getId()));
        Map params = new HashMap();
        params.put("nextDate", nextDate);
        params.put("id", recurringMeeting.getId());
        dao.HQL_updateByQuery(""
                + " UPDATE " + Meeting.class.getCanonicalName() + " "
                + " SET nextDate = :nextDate "
                + " WHERE id = :id", params);
    }

    private Meeting createNextRecurrence(RecurrenceMeetingDTO recurrence, Date next) throws InvalidArgumentException {
        Meeting meeting = dao.HQLT_findById(Meeting.class, recurrence.getMeetingId());
        Meeting nextMeeting = SerializationUtils.clone(meeting);
        nextMeeting.setId(-1L);
        nextMeeting.setPeriodicity(null);
        nextMeeting.setOutstandingSurveyId(null);
        nextMeeting.setStatus(Meeting.STATUS.REPORTED.getValue());
        nextMeeting.setRecurrent(IPeriodicEntity.RECURRENT.NO.getValue());
        nextMeeting.setRecurrenceId(meeting.getId());
        String numberRecurrence = new DecimalFormat("##").format(recurrence.getNumberRecurrence() + 1);
        String newCode = meeting.getCode() + "-" + numberRecurrence;
        nextMeeting.setCode(newCode);
        resetAuditedFields(nextMeeting);
        calculateStartOn(meeting, next, nextMeeting);
        calculateFinishOn(meeting, next, nextMeeting);
        nextMeeting.setNextDate(null);
        nextMeeting.setUpdateSequence(0);
        nextMeeting.setLastHashCode(0);
        nextMeeting.setCurrentHashCode(meeting.hashCode());
        return nextMeeting;
    }

    private void resetAuditedFields(Meeting nextMeeting) {
        nextMeeting.setCreatedBy(null);
        nextMeeting.setCreatedDate(null);
        nextMeeting.setLastModifiedBy(null);
        nextMeeting.setLastModifiedDate(null);
    }

    private void calculateFinishOn(Meeting meeting, Date next, Meeting nextMeeting) {
        DateTime finishOn = new DateTime(meeting.getFinishOn());
        DateTime newFinishOn = new DateTime(next);
        newFinishOn = newFinishOn.withHourOfDay(finishOn.getHourOfDay());
        newFinishOn = newFinishOn.withMinuteOfHour(finishOn.getMinuteOfHour());
        DateTime startOn = new DateTime(meeting.getStartOn());
        Integer days = Days.daysBetween(startOn, finishOn).getDays();
        if (days > 0) {
            newFinishOn = newFinishOn.plusDays(days);
        }
        nextMeeting.setFinishOn(newFinishOn.toDate());
    }

    private void calculateStartOn(Meeting meeting, Date next, Meeting nextMeeting) {
        DateTime startOn = new DateTime(meeting.getStartOn());
        DateTime newStartOn = new DateTime(next);
        newStartOn = newStartOn.withHourOfDay(startOn.getHourOfDay());
        newStartOn = newStartOn.withMinuteOfHour(startOn.getMinuteOfHour());
        nextMeeting.setStartOn(newStartOn.toDate());
    }

    private void cloneParticipants(final Meeting nextMeeting, final Long loggedUserId) {
        List<Long> participants = dao.HQL_findByQuery(""
                + " SELECT c.id.participantId"
                + " FROM " + MeetingParticipant.class.getCanonicalName() + " c"
                        + " WHERE c.id.meetingId = :id", "id", nextMeeting.getRecurrenceId());
        dao.saveLinkedItems(MeetingParticipant.class, nextMeeting.getId(), participants, false, loggedUserId);
    }

    private void cloneComments(final Meeting nextMeeting, final Long loggedUserId) {
        List<Long> comments = dao.HQL_findByQuery(""
                + " SELECT c.id.commentId"
                + " FROM " + MeetingComment.class.getCanonicalName() + " c"
                        + " WHERE c.id.meetingId = :id", "id", nextMeeting.getRecurrenceId());
        dao.saveLinkedItems(MeetingComment.class, nextMeeting.getId(), comments, false, loggedUserId);
    }

    private void cloneFiles(final Meeting nextMeeting, final Long loggedUserId) {
        List<Long> files = dao.HQL_findByQuery(""
                + " SELECT c.id.fileId"
                + " FROM " + MeetingFile.class.getCanonicalName() + " c"
                        + " WHERE c.id.meetingId = :id", "id", nextMeeting.getRecurrenceId());
        dao.saveLinkedItems(MeetingFile.class, nextMeeting.getId(), files, false, loggedUserId);
    }

    private List<RecurrenceMeetingDTO> getRecurrenceMeetings() {
        String hql = ""
                + " SELECT new " + RecurrenceMeetingDTO.class.getCanonicalName() + "("
                    + " recurrence.id as meetingId, max(last.finishOn) as lastRecurrence, count(last.finishOn) as numberRecurrence, p.id as periodicityId"
                + " )"
                + " FROM " + Meeting.class.getCanonicalName() + " recurrence"
                + " CROSS JOIN " + Meeting.class.getCanonicalName() + " last"
                + " INNER JOIN recurrence.periodicity p"
                + " WHERE recurrence.id = last.recurrenceId"
                + " AND recurrence.deleted = 0 "
                + " AND recurrence.recurrent = " + IPeriodicEntity.RECURRENT.YES
                + " AND p.vchtipoperiodicidad <> '" + Periodicity.NEVER + "'"
                + " GROUP BY recurrence.id, p.id";
        List<RecurrenceMeetingDTO> recurrences = dao.HQL_findByQuery(hql);
        return recurrences;
    }

}
