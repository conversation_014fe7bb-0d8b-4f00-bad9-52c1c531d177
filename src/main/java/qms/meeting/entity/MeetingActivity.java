package qms.meeting.entity;

import Framework.Config.CompositeStandardEntity;
import ape.pending.core.SoftBaseAPE;
import ape.pending.core.SoftEntityId;
import ape.pending.core.StrongEntityId;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.activity.entity.Activity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR> @ Bnext
 * @since October 03, 2016
 *
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "meeting_activity")
public class MeetingActivity extends CompositeStandardEntity<MeetingActivityPK> 
        implements SoftBaseAPE<Meeting, Activity>, Serializable, ILinkedComposityGrid<MeetingActivityPK> {

    private static final long serialVersionUID = 1L;

    private MeetingActivityPK id;
    private Long meetingId;
    private Long activityId;

    public MeetingActivity() {
    }

    public MeetingActivity(MeetingActivityPK id) {
        this.id = id;
    }

    public MeetingActivity(Long meetingId, Long activityId) {
        this.id = new MeetingActivityPK(meetingId, activityId);
    }

    @Override
    public MeetingActivityPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public MeetingActivityPK getId() {
        return id;
    }

    @Override
    public void setId(MeetingActivityPK id) {
        this.id = id;
    }
    
    @Basic(optional = false)
    @StrongEntityId
    @Column(name = "meeting_id", insertable = false, updatable = false)
    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    @SoftEntityId
    @Basic(optional = false)
    @Column(name = "activity_id", insertable = false, updatable = false)
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 79 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingActivity other = (MeetingActivity) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "MeetingActivity{" + "id=" + id + '}';
    }

}
