package qms.meeting.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * @<PERSON>do Ger<PERSON> @ Bnext.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "meeting_type_dynamic_field")
public class MeetingTypeDynamicField extends CompositeStandardEntity<MeetingTypeDynamicFieldPK>
        implements Serializable, ILinkedComposityGrid<MeetingTypeDynamicFieldPK> {

    private static final long serialVersionUID = 1L;

    private MeetingTypeDynamicFieldPK id;

    public MeetingTypeDynamicField() {
    }

    public MeetingTypeDynamicField(MeetingTypeDynamicFieldPK id) {
        this.id = id;
    }

    public MeetingTypeDynamicField(Long dynamicFieldId, Long meetingTypeId) {
        this.id = new MeetingTypeDynamicFieldPK(dynamicFieldId, meetingTypeId);
    }

    @Override
    public MeetingTypeDynamicFieldPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public MeetingTypeDynamicFieldPK getId() {
        return id;
    }

    @Override
    public void setId(MeetingTypeDynamicFieldPK id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingTypeDynamicField other = (MeetingTypeDynamicField) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DocumentTypeDynamicField{" + "id=" + id + '}';
    }

}
