package qms.meeting.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR> @ Bnext
 * @since October 03, 2016
 *
 */
@Embeddable
public class MeetingActivityPK implements Serializable {

    private Long meetingId;
    private Long activityId;

    public MeetingActivityPK(Long meetingId, Long participantId) {
        this.meetingId = meetingId;
        this.activityId = participantId;
    }

    public MeetingActivityPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "meeting_id")
    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 71 * hash + Objects.hashCode(this.meetingId);
        hash = 71 * hash + Objects.hashCode(this.activityId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingActivityPK other = (MeetingActivityPK) obj;
        if (!Objects.equals(this.meetingId, other.meetingId)) {
            return false;
        }
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "MeetingActivityPK{" + "meetingId=" + meetingId + ", activityId=" + activityId + '}';
    }

}
