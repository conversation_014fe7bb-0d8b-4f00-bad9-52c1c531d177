/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.struts;

import Framework.Action.BnextAction;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.io.IOException;
import java.sql.SQLException;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.happyornot.entity.HappyornotLink;
import static qms.happyornot.dao.HappyornotDAO.URL_BASE_TOKEN;

/**
 *
 * <AUTHOR>
 */
public class HappyornotSurveyAction extends BnextAction {

    private static final Logger LOGGER = Loggable.getLogger(HappyornotSurveyAction.class);
    private Long happyornotTargetId;
    private String urlToRedirect = "";
    
    @Override
    public String execute() throws IOException, SQLException {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        
        final String linkToRedirect = dao.HQL_findSimpleString(" "
            + " SELECT link.token"
            + " FROM " + HappyornotLink.class.getCanonicalName() + " link "
            + " JOIN link.targetSurvey t"
            + " WHERE t.id = :id", "id", happyornotTargetId);
        
        if (!linkToRedirect.isEmpty()) {
            urlToRedirect = URL_BASE_TOKEN + linkToRedirect;
            return "redirectToToken";
        } else {
            LOGGER.error("Error not url token available: {}", happyornotTargetId);
            return SessionViewer.NO_ACCESS;
        }
    }

    public Long getHappyornotTargetId() {
        return happyornotTargetId;
    }

    public void setHappyornotTargetId(Long happyornotTargetId) {
        this.happyornotTargetId = happyornotTargetId;
    }

    public Long getId() {
        return happyornotTargetId;
    }

    public void setId(Long happyornotTargetId) {
        this.happyornotTargetId = happyornotTargetId;
    }
	
    public String getUrlToRedirect() {
        return urlToRedirect;
    }

    public void setUrlToRedirect(String urlToRedirect) {
        this.urlToRedirect = urlToRedirect;
    }
    
}
