/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.struts;

import Framework.Action.BnextAction;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.Date;
import java.util.Map;
import mx.bnext.cipher.Encryptor;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import qms.happyornot.dao.IHappyornotDAO;
import qms.happyornot.entity.HappyornotLink;
import static Framework.Action.SessionViewer.NO_ACCESS;

/**
 *
 * <AUTHOR>
 */
public class HappyornotSurveyTokenAction extends BnextAction {

    private static final String INACTIVE_SURVEY = "inactive_survey";
    private String html, question = "Is a question", name;
    private Integer aspectHeight = 800, aspectWidth = 1280;
    private String messagePosition = "CENTER";
    private Long waitingTime = 1000L;
    private boolean answerPerDay = false;
    private boolean allowFeedback = false;
    private String settingsLang = "";
    private String settingsLocale = "";
    private String token;
    private Boolean saveUserAgent = true;
    private Long linkId;
    private Long happyornotTargetId;
    
    private enum SurveyStatus {
        ENABLED("ENABLED", 1),
        DISABLED("DISABLED", 0);

        private String key;
        private Integer value;

        private SurveyStatus(String key, Integer value) {
            this.key = key;
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }

    @Override
    public String execute() throws Exception {
        //Se agrega la información de traducción al inicio y asi esten disponibles en NO_ACCESS e INACTIVE_SURVEY
        this.settingsLang = Utilities.getSettings().getLang();
        this.settingsLocale = Utilities.getSettings().getLocale();
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final String plainToken = decryptToken();
        if (plainToken == null) {
            return NO_ACCESS;
        }
        final Map<String, Object> link = dao.HQL_findSimpleMap(""
                + "SELECT new map ("
                    + " l.code AS code,"
                    + " l.id AS idLink,"
                    + " t.id AS targetId,"
                    + " s.status AS status,"
                    + " s.id AS surveyId,"
                    + " s.html AS html,"
                    + " s.description AS description,"
                    + " s.aspectHeight AS aspectHeight,"
                    + " s.aspectWidth AS aspectWidth,"
                    + " s.messagePosition AS messagePosition,"
                    + " s.waitingTime AS waitingTime,"
                    + " t.description AS targetDescription,"
                    + " s.answerPerDay AS answerPerDay,"
                    + " s.allowFeedback AS allowFeedback,"
                    + " t.status AS targetStatus"
                + ")"
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l"
                + " JOIN l.targetSurvey t "
                + " JOIN t.happyornotSurvey s "
                + " WHERE l.code = :plainToken", "plainToken", plainToken);
        if (link.get("status").equals(SurveyStatus.DISABLED.getValue()) || link.get("targetStatus").equals(SurveyStatus.DISABLED.getValue())) {
            return INACTIVE_SURVEY;
        }
        linkId = Long.parseLong(link.get("idLink").toString());
        happyornotTargetId = Long.parseLong(link.get("targetId").toString());
        if (link.get("surveyId") == null) {
            return NO_ACCESS;
        }
        final IHappyornotDAO daoHON = Utilities.getBean(IHappyornotDAO.class);
        Date expiredDate = daoHON.getExpiredDateToke(plainToken);
        if (expiredDate != null) {
            Date currentDate = new Date();
            if (currentDate.after(expiredDate)) {
                return INACTIVE_SURVEY;
            }
        }
        if (!daoHON.checkViewsPerLink(plainToken)) {
            return INACTIVE_SURVEY;
        }
        daoHON.updateCounterView(plainToken);
        this.html = link.get("html").toString(); 
        this.question = link.get("description").toString();
        this.aspectHeight = Integer.parseInt(link.get("aspectHeight").toString());
        this.aspectWidth = Integer.parseInt(link.get("aspectWidth").toString());
        this.messagePosition = link.get("messagePosition").toString();
        this.waitingTime = Long.parseLong(link.get("waitingTime").toString());
        this.name = link.get("targetDescription").toString();
        this.answerPerDay = link.get("answerPerDay") != null ? Boolean.parseBoolean(link.get("answerPerDay").toString()) : false;
        this.allowFeedback = link.get("allowFeedback") != null ? Boolean.parseBoolean(link.get("allowFeedback").toString()) : false;
        
        return SUCCESS;
    }

    private String decryptToken() {
        if (token == null || token.isEmpty()) {
            getLogger().error("Invalid token [{}].", token);
            return null;
        }
        try {
            final String plainToken = Encryptor.decrypt(token, Utilities.getSettings().getSystemId());
            return plainToken;
        } catch (Exception e) {
            getLogger().error("Failed to decrypt token [{}].", token, e);
            return null;
        }
    }

    public Integer getAspectHeight() {
        return aspectHeight;
    }

    public Integer getAspectWidth() {
        return aspectWidth;
    }
    
    public String getMessagePosition() {
        return messagePosition;
    }

    public Long getWaitingTime() {
        return waitingTime;
    }

    public String getQuestion() {
        return question;
    }

    public String getName() {
        return name;
    }

    public String getHtml() {
        return html;
    }
	
    public Boolean getAnswerPerDay(){
        return answerPerDay;
    }
    
    public Boolean getAllowFeedback() {
        return allowFeedback;
    }
    
    public String getSettingsLang() {
        return settingsLang;
    }
    
    public String getSettingsLocale() {
        return settingsLocale;
    }

    public String getToken() {
        return token;
    }

    @StrutsParameter
    public void setToken(String token) {
        this.token = token;
    }

    public Boolean getSaveUserAgent() {
        return saveUserAgent;
    }

    public void setSaveUserAgent(Boolean saveUserAgent) {
        this.saveUserAgent = saveUserAgent;
    }

    public Long getLinkId() {
        return linkId;
    }

    @StrutsParameter
    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }

    public Long getHappyornotTargetId() {
        return happyornotTargetId;
    }

    @StrutsParameter
    public void setHappyornotTargetId(Long happyornotTargetId) {
        this.happyornotTargetId = happyornotTargetId;
    }
}
