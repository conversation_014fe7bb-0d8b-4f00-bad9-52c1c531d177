/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import jakarta.persistence.Convert;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.SQLRestriction;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.template.entity.TemplateDataMap;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "happyornot_survey")
public class HappyornotSurvey extends StandardEntity<HappyornotSurvey> implements IAuditableEntity, Serializable {

    private static final long serialVersionUID = 1L;

    private String code;
    private String description = "";
    private Integer status = StandardEntity.STATUS.ACTIVE.getValue();
    private Integer deleted = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private Long logoFileId;
    private Integer aspectWidth;
    private Integer aspectHeight;
    private String html;
    private String header;
    private String headerPlain;
    private String footer;
    private String footerPlain;
    private String screenBackground;
    private String questionColor;
    private String facesAlignment;
    private String facesOrientation;
    private String facesDesign;
    private String messagePosition;
    private Long waitingTime;
    private String publishTarget;
    private Boolean negativeReport;
    private String timeNegativeReport;
    private Boolean answerPerDay;
    private Boolean alertNegativeAnswer;
    private Boolean allowAnonymousAnswer = true;
    private Boolean allowMultipleUserAnswer = true;
    private String fontStyle;
    private Double fontSize;
    private String feedbackTitle;
    private Boolean allowFeedback;
    private Boolean hasCustomsAnswers;
    private String customAnswer1;
    private String customAnswerName1;
    private String customAnswer2;
    private String customAnswerName2;
    private String customAnswer3;
    private String customAnswerName3;
    private String customAnswer4;
    private String customAnswerName4;
    private String customAnswer5;
    private String customAnswerName5;
    private String customAnswer6;
    private String customAnswerName6;
    private String customAnswer7;
    private String customAnswerName7;
    private String customAnswer8;
    private String customAnswerName8;
    private String customAnswer9;
    private String customAnswerName9;
    private String customAnswer10;
    private String customAnswerName10;
    private List<HappyornotTarget> targetNames;
    private List<HappyornotMail> targetMails;

    // Campos del template
    private List<TemplateDataMap> sizesList;

    public HappyornotSurvey() {
        this.id = -1L;
        this.status = StandardEntity.STATUS.ACTIVE.getValue();
        this.deleted = 0;
    }

    public HappyornotSurvey(Long id) {
        this.id = id;
        this.status = StandardEntity.STATUS.ACTIVE.getValue();
        this.deleted = 0;
    }


    @Id
    @Column(name = "happyornot_survey_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "happyornotSurvey")
    @Fetch(value = FetchMode.SUBSELECT)
    @SQLRestriction("deleted=0")
    @OrderBy("description")
    public List<HappyornotTarget> getTargetNames() {
        return targetNames;
    }

    public void setTargetNames(List<HappyornotTarget> targetNames) {
        this.targetNames = targetNames;
        if (targetNames == null) {
            return;
        }
        this.targetNames.forEach((targetName) -> {
            targetName.setHappyornotSurvey(this);
        });
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "happyornotSurvey")
    @Fetch(value = FetchMode.SUBSELECT)
    @SQLRestriction("deleted=0")
    @OrderBy("mail")
    public List<HappyornotMail> getTargetMails() {
        return targetMails;
    }

    public void setTargetMails(List<HappyornotMail> targetMails) {
        this.targetMails = targetMails;
        if (targetMails == null) {
            return;
        }
        this.targetMails.forEach((targetMail) -> {
            targetMail.setHappyornotSurvey(this);
        });
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public void setLogoFileId(Long logoFileId) {
        this.logoFileId = logoFileId;
    }

    public void setAspectWidth(Integer aspectWidth) {
        this.aspectWidth = aspectWidth;
    }

    public void setAspectHeight(Integer aspectHeight) {
        this.aspectHeight = aspectHeight;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public void setHeaderPlain(String headerPlain) {
        this.headerPlain = headerPlain;
    }

    public void setFooter(String footer) {
        this.footer = footer;
    }

    public void setFooterPlain(String footerPlain) {
        this.footerPlain = footerPlain;
    }

    public void setScreenBackground(String screenBackground) {
        this.screenBackground = screenBackground;
    }

    public void setQuestionColor(String questionColor) {
        this.questionColor = questionColor;
    }

    public void setFacesAlignment(String facesAlignment) {
        this.facesAlignment = facesAlignment;
    }

    public void setFacesOrientation(String facesOrientation) {
        this.facesOrientation = facesOrientation;
    }

    public void setFacesDesign(String facesDesign) {
        this.facesDesign = facesDesign;
    }

    public void setMessagePosition(String messagePosition) {
        this.messagePosition = messagePosition;
    }

    public void setWaitingTime(Long waitingTime) {
        this.waitingTime = waitingTime;
    }

    public void setPublishTarget(String publishTarget) {
        this.publishTarget = publishTarget;
    }
    
    public void setNegativeReport(Boolean negativeReport){
        this.negativeReport = negativeReport;
    }
    
    public void setTimeNegativeReport(String timeNegativeReport){
        this.timeNegativeReport = timeNegativeReport;
    }
    
    public void setAnswerPerDay(Boolean answerPerDay){
        this.answerPerDay = answerPerDay;
    }
    
    public void setAlertNegativeAnswer(Boolean alertNegativeAnswer) {
        this.alertNegativeAnswer = alertNegativeAnswer;
    }

    public void setAllowAnonymousAnswer(Boolean allowAnonymousAnswer) {
        this.allowAnonymousAnswer = allowAnonymousAnswer;
    }

    public void setAllowMultipleUserAnswer(Boolean allowMultipleUserAnswer) {
        this.allowMultipleUserAnswer = allowMultipleUserAnswer;
    }

    public void setAllowFeedback(Boolean allowFeedback) {
        this.allowFeedback = allowFeedback;
    }
    
    public void setFontStyle(String fontStyle) {
        this.fontStyle = fontStyle;
    }
    
    public void setFontSize(Double fontSize) {
        this.fontSize = fontSize;
    }
    
    public void setHasCustomsAnswers(Boolean hasCustomsAnswers) {
        this.hasCustomsAnswers = hasCustomsAnswers;
    }
    
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    @CreatedDate
    @Column(name = "created_date", updatable = false)
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @CreatedBy
    @Override
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Column(name = "logo_file_id")
    public Long getLogoFileId() {
        return logoFileId;
    }

    @Column(name = "aspect_width")
    public Integer getAspectWidth() {
        return aspectWidth;
    }

    @Column(name = "aspect_height")
    public Integer getAspectHeight() {
        return aspectHeight;
    }

    @Column(name = "html")
    public String getHtml() {
        return this.html;
    }

    @Column(name = "header")
    public String getHeader() {
        return this.header;
    }

    @Column(name = "footer")
    public String getFooter() {
        return this.footer;
    }

    @Column(name = "header_plain")
    public String getHeaderPlain() {
        return this.headerPlain;
    }

    @Column(name = "footer_plain")
    public String getFooterPlain() {
        return this.footerPlain;
    }

    @Column(name = "screen_background")
    public String getScreenBackground() {
        return this.screenBackground;
    }

    @Column(name = "question_color")
    public String getQuestionColor() {
        return this.questionColor;
    }

    @Column(name = "faces_alignment")
    public String getFacesAlignment() {
        return this.facesAlignment;
    }

    @Column(name = "faces_orientation")
    public String getFacesOrientation() {
        return this.facesOrientation;
    }

    @Column(name = "faces_design")
    public String getFacesDesign() {
        return this.facesDesign;
    }

    @Column(name = "message_position")
    public String getMessagePosition() {
        return this.messagePosition;
    }
    
    @Column(name = "waiting_time")
    public Long getWaitingTime() {
        return this.waitingTime;
    }
    
    @Column(name = "publish_target")
    public String getPublishTarget() {
        return this.publishTarget;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @JoinTable(
            name = "template_happyornotsurvey_size",
            joinColumns = @JoinColumn(name = "happyornot_survey_id"),
            inverseJoinColumns = @JoinColumn(name = "template_data_map_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @JsonIgnore
    public List<TemplateDataMap> getSizesList() {
        return sizesList;
    }

    @Column(name="negative_report")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getNegativeReport(){
        return this.negativeReport;
    }
    
    @Column(name="time_negative_report")
    public String getTimeNegativeReport(){
        return this.timeNegativeReport;
    }
    
    @Column(name="answer_per_day")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAnswerPerDay(){
        return this.answerPerDay;
    }
    
    @Column(name="alert_negative_answer")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAlertNegativeAnswer(){
        return this.alertNegativeAnswer;
    }

    @Column(name="allow_anonymous_answer")
    @Type(type = "numeric_boolean")
    public Boolean getAllowAnonymousAnswer() {
        return allowAnonymousAnswer;
    }

    @Column(name="allow_multiple_user_answer")
    @Type(type = "numeric_boolean")
    public Boolean getAllowMultipleUserAnswer() {
        return allowMultipleUserAnswer;
    }

    @Column(name="font_style")
    public String getFontStyle() {
        return this.fontStyle;
    }
    
    @Column(name="font_size")
    public Double getFontSize() {
        return this.fontSize;
    }

    @Column(name="feedback_title")
    public String getFeedbackTitle() {
        return feedbackTitle;
    }

    public void setFeedbackTitle(String feedbackTitle) {
        this.feedbackTitle = feedbackTitle;
    }

    @Column(name="allow_feedback")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAllowFeedback() {
        return this.allowFeedback;
    }
    
    @Column(name="hasCustomsAnswers")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getHasCustomsAnswers() {
        return this.hasCustomsAnswers;
    }
    
    public void setSizesList(List<TemplateDataMap> sizesList) {
        this.sizesList = sizesList;
    }

    @Column(name = "customAnswer1")
    public String getCustomAnswer1() {
        return customAnswer1;
    }
    
    public void setCustomAnswer1(String customAnswer1) {
        this.customAnswer1 = customAnswer1;
    }
    @Column(name = "customAnswerName1")
    public String getCustomAnswerName1() {
        return customAnswerName1;
    }

    public void setCustomAnswerName1(String customAnswerName1) {
        this.customAnswerName1 = customAnswerName1;
    }
    @Column(name = "customAnswer2")
    public String getCustomAnswer2() {
        return customAnswer2;
    }

    public void setCustomAnswer2(String customAnswer2) {
        this.customAnswer2 = customAnswer2;
    }
    @Column(name = "customAnswerName2")
    public String getCustomAnswerName2() {
        return customAnswerName2;
    }

    public void setCustomAnswerName2(String customAnswerName2) {
        this.customAnswerName2 = customAnswerName2;
    }
    
    @Column(name = "customAnswer3")
    public String getCustomAnswer3() {
        return customAnswer3;
    }

    public void setCustomAnswer3(String customAnswer3) {
        this.customAnswer3 = customAnswer3;
    }
    @Column(name = "customAnswerName3")
    public String getCustomAnswerName3() {
        return customAnswerName3;
    }

    public void setCustomAnswerName3(String customAnswerName3) {
        this.customAnswerName3 = customAnswerName3;
    }
    @Column(name = "customAnswer4")
    public String getCustomAnswer4() {
        return customAnswer4;
    }

    public void setCustomAnswer4(String customAnswer4) {
        this.customAnswer4 = customAnswer4;
    }
    @Column(name = "customAnswerName4")
    public String getCustomAnswerName4() {
        return customAnswerName4;
    }

    public void setCustomAnswerName4(String customAnswerName4) {
        this.customAnswerName4 = customAnswerName4;
    }
    @Column(name = "customAnswer5")
    public String getCustomAnswer5() {
        return customAnswer5;
    }

    public void setCustomAnswer5(String customAnswer5) {
        this.customAnswer5 = customAnswer5;
    }
    @Column(name = "customAnswerName5")
    public String getCustomAnswerName5() {
        return customAnswerName5;
    }

    public void setCustomAnswerName5(String customAnswerName5) {
        this.customAnswerName5 = customAnswerName5;
    }
    @Column(name = "customAnswer6")
    public String getCustomAnswer6() {
        return customAnswer6;
    }

    public void setCustomAnswer6(String customAnswer6) {
        this.customAnswer6 = customAnswer6;
    }
    @Column(name = "customAnswerName6")
    public String getCustomAnswerName6() {
        return customAnswerName6;
    }

    public void setCustomAnswerName6(String customAnswerName6) {
        this.customAnswerName6 = customAnswerName6;
    }
    @Column(name = "customAnswer7")
    public String getCustomAnswer7() {
        return customAnswer7;
    }

    public void setCustomAnswer7(String customAnswer7) {
        this.customAnswer7 = customAnswer7;
    }
    @Column(name = "customAnswerName7")
    public String getCustomAnswerName7() {
        return customAnswerName7;
    }

    public void setCustomAnswerName7(String customAnswerName7) {
        this.customAnswerName7 = customAnswerName7;
    }
    @Column(name = "customAnswer8")
    public String getCustomAnswer8() {
        return customAnswer8;
    }

    public void setCustomAnswer8(String customAnswer8) {
        this.customAnswer8 = customAnswer8;
    }
    @Column(name = "customAnswerName8")
    public String getCustomAnswerName8() {
        return customAnswerName8;
    }

    public void setCustomAnswerName8(String customAnswerName8) {
        this.customAnswerName8 = customAnswerName8;
    }
    @Column(name = "customAnswer9")
    public String getCustomAnswer9() {
        return customAnswer9;
    }

    public void setCustomAnswer9(String customAnswer9) {
        this.customAnswer9 = customAnswer9;
    }
    @Column(name = "customAnswerName9")
    public String getCustomAnswerName9() {
        return customAnswerName9;
    }

    public void setCustomAnswerName9(String customAnswerName9) {
        this.customAnswerName9 = customAnswerName9;
    }
    @Column(name = "customAnswer10")
    public String getCustomAnswer10() {
        return customAnswer10;
    }

    public void setCustomAnswer10(String customAnswer10) {
        this.customAnswer10 = customAnswer10;
    }
    @Column(name = "customAnswerName10")
    public String getCustomAnswerName10() {
        return customAnswerName10;
    }

    public void setCustomAnswerName10(String customAnswerName10) {
        this.customAnswerName10 = customAnswerName10;
    }
 
    @Override
    public int hashCode() {
        int hash = 3;
        hash = 29 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final HappyornotSurvey other = (HappyornotSurvey) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.happyornot.entity.HappyornotSurvey[ id=" + id + " ]";
    }

    @Transient
    public Map<String, String> getSizes() {
        if (this.sizesList == null) {
            return null;
        }
        Map<String, String> type = new HashMap<>(this.sizesList.size());
        this.sizesList.forEach((templateDataMap) -> {
            type.put(templateDataMap.getFieldName(), templateDataMap.getFieldValue());
        });
        return type;
    }

    public void setSizes(Map<String, String> type) {
        if (type == null) {
            return;
        }
        this.sizesList = new ArrayList<>(type.size());
        type.entrySet().forEach((dynamicField) -> {
            this.sizesList.add(new TemplateDataMap(0, dynamicField.getKey(), dynamicField.getValue()));
        });
    }
}
