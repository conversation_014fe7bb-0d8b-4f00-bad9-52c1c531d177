/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.dao;

import DPMS.DAOInterface.ICodeSequenceDAO;
import Framework.Config.Language;
import Framework.Config.Mail;
import Framework.Config.StandardEntity;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableMap;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.cipher.Encryptor;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.framework.core.Mailer;
import qms.framework.mail.MailDTO;
import qms.framework.util.SettingsUtil;
import qms.happyornot.dto.HappyornotAnswerDto;
import qms.happyornot.dto.HappyornotSurveySavedDto;
import qms.happyornot.entity.HappyornotLink;
import qms.happyornot.entity.HappyornotSurvey;
import qms.happyornot.entity.HappyornotSurveyAnswer;
import qms.happyornot.entity.HappyornotTarget;
import qms.happyornot.entity.HappyornotUpdateLink;
import qms.happyornot.entity.HappyornotUserAgent;
import qms.timesheet.dao.TimesheetDAO;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@Repository(value = "HappyornotDAO")
@Scope(value = "singleton")
@Language(module = "qms.happyornot.dao.HappyornotDAO")
public class HappyornotDAO extends GenericDAOImpl<HappyornotSurvey, Long> implements IHappyornotDAO {

    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public HappyornotSurveySavedDto save(HappyornotSurvey survey, Boolean publish, final ILoggedUser loggedUser) throws QMSException {
        
        final Boolean isNew = survey.getId() == null || survey.getId().equals(-1L);
        if (isNew) {
            survey.setCode(
                    this.getNextCode()
            );
        } else {
            survey = this.getImmutableData(survey);
            if (survey.getStatus() != null && survey.getStatus() == 1) {
                publish = true;
            }
        }
        if (survey.getStatus() == null) {
            if (publish) {
                survey.setStatus(StandardEntity.STATUS.ACTIVE.getValue());
            } else {
                survey.setStatus(StandardEntity.STATUS.INACTIVE.getValue());
            }
        } else {
            if (publish) {
                survey.setStatus(StandardEntity.STATUS.ACTIVE.getValue());
            } else {
                survey.setStatus(StandardEntity.STATUS.INACTIVE.getValue());
            }
        }
        survey = makePersistent(generateSurveyLink(survey, loggedUser), loggedUser.getId());
        if (survey == null || survey.getTargetNames() == null) {
            throw new RuntimeException("Survey wasn't saved, please try again or contact the system administrator");
        }
        HappyornotSurveySavedDto result = new HappyornotSurveySavedDto(survey.getId());
        result.setTargetNames(
                survey.getTargetNames().stream().map(
                        (targetName) -> new TextLongValue(targetName.getDescription(), targetName.getId())
                ).collect(Collectors.toList())
        );
        result.setTargetMails(
                survey.getTargetMails().stream().map(
                        (targetName) -> new TextLongValue(targetName.getDescription(), targetName.getId())
                ).collect(Collectors.toList())
        );
        result.setAllowAnonymousAnswer(survey.getAllowAnonymousAnswer());
        return result;
    }

    private HappyornotSurvey getImmutableData(final HappyornotSurvey survey) {
        
        final HappyornotSurvey original = HQLT_findById(survey.getId());
        
        //La descripción es vacia cuando es edición
        if (!"".equals(survey.getDescription())) {
            original.setLastModifiedDate(new Date());
            original.setHeader(survey.getHeader());
            original.setSizesList(survey.getSizesList());
            original.setHeaderPlain(survey.getHeaderPlain());
            original.setAspectHeight(survey.getAspectHeight());
            original.setAspectWidth(survey.getAspectWidth());
            original.setDescription(survey.getDescription());
            original.setFacesAlignment(survey.getFacesAlignment());
            original.setFacesDesign(survey.getFacesDesign());
            original.setFacesOrientation(survey.getFacesOrientation());
            original.setMessagePosition(survey.getMessagePosition());
            original.setWaitingTime(survey.getWaitingTime());
            original.setFooter(survey.getFooter());
            original.setFooterPlain(survey.getFooterPlain());
            original.setHtml(survey.getHtml());
            original.setLogoFileId(survey.getLogoFileId());
            original.setPublishTarget(survey.getPublishTarget());
            original.setQuestionColor(survey.getQuestionColor());
            original.setScreenBackground(survey.getScreenBackground());
            original.setTimeNegativeReport(survey.getTimeNegativeReport());
            original.setFeedbackTitle(survey.getFeedbackTitle());
            original.setNegativeReport(survey.getNegativeReport());
            original.setAnswerPerDay(survey.getAnswerPerDay());
            original.setAlertNegativeAnswer(survey.getAlertNegativeAnswer());
            original.setFontStyle(survey.getFontStyle());
            original.setFontSize(survey.getFontSize());
            original.setAllowFeedback(survey.getAllowFeedback());
            original.setAllowAnonymousAnswer(survey.getAllowAnonymousAnswer());
            original.setAllowMultipleUserAnswer(survey.getAllowMultipleUserAnswer());
            
            original.setHasCustomsAnswers(survey.getHasCustomsAnswers());
            original.setCustomAnswer1(survey.getCustomAnswer1());
            original.setCustomAnswerName1(survey.getCustomAnswerName1());
            original.setCustomAnswer2(survey.getCustomAnswer2());
            original.setCustomAnswerName2(survey.getCustomAnswerName2());
            original.setCustomAnswer3(survey.getCustomAnswer3());
            original.setCustomAnswerName3(survey.getCustomAnswerName3());
            original.setCustomAnswer4(survey.getCustomAnswer4());
            original.setCustomAnswerName4(survey.getCustomAnswerName4());
            original.setCustomAnswer5(survey.getCustomAnswer5());
            original.setCustomAnswerName5(survey.getCustomAnswerName5());
            original.setCustomAnswer6(survey.getCustomAnswer6());
            original.setCustomAnswerName6(survey.getCustomAnswerName6());
            original.setCustomAnswer7(survey.getCustomAnswer7());
            original.setCustomAnswerName7(survey.getCustomAnswerName7());
            original.setCustomAnswer8(survey.getCustomAnswer8());
            original.setCustomAnswerName8(survey.getCustomAnswerName8());
            original.setCustomAnswer9(survey.getCustomAnswer9());
            original.setCustomAnswerName9(survey.getCustomAnswerName9());
            original.setCustomAnswer10(survey.getCustomAnswer10());
            original.setCustomAnswerName10(survey.getCustomAnswerName10());
        }
        original.setTargetNames(survey.getTargetNames());
        original.setTargetMails(survey.getTargetMails());
        return original;
    }

    private String getNextCode() throws QMSException {
        final String code = "HSURVEY-" + Utilities.todayDateBy("yyyyMMdd");
        final ICodeSequenceDAO dao = getBean(ICodeSequenceDAO.class);
        return (code + "-" + dao.next(
                TimesheetDAO.class.getSimpleName() + "." + code
        )).toUpperCase();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle answer(
            final HappyornotAnswerDto dto, 
            final HappyornotUserAgent userAgent,
            final Long loggedUserId
    ) throws QMSException {
        final GenericSaveHandle handle = new GenericSaveHandle();
        final HappyornotSurveyAnswer answer = new HappyornotSurveyAnswer(dto);
        Long happyornotTargetId = dto.getHappyornotTargetId();
        Map<String, Object> target = HQL_findSimpleMap(" "
                        + " SELECT new map("
                            + " s.allowAnonymousAnswer AS allowAnonymousAnswer"
                            + ", s.allowMultipleUserAnswer AS allowMultipleUserAnswer"
                        + " )"
                        + " FROM " + HappyornotTarget.class.getCanonicalName() + " a"
                        + " JOIN a.happyornotSurvey s"
                        + " WHERE a.id = :id",
                "id", happyornotTargetId
        );
        boolean answerAnonymous = isAnswerAnonymous(loggedUserId);
        Boolean allowAnonymousAnswer = (Boolean) target.get("allowAnonymousAnswer");
        if (!allowAnonymousAnswer) {
            if (answerAnonymous) {
                handle.setErrorMessage("sessionRequired");
                handle.setStatus("bad");
                return handle;
            }
            Boolean allowMultipleUserAnswer = (Boolean) target.get("allowMultipleUserAnswer");
            if (Boolean.FALSE.equals(allowMultipleUserAnswer)
                    && surveyWasAnswered(happyornotTargetId, loggedUserId)) {
                handle.setErrorMessage("answeredPerUser");
                handle.setStatus("bad");
                return handle;
            }
        }
        if (!answerAnonymous) {
            if (Boolean.TRUE.equals(dto.getAnswerPerDay())
                    && surveyWasAnswered(happyornotTargetId, loggedUserId)
                    && answer.getAnswerFromMobile()) {
                handle.setErrorMessage("answered");
                handle.setStatus("bad");
                return handle;
            }
        }
        String consecutivo = Utilities.todayDateBy("yyyyMMdd") + "-" + answer.getHappyornotTargetId();
        final ICodeSequenceDAO daoSeq = getBean(ICodeSequenceDAO.class);
        answer.setFolio(consecutivo + "-" + daoSeq.next(consecutivo).toUpperCase());
        final HappyornotSurveyAnswer savedAnswer = makePersistent(
                answer, loggedUserId
        );
        makePersistent(userAgent, loggedUserId);
        if (savedAnswer != null && savedAnswer.getAnswer() == 1) {
            this.sendMails(savedAnswer);
        }

        if (savedAnswer == null) {
            handle.setStatus("bad");
        } else {
            handle.setStatus("ok");
        }
        return handle;
    }

    private void sendMails(final HappyornotSurveyAnswer answer) {
        
        final Date creationDate = answer.getCreatedDate();
        final Long happyornotTargetId = answer.getHappyornotTargetId();
        final List<Mail> mails = HQL_findByQuery(""
                + " SELECT new " + Mail.class.getCanonicalName() + "("
                    + " m.mail"
                    + ",m.mail"
                + " )"
                + " FROM " + HappyornotTarget.class.getCanonicalName() + " t "
                + " JOIN t.happyornotSurvey c "
                + " JOIN c.targetMails m "
                + " WHERE t.id = :happyornotTargetId" 
                + " AND c.status = 1 "
                + " AND c.deleted = 0"
                + " AND t.deleted = 0"
                + " AND m.deleted = 0"
                + " AND c.alertNegativeAnswer = 1",
                "happyornotTargetId", happyornotTargetId
        );
        final Map<String, Object> params = new HashMap<>(2);
        params.put("happyornotTargetId", happyornotTargetId);
        params.put("answerId", answer.getId());
        final Map<String, Object> target = HQL_findSimpleMap(""
                + " SELECT new map("
                    + " t.description AS targetName"
                    + ",c.description AS question"
                    + ", c.allowFeedback AS allowFeedback"
                    + ", a.feedback AS feedback"
                    + ", a.feedbackEmail AS feedbackEmail"
                    + ",a.folio AS folio"
                + " )"
                + " FROM " + HappyornotTarget.class.getCanonicalName() + " t "
                + " JOIN t.happyornotSurvey c "
                + " JOIN t.answers a"
                + " WHERE "
                + " t.id = :happyornotTargetId"
                + " AND c.status = 1 "
                + " AND c.deleted = 0"
                + " AND t.deleted = 0"
                + " AND a.id = :answerId", params);

        final MailDTO dto = new MailDTO();
        dto.setRecipients(new HashSet<>(mails));
        dto.setSubject(target.get("folio") + ": " + getTag("answerNegativeIn") + target.get("targetName"));
        Boolean allowFeedback = Objects.equals(target.get("allowFeedback"), true);
        dto.setMessageTitle(""
                + getTag("answernegativeMessage") + " " + target.get("targetName") + " "
                + getTag("answerNegativeDateMessage") + " " + Utilities.formatDateWithTime(creationDate) + " "
                + getTag("answernegativeTargetMessage") + " " + target.get("question")
        );
        dto.setMessage(
                    allowFeedback ? ""
                    + "<strong>" + getTag("feedback") + "</strong>"  + ": " + target.get("feedback") + "<br>"
                    + "<strong>" + getTag("feedbackEmail") + "</strong>" + ": " + target.get("feedbackEmail")
                    : ""
        );
        Mailer.send(dto, Module.POLL, Mailer.TYPE.INFO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean surveyWasAnswered(Long happyornotTargetId, Long userId) {

        final Map<String, Object> params = new HashMap<>(2);
        params.put("happyornotTargetId", happyornotTargetId);
        params.put("userId", userId);
        
        final List<HappyornotSurveyAnswer> hasAnswers = HQL_findByQuery(""
                + " SELECT new map( "
                    + " CAST(a.createdDate AS Date) "
                + " ) "
                + " FROM " + HappyornotSurveyAnswer.class.getCanonicalName() + " a "
                + " WHERE a.happyornotTargetId = :happyornotTargetId"
                + " AND a.createdBy = :userId", 
                params
        );
        final Boolean response;
        //Se agrega la funcione de trun para validar unicamente las fechas sin las horas
        if (!hasAnswers.isEmpty()) {
            final List<HappyornotSurveyAnswer> answers = HQL_findByQuery(""
                    + "SELECT new map( "
                        + " CAST(a.createdDate AS Date) "
                    + " )"
                    + " FROM " + HappyornotSurveyAnswer.class.getCanonicalName() + " a "
                    + " WHERE a.happyornotTargetId = :happyornotTargetId"
                        + " AND a.createdBy = :userId"
                        + " AND CAST(current_date AS Date) = cast(a.createdDate as Date)",
                    params
            );
            response = !answers.isEmpty();
        } else {
            response = false;
        }

        return response;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void sendScheduledReport() {
        var now = LocalDate.now();
        var yesterday = now.minusDays(1);
        var todayStr = now.format(DateTimeFormatter.ISO_LOCAL_DATE);
        var yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

        final List<Mail> mails = HQL_findByQuery(""
                + " SELECT new " + Mail.class.getCanonicalName() + "("
                        + " m.mail,"
                        + " m.mail "
                + ") "
                + " FROM " + HappyornotSurvey.class.getCanonicalName() + " s "
                + " JOIN s.targetMails m "
                + " JOIN s.targetNames t "
                + " JOIN t.answers a "
                + " WHERE CAST(s.negativeReport AS int) = 1 AND s.status = 1 "
                    + " AND a.createdDate BETWEEN"
                    + " CAST(CONCAT('" + yesterdayStr + " ', s.timeNegativeReport, ':00') AS Timestamp ) "
                    + " AND CAST(CONCAT('" + todayStr + " ', s.timeNegativeReport, ':00') AS Timestamp ) "
                + "GROUP BY m.mail");
        if (mails == null) {
            return ;
        }

        mails.forEach(mail -> {
            final List<Mail> email = new ArrayList<>();
            email.add(mail);
            List<Map<String, Object>> surveyAnswers = HQL_findByQuery(""
                    + " SELECT new map ("
                        + " s.code AS folio, "
                        + " s.allowFeedback AS allowFeedback, "
                        + " string_agg(a.feedback,',') AS feedback, "
                        + " string_agg(a.feedbackEmail,',') AS feedbackEmail, "
                        + " convertDateISO8601(MIN(a.createdDate)) + ' - ' + convertDateISO8601(MAX(a.createdDate)) AS answerDate, "
                        + " s.description AS description,"
                        + " count(a.answer) AS answers"
                    + " )"
                    + " FROM " + HappyornotSurvey.class.getCanonicalName() + " s"
                    + " JOIN s.targetMails m "
                    + " JOIN s.targetNames t "
                    + " JOIN t.answers a "
                    + " WHERE a.answer = 1 "
                        + " AND m.mail = :mail "
                        + " AND s.negativeReport = 1 "
                        + " AND s.status = 1 "
                        + " AND CAST(a.createdDate AS Date) "
                        + " BETWEEN truncToDateTime(CURRENT_DATE-1) + s.timeNegativeReport "
                        + " AND truncToDateTime(CURRENT_DATE) + s.timeNegativeReport "
                    + " GROUP BY a.answer, s.description, s.code, s.allowFeedback",
                    ImmutableMap.of("mail", mail.getEmail()));
            if (surveyAnswers != null && !surveyAnswers.isEmpty()) {
                MailDTO dto = new MailDTO();
                dto.setRecipients(new HashSet<>(email));
                dto.setSubject(getTag("surveyTotalNegativeResult"));
                String body = getTag("surveyDiaryResult") + "\n"
                        + "<table cellspacing='0' style='width:100%;'>"
                        + "<thead>"
                        + "<th style='border:solid 1px black;'>" + getTag("folio") + "</th>"
                        + "<th style='border:solid 1px black;'>" + getTag("question") + "</th>"
                        + "<th style='border:solid 1px black;'>" + getTag("answers") + "</th>"
                        + "<th style='border:solid 1px black;'>" + getTag("answerDate") + "</th>"
                        + "<th style='border:solid 1px black;'>" + getTag("feedback") + "</th>"
                        + "<th style='border:solid 1px black;'>" + getTag("feedbackEmail") + "</th>"
                        + "</thead>"
                        + "<tbody>";
                for (Map survey : surveyAnswers) {
                    body += buildBody(survey);
                }
                body += "</body></table>";
                dto.setMessageTitle(body);
                Mailer.send(dto, Module.POLL, Mailer.TYPE.INFO);
            }
        });
    }

    private String buildBody(Map surveyData) {
        Boolean allowFeedback = Objects.equals(surveyData.get("allowFeedback"), true);
        return "<tr>"
                + "<td style='border:solid 1px black;'>" + surveyData.get("folio") + "</td> "
                + "<td style='border:solid 1px black;'>" + surveyData.get("description") + "</td> "
                + "<td style='border:solid 1px black;'>" + surveyData.get("answers") + "</td>"
                + "<td style='border:solid 1px black;'>" + surveyData.get("answerDate") + "</td>"
                + "<td style='border:solid 1px black;'>" + (allowFeedback ? surveyData.get("feedback"): "") + "</td>"
                + "<td style='border:solid 1px black;'>" + (allowFeedback ? surveyData.get("feedbackEmail"): "") + "</td>"
                + "</tr>";
    }

    private boolean isAnswerAnonymous(Long loggedUserId){
        return loggedUserId == null;
    }

    private HappyornotSurvey generateSurveyLink(final HappyornotSurvey survey, final ILoggedUser loggedUser) {
        if (survey.getTargetNames() != null && !survey.getTargetNames().isEmpty()) {
            String appUrl = SettingsUtil.getAppUrlNoSlash();
            String surveyUrl = appUrl + "/qms/" + Utilities.getSettings().getLang() + "/menu/legacy/" + SURVEY_URL + "&nh=1&nm=1&nd=1";
            survey.getTargetNames().forEach(item -> {
                if (item.getHappyornotLinks() != null && !item.getHappyornotLinks().isEmpty()) {
                    item.getHappyornotLinks().forEach(link -> {
                        link.setTargetSurvey(item);
                        link.setCode(UUID.randomUUID().toString());
                        
                        final String token = Encryptor.encrypt(link.getCode(), Utilities.getSettings().getSystemId());
                        
                        link.setUrlName("survey-" + link.getCode());
                        link.setToken(token);
                        if (Boolean.TRUE.equals(survey.getAllowAnonymousAnswer())) {
                            link.setUrl(appUrl + URL_BASE_TOKEN + link.getToken());
                        } else {
                            link.setUrl(surveyUrl + link.getToken());
                        }
                        link.setCreatedBy(loggedUser.getId());
                        link.setLastModifiedBy(loggedUser.getId());
                        link.setCreatedDate(new Date());
                        link.setLastModifiedDate(new Date());
                    });
                }
            });
        }
        return survey;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Map getLinkData(final Long happyornotTargetId) {
        
        return HQL_findSimpleMap(""
                + " SELECT new map ("
                    + " l.id AS id,"
                    + " l.status AS status,"
                    + " l.token AS token,"
                    + " l.url AS url,"
                    + " l.viewCounter AS viewCounter,"
                    + " l.deleted AS deleted,"
                    + " l.urlName AS urlName,"
                    + " l.code AS code, "
                    + " l.viewsPerLink AS viewsPerLink,"
                    + " l.expireDate AS expireDate,"
                    + " l.expireTime AS expireTime"
                + " )"
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l "
                + " JOIN l.targetSurvey t "
                + " WHERE t.id = :id",
            "id", happyornotTargetId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateSurveyLink(final HappyornotLink link, final ILoggedUser loggedUser) {
                
        final Map<String, Object> params = new HashMap<>(8);
        
        params.put("link", link.getUrl());
        params.put("urlName", link.getUrlName());
        params.put("expireDate", link.getExpireDate());
        params.put("viewsPerLink", link.getViewsPerLink());
        params.put("expireTime", link.getExpireTime());
        params.put("loggedUserId", loggedUser.getId());
        params.put("id", link.getId());
        params.put("lastModifiedDate", link.getLastModifiedDate());
        
        return HQL_updateByQuery(""
                + " UPDATE " + HappyornotLink.class.getCanonicalName() + " l"
                + " SET l.url = :link, "
                    + " l.urlName = :urlName, "
                    + " l.viewsPerLink = :viewsPerLink,"
                    + " l.expireDate = :expireDate,"
                    + " l.expireTime = :expireTime,"
                    + " l.lastModifiedBy = :loggedUserId,"
                    + " l.lastModifiedDate = :lastModifiedDate"
                + " WHERE l.id = :id", params);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GridInfo<Map<String,Object>> getAllLinks(final GridFilter filter) {
        
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("l.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        return HQL_getRows(""
                + " SELECT new map("
                    + " l.id AS id,"
                    + " l.status AS status,"
                    + " l.token AS token,"
                    + " l.url AS url,"
                    + " l.viewCounter AS viewCounter,"
                    + " l.urlName AS urlName,"
                    + " l.code AS code,"
                    + " s.code AS surveyCode,"
                    + " s.description AS surveyDescription,"
                    + " l.viewsPerLink AS viewsPerLink,"
                    + " l.expireDate AS expireDate,"
                    + " l.expireTime AS expireTime,"
                    + " t.id AS targetId,"
                    + " t.description AS description"
                + " ) "
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l"
                + " JOIN l.targetSurvey t"
                + " JOIN t.happyornotSurvey s"
                + " WHERE l.deleted = 0", filter);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Integer changeUrlTokenSurveyDestination(final Long surveyId, final Long targetId) {
        Integer result = HQL_updateByQuery(""
                + "UPDATE " + HappyornotTarget.class.getCanonicalName() + " t "
                        + "SET t.happyornotSurvey.id = :surveyId "
                        + " WHERE t.id = :targetId", 
                ImmutableMap.of("surveyId", surveyId, "targetId", targetId));
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateCounterView(final String code) {
        
        final Long currentViews = HQL_findSimpleLong(""
                + " SELECT l.viewCounter"
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l"
                + " WHERE l.code = :code ",
                "code", code
        ) + 1;
        
        final Map<String, Object> params = new HashMap<>(2);
        
        params.put("code", code);
        params.put("lastModifiedDate", new Date());
        params.put("currentViews", currentViews);
        
        return HQL_updateByQuery(""
                + " UPDATE " + HappyornotLink.class.getCanonicalName() + " l "
                + " SET"
                        + " l.viewCounter = :currentViews,"
                        + " l.lastModifiedDate = :lastModifiedDate"
                + " WHERE l.code = :code ", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getExpiredDateToke(final String code) throws ParseException {
        
        final Map<String, Object> params = new HashMap<>(1);
        
        params.put("code", code);
        
        final HappyornotLink link = (HappyornotLink) HQL_findSimpleObject(""
                + " SELECT new " + HappyornotLink.class.getCanonicalName() + " ("
                    + "l.expireDate AS expireDate, "
                    + "l.expireTime AS expireTime"
                + ")"
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l"
                + " WHERE l.code = :code ", params);
        
        final Date date = link.getExpireDate();
        final Date time = link.getExpireTime();

        if (date != null && time != null) {
            date.setHours(time.getHours());
            date.setMinutes(time.getMinutes());
            date.setSeconds(time.getSeconds());
        }
        return date;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean checkViewsPerLink(final String code) {
        
        final Map<String, Object> params = new HashMap<>(1);
        params.put("code", code);
        
        final Map<String, Object> link = HQL_findSimpleMap(""
                + " SELECT new map ("
                    + "l.viewCounter AS viewCounter, "
                    + "l.viewsPerLink AS viewsPerLink "
                + " ) "
                + " FROM " + HappyornotLink.class.getCanonicalName() + " l"
                + " WHERE l.code = :code ", params);
        final Long currentViews = link.get("viewCounter") != null ? Long.parseLong(link.get("viewCounter").toString()) : null;
        final Long maxViews = link.get("viewsPerLink") != null ? Long.parseLong(link.get("viewsPerLink").toString()) : null;

        if (maxViews != null && maxViews > 0) {
            return currentViews < maxViews;
        }
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<HappyornotUserAgent> getUsersAgentsByLink(GridFilter filter, Long linkId) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("ua.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        final GridInfo<HappyornotUserAgent> userAgents = HQL_getRows(""
                + " SELECT"
                    + "  new " + HappyornotUserAgent.class.getCanonicalName() + " ("
                        + " ua.device AS device,"
                        + " ua.browser AS browser,"
                        + " ua.os AS os,"
                        + " ua.version AS version,"
                        + " ua.ip AS ip"
                + " )"
                + " FROM " + HappyornotUserAgent.class.getCanonicalName() + " ua "
                + " WHERE ua.happyornotLinkId = " + linkId, 
                filter
        );
        
        return userAgents;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> updateLinkStatus(Long linkId, Integer status, final ILoggedUser loggedUser) {
        final Map<String, Object> params = new HashMap<>(4);
        
        params.put("lastModifiedDate", new Date());
        params.put("status", status);
        params.put("loggedUserId", loggedUser.getId());
        params.put("id", linkId);
        
        final Integer response = HQL_updateByQuery(""
                + " UPDATE "  + HappyornotUpdateLink.class.getCanonicalName() + " l"
                + " SET l.status = :status,"
                + " l.lastModifiedBy = :loggedUserId,"
                + " l.lastModifiedDate = :lastModifiedDate"
                    + " WHERE l.id = :id",
                params
        );
        
        return response != null && response == 1 
                ? new ResponseEntity(true, HttpStatus.OK) 
                : new ResponseEntity(false, HttpStatus.CONFLICT);
    }

}
