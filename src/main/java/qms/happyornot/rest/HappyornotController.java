/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.rest;

import DPMS.Mapping.User;
import Framework.Config.Language;
import Framework.Config.TextHasValue;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import bnext.exception.MakePersistentException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ExecutionError;
import com.google.gson.Gson;
import com.sun.star.auth.InvalidArgumentException;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.SettingsUtil;
import qms.happyornot.dao.IHappyornotDAO;
import qms.happyornot.dto.ResizeImageDto;
import qms.happyornot.entity.HappyornotFonts;
import qms.happyornot.entity.HappyornotLink;
import qms.happyornot.entity.HappyornotSurvey;
import qms.happyornot.entity.HappyornotSurveyAnswer;
import qms.happyornot.entity.HappyornotTarget;
import qms.happyornot.entity.HappyornotUserAgent;
import qms.template.entity.TemplateDataMap;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@RestController
@RequestMapping("happyornot")
@Language(module = "qms.happyornot.dao.HappyornotDAO")
public class HappyornotController {
    
    public enum listType {
        PAPERBIN, CONTROL;
    }
    
    private final String HAS_CREATE_ACCESS = "hasAnyAuthority('IS_ADMIN', 'HAPPYORNOT_SURVEY_CREATOR')";
    private final String HAS_DESIGN_ACCESS = "hasAnyAuthority('IS_ADMIN', 'HAPPYORNOT_SURVEY_CREATOR', 'HAPPYORNOT_SURVEY_MODIFIER')";
    private final String HAS_QUERY_ACCESS = "hasAnyAuthority('IS_ADMIN', 'HAPPYORNOT_SURVEY_CREATOR', 'HAPPYORNOT_SURVEY_MODIFIER', 'HAPPYORNOT_SURVEY_VIEW', 'HAPPYORNOT_SURVEY_VIEW_ALL', 'HAPPYORNOT_SURVEY_RESTORE')";

    @Autowired
    @Qualifier("HappyornotDAO")
    protected IHappyornotDAO dao;
    
    @PostMapping()
    @RequestMapping({"save"})
    @PreAuthorize(HAS_CREATE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity save(
        @RequestBody HappyornotSurvey survey
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return ResponseEntity.ok(
            dao.save(survey, false, SecurityUtils.getLoggedUser())
        );
    }

    @PostMapping()
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @RequestMapping("load/{happyornotSurveyId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map load(
        @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId
    ) {
        // ToDo: Validar permisos
        Map result = dao.HQL_findSimpleMap(""
            + " SELECT new map("
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description "
                + ",c.status AS status "
                + ",c.deleted AS deleted "
                + ",c.createdDate AS createdDate "
                + ",c.lastModifiedDate AS lastModifiedDate "
                + ",c.createdBy AS createdBy "
                + ",c.lastModifiedBy AS lastModifiedBy "
                + ",c.logoFileId AS logoFileId "
                + ",c.aspectWidth AS aspectWidth "
                + ",c.aspectHeight AS aspectHeight "
                + ",c.html AS html "
                + ",c.header AS header "
                + ",c.headerPlain AS headerPlain "
                + ",c.footer AS footer "
                + ",c.footerPlain AS footerPlain "
                + ",c.screenBackground AS screenBackground "
                + ",c.questionColor AS questionColor "
                + ",c.facesAlignment AS facesAlignment "
                + ",c.facesOrientation AS facesOrientation "
                + ",c.facesDesign AS facesDesign "
                + ",c.messagePosition AS messagePosition "
                + ",c.waitingTime AS waitingTime "
                + ",c.publishTarget AS publishTarget "
                + ",c.negativeReport AS negativeReport "
                + ",c.timeNegativeReport AS timeNegativeReport"
                + ",c.answerPerDay AS answerPerDay"
                + ",c.alertNegativeAnswer AS alertNegativeAnswer"
                + ",c.fontStyle AS fontStyle"
                + ",c.feedbackTitle AS feedbackTitle"
                + ",c.fontSize AS fontSize"
                + ",c.allowFeedback AS allowFeedback"
                + ",c.hasCustomsAnswers AS hasCustomsAnswers "
                + ",c.customAnswer1 AS customAnswer1"
                + ",c.customAnswerName1 AS customAnswerName1"
                + ",c.customAnswer2 AS customAnswer2"
                + ",c.customAnswerName2 AS customAnswerName2"
                + ",c.customAnswer3 AS customAnswer3"
                + ",c.customAnswerName3 AS customAnswerName3"
                + ",c.customAnswer4 AS customAnswer4"
                + ",c.customAnswerName4 AS customAnswerName4"
                + ",c.customAnswer5 AS customAnswer5"
                + ",c.customAnswerName5 AS customAnswerName5"
                + ",c.customAnswer6 AS customAnswer6"
                + ",c.customAnswerName6 AS customAnswerName6"
                + ",c.customAnswer7 AS customAnswer7"
                + ",c.customAnswerName7 AS customAnswerName7"
                + ",c.customAnswer8 AS customAnswer8"
                + ",c.customAnswerName8 AS customAnswerName8"
                + ",c.customAnswer9 AS customAnswer9"
                + ",c.customAnswerName9 AS customAnswerName9"
                + ",c.customAnswer10 AS customAnswer10"
                + ",c.customAnswerName10 AS customAnswerName10"
                
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " WHERE c.id = " + happyornotSurveyId
        );
        List<TemplateDataMap> sizes = dao.HQLT_findByQuery(TemplateDataMap.class, ""
            + " SELECT s "
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " JOIN c.sizesList s "
            + " WHERE c.id = " + happyornotSurveyId
        );      
        Map<String, String> size = new HashMap<>(sizes.size());
        sizes.forEach((templateDataMap) -> {
            size.put(templateDataMap.getFieldName(), templateDataMap.getFieldValue());
        });
        result.put("sizes", size);
        return result;
    }

    @PostMapping()
    @RequestMapping({"save-publish"})
    @PreAuthorize(HAS_CREATE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity savePublish(
        @RequestBody HappyornotSurvey survey
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return ResponseEntity.ok(
            dao.save(survey, true, SecurityUtils.getLoggedUser())
        );
    }

    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("paperbin")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo allDeletedRows(@RequestBody GridFilter filter) {
            return this._allRows(filter, listType.PAPERBIN);
    }

    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("list")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo allRows(@RequestBody GridFilter filter) {
            return this._allRows(filter, listType.CONTROL);
    }
    
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("templates/{happyornotSurveyId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo allRows(
            @RequestBody GridFilter filter,
            @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId
    ) {
        if (happyornotSurveyId != -1) {
            filter.getCriteria().put("<condition>", ""
                + "entity.id != " + happyornotSurveyId
            );
        }
        return this._allRows(filter, listType.CONTROL);
    }
    
    private GridInfo _allRows(GridFilter filter, listType list) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
            filter.setDirection(Byte.parseByte("2"));
        }        
        String where = "";
        switch (list) { 
            case CONTROL:
                if(SecurityUtils.isLoggedUserAdmin() || canModifyAnySurvey())                  
                    where = " WHERE entity.deleted = 0";
                else 
                    where = " WHERE entity.deleted = 0"
                        + " AND entity.createdBy = " + SecurityUtils.getLoggedUserId();
                break;
            case PAPERBIN:
                where = " WHERE entity.deleted = 1";
                break;
        }
        return dao.HQL_getRows(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.code AS entity_code "
                + ",entity.status AS entity_status "
                + ",entity.description AS entity_description "
                + ",entity.aspectWidth AS entity_aspectWidth "
                + ",entity.aspectHeight AS entity_aspectHeight "
                + ",entity.facesDesign AS entity_facesDesign "
                + ",entity.publishTarget AS entity_publishTarget "
                + ",entity.messagePosition AS entity_messagePosition "
                + ",entity.createdDate AS createdDate "
                + ",entity.lastModifiedDate AS lastModifiedDate "
                + ",SUM(CASE WHEN a.answer = 1 THEN 1 ELSE 0 END) AS entity_answer1 "
                + ",SUM(CASE WHEN a.answer = 2 THEN 1 ELSE 0 END) AS entity_answer2 "
                + ",SUM(CASE WHEN a.answer = 3 THEN 1 ELSE 0 END) AS entity_answer3 "
                + ",SUM(CASE WHEN a.answer = 4 THEN 1 ELSE 0 END) AS entity_answer4 "
                + ",SUM(CASE WHEN a.answer = 5 THEN 1 ELSE 0 END) AS entity_answer5 "
                + ",SUM(CASE WHEN a.answer = 6 THEN 1 ELSE 0 END) AS entity_answer6 "
                + ",SUM(CASE WHEN a.answer = 7 THEN 1 ELSE 0 END) AS entity_answer7 "
                + ",SUM(CASE WHEN a.answer = 8 THEN 1 ELSE 0 END) AS entity_answer8 "
                + ",SUM(CASE WHEN a.answer = 9 THEN 1 ELSE 0 END) AS entity_answer9 "
                + ",SUM(CASE WHEN a.answer = 10 THEN 1 ELSE 0 END) AS entity_answer10"
                + ",entity.hasCustomsAnswers AS hasCustomsAnswers "
                + ",entity.customAnswer1 AS customAnswer1"
                + ",entity.customAnswerName1 AS customAnswerName1"
                + ",entity.customAnswer2 AS customAnswer2"
                + ",entity.customAnswerName2 AS customAnswerName2"
                + ",entity.customAnswer3 AS customAnswer3"
                + ",entity.customAnswerName3 AS customAnswerName3"
                + ",entity.customAnswer4 AS customAnswer4"
                + ",entity.customAnswerName4 AS customAnswerName4"
                + ",entity.customAnswer5 AS customAnswer5"
                + ",entity.customAnswerName5 AS customAnswerName5"
                + ",entity.customAnswer6 AS customAnswer6"
                + ",entity.customAnswerName6 AS customAnswerName6"
                + ",entity.customAnswer7 AS customAnswer7"
                + ",entity.customAnswerName7 AS customAnswerName7"
                + ",entity.customAnswer8 AS customAnswer8"
                + ",entity.customAnswerName8 AS customAnswerName8"
                + ",entity.customAnswer9 AS customAnswer9"
                + ",entity.customAnswerName9 AS customAnswerName9"
                + ",entity.customAnswer10 AS customAnswer10"
                + ",entity.customAnswerName10 AS customAnswerName10"
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " entity "
            + " LEFT JOIN entity.targetNames t "
            + " LEFT JOIN t.answers a"
            + where
            + " GROUP BY "
                + " entity.id "
                + ",entity.code "
                + ",entity.status "
                + ",entity.description "
                + ",entity.aspectWidth "
                + ",entity.aspectHeight "
                + ",entity.facesDesign "
                + ",entity.publishTarget "
                + ",entity.messagePosition "
                + ",entity.createdDate "
                + ",entity.lastModifiedDate"
                + ",entity.hasCustomsAnswers"
                + ",entity.customAnswer1"
                + ",entity.customAnswerName1"
                + ",entity.customAnswer2"
                + ",entity.customAnswerName2"
                + ",entity.customAnswer3"
                + ",entity.customAnswerName3"
                + ",entity.customAnswer4"
                + ",entity.customAnswerName4"
                + ",entity.customAnswer5"
                + ",entity.customAnswerName5"
                + ",entity.customAnswer6"
                + ",entity.customAnswerName6"
                + ",entity.customAnswer7"
                + ",entity.customAnswerName7"
                + ",entity.customAnswer8"
                + ",entity.customAnswerName8"
                + ",entity.customAnswer9"
                + ",entity.customAnswerName9"
                + ",entity.customAnswer10"
                + ",entity.customAnswerName10"
        , filter);
    }
    
    @PostMapping()
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("answers")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo answers(@RequestBody GridFilter filter) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
            filter.setDirection(Byte.parseByte("2"));
        } 
        return dao.HQL_getRows(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.code AS entity_code "
                + ",entity.status AS entity_status "
                + ",entity.description AS entity_description "
                + ",t.description AS targetName "
                + ",entity.facesDesign AS entity_facesDesign "
                + ",entity.publishTarget AS entity_publishTarget "
                + ",a.answer AS answer "
                + ",a.answer AS answerId "
                + ",case a.answer "
                    + " when 1 then case entity.hasCustomsAnswers when 1 then customAnswerName1 else '" + Utilities.getTag("answer.1") + "' end "
                    + " when 2 then case entity.hasCustomsAnswers when 1 then customAnswerName2 else '" + Utilities.getTag("answer.2") + "' end "
                    + " when 3 then case entity.hasCustomsAnswers when 1 then customAnswerName3 else '" + Utilities.getTag("answer.3") + "' end "
                    + " when 4 then case entity.hasCustomsAnswers when 1 then customAnswerName4 else '" + Utilities.getTag("answer.4") + "' end "
                    + " when 5 then case entity.hasCustomsAnswers when 1 then customAnswerName5 else '" + Utilities.getTag("answer.5") + "' end "
                    + " when 6 then case entity.hasCustomsAnswers when 1 then customAnswerName6 else '' end "
                    + " when 7 then case entity.hasCustomsAnswers when 1 then customAnswerName7 else '' end "
                    + " when 8 then case entity.hasCustomsAnswers when 1 then customAnswerName8 else '' end "
                    + " when 9 then case entity.hasCustomsAnswers when 1 then customAnswerName9 else '' end "
                    + " when 10 then case entity.hasCustomsAnswers when 1 then customAnswerName10 else '' end "
                    + " else '' end AS answerName"
                + ",a.createdDate AS answerDate "
                + ",a.lastModifiedDate AS lastModifiedDate"
                + ",a.folio AS folio"
                + ",a.feedback AS feedback "
                + ",a.feedbackEmail AS feedbackEmail "
                + ",entity.hasCustomsAnswers AS hasCustomsAnswers"
                + ",entity.customAnswerName1 AS customAnswerName1"
                + ",entity.customAnswerName2 AS customAnswerName2"
                + ",entity.customAnswerName3 AS customAnswerName3"
                + ",entity.customAnswerName4 AS customAnswerName4"
                + ",entity.customAnswerName5 AS customAnswerName5"
                + ",entity.customAnswerName6 AS customAnswerName6"
                + ",entity.customAnswerName7 AS customAnswerName7"
                + ",entity.customAnswerName8 AS customAnswerName8"
                + ",entity.customAnswerName9 AS customAnswerName9"
                + ",entity.customAnswerName10 AS customAnswerName10"
                + ",created.id AS answeredUserId "
                + ",created.cuenta AS answeredUserAccount "
                + ",created.description AS answeredUserDescription "
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " entity "
            + " INNER JOIN entity.targetNames t "
            + " INNER JOIN t.answers a "
            + " LEFT JOIN " + User.class.getCanonicalName() + " created "
            + " ON created.id = a.createdBy "
            + " WHERE "
                + " entity.deleted = 0 "
                + " AND a.deleted = 0 "
                + " AND entity.status = 1 "
                + " AND a.answer is not null "
                + " AND a.answer > 0"
        , filter);
    }

    @PostMapping()
    @RequestMapping("deleteQuestion")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> deleteQuestion(@RequestBody Long questionId) {
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + HappyornotSurvey.class.getCanonicalName() + " q "
                + " SET q.deleted = 1 " 
                + " WHERE q.id = " + questionId
            ) == 1
        ) {
            return new ResponseEntity<>(true, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(false, HttpStatus.CONFLICT);
        }
    }
    
    @PostMapping()
    @RequestMapping("restore-question")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> restoreQuestion(@RequestBody Long questionId) {
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + HappyornotSurvey.class.getCanonicalName() + " q "
                + " SET q.deleted = 0 " 
                + " WHERE q.id = " + questionId
            ) == 1
        ) {
            return new ResponseEntity<>(true, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(false, HttpStatus.CONFLICT);
        }
    }
    
    @GetMapping
    @RequestMapping("toggle-status/{id}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    public ResponseEntity toogleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        Integer status = dao.HQL_findSimpleInteger(""
            + " SELECT t.status FROM " + HappyornotSurvey.class.getCanonicalName() + " t WHERE t.id = " + id
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + HappyornotSurvey.class.getCanonicalName() + " t "
                + " SET t.status = " + status
                + " WHERE t.id = " + id
            ) == 1
        ) {
            return new ResponseEntity<>(
                    ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @GetMapping
    @RequestMapping("toggle-target/{happyornotTargetId}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    public ResponseEntity toogleTargetStatus(
        @PathVariable(value = "happyornotTargetId", required = true) Long happyornotTargetId
    ) {
        Integer status = dao.HQL_findSimpleInteger(""
            + " SELECT t.status FROM " + HappyornotTarget.class.getCanonicalName() + " t WHERE t.id = " + happyornotTargetId
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + HappyornotTarget.class.getCanonicalName() + " t "
                + " SET t.status = " + status
                + " WHERE t.id = " + happyornotTargetId
            ) == 1
        ) {
            return new ResponseEntity<>(
                    ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("targets/{happyornotSurveyId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity targets(
        @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId
    ) {
        Map result = new HashMap(2);
        result.put("targetNames", dao.HQL_findByQuery(""
            + " SELECT new map("
                + " t.type AS publishTarget "
                + ",t.description AS targetName "
                + ",t.status AS status "
                + ",u.id AS userId "
                + ",t.id AS id "
                + ",t.deleted AS deleted"
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " JOIN c.targetNames t "
            + " LEFT JOIN t.user u "
            + " WHERE "
                + " c.deleted = 0"
                + " AND t.deleted = 0"
                + " AND c.id = " + happyornotSurveyId
        ));
        result.put("targetMails", dao.HQL_findByQuery(""
            + " SELECT new map("
                + " m.mail AS mail "
                + ",u.id AS userId "
                + ",m.id AS id "
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " JOIN c.targetMails m "
            + " LEFT JOIN m.user u "
            + " WHERE "
                + " c.deleted = 0"
                + " AND m.deleted = 0"
                + " AND c.id = " + happyornotSurveyId
        ));
        return new ResponseEntity<>(
                result, HttpStatus.OK
        );
    }

    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("dashboard/list")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextLongValue> dashboardList() {
        return dao.HQL_findByQuery(""
            + " SELECT new " + TextLongValue.class.getCanonicalName() + "("
                + " c.description "
                + ",c.id "
            + " )"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " WHERE "
                + " c.status = 1 "
                + " AND c.deleted = 0"
        );
    }
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("dashboard/{happyornotSurveyId}/{dateLimit}/{dateEnd}/{dateStart}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map dashboardPie(
        @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId,
        @PathVariable(value = "dateLimit") Integer limit,
        @PathVariable(value = "dateEnd") String dateEnd,
        @PathVariable(value = "dateStart") String dateStart
    ) {
        Map<String, Object> params = new HashMap<>(2);
        if(!"null".equals(dateEnd)){
            params.put("dateEnd", new Date(Long.parseLong(dateEnd)));
        }
        if(!"null".equals(dateStart)){
            params.put("dateStart", new Date(Long.parseLong(dateStart)));
        }
        return getDashboardPie(happyornotSurveyId, limit, params);
    }
    
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("dashboard/{happyornotSurveyId}/{dateLimit}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map dashboardPie(
        @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId,
        @PathVariable(value = "dateLimit") Integer limit
    ) {
        return getDashboardPie(happyornotSurveyId, limit, null);
    }

    private Map<String, Object> getDashboardPie(Long happyornotSurveyId, Integer limit, Map<String, Object> params) {
        if (limit == null) {
            limit = 10;
        }
        Map<String, Object> result = new LinkedHashMap<>(6);
        Map<String, Object> param = ImmutableMap.of("happyornotSurveyId", happyornotSurveyId );
        Map<String, Object> surveyUtilInfo = dao.HQL_findSimpleMap(""
            + " SELECT new map("
                + " c.facesDesign AS facesDesign,"
                + " c.hasCustomsAnswers AS hasCustomsAnswers "
            + ")"
            + " FROM " + HappyornotSurvey.class.getCanonicalName() + " c "
            + " WHERE c.id = :happyornotSurveyId", param
        );
        result.put("facesDesign", surveyUtilInfo.get("facesDesign") == null ? "PLAIN_HAPPY" : surveyUtilInfo.get("facesDesign").toString());
        result.put("hasCustomAnswers", surveyUtilInfo.get("hasCustomsAnswers") == null ? false : Boolean.parseBoolean(surveyUtilInfo.get("hasCustomsAnswers").toString()));
        result.put("pie", this.dashboardQuery(happyornotSurveyId, false, null, params));
        result.put("location", this.dashboardQuery(happyornotSurveyId, true, ImmutableList.of(
            TextHasValue.instance("t.description", "evaluation")
        ), params));
        result.put("hour", this.dashboardQuery(happyornotSurveyId, true, ImmutableList.of(
            TextHasValue.instance("datepartHour(a.createdDate)", "hour")
        ), params));
        result.put("weekday", this.dashboardQuery(happyornotSurveyId, true, ImmutableList.of(
            TextHasValue.instance("datepartWeekday(a.createdDate)", "weekday")
        ), params));
        result.put("date", this.dashboardQuery(happyornotSurveyId, true, ImmutableList.of(
            TextHasValue.instance("trunc(a.createdDate)", "date")
        ), limit, params));
        return result;
    }
    
    private List<Map<String, Object>> dashboardQuery(Long happyornotSurveyId, boolean peerAnswer, List<TextHasValue> columns, Map<String, Object> params) {
        return dashboardQuery(happyornotSurveyId, peerAnswer, columns, null, params);
    }

    private List<Map<String, Object>> dashboardQuery(
            Long happyornotSurveyId,
            boolean peerAnswer,
            List<TextHasValue> columns,
            Integer limit,
            Map<String, Object> params
    ) {
        String select = "";
        String groupBy = "";
        String where = "";
        String orderBy = "";
        if (columns != null) {
            select = (
                columns.stream().map((TextHasValue tv) -> tv.getText() + " AS " + tv.getValue()).reduce("", (a, b) -> a + ", " + b)
            );
            groupBy = (
                columns.stream().map((TextHasValue tv) -> tv.getText()).reduce("", (a, b) -> a + ", " + b)
            );
        }
        if (peerAnswer) {
            select += ""
                + ",SUM(CASE WHEN a.answer = 1 THEN 1 ELSE 0 END) AS answer1 "
                + ",SUM(CASE WHEN a.answer = 2 THEN 1 ELSE 0 END) AS answer2 "
                + ",SUM(CASE WHEN a.answer = 3 THEN 1 ELSE 0 END) AS answer3 "
                + ",SUM(CASE WHEN a.answer = 4 THEN 1 ELSE 0 END) AS answer4 "
                + ",SUM(CASE WHEN a.answer = 5 THEN 1 ELSE 0 END) AS answer5 "
                + ",SUM(CASE WHEN a.answer = 6 THEN 1 ELSE 0 END) AS answer6 "
                + ",SUM(CASE WHEN a.answer = 7 THEN 1 ELSE 0 END) AS answer7 "
                + ",SUM(CASE WHEN a.answer = 8 THEN 1 ELSE 0 END) AS answer8 "
                + ",SUM(CASE WHEN a.answer = 9 THEN 1 ELSE 0 END) AS answer9 "
                + ",SUM(CASE WHEN a.answer = 10 THEN 1 ELSE 0 END) AS answer10";
            orderBy += " min(a.createdDate)";
        } else {
            select += ""
                + ",count(*) AS answerCount "
                + ",a.answer AS answer ";
            groupBy += ""
                + ",a.answer";
            orderBy += ""
                + " a.answer"
                + ", min(a.createdDate)";
        }
        if (params != null){
            if (params.containsKey("dateEnd")) {
                where += " AND a.createdDate <= :dateEnd";
            }
            if (params.containsKey("dateStart")) {
                where += " AND a.createdDate >= :dateStart";
            }        
        }
        String query = ""
            + " SELECT new map("
                + " s.description AS question"
                + ",s.facesDesign AS facesDesign"
                + ",min(a.createdDate) AS firstEvent"
                + ",max(a.createdDate) AS lastEvent"
                + ",s.customAnswer1 AS customAnswer1 "
                + ",s.customAnswerName1 AS customAnswerName1 "
                + ",s.customAnswer2 AS customAnswer2 "
                + ",s.customAnswerName2 AS customAnswerName2 "
                + ",s.customAnswer3 AS customAnswer3 "
                + ",s.customAnswerName3 AS customAnswerName3 "
                + ",s.customAnswer4 AS customAnswer4 "
                + ",s.customAnswerName4 AS customAnswerName4 "
                + ",s.customAnswer5 AS customAnswer5 "
                + ",s.customAnswerName5 AS customAnswerName5 "
                + ",s.customAnswer6 AS customAnswer6 "
                + ",s.customAnswerName6 AS customAnswerName6 "
                + ",s.customAnswer7 AS customAnswer7 "
                + ",s.customAnswerName7 AS customAnswerName7 "
                + ",s.customAnswer8 AS customAnswer8 "
                + ",s.customAnswerName8 AS customAnswerName8 "
                + ",s.customAnswer9 AS customAnswer9 "
                + ",s.customAnswerName9 AS customAnswerName9 "
                + ",s.customAnswer10 AS customAnswer10 "
                + ",s.customAnswerName10 AS customAnswerName10 "
                + ",s.hasCustomsAnswers AS hasCustomsAnswers "
                + select
            + " )"
            + " FROM " + HappyornotSurveyAnswer.class.getCanonicalName() + " a "
            + " JOIN a.happyornotTarget t "
            + " JOIN t.happyornotSurvey s "
            + " WHERE "
                + " a.happyornotTargetId = t.id "
                + " AND t.status = 1 "
                + " AND a.status = 1 "
                + " AND s.status = 1 "
                + " AND s.id = " + happyornotSurveyId
                + where
            + " GROUP BY "
                + " s.description "
                + ",s.facesDesign "
                + ",s.customAnswer1"
                + ",s.customAnswer2"
                + ",s.customAnswer3"
                + ",s.customAnswer4"
                + ",s.customAnswer5"
                + ",s.customAnswer6"
                + ",s.customAnswer7"
                + ",s.customAnswer8"
                + ",s.customAnswer9"
                + ",s.customAnswer10"
                + ",s.customAnswerName1"
                + ",s.customAnswerName2"
                + ",s.customAnswerName3"
                + ",s.customAnswerName4"
                + ",s.customAnswerName5"
                + ",s.customAnswerName6"
                + ",s.customAnswerName7"
                + ",s.customAnswerName8"
                + ",s.customAnswerName9"
                + ",s.customAnswerName10"
                + ",s.hasCustomsAnswers"
                + groupBy
            + " ORDER BY "
                + orderBy;
        List<Map<String, Object>> answers;
        if (limit == null) {
            answers = dao.HQL_findByQuery(query, params);
        } else {
            query += " DESC";
            answers = dao.HQL_findByQueryLimit(query, params, limit);
        }
        return answers;
    }
    
    @PostMapping
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("updateBase64")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> updateBase64(@RequestBody HappyornotTarget target){
        return dao.HQL_updateByQuery("UPDATE " + HappyornotTarget.class.getCanonicalName() + " t "
                + "SET t.qrBase64 = :qrBase64" 
                + " WHERE t.id = :targetId", ImmutableMap.of("qrBase64", target.getQrBase64(), "targetId", target.getId())) 
                == 1 ? new ResponseEntity<>(true, HttpStatus.OK) : new ResponseEntity<>(true, HttpStatus.CONFLICT);
    }
    
    @GetMapping
    @PreAuthorize(HAS_QUERY_ACCESS)
    @RequestMapping("get-qr-code-by-target/{targetId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, String>> getQrCodeByTarget(@PathVariable(name = "targetId", required = true) Long targetId ) {
        String base64QrCodeByTarget = dao.HQL_findSimpleString(""
                + "SELECT t.qrBase64 FROM " + HappyornotTarget.class.getCanonicalName() + " t "
                        + " WHERE t.id = :targetId ",
                ImmutableMap.of("targetId", targetId));
        if (base64QrCodeByTarget == null) {
            base64QrCodeByTarget = "";
            return new ResponseEntity<Map<String, String>>(ImmutableMap.of("qrBase64", base64QrCodeByTarget), HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity<Map<String, String>>(ImmutableMap.of("qrBase64", base64QrCodeByTarget), HttpStatus.OK);
    }
    
   private boolean canModifyAnySurvey(){
       return  SecurityUtils.getLoggedUserServices().contains(ProfileServices.HAPPYORNOT_SURVEY_MODIFIER);
       
   }
   
    
    @GetMapping
    @RequestMapping("fonts/list")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    public List<HappyornotFonts> getAllFontsStyle() {
        return dao.HQL_findByQuery("SELECT new map("
                + " f.id AS fontId, "
                + " f.fontName AS fontName,"
                + " f.header AS header,"
                + " g.fontGroupName AS fontGroupName)"
                + " FROM " + HappyornotFonts.class.getCanonicalName() + " f"
                + " JOIN f.fontGroup g");
    }
    
    
    @RequestMapping("setting/url")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUrlApp(){
        Gson g = new Gson();
        return g.toJson(SettingsUtil.getAppUrlNoSlash());
    }
    
    @RequestMapping("updateCustomsAnswers/{happyornotSurveyId}/{customAnswerName}/{index}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> updateCustomAnswers(
            @PathVariable(value = "happyornotSurveyId", required = true) Long happyornotSurveyId,
            @PathVariable(value = "customAnswerName", required = true) String customAnswerName,
            @PathVariable(value = "index", required = true) Integer index) throws QMSException {

        for (int i = index; i < 10; i++) {
            dao.HQL_updateByQuery(""
                    + "UPDATE " + HappyornotSurveyAnswer.class.getCanonicalName() + " a " + " "
                    + "SET a.answer = " + index + ""
                    + " WHERE a.answer = " + (index + 1) + " "
                    + "AND a.happyornotTarget IN ( SELECT t.id "
                    + "FROM " + HappyornotTarget.class.getCanonicalName() + " t "
                    + "WHERE t.happyornotSurvey IN (SELECT s.id FROM " + HappyornotSurvey.class.getCanonicalName() + " s " + " WHERE s.id = " + happyornotSurveyId + ") )");
        }
        return new ResponseEntity<>(true, HttpStatus.OK);
    }

    @RequestMapping("getSurveyLink/{id}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public ResponseEntity<Map> getSurveyLink(@PathVariable(value = "id", required = true) Long id) {
        return new ResponseEntity(dao.getLinkData(id), HttpStatus.OK);
    }

    @PostMapping("saveSurveyLink")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<HappyornotLink> saveSurveyLink(@RequestBody HappyornotLink link) {
        return new ResponseEntity(dao.updateSurveyLink(link, SecurityUtils.getLoggedUser()), HttpStatus.OK);
    }

    @RequestMapping("getAllLinks")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public GridInfo<Map<String,Object>> getAllLinks(@RequestBody GridFilter filter) {
        return dao.getAllLinks(filter);

    }

    @RequestMapping("changeUrlTokenSurveyDestination/{surveyId}/{targetId}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public ResponseEntity<Integer> changeUrlTokenSurveyDestination(
            @PathVariable(value = "surveyId", required = true) Long surveyId, 
            @PathVariable(value = "targetId", required = true) Long targetId) {
        int result = dao.changeUrlTokenSurveyDestination(surveyId, targetId);
        return new ResponseEntity(result, HttpStatus.OK);
    }
    
    @RequestMapping("getAllTargets")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public GridInfo getAllTargets(@RequestBody GridFilter filter) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("s.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        return dao.HQL_getRows(""
                + "SELECT new map(s.id as id, s.code AS code, s.description AS description) FROM " 
                + HappyornotSurvey.class.getCanonicalName() + " s ",filter);
    }
    
    @RequestMapping("usersAgentsByLink/{linkId}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class}) 
    public GridInfo<HappyornotUserAgent> getUsersAgentsByLink(
            @RequestBody GridFilter filter, 
            @PathVariable(value = "linkId", required = true) Long linkId) {
        return dao.getUsersAgentsByLink(filter, linkId);
    }
    
    
    @RequestMapping("updateLinkStatus/{linkId}/{status}")
    @PreAuthorize(HAS_DESIGN_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class}) 
    public ResponseEntity<Boolean> updateLinkStatus(
            @PathVariable(value = "linkId", required = true) Long linkId,
            @PathVariable(value = "status", required = true) Integer status) {
        return dao.updateLinkStatus(linkId, status, SecurityUtils.getLoggedUser());
    }
    
    @RequestMapping("download-survey-answer-image/{answerNumber}/{surveyId}")
    @PreAuthorize(HAS_QUERY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public void downloadSurveyAnswerImage(
            @PathVariable(name = "answerNumber" , required = true) Integer answerNumber,
            @PathVariable(name = "surveyId", required = true) Long surveyId,
            HttpServletResponse response,
            HttpServletRequest request
    ) throws FileNotFoundException, IOException
    {
        FileManager manager = new FileManager();
        String base64Image = dao.HQL_findSimpleString(""
                + "SELECT "
                    + "s.customAnswer" + answerNumber + 
                " FROM " + HappyornotSurvey.class.getCanonicalName() + " s "
                        + "WHERE s.id = :surveyId", 
                ImmutableMap.of("surveyId", surveyId));
        
        if (base64Image == null) {
            response.sendRedirect(request.getContextPath() + "/qms/assets/images/happy/answerWithoutImage.png");
            return;
        }
        
        if (base64Image.isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/qms/assets/images/happy/answerWithoutImage.png");
            return;
        }
        
        String[] data = base64Image.split(",");
        
        byte[] file = Base64.getDecoder().decode(data[1]);
        String type = data[0].substring(11, data[0].length() - 7);
        manager.writeHeaders(response, "answerName" + answerNumber + "." + type, "image/" + type , new Long(file.length), Boolean.TRUE);
        response.getOutputStream().write(file); 
    }
    
    @PostMapping
    @RequestMapping("resize-custom-image")
    @PreAuthorize(HAS_CREATE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {ExecutionError.class})
    public ResizeImageDto resizeCustomImage(@RequestBody ResizeImageDto image) throws IOException {
            String base64[] = image.getBase64().split(",");
            byte[] imageFile = Base64.getDecoder().decode(base64[1]);
            InputStream is = new ByteArrayInputStream(imageFile);
            BufferedImage bf = ImageIO.read(is);
            BufferedImage thumb = Thumbnails.of(bf).size(512, 512).keepAspectRatio(false).asBufferedImage();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(thumb, base64[0].substring(11, base64[0].length() - 7), baos);
            
            return new ResizeImageDto(base64[0] + "," +Base64.getEncoder().encodeToString(baos.toByteArray()), 512, 512);

    }
    

}
