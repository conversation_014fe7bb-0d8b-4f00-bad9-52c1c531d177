package qms.audit.entity;

import DPMS.Mapping.AuditIndividual;
import Framework.Config.CompositeStandardEntity;
import ape.pending.core.SoftBaseAPE;
import ape.pending.core.SoftEntityId;
import ape.pending.core.StrongEntityId;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.activity.entity.Activity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "audit_individual_activity")
public class AuditIndividualActivity extends CompositeStandardEntity<AuditIndividualActivityPK> 
        implements SoftBaseAPE<AuditIndividual, Activity>, Serializable, ILinkedComposityGrid<AuditIndividualActivityPK> {

    private static final long serialVersionUID = 1L;

    private AuditIndividualActivityPK id;
    private Long auditIndividualId;
    private Long fieldId;
    private Long activityId;

    public AuditIndividualActivity() {
    }

    public AuditIndividualActivity(AuditIndividualActivityPK id) {
        this.id = id;
    }

    public AuditIndividualActivity(Long auditIndividualId, Long fieldId, Long documentId, Integer commitmentTask) {
        this.id = new AuditIndividualActivityPK(auditIndividualId, fieldId, documentId, commitmentTask);
    }

    @Override
    public AuditIndividualActivityPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public AuditIndividualActivityPK getId() {
        return id;
    }

    @Override
    public void setId(AuditIndividualActivityPK id) {
        this.id = id;
    }

    @Basic(optional = false)
    @StrongEntityId
    @Column(name = "audit_individual_id", insertable = false, updatable = false)
    public Long getAuditIndividualId() {
        return auditIndividualId;
    }

    public void setAuditIndividualId(Long auditIndividualId) {
        this.auditIndividualId = auditIndividualId;
    }

    @Basic(optional = false)
    @Column(name = "field_id", insertable = false, updatable = false)
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @SoftEntityId
    @Basic(optional = false)
    @Column(name = "activity_id", insertable = false, updatable = false)
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AuditIndividualActivity other = (AuditIndividualActivity) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.audit.entity.AuditIndividualActivity{" + "id=" + id + '}';
    }

}
