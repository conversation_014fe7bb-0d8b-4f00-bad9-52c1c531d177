package qms.audit.pending;

import DPMS.Mapping.AuditIndividualList;
import DPMS.Mapping.IUser;
import Framework.Config.Utilities;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import qms.audit.dto.AuditIndividualPendingDto;
import qms.audit.pending.imp.ToAcceptResult;
import qms.audit.pending.imp.ToConfirmChangeByLeader;
import qms.audit.pending.imp.ToConfirmChangeByManager;
import qms.audit.pending.imp.ToConfirmDate;
import qms.audit.pending.imp.ToFillByHelper;
import qms.audit.pending.imp.ToFillByLeader;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AuditPendingDataSource {
    
    public static Collection<IPendingDto> getPendingRecords(IPendingRecordDAO dao) {
        final Class<? extends BaseAPE> base = AuditIndividualList.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
                new ToAcceptResult(dao),
                new ToConfirmChangeByLeader(dao),
                new ToConfirmChangeByManager(dao),
                new ToConfirmDate(dao),
                new ToFillByLeader(dao),
                new ToFillByHelper(dao)
        };
        final IUser user = SecurityUtils.getLoggedUser();
        final Integer countPendings = dao.getPendingCount(user, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return Utilities.EMPTY_SET;
        }
        LinkedHashSet<String> commonActions =
            PendingUtil.commonActions(
                CommonAction.ATTEND, CommonAction.HIDE // <--- Agregar funcionalidad general de pendientes (APE): REASIGNAR.
            );
        return dao.getPendingRecords(
                user,
                base,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "id = auditId",
                    "id = pendingRecordId@record",
                    "owner = pendingRecordUserId@record",
                    "code = auditCode",
                    "status = auditStatus",
                    "dteStart",
                    "dteEnd",
                    "tmpStart",
                    "tmpEnd",
                    "description = auditDescription",
                    "audited",
                    "lider = leadAuditor",
                    "businessUnitDepartmentDescription",
                    "departmentProcessDescription",
                    "auditTypeDescription",
                    "areaDescription",
                    "code = pendingTypeCode@ptype",
                    "dteStart = commitmentDate"
                )),
                operations
        ).stream().map((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            AuditIndividualPendingDto value = new AuditIndividualPendingDto();
            // valores de la auditoria
            value.setStatus((Integer) pending.get("auditStatus"));
            value.setLeadAuditor((String) pending.get("leadAuditor"));
            value.setAudited((String) pending.get("audited"));
            value.setDepartmentProcessDescription((String) pending.get("departmentProcessDescription"));
            value.setBusinessUnitDepartmentDescription((String) pending.get("businessUnitDepartmentDescription"));
            value.setAuditTypeDescription((String) pending.get("auditTypeDescription"));
            value.setAreaDescription((String) pending.get("areaDescription"));
            value.setDteStart((Date) pending.get("dteStart"));
            value.setDteEnd((Date) pending.get("dteEnd"));
            value.setTmpStart((Date) pending.get("tmpStart"));
            value.setTmpEnd((Date) pending.get("tmpEnd"));
            // valores requeridos para pendientes
            value.setDto(AuditIndividualPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setBase((String) pending.get("base"));
            value.setCode((String) pending.get("auditCode"));
            value.setPendingTypeCode(ape.getCode());
            value.setDescription((String) pending.get("auditDescription"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            value.setAvailableActions(commonActions);
            return value;
        }).collect(Collectors.toList());
    }
    
    public static IPendingOperation[] getPendingOperations(IPendingRecordDAO dao) {
        return new IPendingOperation[] {
            new ToAcceptResult(dao),
            new ToConfirmChangeByLeader(dao),
            new ToConfirmChangeByManager(dao),
            new ToConfirmDate(dao),
            new ToFillByLeader(dao),
            new ToFillByHelper(dao)
        };
    }
    
    public static Class<? extends BaseAPE> getBaseEntity(Module module) {
        return AuditIndividualList.class;
    }
}
