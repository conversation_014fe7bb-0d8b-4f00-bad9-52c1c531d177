package qms.audit.pending;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingInitializable;
import ape.pending.core.IPendingOperation;
import ape.pending.core.RuntimePending;
import ape.pending.dto.ColumnDTO;
import java.util.List;
import java.util.Set;
import mx.bnext.access.Module; 
import qms.escalation.dto.IEscalableDTO;

/**
 *
 * <AUTHOR>
 */
public abstract class AuditPendingOperations extends RuntimePending implements IPendingOperation, IPendingInitializable {

    protected static final String MODULE = Module.AUDIT.getKey();
    public static final String ALIAS_AUDIT = "a";
    public static final List<ColumnDTO> BASE_SUMMARY_FIELDS = BaseSummaryFieldBuilder.columns(
        "id = auditId",
        "code = auditCode",
        "status = auditStatus",
        "description = auditDescription",
        "audited",
        "lider = leadAuditor",
        "departmentProcessDescription",
        "dteStart = commitmentDate"
    );

    public AuditPendingOperations(IUntypedDAO dao) {
        super(dao);
        setSummaryTemplate("${auditCode}, ${auditDescription}");
        setBaseSummaryFields(BASE_SUMMARY_FIELDS);
    }
    
    @Override
    public void handleResultRecords(Set<IEscalableDTO> records) {}
   
}
