package qms.audit.pending.imp;

import DPMS.Mapping.Audit;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualList;
import DPMS.Mapping.AuditType;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.audit.pending.AuditPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToAcceptResult extends AuditPendingOperations {
    
  public static final String AUDIT_TO_ACCEPT_RESULT =  ""
        + " FROM " + AuditIndividual.class.getCanonicalName() + " a "
        + " CROSS JOIN " + User.class.getCanonicalName() + " u"
        + " LEFT JOIN a.departmentProcess dept "
        + " JOIN a.audit au "
        + " LEFT JOIN a.area area "
        + " WHERE a.status = " + AuditIndividual.STATUS.DONE
        + " AND a.deleted = " + AuditIndividual.IS_NOT_DELETED 
        + " AND au.deleted = " + Audit.IS_NOT_DELETED 
        + " AND ("
            + " au.type.scope = " + AuditType.PROCESS_SCOPE
            + " AND dept.attendantId = u.id "
            + " OR au.type.scope = " + AuditType.AREA_SCOPE
            + " AND area.attendantId = u.id "
        + ")";
  
    public static final String MANAGER = " AND u.id = :userId ";
    
    public ToAcceptResult(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS_AUDIT);
        setQuery(AUDIT_TO_ACCEPT_RESULT);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("u.id");
        setPendingType(getType(APE.AUDIT_TO_ACCEPT_RESULT));
        setModuleKey(MODULE);
        setBase(AuditIndividualList.class);
        setOwnerFieldFilter(MANAGER);
        setDependencies(ToFillByLeader.class, ToFillByHelper.class);
        setOperationType(ApeOperationType.STRONG);
    }
    
}