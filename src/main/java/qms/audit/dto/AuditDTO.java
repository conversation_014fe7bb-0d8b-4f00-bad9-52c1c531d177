package qms.audit.dto;

import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import qms.document.mail.IRecipientId;

/**
 *
 * <AUTHOR>
 */
public class AuditDTO implements IRecipientId {
    
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String code;
    private Date dteStart;
    private Date dteEnd;
    private Date tmpStart;
    private Date tmpEnd ;
    private String businessUnitDepartment;
    private String departmentProcess;
    private String area;
    private String audit;
    private String attendant;
    private String helpers;
    private String survey;
    private String comment;    
    private Long recipientId;

    public AuditDTO() {
        
    }
    
    public AuditDTO(Long id, String code, Date dteStart, Date dteEnd, Date tmpStart, Date tmpEnd, String audit, String attendant, String survey) {
        this.id = id;
        this.code = code;
        this.dteStart = dteStart;
        this.dteEnd = dteEnd;
        this.tmpStart = tmpStart;
        this.tmpEnd = tmpEnd;
        this.audit = audit;
        this.attendant = attendant;
        this.survey = survey;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getDteStart() {
        return dteStart;
    }

    public void setDteStart(Date dteStart) {
        this.dteStart = dteStart;
    }

    public Date getDteEnd() {
        return dteEnd;
    }

    public void setDteEnd(Date dteEnd) {
        this.dteEnd = dteEnd;
    }

    @Temporal(TemporalType.TIME)
    public Date getTmpStart() {
        return tmpStart;
    }

    public void setTmpStart(Date tmpStart) {
        this.tmpStart = tmpStart;
    }

    @Temporal(TemporalType.TIME)
    public Date getTmpEnd() {
        return tmpEnd;
    }

    public void setTmpEnd(Date tmpEnd) {
        this.tmpEnd = tmpEnd;
    }

    public String getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(String businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    public String getDepartmentProcess() {
        return departmentProcess;
    }

    public void setDepartmentProcess(String departmentProcess) {
        this.departmentProcess = departmentProcess;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAudit() {
        return audit;
    }

    public void setAudit(String audit) {
        this.audit = audit;
    }

    public String getAttendant() {
        return attendant;
    }

    public void setAttendant(String attendant) {
        this.attendant = attendant;
    }

    public String getHelpers() {
        return helpers;
    }

    public void setHelpers(String helpers) {
        this.helpers = helpers;
    }

    public String getSurvey() {
        return survey;
    }

    public void setSurvey(String survey) {
        this.survey = survey;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public Long getRecipientId() {
        return this.recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AuditDTO other = (AuditDTO) obj;
        return Objects.equals(this.code, other.code);
    }

    @Override
    public String toString() {
        return "AuditDTO{" + "code=" + code + ", dteStart=" + dteStart + ", dteEnd=" + dteEnd + ", tmpStart=" + tmpStart + ", tmpEnd=" + tmpEnd + ", businessUnitDepartment=" + businessUnitDepartment + ", departmentProcess=" + departmentProcess + ", area=" + area + ", audit=" + audit + ", attendant=" + attendant + ", helpers=" + helpers + ", survey=" + survey + '}';
    }         
    
}
