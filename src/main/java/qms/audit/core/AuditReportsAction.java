package qms.audit.core;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Config.Utilities;
import jakarta.servlet.http.Cookie;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import qms.framework.util.SettingsUtil;
import qms.jasper.ReportGenerator;
import qms.jasper.ReportGeneratorUtil;
import qms.jasper.params.IReportAreaAction;
import qms.jasper.params.IReportBusinessUnitAction;
import qms.jasper.params.IReportDepartamentAction;
import qms.jasper.params.IReportEndDateAction;
import qms.jasper.params.IReportProcessAction;
import qms.jasper.params.IReportStartDateAction;
import qms.util.FileDownloader;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class AuditReportsAction extends FileDownloader implements IReportBusinessUnitAction, IReportStartDateAction, IReportEndDateAction, IReportProcessAction, IReportDepartamentAction, IReportAreaAction {

    private String businessUnitId;
    private String dteStart;
    private String dteEnd;
    private String intTipoExtencionReporte;
    private String processId;
    private String implementerId;
    private String verifierId;
    private String ruta;
    private Boolean disableInline;
    private String departmentId;
    private String areaId;

    @Override
    public String execute() throws Exception {
        Cookie cookie = null;
        if (getToken() != null) {
            handleCookie(cookie);
        }
        IFilesDAO dao = getBean(IFilesDAO.class);
        final String fileName = ReportGeneratorUtil.getFileName(ruta);
        final String tipo = ReportGeneratorUtil.getFileExtension(intTipoExtencionReporte);
        ReportGenerator generator = new ReportGenerator(
                dao, ruta, ReportGenerator.REPORT_EXTENSION.valueOf(tipo), SettingsUtil.getAppUrlNoSlash(), this, this.disableInline
        );
        generator.setReportName(fileName);
        if (!generator.isEmpty()) {
            generator.fillResponseContent(response, getSession());
            return SUCCESS;
        }
        return ERROR;
    }
    
    private String getFilterParam() {
        StringBuilder filter = new StringBuilder(200);
        filter.append(" bud.business_unit_id = ").append(businessUnitId);
        if (!dteStart.isEmpty()) {
            filter.append(" and aup.dte_start >= CONVERT(DATE, '").append(getStartDate()).append("', 103) ");
        }
        if (!dteEnd.isEmpty()) {
            filter.append(" and aup.dte_start <= CONVERT(DATE, '").append(getEndDate()).append("', 103) ");
        }
        if(!"".equals(processId)){
            filter.append(" and ").append(" p.process_id = ").append(processId);
        }
        if(!"".equals(implementerId) && implementerId != null){
            filter.append(" and ").append(" implementer.user_id = ").append(implementerId);
        }
        if(!"".equals(verifierId) && verifierId != null){
            filter.append(" and ").append(" verifier.user_id = ").append(verifierId);
        }
        if (!"-1".equals(departmentId) && !"".equals(departmentId) && departmentId != null) {
            filter.append(" AND").append(" dep.department_id = ").append(departmentId);
        }
        if (!"-1".equals(areaId) && !"".equals(areaId) && areaId != null) {
            filter.append(" AND ").append(" aup.area_id = ").append(areaId);
        }
        return filter.toString();
    }

    @StrutsParameter
    public void setBusinessUnitId(String businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @StrutsParameter
    public void setDteStart(String dteStart) {
        this.dteStart = dteStart;
    }

    @StrutsParameter
    public void setDteEnd(String dteEnd) {
        this.dteEnd = dteEnd;
    }

    @StrutsParameter
    public void setIntTipoExtencionReporte(String intTipoExtencionReporte) {
        this.intTipoExtencionReporte = intTipoExtencionReporte;
    }

    @StrutsParameter
    public void setProcessId(String processId) {
        this.processId = processId;
    }

    @StrutsParameter
    public void setImplementerId(String implementerId) {
        this.implementerId = implementerId;
    }

    @StrutsParameter
    public void setVerifierId(String verifierId) {
        this.verifierId = verifierId;
    }

    @StrutsParameter
    public void setRuta(String ruta) {
        this.ruta = ruta;
    }
    
    @Override
    public String getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public String getFilter() {
        return getFilterParam();
    }

    @Override
    public String getStartDate() {
        return Framework.Config.Utilities.formatDateBy(Framework.Config.Utilities.parseDateBy(dteStart, "yyyy-MM-dd"), "dd/MM/yyyy");
    }

    @Override
    public String getEndDate() {
        return Framework.Config.Utilities.formatDateBy(Framework.Config.Utilities.parseDateBy(dteEnd, "yyyy-MM-dd"), "dd/MM/yyyy");
    }

    @Override
    public String getProcessId() {
        return processId;
    }

    @Override
    public String getProcessName() {
        if (processId == null || processId.trim().isEmpty()) {
            return Utilities.EMPTY_STRING;
        }
        return Utilities.getUntypedDAO().HQL_findSimpleString(""
            + " SELECT p.description"
            + " FROM " + DPMS.Mapping.Process.class.getName() + " p"
            + " WHERE p.id = " + processId
        );
    }
    
    public Boolean setDisableInline(Boolean disableInline) {
        return this.disableInline = disableInline;
    }

    public Boolean getDisableInline() {
        return this.disableInline;
    }

    @Override
    public String getDepartmentId() {
        return departmentId;
    }

    @StrutsParameter
    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    public String getAreaId() {
        return areaId;
    }

    @StrutsParameter
    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }
    
}
