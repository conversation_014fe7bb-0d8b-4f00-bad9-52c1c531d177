
package qms.tress.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.CreatedDate;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@Table(name = "tress_temporal_data")
public class TRESSTemporalData extends DomainObject implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Date createdDate;
    private String processId;
    private String nomina;
    private String nombres;
    private String apellidoPaterno;
    private String apellidoMaterno;
    private String ubicacion;
    private String email;
    private String usuarioAd;
    private String puesto;
    private String rolDelEmpleado;
    private String statusEmpleado;
    private String centroCostosSap;
    private String tipoDeEmpleado;

    @Override
    @Id
    @Basic(optional = false)
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getNomina() {
        return nomina;
    }

    public void setNomina(String nomina) {
        this.nomina = nomina;
    }

    public String getNombres() {
        return nombres;
    }

    public void setNombres(String nombres) {
        this.nombres = nombres;
    }

    @Column(name = "apellido_paterno")
    public String getApellidoPaterno() {
        return apellidoPaterno;
    }

    public void setApellidoPaterno(String apellidoPaterno) {
        this.apellidoPaterno = apellidoPaterno;
    }

    @Column(name = "apellido_materno")
    public String getApellidoMaterno() {
        return apellidoMaterno;
    }

    public void setApellidoMaterno(String apellidoMaterno) {
        this.apellidoMaterno = apellidoMaterno;
    }

    public String getUbicacion() {
        return ubicacion;
    }

    public void setUbicacion(String ubicacion) {
        this.ubicacion = ubicacion;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUsuarioAd() {
        return usuarioAd;
    }

    @Column(name = "usuario_ad")
    public void setUsuarioAd(String usuarioAd) {
        this.usuarioAd = usuarioAd;
    }

    public String getPuesto() {
        return puesto;
    }

    public void setPuesto(String puesto) {
        this.puesto = puesto;
    }

    @Column(name = "rol_del_empleado")
    public String getRolDelEmpleado() {
        return rolDelEmpleado;
    }

    public void setRolDelEmpleado(String rolDelEmpleado) {
        this.rolDelEmpleado = rolDelEmpleado;
    }

    @Column(name = "status_empleado")
    public String getStatusEmpleado() {
        return statusEmpleado;
    }

    public void setStatusEmpleado(String statusEmpleado) {
        this.statusEmpleado = statusEmpleado;
    }

    @Column(name = "centro_costos_sap")
    public String getCentroCostosSap() {
        return centroCostosSap;
    }

    public void setCentroCostosSap(String centroCostosSap) {
        this.centroCostosSap = centroCostosSap;
    }

    @Column(name = "tipo_de_empleado")
    public String getTipoDeEmpleado() {
        return tipoDeEmpleado;
    }

    public void setTipoDeEmpleado(String tipoDeEmpleado) {
        this.tipoDeEmpleado = tipoDeEmpleado;
    }

    @Column(name = "process_id")
    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }
    
    
}
