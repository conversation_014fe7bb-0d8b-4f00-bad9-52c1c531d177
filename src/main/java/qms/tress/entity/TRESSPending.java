
package qms.tress.entity;

import DPMS.Mapping.IAuditableEntity;
import mx.bnext.core.util.IStatusEnum;
import Framework.Config.StandardEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.interfaces.IPersistableManualId;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
@Entity
@Table(name = "tress_pending")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class TRESSPending extends StandardEntity<TRESSPending> implements IAuditableEntity, Serializable, IPersistableManualId {

    private static final long serialVersionUID = 1L;
    public static enum STATUS implements IStatusEnum {
        VERIFY_STATUS(0, IStatusEnum.COLOR_GRAY),
        CREATE_USER(1, IStatusEnum.COLOR_GREEN),
        DEACTIVATE_USER(2, IStatusEnum.COLOR_RED),
        UPDATE_USER(3, IStatusEnum.COLOR_CYAN),
        ERROR_USER(4, IStatusEnum.COLOR_BLACK)
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public String toString() { 
            return this.value.toString();
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return CREATE_USER;
        }
        public static STATUS getEnumValue(Integer value) {
            STATUS temp;
            for (STATUS ar : STATUS.class.getEnumConstants()) {
                if(Objects.equals(value, ar.getValue())) {
                    return ar;
                }
            }
            return null;
        }
    }

    public TRESSPending() {
    }
    
    public TRESSPending(TRESSPendingView tressPendingView) {
        this.setId(tressPendingView.getId());
        this.processId = tressPendingView.getProcessId();
        this.code = tressPendingView.getCode();
        this.status = tressPendingView.getStatus();
        this.newBusinessUnitDescription = tressPendingView.getNewBusinessUnitDescription();
        this.newMail = tressPendingView.getNewMail();
        this.newFullUserDescription = tressPendingView.getNewFullUserDescription();
        this.newPositionDescription = tressPendingView.getNewPositionDescription();
        this.newPhysicalLocation = tressPendingView.getNewPhysicalLocation();
        this.employeeType = tressPendingView.getEmployeeType();
    }

    private String code;            //<-- cuenta de usuario
    private Integer status;         
    private Integer deleted = 0;
    private String description;     //<-- sin usarse tal cual, tiene el mismo valor que "newFullUserDescription" 
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private String newBusinessUnitDescription;
    private String newMail;
    private String newFullUserDescription;
    private String newPositionDescription;

    private String employeeType;
    private String newPhysicalLocation;
    private String processId;
    
    //Updatable, Insertable FALSE
    private TRESSUserInfoView userInfo;
    
    @Id
    @Basic(optional = false)
    @Column(name = "tress_pending_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }   

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "new_full_user_description", updatable = false, insertable = false)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof TRESSPending)) {
            return false;
        }
        TRESSPending other = (TRESSPending) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.TRESSPending[ id=" + id + " ]";
    }

    @Column(name = "new_business_unit_description")
    public String getNewBusinessUnitDescription() {
        return newBusinessUnitDescription;
    }

    public void setNewBusinessUnitDescription(String newBusinessUnitDescription) {
        this.newBusinessUnitDescription = newBusinessUnitDescription;
    }

    @Column(name = "new_mail")
    public String getNewMail() {
        return newMail;
    }

    public void setNewMail(String newMail) {
        this.newMail = newMail;
    }

    @Column(name = "new_full_user_description")
    public String getNewFullUserDescription() {
        return newFullUserDescription;
    }

    public void setNewFullUserDescription(String newFullUserDescription) {
        this.newFullUserDescription = newFullUserDescription;
    }

    @Column(name = "new_position_description")
    public String getNewPositionDescription() {
        return newPositionDescription;
    }

    public void setNewPositionDescription(String newPositionDescription) {
        this.newPositionDescription = newPositionDescription;
    }

    @Column(name = "new_physical_location")
    public String getNewPhysicalLocation() {
        return newPhysicalLocation;
    }

    public void setNewPhysicalLocation(String newPhysicalLocation) {
        this.newPhysicalLocation = newPhysicalLocation;
    }

    @Column(name = "process_id")
    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    
    @OneToOne(optional = true)
    @JoinColumns({
        @JoinColumn(name = "code", referencedColumnName = "account", updatable = false, insertable = false)
    })
    public TRESSUserInfoView getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(TRESSUserInfoView userInfo) {
        this.userInfo = userInfo;
    }

    @Column(name = "employee_type")
    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }
    
    
}
