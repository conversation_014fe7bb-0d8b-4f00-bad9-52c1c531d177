package qms.activity.DAOInterface;

import Framework.Config.TextLongValue;
import Framework.DAO.Implementation;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.dto.ActivitySaveData;
import qms.activity.dto.ActivitySaveManyResult;
import qms.activity.entity.ActivityType;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.QMSException;

@Implementation(name = "ActivityDAO")
public interface IActivityDataExchangeDAO {

    ActivitySaveManyResult reportActivityMany(List<ActivitySaveData> entities, ILoggedUser loggedUser);
    
    ActivityDataSourceDto changeActivityDataSource(
            Long workflowId,
            Long activityTypeId,
            Long businessUnitDepartmentId,
            Module module, 
            ILoggedUser loggedUser,
            Map<String, Object> typeOverrides
    ) throws QMSException;

    Long getFirstActivityTypeId();
    
    ActivityDataSourceDto getNewActivityDataSourceByType(
            Module module,
            Long workflowId,
            Long typeId, 
            Long businessUnitDepartmentId,
            ILoggedUser loggedUser, 
            Map<String, Object> typeOverrides
    ) throws QMSException;
    
    TimesheetDataSourceDto getProjectCatalogs(ILoggedUser loggedUser);

    ActivityType findTypeById(Long activityTypeId);
    
    Set<TextLongValue> getUsers(Set<Long> userIds);
    
    IActivityDataExchangeDAO getDataExchangeProxy();  

    BusinessUnitRef findBusinessUnit(Long businessUnitId);

    BusinessUnitDepartmentRef findBusinessUnitDepartment(Long businessUnitDepartmentId);


}
