package qms.activity.rest;

import DPMS.DAOInterface.IUserDAO;
import Framework.Config.Utilities;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.util.RecordRowsCache;
import bnext.exception.ExplicitRollback;
import bnext.exception.MakePersistentException;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.DAOInterface.IActivityPendingDAO;
import qms.activity.core.ActivityRole;
import qms.activity.core.ActivityStage;
import qms.activity.core.ActivityTaskStatus;
import qms.activity.core.ActivityUtil;
import static qms.activity.core.ActivityUtil.getAvailableActions;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.dto.ActivityDiff;
import qms.activity.dto.ActivityInfoDTO;
import qms.activity.dto.ActivityPendingDto;
import qms.activity.dto.ActivityResolutionItemDto;
import qms.activity.dto.PendingCancellationDto;
import qms.activity.dto.PendingResolutionDto;
import qms.activity.dto.PendingVerifyDto;
import qms.activity.dto.PendingVerifyNotApplyDto;
import qms.activity.dto.load.ActivityLoadDTO;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityResolution;
import qms.activity.entity.ActivityType;
import qms.activity.entity.ActivityTypeResolution;
import qms.activity.pending.VerifyAction;
import qms.activity.util.ActivityCancellationReason;
import qms.activity.util.ActivityField;
import qms.activity.util.ActivtyInfoUtil;
import qms.activity.util.CommitmentTask;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.QMSException;

/**
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("rest/activity/pending")
public class ActivityPendingController extends Loggable {

    private static final String RESOLUTION_QUERY = ""
            + " SELECT new " + ActivityResolutionItemDto.class.getCanonicalName() + "("
            + " r.id AS value "
            + ",r.name AS text "
            + ",r.description AS description "
            + ",r.closeActivityEnabled as closeActivityEnabled "
            + " )"
            + " FROM " + ActivityResolution.class.getCanonicalName() + " r "
            + " WHERE "
            + " r.deleted = 0"
            + " AND r.status = " + ActivityResolution.STATUS.ACTIVE.getValue()
            + " AND r.id IN ( "
            + " SELECT c.id.activityResolutionId "
            + " FROM " + ActivityTypeResolution.class.getCanonicalName() + " c "
            + " WHERE "
            + " c.id.activityTypeId = :activityTypeId"
            + " ) ";

    private static final String RESOLUTION_QUERY_OPEN = " SELECT new " + ActivityResolutionItemDto.class.getCanonicalName() + "("
            + " r.id AS id "
            + ",r.name AS name "
            + ",r.description AS description "
            + ",r.closeActivityEnabled as closeActivityEnabled "
            + " )"
            + " FROM " + ActivityResolution.class.getCanonicalName() + " r "
            + " WHERE "
            + " r.deleted = 0"
            + " AND r.status = " + ActivityResolution.STATUS.ACTIVE.getValue()
            + " AND r.id IN ( "
            + " SELECT c.id.activityResolutionId "
            + " FROM " + ActivityTypeResolution.class.getCanonicalName() + " c "
            + " WHERE "
            + " c.id.activityTypeId = :activityTypeId"
            + " ) "
            + " AND r.closeActivityEnabled IN (0)";

    private static final String RESOLUTION_QUERY_CLOSE = " SELECT new " + ActivityResolutionItemDto.class.getCanonicalName() + "("
            + " r.id AS id "
            + ",r.name AS name "
            + ",r.description AS description "
            + ",r.closeActivityEnabled as closeActivityEnabled "
            + " )"
            + " FROM " + ActivityResolution.class.getCanonicalName() + " r "
            + " WHERE "
            + " r.deleted = 0"
            + " AND r.status = " + ActivityResolution.STATUS.ACTIVE.getValue()
            + " AND r.id IN ( "
            + " SELECT c.id.activityResolutionId "
            + " FROM " + ActivityTypeResolution.class.getCanonicalName() + " c "
            + " WHERE "
            + " c.id.activityTypeId = :activityTypeId"
            + " ) "
            + " AND r.closeActivityEnabled IN (1)";

    @Autowired
    @Qualifier("PendingRecordDAO")
    private IPendingRecordDAO pendingDao;


    @GetMapping({"data-source/to-complete"})
    public List<ActivityPendingDto> dataSourceToComplete() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return pendingDao.getPendingRecords(APE.ACTIVITY_TO_COMPLETE, RecordRowsCache.ENABLED, loggedUser).stream().map(
                this::pendingRecordsProjection
        ).collect(Collectors.toList());
    }


    @GetMapping({"data-source/to-verify"})
    public List<ActivityPendingDto> dataSourceToVerify() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return pendingDao.getPendingRecords(APE.ACTIVITY_TO_VERIFY, RecordRowsCache.ENABLED, loggedUser).stream().map(
                this::pendingRecordsProjection
        ).collect(Collectors.toList());
    }


    @GetMapping({"data-source/to-verify-delayed"})
    public List<ActivityPendingDto> dataSourceToVerifyDelayed() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return pendingDao.getPendingRecords(APE.ACTIVITY_TO_VERIFY_DELAYED, RecordRowsCache.ENABLED, loggedUser).stream().map(
                this::pendingRecordsProjection
        ).collect(Collectors.toList());
    }


    @GetMapping({"data-source/to-verify-not-apply"})
    public List<ActivityPendingDto> dataSourceToVerifyNotApply() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return pendingDao.getPendingRecords(APE.ACTIVITY_TO_VERIFY_NOT_APPLY, RecordRowsCache.ENABLED, loggedUser).stream().map(
                this::pendingRecordsProjection
        ).collect(Collectors.toList());
    }


    @GetMapping({"data-source/{activityId}"})
    public ActivityDataSourceDto dataSourcePending(@PathVariable(value = "activityId", required = true) Long activityId) throws QMSException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        final ActivityDataSourceDto ds = daoWrapper.getEditionActivityDataSource(info, loggedUser);
        final ActivityLoadDTO activity = ActivityUtil.getLinkedActivityLoad(info, loggedUser, daoWrapper);
        ds.setLoad(activity);
        return ds;
    }

    @PostMapping({"to-complete/{activityId}/{progress}"})
    public ResponseEntity<ActivityDiff> attendToStart(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "progress", required = true) Integer progress,
            final @RequestBody PendingResolutionDto dto
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        ActivityDiff diff;
        if (CommitmentTask.VERIFICATION.isEqual(info)) {
            List<ActivityInfoDTO> implementations = ActivityUtil.getVerificationInfoPendings(
                    info.getId(),
                    VerifyAction.INCOMPLETE,
                    Double.valueOf(progress),
                    dao,
                    loggedUser,
                    ""
            );
            diff = daoWrapper.verifyManyCompleted(info, new PendingVerifyDto(
                    dto.getComment(),
                    VerifyAction.INCOMPLETE,
                    implementations
            ), dto.getResolutionId(), loggedUser, null);
        } else {
            if (ActivityRole.IMPLEMENTER.equals(info.getLoggedUserRole())
                    || ActivityRole.SUPER_IMPLEMENTER.equals(info.getLoggedUserRole())
                    || ActivityRole.SUPER_VERIFIER.equals(info.getLoggedUserRole())
                    || ActivityRole.VERIFIER.equals(info.getLoggedUserRole())
                    || ActivityRole.ADMIN.equals(info.getLoggedUserRole())) {
                diff = this.attendToComplete(daoWrapper, loggedUser, dto.getComment(), activityId, Double.valueOf(progress), dto.getResolutionId());
            } else {
                getLogger().error("User {} can not complete activity {} with role {}",
                        new Object[]{
                                loggedUser.getId(),
                                activityId,
                                info.getLoggedUserRole()
                        }
                );
                return new ResponseEntity<>(HttpStatus.FORBIDDEN);
            }
        }
        if (diff != null) {
            return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    private ActivityDiff attendToComplete(
            final IActivityDAO daoWrapper,
            final ILoggedUser loggedUser,
            final String comment,
            final Long activityImplementationId,
            final Double progress,
            final Long resolutionId
    ) throws InvalidArgumentException, MakePersistentException {
        final List<Integer> validStatus = Arrays.asList(new Integer[]{
                Activity.STATUS.REPORTED.getValue(),
                Activity.STATUS.RETURNED.getValue(),
                Activity.STATUS.IN_PROCESS.getValue()
        });
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityImplementationId, loggedUser);
        if (validStatus.contains(info.getStatus())
                && Objects.equals(info.getApply(), 1)) {
            final ActivityDiff diff;
            if (progress < 100) {
                diff = daoWrapper.advanceActivity(
                        info,
                        progress,
                        info.getOutstandingSurveyId(),
                        info.getFormFillIsRequested(),
                        comment, loggedUser
                );
            } else {
                diff = daoWrapper.doneSingleActivity(info, comment, 0, resolutionId, loggedUser);
            }
            if (diff != null) {
                return diff;
            }
        }
        return null;
    }

    @GetMapping({"check-for-resolutions/{activityTypeId}", "check-for-resolutions/{activityTypeId}/{resolutionType}"})
    public ResponseEntity<List<ActivityResolutionItemDto>> checkForResolutions(
            final @PathVariable(value = "activityTypeId", required = true) Long activityTypeId,
            final @PathVariable(value = "resolutionType", required = false) String resolutionType
    ) throws InvalidArgumentException, MakePersistentException {
        Map<String, Object> param = ImmutableMap.of("activityTypeId", activityTypeId);
        String query;
        if (resolutionType != null) {
            switch (resolutionType) {
                case "OPEN_ACTIVITY":
                    query = RESOLUTION_QUERY_OPEN;
                    break;
                case "CLOSE_ACTIVITY":
                    query = RESOLUTION_QUERY_CLOSE;
                    break;
                default:
                    query = RESOLUTION_QUERY;
                    break;
            }
        } else {
            query = RESOLUTION_QUERY;
        }
        List<ActivityResolutionItemDto> resolutions = pendingDao.HQL_findByQuery(query, param, true, CacheRegion.CATALOGS_ACTIVITY, 0);
        return ResponseEntity.ok(resolutions);
    }

    @PostMapping({"to-verify/{activityId}", "to-verify/{activityId}/{resolutionId}"})
    public ResponseEntity<ActivityDiff> attendToVerify(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "resolutionId", required = false) Long resolutionId,
            final @RequestBody PendingVerifyDto activities
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        if (!ActivityRole.VERIFIER.equals(info.getLoggedUserRole())
                && !ActivityRole.ADMIN.equals(info.getLoggedUserRole())
                && !ActivityRole.SUPER_VERIFIER.equals(info.getLoggedUserRole())) {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
        ActivityDiff savedActivity;
        if (CommitmentTask.IMPLEMENTATION.isEqual(info) && info.getRecurrent() == 1) {
            savedActivity = daoWrapper.verifyActivity(info, activities.getComment(), resolutionId, loggedUser);
        } else if (CommitmentTask.IMPLEMENTATION.isEqual(info) && info.getRecurrent() == 0) {
            if (activities.getVerifyAction().equals(VerifyAction.INCOMPLETE)) {
                activities.setAttendingSingleActivityId(info.getId());
                activities.setLoadSpecificImplementation(true);
            }
            info = daoWrapper.getActivityInfoFromId(info.getPlannedVerificationActivityId(), loggedUser);
            savedActivity = daoWrapper.verifyManyCompleted(info, activities, resolutionId, loggedUser, activities.getAttendingSingleActivityId());
        } else {
            savedActivity = daoWrapper.verifyManyCompleted(info, activities, resolutionId, loggedUser, null);
        }
        if (savedActivity != null) {
            return new ResponseEntity<>(savedActivity, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    /**
     * Verificación como no aplica (Una sola implementación)
     *
     * @param activityId
     * @param apply
     * @return
     * @throws com.sun.star.auth.InvalidArgumentException
     * @throws bnext.exception.MakePersistentException
     */
    @PostMapping("to-verify-not-apply/{activityId}")
    public ResponseEntity<ActivityDiff> attendToVerifyNotApply(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingCancellationDto apply
    ) throws InvalidArgumentException, MakePersistentException {
        if (apply == null || apply.getCancellationReason() == null) {
            getLogger().error("Invalid body provided to to-verify-not-apply. Must be a object of type PendingCancellationDto.");
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        if (!ActivityCancellationReason.NONE.equals(apply.getCancellationReason()) && (apply.getSwapActivities() == null || apply.getSwapActivities().isEmpty())) {
            getLogger().error("Invalid body provided to to-verify-not-apply. Must be a object of type PendingCancellationDto with valid swapActivities.");
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityDiff diff;
        ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        if (
                ActivityRole.VERIFIER.equals(info.getLoggedUserRole())
                        || ActivityRole.ADMIN.equals(info.getLoggedUserRole())
                        || ActivityRole.SUPER_VERIFIER.equals(info.getLoggedUserRole())
                        || Objects.equals(apply.getApply(), 0)
        ) {
            List<ActivityInfoDTO> implementations = new ArrayList<>();
            if (!CommitmentTask.VERIFICATION.isEqual(info) && apply.getImplementations() == null) {
                // Solo se verifica la actividad de activityId
                info.setVerifyAction(VerifyAction.NOT_APPLY_VERIFY);
                implementations.add(info);
                ActivityInfoDTO infoVerification = daoWrapper.getActivityInfoFromId(info.getPlannedVerificationActivityId(), loggedUser);
                diff = dao.verifyNotApplyCompleted(infoVerification, new PendingVerifyDto(
                        apply,
                        VerifyAction.NOT_APPLY_VERIFY,
                        implementations
                ), apply, loggedUser);
            } else {
                // Si La actividad es de tipo verificación se obtienen las implementaciones seleccionadas en caso que sea nulo
                if (apply.getImplementations() != null) {
                    // Cuando se seleccionaron las actividades
                    final ActivtyInfoUtil util = new ActivtyInfoUtil();
                    implementations = util.getListActivityInfo(apply.getImplementations(), loggedUser);
                }
                diff = dao.verifyNotApplyCompleted(info, new PendingVerifyDto(
                        apply,
                        VerifyAction.NOT_APPLY_VERIFY,
                        implementations
                ), apply, loggedUser);
            }
        } else {
            diff = dao.returnNotApplyActivity(info, apply.getComment(), loggedUser);
        }
        if (diff != null) {
            return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    /**
     * Verificación como no aplica (con implementaciones)
     *
     * @param activityId
     * @param dto
     * @return
     * @throws com.sun.star.auth.InvalidArgumentException
     * @throws bnext.exception.MakePersistentException
     */
    @PostMapping("to-verify-not-many/{activityId}")
    public ResponseEntity attendToVerifyNotApplyMany(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingVerifyDto dto
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityDiff diff;
        ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        if (
                ActivityRole.VERIFIER.equals(info.getLoggedUserRole())
                        || ActivityRole.ADMIN.equals(info.getLoggedUserRole())
                        || ActivityRole.SUPER_VERIFIER.equals(info.getLoggedUserRole())
                        || Objects.equals(dto.getCancellation().getApply(), 0)
        ) {
            if (CommitmentTask.IMPLEMENTATION.isEqual(info)) {
                info = daoWrapper.getActivityInfoFromId(info.getPlannedVerificationActivityId(), loggedUser);
                diff = dao.verifyNotApplyMany(info, dto, loggedUser);
            } else {
                diff = dao.verifyNotApplyMany(info, dto, loggedUser);
            }
            if (diff != null) {
                return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
            }
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("to-verify-apply/{activityId}")
    public ResponseEntity<ActivityDiff> attendToVerifyApply(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingVerifyNotApplyDto apply
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        final ActivityDiff diff;
        diff = dao.returnNotApplyActivity(info, apply.getComment(), loggedUser);
        if (diff != null) {
            return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("to-not-apply/{activityId}")
    public ResponseEntity<ActivityDiff> attendToNotApply(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingCancellationDto message
    ) throws InvalidArgumentException, MakePersistentException {
        if (message == null || message.getCancellationReason() == null) {
            getLogger().error("Invalid body provided to to-not-apply. Must be a object of type PendingCancellationDto.");
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        if (!ActivityCancellationReason.NONE.equals(message.getCancellationReason()) && (message.getSwapActivities() == null || message.getSwapActivities().isEmpty())) {
            getLogger().error("Invalid body provided to to-not-apply. Must be a object of type PendingCancellationDto with valid swapActivities.");
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final List<Integer> validStatus = Arrays.asList(new Integer[]{
                Activity.STATUS.REPORTED.getValue(),
                Activity.STATUS.IN_PROCESS.getValue(),
                Activity.STATUS.RETURNED.getValue(),
                Activity.STATUS.IMPLEMENTED.getValue()
        });
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        if (!daoWrapper.canEditApply(info, loggedUser)) {
            final ActivityDiff diff = new ActivityDiff();
            diff.getChanges().setError("forbbiden");
            return new ResponseEntity<>(diff, HttpStatus.FORBIDDEN);
        }
        if (validStatus.contains(info.getStatus())) {
            ActivityDiff savedActivity;
            if (ActivityRole.ADMIN.equals(info.getLoggedUserRole())
                    || ActivityRole.VERIFIER.equals(info.getLoggedUserRole())
                    || ActivityRole.SUPER_VERIFIER.equals(info.getLoggedUserRole())) {
                // Si el verificador marca la actividad como NA, se marca automaticamente verificada
                savedActivity = dao.verifyNotApply(info, message, loggedUser);
            } else {
                savedActivity = daoWrapper.notApplyActivity(info, message, loggedUser, message.getResolutionId());
            }
            if (savedActivity == null) {
                return new ResponseEntity<>(HttpStatus.CONFLICT);
            }
            Integer status = savedActivity.getChanges().getStatus();
            Integer apply = savedActivity.getChanges().getApply();
            CommitmentTask commitmentTask = CommitmentTask.PROGRAM;
            if (info.getCommitmentTask() != null) {
                commitmentTask = CommitmentTask.fromValue(info);
            }
            final boolean hasChilds = info.getChildCount() != null && info.getChildCount() > 0;
            ActivityType.FILL_TYPE fillType = ActivityType.FILL_TYPE.fromValue(info.getFillType());
            ActivityStage stage = ActivityUtil.getActivityStage(status, apply, commitmentTask.getValue(), info.getProgress(), hasChilds);
            ActivityTaskStatus taskStatus = ActivityUtil.getActivityObjectiveStatus(
                    activityId,
                    stage,
                    commitmentTask,
                    status,
                    apply,
                    info.getImplementationOn(),
                    info.getVerificationOn(),
                    info.getPlannedImplementation(),
                    info.getPlannedVerificationDate(),
                    info.getDelayedByImplementer(),
                    info.getDelayedByVerifier(),
                    hasChilds
            );
            LinkedHashSet<String> availableActions = ActivityUtil.getAvailableActions(
                    savedActivity.getLoad(),
                    savedActivity.getLoad(),
                    taskStatus,
                    stage,
                    fillType,
                    loggedUser
            );
            savedActivity.getChanges().setTaskStatus(taskStatus);
            savedActivity.getChanges().setAvailableActions(availableActions);
            savedActivity.setSwapActivities(message.getSwapActivities());
            return new ResponseEntity<>(savedActivity, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("to-undone/{activityId}")
    public ResponseEntity attendToUndone(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingResolutionDto dto
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        List<Integer> validStatus = Arrays.asList(new Integer[]{
                Activity.STATUS.REPORTED.getValue(),
                Activity.STATUS.IN_PROCESS.getValue(),
                Activity.STATUS.NOT_APPLY.getValue(),
                Activity.STATUS.RETURNED.getValue(),
                Activity.STATUS.IMPLEMENTED.getValue(),
                Activity.STATUS.UNDONE.getValue()
        });
        if (validStatus.contains(info.getStatus())) {
            ActivityDiff diff;
            if (Objects.equals(CommitmentTask.VERIFICATION.getValue(), info.getCommitmentTask())) {
                info.setResolutionId(dto.getResolutionId());
                diff = daoWrapper.verifyUndoneActivity(info, dto.getComment(), dto.getResolutionId(), loggedUser);
            } else {
                diff = daoWrapper.undoneActivity(info, dto.getComment(), dto.getResolutionId(), loggedUser);
            }
            if (diff != null) {
                return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
            }
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping({"to-undone-many/{activityId}", "to-undone-many/{activityId}/{resolutionId}"})
    public ResponseEntity attendToUndoneMany(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "resolutionId", required = false) Long resolutionId,
            final @RequestBody PendingVerifyDto dto
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        List<Integer> validStatus = Arrays.asList(new Integer[]{
                Activity.STATUS.REPORTED.getValue(),
                Activity.STATUS.IN_PROCESS.getValue(),
                Activity.STATUS.NOT_APPLY.getValue(),
                Activity.STATUS.RETURNED.getValue(),
                Activity.STATUS.IMPLEMENTED.getValue(),
                Activity.STATUS.UNDONE.getValue()
        });
        if (validStatus.contains(info.getStatus())) {
            ActivityDiff diff;
            if (CommitmentTask.IMPLEMENTATION.isEqual(info) && dto.getImplementations() != null) {
                info = daoWrapper.getActivityInfoFromId(info.getPlannedVerificationActivityId(), loggedUser);
                diff = daoWrapper.verifyUndoneActivityMany(info, dto, resolutionId, loggedUser);
                if (diff != null) {
                    return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
                }
            } else if (Objects.equals(CommitmentTask.VERIFICATION.getValue(), info.getCommitmentTask()) && dto.getImplementations() != null) {
                diff = daoWrapper.verifyUndoneActivityMany(info, dto, resolutionId, loggedUser);
                if (diff != null) {
                    return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
                }
            }
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping("to-verify-delayed/{activityId}")
    public ResponseEntity<ActivityDiff> attendToVerifyDelayed(
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @RequestBody PendingVerifyDto activities
    ) throws InvalidArgumentException, MakePersistentException {
        IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, loggedUser);
        final ActivityDiff diff = daoWrapper.verifyManyDelayed(info, activities, loggedUser);
        if (diff != null) {
            return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    private ActivityPendingDto pendingRecordsProjection(Map pending) {
        ActivityPendingDto value = new ActivityPendingDto();
        value.setId((Long) pending.get("activityId"));
        value.setStatus((Integer) pending.get("activityStatus"));
        value.setApply((Integer) pending.get("apply"));
        value.setCommitmentTask((Integer) pending.get("commitmentTask"));
        value.setImplementationCount((Integer) pending.get("implementationCount"));
        value.setFillType((Integer) pending.get("fillType"));
        value.setProgress((Double) pending.get("progress"));
        value.setCommitmentDate((Date) pending.get("commitmentDate"));
        value.setImplementerNames((String) pending.get("implementerNames"));
        value.setImplementerIds((String) pending.get("implementerIds"));
        value.setCode((String) pending.get("activityCode"));
        value.setDescription((String) pending.get("activityDescription"));
        value.setModuleName((String) pending.get("moduleName"));
        return value;
    }


    @PostMapping("to-close-type-main-activity/{activityId}/{resolutionId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivityDiff> toCloseTypeMainActivity(
            @PathVariable(name = "activityId", required = true) Long activityId,
            @PathVariable(name = "resolutionId", required = true) Long resolutionId,
            @RequestBody PendingVerifyDto dto
    ) {
        if (resolutionId == null) {
            throw new ExplicitRollback("Resolution is NULL");
        }
        final IActivityPendingDAO dao = Utilities.getBean(IActivityPendingDAO.class);
        final IActivityDAO daoWrapper = ((IActivityDAO) dao);
        final Map<String, Object> newResolution = daoWrapper.HQL_findSimpleMap(""
                        + " SELECT new map ("
                        + " r.closeActivityEnabled as closeActivityEnabled, "
                        + " r.name as resolutionName"
                        + " )"
                        + " FROM " + ActivityResolution.class.getCanonicalName() + " r "
                        + " WHERE "
                        + " r.id = :resolutionId",
                ImmutableMap.of("resolutionId", resolutionId),
                true,
                CacheRegion.CATALOGS_ACTIVITY,
                0
        );
        if (newResolution == null) {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
        final String resolutionActivity = daoWrapper.HQL_findSimpleString(""
                        + " SELECT r.name as resolutionActivity "
                        + " FROM " + Activity.class.getCanonicalName() + " a "
                        + " JOIN " + ActivityResolution.class.getCanonicalName() + " r ON r.id = a.activityResolutionId"
                        + " WHERE "
                        + " a.id = :activityId"
                        + " AND a.activityResolutionId IS NOT NULL ",
                ImmutableMap.of("activityId", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        Integer status = Activity.STATUS.MAIN_OPEN.getValue();
        if (Boolean.TRUE.equals((Boolean) newResolution.get("closeActivityEnabled"))) {
            status = Activity.STATUS.MAIN_CLOSED.getValue();
        }

        ActivityInfoDTO info = daoWrapper.getActivityInfoFromId(activityId, SecurityUtils.getLoggedUser());
        ActivityDiff diff = new ActivityDiff(info);
        ActivityUtil.evaluateDiff(diff, ActivityField.STATUS, status);
        ActivityUtil.evaluateDiff(diff, ActivityField.RESOLUTION_ID, resolutionId);

        if (
                daoWrapper.HQL_updateByQuery(""
                                + " UPDATE " + Activity.class.getCanonicalName() + " a "
                                + " SET "
                                + " a.status = :status "
                                + ",a.activityResolutionId = " + resolutionId
                                + " WHERE a.id = :activityId",
                        ImmutableMap.of("status", status, "activityId", activityId),
                        true,
                        CacheRegion.ACTIVITY,
                        0
                ) > 0
        ) {
            final String tag = dao.getTag("CLOSE_ACTIVITY_MAIN")
                    .replace("{previousResolution}", resolutionActivity != null && !resolutionActivity.trim().isEmpty() ? resolutionActivity : "-")
                    .replace("{newResolution}", (String) newResolution.get("resolutionName"))
                    .replace("{comment}", dto.getComment());
            diff.getHistory().add(daoWrapper.saveHistoryByUser(info, tag, dto.getComment(), SecurityUtils.getLoggedUser(), null));

            // Agrega las reglas de hidden y disabledFields par ala resolución
            Map<String, Boolean> hiddenRules = new HashMap<>();
            hiddenRules.put("activityResolutionId", false);
            diff.setHiddenRules(hiddenRules);
            diff.setDisabledFields(hiddenRules);

            final LinkedHashSet<String> actions = getAvailableActions(info, info, daoWrapper, SecurityUtils.getLoggedUser());
            diff.getChanges().setAvailableActions(actions);
            diff.setEditionRules(daoWrapper.getEditionRules(info, SecurityUtils.getLoggedUser()));
            diff.setImplementers(daoWrapper.getImplementers(
                    daoWrapper.getBean(IUserDAO.class),
                    info,
                    info.getBusinessUnitDepartmentId(),
                    new ProfileServices[]{ProfileServices.ALL},
                    SecurityUtils.getLoggedUser()
            ));
            diff.setVerifiers(daoWrapper.getVerifiersInfo(
                    daoWrapper.getBean(IUserDAO.class),
                    info,
                    resolutionId,
                    new ProfileServices[]{ProfileServices.ALL},
                    SecurityUtils.getLoggedUser()
            ));

            return new ResponseEntity<>(diff, HttpStatus.ACCEPTED);
        }
        throw new ExplicitRollback("Invalid data, [activityId=" + activityId + "]");
    }
}
