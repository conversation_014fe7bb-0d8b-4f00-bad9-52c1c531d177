/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.rest;

import bnext.exception.MakePersistentException;
import com.sun.star.auth.InvalidArgumentException;
import java.util.List;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.activity.entity.ActivityResolution;
import qms.framework.rest.SecurityUtils;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 * <AUTHOR>
 */
@Lazy
@RestController
@RequestMapping("rest/activity-resolution")
public class ActivityResolutionController extends ActivityResolutionBaseController {

    private static final String HAS_SAVE_ACCESS = ""
            + " hasAuthority('IS_ADMIN')"
            + " or ("
            + " hasAuthority('ADMON_ACCESOS')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
            + " )"
            + " or ("
            + " hasAuthority('CATALOGS_ACTIVITIES')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
            + " )";


    @PostMapping("data-source/list")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GridInfo list(@RequestBody GridFilter filter) {
        return super.list(filter);
    }


    @GetMapping("data-source/available")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List available() {
        return super.available();
    }


    @GetMapping({"load/{activityResolutionId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity<ActivityResolution> load(
            @PathVariable(value = "activityResolutionId") Long activityResolutionId
    ) {
        return super.load(activityResolutionId);
    }

    @GetMapping({"toggle-status/{activityResolutionId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> toogleStatus(
            @PathVariable(value = "activityResolutionId") Long activityResolutionId
    ) {
        return super.toogleStatus(activityResolutionId, SecurityUtils.getLoggedUser());
    }

    @PostMapping({"save"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity save(
            @RequestBody ActivityResolution entity
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return super.save(entity, SecurityUtils.getLoggedUser());
    }

}
