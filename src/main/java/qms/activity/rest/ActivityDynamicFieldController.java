package qms.activity.rest;

import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import bnext.exception.MakePersistentException;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.sun.star.auth.InvalidArgumentException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.dto.ActivityInfoDTO;
import qms.activity.dto.ModifiedFieldRequestDTO;
import qms.activity.dto.ModifiedFieldResponseDTO;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityHistory;
import qms.activity.entity.ActivityType;
import qms.activity.entity.ActivityWorkflowType;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicFieldHandler;
import qms.custom.core.PersistableDynamicEntity;
import qms.custom.dto.DynamicFieldInfoDTO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldOrderDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.entity.DynamicField;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.QMSException;

/**
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("rest/activities-custom")
public class ActivityDynamicFieldController {

    private final DynamicFieldHandler handler;

    public static final String WORKFLOW_CONDITION = " "
            + " EXISTS ("
            + " SELECT wt.id.activityTypeId"
            + " FROM " + ActivityWorkflowType.class.getCanonicalName() + " wt"
            + " WHERE wt.id.activityTypeId = t.id"
            + " AND wt.id.activityWorkflowId = :workflowId"
            + " )";

    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO dao;

    public ActivityDynamicFieldController() {
        this.handler = new DynamicFieldHandler(
                ActivityType.class /*dynamicEntity*/, Activity.class /*custom*/, Utilities.getBean(IDynamicFieldDAO.class)
        );
    }

    @PostMapping("{workflowId}/fields/by/id/{entityTypeId}")
    public ResponseEntity<DynamicFieldsDTO> getDynamicFields(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "entityTypeId", required = true) Long entityTypeId
    ) throws QMSException {
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        final DynamicFieldsDTO result = this.handler.getDynamicFields(workflowCondtion, params, SecurityUtils.getLoggedUserId(), entityTypeId);
        final List<DynamicFieldOrderDTO> order = dao.getDynamicFielsOrderByActivityType(workflowId, entityTypeId);
        return ResponseEntity.ok(
                addOrderDynamicFields(result, order)
        );
    }

    @PostMapping("{workflowId}/fields/by/name/{activityId}")
    public ResponseEntity<DynamicFieldsDTO> getDynamicFieldsByTableName(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @RequestBody String dynamicTableName,
            final @PathVariable(name = "activityId", required = true) Long activityId
    ) {
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        final DynamicFieldsDTO result = this.handler.getDynamicFieldsByTableName(
                dynamicTableName,
                workflowCondtion,
                params,
                SecurityUtils.getLoggedUserId()
        );
        final List<DynamicFieldOrderDTO> order = dao.getDynamicFieldsOrder(activityId, dynamicTableName);

        return ResponseEntity.ok(
                addOrderDynamicFields(result, order)
        );
    }

    @PostMapping("{workflowId}/fields/by/code")
    public ResponseEntity<DynamicFieldsDTO> getDynamicFieldsByCode(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @RequestBody String entityTypeCode
    ) throws QMSException {
        return ResponseEntity.ok(
                this.handler.getDynamicFieldsByCode(entityTypeCode, SecurityUtils.getLoggedUserId())
        );
    }

    @PostMapping("{workflowId}/fields/list")
    public ResponseEntity<DynamicFieldsDTO> getAllDynamicFields(
            final @PathVariable(value = "workflowId", required = true) Long workflowId
    ) {
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        final DynamicFieldsDTO fields = this.handler.getAllDynamicFields(false, workflowCondtion, params);
        return ResponseEntity.ok(fields);
    }


    @GetMapping({"{workflowId}/fields/grid/column/list", "{workflowId}/fields/grid/column/list/{entityTypeIds}"})
    public ResponseEntity<DynamicFieldsDTO> getDynamicEntitySearchColumns(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "entityTypeIds", required = false) Long[] entityTypeIds
    ) {
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        if (entityTypeIds != null && entityTypeIds.length > 0) {
            final DynamicFieldsDTO result = this.handler.getDynamicFields(
                    workflowCondtion,
                    params,
                    SecurityUtils.getLoggedUserId(),
                    Arrays.asList(entityTypeIds)
            );
            return ResponseEntity.ok(result);
        } else {
            final DynamicFieldsDTO result = this.handler.getDynamicEntitySearchColumns(
                    workflowCondtion,
                    params,
                    SecurityUtils.getLoggedUserId()
            );
            return ResponseEntity.ok(result);
        }
    }


    @GetMapping("{workflowId}/fields/grid/catalog/list")
    public ResponseEntity<List<ITextHasValue>> getDynamicEntityCatalog(
            final @PathVariable(value = "workflowId", required = true) Long workflowId
    ) {
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        return ResponseEntity.ok(
                this.handler.getDynamicEntityCatalog(workflowCondtion, params)
        );
    }


    @GetMapping("{workflowId}/fields/get/data/{activityId}")
    public ResponseEntity<PersistableDynamicEntity> getRestDynamicFieldData(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "activityId", required = true) Long activityId
    ) throws QMSException {
        return ResponseEntity.ok(
                this.handler.getDynamicFieldData(workflowId, activityId)
        );
    }


    @GetMapping("{workflowId}/fields/get/data/new/{activityId}")
    public ResponseEntity<Map> getSafeDynamicFieldData(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "activityId", required = true) Long activityId
    ) throws QMSException {
        Long activityTypeId = dao().HQL_findLong(""
                        + " SELECT a.typeId"
                        + " FROM " + Activity.class.getCanonicalName() + " a "
                        + " WHERE a.id = :id",
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        return ResponseEntity.ok(
                this.handler.getDynamicFieldData(
                        activityId,
                        activityTypeId,
                        DynamicFieldHandler.RECALCULATE_TABLE_NAME,
                        workflowCondtion,
                        params,
                        SecurityUtils.getLoggedUserId()
                )
        );
    }

    /**
     * Obtiene los valores dentro del SELECT para campos tipo "catalog"
     *
     * @param dynamicFieldId
     * @return
     */

    @GetMapping("{workflowId}/fields/data/{dynamicFieldId}")
    public ResponseEntity<Map> getDynamicFieldValues(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicFieldId", required = true) Long dynamicFieldId
    ) {
        return ResponseEntity.ok(
                this.handler.getDynamicFieldValues(dynamicFieldId)
        );
    }

    @PostMapping("{workflowId}/fields/reference/refresh/{activityId}/{dynamicTableDataId}")
    public ResponseEntity<Map> refresh(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody String dynamicTableName
    ) {
        return ResponseEntity.ok(
                this.handler.refresh(dynamicTableDataId, activityId, dynamicTableName)
        );
    }

    /**
     * Inserta un valor de tabla dinamica
     *
     * @param dynamicTableDataId: Si vale -1 indica un nuevo renglon, caso contrario un UPDATE
     * @param data
     * @param dynamicTableName:   Nombre de la tabla de los campos dinámicos
     * @return ID insertado
     * TABLA donde se insertó
     * @throws NoSuchAlgorithmException
     * @throws InvalidArgumentException
     */
    @PostMapping("{workflowId}/persist/{dynamicTableDataId}")
    public ResponseEntity<DynamicFieldInsertDTO> persist(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody LinkedHashMap<String, Object> data
    ) throws NoSuchAlgorithmException, InvalidArgumentException, QMSException {
        String dynamicTableName = (String) data.remove("dynamicTableName");
        return ResponseEntity.ok(
                this.handler.persist(dynamicTableDataId, data, dynamicTableName)
        );
    }

    /**
     * Funciona igual que el metodo "persist" pero recibe parametros y regresa
     * resultados según lso requerimientos del componente de front "FieldDisplayComponent"
     *
     * @param dynamicTableName
     * @param activityId
     * @param fieldName
     * @param dynamicTableDataId
     * @param value
     * @return
     * @throws InvalidArgumentException
     * @throws MakePersistentException
     */
    @PostMapping("{workflowId}/save-field/{dynamicTableName}/{activityId}/{fieldName}/{dynamicTableDataId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveFieldString(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicTableName", required = true) String dynamicTableName,
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "fieldName", required = true) String fieldName,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody ModifiedFieldRequestDTO<String> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return this.saveField(workflowId, dynamicTableName, null, activityId, fieldName, dynamicTableDataId, value);
    }

    @PostMapping("{workflowId}/save-field/{dynamicTableName}/{dynamicTableNameLatest}/{activityId}/{fieldName}/{dynamicTableDataId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveFieldString(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicTableName", required = true) String dynamicTableName,
            final @PathVariable(value = "dynamicTableNameLatest", required = true) String dynamicTableNameLatest,
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "fieldName", required = true) String fieldName,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody ModifiedFieldRequestDTO<String> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return this.saveField(workflowId, dynamicTableName, dynamicTableNameLatest, activityId, fieldName, dynamicTableDataId, value);
    }

    /**
     * Funciona igual que el metodo "saveFieldString" pero parsea el arreglo de strings
     * a un string separado por comas
     */
    @PostMapping("{workflowId}/save-field-array/{dynamicTableName}/{activityId}/{fieldName}/{dynamicTableDataId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveFieldArray(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicTableName", required = true) String dynamicTableName,
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "fieldName", required = true) String fieldName,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody ModifiedFieldRequestDTO<String[]> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return _saveFieldArray(workflowId, dynamicTableName, null, activityId, fieldName, dynamicTableDataId, value);
    }

    @PostMapping("{workflowId}/save-field-array/{dynamicTableName}/{dynamicTableNameLatest}/{activityId}/{fieldName}/{dynamicTableDataId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveFieldArray(
            final @PathVariable(value = "workflowId", required = true) Long workflowId,
            final @PathVariable(value = "dynamicTableName", required = true) String dynamicTableName,
            final @PathVariable(value = "dynamicTableNameLatest", required = true) String dynamicTableNameLatest,
            final @PathVariable(value = "activityId", required = true) Long activityId,
            final @PathVariable(value = "fieldName", required = true) String fieldName,
            final @PathVariable(value = "dynamicTableDataId", required = true) Long dynamicTableDataId,
            final @RequestBody ModifiedFieldRequestDTO<String[]> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return _saveFieldArray(workflowId, dynamicTableName, dynamicTableNameLatest, activityId, fieldName, dynamicTableDataId, value);
    }

    private ResponseEntity<ModifiedFieldResponseDTO> _saveFieldArray(
            Long workflowId,
            String dynamicTableName,
            String dynamicTableNameLatest,
            Long activityId,
            String fieldName,
            Long dynamicTableDataId,
            ModifiedFieldRequestDTO<String[]> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        final ModifiedFieldRequestDTO strValue = new ModifiedFieldRequestDTO();
        if (value.getOldValue() != null) {
            strValue.setOldValue(StringUtils.join(value.getOldValue(), ", "));
        }
        if (value.getValue() != null) {
            strValue.setValue(StringUtils.join(value.getValue(), ", "));
        }
        strValue.setComment(value.getComment());
        strValue.setDeleteValue(value.isDeleteValue());
        return this.saveField(workflowId, dynamicTableName, dynamicTableNameLatest, activityId, fieldName, dynamicTableDataId, strValue);
    }

    private ResponseEntity<ModifiedFieldResponseDTO> saveField(
            Long workflowId,
            String dynamicTableName,
            String dynamicTableNameLatest,
            Long activityId,
            String fieldName,
            Long dynamicTableDataId,
            ModifiedFieldRequestDTO<String> value
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        // Datos a guardar
        Map data = new HashMap();

        // Se valida si se debe migrar el registro a la tabla mas reciente
        final boolean migrateToLatestTable = dynamicTableNameLatest != null && !dynamicTableNameLatest.equals(dynamicTableName);
        if (migrateToLatestTable) {
            final DynamicFieldInfoDTO sourceInfo = this.handler.getDynamicFieldInfo(
                    activityId, null, dynamicTableName, null, new HashMap<String, Object>(), SecurityUtils.getLoggedUserId()
            );
            final DynamicFieldsDTO destinyInfo = this.handler.getDynamicFieldsByTableName(
                    dynamicTableNameLatest, null, new HashMap<String, Object>()
            );
            dynamicTableName = dynamicTableNameLatest;
            dynamicTableDataId = -1L;
            // Se llenarán los campos en la nueva tabla
            sourceInfo.getFields().getDynamicFields().forEach(field -> {
                if (destinyInfo.getDynamicFields().stream().noneMatch(f -> f.getName().equals(field.getName()))) {
                    // Solo se migran campos que existan en la nueva tabla
                    return;
                }
                data.put(field.getName(), sourceInfo.getData().get(field.getName()));
            });
            data.put("activity_id1", activityId);
        }

        // Nuevo dato a guardarse recién modificado
        data.put(fieldName, value.getValue());

        // Se continúa con el guardado del cambio
        final ModifiedFieldResponseDTO response = new ModifiedFieldResponseDTO();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        DynamicFieldInsertDTO r;
        response.setUnnecessary(Objects.equals(value.getOldValue(), value.getValue()));
        if (response.isUnnecessary()) {
            r = new DynamicFieldInsertDTO();
        } else {
            if (DynamicField.DYNAMIC_FIELD_TYPE.HTML.toString().toUpperCase().equals(value.getDynamicFieldType())
                    || DynamicField.DYNAMIC_FIELD_TYPE.MARKDOWN.toString().toUpperCase().equals(value.getDynamicFieldType())) {
                Gson gson = new Gson();
                Map htmlValue = gson.fromJson(value.getValue(), Map.class);
                data.put(fieldName, htmlValue.get("text"));
                data.put(fieldName + "Html", htmlValue.get("html"));
            }
            r = this.handler.persist(dynamicTableDataId, data, dynamicTableName);
        }
        if (r.isSuccess()) {
            final Map<String, Object> params = new HashMap<>();
            final String workflowCondtion;
            if (workflowId != null && workflowId > 0) {
                params.put("workflowId", workflowId);
                workflowCondtion = WORKFLOW_CONDITION;
            } else {
                workflowCondtion = null;
            }
            DynamicFieldsDTO dynamicFields = this.handler.getDynamicFieldsByTableName(dynamicTableName, null, workflowCondtion, params, loggedUser.getId());
            ActivityInfoDTO info = dao.getActivityInfoFromId(activityId, loggedUser);
            response.setModified(true);
            response.setHistory(new ArrayList<>(1));
            response.setDynamicTableName(r.getTableName());
            response.setDynamicTableNameId(r.getId());
            if (migrateToLatestTable) {
                // Se actualiza el registro de la actividad para que apunte a la nueva tabla "
                dao.HQL_updateByQuery(" "
                                + " UPDATE " + Activity.class.getCanonicalName() + " a "
                                + " SET "
                                + " a.dynamicTableName = :dynamicTableName"
                                + ",a.dynamicTableNameId = :dynamicTableNameId"
                                + " WHERE a.id = :id",
                        ImmutableMap.of(
                                "dynamicTableNameId", r.getId(),
                                "dynamicTableName", dynamicTableName,
                                "id", activityId
                        ), true, CacheRegion.ACTIVITY, 0
                );
            }
            if (DynamicField.DYNAMIC_FIELD_TYPE.MARKDOWN.toString().toUpperCase().equals(value.getDynamicFieldType())
                    || DynamicField.DYNAMIC_FIELD_TYPE.HTML.toString().toUpperCase().equals(value.getDynamicFieldType())) {
                Gson gson = new Gson();
                Map<String, Object> currentValue = gson.fromJson(value.getValue(), Map.class);
                Map<String, Object> oldValue;
                if (value.getOldValue() == null || !value.getOldValue().startsWith("{")) {
                    String oldValueStr = value.getOldValue() == null ? "-Empty-" : value.getOldValue();
                    oldValue = ImmutableMap.of(
                            "text", oldValueStr,
                            "html", oldValueStr
                    );
                } else {
                    oldValue = gson.fromJson(value.getOldValue(), Map.class);
                }
                if (currentValue != null && oldValue != null && currentValue.get("text") != null && oldValue.get("text") != null) {
                    value.setValue(currentValue.get("text").toString());
                    value.setOldValue(oldValue.get("text").toString());
                }
            }
            response.getHistory().add(dao.saveHistoryBySystem(
                    info,
                    dao.getTag("changeDynamicField")
                            .replace("{current}", value.getValue())
                            .replace("{previous}", value.getOldValue() != null ? value.getOldValue() : "")
                            .replace("{comment}", value.getComment())
                            .replace("{fieldName}", dynamicFields.getDynamicFields().stream().filter(f -> fieldName.equals(f.getName())).collect(Collectors.toList()).get(0).getLabel()),
                    value.getComment(),
                    loggedUser,
                    ActivityHistory.HISTORY_TYPE.FIELD_CHANGE.getValue()
            ));
            dao.fillFromId(info, response, loggedUser);
        }
        response.setPreviousValue(value.getOldValue());
        response.setModifiedValue(value.getValue());
        response.setCascade(Utilities.EMPTY_LIST);
        response.setFieldName(fieldName);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private IDynamicFieldDAO dao() {
        return this.handler.getDynamicFieldDAO();
    }

    public static DynamicFieldsDTO addOrderDynamicFields(DynamicFieldsDTO result, List<DynamicFieldOrderDTO> order) {
        if (result.getDynamicFields() != null && !result.getDynamicFields().isEmpty() && order != null && !order.isEmpty()) {
            result.getDynamicFields().forEach(dynamicField -> {
                DynamicFieldOrderDTO df = order.stream().filter(p -> p.getId().equals(dynamicField.getId())).findFirst().orElse(null);
                if (df != null) {
                    dynamicField.setOrder(df.getOrder());
                } else {
                    dynamicField.setOrder(Long.MAX_VALUE);
                }
            });
            Collections.sort(result.getDynamicFields());
        }
        return result;
    }
}
