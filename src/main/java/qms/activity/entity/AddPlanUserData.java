package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "add_plan_user_data")
public class AddPlanUserData extends CompositeStandardEntity<AddPlanUserDataPK>
        implements IAuditableCompositeEntity<AddPlanUserDataPK>, ILinkedComposityGrid<AddPlanUserDataPK>, Serializable {

    private static final long serialVersionUID = 1L;
    
    private AddPlanUserDataPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long stateId;
    private Long activityWorkflowId;
    private Boolean isTeamScoped;

    public AddPlanUserData() {
    }

    public AddPlanUserData(AddPlanUserDataPK id) {
        this.id = id;
    }

    public AddPlanUserData(String module, Long activityWorkflowId, Long userId, Integer tabIndex) {
        if (activityWorkflowId == null) { 
            this.id = new AddPlanUserDataPK(module, userId, tabIndex);
        } else {
            this.id = new AddPlanUserDataPK(module + "_" + activityWorkflowId, userId, tabIndex);
        }
        this.activityWorkflowId = activityWorkflowId;
    }

    @Override
    public AddPlanUserDataPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public AddPlanUserDataPK getId() {
        return id;
    }

    @Override
    public void setId(AddPlanUserDataPK id) {
        this.id = id;
    }

    @Column(name = "is_team_scoped")
    @Type(type = "numeric_boolean")
    public Boolean getIsTeamScoped() {
        return isTeamScoped;
    }

    public void setIsTeamScoped(Boolean isTeamScoped) {
        this.isTeamScoped = isTeamScoped;
    }


    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "activity_workflow_id")
    public Long getActivityWorkflowId() {
        return activityWorkflowId;
    }

    public void setActivityWorkflowId(Long activityWorkflowId) {
        if (activityWorkflowId != null && activityWorkflowId == 0) {
            activityWorkflowId = null;
        }
        this.activityWorkflowId = activityWorkflowId;
    }
    @Column(name = "state_id")
    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }
    
    
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AddPlanUserData other = (AddPlanUserData) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.activity.entity.AddPlanUserData{" + "id=" + id + '}';
    }

}
