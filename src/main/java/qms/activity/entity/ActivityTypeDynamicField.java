package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import qms.framework.util.CacheConstants;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.custom.entity.DynamicField;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "activity_type_dynamic_field")
public class ActivityTypeDynamicField extends CompositeStandardEntity<ActivityTypeDynamicFieldPK>
        implements Serializable, IAuditableCompositeEntity<ActivityTypeDynamicFieldPK>, ILinkedComposityGrid<ActivityTypeDynamicFieldPK> {

    private static final long serialVersionUID = 1L;
    
    private ActivityTypeDynamicFieldPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long orderField;
    private DynamicField dynamicField;
    private ActivityType activityType;

    public ActivityTypeDynamicField() {
    }

    public ActivityTypeDynamicField(ActivityTypeDynamicFieldPK activityTypeDynamicFieldPK) {
        this.id = activityTypeDynamicFieldPK;
    }

    public ActivityTypeDynamicField(Long dynamicFieldId, Long documentTypeId) {
        this.id = new ActivityTypeDynamicFieldPK(dynamicFieldId, documentTypeId);
    }

    @Override
    public ActivityTypeDynamicFieldPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ActivityTypeDynamicFieldPK getId() {
        return id;
    }

    @Override
    public void setId(ActivityTypeDynamicFieldPK documentTypeDynamicFieldPK) {
        this.id = documentTypeDynamicFieldPK;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Column(name = "dynamic_field_order")
    public Long getOrderField() {
       return orderField;
    }

    public void setOrderField(Long orderField) {
         this.orderField = orderField;
    }

    @JoinColumn(name = "dynamic_field_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public DynamicField getDynamicField() {
        return dynamicField;
    }

    public void setDynamicField(DynamicField dynamicField) {
        this.dynamicField = dynamicField;
    }
    
    
    @JoinColumn(name = "activity_type_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public ActivityType getActivityType() {
        return activityType;
    }

    public void setActivityType(ActivityType activityType) {
        this.activityType = activityType;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityTypeDynamicField other = (ActivityTypeDynamicField) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityTypeDynamicField{" + "id=" + id + '}';
    }

}
