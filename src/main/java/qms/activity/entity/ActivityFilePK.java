package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;
import qms.util.interfaces.ILinkedFilePK;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityFilePK implements Serializable, ILinkedFilePK {

    private Long activityId;
    private Long fileId;

    public ActivityFilePK(Long activityId, Long fileId) {
        this.activityId = activityId;
        this.fileId = fileId;
    }

    public ActivityFilePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "file_id")
    @Override
    public Long getFileId() {
        return fileId;
    }

    @Override
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.activityId);
        hash = 97 * hash + Objects.hashCode(this.fileId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityFilePK other = (ActivityFilePK) obj;
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        if (!Objects.equals(this.fileId, other.fileId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityFilePK{" + "activityId=" + activityId + ", fileId=" + fileId + '}';
    }

}
