/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import qms.framework.util.CacheConstants;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "activity_type_resolution")
public class ActivityTypeResolution extends CompositeStandardEntity<ActivityTypeResolutionPK>
        implements Serializable, IAuditableCompositeEntity<ActivityTypeResolutionPK>, ILinkedComposityGrid<ActivityTypeResolutionPK> {
    
    private static final long serialVersionUID = 1L;
    
    private ActivityTypeResolutionPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public ActivityTypeResolution() {
    }

    public ActivityTypeResolution(ActivityTypeResolutionPK activityTypeDynamicFieldPK) {
        this.id = activityTypeDynamicFieldPK;
    }

    public ActivityTypeResolution(Long activityResolutionId, Long activityTypeId) {
        this.id = new ActivityTypeResolutionPK(activityResolutionId, activityTypeId);
    }

    @Override
    public ActivityTypeResolutionPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ActivityTypeResolutionPK getId() {
        return id;
    }

    @Override
    public void setId(ActivityTypeResolutionPK documentTypeDynamicFieldPK) {
        this.id = documentTypeDynamicFieldPK;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityTypeResolution other = (ActivityTypeResolution) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityTypeResolution{" + "id=" + id + '}';
    }

}
