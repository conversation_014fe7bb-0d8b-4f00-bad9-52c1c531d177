/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import jakarta.persistence.Convert;
import qms.framework.util.CacheConstants;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "activity_relationship")
public class ActivityRelationship extends CompositeStandardEntity<ActivityRelationshipPK> 
        implements Serializable, IAuditableCompositeEntity<ActivityRelationshipPK>, ILinkedComposityGrid<ActivityRelationshipPK> {
    
    private static final long serialVersionUID = 1L;
    
    public static enum RELATION_TYPE {
        SIBLING(1),
        CHILD(2),
        PARENT(3);
        private final Integer value;
        private RELATION_TYPE(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return this.value;
        }
    }

    private ActivityRelationshipPK id;
    private String comment;
    private Integer relationType;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Boolean deleted;

    public ActivityRelationship() {
    }
    
    public ActivityRelationship(ActivityRelationshipPK id) {
        this.id = id;
    }

    public ActivityRelationship(Long activityId, Long relationActivityId) {
        this.id = new ActivityRelationshipPK(activityId, relationActivityId);
    }

    public ActivityRelationship(Long activityId, Long relationActivityId, String comment, Integer relationType) {
        this.id = new ActivityRelationshipPK(activityId, relationActivityId);
        this.comment = comment;
        this.relationType = relationType;
    }

    @Override
    @EmbeddedId
    public ActivityRelationshipPK getId() {
        return id;
    }

    @Override
    public void setId(ActivityRelationshipPK id) {
        this.id = id;
    }
    
    @Override
    public ActivityRelationshipPK identifuerValue() {
        return id;
    }
    
    @Column(name = "comment")
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
    
    @Column(name = "relation_type")
    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "deleted")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityRelationship other = (ActivityRelationship) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "ActivityRelationship{" + "id=" + id + ", deleted=" + deleted + '}';
    }
    
}
