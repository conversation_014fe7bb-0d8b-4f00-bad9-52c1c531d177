package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class ActivityTypeCategoryPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long activityCategoryId;
    private Long activityTypeId;

    public ActivityTypeCategoryPK(Long activityCategoryId, Long activityTypeId) {
        this.activityCategoryId = activityCategoryId;
        this.activityTypeId = activityTypeId;
    }

    public ActivityTypeCategoryPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_category_id")
    public Long getActivityCategoryId() {
        return activityCategoryId;
    }

    public void setActivityCategoryId(Long activityCategoryId) {
        this.activityCategoryId = activityCategoryId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_type_id")
    public Long getActivityTypeId() {
        return activityTypeId;
    }

    public void setActivityTypeId(Long activityTypeId) {
        this.activityTypeId = activityTypeId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.activityCategoryId);
        hash = 97 * hash + Objects.hashCode(this.activityTypeId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityTypeCategoryPK other = (ActivityTypeCategoryPK) obj;
        if (!Objects.equals(this.activityCategoryId, other.activityCategoryId)) {
            return false;
        }
        return Objects.equals(this.activityTypeId, other.activityTypeId);
    }

    @Override
    public String toString() {
        return "ActivityTypeCategoryPK{" + "activityCategoryId=" + activityCategoryId + ", activityTypeId=" + activityTypeId + '}';
    }
}
