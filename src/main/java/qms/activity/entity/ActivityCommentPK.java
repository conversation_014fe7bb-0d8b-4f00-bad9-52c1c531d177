package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;
import qms.util.interfaces.ILinkedCommentPK;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityCommentPK implements Serializable, ILinkedCommentPK {

    private Long activityId;
    private Long commentId;

    public ActivityCommentPK(Long activityId, Long commentId) {
        this.activityId = activityId;
        this.commentId = commentId;
    }

    public ActivityCommentPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "comment_id")
    @Override
    public Long getCommentId() {
        return commentId;
    }

    @Override
    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.activityId);
        hash = 97 * hash + Objects.hashCode(this.commentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityCommentPK other = (ActivityCommentPK) obj;
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        if (!Objects.equals(this.commentId, other.commentId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityCommentPK{" + "activityId=" + activityId + ", commentId=" + commentId + '}';
    }

}
