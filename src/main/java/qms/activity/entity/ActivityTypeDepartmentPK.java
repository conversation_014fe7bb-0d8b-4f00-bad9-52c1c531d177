/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityTypeDepartmentPK implements Serializable {
    
    private static final long serialVersionUID = 1L;
     
    private Long businnesUnitDepartmentId;
    private Long activityTypeId;

    public ActivityTypeDepartmentPK(Long businnesUnitDepartmentId, Long activityTypeId) {
        this.businnesUnitDepartmentId = businnesUnitDepartmentId;
        this.activityTypeId = activityTypeId;
    }

    public ActivityTypeDepartmentPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "business_unit_department_id")
    public Long getBusinnesUnitDepartmentId() {
        return businnesUnitDepartmentId;
    }

    public void setBusinnesUnitDepartmentId(Long businnesUnitDepartmentId) {
        this.businnesUnitDepartmentId = businnesUnitDepartmentId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_type_id")
    public Long getActivityTypeId() {
        return activityTypeId;
    }

    public void setActivityTypeId(Long activityTypeId) {
        this.activityTypeId = activityTypeId;
    }

}
