package qms.activity.pending.imp;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.PendingConditional;
import ape.pending.entities.PendingRecord;
import ape.pending.util.ApeConstants;
import bnext.exception.ApeConfigurationException;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Nonnull;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityType;
import qms.activity.pending.ActivityPending;
import qms.activity.pending.ActivityPendingOperations;
import qms.activity.util.CommitmentTask;
import qms.audit.entity.AuditIndividualActivity;
import qms.finding.entity.FindingActivity;
import qms.form.entity.OutstandingSurveysActivity;
import qms.framework.util.TimeUnit;
import qms.meeting.entity.MeetingActivity;

/**
 *
 * <AUTHOR>
 */
public class ToVerifyDelayed extends ActivityPendingOperations {

    private static final Map<String, Object> DELAYED_FIELD = new HashMap<String, Object>() {{
        put("delayedByImplementer", 1);
    }};
    private final String TO_VERIFY_DELAYED = ""
        + " FROM " + Activity.class.getCanonicalName() + " act "
        + " JOIN act.type ty"
        + " WHERE"
            + " act.commitmentTask  = " + CommitmentTask.VERIFICATION.getValue()
            + " AND act.followUpImplementationDelay = true"
            + " AND act.apply = 1 "
            + " AND act.childCount IS NULL"
            + " AND act.implementerOwnerId IS NOT NULL"
            + " AND ("
                + " act.fillType <> " +  ActivityType.FILL_TYPE.FILL_FORM.getValue()
                + " OR act.formFillIsClosed = 1"
                + ")"
            + " AND act.status NOT IN ("
                + Activity.STATUS.VERIFIED.getValue()
                + "," + Activity.STATUS.NOT_APPLY_VERIFIED.getValue()
                + "," + Activity.STATUS.UNDONE_VERIFIED.getValue()
            + " ) "
            + " AND (("
                    // actividades periodicas abiertas empalmadas
                    + " SELECT count(i) "
                    + " FROM act.plannedImplementations i "
                    + " WHERE act.status IN ("
                        + Activity.STATUS.REPORTED.getValue()
                        + "," + Activity.STATUS.IN_PROCESS.getValue()
                        + "," + Activity.STATUS.RETURNED.getValue()
                        + "," + Activity.STATUS.NOT_APPLY.getValue()
                        + "," + Activity.STATUS.UNDONE.getValue()
                    + " ) "
                    + " AND i.apply = 1 "
                    + " AND i.progress != 100 "
                    + " AND i.deleted = " + Activity.IS_NOT_DELETED
                    + " AND (("
                        // se empalman cuando la fecha de implementación de un solida para ambas ya llegó
                        + " i.anticipationAttendDays IS NULL"
                        + " AND trunc(i.commitmentDate) <= " + ApeConstants.CURRENT_DATE_NO_TIME
                    + " ) OR ("
                        // se empalman cuando la fecha de implementación de un solida para ambas ya llegó
                        + " i.anticipationAttendDays IS NOT NULL"
                        + " AND i.reminder <= " + ApeConstants.CURRENT_DATE_NO_TIME
                    + " ))"
                + " ) > 1 " // <--- debe haber al menos 2
                + " OR ( "
                    // actividades cuyo tiempo configurado de "tiempo maximo abierto" fue excedido
                    + " SELECT count(i) "
                    + " FROM act.plannedImplementations i "
                    + " JOIN i.type it "
                    + " WHERE act.status IN ("
                        + Activity.STATUS.REPORTED.getValue()
                        + "," + Activity.STATUS.IN_PROCESS.getValue()
                        + "," + Activity.STATUS.RETURNED.getValue()
                        + "," + Activity.STATUS.NOT_APPLY.getValue()
                        + "," + Activity.STATUS.UNDONE.getValue()
                    + " ) "
                    + " AND i.apply = 1 "
                    + " AND i.progress != 100 "
                    + " AND i.deleted = " + Activity.IS_NOT_DELETED
                    + " AND (("
                        // tiempo máximo abierto excedido para actividades de un solo día
                        + " i.anticipationAttendDays IS NULL"
                        + " AND (("
                            + " it.maxOpenTimeUnit = '" + TimeUnit.HOUR + "'"
                            + " AND DATEDIFF(hour, trunc(i.commitmentDate), getdate()) > it.maxOpenTime"
                        + " ) OR ("
                            + " it.maxOpenTimeUnit = '" + TimeUnit.DAY + "'"
                            + " AND DATEDIFF(day, trunc(i.commitmentDate), getdate()) > it.maxOpenTime"
                        + " ))"
                    + " )) OR ("
                        // tiempo máximo abierto excedido para actividades de mas de un día
                        + " i.anticipationAttendDays IS NOT NULL"
                        + " AND (("
                            + " it.maxOpenTimeUnit = '" + TimeUnit.HOUR + "'"
                            + " AND DATEDIFF(hour, i.reminder, getdate()) > it.maxOpenTime"
                        + " ) OR ("
                            + " it.maxOpenTimeUnit = '" + TimeUnit.DAY + "'"
                            + " AND DATEDIFF(day, i.reminder, getdate()) > it.maxOpenTime"
                        + " ))"
                    + " )"
                + " ) > 0 "
                + " OR (( "
                    // actividades cuya fecha de verificación ya pasó y no han sido verificadas
                    + " SELECT count(i) "
                    + " FROM act.plannedImplementations i "
                    + " WHERE"
                        + " act.status IN ("
                            + Activity.STATUS.REPORTED.getValue()
                            + "," + Activity.STATUS.IN_PROCESS.getValue()
                            + "," + Activity.STATUS.RETURNED.getValue()
                            + "," + Activity.STATUS.NOT_APPLY.getValue()
                            + "," + Activity.STATUS.UNDONE.getValue()
                        + " ) "
                        + " AND i.apply = 1 "
                        + " AND i.progress != 100 "
                        + " AND i.deleted = " + Activity.IS_NOT_DELETED
                    + " ) > 0"
                    // verificación con fechas atrasada
                    + " AND trunc(act.commitmentDate) < " + ApeConstants.CURRENT_DATE_NO_TIME
                + " ) "
            + " )"
            + " AND ty.module = ':module' "
            + " AND act.deleted = " + Activity.IS_NOT_DELETED
        + " ";

    private mx.bnext.access.Module _module;

    public ToVerifyDelayed(mx.bnext.access.Module module, IUntypedDAO dao) {
        super(dao);
        setModuleConfiguration(module);
        setBaseAlias(ALIAS);
        setScope(PendingRecord.Scope.USER);
        setOwnerField(ActivityPending.VERIFIER);
        setModuleKey(module.getKey());
        setOwnerFieldFilter(ActivityPending.FILTER_BY_VERIFIER);
        setDependencies(
            ToComplete.class,
            ToVerifyNotApply.class
        );
        setDeadline(ActivityPending.FIELD_DEADLINE_DATE);
        setNoticeDate(ActivityPending.FIELD_NOTICE_DATE);
        setReminder(ActivityPending.FIELD_REMINDER_DATE);
    }

    private void setModuleConfiguration(mx.bnext.access.Module module) {
        setQuery(TO_VERIFY_DELAYED.replaceAll(":module", module.getKey()));
        switch (module) {
            case ACTION:
                setPendingType(getType(APE.FINDING_ACTIVITY_TO_VERIFY_DELAYED));
                setBase(FindingActivity.class);
                setOperationType(ApeOperationType.SOFT);
                setSummaryTemplate("${findingCode}, ${activityDescription}");
                setModuleSummaryFields(new ColumnDTO("code", "findingCode"));
                setBaseSummaryFields(new ColumnDTO("description", "activityDescription"));
                break;
            case AUDIT:
                setPendingType(getType(APE.AUDIT_ACTIVITY_TO_VERIFY_DELAYED));
                setBase(AuditIndividualActivity.class);
                setOperationType(ApeOperationType.SOFT);
                setSummaryTemplate("${auditIndividualCode}, ${activityDescription}");
                setModuleSummaryFields(new ColumnDTO("code", "auditIndividualCode"));
                setBaseSummaryFields(new ColumnDTO("description", "activityDescription"));
                break;
            case FORMULARIE:
                setPendingType(getType(APE.FORM_ACTIVITY_TO_VERIFY_DELAYED));
                setBase(OutstandingSurveysActivity.class);
                setOperationType(ApeOperationType.SOFT);
                setSummaryTemplate("${activityCode}, ${activityDescription}");
                setBaseSummaryFields(BASE_SUMMARY_FIELDS);
                break;
            case PLANNER:
                getLogger().debug("ToVerifyDelayed not required for tasks in Planner module.");
                clearDependencies();
                break;
            case ACTIVITY:
                setPendingType(getType(APE.ACTIVITY_TO_VERIFY_DELAYED));
                setBase(Activity.class);
                setOperationType(ApeOperationType.STRONG);
                setSummaryTemplate("${activityCode}, ${activityDescription}");
                setBaseSummaryFields(BASE_SUMMARY_FIELDS);
                break;
            case MEETING:
                setPendingType(getType(APE.MEETING_ACTIVITY_TO_VERIFY_DELAYED));
                setOperationType(ApeOperationType.SOFT);
                setBase(MeetingActivity.class);
                setSummaryTemplate("${meetingCode}, ${activityDescription}");
                setModuleSummaryFields(new ColumnDTO("code", "meetingCode"));
                setBaseSummaryFields(new ColumnDTO("description", "activityDescription"));
                break;
            default:
                throw new ApeConfigurationException("APE not implemented for module " + module.getKey());
        }
        _module = module;
    }

    @Override
    public mx.bnext.access.Module getImplementedModule() {
        return _module;
    }


    @Override
    public void dailyCheck(Class trigger, @Nonnull ILoggedUser loggedUser) {
        super.dailyCheck(trigger, loggedUser);
        updateActive(Activity.class.getCanonicalName(), DELAYED_FIELD);
    }

    @Override
    public void execute(Long recordId, Class trigger, @Nonnull ISecurityUser loggedUser) {
        super.execute(recordId, trigger, loggedUser);
        updateActive(Activity.class.getCanonicalName(), DELAYED_FIELD, recordId);
    }

    @Override
    public void execute(Long recordId, PendingConditional conditional, Class trigger, @Nonnull ISecurityUser loggedUser) {
        super.execute(recordId, conditional, trigger, loggedUser);
        updateActive(Activity.class.getCanonicalName(), DELAYED_FIELD, recordId);
    }


}
