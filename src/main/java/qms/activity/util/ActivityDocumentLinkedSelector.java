/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.util;

import DPMS.DAOInterface.IDocumentDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import mx.bnext.core.util.GridInfo;
import qms.access.dto.ILoggedUser;
import qms.activity.entity.Activity;
import qms.document.dto.DocumentLinkedSelectorDTO;
import qms.framework.util.CacheRegion;
import qms.util.GridFilter;
import qms.util.LinkedSelector;
import qms.util.interfaces.ILinkedSelector;

/**
 *
 * <AUTHOR>
 */
public class ActivityDocumentLinkedSelector extends LinkedSelector implements ILinkedSelector {
 
    @Override
    public GridInfo<DocumentLinkedSelectorDTO> getDialogRows(final IUntypedDAO dao, final GridFilter filter, ILoggedUser loggedUser) {
        final IDocumentDAO documentDao = dao.getBean(IDocumentDAO.class);
        final String condition = documentDao.filterRowsForCurrentUser(loggedUser);
        final Object previousCondition = filter.getCriteria().get("<condition>");
        if (previousCondition == null || previousCondition.toString().trim().isEmpty()) {
            filter.getCriteria().put("<condition>", condition);
        } else {
            filter.getCriteria().put("<condition>", previousCondition + " AND " + condition);
        }
        if (!filter.getCriteria().containsKey("deleted")) {
            filter.getCriteria().put("deleted", "0");
        }
        filter.getField().setOrderBy("id");
        filter.setDirection((byte) 2);
        return dao.HQL_getRows(""
                + " SELECT new " + DocumentLinkedSelectorDTO.class.getCanonicalName() + " ( "
                + DocumentLinkedSelectorDTO.HQL_DIALOG_DOCUMENT_LINKED_SELECTOR
            + " )"
            + " FROM " + Document.class.getCanonicalName() + " c "
            + " WHERE c.status = " + Document.STATUS.ACTIVE.getValue() + ""
            + " AND c.documentType.documentControlledType = '" + DocumentType.documentControlledType.CONTROLLED.toString() + "'", filter);
    }
    
    @Override
    public List<DocumentLinkedSelectorDTO> getGroundValues(final Long baseId, final IUntypedDAO dao, ILoggedUser loggedUser) {
        final List<DocumentLinkedSelectorDTO> values = dao.HQL_findByQuery(""
                + " SELECT new " + DocumentLinkedSelectorDTO.class.getCanonicalName() + " ("
                    + DocumentLinkedSelectorDTO.HQL_GROUND_DOCUMENT_LINKED_SELECTOR
                    + ", act.commitmentTask AS activityCommitmentTask"
                    + ", a.id.activityId AS activityId"
                    + ", a.createdBy AS createdBy"
                + " )"
                + " FROM " + getBaseClazzName() + " a"
                + " CROSS JOIN " + Document.class.getCanonicalName() + " c"
                + " CROSS JOIN " + User.class.getCanonicalName() + " u"
                + " INNER JOIN " + Activity.class.getCanonicalName() + " act ON a.id." + getGroundIdField() + " = act.id "
                + " WHERE a.id.documentId = c.id"
                + " AND u.id = a.createdBy"
                + " AND a.id." + getGroundIdField() + " = :baseId",
                ImmutableMap.of("baseId", baseId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        return values;
    }

}