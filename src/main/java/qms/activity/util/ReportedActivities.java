package qms.activity.util;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ReportedActivities {
    
    private Boolean success;
    private String code;
    private String description;
    private Long recurrenceId;
    private Long parentTreeActivityId;
    private Long implementationId;
    private String implementationCode;
    private Long verificationId;
    private String verificationCode;
    private ReportedEntity entity;
    private List<ReportedActivities> childs;
    private Integer commitmentTask;
    private Boolean replacingOldVerification;
    
    public ReportedActivities(boolean success) {
        this.success = success;
    }

    public ReportedActivities(Long recurrenceId, Long parentTreeActivityId, Long implementationId, Long verificationId) {
        this.recurrenceId = recurrenceId;
        this.parentTreeActivityId = parentTreeActivityId;
        this.implementationId = implementationId;
        this.verificationId = verificationId;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
     
    public Long getRecurrenceId() {
        return recurrenceId;
    }

    public void setRecurrenceId(Long recurrenceId) {
        this.recurrenceId = recurrenceId;
    }

    public Long getParentTreeActivityId() {
        return parentTreeActivityId;
    }

    public void setParentTreeActivityId(Long parentTreeActivityId) {
        this.parentTreeActivityId = parentTreeActivityId;
    }

    public Long getImplementationId() {
        return implementationId;
    }

    public void setImplementationId(Long implementationId) {
        this.implementationId = implementationId;
    }

    public String getImplementationCode() {
        return implementationCode;
    }

    public void setImplementationCode(String implementationCode) {
        this.implementationCode = implementationCode;
    }

    public Long getVerificationId() {
        return verificationId;
    }

    public void setVerificationId(Long verificationId) {
        this.verificationId = verificationId;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    @JsonIgnore
    public ReportedEntity getEntity() {
        return entity;
    }

    public void setEntity(ReportedEntity entity) {
        this.entity = entity;
    }

    public List<ReportedActivities> getChilds() {
        return childs;
    }

    public void setChilds(List<ReportedActivities> childs) {
        this.childs = childs;
    }

    public Integer getCommitmentTask() {
        return commitmentTask;
    }

    public void setCommitmentTask(Integer commitmentTask) {
        this.commitmentTask = commitmentTask;
    }

    public Boolean getReplacingOldVerification() {
        return replacingOldVerification;
    }

    public void setReplacingOldVerification(Boolean replacingOldVerification) {
        this.replacingOldVerification = replacingOldVerification;
    }
    
}
