
package qms.custom.core;

import DPMS.DAOInterface.ICodeSequenceDAO;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.util.BaseClassParser;
import com.google.common.collect.ImmutableMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.core.util.Loggable.LOGGER;
import qms.access.dto.ILoggedUser;
import qms.configuration.entity.DynamicFieldsFiles;
import qms.configuration.util.GridStateUserHelper;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldStatementDTO;
import qms.custom.entity.DynamicFieldFkMapping;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.Helper;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class DynamicT<PERSON>elper extends Helper {

    public DynamicTableHelper(IUntypedDAO dao) {
        super(dao);
    }

    /**
     *
     *
     * @param dataId id del registro a actualizar, -1 si es un insert
     * @param data mapa cuya llave es la columna de la tabla, y el valor es loq ue se insertara
     * @param dynamicTableName tabla donde realizara el insert
     * @return definicion de la tabla y el id del registro
     */
    public DynamicFieldInsertDTO persist(Long dataId, Map<String, Object> data, String dynamicTableName) throws QMSException {
        return persist(dataId, data, "dbo", dynamicTableName);
    }
    public DynamicFieldInsertDTO persist(
            Long dataId,
            Map<String, Object> data,
            String schemaName,
            String dynamicTableName
    ) throws QMSException {
        DynamicFieldInsertDTO r = new DynamicFieldInsertDTO();
        DynamicFieldStatementDTO STATEMENT;
        if (dynamicTableName == null
                || dynamicTableName.trim().isEmpty()
                || "null".equals(dynamicTableName)) {
            return r;
        }
        boolean hasHtmlFields = data.entrySet().stream()
                .anyMatch(match -> match.getKey() != null && match.getKey().endsWith("Html"));
        Map<String, Object> htmlFields = null;
        if (hasHtmlFields) {
            htmlFields = data.entrySet().stream()
                    .filter(f -> f.getKey() != null && f.getKey().endsWith("Html"))
                    .peek(m -> {
                if (m.getValue() == null) {
                    m.setValue("");
                }
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            data.keySet().removeIf(f -> f != null && f.endsWith("Html"));
        }
        r.setSchemaName(schemaName);
        r.setTableName(dynamicTableName);
        if (dataId == -1L) {
            if (dao instanceof ICodeSequenceDAO) {
                r.setId(IdFactory.getNextId(dynamicTableName, (ICodeSequenceDAO) dao));
            } else {
                r.setId(IdFactory.getNextId(dynamicTableName));
            }
            STATEMENT = prepareInsertStatement(schemaName, dynamicTableName, data);
        } else {
            r.setId(dataId);
            STATEMENT = prepareUpdateStatement(schemaName, dynamicTableName, data);
        }
        STATEMENT.getParameters().put("id", r.getId());
        if (STATEMENT.getSql().contains("SET WHERE id")) {
            if (getLogger(LOGGER.DYNAMIC_TABLE).isInfoEnabled()) {
                getLogger(LOGGER.DYNAMIC_TABLE).info("No se relaciono ninguna registro con el campo, registro {}", Utilities.getSerializedObj(r));
            }
        } else {
            dao.SQL_updateByQuery(STATEMENT.getSql(), STATEMENT.getParameters(), 0, Collections.singletonList(dynamicTableName));
            r.setSuccess(true);
        }
        if (hasHtmlFields) {
            this.saveHtmlFiles(r, htmlFields);
        }
        return r;
    }
    
    public void saveHtmlFiles(DynamicFieldInsertDTO r, Map<String, Object> data) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        Long userId = loggedUser.getId();
        Map<String, Object> htmlFields = data;
        final GridStateUserHelper helper = new GridStateUserHelper(dao);
        for (Map.Entry<String, Object> entry : htmlFields.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            
            String tableName = r.getTableName();
            Long tableRowId = r.getId();
            String dynamicFieldName = k.substring(0, k.indexOf("Html"));
            
            DynamicFieldsFiles fileInfo = getDynamicFieldFile(tableName, tableRowId, dynamicFieldName);
            final String preffix = "DynamicFieldHtml_";
            if (fileInfo == null) {
                Long fileId = helper.persistGeneric(-1L, loggedUser.getId(), v, preffix);
                dao.SQL_updateByQuery(""
                        + "INSERT INTO "
                        + "dynamic_field_files ("
                            + "dynamic_field_table_name,"
                            + " dynamic_field_table_name_row_id,"
                            + " dynamic_field_name,"
                            + " dynamic_field_file_id"
                        + ") VALUES ("
                            + ":dynamicFieldTableName,"
                            + " :dynamicFieldTableNameRowId,"
                            + " :dynamicFieldName,"
                            + " :dynamicFieldFileId "
                        + ")", ImmutableMap.of(
                                "dynamicFieldTableName", tableName,
                                "dynamicFieldTableNameRowId", tableRowId,
                                "dynamicFieldName", dynamicFieldName,
                                "dynamicFieldFileId", fileId
                                ),
                        0,
                        Arrays.asList("dynamic_field_files"));
            } else {
                if (fileInfo.getDynamicFieldFileId() == null) {
                    Long fileId = helper.persistGeneric(-1L, userId, v, preffix);
                    fileInfo.setDynamicFieldFileId(fileId);
                    dao.SQL_updateByQuery(" UPDATE dynamic_field_files df "
                            + " SET df.dynamic_field_file_id = :fileId "
                            + " WHERE "
                            + "df.dynamic_field_table_name = :dynTableName AND "
                            + "df.dynamic_field_table_name_row_id = :dynTableRowId AND "
                            + "df.dynamic_field_name = :dynField" , 
                            ImmutableMap.of(
                                    "dynTableName", tableName,
                                    "dynTableRowId", tableRowId,
                                    "dynField", dynamicFieldName, 
                                    "fileId", fileId
                            ),
                        0,
                        Arrays.asList("dynamic_field_files")
                    );
                } else {
                    helper.persistGeneric(fileInfo.getDynamicFieldFileId(), userId, v, preffix);
                }
            }
        }
    }
    
    private DynamicFieldsFiles getDynamicFieldFile(String dynTableName, Long id, String dynamicFieldName) {
        DynamicFieldsFiles data = (DynamicFieldsFiles) dao.HQL_findSimpleObject(" "
                + " SELECT f "
                + " FROM " + DynamicFieldsFiles.class.getCanonicalName() + " f "
                + " WHERE"
                        + " f.dynamicFieldTableNameRowId = :id"
                        + " AND f.dynamicFieldTableName = :dynTableName"
                        + " AND f.dynamicFieldName = :dynamicFieldName", 
                ImmutableMap.of(
                        "id", id , 
                        "dynTableName", dynTableName, 
                        "dynamicFieldName", dynamicFieldName));
        
        return data;
    }

    public void updateDynReference(PersistableDynamicEntity entity) {
        DynamicFieldInsertDTO dto = new DynamicFieldInsertDTO(entity.getDynamicTableName(), entity.getDynamicTableNameId());
        this.updateDynReference(dto, entity);
    }


    public void updateDynReference(final DynamicFieldInsertDTO dto, final EntityDynamicFields entity) {
        final Class<? extends EntityDynamicFields> entityClazz = entity.getClass();
        final String name = entityClazz.getCanonicalName();
        final Long entityId = entity.getId();
        if (dto == null) {
            getLogger(LOGGER.DYNAMIC_TABLE).error(
                    "Al actualizar dynamic reference el entity '{}' llegó sin su valor de DynamicFieldInsertDTO para el ID = {}",
                    new Object[]{name,entityId}
            );
            return;
        }
        if (dto.getTableName() == null) {
            getLogger(LOGGER.DYNAMIC_TABLE).error(
                    "\"Al actualizar dynamic reference el entity '{}' no tiene tableName en su DynamicFieldInsertDTO del ID = {}",
                    new Object[]{name,entityId}
            );
            return;
        }
        updateDynReference(dto, BaseClassParser.getTableName(entityClazz), entity.getId());
    }
    
    public void updateDynReference(
            final DynamicFieldInsertDTO dto,
            final String entityTable,
            final Long entityId
    ) {
        if (entityId == null) {
            getLogger().error("Called update dynamic reference with null entity id for {}", dto.getId());
            return;
        }
        final String uptatableColumnName = getReferenceIdName(dao, dto.getTableName(), entityTable);
        final Map<String, Object> params = new HashMap<>();
        params.put("entityId", entityId);
        params.put("id", dto.getId());
        final Integer updateCount = dao.SQL_updateByQuery(""
                + " UPDATE " + dto.getTableName()
                + " SET " + uptatableColumnName + " = :entityId "
                + " WHERE id = :id",
                params,
                0,
                Arrays.asList(dto.getTableName()));
        if (!uptatableColumnName.isEmpty() && updateCount > 0) {
            final Map<String, Object> deleteParams = new HashMap<>(2);
            deleteParams.put("id", dto.getId());
            deleteParams.put("entityId", entityId);
            dao.SQL_updateByQuery(""
                    + " DELETE FROM " + dto.getTableName()
                    + " WHERE "
                    + " id != :id"
                    + " AND " + uptatableColumnName + " = :entityId",
                    deleteParams,
                    0,
                    Arrays.asList(dto.getTableName())
            );

        } else {
            getLogger(LOGGER.DYNAMIC_TABLE).error("The dynamic fields data wasn't updated!! please check its DB relation, uptatableColumnName = '{}', referenceTableName = '{}', dto = '{}'", new Object[]{
                uptatableColumnName, entityTable, dto
            });
        }
    }

    
    public static String getReferenceIdName(IUntypedDAO dao, String dynamicFieldTableName, String referenceTableName) {
        Map m = new HashMap();
        m.put("dynamicFieldTableName", dynamicFieldTableName);
        m.put("referenceTableName", referenceTableName);
        return dao.HQL_findSimpleString(""
                + " SELECT c.description"
                + " FROM " + DynamicFieldFkMapping.class.getCanonicalName() + " c "
                + " WHERE c.dynamicFieldTableName = :dynamicFieldTableName"
                + " AND c.referenceTableName = :referenceTableName",
                m, 
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        
    }
    
    public DynamicFieldStatementDTO prepareUpdateStatement(
            final String schemaName,
            final String tableName,
            final Map<String, Object> data
    ) {
        List<String> keys = getValidKeys(data);
        StringBuilder UPDATE_STATEMENT = new StringBuilder("UPDATE ");
        UPDATE_STATEMENT.append("[").append(schemaName).append("].[").append(tableName).append("] SET");
        final Map<String, Object> parameters = new HashMap<>(data.size());
        for (String key : keys) {
            if (data.get(key) == null) {
                UPDATE_STATEMENT.append("[").append(key).append("]").append(" = null,");
            } else if (data.get(key) instanceof String && ((String) data.get(key)).trim().isEmpty()) {
                UPDATE_STATEMENT.append("[").append(key).append("]").append(" = null,");
            } else {
                final String namedKey = cleanParameterName(key);
                parameters.put(namedKey, data.get(key));
                UPDATE_STATEMENT.append("[").append(key).append("]").append(" = :").append(namedKey).append(',');
            }
        }
        UPDATE_STATEMENT.append("last_modified_date = current_timestamp WHERE id = :id");
        final DynamicFieldStatementDTO statement = new DynamicFieldStatementDTO();
        statement.setSql(UPDATE_STATEMENT.toString());
        statement.setParameters(parameters);
        return statement;
    }

    /**
     *
     * @param tableName tabla donde realizara el insert
     * @param data mapa cuya llave es la columna de la tabla, y el valor es loq ue se insertara
     * @return
     */
    public DynamicFieldStatementDTO prepareInsertStatement(
            final String schemaName,
            final String tableName,
            final Map<String, Object> data
    ) {
        List<String> keys = getValidKeys(data);
        StringBuilder INSERT_STATEMENT = new StringBuilder("INSERT INTO "),
                VALUES = new StringBuilder(255)
                .append("id, status, deleted, created_date, last_modified_date").append(") VALUES (");
        INSERT_STATEMENT.append("[").append(schemaName).append("].[").append(tableName).append("] (");
        final Map<String, Object> parameters = new HashMap<>(data.size());
        for (String key : keys) {
            if (Objects.equals("id", key)) {
                if (getLogger(LOGGER.DYNAMIC_TABLE).isWarnEnabled()) {
                    getLogger(LOGGER.DYNAMIC_TABLE).warn(""
                        + " To prevent an exception a parameter called 'Id' (id: {}) was SKIPPED"
                        + " at the INSERT statement of dynamic fields at table '{}', "
                        + " this may ocurr due to a bad implementation of the 'makePersistance' method.", new Object[] {
                            data.get(key), tableName
                        }
                    );
                }
                continue;
            }
            if (data.get(key) != null && !Objects.equals(data.get(key), "")) {
                INSERT_STATEMENT.append("[").append(key).append("]").append(',');
                final String namedKey = cleanParameterName(key);
                parameters.put(namedKey, data.get(key));
                VALUES.append(':').append(namedKey).append(',');
            }
        }
        INSERT_STATEMENT.append(VALUES)
                .append(":id, 1, 0, current_timestamp, current_timestamp")
                .append(')');
        final DynamicFieldStatementDTO statement = new DynamicFieldStatementDTO();
        statement.setSql(INSERT_STATEMENT.toString());
        statement.setParameters(parameters);
        return statement;
    }

    private List<String> getValidKeys(Map<String, Object> data) {
        final List<String> keys = data.keySet().stream().filter((key) -> key != null).sorted().collect(Collectors.toList());
        List<String> columns = Arrays.asList("id", "status", "deleted", "created_date", "last_modified_date");
        columns.forEach(column -> {
            keys.remove(column);
        });
        return keys;
    }

    public static String cleanParameterName(final String parameter) {
        return parameter.replace(":", "_").replace("-", "_");
    }
    
}
