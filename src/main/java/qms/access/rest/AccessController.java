package qms.access.rest;

import DPMS.DAO.HibernateDAO_User;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.DAOInterface.IUserIdNameDAO;
import DPMS.DAOInterface.IUserRefDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.DepartmentProcessLoad;
import DPMS.Mapping.Process;
import DPMS.Mapping.User;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import mx.bnext.access.IBnextModule;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.AuthenticationException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.access.dto.MailUserScheduleDTO;
import qms.access.dto.MailUserTypeDTO;
import qms.access.util.MailSettingsHandler;
import qms.access.util.MailSettingsUtil;
import qms.form.dto.FavoriteTaskDto;
import qms.form.dto.FavoriteTaskSaveDTO;
import qms.form.entity.FavoriteTask;
import qms.form.entity.IFavoriteTask;
import qms.form.entity.UserFavoriteTask;
import qms.form.rest.FormController;
import qms.framework.entity.UserGroup;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.task.dto.FavoriteMenuItem;
import qms.task.dto.IQualifiedMenuItem;
import qms.task.enums.FavoriteTaskType;
import qms.task.util.QualifiedMenuItemUtil;
import qms.template.entity.TemplateDataMap;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("rest/access")
public class AccessController {

    private final String HAS_QUERY_REPORT_ACTIVITY_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'ACTIVITY_REGISTER_QUERY', 'ACTIVITY_DELETE_QUERY',"
            + " 'ACTIVITY_ENABLE_CACHE', 'ACTIVITY_SYNC_CACHE', 'ACTIVITY_VIEW_QUERY')";

    private final String HAS_REPORT_ACTIVITY_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'ACTIVITY_REGISTER_REPORT', 'ACTIVITY_ADMON_REPORT_ACCESS',"
            + " 'ACTIVITY_DELETE_REPORT')";

    private final String HAS_USER_GROUP_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA', 'ADMON_ACCESOS',"
            + " 'SPECIAL_ACCESS_EDIT_PROFILE')";

    private final String HAS_ACTIVITIES_PLANNER_ACESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_CREATOR',"
            + " 'ACTIVITY_MANAGER')";

    private final String FAVORITE_QUERIE =
            " SELECT c "
                    + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                    + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                    + " ON uf.id.favoriteTaskId = c.id"
                    + " JOIN " + UserRef.class.getCanonicalName() + " u "
                    + " ON uf.id.userId = u.id"
                    + " WHERE"
                    + " u.id = :loggedUser"
                    + " AND c.status = 1"
                    + " AND c.type IN ( :favoriteType ) "
                    + " ANd c.isSystemGenerated = false"
                    + " AND c.deleted = 0";

    private final String HAS_ACCESS_ACTIVITIES_RESOLUTION = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'CATALOGS_ACTIVITIES', 'USUARIO_CORPORATIVO')";

    private final String HAS_ACCESS_ACTIVITIES_CONNECTIONS_REPORTS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'ACTIVITY_REGISTER_CONNECTION', 'ACTIVITY_DELETE_CONNECTION', 'ACTIVITY_VIEW_CONNECTION', 'USUARIO_CORPORATIVO')";

    private final String HAS_ACCESS_ACTIVITIES_CACHE_REPORTS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'ACTIVITY_VIEW_SYNC_EVENT', 'ACTIVITY_SYNC_CACHE', 'ACTIVITY_ENABLE_CACHE', 'USUARIO_CORPORATIVO')";

    private final String HAS_ACCESS_DYNAMIC_FIELDS = "hasAnyAuthority('IS_ADMIN', 'ADMON_SISTEMA',"
            + " 'CATALOGS_GENERALS', 'USUARIO_CORPORATIVO')";

    @Autowired
    @Qualifier("HibernateDAO_User")
    private IUserDAO user;

    @Autowired
    @Qualifier("HibernateDAO_UserRef")
    private IUserRefDAO links;

    @Autowired
    @Qualifier("HibernateDAO_User_IdName")
    private IUserIdNameDAO userDao;

    @PostMapping(value = "/data-source/business-unit/list")
    public GridInfo<Map<String, Object>> getBusinessUnitList(@RequestBody GridFilter filter) {
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        return user.HQL_getRows(""
                        + " SELECT new map("
                        + " c.id AS id "
                        + ",c.code AS code "
                        + ",c.description AS description "
                        + " ) "
                        + " FROM " + BusinessUnit.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.id IN ("
                        + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                        + " )"
                , filter);
    }

    @PostMapping(value = "/data-source/business-unit-department/list")
    public GridInfo<Map<String, Object>> getBusinessUnitDepartmentList(@RequestBody GridFilter filter) {
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        return user.HQL_getRows(""
                        + " SELECT new map("
                        + " c.id AS id "
                        + ",c.code AS code "
                        + ",c.description AS description "
                        + " ) "
                        + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.id IN ("
                        + IBusinessUnitDepartmentDAO.getLoggedUserBusinessDepartmentUnitIdsQuery(loggedUser.getId(), false, loggedUser.isAdmin())
                        + " )"
                , filter);
    }

    @PostMapping(value = "/data-source/group/list")
    public GridInfo<Map<String, Object>> getGroupList(@RequestBody GridFilter filter) {
        return user.HQL_getRows(""
                        + " SELECT new map("
                        + " c.id AS id "
                        + ",c.code AS code "
                        + ",c.description AS description "
                        + " ) "
                        + " FROM " + UserGroup.class.getCanonicalName() + " c "
                        + " WHERE c.deleted = 0 AND c.status = " + UserGroup.STATUS.ACTIVE.getValue()
                , filter);
    }


    @PostMapping({"/data-source/user/list"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo userList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.description");
        }
        /**
         * ToDo: Filtrar solo por los usuarios a los que "loggedUser" tiene sentido que vea.
         *
         * Ej. 1) Minimo personas de su departamento,
         *     2) Tal vez gente de su planta... (validar con funcional)
         *     2) Tal vez gente de los departamentos donde participa... (validar con funcional)
         *     2) Tal vez gente de las plantas donde participa... (validar con funcional)
         *     3) Si tiene permisos de ADMON_ACCESOS (o similar) que vea todo
         */
        return user.HQL_getRows(""
                        + " SELECT new map( "
                        + " entity.id AS id"                                         // AS id
                        + ",bu.id AS businessUnitId"                            // AS businessUnitId
                        + ",bud.id AS businessUnitDepartmentId "                // AS businessUnitDepartmentId
                        + ",g.id AS mainTeamOwnerId "                           // AS mainTeamOwnerId
                        + ",entity.description AS description"                  // AS description
                        + ",entity.correo AS correo"                            // AS correo
                        + ",entity.code AS code"                                // AS code
                        + ",entity.cuenta AS cuenta"                            // AS cuenta
                        + ",bud.description AS businessUnitDepartmentName"      // AS businessUnitDepartmentName
                        + ",bu.description AS businessUnitName"                 // AS businessUnitName
                        + ",g.description AS mainTeamOwnerName"                 // AS mainTeamOwnerName
                        + " )"
                        + " FROM " + User.class.getCanonicalName() + " entity "
                        + " JOIN entity.businessUnitDepartment bud "
                        + " JOIN bud.businessUnit bu "
                        + " LEFT JOIN entity.mainTeamOwner g "
                        + " WHERE "
                        + " entity.deleted = 0"
                        + " AND entity.status = " + User.STATUS.ACTIVE.getValue()
                , filter
        );
    }

    @PostMapping({"/data-source/user-for-current-user/list"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String,Object>> userListForCurrentUser(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.description");
        }
        var loggedUserId = SecurityUtils.getLoggedUserId();

        /**
         * ToDo: Filtrar solo por los usuarios a los que "loggedUser" tiene sentido que vea.
         *
         * Ej. 1) Minimo personas de su departamento,
         *     2) Tal vez gente de su planta... (validar con funcional)
         *     2) Tal vez gente de los departamentos donde participa... (validar con funcional)
         *     2) Tal vez gente de las plantas donde participa... (validar con funcional)
         *     3) Si tiene permisos de ADMON_ACCESOS (o similar) que vea todo
         */
        return user.HQL_getRows(""
                        + " SELECT new map( "
                        + " c.id AS id"                                         // AS id
                        + ",c.description AS description"                  // AS description
                        + ",c.correo AS correo"                            // AS correo
                        + ",c.cuenta AS cuenta"                            // AS cuenta
                        + " )"
                        + " FROM " + User.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.deleted = 0"
                        + " AND c.status = " + User.STATUS.ACTIVE.getValue()
                        + (SecurityUtils.isLoggedUserAdmin() ? "" : " AND " + HibernateDAO_User.getValidEntitiesFilterByUser(loggedUserId))
                , filter
        );
    }

    @PostMapping({"/data-source/process/list"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String,Object>> processList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.description");
        }

        return user.HQL_getRows("""
                    SELECT new map(
                    c.id as id,
                    c.description as description,
                    c.code as code
                    )
                     FROM %s c
                    WHERE (c.deleted is null OR c.deleted = 0) AND c.status = 1
                    """.formatted(Process.class.getCanonicalName())
                , filter
        );
    }

    @GetMapping(value = "/business-unit-departments")
    public ResponseEntity<List<Map<String, Object>>/*<BusinessUnitDepartmentEntity>*/> getBusinessUnitDepartments() {
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        return ResponseEntity.ok(
                user.HQL_findByQuery(""
                        + " SELECT new map("
                        + " c.id AS value "
                        + ",c.businessUnitId AS businessUnitId "
                        + ",c.description AS text "
                        + " ) "
                        + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.businessUnitId IN ("
                        + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                        + " )"
                )
        );
    }

    @GetMapping(value = "/processes-catalogs")
    public ResponseEntity<Map<String, Object>> getProcessesCatalogs() {
        return ResponseEntity.ok(
                ImmutableMap.of(
                        "businessUnitDepartments", this.getBusinessUnitDepartments().getBody(),
                        "departmentProcesses", this.getDepartmentProcesses(true).getBody()
                )
        );
    }

    @GetMapping(value = "/department-processes")
    public ResponseEntity<List<Map<String, Object>>> getDepartmentProcesses() {
        return getDepartmentProcesses(false);
    }

    private ResponseEntity<List<Map<String, Object>>> getDepartmentProcesses(boolean departmentName) {
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        return ResponseEntity.ok(
                user.HQL_findByQuery(""
                        + " SELECT new map("
                        + " c.id AS value "
                        + ",d.id AS businessUnitDepartmentId "
                        + (departmentName ? ""
                        + ",concat(c.description, ' (', d.departmentDescription, ')')" : ""
                        + ",c.description") + " AS text "
                        + " ) "
                        + " FROM " + DepartmentProcessLoad.class.getCanonicalName() + " c "
                        + " JOIN c.department d "
                        + " WHERE "
                        + " d.businessUnitId IN ("
                        + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                        + " )"
                )
        );
    }

    @GetMapping(value = "/business-units")
    public ResponseEntity<List<TextLongValue>> getBusinessUnits() {
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        return ResponseEntity.ok(
                user.HQL_findByQuery(""
                        + " SELECT new " + TextLongValue.class.getCanonicalName() + "("
                        + " c.description "
                        + ",c.id "
                        + " ) "
                        + " FROM " + BusinessUnit.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.id IN ("
                        + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                        + " )"
                )
        );
    }

    @GetMapping(value = "/business-unit-ids")
    public ResponseEntity<List<Long>> getBusinessUnitIds() {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        return ResponseEntity.ok(user.getBusinessUnits(loggedUserId,
                SecurityUtils.isLoggedUserAdmin(),
                SecurityUtils.hasLoggedUserService(ProfileServices.USUARIO_CORPORATIVO)
        ));
    }

    @GetMapping(value = "/modules")
    public ResponseEntity getModules() throws AuthenticationException {
        if (SecurityUtils.getLoggedUserId() == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Collection<IBnextModule> modules = SecurityUtils.getLoggedUserModules();
        return ResponseEntity.ok(modules);
    }

    @GetMapping(value = "/services")
    public ResponseEntity getAuthorities() throws AuthenticationException {
        if (SecurityUtils.getLoggedUserId() == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Collection<String> services = SecurityUtils.getLoggedUserAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(Collectors.toList());
        return ResponseEntity.ok(services);
    }

    @PostMapping(value = "/favorite/add/menu-path")
    public ResponseEntity<FavoriteTaskSaveDTO> addMenuPath(
            final @RequestBody FavoriteMenuItem favorite
    ) throws QMSException {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final FavoriteTaskSaveDTO result = links.addMenuPath(favorite, loggedUserId);
        if (result.getSuccess()) {
            return new ResponseEntity<>(result, HttpStatus.OK);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @PostMapping("my-links")
    public GridInfo<Map<String, Object>> myLinks(@RequestBody GridFilter filter) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        return links.myLinks(filter, loggedUserId);
    }

    @PostMapping("mail-settings-mine/types")
    public GridInfo<MailUserTypeDTO> getMailSettingsTypesMine() {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final List<String> modules = SecurityUtils.getLoggedUserModules().stream()
                .map((module) -> module.getKey())
                .collect(Collectors.toList());
        return MailSettingsUtil.getMailSettingsTypesByUser(loggedUserId, modules);
    }

    @PostMapping("mail-settings-mine/schedules")
    public GridInfo<MailUserScheduleDTO> getMailSettingsSchedulesMine() {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final List<String> modules = SecurityUtils.getLoggedUserModules().stream()
                .map((module) -> module.getKey())
                .collect(Collectors.toList());
        return MailSettingsUtil.getMailSettingsSchedulesByUser(loggedUserId, modules);
    }

    @PostMapping("mail-settings-mine/types/update")
    public ResponseEntity<List<Long>> updateMailSettingsTypesMine(@RequestBody List<MailUserTypeDTO> types) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final List<Long> result = userDao.updateMailSettingsTypeByUser(loggedUserId, types, loggedUserId);
        if (result != null && result.size() == types.size()) {
            MailSettingsHandler.reset();
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @PostMapping("mail-settings-mine/schedules/update")
    public ResponseEntity<List<Long>> updateSchedulesMailSettingsMine(@RequestBody List<MailUserScheduleDTO> schedules) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final List<Long> result = userDao.updateMailSettingsScheduleByUser(loggedUserId, schedules, loggedUserId);
        if (result != null && result.size() == schedules.size()) {
            MailSettingsHandler.reset();
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @PostMapping("delete-my-links")
    public ResponseEntity<List<Long>> deleteMyLinks(@RequestBody List<Long> favoriteTaskIds) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final List<Long> result = links.deleteMyLinks(favoriteTaskIds, loggedUserId);
        if (result != null && result.size() == favoriteTaskIds.size()) {
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @PostMapping("update-my-links")
    public ResponseEntity<List<Long>> updateMyLinks(@RequestBody List<FavoriteTaskDto> linksList) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final List<Long> result = links.updateMyLinks(linksList, loggedUserId);
        if (result != null && result.size() == linksList.size()) {
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }


    @GetMapping("/get-favorite-link/{favoriteTaskId}")
    public ResponseEntity<Map<String, Object>> getFavoriteLink(
            @PathVariable(value = "favoriteTaskId", required = true) Long favoriteTaskId
    ) {
        Map<String, Object> favorite = links.HQL_findSimpleMap(""
                        + " SELECT new map("
                        + " ft.urlParams AS urlParams"
                        + ",ft.description AS description"
                        + ",ft.isSystemGenerated AS isSystemGenerated"
                        + ",ft.type AS type"
                        + ",ft.menuPath AS menuPath"
                        + " )"
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " ft "
                        + " WHERE ft.id = :favoriteTaskId",
                ImmutableMap.of("favoriteTaskId", favoriteTaskId),
                true,
                CacheRegion.FAVORITE,
                0
        );
        if (favorite == null) {
            return new ResponseEntity(favorite, HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity(favorite, HttpStatus.OK);
    }


    @GetMapping("/get-favorite-params/{favoriteTaskId}")
    public ResponseEntity<Map<String, String>> getFavoriteParams(
            @PathVariable(value = "favoriteTaskId", required = true) Long favoriteTaskId
    ) {
        List<TemplateDataMap> paramList = links.HQL_findByQuery(""
                        + " SELECT new " + TemplateDataMap.class.getCanonicalName() + "("
                        + " p.deleted "
                        + ",p.fieldName "
                        + ",p.fieldValue "
                        + " )"
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " ft "
                        + " JOIN ft.paramList p"
                        + " WHERE ft.id = :favoriteTaskId",
                ImmutableMap.of("favoriteTaskId", favoriteTaskId),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        if (paramList == null) {
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity(
                FavoriteTask.getParams(paramList), HttpStatus.OK
        );
    }

    @GetMapping(value = "/links")
    public ResponseEntity<List<IQualifiedMenuItem>> getExternalLinks() throws AuthenticationException {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        final List<IQualifiedMenuItem> result = links.getExternalLinks(SecurityUtils.getLoggedUser());
        if (result != null) {
            return ResponseEntity.ok(result);
        }
        return new ResponseEntity<>(result, HttpStatus.CONFLICT);
    }

    @GetMapping("favoriteReports")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> timesheetFavoriteReports() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        List<IQualifiedMenuItem> result = new ArrayList<>();
        List<IFavoriteTask> favorites = links.HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                        + " ON uf.id.favoriteTaskId = c.id"
                        + " JOIN " + UserRef.class.getCanonicalName() + " u "
                        + " ON uf.id.userId = u.id"
                        + " WHERE"
                        + " u.id = :loggedUserId"
                        + " AND c.status = 1"
                        + " AND c.type IN ("
                        + FavoriteTaskType.REPORT.getValue()
                        + " ) "
                        + " ANd c.isSystemGenerated = false"
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "loggedUserId",
                        loggedUser.getId()
                ),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        String account = SecurityUtils.getLoggedUserAccount();
        favorites.forEach((IFavoriteTask fav) -> {
            IQualifiedMenuItem task = null;
            try {
                task = QualifiedMenuItemUtil.fromUserFavoriteTask(account, fav, links);
            } catch (UnsupportedEncodingException ex) {
                Loggable.getLogger(FormController.class).error("Invalid userFavoriteTask, account ''", account, ex);
            }
            if (task != null) {
                result.add(task);
            }
        });
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("userGroupFavorites")
    @PreAuthorize(HAS_USER_GROUP_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> userGroupFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        List<IFavoriteTask> favorites = links.HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                        + " ON uf.id.favoriteTaskId = c.id"
                        + " JOIN " + UserRef.class.getCanonicalName() + " u "
                        + " ON uf.id.userId = u.id"
                        + " WHERE"
                        + " u.id = :loggedUserId"
                        + " AND c.status = 1"
                        + " AND c.type IN ("
                        + FavoriteTaskType.USER_GROUP.getValue()
                        + " ) "
                        + " ANd c.isSystemGenerated = false"
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "loggedUserId",
                        loggedUser.getId()
                ),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesReportFavorites")
    @PreAuthorize(HAS_REPORT_ACTIVITY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesReportFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        List<IFavoriteTask> favorites = links.HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                        + " ON uf.id.favoriteTaskId = c.id"
                        + " JOIN " + UserRef.class.getCanonicalName() + " u "
                        + " ON uf.id.userId = u.id"
                        + " WHERE"
                        + " u.id = :loggedUserId"
                        + " AND c.status = 1"
                        + " AND c.type IN ("
                        + FavoriteTaskType.ACTIVITIES_REPORT_BD.getValue()
                        + " ) "
                        + " ANd c.isSystemGenerated = false"
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "loggedUserId",
                        loggedUser.getId()
                ),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesQueryFavorites")
    @PreAuthorize(HAS_QUERY_REPORT_ACTIVITY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesQueryFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        List<IFavoriteTask> favorites = links.HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                        + " ON uf.id.favoriteTaskId = c.id"
                        + " JOIN " + UserRef.class.getCanonicalName() + " u "
                        + " ON uf.id.userId = u.id"
                        + " WHERE"
                        + " u.id = :loggedUserId"
                        + " AND c.status = 1"
                        + " AND c.type IN ("
                        + FavoriteTaskType.ACTIVITIES_QUERY_BD.getValue()
                        + " ) "
                        + " AND c.isSystemGenerated = false"
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "loggedUserId",
                        loggedUser.getId()
                ),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesPlanner")
    @PreAuthorize(HAS_ACTIVITIES_PLANNER_ACESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesPlannerFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        List<IFavoriteTask> favorites = links.HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                        + " ON uf.id.favoriteTaskId = c.id"
                        + " JOIN " + UserRef.class.getCanonicalName() + " u "
                        + " ON uf.id.userId = u.id"
                        + " WHERE"
                        + " u.id = :loggedUserId"
                        + " AND c.status = 1"
                        + " AND c.type IN ("
                        + FavoriteTaskType.ACTIVITY_PLANNED.getValue()
                        + " ) "
                        + " ANd c.isSystemGenerated = false"
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "loggedUserId",
                        loggedUser.getId()
                ),
                true, CacheRegion.TEMPLATE_ACTIVITY,
                0
        );
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesQueryControlFavorites")
    @PreAuthorize(HAS_QUERY_REPORT_ACTIVITY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesQueryControlFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_QUERY_BD_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesReportControlFavorites")
    @PreAuthorize(HAS_REPORT_ACTIVITY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesReportControlFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_REPORT_BD_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("userGroupControlFavorites")
    @PreAuthorize(HAS_USER_GROUP_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> userGroupControlFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.USER_GROUP_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    private List<IQualifiedMenuItem> setQualifiedItemsToFavorites(List<IFavoriteTask> favorites) {
        if (SecurityUtils.getLoggedUserId() == null) {
            return Utilities.EMPTY_LIST;
        }
        List<IQualifiedMenuItem> result = new ArrayList<>();
        String account = SecurityUtils.getLoggedUserAccount();
        favorites.forEach((IFavoriteTask fav) -> {
            IQualifiedMenuItem task = null;
            try {
                task = QualifiedMenuItemUtil.fromUserFavoriteTask(account, fav, links);
            } catch (UnsupportedEncodingException ex) {
                Loggable.getLogger(FormController.class).error("Invalid userFavoriteTask, account ''", account, ex);
            }
            if (task != null) {
                result.add(task);
            }
        });
        return result;
    }

    @GetMapping("activitiesResolutions")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_RESOLUTION)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesResolutions() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_RESOLUTION_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesConnectionsFavorites")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_CONNECTIONS_REPORTS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesConnectionsFavorites() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_CONNECTIONS_REPORTS.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesPrioritiesControl")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_RESOLUTION)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesPrioritiesControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_PRIORITIES_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesSourceControl")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_RESOLUTION)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesSourceControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_SOURCES_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesObjetiveControl")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_RESOLUTION)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesObjetiveControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_OBJETIVE_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesReportCacheControl")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_CACHE_REPORTS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesReportCacheControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_CACHE_BD_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("activitiesTypesControl")
    @PreAuthorize(HAS_ACCESS_ACTIVITIES_RESOLUTION)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> activitiesTypesControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.ACTIVITIES_TYPES_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }

    @GetMapping("dynamicFieldsControl")
    @PreAuthorize(HAS_ACCESS_DYNAMIC_FIELDS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> dynamicFieldsControl() {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
        Integer type = FavoriteTaskType.DYNAMIC_FIELD_CONTROL.getValue();
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUser", loggedUser.getId());
        params.put("favoriteType", type);
        List<IFavoriteTask> favorites = links.HQL_findByQuery(FAVORITE_QUERIE, params, true, CacheRegion.TEMPLATE_ACTIVITY, 0);
        return new ResponseEntity<>(setQualifiedItemsToFavorites(favorites), HttpStatus.OK);
    }
}
