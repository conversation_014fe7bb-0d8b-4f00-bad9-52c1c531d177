package qms.access.logic;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.AccessHistory;
import DPMS.Mapping.Node;
import DPMS.Mapping.NodeArea;
import DPMS.Mapping.NodeBusinessUnit;
import DPMS.Mapping.NodeBusinessUnitDepartment;
import DPMS.Mapping.NodeUser;
import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.login.core.IUserDetail;
import bnext.login.dto.ClientBrowserDTO;
import bnext.login.dto.ScreenResolutionDTO;
import bnext.login.dto.ServicesDTO;
import bnext.login.util.OidcProvider;
import com.opensymphony.xwork2.ActionContext;
import isoblock.common.Properties;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.Loggable;
import org.apache.struts2.dispatcher.SessionMap;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.access.util.ProfileServicesUtil;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.security.UserLogin;
import qms.framework.util.LoginSource;
import qms.framework.util.SessionFilterHandler;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class SessionHelper {
    
    private static final Logger LOGGER = Loggable.getLogger(SessionHelper.class);
    
    private static final String HAS_ACCESS_FOLDERS = ""
            + " SELECT n.id "
            + " FROM " + Node.class.getCanonicalName() + " n"
            + " WHERE n.deleted = 0"
            + " AND n.status = 1"
            + " AND ("
                + " exists ("
                    + " SELECT 1"
                    + " FROM " + NodeBusinessUnit.class.getCanonicalName() + " nbu, DPMS.Mapping.User u"
                    + " JOIN u.puestos p"
                    + " WHERE nbu.id.nodeId = n.id"
                    + " AND nbu.id.businessUnitId = p.une.id"
                    + " AND u.id =:uId"
                + " ) OR exists ("
                    + " SELECT 1"
                    + " FROM " + NodeBusinessUnitDepartment.class.getCanonicalName() + " nbud, DPMS.Mapping.User u"
                    + " JOIN u.puestos p"
                    + " JOIN p.departamentos d"
                    + " WHERE nbud.id.nodeId = n.id"
                    + " AND nbud.id.businessUnitDepartmentId = d.id"
                    + " AND u.id =:uId"
                + " ) OR exists ("
                + " SELECT 1"
                    + " FROM " + NodeUser.class.getCanonicalName() + " nu"
                    + " WHERE nu.id.userId = :uId"
                    + " AND nu.id.nodeId = n.id"
                + " ) OR exists ("
                + " SELECT 1"
                    + " FROM " + NodeArea.class.getCanonicalName() + " na"
                    + " , " + User.class.getCanonicalName() + " u"
                    + " JOIN u.puestos p"
                    + " JOIN p.procesos pro"
                    + " WHERE na.id.nodeId = n.id"
                    + " AND na.id.areaId = pro.id AND u.id =:uId"
                + " )"
            + " )"
            + " group by n.id";
    
    private final static List<Long> NOT_INCLUDED_IN_FOLDERS = Arrays.asList(
            0L,//Root
            8L,//Proyectos
            9L,//acciones
            10L,//Reuniones
            12L);//Quejas
    
    public static final String SESSION_ATTRIBUTE_LOGIN_INFO = "LOGIN_INFO";
    public static final String SESSION_ATTRIBUTE_SERVICES = "services";

    public boolean validSession(final HttpSession session) {
        try {
            session.getCreationTime();
        } catch(IllegalStateException e) {
            //La sesión ya sido invalida
            return false;
        }
        return true;
    }
    
    public static List<Long> getFolderAccess(Long currentLoggedUserId, IUntypedDAO dao) {
        final List<Long> nodeList = dao.HQL_findByQuery(HAS_ACCESS_FOLDERS, "uId", currentLoggedUserId);
        nodeList.removeAll(NOT_INCLUDED_IN_FOLDERS);
        return nodeList;
    }

    public Long getBusinessUnitId(Long currentLoggedUserId, IUntypedDAO dao) {
        Long businessUnitId = dao.HQL_findLong(""
            + " SELECT max(p.id) "
            + " FROM " + User.class.getCanonicalName() + " c "
            + " LEFT JOIN c.defaultWorkflowPosition p"
            + " WHERE c.id = :id",
            "id", currentLoggedUserId
        );
        if (businessUnitId == 0L) {
            return null;
        }
        return businessUnitId;
    }
    
    public void resetSession(final HttpServletRequest request, final IUntypedDAO dao) {
        HttpSession session = request.getSession(false);
        this.resetSession(session, dao);
    }
        
    public void resetSession(final HttpSession session, final IUntypedDAO dao) {
        closeSession(session, dao);
        invalidateStrutsSession();
    }


    public static List<ProfileServices> getLoggedUserServices(HttpSession session, Long loggedUserId, boolean isAdmin) {
        //Primero busca los servicios en la session del usuario
        if (session != null && session.getAttribute(SessionHelper.SESSION_ATTRIBUTE_SERVICES) != null){
            List<ProfileServices> services = (List<ProfileServices>) session.getAttribute(SessionHelper.SESSION_ATTRIBUTE_SERVICES);
            services.remove(ProfileServices.NONE);
            return services;
        }
        List<ProfileServices> userServices = new ArrayList<>();
        Loggable.getLogger(SessionViewer.class).trace("Framework.Action.SessionViewer.getLoggedUserServices() ... " + loggedUserId);
        //Si no lo encuentra carga los servicios de la base de datos
        if (isAdmin) {
            final List<ProfileServices> allServices = ProfileServicesUtil.getAllServices();
            Loggable.getLogger(SessionViewer.class).info(">>> Es admin se le dan {} total", allServices.size());
            userServices.addAll(allServices);
            userServices.remove(ProfileServices.DOCUMENT_STARTED_AT_DOCUMENT_VIEWER);
            Loggable.getLogger(SessionViewer.class).info("loggedUserServices: {}", userServices.size());
        } else {
            try {
                IUserDAO daoU = Utilities.getBean(IUserDAO.class);
                final ServicesDTO userServicesData = daoU.getUserServices(loggedUserId, isAdmin);
                userServices = new ArrayList<>(userServicesData.getUserServices());
            } catch (Exception e) {
                Loggable.getLogger(SessionViewer.class).error("Error: no se pudieron obtener los servicios a los que tiene acceso el usuario",e);
            }
        }
        if (session != null){
            session.setAttribute(SessionHelper.SESSION_ATTRIBUTE_SERVICES, userServices);
        }
        userServices.remove(ProfileServices.NONE);
        return userServices;
    }

    private List<ProfileServices> getLoggedUserServices(final HttpSession session) {
        if (session == null) {
            return Utilities.EMPTY_LIST;
        }
        String userId = (String) session.getAttribute("intusuarioid");
        return getLoggedUserServices(session, Long.valueOf(userId), false);
    }
    
    public void closeSession(final HttpSession session, final IUntypedDAO dao) {
        if (session == null) {
            return;
        }
        final UserLogin loginInfo = getUserLoginInfo(session);
        if (loginInfo == null) {
            LOGGER.trace("Close session failed, Session has expired");
            return;
        }

        if (Boolean.TRUE.equals(loginInfo.getAnonymous())) {
            // Los usuarios anónimos no tienen historial de acceso
            return;
        }

        insertaHistorialSalida(session, dao);
        if (!validSession(session)) {
            return;
        }
        String userId = (String) session.getAttribute("intusuarioid");
        if (userId != null) {
            Properties.usersCofig.remove(Long.valueOf(userId));
            Enumeration<String> enume = session.getAttributeNames();
            while (enume.hasMoreElements()) {
                String elem = enume.nextElement();
                session.removeAttribute(elem);
            }
        }
        LOGGER.trace("Session is closed");
    }

    @Nullable
    public static UserLogin getUserLoginInfo(HttpSession session) {
        try {
            return (UserLogin) session.getAttribute(SESSION_ATTRIBUTE_LOGIN_INFO);
        } catch (Exception e) {
            LOGGER.error("Error: no se pudo obtener la información de login del usuario", e);
            return null;
        }
    }

    private void invalidateStrutsSession() {
        LOGGER.trace("Session is being invalidated");
        final ActionContext context = ActionContext.getContext();
        if (context == null) {
            LOGGER.trace("Session is invalidation faled, context is null");
            return;
        }
        final Map<String, Object> sessionMap = context.getSession();
        if (sessionMap == null) {
            LOGGER.trace("Session is invalidation faled, sessionMap is null");
            return;
        }
        try {
            final SessionMap session = (SessionMap) sessionMap;
            if (session.isEmpty()) {
                return;
            }
            session.invalidate();
            session.put("renewServletSession", null);
            session.remove("renewServletSession");
            session.entrySet();
        } catch(IllegalStateException e) {
            //La sesión ya sido invalida
        }
    }
    
    
    public boolean insertaHistorialSalida(final HttpSession session, final IUntypedDAO dao) {
        if (session == null) {
            LOGGER.trace("Session has expired");
            return false;            
        }
        if (getLoggedUserServices(session).contains(ProfileServices.SPECIAL_IS_ANONYMOUS_USER)) {
            // Los usuarios anónimos no tienen historial de acceso
            return false;
        }
        final UserLogin loginInfo = getUserLoginInfo(session);
        if (loginInfo == null) {
            LOGGER.trace("Session has expired");
            return false;
        }
        AccessHistory accessHistory = null;
        try {
            accessHistory = dao.HQLT_findById(AccessHistory.class, loginInfo.getAccessHistoryId());
            if (accessHistory == null) {
                LOGGER.error("Fail to load logout entry for user: '{}'", loginInfo.getAccessHistoryId());
                return false;
            }
            accessHistory.setExit(new Date());
            accessHistory = dao.makePersistent(accessHistory, accessHistory.getUserId());
            final FilterUserHelper filterHelper = new FilterUserHelper(dao);
            filterHelper.persist(
                accessHistory.getUserId(), 
                session.getAttribute(SessionFilterHandler.SORTED_PAGED_FILTERS)
            ); 
        } catch (Exception e) {
            LOGGER.error("Fail to save logout entry for user: '{}'",
                    new Object[]{loginInfo.getId(), e}
            );
        }
        if (accessHistory != null) {
            Properties.usersCofig.remove(accessHistory.getUserId());
            return true;
        }
        return false;
    }
    
    public AccessHistory insertHistory(
            final IUserDetail principal,
            final Long userId,
            final ClientBrowserDTO browser,
            final String token,
            final GeolocationCoordinates location,
            final ScreenResolutionDTO screenResolution,
            final LoginSource loginSource,
            final OidcProvider oidcProvider,
            final String oidcId,
            final IUntypedDAO dao
    ) {
        if (Boolean.TRUE.equals(principal.getIsAnonymous())) {
            // Los usuarios anónimos no tienen historial de acceso
            return null;
        }
        final String userAgent = browser.getUserAgent();
        final String ipAddress = browser.getIpAddress();
        AccessHistory h = new AccessHistory(-1L, userId, new Date(), null, userAgent, ipAddress);
        if (loginSource != null) {
            h.setLoginSource(loginSource.getValue());
        }
        if (oidcProvider != null) {
            h.setOidcProvider(oidcProvider.getValue());
        }
        h.setOidcId(oidcId);
        h.setToken(token);
        if (location != null) {
            location.setId(-1L);
            final GeolocationCoordinates savedLocation = dao.makePersistent(location, userId);
            h.setLocationId(savedLocation.getId());
        }
        if (screenResolution != null) {
            updateEntityWithScreenResolutionData(screenResolution, h);
        }
        return dao.makePersistent(h, userId);
    }

    private static void updateEntityWithScreenResolutionData(ScreenResolutionDTO screenResolution, AccessHistory h) {
        h.setPageWidth(Utilities.formatDouble(screenResolution.getPageWidth(), 2));
        h.setPageHeight(Utilities.formatDouble(screenResolution.getPageHeight(), 2));
        h.setScreenWidth(Utilities.formatDouble(screenResolution.getScreenWidth(), 2));
        h.setScreenHeight(Utilities.formatDouble(screenResolution.getScreenHeight(), 2));
        h.setDevicePixelRatio(Utilities.formatDouble(screenResolution.getDevicePixelRatio(), 2));
        h.setColorDepth(Utilities.formatDouble(screenResolution.getColorDepth(), 2));
        h.setPixelDepth(Utilities.formatDouble(screenResolution.getPixelDepth(), 2));
        h.setDeviceOrientation(screenResolution.getOrientation());
    }

    public static boolean updateLocation(GeolocationCoordinates location, ILoggedUser loggedUser, HttpSession session) {
        final UserLogin userLogin = SessionHelper.getUserLoginInfo(session);
        if (userLogin == null) {
            LOGGER.error("Failed to update location as the user is not logged in");
            return false;
        }
        if (Boolean.TRUE.equals(userLogin.getProvidedLocation())) {
            LOGGER.error("Failed to update location as the location is already set");
            return false;
        }
        final Long accessHistoryId = userLogin.getAccessHistoryId();
        if (accessHistoryId == null || accessHistoryId <= 0) {
            LOGGER.error("Failed to update location as the access history id is not available");
            return false;
        }
        userLogin.setProvidedLocation(true);
        final IUntypedDAO dao = Utilities.getBean(IUntypedDAO.class);
        final AccessHistory accessHistory = dao.HQLT_findById(AccessHistory.class, accessHistoryId);
        if (accessHistory.getLocationId() != null && accessHistory.getLocationId() > 0) {
            LOGGER.error("Failed to update location as the location is already set");
            return false;
        }
        location.setId(-1L);
        final GeolocationCoordinates savedLocation = dao.makePersistent(location, loggedUser.getId());
        accessHistory.setLocationId(savedLocation.getId());
        accessHistory.setLocation(savedLocation);
        return dao.makePersistent(accessHistory, loggedUser.getId()) != null;
    }

    public static boolean updateScreenResolution(ScreenResolutionDTO data, ILoggedUser loggedUser, HttpSession session) {
        final UserLogin userLogin = SessionHelper.getUserLoginInfo(session);
        if (userLogin == null) {
            LOGGER.error("Failed to update screen resolution as the user is not logged in");
            return false;
        }
        if (Boolean.TRUE.equals(userLogin.getProvidedScreenResolution())) {
            LOGGER.error("Failed to update screen resolution as the screen resolution is already set");
            return false;
        }
        final Long accessHistoryId = userLogin.getAccessHistoryId();
        if (accessHistoryId == null || accessHistoryId <= 0) {
            LOGGER.error("Failed to screen resolution as the access history id is not available");
            return false;
        }
        userLogin.setProvidedScreenResolution(true);
        final IUntypedDAO dao = Utilities.getBean(IUntypedDAO.class);
        final AccessHistory accessHistory = dao.HQLT_findById(AccessHistory.class, accessHistoryId);
        if (accessHistory.getScreenWidth() != null) {
            LOGGER.error("Failed to update screen resolution as the screen resolution is already set");
            return false;
        }
        updateEntityWithScreenResolutionData(data, accessHistory);
        return dao.makePersistent(accessHistory, loggedUser.getId()) != null;
    }

}
