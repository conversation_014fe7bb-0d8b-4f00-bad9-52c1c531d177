package qms.access.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ProfileLicensePK implements Serializable {

    private String licenseCode;
    private String licenseService;

    public ProfileLicensePK(String licenseCode, String licenseService) {
        this.licenseCode = licenseCode;
        this.licenseService = licenseService;
    }

    public ProfileLicensePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "license_code")
    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "license_service")
    public String getLicenseService() {
        return licenseService;
    }

    public void setLicenseService(String licenseService) {
        this.licenseService = licenseService;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.licenseCode);
        hash = 17 * hash + Objects.hashCode(this.licenseService);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ProfileLicensePK other = (ProfileLicensePK) obj;
        if (!Objects.equals(this.licenseCode, other.licenseCode)) {
            return false;
        }
        if (!Objects.equals(this.licenseService, other.licenseService)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ProfileLicensePK{" + "licenseCode=" + licenseCode + ", licenseService=" + licenseService + '}';
    }

}
