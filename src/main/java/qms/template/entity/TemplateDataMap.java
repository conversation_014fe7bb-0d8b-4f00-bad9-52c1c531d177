/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_data_map")
@JsonPropertyOrder(alphabetic = true)
public class TemplateDataMap extends TemplateDataMapSuperclass<String> implements IAuditable {

    public TemplateDataMap() {
    }

    public TemplateDataMap(Integer deleted, String fieldName, String fieldValue) {
        super(deleted, fieldName, fieldValue);
    }

    @Override
    public String getFieldValue() {
        return this.fieldValue;
    }

    @Override
    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }
    
}
