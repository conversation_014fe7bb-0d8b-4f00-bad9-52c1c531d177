/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.template.dto.TemplateGenericEntityDTO;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_generic_entity")
@JsonPropertyOrder(alphabetic = true)
public class TemplateCommentEntity extends TemplateGenericEntitySuperclass implements IAuditable, ICommentEntity {

    private String stage;

    public TemplateCommentEntity() {
        this.id = -1L;
    }

    public TemplateCommentEntity(TemplateGenericEntityDTO generic) {
        this.id = generic.getId();
        this.createdDate = generic.getCreatedDate();
        this.lastModifiedDate = generic.getLastModifiedDate();
        this.createdBy = generic.getCreatedBy();
        this.lastModifiedBy = generic.getLastModifiedBy();
        this.description = generic.getDescription();
        this.stage = generic.getStage();
    }
    
    

    @Column(name = "stage")
    @Override
    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    @Override
    @Column(name = "generic_entity_type")
    @JsonIgnore
    public String getGenericEntityType() {
        return "ICommentEntity";
    }
}
