/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import Framework.Config.DomainObject;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_variable_item")
@JsonPropertyOrder(alphabetic = true)
public class TemplateVariableItem extends DomainObject implements IAuditable  {
    
    private Boolean dragging;
    private Boolean mergeable;
    private Integer deleted;
    private String code;
    private String description;
    private String type;
    private String iconName;
    // Valores
    private String valueUserItemSerialized;
    private String valueString;
    private Date valueDate;
    private Long valueNumber;
    // IAuditable
    private Long createdBy;
    private Long lastModifiedBy;
    private Date createdDate;
    private Date lastModifiedDate;
    
    // Template
    private TemplateActivityTemplate template; // <-- @JsonIgnore

    public TemplateVariableItem() {
        this.id = -1L;
        this.deleted = 0;
    }

    @Id
    @Column(name = "template_variable_item_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    @JsonIgnore
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "deleted")
    @JsonIgnore
    public Integer getDeleted() {
        return this.deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    @JsonIgnore
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Override
    @CreatedDate
    @JsonIgnore
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @JsonIgnore
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @JsonIgnore
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "type")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "icon_name")
    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    @Column(name = "value_user_item")
    @JsonIgnore
    public String getValueUserItemSerialized() {
        return valueUserItemSerialized;
    }

    public void setValueUserItemSerialized(String valueUserItemSerialized) {
        this.valueUserItemSerialized = valueUserItemSerialized;
    }

    @Column(name = "value_string")
    public String getValueString() {
        return valueString;
    }

    public void setValueString(String valueString) {
        this.valueString = valueString;
    }

    @Column(name = "value_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getValueDate() {
        return valueDate;
    }

    public void setValueDate(Date valueDate) {
        this.valueDate = valueDate;
    }

    @Column(name = "value_number")
    public Long getValueNumber() {
        return valueNumber;
    }

    public void setValueNumber(Long valueNumber) {
        this.valueNumber = valueNumber;
    }

    @Column(name = "mergeable")
    public Boolean getMergeable() {
        return mergeable;
    }

    public void setMergeable(Boolean mergeable) {
        this.mergeable = mergeable;
    }

    @Column(name = "dragging")
    public Boolean getDragging() {
        return dragging;
    }

    public void setDragging(Boolean dragging) {
        this.dragging = dragging;
    }

    @JsonIgnore
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "template_activity_template_id")
    public TemplateActivityTemplate getTemplate() {
        return template;
    }

    public void setTemplate(TemplateActivityTemplate template) {
        this.template = template;
    }

    @Transient
    public Map<String, Object> getValueUserItem() {
        if (this.valueUserItemSerialized == null) {
            return null;
        }
        return (Map) Utilities.parse(this.valueUserItemSerialized);
    }

    public void setValueUserItem(TextLongValue valueUser) {
        if (valueUser == null) {
            this.valueUserItemSerialized = null;
        } else {
            this.valueUserItemSerialized = Utilities.getSerializedObj(valueUser);
        }
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.dragging);
        hash = 47 * hash + Objects.hashCode(this.mergeable);
        hash = 47 * hash + Objects.hashCode(this.code);
        hash = 47 * hash + Objects.hashCode(this.description);
        hash = 47 * hash + Objects.hashCode(this.type);
        hash = 47 * hash + Objects.hashCode(this.valueUserItemSerialized);
        hash = 47 * hash + Objects.hashCode(this.valueString);
        hash = 47 * hash + Objects.hashCode(this.valueDate);
        hash = 47 * hash + Objects.hashCode(this.valueNumber);
        hash = 47 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateVariableItem other = (TemplateVariableItem) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.description, other.description)) {
            return false;
        }
        if (!Objects.equals(this.type, other.type)) {
            return false;
        }
        if (!Objects.equals(this.valueUserItemSerialized, other.valueUserItemSerialized)) {
            return false;
        }
        if (!Objects.equals(this.valueString, other.valueString)) {
            return false;
        }
        if (!Objects.equals(this.dragging, other.dragging)) {
            return false;
        }
        if (!Objects.equals(this.mergeable, other.mergeable)) {
            return false;
        }
        if (!Objects.equals(this.valueDate, other.valueDate)) {
            return false;
        }
        if (!Objects.equals(this.valueNumber, other.valueNumber)) {
            return false;
        }
        return Objects.equals(this.id, other.id);
    }

    
}
