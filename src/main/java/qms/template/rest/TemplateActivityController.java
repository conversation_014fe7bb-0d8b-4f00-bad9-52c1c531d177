/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.rest;

import bnext.exception.MakePersistentException;
import com.sun.star.auth.InvalidArgumentException;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.template.entity.TemplateActivityTemplate;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 * <AUTHOR>
 */
@Lazy
@RestController
@RequestMapping("rest/template")
public class TemplateActivityController extends TemplateBaseController {

    private final String HAS_SAVE_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_CREATOR', 'ACTIVITY_TEMPLATE_CREATOR')";


    @PostMapping("activity/data-source/mine")
    @PreAuthorize(HAS_SAVE_ACCESS)
    public GridInfo<Map<String, Object>> mine(@RequestBody GridFilter filter) {
        return super.mine(filter, Module.ACTIVITY);
    }


    @PostMapping("activity/data-source/availableTemplates")
    @PreAuthorize(HAS_SAVE_ACCESS)
    public GridInfo<Map<String, Object>> availableTemplates(@RequestBody GridFilter filter) {
        filter.getCriteria().put("<condition>", "c.status = 1");
        GridInfo<Map<String, Object>> result = super.mine(filter, Module.ACTIVITY);
        return result;
    }


    @GetMapping({"activity/load/{templateActivityTemplateId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    public ResponseEntity<TemplateActivityTemplate> load(
            @PathVariable(value = "templateActivityTemplateId") Long templateActivityTemplateId
    ) throws QMSException {
        return super.load(templateActivityTemplateId);
    }

    @PostMapping({"activity/toogle-status/{templateActivityTemplateId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    public ResponseEntity<Map<String, Object>> toogleStatus(
            @PathVariable(value = "templateActivityTemplateId") Long templateActivityTemplateId
    ) {
        return super.toogleStatus(templateActivityTemplateId);
    }

    @PostMapping({"activity/save"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    public ResponseEntity save(
            @RequestBody TemplateActivityTemplate template
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return super.save(template);
    }

}
