/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.workflow.util;

import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.math.BigInteger;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import qms.form.dto.SequenceDetailDTO;

/**
 *
 * <AUTHOR>
 */
public class SequenceDetailGenerator {
    private static final String SQL_REQUEST_FILL_DETAILS = ""
            + getSequenceDetailQuerySQL(WorkflowSupported.REQUEST)
            + " SELECT "
                + " sq.indice AS sequenceIndex,"
                + " sq.intestado AS status,"
                + " u.first_name AS userDescription,"
                + " u.user_id as userId"
            + " FROM sequence_detail_provider sq "
            + " JOIN users u"
            + " ON u.user_id = sq.user_id"
            + " WHERE sq.request_id = :requestId"
            + " AND u.status = " + User.STATUS.ACTIVE.getValue()
            + " GROUP BY"
                + " sq.indice,"
                + " sq.intestado,"
                + " u.first_name,"
                + " u.user_id";

    private SequenceDetailGenerator() {}
    
    /**
     * !ToDo: Crear versión HQL!
     * 
     * Otra opción podría ser crear
     * (necesario para implementación de `ToAuthorizeRequest.java`)
     * 
     * @param field
     * @return 
     */
    public static String getSequenceDetailQuerySQL(WorkflowSupported field) {
        final String sql = replaceKeys(field, ISequenceDetailView.SEQUENCE_DETAIL_SQL);
        return sql;
    }
    
    public static String getSequenceDetailProviderQuerySQL(WorkflowSupported field) {
        return replaceKeys(field, ISequenceDetailProviderView.SEQUENCE_DETAIL_PROVIDER_SQL);
    }
    
    private static final String TO_VERIFY_REQUEST = ""
            + " FROM"
                + " ${entityClazz} request "
                + " CROSS JOIN " + User.class.getCanonicalName() + " usr "
            + " LEFT JOIN request.businessUnitDepartment dep"
            + " \n--[CUSTOM_JOIN]\n "
            + " WHERE"
                + " request.deleted = 0 "
                + " AND request.status = " + WorkflowRequestStatus.VERIFING.getValue()
                + " AND usr.status = " + User.STATUS.ACTIVE.getValue()
                + " AND usr.deleted = 0 "
            + "";
  
  
    public static String getToVerifyRequestQuery(WorkflowSupported field) {
        return getToVerifyRequestQuery(field, "", "");
    }
  
    public static String getToVerifyRequestQuery(WorkflowSupported field, String where) {
        return getToVerifyRequestQuery(field, "", where);
    }
  
    public static String getToVerifyRequestQuery(WorkflowSupported field, String join, String where) {
        return replaceKeys(field, TO_VERIFY_REQUEST).replace(""
        + " \n--[CUSTOM_JOIN]\n", join) + " " + where;
    }
  
    public static String getRequestAuthorizatorNames(WorkflowSupported field, Long requestId) {
        return getRequestAuthorizatorNames(field, null, requestId);
    }

    public static String getRequestAuthorizatorNames(WorkflowSupported field, IUntypedDAO dao, Long requestId) {
        if (dao == null) {
            dao = Utilities.getUntypedDAO();
        }
        final List<String> results = dao.SQL_findByQuery(""
                + getSequenceDetailQuerySQL(field)
                + " SELECT sq.user_description"
                + " FROM sequence_detail sq "
                + " WHERE sq.request_id = :requestId"
                + " ORDER BY"
                    + " sq.indice ASC,"
                    + " sq.request_id DESC "
                ,
                "requestId", requestId
        );
        if (results == null || results.isEmpty()) {
            return null;
        }
        final String result = StringUtils.join(new LinkedHashSet<>(results), ", ");
        return result;
    }
   
    public static List<SequenceDetailDTO> getRequestFillDetails(Long requestId) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final List<Map<String, Object>> results = dao.SQL_findMap(SQL_REQUEST_FILL_DETAILS,ImmutableMap.of("requestId", requestId));
        final List<SequenceDetailDTO> details = results.stream()
                .map((record) -> {
                    final Integer sequenceIndex = switch (record.get("sequenceIndex")) {
                        case Short s -> s.intValue();
                        case Integer i -> i;
                        default -> null;
                    };
                    final Long userId = switch (record.get("userId")) {
                        case Long l -> l;
                        case BigInteger i -> i.longValue();
                        default -> null;
                    };
                    return new SequenceDetailDTO(
                            sequenceIndex,
                            (Integer) record.get("status"),
                            (String) record.get("userDescription"), 
                            userId
                    );
                })
                .collect(Collectors.toList());
        return details;
    }
    
    private static String replaceKeys(WorkflowSupported field, String query) {
        // ToDo: Agregar caché de la consulta en el metodo que llamó a este!
        final String result = field.replaceEntityClazz(
                field.replaceDeletedColumn(
                        field.replaceDatabaseWorkflowId(
                                field.replaceCreatedById(
                                        field.replaceDatabaseId(
                                                field.replaceDatabaseTable(
                                                        field.replaceDatabaseWorkflowPreviewTable(query)
                                                )
                                        )
                                )
                        )
                )
        );
        return result;
    }
}
