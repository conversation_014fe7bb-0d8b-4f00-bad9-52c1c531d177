package qms.workflow.entity;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = WorkflowRequestFirstUser.WORKFLOW_REQUEST_FIRST_USER_SQL)
@Immutable
public class WorkflowRequestFirstUser extends CompositeStandardEntity<WorkflowRequestFirstUserPK> {

    public static final String WORKFLOW_REQUEST_FIRST_USER_SQL = " ("
            + " SELECT" +
                " w.request_id AS request_id" +
                ", w.indice AS indice" +
                ", w.deleted AS deleted" +
                ", MAX(w.user_id) as user_id"
            + " FROM workflow_preview_data w"
            + " GROUP BY w.request_id, w.indice, w.deleted"
            + ")";

    private WorkflowRequestFirstUserPK id;

    private Long requestId;
    private Long workflowIndex;
    private Long deleted;
    private Long userId;

    public WorkflowRequestFirstUser() {
    }

    @Override
    public WorkflowRequestFirstUserPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public WorkflowRequestFirstUserPK getId() {
        return id;
    }

    @Override
    public void setId(WorkflowRequestFirstUserPK id) {
        this.id = id;
    }

    @Column(name = "request_id", updatable = false, insertable = false)
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "indice", updatable = false, insertable = false)
    public Long getWorkflowIndex() {
        return workflowIndex;
    }

    public void setWorkflowIndex(Long workflowIndex) {
        this.workflowIndex = workflowIndex;
    }

    @Column(name = "deleted", updatable = false, insertable = false)
    public Long getDeleted() {
        return deleted;
    }

    public void setDeleted(Long deleted) {
        this.deleted = deleted;
    }

    @Column(name = "user_id", updatable = false, insertable = false)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
