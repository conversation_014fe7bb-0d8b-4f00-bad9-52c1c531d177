
package qms.workflow.entity;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.workflow.util.IWorkflowPreview;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@Immutable
@Table(name = "workflow_form_request_data")
public class WorkflowFormRequestData
       extends WorkflowPreview  implements Serializable, IWorkflowPreview {

    private static final long serialVersionUID = -8371665743346569839L;
    
    private Long formRequestId;
    private Long requestId;

    @Column(name = "form_request_id")
    public Long getFormRequestId() {
        return formRequestId;
    }

    public void setFormRequestId(Long formRequestId) {
        this.formRequestId = formRequestId;
    }   

    @Column(name = "form_request_id", updatable = false, insertable = false)
    @Override
    public Long getRequestId() {
        return requestId;
    }

    @Override
    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }
    
}
