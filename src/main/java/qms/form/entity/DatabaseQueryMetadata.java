
package qms.form.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.CodePrefix;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "database_query_metadata")
@CodePrefix("databaseQueryMetadata")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class DatabaseQueryMetadata extends StandardEntity<DatabaseQueryMetadata> implements IAuditableEntity {

    private static final long serialVersionUID = 1L;
    
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private Long databaseQueryId;
    private Long databaseQueryColumnId;
    private Long dynamicFieldTableId;
    private String dynamicFieldTableName;
    private String columnCode;

    public DatabaseQueryMetadata() {
    }
    
    
    public DatabaseQueryMetadata(Long id) {
        this.id = id;
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "database_query_metadata_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @CreatedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "database_query_id")
    public Long getDatabaseQueryId() {
        return databaseQueryId;
    }

    public void setDatabaseQueryId(Long databaseQueryId) {
        this.databaseQueryId = databaseQueryId;
    }

    @Column(name = "query_column_id")
    public Long getDatabaseQueryColumnId() {
        return databaseQueryColumnId;
    }

    public void setDatabaseQueryColumnId(Long databaseQueryColumnId) {
        this.databaseQueryColumnId = databaseQueryColumnId;
    }

    @Column(name = "column_code")
    public String getColumnCode() {
        return columnCode;
    }

    public void setColumnCode(String columnCode) {
        this.columnCode = columnCode;
    }

    @Column(name = "dynamic_field_table_id")
    public Long getDynamicFieldTableId() {
        return dynamicFieldTableId;
    }

    public void setDynamicFieldTableId(Long dynamicFieldTableId) {
        this.dynamicFieldTableId = dynamicFieldTableId;
    }

    @Column(name = "dynamic_field_table_name")
    public String getDynamicFieldTableName() {
        return dynamicFieldTableName;
    }

    public void setDynamicFieldTableName(String dynamicFieldTableName) {
        this.dynamicFieldTableName = dynamicFieldTableName;
    }

}
