package qms.form.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.LastModifiedBy;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "outs_sur_anybody_read")
public class OutstandingSurveysAnybodyRead extends StandardEntity<OutstandingSurveysAnybodyRead> implements DomainObjectInterface, IAuditableEntity {
    private static final long serialVersionUID = 1L;
    
    public static enum STATUS {
        TO_READ(0), 
        IS_READED(1);
        
        private final Integer status;

        private STATUS(Integer status) {
            this.status = status;
        }

        public Integer parseInt() {
            return this.status;
        }
    }
   
    private String code;
    private String description;
    private Integer status = 0;
    private Integer deleted = 0;
    private Long outstandingSurveysId;
    private Long fieldObjectId;
    private Integer currentRecurrence;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
        
    public OutstandingSurveysAnybodyRead() {
    }

    public OutstandingSurveysAnybodyRead(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 50)
    @Column(name = "outs_sur_anybody_read_id", nullable = false, precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @Column(name = "field_object_id")
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }
    
    @Column(name = "current_recurrence")
    public Integer getCurrentRecurrence() {
        return currentRecurrence;
    }

    public void setCurrentRecurrence(Integer currentRecurrence) {
        this.currentRecurrence = currentRecurrence;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    @Column(name = "created_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }
    
    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysAnybodyRead other = (OutstandingSurveysAnybodyRead) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "OutstandingSurveysAnybodyRead{" + "id=" + id + '}';
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.code);
        hash = 97 * hash + Objects.hashCode(this.description);
        hash = 97 * hash + Objects.hashCode(this.status);
        hash = 97 * hash + Objects.hashCode(this.deleted);
        return hash;
    }
    
    
}