
package qms.form.entity;

import DPMS.Mapping.Request;
import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.workflow.util.ISequenceDetailProvider;

/**
 * 
 * ToDo: Implementar consulta de `SequenceDetailGenerator`, ver ejemplo: `SequenceDetailFormRequest`
 * 
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "sequence_detail_provider")
@Immutable
public class SequenceDetailRequest extends DomainObject implements Serializable, ISequenceDetailProvider<Request> {

    private static final long serialVersionUID = 1L;

    private Long requestId;
    private Long workflowId;
    private Long userId;
    private Long positionId;
        	
    private String userDescription;
    private String positionDescription;		
    private Integer userStatus;
    
    private Integer ownerType;
    private Integer autorizationPoolDetailStatus;
    private Long autorizationPoolDetailsId;
    private Integer status;
    private Integer sequenceIndex;
    
    private Request request;

    private Integer requestDeleted;
    private Integer requestStatus;
    private Integer requestType;
    private String requestDocumentCode;
    private String requestDescription;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id", updatable = false, nullable = false)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }
    
    @Override
    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    @Override
    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Override
    @Column(name = "flujo_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    @Override
    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    @Override
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    @Override
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    @Column(name = "puesto_id")
    public Long getPositionId() {
        return positionId;
    }

    @Override
    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    @Override
    @Column(name = "auth_pool_detail_status")
    public Integer getAutorizationPoolDetailStatus() {
        return autorizationPoolDetailStatus;
    }

    @Override
    public void setAutorizationPoolDetailStatus(Integer autorizationPoolDetailStatus) {
        this.autorizationPoolDetailStatus = autorizationPoolDetailStatus;
    }
    
    @Override
    @Column(name = "autorization_pool_detail_id")
    public Long getAutorizationPoolDetailsId() {
        return autorizationPoolDetailsId;
    }

    @Override
    public void setAutorizationPoolDetailsId(Long autorizationPoolDetailsId) {
        this.autorizationPoolDetailsId = autorizationPoolDetailsId;
    }

    @Override
    @Column(name = "intestado")
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    @Column(name = "indice")
    public Integer getSequenceIndex() {
        return sequenceIndex;
    }

    @Override
    public void setSequenceIndex(Integer sequenceIndex) {
        this.sequenceIndex = sequenceIndex;
    }

    @Override
    @Column(name = "type")
    public Integer getOwnerType() {
        return ownerType;
    }

    @Override
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    @Override
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "request_id", referencedColumnName = "id", updatable = false, insertable = false)
    public Request getRequest() {
        return request;
    }

    @Override
    public void setRequest(Request request) {
        this.request = request;
    }

    @Column(name = "request_deleted")
    public Integer getRequestDeleted() {
        return requestDeleted;
    }

    public void setRequestDeleted(Integer requestDeleted) {
        this.requestDeleted = requestDeleted;
    }

    @Column(name = "request_status")
    public Integer getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(Integer requestStatus) {
        this.requestStatus = requestStatus;
    }

    @Column(name = "request_type")
    public Integer getRequestType() {
        return requestType;
    }

    public void setRequestType(Integer requestType) {
        this.requestType = requestType;
    }

    @Column(name = "request_document_code")
    public String getRequestDocumentCode() {
        return requestDocumentCode;
    }

    public void setRequestDocumentCode(String requestDocumentCode) {
        this.requestDocumentCode = requestDocumentCode;
    }

    @Column(name = "request_description")
    public String getRequestDescription() {
        return requestDescription;
    }

    public void setRequestDescription(String requestDescription) {
        this.requestDescription = requestDescription;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 61 * hash + Objects.hashCode(this.requestId);
        hash = 61 * hash + Objects.hashCode(this.workflowId);
        hash = 61 * hash + Objects.hashCode(this.status);
        hash = 61 * hash + Objects.hashCode(this.sequenceIndex);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SequenceDetailRequest other = (SequenceDetailRequest) obj;
        if (!Objects.equals(this.requestId, other.getRequestId())) {
            return false;
        }
        if (!Objects.equals(this.workflowId, other.getWorkflowId())) {
            return false;
        }
        if (!Objects.equals(this.userId, other.getUserId())) {
            return false;
        }
        if (!Objects.equals(this.positionId, other.getPositionId())) {
            return false;
        }
        if (!Objects.equals(this.ownerType, other.getOwnerType())) {
            return false;
        }
        if (!Objects.equals(this.autorizationPoolDetailStatus, other.getAutorizationPoolDetailStatus())) {
            return false;
        }
        if (!Objects.equals(this.status, other.getStatus())) {
            return false;
        }
        return Objects.equals(this.sequenceIndex, other.getSequenceIndex());
    }


    @Column(name = "user_name")
    public String getUserDescription() {
        return userDescription;
    }

    public void setUserDescription(String userDescription) {
        this.userDescription = userDescription;
    }

    @Column(name = "position_description")
    public String getPositionDescription() {
        return positionDescription;
    }

    public void setPositionDescription(String positionDescription) {
        this.positionDescription = positionDescription;
    }
    
    @Column(name = "user_status")
    public Integer getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(Integer userStatus) {
        this.userStatus = userStatus;
    }
    

    @Override
    public String toString() {
        return "SequenceDetailRequest{" 
                + "requestId=" + requestId 
                + ", workflowId=" + workflowId 
                + ", userId=" + userId 
                + ", positionId=" + positionId 
                + ", ownerType=" + ownerType
                + ", autorizationPoolDetailStatus=" + autorizationPoolDetailStatus 
                + ", status=" + status 
                + ", sequenceIndex=" + sequenceIndex 
                + '}';
    }
    
    
    
}
