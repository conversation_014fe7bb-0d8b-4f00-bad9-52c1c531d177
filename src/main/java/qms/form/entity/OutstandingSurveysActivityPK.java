package qms.form.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class OutstandingSurveysActivityPK implements Serializable {

    private Long outstandingSurveysId;
    private Long activityId;
    private Long fieldId;

    public OutstandingSurveysActivityPK(Long outstandingSurveysId, Long fieldId, Long activityId) {
        this.outstandingSurveysId = outstandingSurveysId;
        this.fieldId = fieldId;
        this.activityId = activityId;
    }

    public OutstandingSurveysActivityPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "field_id")
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 61 * hash + Objects.hashCode(this.outstandingSurveysId);
        hash = 61 * hash + Objects.hashCode(this.activityId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysActivityPK other = (OutstandingSurveysActivityPK) obj;
        if (!Objects.equals(this.outstandingSurveysId, other.outstandingSurveysId)) {
            return false;
        }
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "OutstandingSurveysActivityPK{" + "outstandingSurveysId=" + outstandingSurveysId + ", activityId=" + activityId + '}';
    }

}
