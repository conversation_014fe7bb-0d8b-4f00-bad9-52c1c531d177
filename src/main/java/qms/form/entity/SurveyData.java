package qms.form.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.interfaces.ISurveyData;
import java.util.Date;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import qms.form.dto.SurveyDataFieldDTO;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_data")
public class SurveyData extends DomainObject implements ISurveyData {
    
    private static final long serialVersionUID = 1L;

    private Integer answerType;
    private Integer weightingType;
    private Boolean signRejectApproval;
    private Boolean allowDeleteStopwatchRecord;
    private Date creationDate;
    private Date lastModificationDate;
    private Integer cancelByResponsible;
    private Integer daysToExpire;
    private Integer daysToNotifyBeforeExpiration;
    private Integer defaultDateType;
    private Integer fieldOrder;
    private Integer includeInMail;
    private Integer includeInSubject;
    private Integer required;
    private Integer surveyDeleted;
    private Integer surveyQuestionCount;
    private Integer surveyStatus;
    private Long attendProgressStateId;
    private Long externalCatalogId;
    private Long fieldId;
    private Long fieldObjectId;
    private Long fillBusinessUnitPositionId;
    private Long fillOUnitPositionId;
    private Long fillUserId;
    private Long internalCatalogId;
    private Long optionId;
    private Integer optionSummation;
    private Integer optionAnswerType;
    private Long parentFieldId;
    private Long sectionId;
    private Long surveyAuthorId;
    private Long surveyId;
    private Long tableHeaderId;
    private Short otherOption;
    private String catalogSubType;
    private String catalogType;
    private String defaultValue;
    private String fieldCode;
    private String fieldQuestionDesc;
    private String fieldQuestionHDesc;
    private String fieldQuestionHSubdesc;
    private String fieldQuestionSubdesc;
    private String fieldStage;
    private String fieldType;
    private String fillBusinessUnitPosition;
    private String fillEntity;
    private String fillOUnitPosition;
    private String fillUser;
    private Boolean includeTime;
    private Boolean restrictPastDates;
    private Integer maxPastHours;
    private String optionCode;
    private String optionDesc;
    private String optionWeight;
    private String otherOptionTitle;
    private String sectionDesc;
    private String sectionHDesc;
    private String surveyCode;
    private String surveyDesc;
    private String surveyType;
    private String tableHeaderCode;
    private String fixedQrUrl; 
    private String qrUrlType;
    private String surveyAnswersColumns;
    private Set<SurveyDataFieldDTO> associatedFields;
    private Boolean disableColumnPerOption; // To avoid creating a column per option in the SURVEY_ANSWER table.

    @Id
    @Basic
    @Column(name = "id")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Override
    @Column(name = "answer_type")
    public Integer getAnswerType() {
        return answerType;
    }
    
    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

    @Override
    @Column(name = "weighting_type")
    public Integer getWeightingType() {
        return weightingType;
    }

    public void setWeightingType(Integer weightingType) {
        this.weightingType = weightingType;
    }

    @Column(name = "external_catalog_id")
    @Override
    public Long getExternalCatalogId() {
        return externalCatalogId;
    }

    public void setExternalCatalogId(Long externalCatalogId) {
        this.externalCatalogId = externalCatalogId;
    }

    @Column(name = "days_to_expire")
    public Integer getDaysToExpire() {
        return daysToExpire;
    }

    public void setDaysToExpire(Integer daysToExpire) {
        this.daysToExpire = daysToExpire;
    }

    @Column(name = "days_to_notify_before_expiration")
    public Integer getDaysToNotifyBeforeExpiration() {
        return daysToNotifyBeforeExpiration;
    }

    public void setDaysToNotifyBeforeExpiration(Integer daysToNotifyBeforeExpiration) {
        this.daysToNotifyBeforeExpiration = daysToNotifyBeforeExpiration;
    }
    
    @Column(name = "include_in_mail")
    @Override
    public Integer getIncludeInMail() {
        return includeInMail;
    }

    public void setIncludeInMail(Integer includeInMail) {
        this.includeInMail = includeInMail;
    }
    
    @Column(name = "include_in_subject")
    @Override
    public Integer getIncludeInSubject() {
        return includeInSubject;
    }

    public void setIncludeInSubject(Integer includeInSubject) {
        this.includeInSubject = includeInSubject;
    }

    @Column(name = "internal_catalog_id")
    public Long getInternalCatalogId() {
        return internalCatalogId;
    }

    public void setInternalCatalogId(Long internalCatalogId) {
        this.internalCatalogId = internalCatalogId;
    }

    @Column(name = "fill_business_unit_position_id")
    public Long getFillBusinessUnitPositionId() {
        return fillBusinessUnitPositionId;
    }

    public void setFillBusinessUnitPositionId(Long fillBusinessUnitPositionId) {
        this.fillBusinessUnitPositionId = fillBusinessUnitPositionId;
    }

    @Column(name = "fill_o_unit_position_id")
    public Long getFillOUnitPositionId() {
        return fillOUnitPositionId;
    }

    public void setFillOUnitPositionId(Long fillOUnitPositionId) {
        this.fillOUnitPositionId = fillOUnitPositionId;
    }

    @Column(name = "fill_user_id")
    public Long getFillUserId() {
        return fillUserId;
    }

    public void setFillUserId(Long fillUserId) {
        this.fillUserId = fillUserId;
    }

    @Column(name = "survey_id")
    @Override
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Column(name = "survey_author_id")
    public Long getSurveyAuthorId() {
        return surveyAuthorId;
    }

    public void setSurveyAuthorId(Long surveyAuthorId) {
        this.surveyAuthorId = surveyAuthorId;
    }

    @Column(name = "section_id")
    @Override
    public Long getSectionId() {
        return sectionId;
    }

    @Transient
    @Override
    public Set<SurveyDataFieldDTO> getAssociatedFields() {
        return this.associatedFields;
    }

    @Transient
    @Override
    public Boolean getDisableColumnPerOption() {
        return this.disableColumnPerOption;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    @Column(name = "field_id")
    @Override
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @Column(name = "field_code")
    @Override
    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }
    
    @Column(name = "option_id")
    @Override
    public Long getOptionId() {
        return optionId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }
    
    @Column(name = "option_code")
    @Override
    public String getOptionCode() {
        return optionCode;
    }

    public void setOptionCode(String optionCode) {
        this.optionCode = optionCode;
    }

    @Override   
    @Type(type = "numeric_boolean")
    @Column(name = "include_time")
    public Boolean getIncludeTime() {
        return includeTime;
    }

    public void setIncludeTime(Boolean includeTime) {
        this.includeTime = includeTime;
    }

    @Override  
    @Type(type = "numeric_boolean")
    @Column(name = "restrict_past_dates")
    public Boolean getRestrictPastDates() {
        return restrictPastDates;
    }

    public void setRestrictPastDates(Boolean restrictPastDates) {
        this.restrictPastDates = restrictPastDates;
    }

    @Override
    @Column(name = "max_past_hours")
    public Integer getMaxPastHours() {
        return maxPastHours;
    }

    public void setMaxPastHours(Integer maxPastHours) {
        this.maxPastHours = maxPastHours;
    }

    @Override
    @Column(name = "option_summation")
    public Integer getOptionSummation() {
        return optionSummation;
    }

    public void setOptionSummation(Integer optionSummation) {
        this.optionSummation = optionSummation;
    }

    @Override
    @Column(name = "option_answer_type")
    public Integer getOptionAnswerType() {
        return optionAnswerType;
    }

    public void setOptionAnswerType(Integer itemAnswerType) {
        this.optionAnswerType = itemAnswerType;
    }
    
    @Column(name = "default_date_type")
    public Integer getDefaultDateType() {
        return defaultDateType;
    }

    public void setDefaultDateType(Integer defaultDateType) {
        this.defaultDateType = defaultDateType;
    }

    @Column(name = "survey_status")
    public Integer getSurveyStatus() {
        return surveyStatus;
    }

    public void setSurveyStatus(Integer surveyStatus) {
        this.surveyStatus = surveyStatus;
    }

    @Column(name = "survey_question_count")
    public Integer getSurveyQuestionCount() {
        return surveyQuestionCount;
    }

    public void setSurveyQuestionCount(Integer surveyQuestionCount) {
        this.surveyQuestionCount = surveyQuestionCount;
    }

    @Column(name = "survey_deleted")
    public Integer getSurveyDeleted() {
        return surveyDeleted;
    }

    public void setSurveyDeleted(Integer surveyDeleted) {
        this.surveyDeleted = surveyDeleted;
    }

    @Column(name = "field_order")
    @Override
    public Integer getFieldOrder() {
        return fieldOrder;
    }

    public void setFieldOrder(Integer fieldOrder) {
        this.fieldOrder = fieldOrder;
    }

    @Column(name = "required")
    public Integer getRequired() {
        return required;
    }

    public void setRequired(Integer required) {
        this.required = required;
    }

    @Column(name = "cancel_by_responsible")
    public Integer getCancelByResponsible() {
        return cancelByResponsible;
    }

    public void setCancelByResponsible(Integer cancelByResponsible) {
        this.cancelByResponsible = cancelByResponsible;
    }

    @Column(name = "catalog_type")
    public String getCatalogType() {
        return catalogType;
    }

    public void setCatalogType(String catalogType) {
        this.catalogType = catalogType;
    }

    @Column(name = "catalog_sub_type")
    @Override
    public String getCatalogSubType() {
        return catalogSubType;
    }

    public void setCatalogSubType(String catalogSubType) {
        this.catalogSubType = catalogSubType;
    }

    @Column(name = "default_value")
    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Column(name = "fill_business_unit_position")
    public String getFillBusinessUnitPosition() {
        return fillBusinessUnitPosition;
    }

    public void setFillBusinessUnitPosition(String fillBusinessUnitPosition) {
        this.fillBusinessUnitPosition = fillBusinessUnitPosition;
    }

    @Column(name = "fill_o_unit_position")
    public String getFillOUnitPosition() {
        return fillOUnitPosition;
    }

    public void setFillOUnitPosition(String fillOUnitPosition) {
        this.fillOUnitPosition = fillOUnitPosition;
    }

    @Column(name = "fill_user")
    public String getFillUser() {
        return fillUser;
    }

    public void setFillUser(String fillUser) {
        this.fillUser = fillUser;
    }

    @Column(name = "fill_entity")
    public String getFillEntity() {
        return fillEntity;
    }

    public void setFillEntity(String fillEntity) {
        this.fillEntity = fillEntity;
    }

    @Column(name = "survey_type")
    public String getSurveyType() {
        return surveyType;
    }

    public void setSurveyType(String surveyType) {
        this.surveyType = surveyType;
    }

    @Column(name = "survey_code")
    public String getSurveyCode() {
        return surveyCode;
    }

    public void setSurveyCode(String surveyCode) {
        this.surveyCode = surveyCode;
    }

    @Column(name = "field_type")
    @Override
    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    @Column(name = "option_weight")
    public String getOptionWeight() {
        return optionWeight;
    }

    public void setOptionWeight(String optionWeight) {
        this.optionWeight = optionWeight;
    }

    @Column(name = "creation_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "last_modification_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModificationDate() {
        return lastModificationDate;
    }

    public void setLastModificationDate(Date lastModificationDate) {
        this.lastModificationDate = lastModificationDate;
    }

    @Column(name = "survey_desc")
    public String getSurveyDesc() {
        return surveyDesc;
    }

    public void setSurveyDesc(String surveyDesc) {
        this.surveyDesc = surveyDesc;
    }

    @Column(name = "section_desc")
    @Override
    public String getSectionDesc() {
        return sectionDesc;
    }

    public void setSectionDesc(String sectionDesc) {
        this.sectionDesc = sectionDesc;
    }

    @Column(name = "option_desc")
    @Override
    public String getOptionDesc() {
        return optionDesc;
    }

    public void setOptionDesc(String optionDesc) {
        this.optionDesc = optionDesc;
    }

    @Column(name = "field_question_desc")
    @Override
    public String getFieldQuestionDesc() {
        return fieldQuestionDesc;
    }

    public void setFieldQuestionDesc(String fieldQuestionDesc) {
        this.fieldQuestionDesc = fieldQuestionDesc;
    }

    @Column(name = "field_question_subdesc")
    public String getFieldQuestionSubdesc() {
        return fieldQuestionSubdesc;
    }

    public void setFieldQuestionSubdesc(String fieldQuestionSubdesc) {
        this.fieldQuestionSubdesc = fieldQuestionSubdesc;
    }

    @Column(name = "table_header_id")
    @Override
    public Long getTableHeaderId() {
        return tableHeaderId;
    }

    public void setTableHeaderId(Long tableHeaderId) {
        this.tableHeaderId = tableHeaderId;
    }

    @Column(name = "table_header_code")
    @Override
    public String getTableHeaderCode() {
        return tableHeaderCode;
    }

    public void setTableHeaderCode(String tableHeaderCode) {
        this.tableHeaderCode = tableHeaderCode;
    }

    @Column(name = "field_object_id")
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    @Column(name = "parent_field_id")
    public Long getParentFieldId() {
        return parentFieldId;
    }

    public void setParentFieldId(Long parentFieldId) {
        this.parentFieldId = parentFieldId;
    }

    @Column(name = "section_hdesc")
    public String getSectionHDesc() {
        return sectionHDesc;
    }

    public void setSectionHDesc(String sectionHDesc) {
        this.sectionHDesc = sectionHDesc;
    }

    @Column(name = "field_question_hdesc")
    public String getFieldQuestionHDesc() {
        return fieldQuestionHDesc;
    }

    public void setFieldQuestionHDesc(String fieldQuestionHDesc) {
        this.fieldQuestionHDesc = fieldQuestionHDesc;
    }
    @Column(name = "field_question_hsubdesc")
    public String getFieldQuestionHSubdesc() {
        return fieldQuestionHSubdesc;
    }

    public void setFieldQuestionHSubdesc(String fieldQuestionHSubdesc) {
        this.fieldQuestionHSubdesc = fieldQuestionHSubdesc;
    }

    @Column(name = "other_option_title")
    @Override
    public String getOtherOptionTitle() {
        return otherOptionTitle;
    }

    public void setOtherOptionTitle(String otherOptionTitle) {
        this.otherOptionTitle = otherOptionTitle;
    }

    @Column(name = "other_option")
    @Override
    public Short getOtherOption() {
        return otherOption;
    }

    public void setOtherOption(Short otherOption) {
        this.otherOption = otherOption;
    }

    @Column(name = "attend_progress_state_id")
    @Override
    public Long getAttendProgressStateId() {
        return attendProgressStateId;
    }

    public void setAttendProgressStateId(Long attendProgressStateId) {
        this.attendProgressStateId = attendProgressStateId;
    }

    @Column(name = "field_stage")
    @Override
    public String getFieldStage() {
        return fieldStage;
    }

    @Override
    public void setFieldStage(String fieldStage) {
        this.fieldStage = fieldStage;
    }
    

    @Column(name = "sign_reject_approval")
    @Type(type = "numeric_boolean")
    public Boolean getSignRejectApproval() {
        return signRejectApproval;
    }

    public void setSignRejectApproval(Boolean signRejectApproval) {
        this.signRejectApproval = signRejectApproval;
    }

    @Column(name = "allow_delete_stopwatch_record")
    @Type(type = "numeric_boolean")
    public Boolean getAllowDeleteStopwatchRecord() {
        return allowDeleteStopwatchRecord;
    }

    public void setAllowDeleteStopwatchRecord(Boolean allowDeleteStopwatchRecord) {
        this.allowDeleteStopwatchRecord = allowDeleteStopwatchRecord;
    }

    @Column(name = "fixed_qr_url")
    public String getFixedQrUrl() {
        return fixedQrUrl;
    }

    public void setFixedQrUrl(String fixedQrUrl) {
        this.fixedQrUrl = fixedQrUrl;
    }

    @Column(name = "qr_url_type")
    public String getQrUrlType() {
        return qrUrlType;
    }

    public void setQrUrlType(String qrUrlType) {
        this.qrUrlType = qrUrlType;
    }

    @Column(name = "survey_answers_columns")
    public String getSurveyAnswersColumns() {
        return surveyAnswersColumns;
    }

    public void setSurveyAnswersColumns(String surveyAnswersColumns) {
        this.surveyAnswersColumns = surveyAnswersColumns;
    }

    @Override
    public String toString() {
        return "SurveyData{" + "fieldObjectId=" + fieldObjectId + ", surveyId=" +
                surveyId + ", fieldCode=" + fieldCode + ", optionId=" + optionId +
                ", fieldType=" + fieldType + ", fieldQuestionDesc=" +
                fieldQuestionDesc + '}';
    }

}