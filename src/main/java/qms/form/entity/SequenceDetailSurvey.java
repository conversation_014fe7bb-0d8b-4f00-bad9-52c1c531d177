
package qms.form.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.document.entity.RequestRef;

/**
 * 
 * ToDo: Implementar consulta de `SequenceDetailGenerator`, ver ejemplo: `SequenceDetailFormRequest`
 * 
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "sequence_detail_surveys")
@Immutable
public class SequenceDetailSurvey extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long requestId;
    private Long workflowId;
    private Long userId;
    private Long positionId;
        
    private Integer ownerType;
    private Integer autorizationPoolDetailStatus;
    private Integer status;
    private Integer sequenceIndex;
    private Integer daysToWait;
    
    private RequestRef request;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id", updatable = false, nullable = false)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }
    
    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "flujo_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "puesto_id")
    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    @Column(name = "auth_pool_detail_status")
    public Integer getAutorizationPoolDetailStatus() {
        return autorizationPoolDetailStatus;
    }

    public void setAutorizationPoolDetailStatus(Integer autorizationPoolDetailStatus) {
        this.autorizationPoolDetailStatus = autorizationPoolDetailStatus;
    }

    @Column(name = "intestado")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "indice")
    public Integer getSequenceIndex() {
        return sequenceIndex;
    }

    public void setSequenceIndex(Integer sequenceIndex) {
        this.sequenceIndex = sequenceIndex;
    }

    @Column(name = "days_to_wait")
    public Integer getDaysToWait() {
        return daysToWait;
    }

    public void setDaysToWait(Integer daysToWait) {
        this.daysToWait = daysToWait;
    }
    
    @Column(name = "type")
    public Integer getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "request_id", referencedColumnName = "id", updatable = false, insertable = false)
    public RequestRef getRequest() {
        return request;
    }

    public void setRequest(RequestRef request) {
        this.request = request;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 61 * hash + Objects.hashCode(this.requestId);
        hash = 61 * hash + Objects.hashCode(this.workflowId);
        hash = 61 * hash + Objects.hashCode(this.status);
        hash = 61 * hash + Objects.hashCode(this.sequenceIndex);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SequenceDetailRequest other = (SequenceDetailRequest) obj;
        if (!Objects.equals(this.requestId, other.getRequestId())) {
            return false;
        }
        if (!Objects.equals(this.workflowId, other.getWorkflowId())) {
            return false;
        }
        if (!Objects.equals(this.userId, other.getUserId())) {
            return false;
        }
        if (!Objects.equals(this.positionId, other.getPositionId())) {
            return false;
        }
        if (!Objects.equals(this.ownerType, other.getOwnerType())) {
            return false;
        }
        if (!Objects.equals(this.autorizationPoolDetailStatus, other.getAutorizationPoolDetailStatus())) {
            return false;
        }
        if (!Objects.equals(this.status, other.getStatus())) {
            return false;
        }
        return Objects.equals(this.sequenceIndex, other.getSequenceIndex());
    }

    @Override
    public String toString() {
        return "SequenceDetailRequest{" 
                + "requestId=" + requestId 
                + ", workflowId=" + workflowId 
                + ", userId=" + userId 
                + ", positionId=" + positionId 
                + ", ownerType=" + ownerType
                + ", autorizationPoolDetailStatus=" + autorizationPoolDetailStatus 
                + ", status=" + status 
                + ", sequenceIndex=" + sequenceIndex 
                + '}';
    }
    
    
    
}
