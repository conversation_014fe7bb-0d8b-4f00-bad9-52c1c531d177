package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.interfaces.ISurveyData;
import java.util.Objects;
import java.util.Set;
import qms.custom.core.IDynamicTableField;
import qms.custom.dto.BaseCustomField;
import qms.framework.util.IDatabaseFieldName;

/**
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SurveyDataFieldDTO extends BaseCustomField implements IDynamicTableField<String>, ISurveyData, IDatabaseFieldName {

    private static final long serialVersionUID = 1L;

    private Integer answerType;
    private Integer weightingType;
    private String fieldStage;
    private String sectionDesc;
    private String title;               // <-- El nombre del campo "como" se muestra en pantalla
    private String originalTitle;
    private String subType;

    private Long surveyDataId;
    private Long surveyId;
    private Long fieldObjectId;
    private Long fieldId;
    private String fieldCode;
    private Long optionId;
    private String optionCode;
    private Long tableHeaderId;
    private String tableHeaderCode;
    private Integer answerPartType;
    private String catalogSubType;
    private String catalogLabel;
    private Long externalCatalogId;
    private Long surveyAnswerMetadataId; // <-- Se llena únicamente cuando se usa la bandera `includeMetadata`
    private String optionDesc;
    private String fieldQuestionDesc;
    private String fieldType;
    public Integer fieldOrder;
    public Integer optionSummation;
    private Integer optionAnswerType;
    public Short otherOption;
    public String otherOptionTitle;
    public Long attendProgressStateId;
    public Integer includeInMail;
    public Integer includeInSubject;
    public Long sectionId;
    private Boolean includeTime;
    private Boolean restrictPastDates;
    private Integer maxPastHours;
    private Set<SurveyDataFieldDTO> associatedFields;
    private Boolean disableColumnPerOption; // To avoid creating a column per option in the SURVEY_ANSWER table.
    private Integer showFillOutDate;

    public SurveyDataFieldDTO(String name, String type) {
        super(name, type);
    }

    public SurveyDataFieldDTO(String name, String type, Boolean allowNulls) {
        super(name, type, allowNulls);
    }
    
    public SurveyDataFieldDTO(String name, String type, Boolean allowNulls, Boolean isSystem) {
        super(name, type, allowNulls, isSystem);
    }
    
    public SurveyDataFieldDTO(ISurveyData field) {
        super(field.getId(), null, field.getFieldType(), true);
        this.answerType = field.getAnswerType();
        this.weightingType = field.getWeightingType();
        this.fieldStage = field.getFieldStage();
        this.sectionDesc = field.getSectionDesc();
        this.externalCatalogId = field.getExternalCatalogId();
        this.surveyDataId = field.getId();
        this.surveyId = field.getSurveyId();
        this.fieldId = field.getFieldId();
        this.fieldObjectId = field.getFieldObjectId();
        this.fieldCode = field.getFieldCode();
        this.optionId = field.getOptionId();
        this.optionCode = field.getOptionCode();
        this.tableHeaderId = field.getTableHeaderId();
        this.tableHeaderCode = field.getTableHeaderCode();
        this.title = field.getFieldQuestionDesc();
        this.originalTitle = field.getFieldQuestionDesc();
        this.catalogSubType = field.getCatalogSubType();
        this.optionDesc = field.getOptionDesc();
        this.fieldQuestionDesc = field.getFieldQuestionDesc();
        this.fieldType = field.getFieldType();
        this.fieldOrder = field.getFieldOrder();
        this.optionSummation = field.getOptionSummation();
        this.optionAnswerType = field.getOptionAnswerType();
        this.otherOption = field.getOtherOption();
        this.otherOptionTitle = field.getOtherOptionTitle();
        this.attendProgressStateId = field.getAttendProgressStateId();
        this.includeInMail = field.getIncludeInMail();
        this.includeInSubject = field.getIncludeInSubject();
        this.includeTime = field.getIncludeTime();
        this.restrictPastDates = field.getRestrictPastDates();
        this.maxPastHours = field.getMaxPastHours();
        this.sectionId = field.getSectionId();
        this.associatedFields = field.getAssociatedFields();
        this.disableColumnPerOption = field.getDisableColumnPerOption();
        this.showFillOutDate = field.getShowFillOutDate();
    }
    
    @Override
    public Integer getAnswerType() {
        return answerType;
    }
    
    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

    @Override
    public Integer getWeightingType() {
        return weightingType;
    }

    public void setWeightingType(Integer weightingType) {
        this.weightingType = weightingType;
    }

    public Long getSurveyDataId() {
        return surveyDataId;
    }

    public void setSurveyDataId(Long surveyDataId) {
        this.surveyDataId = surveyDataId;
    }

    @Override
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Override
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @Override
    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }
    
    @Override
    public Long getOptionId() {
        return optionId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }

    @Override
    public String getOptionCode() {
        return optionCode;
    }

    public void setOptionCode(String optionCode) {
        this.optionCode = optionCode;
    }

    @Override
    public Long getTableHeaderId() {
        return tableHeaderId;
    }

    public void setTableHeaderId(Long tableHeaderId) {
        this.tableHeaderId = tableHeaderId;
    }

    @Override
    public String getTableHeaderCode() {
        return tableHeaderCode;
    }

    public void setTableHeaderCode(String tableHeaderCode) {
        this.tableHeaderCode = tableHeaderCode;
    }

    /**
     * Texto de la pregunta
     */
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * Texto de la pregunta, se agrega para mantener el valor original de tittle
     */
    public String getOriginalTitle() {
        return originalTitle;
    }

    public void setOriginalTitle(String originalTitle) {
        this.originalTitle = originalTitle;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public Integer getAnswerPartType() {
        return answerPartType;
    }

    public void setAnswerPartType(Integer answerPartType) {
        this.answerPartType = answerPartType;
    }

    @Override
    public String getCatalogSubType() {
        return catalogSubType;
    }

    public void setCatalogSubType(String catalogSubType) {
        this.catalogSubType = catalogSubType;
    } 

    /**
     * Se utiliza en la pantalla Respuestas de formulario
     * @return 
     */
    public String getCatalogLabel() {
        return catalogLabel;
    }

    public void setCatalogLabel(String catalogLabel) {
        this.catalogLabel = catalogLabel;
    }

    @Override
    public String getFieldStage() {
        return fieldStage;
    }

    @Override
    public void setFieldStage(String fieldStage) {
        this.fieldStage = fieldStage;
    }
    
    /**
     * Se utiliza en la pantalla Respuestas de formulario
     * @return 
     */
    @Override
    public String getSectionDesc() {
        return sectionDesc;
    }
    
    public void setSectionDesc(String sectionDesc) {
        this.sectionDesc = sectionDesc;
    }

    @Override
    public Long getExternalCatalogId() {
        return externalCatalogId;
    }

    public void setExternalCatalogId(Long externalCatalogId) {
        this.externalCatalogId = externalCatalogId;
    }

    public Long getSurveyAnswerMetadataId() {
        return surveyAnswerMetadataId;
    }

    public void setSurveyAnswerMetadataId(Long surveyAnswerMetadataId) {
        this.surveyAnswerMetadataId = surveyAnswerMetadataId;
    }

    @Override
    public String getOptionDesc() {
        return optionDesc;
    }

    public void setOptionDesc(String optionDesc) {
        this.optionDesc = optionDesc;
    }

    @Override
    public String getFieldQuestionDesc() {
        return fieldQuestionDesc;
    }

    public void setFieldQuestionDesc(String fieldQuestionDesc) {
        this.fieldQuestionDesc = fieldQuestionDesc;
    }

    @Override
    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    @Override
    public Integer getFieldOrder() {
        return fieldOrder;
    }

    public void setFieldOrder(Integer fieldOrder) {
        this.fieldOrder = fieldOrder;
    }

    @Override
    public Integer getOptionSummation() {
        return optionSummation;
    }

    public void setOptionSummation(Integer optionSummation) {
        this.optionSummation = optionSummation;
    }

    @Override
    public Integer getOptionAnswerType() {
        return optionAnswerType;
    }

    public void setOptionAnswerType(Integer itemAnswerType) {
        this.optionAnswerType = itemAnswerType;
    }
    
    @Override
    public Short getOtherOption() {
        return otherOption;
    }

    public void setOtherOption(Short otherOption) {
        this.otherOption = otherOption;
    }

    @Override
    public String getOtherOptionTitle() {
        return otherOptionTitle;
    }

    public void setOtherOptionTitle(String otherOptionTitle) {
        this.otherOptionTitle = otherOptionTitle;
    }

    @Override
    public Long getAttendProgressStateId() {
        return attendProgressStateId;
    }

    public void setAttendProgressStateId(Long attendProgressStateId) {
        this.attendProgressStateId = attendProgressStateId;
    }

    @Override
    public Integer getIncludeInMail() {
        return includeInMail;
    }

    public void setIncludeInMail(Integer includeInMail) {
        this.includeInMail = includeInMail;
    }

    @Override
    public Integer getIncludeInSubject() {
        return includeInSubject;
    }

    public void setIncludeInSubject(Integer includeInSubject) {
        this.includeInSubject = includeInSubject;
    }

    @Override
    public Boolean getIncludeTime() {
        return includeTime;
    }

    public void setIncludeTime(Boolean includeTime) {
        this.includeTime = includeTime;
    }

    @Override
    public Boolean getRestrictPastDates() {
        return restrictPastDates;
    }

    public void setRestrictPastDates(Boolean restrictPastDates) {
        this.restrictPastDates = restrictPastDates;
    }

    @Override
    public Integer getMaxPastHours() {
        return maxPastHours;
    }

    public void setMaxPastHours(Integer maxPastHours) {
        this.maxPastHours = maxPastHours;
    }

    @Override
    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public String getDatabaseFieldName() {
        return getName();
    }

    public Set<SurveyDataFieldDTO> getAssociatedFields() {
        return associatedFields;
    }

    @Override
    public Boolean getDisableColumnPerOption() {
        if (this.disableColumnPerOption == null) {
            this.disableColumnPerOption = false;
        }
        return this.disableColumnPerOption;
    }

    public void setDisableColumnPerOption(Boolean disableColumnPerOption) {
        this.disableColumnPerOption = disableColumnPerOption;
    }

    public void setAssociatedFields(Set<SurveyDataFieldDTO> associatedFields) {
        this.associatedFields = associatedFields;
    }

    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    @Override
    public Integer getShowFillOutDate() {
        return showFillOutDate;
    }

    public void setShowFillOutDate(Integer showFillOutDate) {
        this.showFillOutDate = showFillOutDate;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(getName());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyDataFieldDTO other = (SurveyDataFieldDTO) obj;
        if (!Objects.equals(this.answerPartType, other.answerPartType)) {
            return false;
        }
        return Objects.equals(getName(), other.getName());
    }

    @Override
    public String toString() {
        return "SurveyDataFieldDTO{" + "name=" + getName() + ", fieldId=" + fieldId + '}';
    }
    
}
