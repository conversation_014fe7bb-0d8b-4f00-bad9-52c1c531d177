/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SlimReportOrderColumnsDTO implements Comparable<SlimReportOrderColumnsDTO> {
    private String id;
    private String description;
    private String alias;
    private Integer order;
    private FieldType fieldType;

    public enum FieldType {
        FIXED, FLEXI, GHOST, TRANSFORMED, SUMMARY_GROUP
    }

    public SlimReportOrderColumnsDTO() {
    }

    public SlimReportOrderColumnsDTO(String id, String description, Integer order, FieldType fieldType, String alias) {
        this.id = id;
        this.description = description;
        this.order = order;
        this.fieldType = fieldType;
        this.alias = alias;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getFixedField() {
        return fieldType.equals(FieldType.FIXED);
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }

    @Override
    public int compareTo(SlimReportOrderColumnsDTO other) {
            if (other == null || other.getOrder() == null) {
                return 1;
            }
            if (this.order == null) {
                return -1;
            }
            return this.order.compareTo(other.getOrder());
        }
}
