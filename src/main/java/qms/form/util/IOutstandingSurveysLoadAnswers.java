package qms.form.util;

import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import isoblock.surveys.dao.hibernate.IOutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingQuestion;
import isoblock.surveys.dao.hibernate.Survey;
import qms.document.entity.RequestRef;
import qms.util.interfaces.ICommonEntity;

import java.util.Date;
import java.util.List;

public interface IOutstandingSurveysLoadAnswers extends ICommonEntity, IOutstandingSurveys {
    Long getAnswersTableId();

    void setAnswersTableId(Long answersTableId);

    Long getBusinessUnitDepartmentId();

    void setBusinessUnitDepartmentId(Long businessUnitDepartmentId);

    String getCode();

    void setCode(String code);

    Boolean isCreateCopy();

    void setCreateCopy(boolean createCopy);

    Long getCreatorUserId();

    void setCreatorUserId(Long creatorUserId);

    Integer getDeleted();

    void setDeleted(Integer deleted);

    String getDescription();

    void setDescription(String description);

    Long getDocumentId();

    void setDocumentId(Long documentId);

    Date getDteFechaInicio();

    void setDteFechaInicio(Date dteFechaInicio);

    Short getEstatus();

    void setEstatus(Short estatus);

    Long getId();

    void setId(Long id);

    Long getLastModifiedBy();

    void setLastModifiedBy(Long lastModifiedBy);

    Long getCreatedBy();

    void setCreatedBy(Long createdBy);

    Date getLastModifiedDate();

    void setLastModifiedDate(Date lastModifiedDate);

    Date getCreatedDate();

    void setCreatedDate(Date createdDate);

    Long getProgressStateId();

    void setProgressStateId(Long progressStateId);

    Long getRequestId();

    void setRequestId(Long requestId);

    Boolean isSignatureAsSaveBehavior();

    void setSignatureAsSaveBehavior(boolean signatureAsSaveBehavior);

    String getStage();

    void setStage(String stage);

    Integer getStatus();

    void setStatus(Integer status);

    Long getSurveyId();

    void setSurveyId(Long surveyId);

    Survey getCuestionario();

    void setCuestionario(Survey cuestionario);

    List<OutstandingSurveysAttendantLoad> getQuestionAttendants();

    void setQuestionAttendants(List<OutstandingSurveysAttendantLoad> questionAttendants);

    List<OutstandingQuestion> getPreguntasRespondidas();

    void setPreguntasRespondidas(List<OutstandingQuestion> preguntasRespondidas);

    Double getScore();

    void setScore(Double score);

    RequestRef getRequest();

    void setRequest(RequestRef request);

    void setBusinessUnitId(Long businessUnitId);

    Boolean getArchived();

    void setArchived(Boolean archived);

    Integer getSaveAction();

    void setSaveAction(Integer saveAction);
}
