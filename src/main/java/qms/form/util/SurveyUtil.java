package qms.form.util;

import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.Document;
import Framework.Config.PagedQuery;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.PendingHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyItem;
import isoblock.surveys.dao.hibernate.SurveyTextItem;
import isoblock.surveys.dao.interfaces.ISurveyData;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.ITableFactory;
import qms.custom.core.IdFactory;
import qms.custom.entity.DynamicFieldTable;
import qms.form.core.TableFactorySurveyAnswers;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.AnswerPartType;
import qms.form.entity.SurveyAnswerMetadata;
import qms.form.entity.SurveyData;
import qms.form.entity.SurveyDataPreview;
import qms.form.entity.SurveyFieldConfigMail;
import qms.framework.dto.HierarchyFieldDTO;
import qms.framework.entity.QueryColumn;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.util.ModuleUtil;
import qms.util.QMSException;
import qms.util.interfaces.IPagedQuery;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
public class SurveyUtil {
        
    private final static Double ERROR_PER_PAGE = 1000.0;
    public static final String SURVEY_ID = "surveyId";
    public static final String OUTSTANDING_SURVEY_ID = "outstandingSurveyId";
    public static final String ANSWERS_TABLE = "answersTable";
    public static final String ANSWERS_TABLE_PREFFIX = "FORM_";
    public static final String SURVEY_FIELD_ALIAS = "surveyField";
    public static final String EXTRA_WHERE_TOKEN = "/** EXTRA_WHERE_TOKEN **/";
    public static final String EXTRA_COLUMNS_TOKEN = "/** EXTRA_COLUMNS_TOKEN **/";
    public static final String EXTRA_JOINS_TOKEN = "/** EXTRA_JOINS_TOKEN **/";
    public static final Pattern EXTRA_WHERE_PATTERN = Pattern.compile(EXTRA_WHERE_TOKEN.replace("*", "\\*"));
    public static final String UPDATE_SURVEY_ANSWER_TABLE_BLOCKED = ""
        + " UPDATE " + Survey.class.getCanonicalName() + " c "
        + " SET"
            + " c.answersTable = :" + ANSWERS_TABLE + ","
            + " c.estatus = " + Survey.ESTATUS_BLOQUEADO
        + " WHERE c.id = :" + SURVEY_ID
    ;
    private static final Logger LOGGER = Loggable.getLogger(SurveyUtil.class);
    private static final String SLASH = " / ";
    private static final String FREEZE_SURVEY_HQL = ""
        + " UPDATE " + Survey.class.getCanonicalName() + " c "
        + " SET c.isFreezed = 1"
        + " WHERE c.id = :surveyId"
    ;
    public static final String FREEZE_SURVEY_COLUMNS_SQL = ""
        + " attend_progress_state_id "
        + ",cancel_by_responsible "
        + ",catalog_sub_type "
        + ",catalog_type "
        + ",creation_date "
        + ",days_to_expire "
        + ",days_to_notify_before_expiration "
        + ",default_date_type "
        + ",include_time "
        + ",restrict_past_dates "
        + ",max_past_hours "
        + ",default_value "
        + ",external_catalog_id "
        + ",field_code "
        + ",field_id "
        + ",field_object_id "
        + ",field_order "
        + ",field_question_desc "
        + ",field_question_hdesc "
        + ",field_question_hsubdesc "
        + ",field_question_subdesc "
        + ",show_fill_out_date"
        + ",field_stage "
        + ",field_type "
        + ",answer_type "
        + ",weighting_type "
        + ",fill_business_unit_position "
        + ",fill_business_unit_position_id "
        + ",fill_entity "
        + ",fill_o_unit_position "
        + ",fill_o_unit_position_id "
        + ",fill_user "
        + ",fill_user_id "
        + ",include_in_mail "
        + ",include_in_subject "
        + ",internal_catalog_id "
        + ",last_modification_date "
        + ",option_code "
        + ",option_desc "
        + ",option_id "
        + ",option_weight "
        + ",option_summation "
        + ",option_answer_type "
        + ",other_option "
        + ",other_option_title "
        + ",parent_field_id "
        + ",required "
        + ",section_desc "
        + ",section_hdesc "
        + ",section_id "
        + ",sign_reject_approval "
        + ",allow_delete_stopwatch_record "
        + ",survey_author_id "
        + ",survey_code "
        + ",survey_deleted "
        + ",survey_desc "
        + ",survey_id "
        + ",survey_question_count "
        + ",survey_status "
        + ",survey_type "
        + ",table_header_code "
        + ",fixed_qr_url "
        + ",qr_url_type "
        + ",table_header_id "
    ;

    private static final Set<String> HIERARCHY_SYSTEM_COLUMN_FIELDS = ImmutableSet.of(
            "request_id",
            "outstanding_surveys_id",
            "business_unit_id",
            "business_unit_department_id",
            "created_date", "area_id"
    );


    public static final String SURVEY_DAY_PREVIEW_INSERT_ALL =
            " WITH survey_data_preview AS (" + SurveyDataPreview.SURVEY_DATA_PREVIEW + ")"
            + " INSERT INTO survey_data (" +
            FREEZE_SURVEY_COLUMNS_SQL
            + " ) "
            + " SELECT "
            + FREEZE_SURVEY_COLUMNS_SQL + " "
            + " FROM survey_data_preview "
            + " WHERE survey_id = :surveyId";

    public static Integer freezeSurveyData(final IUntypedDAO dao) {
        final Integer insertCounter = dao.SQL_updateByQuery(""
            + " WITH survey_data_preview AS ("
                + SurveyDataPreview.SURVEY_DATA_PREVIEW
                + " )"
            + " INSERT INTO survey_data (" + FREEZE_SURVEY_COLUMNS_SQL + ")"
            + " SELECT " + FREEZE_SURVEY_COLUMNS_SQL
            + " FROM survey_data_preview ",
            Utilities.EMPTY_MAP,
            0,
            Collections.singletonList("survey_data")
        );
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("Survey data has been freezed to {} records.", insertCounter);
        }
        final Integer countUpdated = dao.HQL_updateByQuery(""
            + " UPDATE " + Survey.class.getCanonicalName() + " c "
            + " SET c.isFreezed = 1"
            + " WHERE EXISTS ("
                    + " SELECT d.id"
                    + " FROM " + SurveyData.class.getCanonicalName() + " d"
                    + " WHERE d.surveyId = c.id"
            + " )",
            Utilities.EMPTY_MAP,
            true,
            CacheRegion.SURVEY,
            0
        );
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("Updated {} surveys with isFreezed = 1.", countUpdated);
        }
        return countUpdated;
    }
    
    public static boolean freeze(Long surveyId, IUntypedDAO dao) {
        Integer counter = dao.HQL_findSimpleInteger(""
            + " SELECT count(*)"
            + " FROM " +  SurveyData.class.getCanonicalName() + " c "
            + " WHERE c.surveyId = :surveyId",
            ImmutableMap.of("surveyId", surveyId),
            true,
            CacheRegion.SURVEY,
            0
        );
        if (counter != null && counter > 0) {
            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace("Survey id {} has been already freezed to {} records.", surveyId, counter);
            }
            return false;
        }
        counter = dao.SQL_updateByQuery(
                SURVEY_DAY_PREVIEW_INSERT_ALL,
            ImmutableMap.of("surveyId", surveyId),
            0,
            Collections.singletonList("survey_data")
        );
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace("Survey id {} has been freezed to {} records.", surveyId, counter);
        }
        dao.HQL_updateByQuery(FREEZE_SURVEY_HQL, "surveyId", surveyId);
        return true;
    }

    private static Integer getNextRow(final Long fieldId, final Long headerId, final Map<String, Integer> countRows) {
        final String id = fieldId + "_" + headerId;
        final Integer nextRow;
        if (countRows.get(id) == null) {
            nextRow = 1;
        } else {
            nextRow = countRows.get(id) + 1;
        }
        countRows.put(id, nextRow);
        return nextRow;
    }

    @Nonnull
    public static FieldConfigDTO getTableFields(final Long surveyId, final ILoggedUser loggedUser) {
        final SurveyUtilCache cache = new SurveyUtilCache();
        return getFields(
                surveyId,
                true,
                false,
                false,
                null,
                0,
                cache,
                null,
                loggedUser,
                true,
                true,
                true
        );
    }

    @Nonnull
    public static FieldConfigDTO getAllFields(final Long surveyId, final SurveyUtilCache cache, final ILoggedUser loggedUser) {
        return getFields(
                surveyId,
                true,
                false,
                false,
                null,
                0,
                cache,
                null,
                loggedUser,
                true,
                true,
                false
        );
    }

    @Nonnull
    public static FieldConfigDTO getFields(
            final Long surveyId,
            final boolean includeSections,
            final boolean onlyMailFields,
            final boolean includeMetadata,
            final List<Long> fieldIds,
            final Integer maxResults,
            final ILoggedUser loggedUser,
            final boolean joinOptions,
            final boolean allowOptionValue,
            final boolean checkFieldsExistsTable
    ) {
        final SurveyUtilCache cache = new SurveyUtilCache();
        final Map<String, SurveyFieldConfigMail> indexFields = onlyMailFields ? getMailConfigIndex(surveyId) : null;
        return getFields(
                surveyId,
                includeSections,
                onlyMailFields,
                includeMetadata,
                fieldIds,
                maxResults,
                cache,
                indexFields,
                loggedUser,
                joinOptions,
                allowOptionValue,
                checkFieldsExistsTable
        );
    }

    /**
     *
     * @param joinOptions Si es true transforma los campos items a dos. E.g. field0_value = item0 -> field0_description = Opción 1, caso contrario, genera una columna por item
     * @param allowOptionValue Si es true agrega el field0_value al valor de retorno.
     */
    @Nonnull
    public static FieldConfigDTO getFields(
            final Long surveyId,
            final boolean includeSections,
            final boolean onlyMailFields,
            final boolean includeMetadata,
            final List<Long> fieldIds,
            final Integer maxResults,
            final SurveyUtilCache cache,
            final Map<String, SurveyFieldConfigMail> mailConfigIndex,
            final ILoggedUser loggedUser,
            final boolean joinOptions,
            final boolean allowOptionValue,
            final boolean checkFieldsExistsTable
    ) {
        final IFormCaptureDAO formDao = Utilities.getBean(IFormCaptureDAO.class);
        final Long fieldsCount = formDao.getFreezedFieldsCount(surveyId);
        if (fieldsCount == 0) {
            freeze(surveyId, formDao); 
        }           
        final Set<ISurveyData> datas = formDao.getFreezedDatas(
                surveyId,
                includeSections,
                onlyMailFields,
                fieldIds,
                maxResults
        );
        return parseFieldsConfig(
                surveyId,
                datas,
                includeMetadata,
                0,
                cache,
                mailConfigIndex,
                loggedUser,
                joinOptions,
                allowOptionValue,
                checkFieldsExistsTable
        );
    }


    @Nonnull
    public static Set<SurveyDataFieldDTO> getFields(
            @Nonnull final Long surveyId,
            @Nonnull final List<String> fieldCodes,
            @Nonnull final ILoggedUser loggedUser
    ) {
        final IFormCaptureDAO formDao = Utilities.getBean(IFormCaptureDAO.class);
        final Long fieldsCount = formDao.getFreezedFieldsCount(surveyId);
        if (fieldsCount == 0) {
            freeze(surveyId, formDao);
        }
        final Set<ISurveyData> datas = formDao.getFreezedDatas(
                surveyId,
                fieldCodes
        );
        final SurveyUtilCache cache = new SurveyUtilCache();
        return parseFieldsConfig(
                surveyId,
                datas,
                false,
                0,
                cache,
                null,
                loggedUser,
                true,
                false,
                false
        ).getFields().stream().filter(f ->
            fieldCodes.contains(f.getName())
        ).collect(Collectors.toSet());
    }

    private static Map<String, SurveyFieldConfigMail> getMailConfigIndex(final Long surveyId) {
        final List<SurveyFieldConfigMail> fieldConfig = getFieldConfig(surveyId);
        return fieldConfig.stream()
                .collect(Collectors.toMap(SurveyFieldConfigMail::getFieldColumn, Function.identity()));
    }

    private static void addExternalCatalogHierarchyFields(
            final ISurveyData dataField,
            final SurveyField.TYPE type,
            @Nonnull final SurveyUtilCache cache,
            @Nullable  final Map<String, SurveyFieldConfigMail> indexFields,
            final Set<SurveyDataFieldDTO> result
    ) {
        final List<HierarchyFieldDTO> fields = cache.getHierarchFields(dataField.getExternalCatalogId());
        for (HierarchyFieldDTO field : fields) {
            addExternalCatalogHierarchyField(field, dataField, type, result, indexFields);
        }
    }
    
    private static void addExternalCatalogHierarchyField(
            final HierarchyFieldDTO field,
            final ISurveyData dataField,
            final SurveyField.TYPE type,
            final Set<SurveyDataFieldDTO> result,
            @Nullable final Map<String, SurveyFieldConfigMail> indexFields
    ) {
        final SurveyDataFieldDTO hierachyDataField = getCatalogFieldByPartType(
                field, 
                dataField, 
                type,
                AnswerPartType.CATALOG_VALUE,
                indexFields
        );
        result.add(hierachyDataField);
    }
    
    private static SurveyDataFieldDTO getCatalogFieldByPartType(
            final HierarchyFieldDTO hierarchyField,
            final ISurveyData dataField,
            final SurveyField.TYPE type,
            final AnswerPartType partType,
            @Nullable final Map<String, SurveyFieldConfigMail> indexFields
    ) {
        final SurveyDataFieldDTO field = getFieldInstance(dataField, type, partType);
        field.setName(field.getName() + "_" + hierarchyField.getColumn());
        field.setTitle(field.getTitle());
        field.setOriginalTitle(field.getOriginalTitle());
        field.setCatalogLabel(hierarchyField.getLabel());
        field.setExternalCatalogId(dataField.getExternalCatalogId());
        if (Objects.equals(hierarchyField.getIsEntityKeyId(), true)) {
            field.setType("bigint");
        }
        if (indexFields != null) {
            SurveyFieldConfigMail config = indexFields.get(field.getName());
            if (config != null) {
                field.setIncludeInSubject(config.getIncludeInSubject());
            }
        }
        return field;
    }

    /**
     *
     * @param joinOptions Si es true transforma los campos items a dos. E.g. field0_value = item0 -> field0_description = Opción 1, caso contrario, genera una columna por item
     * @param allowOptionValue Si es true agrega el field0_value al valor de retorno.
     */
    @Nonnull
    public static FieldConfigDTO parseFieldsConfig(
            final Long surveyId,
            final Set<ISurveyData> datas,
            final boolean includeMetadata,
            final Integer maxResults,
            @Nonnull final SurveyUtilCache cache,
            @Nullable final Map<String, SurveyFieldConfigMail> indexFields,
            final ILoggedUser loggedUser,
            final boolean joinOptions,
            final boolean allowOptionValue,
            final boolean checkFieldsExistsTable
    ) {
        if (datas == null || datas.isEmpty()) {
            return new FieldConfigDTO(null, null);
        }
        final Set<SurveyDataFieldDTO> result = new LinkedHashSet<>(datas.size());
        final Map<String, Integer> countRows = new LinkedHashMap<>(datas.size());
        for (final ISurveyData f : datas) {
            final SurveyField.TYPE type = SurveyField.TYPE.fromValue(f.getFieldType());
            if (type == null) {
                LOGGER.warn("Field type not found for fieldId : {}", f.getFieldId());
                continue;
            }
            AnswerPartType answerPartType = AnswerPartType.REGULAR_FIELD;
            if (SurveyField.TYPE.EXTERNAL_CATALOG.equals(type)) {
                if (Objects.equals(f.getCatalogSubType(), CatalogFieldType.CATALOG_HIERARCHY.toString())) {
                    addExternalCatalogHierarchyFields(f, type, cache, indexFields, result);
                    continue;
                }
                answerPartType = AnswerPartType.CATALOG_VALUE;
            }
            final SurveyDataFieldDTO field = getFieldInstance(f, type, answerPartType);
            if (SurveyField.TYPE.EXCLUSIVE_SELECT.equals(type) || SurveyField.TYPE.MENU_SELECT.equals(type)) {
                field.setDisableColumnPerOption(true);
            }
            updateFieldTitle(field, f, type, joinOptions, countRows);
            result.add(field);
            //Se agregan campos adicionales por tipo de pregunta
            switch (type) {
                case EXCLUSIVE_SELECT_YESNO:
                case MULTI_SELECT:
                case EXCLUSIVE_SELECT:
                    //En caso de tener habilitada la opción "otherField" se agrega la columna
                    if (f.getOtherOption() != null && f.getOtherOption() == 1) {
                        final StringBuilder titleBuilder = new StringBuilder(250);
                        SurveyDataFieldDTO otherField = getFieldInstance(f, type, AnswerPartType.OTHER_FIELD);
                        otherField.setSubType(type.getOptionSubtype());
                        otherField.setTitle(titleBuilder
                            .append(f.getFieldQuestionDesc())
                            .append(SLASH)
                            .append(f.getOtherOptionTitle())
                            .toString()
                        );
                        otherField.setOriginalTitle(otherField.getTitle());
                        result.remove(otherField);
                        result.add(otherField);
                    } else {
                        LOGGER.trace("Configuration of field {} doesn't have an 'otherOption' textbox", f);
                    }
                    break;
                case EXTERNAL_CATALOG:
                    SurveyDataFieldDTO catalogDescriptionField = getFieldInstance(f, type, AnswerPartType.CATALOG_DESCRIPTION);
                    result.add(catalogDescriptionField);
                    break;
                case FILE_UPLOAD:
                    final SurveyDataFieldDTO fileNameField = getFieldInstance(f, type, AnswerPartType.FILE_NAME);
                    updateFieldTitle(fileNameField, f, type, joinOptions, countRows);
                    result.add(fileNameField);
                    final SurveyDataFieldDTO fileExtensionField = getFieldInstance(f, type, AnswerPartType.FILE_EXTENSION);
                    updateFieldTitle(fileExtensionField, f, type, joinOptions, countRows);
                    result.add(fileExtensionField);
                    final SurveyDataFieldDTO fileContentTypeField = getFieldInstance(f, type, AnswerPartType.FILE_CONTENT_TYPE);
                    updateFieldTitle(fileContentTypeField, f, type, joinOptions, countRows);
                    result.add(fileContentTypeField);
                    break;
                case STOPWATCH:
                    final SurveyDataFieldDTO timeworkIdField = getFieldInstance(f, type, AnswerPartType.TIMEWORK_ID);
                    updateFieldTitle(timeworkIdField, f, type, joinOptions, countRows);
                    result.add(timeworkIdField);
                    final SurveyDataFieldDTO rangeStartField = getFieldInstance(f, type, AnswerPartType.RANGE_START);
                    updateFieldTitle(rangeStartField, f, type, joinOptions, countRows);
                    result.add(rangeStartField);
                    final SurveyDataFieldDTO rangeEndField = getFieldInstance(f, type, AnswerPartType.RANGE_END);
                    updateFieldTitle(rangeEndField, f, type, joinOptions, countRows);
                    result.add(rangeEndField);
                    final SurveyDataFieldDTO rangeSecondsField = getFieldInstance(f, type, AnswerPartType.RANGE_SECONDS);
                    updateFieldTitle(rangeSecondsField, f, type, joinOptions, countRows);
                    result.add(rangeSecondsField);
                    break;
                case HANDWRITTEN_SIGNATURE:
                    final SurveyDataFieldDTO fileNameDataContent = getFieldInstance(f, type, AnswerPartType.DATA_CONTENT);
                    updateFieldTitle(fileNameDataContent, f, type, joinOptions, countRows);
                    result.add(fileNameDataContent);
                    break;
                case TEXT_DATE:
                    final SurveyDataFieldDTO dayDescriptionField = getFieldInstance(f, type, AnswerPartType.DAY_FIELD);
                    updateFieldTitle(dayDescriptionField, f, type, joinOptions, countRows);
                    result.add(dayDescriptionField);
                    final SurveyDataFieldDTO timezoneField = getFieldInstance(f, type, AnswerPartType.TIMEZONE);
                    updateFieldTitle(timezoneField, f, type, joinOptions, countRows);
                    result.add(timezoneField);
                    break;
                case TEXT_FIELD:
                    addCurrencyConversionFields(f, type, result, joinOptions, countRows);
                    addFillOutDateFields(f, type, result, joinOptions, countRows);
                    break;
                case TEXT_FIELD_ARRAY:
                    if (Objects.equals(f.getOptionSummation(), 1)) {
                        final SurveyDataFieldDTO summaryField = getFieldInstance(f, type, AnswerPartType.SUMMARY_FIELD);
                        if (result.contains(summaryField)) {
                            //Para ordenar al final de las partes del mismo campo
                            summaryField.setFieldId(f.getFieldId());
                        } else {
                            summaryField.setOptionId(null);
                            summaryField.setOptionCode(null);
                            summaryField.setOptionDesc(null);
                            result.add(summaryField);
                        }
                        updateFieldTitle(summaryField, f, type, joinOptions, countRows);
                    }
                    addCurrencyConversionFields(f, type, result, joinOptions, countRows);
                    addFillOutDateFields(f, type, result, joinOptions, countRows);
                    break;
            }
        }
        if (result.isEmpty()) {
            throw new RuntimeException("No fields defined for surveyId : " + surveyId);
        }
        Set<SurveyDataFieldDTO> fieldsReduced = new LinkedHashSet<>();
        result.forEach( f -> {
            // Se juntan los campos de opciones en uno solo cuando está activa joinOptions
            // y el tipo de campo lo permite con disableColumnPerOpttion.
            if (f.getDisableColumnPerOption() && joinOptions) {
                SurveyDataFieldDTO mainField = new SurveyDataFieldDTO(f);
                mainField.setName(f.getFieldCode() + AnswerPartType.CATALOG_VALUE.getSuffix());
                mainField.setOptionId(null);
                mainField.setOptionCode(null);
                mainField.setOptionDesc(null);
                mainField.setAnswerPartType(AnswerPartType.CATALOG_VALUE.getValue());
                // Se verifica si en fields ya esta el nombre del campo y solo se agrega si no (ver equals de clase SurveyDataFieldDTO).
                // Los campos que eran opciones, pasan a guardarse en assosiatedIds.
                if (!fieldsReduced.contains(mainField)) {
                    Set<SurveyDataFieldDTO> associatedFields = result.stream()
                            .filter(f2 -> f2.getFieldCode().equals(mainField.getFieldCode()))
                            .collect(Collectors.toSet());
                    mainField.setAssociatedFields(associatedFields);
                    if (allowOptionValue) {
                        fieldsReduced.add(mainField);
                    }
                    // Campo adicional para obtener la descripción
                    SurveyDataFieldDTO mainFieldDescription = new SurveyDataFieldDTO(mainField);
                    mainFieldDescription.setName(f.getFieldCode() + AnswerPartType.CATALOG_DESCRIPTION.getSuffix());
                    mainFieldDescription.setAnswerPartType(AnswerPartType.CATALOG_DESCRIPTION.getValue());
                    fieldsReduced.add(mainFieldDescription);
                }
            } else {
                fieldsReduced.add(f);
            }
        });
        final Set<SurveyDataFieldDTO> fields;
        if (!Objects.equals(maxResults, 0) &&  fieldsReduced.size() > maxResults) {
            fields = new LinkedHashSet<>((new ArrayList<>(fieldsReduced)).subList(0, maxResults));
        } else {
            fields = fieldsReduced;
        }
        final ResourceBundle answerLocale = LocaleUtil.getI18n(AnswerPartType.class, loggedUser.getLocale());
        final Map<String, Long> metaIds;
        if (includeMetadata) {
            metaIds = getSurveyAnswerMetadataIds(surveyId);
        } else {
            metaIds = new HashMap<>();
        }
        fields.forEach(field -> configureField(field, includeMetadata, metaIds, answerLocale));
        //TODO: Llenar fields con la información de externalFields y borrar externalFields
        final Map<Long, Map<String, HierarchyFieldDTO>> externalFields = loadExternalFields(fields, cache);
        Set<SurveyDataFieldDTO> sortedFields = sortFields(fields, externalFields);
        if (checkFieldsExistsTable) {
            removeMissingFieldsTable(surveyId, sortedFields);
        }
        handleDuplicatedTitles(sortedFields);
        return new FieldConfigDTO(sortedFields, externalFields);
    }

    private static void handleDuplicatedTitles(Set<SurveyDataFieldDTO> fields) {
        // Los campos repetidos se renombrarán con un número al final, por ejemplo Clave (2)
        final Map<String, Integer> countTitles = new LinkedHashMap<>();
        fields.forEach(field -> {
            String title = field.getTitle();
            if (countTitles.containsKey(title)) {
                Integer countTitle = countTitles.get(title);
                if (countTitle == null) {
                    countTitle = 1;
                }
                countTitle++;
                field.setTitle(title + " (" + countTitle + ")");
                countTitles.put(title, countTitle);
            } else {
                countTitles.put(title, 1);
            }
        });
    }

    private static void removeMissingFieldsTable(Long surveyId, Set<SurveyDataFieldDTO> sortedFields) {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class);
        final String answersTableName = dao.HQL_findSimpleString(" "
                        + " SELECT s.answersTable"
                        + " FROM " + Survey.class.getCanonicalName() + " s "
                        + " WHERE s.id = :surveyId ",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        if (answersTableName.isEmpty()) {
            return;
        }
        final IDynamicTableDAO dynamicDao = Utilities.getBean(IDynamicTableDAO.class);
        final List<String> answerNames = dynamicDao.getDynamicTableColumns(answersTableName);
        if (LOGGER.isDebugEnabled()) {
            answerNames.forEach((x) -> LOGGER.debug("Column `{}` of table `{}`", x, answersTableName));
        }
        List<SurveyDataFieldDTO> valueKeys = new ArrayList<>(sortedFields);
        valueKeys.stream()
                .filter(o -> !answerNames.contains(o.getName())
                        && !answerNames.contains(o.getFieldCode() + "_value")
                        && !answerNames.contains(o.getFieldCode() + "_description"))
                .forEach(o -> {
                    LOGGER.info("Removed field `{}` of type `{}` because it doesn't exist in table `{}`", o.getName(), o.getFieldType(), answersTableName);
                    sortedFields.remove(o);
                });
    }

    private static void updateFieldTitle(
            final SurveyDataFieldDTO field,
            final ISurveyData fieldData,
            final SurveyField.TYPE type, 
            final Boolean joinOptions,
            final Map<String, Integer> countRows
    ) {
        if (type == null) {
            return;
        }
        switch (type) {
            case EXCLUSIVE_SELECT:
            case MENU_SELECT:
                if (Objects.equals(joinOptions, true)) {
                    return;
                }
                break;
        }
        final StringBuilder titleBuilder = new StringBuilder(250);
        titleBuilder.setLength(0);
        switch (type) {
            case MULTI_SELECT:
            case EXCLUSIVE_SELECT:
            case TEXT_FIELD_ARRAY:
            case EXCLUSIVE_SELECT_YESNO:
            case MENU_SELECT:
                titleBuilder
                        .append(fieldData.getFieldQuestionDesc())
                        .append(SLASH)
                        .append(fieldData.getOptionDesc())
                        ;
                break;
            case TABLE_FIELD:
            case EXCLUSIVE_SELECT_ARRAY:
            case MULTI_SELECT_ARRAY:
                titleBuilder
                        .append(fieldData.getFieldQuestionDesc())
                        .append(SLASH)
                        .append(fieldData.getOptionDesc())
                        .append(SLASH)
                        .append(getNextRow(fieldData.getFieldId(), fieldData.getTableHeaderId(), countRows))
                        ;
                break;
            default:
                titleBuilder
                        .append(field.getTitle())
                        ;
                break;
        }
        field.setTitle(titleBuilder.toString());
        field.setOriginalTitle(field.getTitle());
    }
    
    public static Map<String, Long> getSurveyAnswerMetadataIds(Long surveyId) {
        final IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
        final List<Map<String, Object>> meta = formCaptureDAO.HQL_findByQuery(""
            + " SELECT new map("
                + " c.surveyAnswerFieldName AS surveyAnswerFieldName"
                + ",c.id AS id"
            + " ) "
            + " FROM " + SurveyAnswerMetadata.class.getCanonicalName() + " c "
            + " WHERE c.surveyId = :surveyId", 
            ImmutableMap.of("surveyId", surveyId), true, CacheRegion.SURVEY, 0
        );
        final Map<String, Long> metaIds = new HashMap<>(meta.size());
        meta.forEach(m -> {
            if (metaIds.containsKey(m.get("surveyAnswerFieldName").toString())) {
                LOGGER.error("The field `" + m.get("surveyAnswerFieldName") + "` is repeated at surveyId: " + surveyId);
            }
            metaIds.put(m.get("surveyAnswerFieldName").toString(), Long.valueOf(m.get("id").toString()));
        });
        return metaIds;
    }
    
    private static SurveyDataFieldDTO configureField(
            final SurveyDataFieldDTO field,
            final Boolean includeMetadata,
            final Map<String, Long> metaIds,
            final ResourceBundle answerLocale
    ) {
        final StringBuilder title = new StringBuilder(
                getDefaultFieldTitle(field)
        );
        if (field.getCatalogLabel() == null) {
            title.append(getFieldLabel(field, answerLocale));
        } else {
            if (CatalogFieldType.CATALOG_HIERARCHY.getName().equals(field.getCatalogSubType())) {
                title.append(" / ").append(field.getCatalogLabel());
            } else {
                title.append(" / ").append(field.getCatalogLabel()).append(getFieldLabel(field, answerLocale));
            }
        }
        field.setTitle(title.toString());
        field.setOriginalTitle(field.getTitle());
        Long metaId = metaIds.get(field.getName());
        if (includeMetadata && metaId == null) {
            LOGGER.error(
                    "Excluded field, because there metadata missing for database field `{}`, field: {}",
                        field.getName(),
                    field);
        } else {
            field.setSurveyAnswerMetadataId(metaId);
        }
        return field;
    }

    private static String getFieldLabel(SurveyDataFieldDTO field, ResourceBundle answerLocale) {
        final String result;
        if (SurveyField.TYPE.TEXT_FIELD.getValue().equals(field.getType())) {
            result = getTextAnswerPartTypeLabel(field.getAnswerType(), field, answerLocale);
        } else if (SurveyField.TYPE.TEXT_FIELD_ARRAY.getValue().equals(field.getType())) {
            result = getTextAnswerPartTypeLabel(field.getOptionAnswerType(), field, answerLocale);
        } else {
            result = getDefaultAnswerPartTypeLabel(field, answerLocale);
        }
        if (result != null && !result.isEmpty()) {
            return " " + result;
        } else {
            return result;
        }
    }

    private static String getDefaultAnswerPartTypeLabel(SurveyDataFieldDTO field, ResourceBundle answerLocale) {
        final AnswerPartType partType = SurveyUtil.getAnswerPartType(field);
        if (partType == null) {
            return "";
        }
        return LocaleUtil.getTag(partType.name(), answerLocale);
    }

    private static String getTextAnswerPartTypeLabel(final Integer answerType, SurveyDataFieldDTO field, ResourceBundle answerLocale) {
        final SurveyFieldAnswerType answerTypeEnum = SurveyFieldAnswerType.fromValue(answerType);
        if (SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.equals(answerTypeEnum) 
                && AnswerPartType.REGULAR_FIELD.getValue().equals(field.getAnswerPartType())) {
            return LocaleUtil.getTag("EXCHANGE_RATE_TARGET", answerLocale);
        } else {
            return getDefaultAnswerPartTypeLabel(field, answerLocale);
        }
    }
    
    private static String getDefaultFieldTitle(SurveyDataFieldDTO field) {
        if (field.getTitle() == null) {
            return "A llenar por el solicitante";
        }
        return field.getTitle();
    } 

    @Nullable
    public static SurveyFieldAnswerType getSurveyFieldAnswerType(final ISurveyAnswerType field) {
        SurveyFieldAnswerType value = SurveyUtil.getSurveyFieldAnswerType(field, null);
        if (value == null) {
            LOGGER.warn("Not supported currency conversión for field {}", field);
        }
        return value;
    }

    public static SurveyFieldAnswerType getSurveyFieldAnswerType(final ISurveyAnswerType field, SurveyFieldAnswerType defaultValue) {
        final SurveyField.TYPE type = SurveyField.TYPE.fromValue(field.getFieldType());
        if (type == null) {
            return defaultValue;
        }
        switch (type) {
            case TEXT_FIELD_ARRAY:
                return SurveyFieldAnswerType.fromValue(field.getOptionAnswerType());
            case TEXT_FIELD:
                return SurveyFieldAnswerType.fromValue(field.getAnswerType());
            default:
                return defaultValue;
        }
    }

    private static void addCurrencyConversionFields(
            final ISurveyData field,
            final SurveyField.TYPE type, 
            final Set<SurveyDataFieldDTO> result,
            final Boolean joinOptions,
            final Map<String, Integer> countRows
    ) {
        final SurveyFieldAnswerType answerType = getSurveyFieldAnswerType(field);
        if (!SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.equals(answerType)) {
            return;
        }
        final SurveyDataFieldDTO exchangeRateIdField = getFieldInstance(field, type, AnswerPartType.EXCHANGE_RATE_ID);
        updateFieldTitle(exchangeRateIdField, field, type, joinOptions, countRows);
        result.add(exchangeRateIdField);
        final SurveyDataFieldDTO exchangeRatSourceField = getFieldInstance(field, type, AnswerPartType.EXCHANGE_RATE_SOURCE);
        updateFieldTitle(exchangeRatSourceField, field, type, joinOptions, countRows);
        result.add(exchangeRatSourceField);
        final SurveyDataFieldDTO exchangeConversionRateField = getFieldInstance(field, type, AnswerPartType.EXCHANGE_CONVERSION_RATE);
        updateFieldTitle(exchangeConversionRateField, field, type, joinOptions, countRows);
        result.add(exchangeConversionRateField);
        final SurveyDataFieldDTO exchangeRateDateField = getFieldInstance(field, type, AnswerPartType.EXCHANGE_RATE_DATE);
        updateFieldTitle(exchangeRateDateField, field, type, joinOptions, countRows);
        result.add(exchangeRateDateField);
    }

    private static void addFillOutDateFields(
            final ISurveyData f,
            final SurveyField.TYPE type,
            final Set<SurveyDataFieldDTO> result,
            final Boolean joinOptions,
            final Map<String, Integer> countRows
    ) {
        if (!Objects.equals(f.getShowFillOutDate(), 1)) {
            return;
        }
        final SurveyDataFieldDTO fillOutDateField = getFieldInstance(f, type, AnswerPartType.FILL_OUT_DATE);
        updateFieldTitle(fillOutDateField, f, type, joinOptions, countRows);
        result.add(fillOutDateField);
    }

    
    public static AnswerPartType getAnswerPartType(final SurveyDataFieldDTO field) {
        if (field == null) {
            return null;
        }
        if (field.getAnswerPartType() == null) {
            return null;
        }
        return getAnswerPartType(
            field.getAnswerPartType(), 
            field.getName(), 
            field.getFieldCode(), 
            field.getCatalogSubType()
        );
    }
    
    
    public static AnswerPartType getAnswerPartType(
        String fieldName,                               // <-- Es el nombre de la columna en BD final
        String fieldCode,                               // <-- Es el nombre del campo `VCH_ID` en `SURVEY_FIELD_OBJECT`
        String catalogSubType
    ) {
        return getAnswerPartType(null, fieldName, fieldCode, catalogSubType);
    }
    
    
    private static AnswerPartType getAnswerPartType(
        Integer fieldAnswerPartType,
        String fieldName,                               // <-- Es el nombre de la columna en BD final
        String fieldCode,                               // <-- Es el nombre del campo `VCH_ID` en `SURVEY_FIELD_OBJECT`
        String catalogSubType
    ) {
        if (CatalogFieldType.CATALOG_HIERARCHY.equals(CatalogFieldType.fromValue(catalogSubType))) {
            return null;
        }
        AnswerPartType partType = AnswerPartType.fromValue(fieldAnswerPartType);
        if (partType == null) {
            partType = AnswerPartType.fromField(fieldName, fieldCode);
        }
        if (partType == null || AnswerPartType.REGULAR_FIELD.equals(partType)) {
            return null;
        }
        return partType;
    }
    
    private static SurveyDataFieldDTO getFieldInstance(
            final ISurveyData field,
            final SurveyField.TYPE type,
            final AnswerPartType suffix
    ) {
        final SurveyDataFieldDTO config = new SurveyDataFieldDTO(field);
        final String name = getNewFieldName(suffix, field);  
        config.setName(name);
        if (type != null) {
            config.setSubType(type.getSubtype());
        }
        config.setAnswerPartType(suffix.getValue());
        config.setSectionDesc(field.getSectionDesc());
        config.setFieldStage(field.getFieldStage());
        config.setIncludeInMail(field.getIncludeInMail());
        config.setIncludeInSubject(field.getIncludeInSubject());
        config.setSectionId(field.getSectionId());
        return config;
    }


    public static Long getDocumentId(final String formMasterId, final String formVersion) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("masterId", formMasterId);
        params.put("version", formVersion);
        final Long surveyId = Utilities.getUntypedDAO().HQL_findSimpleLong(""
            + " SELECT c.id"
            + " FROM " + Document.class.getCanonicalName() + " c "
            + " WHERE c.masterId = :masterId AND c.version = :version",
            params
        );
        return surveyId;
    }
    
    public static Long getSurveyId(final String formMasterId, final String formVersion) {
        return getSurveyId(Utilities.getUntypedDAO(), formMasterId, formVersion);
    }
    
    public static Long getSurveyId(final IUntypedDAO dao, final String formMasterId, final String formVersion) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("masterId", formMasterId);
        params.put("version", formVersion);
        final Long surveyId = dao.HQL_findSimpleLong(""
            + " SELECT c.surveyId"
            + " FROM " + Document.class.getCanonicalName() + " c "
            + " WHERE c.masterId = :masterId"
            + " AND c.version = :version",
            params
        );
        return surveyId;
    }
    
    public static Long getRequestId(final IUntypedDAO dao, final String formMasterId, final String formVersion) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("masterId", formMasterId);
        params.put("version", formVersion);
        final Long requestId = dao.HQL_findSimpleLong(""
            + " SELECT c.requestId"
            + " FROM " + Document.class.getCanonicalName() + " c "
            + " WHERE c.masterId = :masterId AND c.version = :version",
            params
        );
        return requestId;
    }

    public static String getNewFieldName(AnswerPartType suffix, String fieldCode, String optionCode, String headerCode) {
        final StringBuilder nameBuilder = new StringBuilder(50);
        switch (suffix) {
            case SUMMARY_FIELD:
            case OTHER_FIELD:
                nameBuilder.append(fieldCode);
                break;
            default:
                addPrefixProperties(nameBuilder, fieldCode, optionCode, headerCode);
                break;
        }
        nameBuilder.append(suffix.getSuffix());
        return nameBuilder.toString();
    }
    
    public static String getNewFieldName(AnswerPartType suffix, ISurveyData item) {
        return getNewFieldName(suffix, item.getFieldCode(), item.getOptionCode(), item.getTableHeaderCode());
    }
    
    public static String getGuessFieldName(Long surveyId, ISurveyData field) {
        final SurveyField.TYPE type = SurveyField.TYPE.fromValue(field.getFieldType());
        final SurveyDataFieldDTO f = getFieldInstance(field, type, AnswerPartType.REGULAR_FIELD);
        return getGuessFieldName(surveyId, ImmutableSet.of(f), field.getFieldCode(), field.getOptionCode(), field.getTableHeaderCode());
    }

    public static String getGuessFieldName(Long surveyId, Set<SurveyDataFieldDTO> fields, String fieldCode, String optionCode, String tableHeaderCode) {
        StringBuilder nameBuilder = new StringBuilder(45);
        SurveyDataFieldDTO configQuestion = fields.stream().filter(fl -> Objects.equals(fl.getFieldCode(), fieldCode)).findFirst().get();
        boolean disableColumnPerOption = configQuestion.getDisableColumnPerOption();
        if (disableColumnPerOption) {
            return configQuestion.getFieldCode() + AnswerPartType.CATALOG_VALUE.getSuffix();
        }
        addPrefixProperties(nameBuilder, fieldCode, optionCode, tableHeaderCode);
        for (SurveyDataFieldDTO field : fields) {
            for (AnswerPartType suffix : AnswerPartType.values()) {
                switch (suffix) {
                    case OTHER_FIELD:
                        if (Objects.equals((fieldCode + suffix.getSuffix()), field.getName())) {
                            return field.getName();
                        }
                        break;
                    default:
                        if (Objects.equals(nameBuilder.toString(), field.getName())
                                || Objects.equals(nameBuilder + suffix.getSuffix(), field.getName())) {
                            return field.getName();
                        }
                        break;
                }
            }
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        if (
            dao.HQL_findSimpleInteger(""
                + " SELECT count(*) "
                + " FROM " + SurveyAnswerMetadata.class.getCanonicalName() + " m "
                + " WHERE "
                    + " m.surveyId = :surveyId"
                    + " AND m.surveyAnswerFieldName = :surveyAnswerFieldName"
                + " ",
                ImmutableMap.of(
                    "surveyId", surveyId,
                    "surveyAnswerFieldName", nameBuilder.toString()
                ),
                true,
                CacheRegion.SURVEY,
                0
            ) > 0
        ) {
            return nameBuilder.toString();
        }
        return null;
    }

    private static void addPrefixProperties(StringBuilder nameBuilder, String fieldCode, String optionCode, String tableHeaderCode) {
        nameBuilder.append(fieldCode);
        if (optionCode != null) {
            nameBuilder.append("_").append(optionCode);
        }
        if(tableHeaderCode != null) {
            nameBuilder.append("_").append(tableHeaderCode);
        }
    }

    public static String getOptions(String input, int columnas, String inputClass, int rowOptionNum, SurveyField field, SurveyItem item, boolean verticalLayout) {
        String r = "";
        String sentInput;
        int rowNum = field.getObj().getOrder();
        String onclick = "void(0)";
        if (SurveyField.TYPE_SI_NO_PORQUE.equals(field.getType())) {
            onclick = "showHideWhy(this)";
        }
        List<String> hidden = new ArrayList<>();
        if (field.getObj().getHasHeaders() == 1) {
            List<SurveyTextItem> hiddenMatrixItems = field.getObj().getHiddenMatrixItems();
            for (SurveyTextItem hiddenItem : hiddenMatrixItems) {
                hidden.add(hiddenItem.getTitle());
            }
        }
        String display = "";
        String thisRowNum = "" + rowNum;
        String identifier;
        for (int i = 0; i < columnas; i++) {
            if (field.getObj().getHasHeaders() == 1) {
                if (hidden.contains(field.getObj().getField_id() + "_uiObject_array_" + rowOptionNum + "_list" + i)
                        || hidden.contains(field.getObj().getField_id() + "_uiObject_array_" + rowOptionNum + "_col" + i)) {
                    display = "none";
                    thisRowNum = "hiddenStuff";
                }
            }
            identifier = thisRowNum + "_" + rowOptionNum;
            sentInput = input
                    .replace("${name}", "row_" + thisRowNum + "_column")
                    .replace("${name_row}", "row_" + identifier)
                    .replace("${name_matrix}", "col_" + identifier + "_" + i)
                    .replace("${id}", "field_" + identifier + "_" + i)
                    .replace("${value}", "" + item.getId() + "")
                    .replace("${onclick}", onclick)
                    .replace("${display}", display)
                    //DB: No aplica para seleccion multiple de radios ni matrizes de radios
                    .replace("${dbId_name}", "dbId_row_" + thisRowNum + "_column")
                    .replace("${dbId_name_row}", "dbId_row_" + identifier)
                    .replace("${dbId_name_matrix}", "dbId_col_" + identifier + "_" + i)
                    .replace("${dbId_id}", "dbId_field_" + identifier + "_" + i);
            display = "";
            r += (verticalLayout ? "</td><td class='" + inputClass + "'>" : "</td><td>") + sentInput;
            thisRowNum = "" + rowNum;
        }
        return r;
    }

    public static String getLabel(List<? extends ISurveyTitle> s){
        return getLabel(s,null);
    }
    public static String getLabel(List<? extends ISurveyTitle> s, Integer columns) {
        if (s == null || s.isEmpty()) {
            return "";
        }
        ISurveyTitle textItem = null;
        Iterator<? extends ISurveyTitle> iTextItem = s.iterator();
        StringBuilder str = new StringBuilder(100);
        if (iTextItem.hasNext()) {
            textItem = iTextItem.next();
            str.append("<div>");

            str.append(textItem.getTitle() == null ? "" : columns == null ? textItem.getTitle() : cutLabel(textItem.getTitle(), columns));
            while (iTextItem.hasNext()) {
                textItem = iTextItem.next();
                str.append("</div><div style='display:none'>");
                str.append(textItem.getTitle() == null ? "" : columns == null ? textItem.getTitle() : cutLabel(textItem.getTitle(), columns));
            }
            str.append("</div>");
        }
        try {
            return URLDecoder.decode(str.toString(), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException | IllegalArgumentException ex) {
            if (textItem != null) {
                LOGGER.error(
                        "Can not decode label for text item id {} and text {}.",
                        textItem.getId(), str
                );        
    }
            return str.toString();
        }
    }

    public static String getPlainLabel(Collection<? extends ISurveyTitle> s, Integer columns) {
        if (s == null || s.isEmpty()) {
            return "";
        }
        ISurveyTitle textItem;
        Iterator<? extends ISurveyTitle> iTextItem = s.iterator();
        StringBuilder str = new StringBuilder(100);
        if (iTextItem.hasNext()) {
            textItem = iTextItem.next();
            str.append("<div>");
            str.append(textItem.getTitle() == null ? "" : columns == null ? textItem.getTitle() : cutLabel(textItem.getTitle(), columns));
            while (iTextItem.hasNext()) {
                textItem = iTextItem.next();
                str.append("</div><div style='display:none'>");
                str.append(textItem.getTitle() == null ? "" : columns == null ? textItem.getTitle() : cutLabel(textItem.getTitle(), columns));
            }
            str.append("</div>");
        }
        return str.toString();
    }

    public static String cutLabel(String etiqueta, Integer columns) {
        if (columns == 0) {
            return etiqueta;
        }
        int maxWidth = 700 / columns; //<--- ancho maximo por columna en pixeles
        int fontWidth = 10;            //<--- ancho de la letra en pixeles
        StringBuilder str = new StringBuilder(100);
        String[] words = etiqueta.split(" ");
        for (int i = 0; i < words.length; i++) {
            if ((words[i].replace(",", "").replace(".", "").replace("(", "").replace("?", "").replace(")", "").length() * fontWidth) > maxWidth) {
                for (int j = 0; j < words[i].length(); j++) {
                    if (((j + 1) * fontWidth) >= maxWidth) {
                        str.append("<br/>").append(words[i].charAt(j));
                        words[i] = words[i].substring(j);
                        j = 0;
                    } else {
                        str.append(words[i].charAt(j));
                    }
                }
                str.append(" ");
            } else {
                str.append(words[i]).append(" ");
            }
        }
        try {
            return URLDecoder.decode(str.toString(), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException | IllegalArgumentException ex) {
            LOGGER.error(
                    "Can not cut zero label for etiqueta {} and text {}.",
                    etiqueta, str
            );        
            return str.toString();
    }
    }

    public static String getZeroLabel(List<? extends ISurveyTitle> s) {
        if (s == null || s.isEmpty()) {
            return "";
        }
        ISurveyTitle textItem = null;
        Iterator<? extends ISurveyTitle> iTextItem = s.iterator();
        StringBuilder str = new StringBuilder(100);
        if (iTextItem.hasNext()) {
            textItem = iTextItem.next();
            str.append(textItem.getTitle() == null ? "" : textItem.getTitle());
            while (iTextItem.hasNext()) {
                textItem = iTextItem.next();
                str.append(textItem.getTitle() == null ? "" : textItem.getTitle());
            }
        }
        try {
            return URLDecoder.decode(str.toString(), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException | IllegalArgumentException ex) {
            if (textItem != null) {
                LOGGER.error(
                        "Can not decode zero label for text item id {} and text {}.",
                        textItem.getId(), str
                );        
    }
            return str.toString();
        }
    }
    
    public static DynamicFieldTable getFactoryBuilded(
            final Long surveyId,
            final SurveyUtilCache cache,
            final IDynamicTableDAO tableDao,
            final ILoggedUser loggedUser
    ) throws QMSException {
        final FieldConfigDTO fieldConfig = getAllFields(surveyId, cache, loggedUser);
        final Set<SurveyDataFieldDTO> fields = fieldConfig.getFields();
        return getFactoryBuilded(surveyId, fields, tableDao, loggedUser.getId());
    }

    public static Boolean allowComputedColumns(Long surveyId) {
        Boolean allowComputedColumns = Utilities.getUntypedDAO().HQL_findSimpleBoolean(""
                        + " SELECT c.computeOptionalCodeColumns"
                        + " FROM " +  Survey.class.getCanonicalName() + " c "
                        + " WHERE c.id = :surveyId",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        return Objects.equals(allowComputedColumns, true);
    }
    
    public static DynamicFieldTable getFactoryBuilded(
            final Long surveyId, 
            final Set<SurveyDataFieldDTO> fields, 
            final IDynamicTableDAO<String, SurveyDataFieldDTO> tableDao,
            final Long loggedUserId
    ) throws QMSException {
        final ITableFactory<String, SurveyDataFieldDTO> factory = new TableFactorySurveyAnswers(surveyId, new ArrayList<>(fields), allowComputedColumns(surveyId));
        final DynamicFieldTable newTable = tableDao.getFactoryBuilded(factory, loggedUserId);
        if (newTable == null) {
            LOGGER.error(
                    "No se pudo crear la tabla de respuestas congeladas para el cuestionario con id {} con {} preguntas.",
                    surveyId,
                    fields.size()
            );
            return null;
        }
        final String newTableName = newTable.getDescription();
        final Map<String, Object> params = new HashMap<>(2);
        params.put(SURVEY_ID, surveyId);
        params.put(ANSWERS_TABLE, newTableName);
        final Integer updatedRows = tableDao.HQL_updateByQuery(UPDATE_SURVEY_ANSWER_TABLE_BLOCKED, params);
        IdFactory.setNextId(newTableName, 0);
        if (updatedRows != 1) {
            LOGGER.error("the values answersTable='{}' and status='{}' (blocked) could not be setted to surveyId '{}'",
                newTableName, Survey.ESTATUS_BLOQUEADO, surveyId
            );
            return null;
        }
        return newTable;
    }  
    
    public static void notifySaveAnswersFailures(@Nonnull final ILoggedUser loggedUser) {
        double numberPages = 1.0;
        int currentPage = 0;
        try {
            final IFormCaptureDAO formDao = Utilities.getBean(IFormCaptureDAO.class);
            final Long count = formDao.getNotifySaveAnswersFailuresCount();
            if (count == null || count <= 0) {
                return;
            }
            numberPages = Math.ceil(count > ERROR_PER_PAGE ? count / ERROR_PER_PAGE : 1.0);
            final IPagedQuery pageConfig = new PagedQuery();
            pageConfig.setPageSize(ERROR_PER_PAGE.intValue());
            for (currentPage = 0; currentPage < numberPages; currentPage++) {
                pageConfig.setPage(currentPage);
                try {
                    formDao.sendNotifySaveAnswersFailures(pageConfig, loggedUser);
                } catch (final Exception ex) {
                    LOGGER.error(
                            "Failed to send save answers failures for page {} of {}", new Object[] {currentPage, numberPages},
                            ex
                    );
                }
            }
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to send save answers failures for page {} of {}", new Object[] {currentPage, numberPages},
                    e
            );
        }
    }

    public static Boolean cancelFillForm(
            final IRequestDAO daoRequest,
            final IOutstandingSurveysDAO daoSurveys,
            final Long outstandingSurveyId,
            final Long requestId,
            final String comment,
            final String razon,
            final ILoggedUser loggedUser
    ) {
        if (daoRequest.cancelSurveyRequest(requestId, razon, loggedUser)) {
            return daoSurveys.cancelOutstandingSurvey(
                    requestId,
                    comment,
                    outstandingSurveyId,
                    true,
                    loggedUser
            ) == 1;
        }
        return false;
    }
    
    private static List<SurveyFieldConfigMail> getFieldConfig(final Long surveyId) {
        final List<SurveyFieldConfigMail> info = Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(SurveyFieldConfigMail.class, ""
                + " SELECT ssc"
                + " FROM " + SurveyFieldConfigMail.class.getCanonicalName() + " ssc "
                + " WHERE ssc.surveyId = :surveyId",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        return info;
    }
    
    private static Set<SurveyDataFieldDTO> sortFields(final Set<SurveyDataFieldDTO> fields, final Map<Long, Map<String, HierarchyFieldDTO>> externalFields) {
        if (fields == null || fields.isEmpty()) {
            return fields;
        }
        final List<SurveyDataFieldDTO> values = new ArrayList<>(fields);
        values.sort((f1, f2) -> sortField(f1, f2, externalFields));
        return new LinkedHashSet<>(values);
    }
    
    private static Integer sortField(
            final SurveyDataFieldDTO f1, 
            final SurveyDataFieldDTO f2,
            final Map<Long, Map<String, HierarchyFieldDTO>> externalFields
    ) {
        final Integer fieldIdResult = ObjectUtils.compare(f1.getFieldId(), f2.getFieldId(), true);
        if (!Objects.equals(fieldIdResult, 0)) {
            return fieldIdResult;
        }
        final Integer optionResult = ObjectUtils.compare(f1.getOptionId(), f2.getOptionId(), true);
        if (!Objects.equals(optionResult, 0)) {
            return optionResult;
        }
        final Integer headerResult = ObjectUtils.compare(f1.getTableHeaderId(), f2.getTableHeaderId(), true);
        if (!Objects.equals(headerResult, 0)) {
            return headerResult;
        }
        final HierarchyFieldDTO h1 = getHierarchyField(f1, externalFields);
        final HierarchyFieldDTO h2 = getHierarchyField(f2, externalFields);
        if (h1 == null || h2 == null) {
            return 0;
        }
        final Integer hierarchyResult = ObjectUtils.compare(h1.getReadonly(), h2.getReadonly(), true);
        if (!Objects.equals(hierarchyResult, 0)) {
            return hierarchyResult;
        }
        return ObjectUtils.compare(h1.getLevel(), h2.getLevel());
    }
    
    public static HierarchyFieldDTO getHierarchyField(final SurveyDataFieldDTO field, final Map<Long, Map<String, HierarchyFieldDTO>> externalFields) {
        final CatalogFieldType subType = CatalogFieldType.fromValue(field.getCatalogSubType());
        if (subType == null) {
            return null;
        } else if (subType.equals(CatalogFieldType.CATALOG_HIERARCHY)) {
            final Map<String, HierarchyFieldDTO> fieldIndex = externalFields.get(field.getExternalCatalogId());
            if (fieldIndex != null) {
                final String fieldCode = field.getName().replace(field.getFieldCode() + "_value_", "");
                final HierarchyFieldDTO hierarchyField = fieldIndex.get(fieldCode);
                return hierarchyField;
            }
        }
        return null;
    }
    
    public static Map<Long, Map<String, HierarchyFieldDTO>> loadExternalFields(Set<SurveyDataFieldDTO> fields, @Nonnull SurveyUtilCache cache) {
        final Map<Long, Map<String, HierarchyFieldDTO>> externalFields = new HashMap<>();
        if (fields == null || fields.isEmpty()) {
            return externalFields;
        }
        fields.forEach((field) -> {
            final Long externalCatalogId = field.getExternalCatalogId();
            if (externalCatalogId == null) {
                return;
            }
            if (externalFields.containsKey(externalCatalogId)) {
                return;
            }
            final List<HierarchyFieldDTO> hierarchyFields = cache.getHierarchFields(externalCatalogId);
            if (hierarchyFields == null || hierarchyFields.isEmpty()) {
                return;
            }
            final Map<String, HierarchyFieldDTO> fieldIndex = new HashMap<>();
            hierarchyFields.forEach((hierarchyField) -> {
                fieldIndex.put(hierarchyField.getColumn(), hierarchyField);
            });
            externalFields.put(externalCatalogId, fieldIndex);
            
        });
        return externalFields;
    }

    public static String generateTableSynonymName(final String masterId) {
        final String name = SurveyUtil.ANSWERS_TABLE_PREFFIX + masterId.replaceAll("-", "");
        return name;
    }

    public static String generateTablSynonymWithVersionName(final String masterId, final String version) {
        final String mainName = generateTableSynonymName(masterId);
        final String name = mainName + "_" + version
                .replaceAll("-", "")
                .replaceAll(" ", "");
        return name;
    }
    
    public static Boolean isValidPendingAttender(Long recordId, Long pendingRecordId, String moduleName, String pendingCamelName, ILoggedUser loggedUser) {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        PendingHelper helper = new PendingHelper(dao);
        // Se parcha valor del parametro "recordId" para que busque solo números
        APE ape = loadApe(moduleName, pendingCamelName);
        if (pendingRecordId != null && pendingRecordId != 0L) {
            return helper.isPendingValidById(pendingRecordId, ape, loggedUser.getId());
        } else {
            return helper.isPendingValidByRecordId(recordId, ape, loggedUser.getId());
        }
    }

    public static APE loadApe(String moduleName, String pendingCamelName) {
        if (pendingCamelName.startsWith("accFinding")) {
            pendingCamelName = pendingCamelName.replace("accFinding", "finding");
        }
        return ModuleUtil.getApeFromModule(moduleName, pendingCamelName);
    }

    public static boolean isCatalogHierarchyOrSystemColumn(QueryColumn column) {
        return isCatalogHierarchyOrSystemColumn(column.getCode());
    }

    public static boolean isCatalogHierarchyOrSystemColumn(SurveyDataFieldDTO field) {
        if (field.getCatalogLabel() == null || field.getCatalogLabel().isEmpty()) {
            return false;
        }
        return isCatalogHierarchyOrSystemColumn(field.getCatalogLabel());
    }

    public static boolean isCatalogHierarchyOrSystemColumn(String columnField) {
        return HIERARCHY_SYSTEM_COLUMN_FIELDS.contains(columnField);
    }
}