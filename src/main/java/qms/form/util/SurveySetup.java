package qms.form.util;

import DPMS.DAOInterface.ISettingsDAO;
import DPMS.Mapping.Document;
import Framework.Config.PagedQuery;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.ServletContext;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.dto.SurveyAnswerMetadataFreezeBK;
import qms.custom.dto.WebhookAnswerMetadataBK;
import qms.custom.entity.DynamicFieldTable;
import qms.custom.entity.PrintingFormatSurveyField;
import qms.form.core.FillManager;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.dto.SurveyRestoreDataDTO;
import qms.form.entity.AnswerPartType;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.interfaces.IPagedQuery;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class SurveySetup {

    private static final Class<SurveySetup> CLAZZ = SurveySetup.class;
    private static final Logger LOGGER = Loggable.getLogger(CLAZZ);
    private final static Double RECORDS_PER_PAGE = 1000.0;
    
    public GenericSaveHandle initializeAnwersMigration(final ServletContext servletContext, final Boolean forceReset, final ILoggedUser userAdmin) {
        try {
            if (!forceReset && !Utilities.isRecalculateSurveyAnswerMigrationsOn()) {
                return GenericSaveHandle.newFailure("Invalid call");
            }
            final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
            
            // Borra y recalcula valores para la migración de respuestas
            resetSurveyAnswersMigration(dao, userAdmin);
            final ISettingsDAO settingsDao = dao.getBean(ISettingsDAO.class);
            
            // Devuelve la bandera a su estado original
            Integer result = updateRecalculateSurveyMigration(settingsDao, false);
            if (result != null && result.equals(1)) {
                Utilities.resetSettings(servletContext);
                return GenericSaveHandle.newSuccess();
            }
            return GenericSaveHandle.newFailure("Can not update settings");
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            updateRecalculateSurveyMigration(settingsDao, false);
            LOGGER.error("Ocurrió un error al inicializar respuestas congeladas de formularios, error:", ex);
            return GenericSaveHandle.newFailure(ex);
        }
    }
    public GenericSaveHandle initializeAnwers(final ServletContext servletContext, final Boolean forceReset, final ILoggedUser userAdmin) {
        try {
            if (!forceReset && Utilities.isRecalculateSurveyAnswersOff()) {
                return GenericSaveHandle.newFailure("Invalid call");
            }
            final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
            
            // Borra y recalcula las tablas congeladas de respuestas
            resetSurveyAnswersData(dao, userAdmin);
            final ISettingsDAO settingsDao = dao.getBean(ISettingsDAO.class);
            
            // Devuelve la bandera a su estado original
            Integer result = updateFreezedFormAnswersRun(settingsDao, 1);
            if (result != null && result.equals(1)) {
                if (Boolean.TRUE.equals(Utilities.getSettings().getRegenerateAllSlimReportsAfterFreeze())) {
                    try {
                        FormUtil.regenerateAllSlimReports(servletContext, userAdmin);
                    } catch (final Exception ex) {
                        LOGGER.error("Ocurrió un error al regenerar slimreports después de congelar respuestas, error:", ex);
                    }
                }
                Utilities.resetSettings(servletContext);
                return GenericSaveHandle.newSuccess();
            }
            return GenericSaveHandle.newFailure("Can not update settings");
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            updateFreezedFormAnswersRun(settingsDao, 0);
            LOGGER.error("Ocurrió un error al inicializar respuestas congeladas de formularios, error:", ex);
            return GenericSaveHandle.newFailure(ex);
        }
    }
    
    public GenericSaveHandle initializeAnwers(final ServletContext servletContext, final Set<Long> surveyIds, final ILoggedUser userAdmin) {
        try {
            final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
            List<Long> restOfIds = dao.HQL_findByQuery(" "
                    + " SELECT d.surveyId "
                    + " FROM " + Document.class.getCanonicalName() + " d "
                    + " WHERE "
                        + " d.masterId IN ("
                            + " SELECT masterId "
                            + " FROM " + Document.class.getCanonicalName() + " do "
                            + " WHERE do.surveyId IN ("
                                    + ":surveyIds"
                            + " )"
                        + " )"
                    + " ORDER BY d.version",
                    "surveyIds", surveyIds
            );
            if (resetSurveyAnswersData(dao, userAdmin, new LinkedHashSet<>(restOfIds))) {
                return GenericSaveHandle.newSuccess();
            }
            return GenericSaveHandle.newFailure("some-survey-ids-failed");
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            updateFreezedFormAnswersRun(settingsDao, 0);
            LOGGER.error("Ocurrió un error al inicializar respuestas congeladas de formularios, error", ex);
            return GenericSaveHandle.newFailure(ex);
        }
    }
    
    public void initializeSlimReports(final ServletContext servletContext, final ILoggedUser userAdmin) {
        boolean buildAllSlimReports = Boolean.TRUE.equals(Utilities.getSettings().getPendingBuildAllSlimReports());
        try {
            if (buildAllSlimReports) {
                FormUtil.executeBuildAllSlimReports(servletContext, userAdmin);
            }
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            resetDefaultPendingBuildAllSlimReports(settingsDao);
            LOGGER.error("Ocurrió un error al inicializar columns predeterminadas de slimreports, error", ex);
        }
        try {
            if (buildAllSlimReports || Boolean.TRUE.equals(Utilities.getSettings().getPendingRegenerateAllSlimReports())) {
                FormUtil.regenerateAllSlimReports(servletContext, userAdmin);
            }
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            resetDefaultRegenerateAllSlimReports(settingsDao);
            LOGGER.error("Ocurrió un error al regenerar slimreports, error", ex);
        }
    }
    
    public void initializeWorkflowPreviewData(final ServletContext servletContext, final ILoggedUser userAdmin) {
        try {
            if (Boolean.TRUE.equals(Utilities.getSettings().getRecalculateWorkflowPreviewDataRun())) {
                return;
            }
            FormUtil.recalculateWorkflowPreviewRun(servletContext, userAdmin);
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            saveRecalculateWorkflowPreviewDataRun(settingsDao, true);
            Utilities.getSettings().setRecalculateWorkflowPreviewDataRun(true);
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            saveRecalculateWorkflowPreviewDataRun(settingsDao, false);
            Utilities.getSettings().setRecalculateWorkflowPreviewDataRun(false);
            LOGGER.error("Ocurrió un error al recalcular workflow_preview_data, error", ex);
        }
    }
    
    public void initializeWorkflowFormRequestData(final ServletContext servletContext, final ILoggedUser userAdmin) {
        try {
            if (Boolean.TRUE.equals(Utilities.getSettings().getRecalculateWorkflowFormRequestDataRun())) {
                return;
            }
            FormUtil.recalculateWorkflowFormRequestPreviewRun(servletContext, userAdmin);
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            saveWorkflowFormRequestDataRun(settingsDao, true);
            Utilities.getSettings().setRecalculateWorkflowFormRequestDataRun(true);
        } catch (final Exception ex) {
            final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
            saveWorkflowFormRequestDataRun(settingsDao, false);
            Utilities.getSettings().setRecalculateWorkflowFormRequestDataRun(false);
            LOGGER.error("Ocurrió un error al recalcular workflow_form_request_data, error:", ex);
        }
    }

    public void initializeSurveyDataAnswerColumns(final ServletContext servletContext, final ILoggedUser userAdmin) {
        if (Boolean.TRUE.equals(Utilities.getSettings().getMapAllSurveyAnswersColumns())) {
            return;
        }
        final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class, servletContext);
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("mapAllSurveyAnswersColumns", true);
        settingsDao.setValueSetting(currentValues, null);
        Utilities.getSettings().setMapAllSurveyAnswersColumns(true);
    }
    

    private static Integer updateFreezedFormAnswersRun(final ISettingsDAO settingsDao, final Integer freezedFormAnswersRun) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("freezedFormAnswersRun", freezedFormAnswersRun);
        return settingsDao.setValueSetting(currentValues, null);
    }
    
    private static Integer updateRecalculateSurveyMigration(final ISettingsDAO settingsDao, final boolean recalculateSurveyMigration) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("recalculateSurveyMigration", recalculateSurveyMigration);
        return settingsDao.setValueSetting(currentValues, null);
    }
    
    private static Integer resetDefaultPendingBuildAllSlimReports(final ISettingsDAO settingsDao) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("pendingBuildAllSlimReports", true);
        return settingsDao.setValueSetting(currentValues, null);
    }
    
    private static Integer resetDefaultRegenerateAllSlimReports(final ISettingsDAO settingsDao) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("pendingRegenerateAllSlimReports", true);
        return settingsDao.setValueSetting(currentValues, null);
    }

    private static Integer saveRecalculateWorkflowPreviewDataRun(final ISettingsDAO settingsDao, final Boolean value) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("recalculateWorkflowPreviewDataRun", value);
        return settingsDao.setValueSetting(currentValues, null);
    }
    
    private static Integer saveWorkflowFormRequestDataRun(final ISettingsDAO settingsDao, final Boolean value) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("recalculateWorkflowFormRequestDataRun", value);
        return settingsDao.setValueSetting(currentValues, null);
    }



    private boolean resetSurveyAnswersData(final IFormCaptureDAO dao, final ILoggedUser loggedUser) throws QMSException {
        return resetSurveyAnswersData(dao, loggedUser, null); 
    }
    
    private boolean resetSurveyAnswersData(
            final IFormCaptureDAO dao,
            final ILoggedUser loggedUser,
            Set<Long> allFormSurveyIds
    ) throws QMSException {
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        try {
            SurveyRestoreDataDTO metaToRestore;
            final Set<Long> failedFormSurveyIds;
            if (allFormSurveyIds == null || allFormSurveyIds.isEmpty()) {
                metaToRestore = dao.clearSavedData();
                failedFormSurveyIds = freezeSurveyAnswers(dao, metaToRestore, loggedUser);
                resetSurveyAnswersMigration(dao, loggedUser);
            } else {
                metaToRestore = dao.clearSavedData(allFormSurveyIds);
                failedFormSurveyIds = freezeSurveyAnswers(dao, allFormSurveyIds, metaToRestore, loggedUser);
                resetSurveyAnswersMigration(dao, loggedUser, allFormSurveyIds);
            }
            MeasureTime.stop(tStart, "Elapsed time in resetting frozen survey answers.");
            return failedFormSurveyIds.isEmpty();
        } catch (final Exception e) {
            LOGGER.error("failed to reset surveyData");
            MeasureTime.stop(tStart, "Elapsed time in failed attempt to reset frozen survey answers.");
            throw e;
        }
    }
    
    private void resetSurveyAnswersMigration(final IFormCaptureDAO dao, final ILoggedUser loggedUser) {
        resetSurveyAnswersMigration(dao, loggedUser, null);
    }
    
    private void resetSurveyAnswersMigration(
            final IFormCaptureDAO dao,
            final ILoggedUser loggedUser,
            final Set<Long> allFormSurveyIds
    ) {
        final ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        try {
            if (allFormSurveyIds == null || allFormSurveyIds.isEmpty()) {
                dao.refreshSurveyAnswersMigration(loggedUser);
            } else {
                final List<String> masterIds = dao.HQL_findByQuery(" "
                    + " SELECT d.masterId "
                    + " FROM " + Document.class.getCanonicalName() + " d "
                    + " WHERE "
                        + " d.status IN ("
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue()
                        + " )"
                        + " AND d.surveyId IN (:allFormSurveyIds)",
                        ImmutableMap.of("allFormSurveyIds", allFormSurveyIds)
                );
                masterIds.forEach((masterId) -> {
                    try {
                        dao.refreshSurveyAnswersMigration(masterId, loggedUser);
                    } catch (final Exception e) {
                        LOGGER.error("failed to reset survey answers migration for masterId {}", masterId, e);
                    }
                });
            }
            MeasureTime.stop(tStart, "Elapsed time in resetting survey answers migration.");
        } catch (final Exception e) {
            LOGGER.error("failed to reset survey answers migration", e);
            MeasureTime.stop(tStart, "Elapsed time in failed attempt to reset survey answers migration.");
            throw e;
        }
    }

    private void savePrintingFormatSurveyField(
            IFormCaptureDAO dao,
            Map<String, Long> metaIds,
            String name,
            Long printingFormatId,
            Set<String> savedFields,
            Long surveyId
    ) {
        try {
            if (metaIds.containsKey(name)) {
                if (name != null) {
                    String keyName = name + "_" + printingFormatId;
                    boolean saved = savedFields.contains(keyName);
                    if (!saved) {
                        dao.makePersistent(new PrintingFormatSurveyField(printingFormatId, metaIds.get(name)), surveyId);
                        savedFields.add(keyName);
                    } else {
                        LOGGER.warn("Column name `{}` already saved from restored data at `printing_format_survey_field`!", name);
                    }
                }
            } else {
                LOGGER.error("Column name `{}` is missing from restored data at `printing_format_survey_field`!", name);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to save printing format survey field");
        }
    }

    private void saveWebhookSurveyField(
            Map<String, Long> metaIds,
            String name,
            Long webhookId,
            Set<String> savedFields,
            IUntypedDAO dao
    ) {
        try {
            if (!metaIds.containsKey(name)) {
                LOGGER.error("Column name `{}` is missing from restored data at `webhook_survey_field`!", name);
                return;
            }
            if (name == null) {
                return;
            }
            String keyName = name + "_" + webhookId;
            boolean saved = savedFields.contains(keyName);
            if (!saved) {
                Long surveyAnswerMetadataId = metaIds.get(name);
                dao.SQL_updateByQuery(" "
                        + " INSERT INTO webhook_survey_field (webhook_id, survey_answer_metadata_id)"
                        + " VALUES (:webhookId, :surveyAnswerMetadataId)",
                        ImmutableMap.of(
                                "webhookId", webhookId,
                                "surveyAnswerMetadataId", surveyAnswerMetadataId
                        ),
                        0,
                        Collections.singletonList("webhook_survey_field")
                );
            } else {
                LOGGER.warn("Column name `{}` already saved from restored data at `webhook_survey_field`!", name);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to save webhook survey field");
        }
    }

    private void freezeSurveyAnswerMetadatas(
            final IFormCaptureDAO dao, 
            final SurveyRestoreDataDTO restoreData,
            final ILoggedUser loggedUser
    ) {
        final List<SurveyAnswerMetadataFreezeBK> metaToRestore = restoreData.getMetaToRestore();
        if (metaToRestore != null &&! metaToRestore.isEmpty()) {
            // Se vuelve a crear información de `SurveyAnswerMetadata`
            final Set<Long> surveyIds = metaToRestore.stream()
                    .map(SurveyAnswerMetadataFreezeBK::getSurveyId)
                    .collect(Collectors.toSet());
            surveyIds.forEach(surveyId -> freezeSurveyAnswerMetadataBySurvey(surveyId, metaToRestore, dao, loggedUser));
        }
        final List<WebhookAnswerMetadataBK> webhookToRestore = restoreData.getWebhookMetaToRestore();
        if (webhookToRestore != null &&! webhookToRestore.isEmpty()) {
            // Se vuelve a crear información de `SurveyAnswerMetadata`
            final Set<Long> surveyIds = webhookToRestore.stream()
                    .map(WebhookAnswerMetadataBK::getSurveyId)
                    .collect(Collectors.toSet());
            surveyIds.forEach(surveyId -> freezeSurveWebhookMetadataBySurvey(surveyId, webhookToRestore, dao, loggedUser));
        }
    }

    private void freezeSurveyAnswerMetadataBySurvey(
            Long surveyId,
            @Nonnull List<SurveyAnswerMetadataFreezeBK> metaToRestore,
            IFormCaptureDAO dao,
            ILoggedUser loggedUser
    ) {
        final Map<String, Long> metaIds = SurveyUtil.getSurveyAnswerMetadataIds(surveyId);
        final FieldConfigDTO fieldsConfig = SurveyUtil.getFields(
                surveyId,
                false,
                false,
                false,
                null,
                0,
                loggedUser,
                false,
                true,
                false
        );
        final Set<SurveyDataFieldDTO> currentFields = fieldsConfig.getFields();
        final Set<String> savedFields = new HashSet<>(); // Guarda los campos salvados para evitar duplicar registros por items, ya que los items pueden generar múltiples duplas (value, description).
        metaToRestore.stream()
                .filter(m -> m.getSurveyId().equals(surveyId))
                .forEach(dto -> restoreMetadata(dto, surveyId, currentFields, metaIds, savedFields, dao));
    }

    private void freezeSurveWebhookMetadataBySurvey(
            Long surveyId,
            @Nonnull List<WebhookAnswerMetadataBK> metaToRestore,
            IFormCaptureDAO dao,
            ILoggedUser loggedUser
    ) {
        final Map<String, Long> metaIds = SurveyUtil.getSurveyAnswerMetadataIds(surveyId);
        final FieldConfigDTO fieldsConfig = SurveyUtil.getFields(
                surveyId,
                false,
                false,
                false,
                null,
                0,
                loggedUser,
                false,
                true,
                false
        );
        final Set<SurveyDataFieldDTO> currentFields = fieldsConfig.getFields();
        final Set<String> savedFields = new HashSet<>(); // Guarda los campos salvados para evitar duplicar registros por items, ya que los items pueden generar múltiples duplas (value, description).
        metaToRestore.stream()
                .filter(m -> m.getSurveyId().equals(surveyId))
                .forEach(dto -> restoreWebhookMetadata(dto, currentFields, metaIds, savedFields, dao));
    }


    private void restoreMetadata(
            SurveyAnswerMetadataFreezeBK dto,
            Long surveyId,
            Set<SurveyDataFieldDTO> currentFields,
            Map<String, Long> metaIds,
            Set<String> savedFields,
            IFormCaptureDAO dao
    ) {
        String name = dto.getSurveyAnswerFieldName();
        String nameValue = null;
        if (currentFields != null) {
            // Mapea campo de múltiples items a solo dos. Ejemplo: field0_item0, field0_item1, field0_item2 -> field0_description, field0_value
            SurveyDataFieldDTO configField = findSurveyFieldByFieldName(dto.getSurveyAnswerFieldName(), currentFields);
            if (configField != null && configField.getDisableColumnPerOption()) {
                name = configField.getFieldCode() + AnswerPartType.CATALOG_DESCRIPTION.getSuffix();
                nameValue = configField.getFieldCode() + AnswerPartType.CATALOG_VALUE.getSuffix();
            }
        }
        savePrintingFormatSurveyField(dao, metaIds, name, dto.getPrintingFormatId(), savedFields, surveyId);
        savePrintingFormatSurveyField(dao, metaIds, nameValue, dto.getPrintingFormatId(), savedFields, surveyId);
    }


    public void restoreWebhookMetadata(
            WebhookAnswerMetadataBK dto,
            Set<SurveyDataFieldDTO> currentFields,
            Map<String, Long> metaIds,
            Set<String> savedFields,
            IUntypedDAO dao
    ) {
        String name = dto.getSurveyAnswerFieldName();
        String nameValue = null;
        if (currentFields != null) {
            // Mapea campo de múltiples items a solo dos. Ejemplo: field0_item0, field0_item1, field0_item2 -> field0_description, field0_value
            SurveyDataFieldDTO configField = findSurveyFieldByFieldName(dto.getSurveyAnswerFieldName(), currentFields);
            if (configField != null && configField.getDisableColumnPerOption()) {
                name = configField.getFieldCode() + AnswerPartType.CATALOG_DESCRIPTION.getSuffix();
                nameValue = configField.getFieldCode() + AnswerPartType.CATALOG_VALUE.getSuffix();
            }
        }
        saveWebhookSurveyField(metaIds, name, dto.getWebhookId(), savedFields, dao);
        saveWebhookSurveyField(metaIds, nameValue, dto.getWebhookId(), savedFields, dao);
    }

    @Nullable
    public SurveyDataFieldDTO findSurveyFieldByFieldName(String answerFieldName, Set<SurveyDataFieldDTO> currentFields) {
        if (currentFields == null || currentFields.isEmpty() || answerFieldName == null || answerFieldName.isEmpty()) {
            return null;
        }
        return currentFields.stream()
                .filter(f -> f.getName().equals(answerFieldName)
                        || f.getFieldCode().equals(answerFieldName.replace(AnswerPartType.CATALOG_DESCRIPTION.getSuffix(), ""))
                        || f.getFieldCode().equals(answerFieldName.replace(AnswerPartType.CATALOG_VALUE.getSuffix(), "")))
                .findFirst()
                .orElse(null);
    }

    private Set<Long> freezeSurveyAnswers(
            final IFormCaptureDAO dao,
            final SurveyRestoreDataDTO metaToRestore,
            final ILoggedUser loggedUser
    ) {
        final Set<Long> allFormSurveyIds = dao.getAllFormSurveyIds();
        return freezeSurveyAnswers(dao, allFormSurveyIds, metaToRestore, loggedUser);
    }

    private Set<Long> freezeSurveyAnswers(
            final IFormCaptureDAO dao,
            final Set<Long> allFormSurveyIds, 
            final SurveyRestoreDataDTO metaToRestore,
            final ILoggedUser loggedUser
    ) {
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        final Set<Long> failedFormSurveyIds = new HashSet<>();
        try {
            final Integer inserted = SurveyUtil.freezeSurveyData(dao);
            if (inserted == null || inserted == 0) {
                LOGGER.warn("There are not any surveys to froze answers.");
                return failedFormSurveyIds;
            }
            final IDynamicTableDAO tableDao = dao.getBean(IDynamicTableDAO.class);


            
            final SurveyUtilCache cache = new SurveyUtilCache();

            allFormSurveyIds.forEach(surveyId -> {
                try {
                    final DynamicFieldTable result = dao.createFormsTable(surveyId, cache, tableDao, loggedUser);
                    if (result == null) {
                        LOGGER.error("Failed creating form answers table for {}.", surveyId);
                        failedFormSurveyIds.add(surveyId);
                    }
                    dao.recalculateSurveyDataAnswerColumns(surveyId, cache, loggedUser);
                } catch (final Exception e) {
                    LOGGER.error("Failed creating form answers table for {}.", surveyId, e);
                    failedFormSurveyIds.add(surveyId);
                }
            });

            allFormSurveyIds.forEach(surveyId -> {
                try {
                    if (failedFormSurveyIds.contains(surveyId)) {
                        return;
                    }
                    final FillManager fillManager = new FillManager(surveyId, dao, loggedUser);
                    final Long answersCount = importFormAnswers(fillManager, surveyId, dao, loggedUser);
                    LOGGER.debug("Imported {} form answers", answersCount);
                } catch (final Exception e) {
                    LOGGER.error("Failed importing form previous answers for {}.", surveyId, e);
                    failedFormSurveyIds.add(surveyId);
                }
            });

            final Set<Long> masterSurveyIds = dao.getMasterIdFormSurveyIds(allFormSurveyIds);
            masterSurveyIds.forEach(surveyId -> {
                try {
                    if (failedFormSurveyIds.contains(surveyId)) {
                        return;
                    }
                    final Long othersAnswersCount = importOtherFormVersionAnswers(surveyId, failedFormSurveyIds, dao);
                    LOGGER.debug("Imported {} other form version answers", othersAnswersCount);
                } catch (final Exception e) {
                    LOGGER.error("Failed importing other form previous answers for {}.", surveyId, e);
                    failedFormSurveyIds.add(surveyId);
                }
            });

            if (!failedFormSurveyIds.isEmpty()) {
                LOGGER.error(
                        "Failed to create answers table for {} forms, survey ids: {}",
                        failedFormSurveyIds.size(),
                        StringUtils.join(failedFormSurveyIds, ", "));
            }
            if (allFormSurveyIds.isEmpty()) {
                final Set<Long> failedPollSurveyIds = new HashSet<>();

                final Set<Long> pollSurveyIds = dao.getPollSurveyIds();

                pollSurveyIds.forEach(surveyId -> {
                    try {
                        final DynamicFieldTable result = dao.createPollsTable(surveyId, cache, tableDao, loggedUser);
                        if (result == null) {
                            LOGGER.error("Failed creating poll answers table for {}.", surveyId);
                            failedPollSurveyIds.add(surveyId);
                        }
                    } catch (final Exception e) {
                        LOGGER.error("Failed creating poll answers table for {}.", surveyId, e);
                        failedPollSurveyIds.add(surveyId);
                    }
                });

                pollSurveyIds.forEach(surveyId -> {
                    try {
                        if (failedPollSurveyIds.contains(surveyId)) {
                            return;
                        }
                        final Long pollsCount = importPollsData(surveyId, dao, loggedUser);
                        LOGGER.debug("Imported {} poll answers", pollsCount);
                    } catch (final Exception e) {
                        LOGGER.error("Failed importing poll previous answers for {}.", surveyId, e);
                    }
                });

                if (!failedPollSurveyIds.isEmpty()) {
                    LOGGER.error(
                            "Failed to create answers table for {} poll, survey ids: {}",
                            failedPollSurveyIds.size(),
                            StringUtils.join(failedPollSurveyIds, ", "));
                }

                final Set<Long> auditSurveyIds = dao.getAuditSurveyIds();

                final Set<Long> failedAuditSurveyIds = new HashSet<>();

                auditSurveyIds.forEach(surveyId -> {
                    try {
                        final DynamicFieldTable result = dao.createAuditsTable(surveyId, cache, tableDao, loggedUser);
                        if (result == null) {
                            LOGGER.error("Failed creating audit answers table for {}.", surveyId);
                            failedAuditSurveyIds.add(surveyId);
                        }
                    } catch (final Exception e) {
                        LOGGER.error("Failed creating audit answers table for {}.", surveyId, e);
                        failedAuditSurveyIds.add(surveyId);
                    }
                });

                auditSurveyIds.forEach(surveyId -> {
                    try {
                        if (failedAuditSurveyIds.contains(surveyId)) {
                            return;
                        }
                        final Long auditsCount = importAuditsData(surveyId, dao, loggedUser);
                        LOGGER.debug("Imported {} audit answers", auditsCount);
                    } catch (final Exception e) {
                        LOGGER.error("Failed importing audit answers table for {}.", surveyId, e);
                    }
                });

                if (!failedAuditSurveyIds.isEmpty()) {
                    LOGGER.error(
                            "Failed to create answers table for {} audits, survey ids: {}",
                            failedAuditSurveyIds.size(),
                            StringUtils.join(failedAuditSurveyIds, ", "));
                }
            }
        } finally {
            freezeSurveyAnswerMetadatas(dao, metaToRestore, loggedUser);
        }
        MeasureTime.stop(tStart, "Elapsed time freezing survey answers.");
        return failedFormSurveyIds;
    }
    
    public Long importFormAnswers(final FillManager fillManager, final Long surveyId, final IFormCaptureDAO dao, final ILoggedUser loggedUser) {
        final Long count = dao.countImportFormAnswers(surveyId);
        if (count == null || count == 0) {
            LOGGER.trace("No answers to import for survey {}", surveyId);
            return 0L;
        }
        final double numberPages = Math.ceil(count > RECORDS_PER_PAGE ? count / RECORDS_PER_PAGE : 1.0);
        Long updatedRows = 0L;
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECORDS_PER_PAGE.intValue());
        for (int currentPage = 0; currentPage < numberPages; currentPage++) {
            page.setPage(currentPage);
            updatedRows += dao.importFormAnswers(surveyId, fillManager, page, loggedUser);
        }
        return updatedRows;
    }
    
    public Long importOtherFormVersionAnswers(
            final Long surveyId,
            final Set<Long> failedFormSurveyIds,
            final IFormCaptureDAO dao
    ) throws QMSException {
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        final String masterId = dao.HQL_findSimpleString(" "
                + " SELECT c.masterId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.surveyId = :surveyId",
                ImmutableMap.of("surveyId", surveyId)
        );
        final Long countOtherVersions = dao.countImportOtherFormVersionAnswers(masterId);
        if (countOtherVersions == null || countOtherVersions == 0 || Objects.equals(countOtherVersions, 1L)) {
            return 0L;
        }
        final List<Long> formVersions = dao.getSurveyIdsByMasterId(masterId);
        if (formVersions == null || formVersions.isEmpty() || Objects.equals(formVersions.size(), 1)) {
            return 0L;
        }
        final int noVersions = formVersions.size() - 1;
        Long updatedRows = 0L;
        for (int i = 0; i < noVersions; i++) {
            final Long sourceSurveyId = formVersions.get(i);
            final Long targetSurveyId = formVersions.get(i + 1);
            if (failedFormSurveyIds.contains(sourceSurveyId)) {
                LOGGER.error("Failed importing other form answer as source survey table could not be created for survey id {}.", surveyId);
                break;
            }
            if (failedFormSurveyIds.contains(targetSurveyId)) {
                LOGGER.error("Failed importing other form answer as target target table could not be created for survey id {}.", surveyId);
                break;
            }
            updatedRows += importAnswersFromSourceTable(sourceSurveyId, targetSurveyId, dao);
        }
        MeasureTime.stop(tStart, "Elapsed time importing " + updatedRows + " answers for survey " + surveyId);
        return updatedRows;
    }

    public Long importAnswersFromSourceTable(final Long sourceSurveyId, final Long targetSurveyId, final IFormCaptureDAO dao) throws QMSException {
        final Long count = dao.countImportAnswers(sourceSurveyId);
        if (count == null || count == 0) {
            return 0L;
        }
        final DynamicFieldTable targetTable = dao.getDynamicTableOfSurvey(targetSurveyId);
        if (targetTable == null) {
            LOGGER.error("Failed importing other form answer as target table could not be loaded for survey id {}.", targetSurveyId);
            return 0L;
        }
        Long updatedRows = 0L;
        updatedRows += dao.copyAnswers(targetTable, sourceSurveyId);
        return updatedRows;
    }
    
    public Long importPollsData(final Long surveyId, final IFormCaptureDAO dao, final ILoggedUser loggedUser) {
        final FillManager formManager = new FillManager(surveyId, dao, loggedUser);
        final Long count = dao.countImportAnswers(surveyId);
        if (count == null || count == 0) {
            return 0L;
        }
        final double numberPages = Math.ceil(count > RECORDS_PER_PAGE ? count / RECORDS_PER_PAGE : 1.0);
        Long updatedRows = 0L;
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECORDS_PER_PAGE.intValue());
        for (int currentPage = 0; currentPage < numberPages; currentPage++) {
            page.setPage(currentPage);
            updatedRows += dao.importPollsData(surveyId, formManager, page, loggedUser);
        }
        return updatedRows;
        
    }

    public Long importAuditsData(final Long surveyId, final IFormCaptureDAO dao, final ILoggedUser loggedUser) {
        final FillManager formManager = new FillManager(surveyId, dao, loggedUser);
        final Long count = dao.countImportAnswers(surveyId);
        if (count == null || count == 0) {
            LOGGER.trace("No audits records to import of survey {}", surveyId);
            return 0L;
        }
        final double numberPages = Math.ceil(count > RECORDS_PER_PAGE ? count / RECORDS_PER_PAGE : 1.0);
        Long updatedRows = 0L;
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECORDS_PER_PAGE.intValue());
        for (int currentPage = 0; currentPage < numberPages; currentPage++) {
            page.setPage(currentPage);
            updatedRows += dao.importAuditsData(surveyId, formManager, page, loggedUser);
        }
        return updatedRows;        
    }

}

