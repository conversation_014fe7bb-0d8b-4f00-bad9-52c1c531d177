package qms.document.pending.imp;

import DPMS.Mapping.Document;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.document.pending.DocumentPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToAssignReader extends DocumentPendingOperations {
    
  private static final String TO_ASSIGN_READER =  ""
            + " FROM " 
                + User.class.getCanonicalName() + " usr, "  
                + Document.class.getCanonicalName() + " doc "
            + " CROSS JOIN " + Settings.class.getCanonicalName() + " s"
            + " JOIN doc.documentType type"
            + " LEFT JOIN doc.department bud"
            + " LEFT JOIN doc.businessUnit bu"
            + " WHERE s.id = 1 AND s.readers = 1"
            + " AND type.mustRead = 1"
            + " AND type.mustAssignReaders = 1"
            + " AND doc.readers = 0 "
            + " AND doc.deleted = 0"
            + " AND doc.status = " + Document.STATUS.ACTIVE.getValue()
            + " AND usr.id IN ("
                  //donde soy encargado de documentos por planta
                  + "bu.documentManagerId,"
                  //donde soy encargado de documentos por departamento
                  + "bud.documentManagerId"
            + ") ";
  
    private final String RESPONSIBLE = " AND usr.id = :userId ";

    public ToAssignReader(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS_DOCUMENT);
        setQuery(TO_ASSIGN_READER);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("usr.id");
        setPendingType(getType(APE.DOCUMENT_TO_ASSIGN_READER));
        setModuleKey(MODULE);
        setBase(Document.class);
        setOwnerFieldFilter(RESPONSIBLE);
        setOperationType(ApeOperationType.STRONG);
    }


}