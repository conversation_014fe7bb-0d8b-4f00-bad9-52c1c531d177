package qms.document.pending.imp;

import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.Request;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.document.pending.DocumentPendingOperations;
import qms.workflow.util.WorkflowRequestStatus;

/**
 *
 * <AUTHOR>
 */
public class ToRenew extends DocumentPendingOperations {
    
    private static final String TO_RENEW = ""
            + " FROM " + Document.class.getCanonicalName() + " doc"
            + " , " + User.class.getCanonicalName() + " usr"
            + " , " + Settings.class.getCanonicalName() + " s"
            + " JOIN doc.documentType type"
            + " LEFT JOIN doc.department dep"
            + " LEFT JOIN doc.author aut"
            + " LEFT JOIN doc.businessUnit dbu "
            + " LEFT JOIN usr.puestos pst "
            + " LEFT JOIN pst.perfil prf "
            + " LEFT JOIN pst.une bu "
            + " WHERE doc.deleted = 0"
            + " AND type.documentControlledType = '" + DocumentType.documentControlledType.CONTROLLED.toString() + "'"
            + " AND doc.status IN ( " 
                + Document.STATUS.ACTIVE.getValue() 
                + ", " + Document.STATUS.AUTORIZATION.getValue()
                + ", " + Document.STATUS.IN_EDITION.getValue() 
            + " )"
            + " AND ("
                + " ("
                    + " s.expiredDocumentPendingModuleManager = 1"
                    + " AND prf.intBUsuarioPlanta = 1"
                    + " AND prf.intBEncargadoDocumento = 1"
                    + " AND prf.deleted = 0"
                    + " AND ( "
                       + " usr.root = 1"
                       + " OR bu.id = doc.businessUnitId"
                    + " )"
                + " ) OR ("
                    + " s.expiredDocumentPendingDocumentManager = 1"
                    + " AND (  "
                            + " dep.documentManagerId = usr.id"
                            + " OR dbu.documentManagerId = usr.id"
                    + " )"
                + " )"
                + " OR aut.id = usr.id"
            + " )"
            + " AND usr.status = " + User.STATUS.ACTIVE.getValue()
            + " AND usr.deleted = 0"
            + " AND current_date() > doc.expirationDate"
            + " AND NOT exists ( "
                + " SELECT 1 "
                + " FROM "+ Request.class.getCanonicalName() + " req "
                + " where req.document.id = doc.id "
                + " AND req.type IN ( "
                    + Request.TYPE.MODIFY.getValue()
                    + ", " + Request.TYPE.EDIT_DETAILS.getValue()
                    + ", " + Request.TYPE.APROVE.getValue()
                    + ", " + Request.TYPE.CANCEL.getValue()
                + " )"
                + " AND req.status IN ( "
                    + WorkflowRequestStatus.VERIFING.getValue()
                    + ", " + WorkflowRequestStatus.APROVING.getValue()
                    + ", " + WorkflowRequestStatus.RETURNED.getValue()
                + " )  "
            + ")";
    
    public static final String TO_RENEW_SOON = " EXISTS ("
                + " SELECT 1"
                + " FROM " + Document.class.getCanonicalName() + " doc"
                + " , " + User.class.getCanonicalName() + " usr"
                + " , " + Settings.class.getCanonicalName() + " s"
                + " JOIN doc.documentType type"
                + " LEFT JOIN doc.department dep"
                + " LEFT JOIN doc.author aut"
                + " LEFT JOIN doc.businessUnit dbu "
                + " LEFT JOIN usr.puestos pst "
                + " LEFT JOIN pst.perfil prf "
                + " LEFT JOIN pst.une bu "
                + " WHERE doc.id = :documentId "
                + " AND usr.id = :userId"
                + " AND doc.deleted = 0"
                + " AND type.documentControlledType = '" + DocumentType.documentControlledType.CONTROLLED.toString() + "'"
                + " AND doc.status IN ( " 
                    + Document.STATUS.ACTIVE.getValue() 
                    + ", " + Document.STATUS.AUTORIZATION.getValue()
                    + ", " + Document.STATUS.IN_EDITION.getValue() 
                + " )"
                + " AND ("
                    + " usr.root = 1"
                    + " OR ("
                        + " s.expiredDocumentPendingModuleManager = 1"
                        + " AND prf.intBUsuarioPlanta = 1"
                        + " AND prf.intBEncargadoDocumento = 1"
                        + " AND prf.deleted = 0"
                        + " AND bu.id = doc.businessUnitId"
                    + " ) OR ("
                        + " s.expiredDocumentPendingDocumentManager = 1"
                        + " AND (  "
                                + " dep.documentManagerId = usr.id"
                                + " OR dbu.documentManagerId = usr.id "
                        + " )"
                    + " )"
                    + " OR aut.id = usr.id"
                + " )"
                + " AND usr.status = " + User.STATUS.ACTIVE.getValue()
                + " AND usr.deleted = 0"
                + " AND doc.expirationDate BETWEEN current_date() AND DATEADD(DAY, s.daysAnticipation, current_date())"
                + " AND NOT exists ( "
                    + " SELECT 1 "
                    + " FROM "+ Request.class.getCanonicalName() + " req "
                    + " where req.document.id = doc.id "
                    + " AND req.type IN ( "
                        + Request.TYPE.MODIFY.getValue()
                        + ", " + Request.TYPE.EDIT_DETAILS.getValue()
                        + ", " + Request.TYPE.APROVE.getValue()
                        + ", " + Request.TYPE.CANCEL.getValue()
                    + " )"
                    + " AND req.status IN ( "
                        + WorkflowRequestStatus.VERIFING.getValue()
                        + ", " + WorkflowRequestStatus.APROVING.getValue()
                        + ", " + WorkflowRequestStatus.RETURNED.getValue()
                    + " )  "
                + ")"
            + ")";
    
    private final String DOCUMENT_MANAGER = " AND usr.id = :userId ";
    
    public ToRenew(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS_DOCUMENT);
        setQuery(TO_RENEW);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("usr.id");
        setPendingType(getType(APE.DOCUMENT_TO_RENEW));
        setModuleKey(MODULE);
        setBase(Document.class);
        setOwnerFieldFilter(DOCUMENT_MANAGER);
        setOperationType(ApeOperationType.STRONG);
    }
}