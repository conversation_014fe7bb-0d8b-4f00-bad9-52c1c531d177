package qms.document.util;

import DPMS.Mapping.Files;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import qms.access.dto.ILoggedUser;
import qms.util.LinkedSelector;
import qms.util.dto.MultiFileDTO;
import qms.util.interfaces.ILinkedSelector;

/**
 *
 * <AUTHOR>
 */
public class MultiFileSelector extends LinkedSelector implements ILinkedSelector { 

    @Override
    public List<MultiFileDTO> getGroundValues(final Long baseId, final IUntypedDAO dao, ILoggedUser loggedUser) {
        final List<MultiFileDTO> values = dao.HQL_findByQuery(""
                + " SELECT new " + MultiFileDTO.class.getCanonicalName() + "("
                    + " c.id as id,"
                    + " c.contentSize AS size,"
                    + " c.description AS description," 
                    + " a.lastModifiedDate AS lastModifiedDate,"
                    + " u.description AS lastModifiedBy,"
                    + " a.stage AS stage,"
                    + " c.extension AS extension,"    
                    + " c.contentType AS contentType"    
                + " )"
                + " FROM " + getBaseClazzName() + " a"
                + " CROSS JOIN " + Files.class.getCanonicalName() + " c"
                + " CROSS JOIN " + User.class.getCanonicalName() + " u" 
                + " WHERE a.id.fileId = c.id"
                + " AND c.lastModifiedBy = u.id"
                + " AND a.id." + getGroundIdField() + " = :baseId",
                ImmutableMap.of("baseId", baseId),
                true,
                null,
                0
        );
        return values;
    }

    
}
