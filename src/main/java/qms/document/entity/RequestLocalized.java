package qms.document.entity;

import DPMS.Mapping.DocumentType;
import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "request")
public class RequestLocalized extends StandardEntity<RequestLocalized> implements IRequest, Serializable {

    private static final long serialVersionUID = 1L;
    
    private String documentMasterId;
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Long autorId;

    private Integer scope;

    private String documentCode;
    private Date creationDate;
    private Integer type;
    private String reazon;
    private DocumentType documentType;
    private String version;
    private UserRef author;
    private BusinessUnitRef businessUnit;
    private BusinessUnitDepartmentRef department;

    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Integer enablePdfViewer;
    private String authorizersNames;
    private Boolean restrictRecordsByDepartment = false;
    private Boolean validateAccessFormDepartment = false;
    private String restrictRecordsField;
    private Long restrictRecordsObjId;

    public RequestLocalized() {
    }

    public RequestLocalized(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Basic
    @Column(name = "document_master_id", length = 36)
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Basic(optional = false)
    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Basic(optional = false)
    @Column(name = "creation_date", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Basic(optional = false)
    public String getReazon() {
        return reazon;
    }

    public void setReazon(String reazon) {
        this.reazon = reazon;
    }

    @ManyToOne
    @JoinColumn(name = "document_type", referencedColumnName = "document_type_id")
    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "autor_id", referencedColumnName = "user_id")
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id", updatable = false, insertable = false)
    public BusinessUnitRef getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitRef businessUnit) {
        this.businessUnit = businessUnit;
    }
    
    @ManyToOne
    @JoinColumn(name = "business_unit_department_id", updatable = false, insertable = false)
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof RequestLocalized)) {
            return false;
        }
        RequestLocalized other = (RequestLocalized) object;
        return !((this.id == null && other.id != null)
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "qms.document.entity.RequestLocalized[ id=" + id + " ]";
    }

    /**
     * @return the scope
     */
    @Column(name = "scope")
    public Integer getScope() {
        return scope;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    /**
     * @param scope the scope to set
     */
    public void setScope(Integer scope) {
        this.scope = scope;
    }

    @Column(name = "autor_id", updatable = false, insertable = false)
    @Override
    public Long getAutorId() {
        return autorId;
    }

    public void setAutorId(Long autorId) {
        this.autorId = autorId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "enable_pdf_viewer")
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }

    @Column(name = "authorizers_names")
    public String getAuthorizersNames() {
        return authorizersNames;
    }

    public void setAuthorizersNames(String authorizersNames) {
        this.authorizersNames = authorizersNames;
    }
    
    @Type(type = "numeric_boolean")
    @Column(name = "restrict_records_department")
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "validate_access_department")
    public Boolean getValidateAccessFormDepartment() {
        return validateAccessFormDepartment;
    }

    public void setValidateAccessFormDepartment(Boolean validateAccessFormDepartment) {
        this.validateAccessFormDepartment = validateAccessFormDepartment;
    }

    @Column(name = "restrict_records_field")
    public String getRestrictRecordsField() {
        return restrictRecordsField;
    }

    public void setRestrictRecordsField(String restrictRecordsField) {
        this.restrictRecordsField = restrictRecordsField;
    }

    @Column(name = "restrict_records_obj_id")
    public Long getRestrictRecordsObjId() {
        return restrictRecordsObjId;
    }

    public void setRestrictRecordsObjId(Long restrictRecordsObjId) {
        this.restrictRecordsObjId = restrictRecordsObjId;
    }
    
}
