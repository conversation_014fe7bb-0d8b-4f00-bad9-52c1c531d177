package qms.document.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class DocumentTraceabilityUserPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long documentTraceabilityId;
    private Long authorizedBy;
    private Integer authorizedIndex;

    public DocumentTraceabilityUserPK(Long documentTraceabilityId, Long authorizedBy, Integer authorizedIndex) {
        this.documentTraceabilityId = documentTraceabilityId;
        this.authorizedBy = authorizedBy;
        this.authorizedIndex = authorizedIndex;
    }

    public DocumentTraceabilityUserPK() {

    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "document_traceability_id")
    public Long getDocumentTraceabilityId() {
        return documentTraceabilityId;
    }

    public void setDocumentTraceabilityId(Long documentTraceabilityId) {
        this.documentTraceabilityId = documentTraceabilityId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "authorized_by")
    public Long getAuthorizedBy() {
        return authorizedBy;
    }

    public void setAuthorizedBy(Long authorizedBy) {
        this.authorizedBy = authorizedBy;
    }

    @Basic(optional = false)
    @Column(name = "authorized_index")
    public Integer getAuthorizedIndex() {
        return authorizedIndex;
    }

    public void setAuthorizedIndex(Integer authorizedIndex) {
        this.authorizedIndex = authorizedIndex;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 83 * hash + Objects.hashCode(this.documentTraceabilityId);
        hash = 83 * hash + Objects.hashCode(this.authorizedBy);
        hash = 83 * hash + Objects.hashCode(this.authorizedIndex);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DocumentTraceabilityUserPK other = (DocumentTraceabilityUserPK) obj;
        if (!Objects.equals(this.documentTraceabilityId, other.documentTraceabilityId)) {
            return false;
        }
        if (!Objects.equals(this.authorizedBy, other.authorizedBy)) {
            return false;
        }
        if (!Objects.equals(this.authorizedIndex, other.authorizedIndex)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DocumentTraceabilityUserPK{" + "documentTraceabilityId=" + documentTraceabilityId + ", authorizedBy=" + authorizedBy + ", authorizedIndex=" + authorizedIndex + '}';
    }

}
