package qms.document.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.interfaces.IAuditableCompositeEntity;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "request_history")
public class RequestHistory extends CompositeStandardEntity<RequestHistoryPK> 
        implements Serializable, IAuditableCompositeEntity<RequestHistoryPK> {

    private RequestHistoryPK id;
    private Long businessUnitDepartmentId;
    private String code;
    private String description;
    private Integer status = 1;
    private Integer deleted = 0;
    private Date lastModifiedDate;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public RequestHistory() {
    }

    public RequestHistory(Long version, Long requestId) {
        this.id = new RequestHistoryPK(version, requestId);
    }

    @Override
    @EmbeddedId
    public RequestHistoryPK getId() {
        return id;
    }

    @Override
    public RequestHistoryPK identifuerValue() {
        return id;
    }

    @Override
    public void setId(RequestHistoryPK id) {
        this.id = id;
    }

    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic(optional = false)
    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Basic(optional = false)
    @Column(name = "created_date")
    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Basic(optional = false)
    @CreatedBy
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }
 
    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Override
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RequestHistory other = (RequestHistory) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.document.entity.RequestHistory{" + "id=" + id + '}';
    }
}
