package qms.document.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Embeddable
public class DocumentHistoryModifyedPK implements Serializable {

    private Long documentId;
    private Long previousId;

    public DocumentHistoryModifyedPK() {
    }

    public DocumentHistoryModifyedPK(Long documentId, Long previousId) {
        this.documentId = documentId;
        this.previousId = previousId;
    }

    @Basic(optional = false)
    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Basic(optional = false)
    @Column(name = "previous_id")
    public Long getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 41 * hash + Objects.hashCode(this.documentId);
        hash = 41 * hash + Objects.hashCode(this.previousId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DocumentHistoryModifyedPK other = (DocumentHistoryModifyedPK) obj;
        if (!Objects.equals(this.documentId, other.documentId)) {
            return false;
        }
        if (!Objects.equals(this.previousId, other.previousId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DocumentHistoryModifyedPK{" + "documentId=" + documentId + ", previousId=" + previousId + '}';
    }

}
