package qms.document.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Embeddable
public class RequestHistoryPK implements Serializable {

    private Long version;
    private Long requestId;

    public RequestHistoryPK(Long version, Long requestId) {
        this.requestId = requestId;
        this.version = version;
    }

    public RequestHistoryPK() {
    }

    @Basic(optional = false)
    @Column(name = "version")
    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Basic(optional = false)
    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 11 * hash + Objects.hashCode(this.version);
        hash = 11 * hash + Objects.hashCode(this.requestId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RequestHistoryPK other = (RequestHistoryPK) obj;
        if (!Objects.equals(this.version, other.version)) {
            return false;
        }
        if (!Objects.equals(this.requestId, other.requestId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "RequestHistoryPK{" + "version=" + version + ", requestId=" + requestId + '}';
    }

}
