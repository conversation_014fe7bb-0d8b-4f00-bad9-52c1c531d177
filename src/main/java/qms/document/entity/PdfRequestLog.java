package qms.document.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.util.interfaces.ILinkedCompositeEntity;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = PdfRequestLog.PDF_REQUEST_LOG_SQL)
public class PdfRequestLog extends CompositeStandardEntity<PdfRequestLogPK>
        implements Serializable, ILinkedCompositeEntity<PdfRequestLogPK> {

    private static final long serialVersionUID = 1L;
    public static final String PDF_REQUEST_LOG_SQL = "("
            + " SELECT log.pdf_conversion_log_id, req.id as request_id"
            + " FROM pdf_conversion_log log"
            + " INNER JOIN files f"
            + " ON f.id = log.file_id"
            + " INNER JOIN request req"
            + " ON req.file_id = f.id"
        + ")";

    private PdfRequestLogPK id;
    private PdfConversionLog log;
    private RequestRef request;

    @Override
    public PdfRequestLogPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public PdfRequestLogPK getId() {
        return id;
    }

    @Override
    public void setId(PdfRequestLogPK id) {
        this.id = id;
    }

    @JoinColumn(name = "pdf_conversion_log_id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public PdfConversionLog getLog() {
        return log;
    }

    public void setLog(PdfConversionLog log) {
        this.log = log;
    }

    @JoinColumn(name = "request_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public RequestRef getRequest() {
        return request;
    }

    public void setRequest(RequestRef request) {
        this.request = request;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PdfRequestLog other = (PdfRequestLog) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PdfRequestLog{" + "id=" + id + '}';
    }

}
