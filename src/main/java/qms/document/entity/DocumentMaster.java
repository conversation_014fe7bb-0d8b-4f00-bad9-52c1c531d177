package qms.document.entity;

import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import Framework.Config.BaseDomainObject;
import static Framework.Config.StandardEntity.IS_DELETED;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitId;
import qms.util.interfaces.IDescription;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = DocumentMaster.DOCUMENT_MASTER)
@Immutable
public class DocumentMaster extends BaseDomainObject<String> implements Serializable, IDescription, IBusinessUnitDepartmentId, IBusinessUnitId {

    private static final long serialVersionUID = -6298738639563325432L;
    public static final String DOCUMENT_MASTER = ""
        + " ("
            + " SELECT d.master_id "
                + " ,d.vch_clave "
                + " ,d.vch_descripcion "
                + " ,d.int_estado "
                + " ,d.int_borrado "
                + " ,d.business_unit_id "
                + " ,d.business_unit_department_id "
                + " ,n.path"
                + " ,max(d.version) version"
            + " FROM document d"
            + " JOIN tblnodo n on d.node_id = n.intnodoid"
            + " WHERE"
                + " d.int_estado in (1,0)" //<-- Solo activos para traer el valor correcto de "version"
            + " GROUP BY d.master_id "
                + " ,d.vch_clave "
                + " ,d.vch_descripcion "
                + " ,d.int_estado "
                + " ,d.int_borrado "
                + " ,d.business_unit_id "
                + " ,d.business_unit_department_id "
                + " ,n.path"
        + " )"
    ;

    private String id;
    private String code;
    private String description;
    private String version;
    private String path;
    private Integer status;
    private Integer deleted;
    
    private BusinessUnitDepartment businessUnitDepartment;
    private BusinessUnit businessUnit;

    private Long businessUnitId;
    private Long businessUnitDepartmentId;

    @Id
    @Column(name = "master_id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    
    @Column(name = "vch_clave")
    public String getCode() {
        return code;
    }

    public void setCode(String clave) {
        this.code = clave;
    }

    @Column(name = "vch_descripcion")
    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer estatus) {
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_id", insertable = false, updatable = false)
    public BusinessUnit getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnit businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_department_id", insertable = false, updatable = false)
    public BusinessUnitDepartment getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartment businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Override
    public String identifuerValue() {
        return this.id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

}
