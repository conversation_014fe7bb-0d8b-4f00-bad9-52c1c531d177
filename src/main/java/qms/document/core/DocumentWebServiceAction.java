/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package qms.document.core;

import DPMS.DAO.HibernateDAO_Document;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.Mapping.DocumentSimple;
import Framework.Action.DefaultAction;
import Framework.Config.SortedPagedFilter;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR>
 */
public class DocumentWebServiceAction extends DefaultAction {

    private String ubicacionTecnica;
    private String equipo;
    private String operacion;
    private String contadorHojaRuta;

    @Override
    public String execute() throws Exception {
        final SortedPagedFilter filter = new SortedPagedFilter();
        final IDocumentDAO dao = getBean(IDocumentDAO.class);
        if (!filter.getCriteria().containsKey("deleted")) {
            filter.getCriteria().put("deleted", "0");
        }
        boolean isValidDynamicSearch = true;
        final HashMap<String, String> criterios = new HashMap<>();
        String code;
        if (!ubicacionTecnica.isEmpty()) {
            code = "ubicacionTecnica";
            criterios.put(code, ubicacionTecnica);
            final Boolean ubicacionTecnicaField = dynamicFieldValidate(dao, code);
            isValidDynamicSearch = isValidDynamicSearch && ubicacionTecnicaField;
        }
        if (!equipo.isEmpty()) {
            code = "equipo";
            criterios.put(code, equipo);
            final Boolean dynamicFieldEquipo = dynamicFieldValidate(dao, code);
            isValidDynamicSearch = isValidDynamicSearch && dynamicFieldEquipo;
        }
        if (!operacion.isEmpty()) {
            code = "operacion";
            criterios.put(code, operacion);
            final Boolean dynamicFieldOperacion = dynamicFieldValidate(dao, code);
            isValidDynamicSearch = isValidDynamicSearch && dynamicFieldOperacion;
        }
        if (!contadorHojaRuta.isEmpty()) {
            code = "contadorHojaRuta";
            criterios.put(code, contadorHojaRuta);
            final Boolean dynamicFieldContador = dynamicFieldValidate(dao, code);
            isValidDynamicSearch = isValidDynamicSearch && dynamicFieldContador;
        }

        String id = "";

        if (isValidDynamicSearch) {
            filter.setDynamicSearchEnabled(true);
            filter.setDynamicFieldCriteria(criterios);
            filter.getCriteria().put("<filtered-entity>", 
                    HibernateDAO_Document.ACTIVE_DOCUMENT + " AND " + HibernateDAO_Document.ONLY_DOCUMENT_MODULE
            );
            final GridInfo<DocumentSimple> result = dao.getRows(filter);
            final List data = result.getData();
            if (data != null && !data.isEmpty()) {
                final Map<String, Object> document = (HashMap) data.get(0);
                final Set<String> keys = document.keySet(); 
                Boolean validDynamicResult = true;
                if (!ubicacionTecnica.isEmpty()) {
                    validDynamicResult = validDynamicResult && keys.stream().anyMatch((key) -> key.startsWith("ubicacionTecnica"));
                }
                if (!equipo.isEmpty()) {
                    validDynamicResult = validDynamicResult && keys.stream().anyMatch((key) -> key.startsWith("equipo"));
                }
                if (!operacion.isEmpty()) {
                    validDynamicResult = validDynamicResult && keys.stream().anyMatch((key) -> key.startsWith("operacion"));
                }
                if (!contadorHojaRuta.isEmpty()) {
                    validDynamicResult = validDynamicResult && keys.stream().anyMatch((key) -> key.startsWith("contadorHojaRuta"));
                }
                if (validDynamicResult) {
                    id = "" + document.get("id");
                } else {
                    getLogger().info(
                        "Resultados de búsqueda inválidos. Revisar la configuración de campos dinámicos"
                        + " en el tipo de documento para contadorHojaRuta {}, equipo {}, operacion {}, ubicacionTecnica {}.",
                        new Object[]{
                            contadorHojaRuta, equipo, operacion, ubicacionTecnica
                        });
                }
            } else {
                getLogger().info(
                        "No se encontró ningún documento. Revisar la configuración de campos dinámicos"
                        + " en el tipo de documento para contadorHojaRuta {}, equipo {}, operacion {}, ubicacionTecnica {}.",
                    new Object[]{
                        contadorHojaRuta, equipo, operacion, ubicacionTecnica
                    });
            }
        } else {
            getLogger().error(
                    "No se pudo ejecutar la búsqueda del documento. Revisar la configuración de campos dinámicos "
                    + "para contadorHojaRuta {}, equipo {}, operacion {}, ubicacionTecnica {}.",
                    new Object[]{
                        contadorHojaRuta, equipo, operacion, ubicacionTecnica
                    });
        }
        response.setContentType("text/json");
        try (final PrintWriter outWrit = response.getWriter()) {
            outWrit.println(id);
        }

        return super.execute();
    }

    private boolean dynamicFieldValidate(final IDocumentDAO dao, final String code) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("code", code);
        final String val = dao.SQL_findSingleString(""
                + " SELECT"
                + " dyn.code"
                + " FROM dynamic_field dyn"
                + " WHERE dyn.code = :code", params);
        if (val == null || val.isEmpty()) {
            getLogger().error("No existe el campo dinámico {} ", code);
            return false;
        }
        return true;
    }

    public String getUbicacionTecnica() {
        return ubicacionTecnica;
    }

    @StrutsParameter
    public void setUbicacionTecnica(String ubicacionTecnica) {
        this.ubicacionTecnica = ubicacionTecnica;
    }

    public String getEquipo() {
        return equipo;
    }

    @StrutsParameter
    public void setEquipo(String equipo) {
        this.equipo = equipo;
    }

    public String getOperacion() {
        return operacion;
    }

    @StrutsParameter
    public void setOperacion(String operacion) {
        this.operacion = operacion;
    }

    public String getContadorHojaRuta() {
        return contadorHojaRuta;
    }

    @StrutsParameter
    public void setContadorHojaRuta(String contadorHojaRuta) {
        this.contadorHojaRuta = contadorHojaRuta;
    }

}
