package qms.document.core;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.Gallery; 
import Framework.Action.SessionViewer;
import bnext.dto.DownloadDto;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import java.util.ArrayList;
import java.util.Map;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.file.FileUtils;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import qms.framework.file.FileManager;
import qms.framework.player.VideoCompatibilityHTML5;

public class DownloadMediaAction extends SessionViewer {

    private static final long serialVersionUID = 1L;
    
    private Long galleryId;
    private Long fileId;
    private String contentType;
    private String downloadResult;
    
    @Override
    @SuppressWarnings("empty-statement")
    public String execute() throws Exception { 
        
        String column = FileUtils.COLUMN_FILES_CONTENT;
        IFilesDAO dao = getBean(IFilesDAO.class);
        Map file = dao.HQL_findSimpleMap(""
            + " SELECT"
                + " new map("
                    + " f.id as fileId,"
                    + " f.contentType as contentType"
                + " )"
            + " FROM " + Gallery.class.getCanonicalName() + " c "
            + " JOIN " + FileRef.class.getCanonicalName() + " f "
            + " ON c.fileId = f.id "
            + " WHERE c.id = :galleryId",
            ImmutableMap.of("galleryId", galleryId)
        );
        this.fileId = (Long) file.get("fileId");
        this.contentType = (String) file.get("contentType");
        if (VideoCompatibilityHTML5.isVideoCompatibility(this.contentType)) {
            return VideoCompatibilityHTML5.IS_HTML5_VIDEO;
        }
        DownloadDto dto = new DownloadDto(this.fileId);
        dto.setOriginalForce(1);
        final FileManager fileManager = new FileManager();
        try {
        return fileManager.generateFilePdfAndDownload(
                dto,
                false,
                new ArrayList<>(),
                response, 
                column, 
                getLoggedUserDto()
        ).getMessage();
        } catch (final ClientAbortException ex) {
            getLogger().debug("Client cancelled request for download media of file with id {}. Details: {}",
                    new Object[]{fileId, ex.getMessage()});
            return null;
        }
    }
    
    public String getDownloadResult() {
        return downloadResult;
    }

    public Long getGalleryId() {
        return galleryId;
    }

    @StrutsParameter
    public void setGalleryId(Long galleryId) {
        this.galleryId = galleryId;
    }

    public Long getFileId() {
        return fileId;
    }

    public String getContentType() {
        return contentType;
    }

}
