package qms.document.logic;

import Framework.Action.SessionViewer;
import java.io.IOException;
import java.sql.SQLException;
import mx.bnext.core.errors.ClientAbortException;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import qms.framework.file.FileManager;
/**
 *
 * <AUTHOR>
 */
public class FilePdfPageThumbnailAction  extends SessionViewer {

    private static final long serialVersionUID = 1L;

    private Long id = null;
    private Long fileId = null;
    private Integer pageNum;
    private Integer rotationDegrees = 0;

    @Override
    public String execute() throws IOException, InterruptedException, SQLException {
        final FileManager fileManager = new FileManager();
        try {
            if (id != null && id > 0) {
                fileManager.writePageThumbnailToResponse(
                        id,
                        fileId,
                        rotationDegrees,
                        response,
                        getLoggedUserId()
                );
            } else {
                fileManager.writePageThumbnailToResponse(
                        fileId,
                        pageNum,
                        rotationDegrees,
                        response,
                        getLoggedUserId()
                );
            }
        } catch (final ClientAbortException ex) {
            getLogger().debug("Client cancelled request for pdf page tumbnail of id {} and file id {}. Details: {}",
                    new Object[]{id, fileId, ex.getMessage()});
            return null;
        }
        return null;
    }

    @StrutsParameter
    public void setId(Long id) {
        this.id = id;
    }

    @StrutsParameter
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @StrutsParameter
    public void setRotationDegrees(Integer rotationDegrees) {
        this.rotationDegrees = rotationDegrees;
    }

    @StrutsParameter
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
    
}
