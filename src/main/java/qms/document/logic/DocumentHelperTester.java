package qms.document.logic;

import DPMS.DAOInterface.IDocumentDAO;
import Framework.Config.Utilities;
import java.util.ArrayList;
import mx.bnext.access.ProfileServices;
import org.springframework.util.Assert;

/**
 *
 * <AUTHOR>
 */
public class DocumentHelperTester {
    
    private final DocumentHelper documentHelper;
    
    public DocumentHelperTester() {
        documentHelper = new DocumentHelper();
    }

    public void testGetAccessPermissionReason() {
        Assert.isTrue(
                documentHelper.getAccessPermissionReason(1L, 1L, true, new ArrayList<ProfileServices>(0)) != null,
                "Must have access permission reason"
        );
        testAccessPermissions(1L, 1L);
    }

    private void testAccessPermissions(Long documentId, Long loggedUserId) { 
        IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        dao.hasAccessByFolderAccess(documentId, loggedUserId);
        dao.hasAccessByBusinessUnit(documentId, loggedUserId);
        dao.hasAccessByBusinessUnitDepartment(documentId, loggedUserId);
        dao.hasAccessByLastAuthor(documentId, loggedUserId);
        dao.hasAccessByOriginator(documentId, loggedUserId);
        dao.hasAccessByBusinessUnitDocumentManager(documentId, loggedUserId);
        dao.hasAccessByBusinessUnitDepartmentDocumentManager(documentId, loggedUserId);
    }
}
