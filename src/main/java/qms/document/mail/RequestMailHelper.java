package qms.document.mail;

import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.OrganizationalUnit;
import DPMS.Mapping.Position;
import DPMS.Mapping.ReceiptAcknowledgment;
import DPMS.Mapping.Request;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import DPMS.Mapping.WorkflowPool;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import ape.pending.core.APE;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Set;
import qms.document.entity.RequestAuthorization;
import qms.document.entity.RequestLocalized;
import qms.document.logic.RequestHelper;
import qms.form.entity.FormRequest;
import qms.framework.util.LocaleUtil;
import qms.util.HTMLEncoding;
import qms.util.OwnerUtil;
import qms.workflow.util.WorkflowRequestStatus;
import qms.workflow.util.WorkflowSupported;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class RequestMailHelper extends MailHelper {

    private static final String  IN_TIME_AUTHORIZER = ""
            + " SELECT DISTINCT new " + RequestAuthorizationDTO.class.getCanonicalName() + "("
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description, user.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request "
                + " CROSS JOIN " + User.class.getCanonicalName() + " user "
                + " CROSS JOIN " + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND dateadd(day, pool.delay * -1, CURRENT_DATE) <= detail.modificationDate";
    private static final String DELAYED_AUTHORIZER = ""
            + " SELECT DISTINCT new " + RequestAuthorizationDTO.class.getCanonicalName() + "("
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description, user.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request "
                + " CROSS JOIN " + User.class.getCanonicalName() + " user "
                + " CROSS JOIN " + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.deleted = 0"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND dateadd(day, pool.delay * -1, CURRENT_DATE) > detail.modificationDate";
    private static final String DELATED_MODULE_MANAGER = ""
            + " SELECT DISTINCT new " + RequestAuthorizationDTO.class.getCanonicalName() + "("
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description, manager.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request "
                + " CROSS JOIN " + User.class.getCanonicalName() + " user "
                + " CROSS JOIN " + User.class.getCanonicalName() + " manager "
                + " CROSS JOIN " + Settings.class.getCanonicalName() + " s "
                + " CROSS JOIN " + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " LEFT JOIN manager.puestos managerPosition "
            + " LEFT JOIN managerPosition.perfil managerProfile "
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND s.id = 1"
                + " AND request.deleted = 0 "
                + " AND s.expiredRequestMailDocumentManager = 1"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND dateadd(day, pool.delay * -1, CURRENT_DATE) > detail.modificationDate"
                + " AND managerProfile.intBEncargadoDocumento = 1"
                + " AND managerPosition.status = " + Position.STATUS.ACTIVE.getValue()
                + " AND managerPosition.deleted = 0 "
                + " AND manager.status = " + User.STATUS.ACTIVE.getValue()
                + " AND manager.deleted = 0 "
                + " AND user.status = " + User.STATUS.ACTIVE.getValue()
                + " AND user.deleted = 0 "
                + " AND ("
                    + " request.businessUnitId = manager.businessUnitId"
                    + " OR request.businessUnitId = managerPosition.businessUnitId"
                + " )"
                + " AND pnd.recordId = request.id"
                + " AND pnd.deleted = 0"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) ";
    private static final String ESCALATE = ""
            + " SELECT DISTINCT new " + RequestAuthorizationDTO.class.getCanonicalName() + "("
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description, boss.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + " CROSS JOIN " +  RequestAuthorization.class.getCanonicalName() + " request "
                + " CROSS JOIN " + User.class.getCanonicalName() + " user "
                + " CROSS JOIN " + User.class.getCanonicalName() + " boss "
                + " CROSS JOIN " + PendingRecord.class.getCanonicalName() + " pnd "
                + " CROSS JOIN " + BusinessUnit.class.getCanonicalName() + " planta"
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND ("
                    + " planta.id = request.businessUnit.id"
                    + " OR planta.organizationalUnit.id = request.organizationalUnit.id"
                + " )"
                + " AND dateadd(day, pool.delay * -1, CURRENT_DATE) > detail.modificationDate"
                + " AND dateadd(day, planta.businessUnitSettings.daysToEscalate * -1, CURRENT_DATE) > pnd.commitmentDate"
                + " AND boss.status = " + User.STATUS.ACTIVE.getValue()
                + " AND boss.id = user.bossId";
    
    private final RequestHelper _helper;
    
    public RequestMailHelper(IUntypedDAO dao) {
        super(dao);
        _helper = new RequestHelper(dao);
    }

    public String getStatusString(String status, ResourceBundle tags) {
        if (status == null || status.trim().isEmpty() || !Utilities.isInteger(status)) {
            return LocaleUtil.getTag("status.active", tags);
        }
        return getStatusString(Integer.valueOf(status), tags);
    }

    public String getStatusString(Integer status, ResourceBundle tags) {
        String tag;
        Request.STATUS statusEnum = Request.STATUS.fromValue(status);
        switch (statusEnum) {
            case VERIFING:
                tag = LocaleUtil.getTag("status.requested", tags);
                break;
            case RETURNED:
                tag = LocaleUtil.getTag("status.return", tags);
                break;
            case CANCELED:
                tag = LocaleUtil.getTag("status.declined", tags);
                break;
            case APROVING:
                tag = LocaleUtil.getTag("status.inProcess", tags);
                break;
            case CLOSED:
                tag = LocaleUtil.getTag("status.completed", tags);
                break;
            case EXPIRED:
                tag = LocaleUtil.getTag("status.expired", tags);
                break;
            default:
                tag = LocaleUtil.getTag("status.active", tags);
                break;
        }
        return tag;
    }

    public boolean hasAssignReadersByType(final Long documentTypeId) {
        return _helper.hasAssignReadersByType(documentTypeId);
    }
    
    private RequestDTO requestToDTO(RequestLocalized requestMail, ResourceBundle tags) {
        RequestDTO requestDTO = new RequestDTO();
        requestDTO.setId(requestMail.getId());
        requestDTO.setScope(requestMail.getScope());
        requestDTO.setStatus(getStatusString(requestMail.getStatus(), tags));
        requestDTO.setType(requestMail.getType());
        requestDTO.setCode(requestMail.getCode());
        requestDTO.setDocumentCode(requestMail.getDocumentCode());
        requestDTO.setDocumentTitle(requestMail.getDescription());
        requestDTO.setCreationDate(requestMail.getCreationDate());
        requestDTO.setAuthor(requestMail.getAuthor().getDescription());
        requestDTO.setReason(HTMLEncoding.stripBold(requestMail.getReazon()));
        requestDTO.setVersion(requestMail.getVersion());
        if (requestMail.getBusinessUnit() != null) {
        requestDTO.setBusinessUnit(requestMail.getBusinessUnit().getDescription());
        }
        requestDTO.setDepartment(requestMail.getDepartment() != null ? requestMail.getDepartment().getDescription() : LocaleUtil.getTag("unassigned", tags));
        return requestDTO;
    }
    
    private List<RequestDTO> requestsToDTO(List<RequestLocalized> requestMails, ResourceBundle tags) {
        List<RequestDTO> requests = new ArrayList<>(requestMails.size());
        for(RequestLocalized requestMap : requestMails) {
            requests.add(requestToDTO(requestMap, tags));
        }
        return requests;
    }

    public Set<Mail> getVerifier(Long requestId) {
        return toMail(_helper.getVerifier(requestId));
    }

    public Set<Mail> getAuthorizers(Long requestId, Boolean excludeAuthor, Long loggedUserId) {
        final Set<Mail> users = toMailSet(_helper.getAuthorizers(requestId, loggedUserId));
        if (excludeAuthor && users != null && !users.isEmpty()) {
            final Mail mailAuthor = toMail(_helper.getAuthor(requestId));
            except(mailAuthor, users);
        }
        return users;
    }

    public Set<Mail> getModuleManagers(Long requestId) {
        return toMailSet(_helper.getModuleManagers(requestId));
    }

    public Mail getDocumentManagerInBusinessUnit(Long requestId) {
        return toMail(_helper.getDocumentManagerInBusinessUnit(requestId));
    }
    
    public Mail getDocumentManagerInDepartment(Long requestId) {
        return toMail(_helper.getDocumentManagerInDepartment(requestId));
    }

    public Set<Mail> getReaders(Long requestId) {
        return toMailSet(_helper.getReaders(requestId));
    }

    public Set<Mail> getBusinessUnitPermissionUsers(Long requestId) {
        return toMailSet(_helper.getBusinessUnitPermissionUsers(requestId));
    }

    public Set<Mail> getProcessPermissionUsers(Long requestId) {
        return toMailSet(_helper.getProcessPermissionUsers(requestId));
    }

    public Set<Mail> getDepartmentPermissionUsers(Long requestId) {
        return toMailSet(_helper.getDepartmentPermissionUsers(requestId));
    }

    public Set<Mail> getUserPermissionUsers(Long requestId) {
        return toMailSet(_helper.getUserPermissionUsers(requestId));
    }
    
    public Set<Mail> getAuthorizationUsers(WorkflowSupported target,WorkflowPool poolDetails, Long requestId) {
        return toMailSet(_helper.getAuthorizationUsers(target, poolDetails, requestId));
    }

    public RequestDTO getRequestDTO(Request request, ResourceBundle tags) {
        RequestDTO requestDTO = new RequestDTO();
        requestDTO.setId(request.getId());
        requestDTO.setScope(request.getScope());
        requestDTO.setType(request.getType());
        requestDTO.setStatus(getStatusString(request.getStatus(), tags));
        requestDTO.setCode(request.getCode());
        if(request.getDocument() != null){
            requestDTO.setDocumentId(request.getDocument().getId());   
        }
        requestDTO.setDocumentCode(request.getDocumentCode());
        requestDTO.setDocumentTitle(request.getDescription());
        requestDTO.setCreationDate(request.getCreationDate());
        requestDTO.setAuthor(request.getAuthor().getDescription());
        requestDTO.setReason(HTMLEncoding.stripBold(request.getReazon()));
        requestDTO.setVersion(request.getVersion());
        if (request.getBusinessUnit() != null) {
            requestDTO.setBusinessUnit(_helper.getBusinessUnitDescription(request.getBusinessUnit().getId()));
        }
        requestDTO.setDepartment(request.getDepartment() != null ? _helper.getDepartmentDescription(request.getDepartment().getId()) : LocaleUtil.getTag("unassigned", tags));
        return requestDTO;
    }

    public RequestDTO getRequestDTO(Long requestId, ResourceBundle tags) {
        RequestLocalized request = dao.HQLT_findById(RequestLocalized.class, requestId);
        return requestToDTO(request, tags);
    }

    public Mail getAuthor(Long requestId) {
        return toMail(_helper.getAuthor(requestId));
    }
    
    public List<RequestDTO> toVerifyByBusinessDocumentManager(Long documentManagerId, ResourceBundle tags) {
        String query = ""
                + " SELECT c"
                + " FROM " + RequestLocalized.class.getCanonicalName() + " c"
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request"
                + " WHERE request.id = c.id"
                + " AND request.status = " + WorkflowRequestStatus.VERIFING.getValue()
                + " AND request.scope = " + Request.SCOPE.BUSINESS_UNIT.getValue() 
                + " AND request.deleted = 0 "
                + " AND exists ("
                    + " SELECT bu.id "
                    + " FROM " + BusinessUnit.class.getCanonicalName() + " bu "
                    + " WHERE bu.documentManagerId = :documentManagerId"
                    + " AND request.businessUnit.id = bu.id"
                + ")";          
        List<RequestLocalized> requests = dao.HQL_findByQuery(query, "documentManagerId", documentManagerId);
        return requestsToDTO(requests, tags);
    }

    public List<RequestDTO> toVerifyByOrganizationDocumentManager(Long documentManagerId, ResourceBundle tags) {
        String query = ""
                + " SELECT c"
                + " FROM " + RequestLocalized.class.getCanonicalName() + " c"
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request"
                + " WHERE request.id = c.id"
                + " AND request.status = " + WorkflowRequestStatus.VERIFING.getValue()
                + " AND request.scope = " + Request.SCOPE.ORGANIZATIONAL_UNIT.getValue() 
                + " AND request.deleted = 0 "
                + " AND exists ("
                    + " SELECT ou.id "
                    + " FROM " + OrganizationalUnit.class.getCanonicalName() + " ou "
                    + " WHERE ou.documentManagerId = :documentManagerId"
                    + " AND request.organizationalUnit.id = ou.id"
                + ")";     
        
        List<RequestLocalized> requests = dao.HQL_findByQuery(query, "documentManagerId", documentManagerId);
        return requestsToDTO(requests, tags);
    }

    public List<RequestDTO> toVerifyByDepartmentDocumentManager(Long documentManagerId, ResourceBundle tags) {
        String query = ""
                + " SELECT c"
                + " FROM " + RequestLocalized.class.getCanonicalName() + " c"
                + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request"
                + " WHERE request.id = c.id"
                + " AND request.status = " + WorkflowRequestStatus.VERIFING.getValue()
                + " AND request.scope = " + Request.SCOPE.DEPARTMENT.getValue() 
                + " AND request.deleted = 0 "
                + " AND exists ("
                    + " SELECT bud.id "
                    + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
                    + " WHERE bud.documentManagerId = :documentManagerId"
                    + " AND request.businessUnitDepartmentId = bud.id"
                + ")";
        List<RequestLocalized> requests = dao.HQL_findByQuery(query, "documentManagerId", documentManagerId);
        return requestsToDTO(requests, tags);
    }

    public List<RequestDTO> toAuthorizeByJobs(List<Long> jobs, ResourceBundle tags) {
        String query = ""
                + " SELECT c"
                + " FROM " 
                    + RequestLocalized.class.getCanonicalName() + " c "
                    + " CROSS JOIN " + RequestAuthorization.class.getCanonicalName() + " request"
                + " WHERE"
                    + " request.id = c.id"
                    + " AND request.status = " + WorkflowRequestStatus.APROVING.getValue()
                	+ " AND request.deleted = 0 "
                    + " AND request.type != " + Request.FILL
                    + " AND EXISTS ( "
                        + " SELECT 1"
                        + " FROM request.autorizationPool pool"
                        + " JOIN pool.autorizationPoolDetailsList detail"
                        + " JOIN detail.owner owner "
                        + " JOIN owner.positions position"
                        + " WHERE "
                            + " detail.accepted IS NULL "
                            + " AND position.positionId IN (:jobs)"
                    + " )";
        List requests = dao.HQL_findByQuery(query, "jobs", jobs);
        return requestsToDTO(requests, tags);
    }
    
    public Set<RequestAuthorizationDTO> getInTimeAuthorizer() {
        Set<RequestAuthorizationDTO> docs = new HashSet<>(
            dao.HQL_findByQuery(IN_TIME_AUTHORIZER, "apeCode", APE.DOCUMENT_TO_AUTHORIZE_REQUEST.getCode())
        );
        return docs;   
    }
    
    
    public Set<RequestAuthorizationDTO> getDelayedAuthorizer() {
        Set<RequestAuthorizationDTO> docs = new HashSet<>(
            dao.HQL_findByQuery(DELAYED_AUTHORIZER, "apeCode", APE.DOCUMENT_TO_AUTHORIZE_REQUEST.getCode())
        );
        return docs;   
    }

    public Set<RequestAuthorizationDTO> getDelayedModuleManager() {
        Set<RequestAuthorizationDTO> docs = new HashSet<>(
            dao.HQL_findByQuery(DELATED_MODULE_MANAGER, "apeCode", APE.DOCUMENT_TO_AUTHORIZE_REQUEST.getCode())
        );
        return docs;   
    }
    
    
    public Set<RequestAuthorizationDTO> getEscalateBoss() {
        Set<RequestAuthorizationDTO> docs = new HashSet<>(
            dao.HQL_findByQuery(ESCALATE, "apeCode", APE.DOCUMENT_TO_AUTHORIZE_REQUEST.getCode())
        );
        return docs;   
    }
    
    
    public Long getRequestId(Long formRequestId) {
        return dao.HQL_findLong(""
            + " SELECT o.requestId"
            + " FROM " + FormRequest.class.getCanonicalName() + " f "
            + " JOIN f.outstandingSurvey o "
            + " WHERE f.id = :formRequestId", "formRequestId", formRequestId
        );
    }
    
    
    public String getUserPositionDescription(
            final WorkflowSupported target,
            final List<AutorizationPoolDetails> details
    ) {
        final StringBuilder userNames = new StringBuilder(255);
        for (final AutorizationPoolDetails detail: details) {
            final List<UserRef> requestActiveUsers = OwnerUtil.getRequestActiveUsers(
                    target,
                    detail.getOwner(),
                    detail.getRequestId(),
                    dao
            );
            final Set<UserRef> users = new HashSet<>(
                    requestActiveUsers
            );
            for (final UserRef user : users) {
                userNames.append(", ").append(user.getDescription());
            }
        }
        if (userNames.length() > 2) {
            return userNames.substring(1).trim();
        }
        return Utilities.EMPTY_STRING;
    }
    
    /**
     * Obtiene la cantidad de copias controladas por entregar sobre la modificación o 
     * reaprobación de un documento.
     * @param documentId
     * @return
     */
    public Long countDeliverPhysicalCopy(Long documentId){
        return dao.HQL_findSimpleLong(""
                + " SELECT count(c.id) "
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
                + " WHERE c.documentId = :documentId"
                + " AND c.status = 0 ",
                ImmutableMap.of("documentId", documentId)
        );
    }
            
    /**
     * Se obtiene la cantidad de copias controladas por entregar de los diferentes 
     * encargados de documentos de departamento
     * @return 
     */
    public Set<PhysicalCopyDTO> getDeliverPhysicalCopies() {
	Set<PhysicalCopyDTO> docs = new HashSet<>(dao.HQL_findByQuery(""
                + " SELECT new " + PhysicalCopyDTO.class.getCanonicalName() + 
                    "("
                        + "c.code as code,"
                        + "doc.code as documentCode,"
                        + "doc.description as documentDescription,"
                        + "doc.version as documentVersion,"
                        + "doc.department.documentManagerId as documentManagerId"
                    + ") "
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
                + " LEFT JOIN c.document doc "
                + " WHERE c.status = 0"));
	return docs;   
}
    
}
