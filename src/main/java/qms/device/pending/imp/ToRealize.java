package qms.device.pending.imp;

import DPMS.Mapping.ScheduledServices;
import DPMS.Mapping.SchedulingApproval;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.mail.dto.MailColumn;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import ape.pending.util.ApeConstants;
import java.util.LinkedHashMap;
import qms.device.pending.DevicePendingOperations;
import qms.util.MailUtil;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class ToRealize extends DevicePendingOperations {

    private static final String TO_REALIZE = ""
            + " FROM " + ScheduledServices.class.getCanonicalName() + " ss "
            + " CROSS JOIN " + User.class.getCanonicalName() + " usr "
            + " CROSS JOIN " + Settings.class.getCanonicalName() + " se "
            + " WHERE ss.responsible_id = usr.id "
            + " AND ss.status = 1 "
            + " AND ss.next_service >=" + ApeConstants.CURRENT_DATE_NO_TIME
            + " AND ss.next_service <=  " + ApeConstants.CURRENT_DATE_NO_TIME + " + 3 day"
            + " AND ss.device_deleted = 0 "
            + " AND ("
                + " se.schedulingToApprove = 0 "
                + " OR ("
                    + " se.schedulingToApprove = 1 "
                    + " AND ss.id in ("
                        + " SELECT sa.scheduleId  FROM " + SchedulingApproval.class.getCanonicalName() + " sa "
                        + " WHERE sa.approvedBy IS NOT NULL"
                        + " AND sa.approvedOn IS NOT NULL"
                        + " AND sa.status =  " + SchedulingApproval.APPROVED + ""
                    + " )"
                + " ) "
            + " ) ";

    public ToRealize(final IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS_SCHEDULE_SERVICE);
        setQuery(TO_REALIZE);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("usr.id");
        setPendingType(getType(APE.DEVICE_TO_REALIZE));
        setModuleKey(MODULE);
        setBase(ScheduledServices.class);
        setNoticeDate(FIELD_NOTICE_DATE);
        setCommitment(FIELD_COMMITMENT);
        setReminder(FIELD_COMMITMENT);
        setDeadline(FIELD_DEAD_LINE);
        setExtraMails("owner_id");
        final LinkedHashMap<String, MailColumn> extraFields = new LinkedHashMap<>(4);
        extraFields.put("device_description", MailUtil.DEFAULT_MAIL_COLUMN);
        extraFields.put("tipoperiodicidad", MailUtil.getLabelMailColumn());
        extraFields.put("responsible", MailUtil.DEFAULT_MAIL_COLUMN);
        extraFields.put("next_service", MailUtil.getAllowedAnticipationMailColumn());
        setExtraFieldsMap(extraFields);
        setDependencies(ServiceScheduled.class, ToRealizePastService.class);
        setOperationType(ApeOperationType.STRONG);
    }

}
