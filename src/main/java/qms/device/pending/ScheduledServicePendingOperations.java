package qms.device.pending;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.dto.ColumnDTO;

/**
 *
 * <AUTHOR>
 */
public class ScheduledServicePendingOperations extends DevicePendingOperations {
    public static final ColumnDTO[] BASE_SUMMARY_FIELDS = BaseSummaryFieldBuilder.columns(
        "id = findingId",
        "code = findingCode",
        "status = findingStatus",
        "description = findingDescription",
        "code = pendingTypeCode@ptype"
    );


    public ScheduledServicePendingOperations(IUntypedDAO dao) {
        super(dao);
        setSummaryTemplate("${findingCode}, ${findingDescription}");
        setBaseSummaryFields(BASE_SUMMARY_FIELDS);
    }

}
