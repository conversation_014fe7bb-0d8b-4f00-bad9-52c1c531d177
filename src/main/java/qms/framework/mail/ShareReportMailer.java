package qms.framework.mail;

import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.exception.InvalidCipherDecryption;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.mail.Message.RecipientType;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import qms.access.dto.ILoggedUser;
import qms.framework.core.Mailer;
import qms.framework.dao.IDatabaseQueryDAO;
import qms.framework.dao.IShareReportDAO;
import qms.framework.dto.IReportColumnDTO;
import qms.framework.dto.ReportDTO;
import qms.framework.dto.ShareReportDTO;
import qms.framework.logic.ShareReportQueryException;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.ReportHandler;
import qms.framework.util.ShareReportMailBuilder;
import qms.framework.util.ShareReportMailHelper;
import qms.util.GridFilter;
import qms.util.GridOrderBy;
import qms.util.ModuleUtil;

public class ShareReportMailer extends Mailer {

    private final ShareReportMailBuilder _builder;
    private final ShareReportMailHelper _helper;
    private final String _template;

    public ShareReportMailer(IUntypedDAO dao) {
        super(dao);
        loadTags(ShareReportMailer.class.getCanonicalName());
        _builder = new ShareReportMailBuilder();
        _helper = new ShareReportMailHelper(dao);
        _template = getTemplate(ShareReportDTO.class.getCanonicalName());
    }

    public Boolean send(
            final ShareReportDTO report, final ILoggedUser loggedUser
    ) throws DataSourceCredentialError, SQLException, ShareReportQueryException, InvalidCipherDecryption {
        final MailDTO mail = new MailDTO();
        final ReportDTO config = getConfig(report, loggedUser);
        if (config == null) {
            getLogger().trace("Share Report {} not configured, missing report.", report.getId());
            return false;
        }
        final GridInfo<Map<String, Object>> rows = getRows(report, config, loggedUser);
        if (rows == null || rows.getData().size() <= 0) {
            if (getLogger().isTraceEnabled()) {
                getLogger().trace("Share Report {} without data.", report.getId());
            }
            return false;
        }
        final String plainRecipients = report.getPlainRecipients();
        if (plainRecipients != null && !plainRecipients.isEmpty()) {
            if (plainRecipients.contains(";")) {
                final Set<Mail> recipients = new HashSet<>(
                        Arrays.asList(plainRecipients.split(";")).stream()
                                .map(recipient -> new Mail(recipient, recipient, RecipientType.BCC))
                                .collect(Collectors.toList())
                );
                mail.setRecipients(recipients);
            } else {
                final Set<Mail> recipients = new HashSet<>(1);
                recipients.add(new Mail(plainRecipients, plainRecipients, RecipientType.BCC));
                mail.setRecipients(recipients);
            }
        }
        mail.getRecipients().addAll(_helper.getInternalRecipients(report.getPlanId(), report.getModule()));
        if (mail.getRecipients().isEmpty()) {
            if (getLogger().isTraceEnabled()) {
                getLogger().trace("Share Report {} without recipients.", report.getId());
            }
            return false;
        }
        final String message = writeReport(report, config, rows);
        if (message == null || message.isEmpty()) {
            return false;
        }
        mail.setMessageTitle(report.getDetails());
        mail.setMessageModuleTitle(config.getDescription());
        mail.setSubject(report.getShareDescription());
        mail.setMessage(message);
        final String footer = getTag("footer")
                .replace("{timestamp}", Utilities.formatDateWithTime(new Date()))
                .replace("{currentRecords}", rows.getData().size() + "")
                .replace("{totalRecords}", rows.getCount().toString());
        mail.setMessageFooter(footer);
        mail.setMessageLink("");
        send(mail, report.getModule(), TYPE.REPORT, getMailTags());
        return true;
    }

    private String writeReport(
            final ShareReportDTO report,
            final ReportDTO config,
            final GridInfo<Map<String, Object>> rows
    ) {
        try {
            final List<Long> configuredColumns = getConfiguredColumns(report);
            final String content = _builder.writeReport(config, configuredColumns, rows, _template);
            return content;
        } catch (Exception ex) {
            final String message = ExceptionUtils.getRootCauseMessage(ex);
            getLogger().error("Failed to write share report {}. Details: {}", new Object[]{report.getId(), message, ex});
            return null;
        }
    }

    private ReportDTO getConfig(
            final ShareReportDTO report, 
            final ILoggedUser loggedUser
    ) throws SQLException, InvalidCipherDecryption {
        final Module module = ModuleUtil.fromKey(report.getModule());
        final ReportHandler handler = new ReportHandler();
        return handler.reportColumn(report.getReportId(), module, loggedUser);
    }

    private List<Long> getConfiguredColumns(final ShareReportDTO report) {
        final IShareReportDAO reportDao = Utilities.getBean(IShareReportDAO.class);
        return reportDao.getConfiguredColumns(report.getPlanId());
    }

    private GridInfo<Map<String, Object>> getRows(
            final ShareReportDTO report,
            final ReportDTO config,
            final ILoggedUser loggedUser
    ) throws ShareReportQueryException {
        if (config == null) {
            return Utilities.EMPTY_GRID_INFO;
        }
        try {
            final IDatabaseQueryDAO queryDao = Utilities.getBean(IDatabaseQueryDAO.class);
            final GridFilter filter = new GridFilter();
            filter.setPage(0);
            filter.setPageSize(report.getMaxRecords().intValue());
            final List<IReportColumnDTO> sortColumns = config.getReportColumns().stream()
                    .filter(column -> column.getSortPriority() != null)
                    .sorted()
                    .collect(Collectors.toList());
            if (!sortColumns.isEmpty()) {
                filter.getField().setOrderBy(sortColumns.get(0).getQueryColumnCode());
                filter.setDirection(sortColumns.get(0).getSortDirection().byteValue());
                if (sortColumns.size() > 1) {
                    final List<GridOrderBy> orderByMany = sortColumns
                            .subList(1, sortColumns.size())
                            .stream()
                            .map((column) -> new GridOrderBy(column.getQueryColumnCode(), column.getSortDirection()))
                            .collect(Collectors.toList());
                    filter.getField().setOrderByMany(orderByMany);
                }
            }
            if (report.getQueryConditions() != null && !report.getQueryConditions().isEmpty()) {
                filter.getCriteria().put("<condition>", report.getQueryConditions());
            }
            final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
            return queryHandler.getRowsById(report.getDatabaseQueryId(), filter, null, null, loggedUser);
        } catch(Exception e) {
            throw new ShareReportQueryException("Invalid share report query", e);
        }
    }

}
