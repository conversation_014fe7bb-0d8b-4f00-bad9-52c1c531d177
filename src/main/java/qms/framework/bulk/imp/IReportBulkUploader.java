package qms.framework.bulk.imp;

import java.util.List;
import java.util.Map;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.dto.DynamicTableBuildDTO;
import qms.framework.bulk.dto.IBulkLocalizedRow;
import qms.framework.dto.IReportColumnDTO;

public interface IReportBulkUploader extends IReportTableParams, IBulkUploader<
        ReportBulkPlainRow, /* Renglon del EXCEL */
        IReportColumnDTO, /* Encabezados del EXCEL */
        ReportBulkSaveResultRow, /* Resultado de guardar */
        Map<String, Object> /* Input para guardar */
    > {

    public List<IReportColumnDTO> getHeaders();

    public List<IReportColumnDTO> getFields();

    public List<String> getExcelIdFieldList();

    public Long getReportId();

    public Long getSlimReportId();

    public String getExcelIdFields();

    public String getSchema();

    public String getEntityName();

    public void setCrossReference(Map<String, Object> crossReference, IBulkLocalizedRow<ReportBulkPlainRow> row);

    public DynamicTableBuildDTO getDynamicTable();

    public String getMasterId();

    public IDynamicTableDAO<Integer, IReportColumnDTO> getDynamicTableDAO();
}
