package qms.framework.bulk.imp;

import Framework.Config.Utilities;
import biweekly.util.StringUtils;
import bnext.exception.MakePersistentException;
import com.google.common.collect.ImmutableList;
import com.sun.star.auth.InvalidArgumentException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.core.file.IFileManager;
import qms.access.dto.ILoggedUser;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.DynamicTableHelper;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicTableBuildDTO;
import qms.form.bulk.logic.FormReportBulkWorkBook;
import qms.framework.bulk.dto.BulkSheetData;
import qms.framework.bulk.dto.BulkVariableColumn;
import qms.framework.bulk.dto.BulkWorkBook;
import qms.framework.bulk.dto.IBulkActivityIndex;
import qms.framework.bulk.dto.IBulkLocalizedRow;
import qms.framework.bulk.dto.IBulkWorkBook;
import qms.framework.bulk.logic.BulkUploader;
import qms.framework.bulk.logic.BulkWriter;
import qms.framework.bulk.util.BuilkUploadType;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.DataExchangeDTO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.dto.IReportColumnDTO;
import qms.framework.dto.ReportColumnDTO;
import qms.framework.dto.ReportDTO;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.MeasureTime;
import qms.framework.util.ReportColumnType;
import qms.framework.util.ReportHandler;
import qms.util.QMSException;

public abstract class ReportBulkUploader extends BulkUploader<
        ReportBulkPlainRow, /* Renglón del EXCEL */
        IReportColumnDTO, /* Encabezados del EXCEL */
        ReportBulkSaveResultRow, /* Resultado de guardar */
        Map<String, Object> /* Input para guardar en tabla dinamica  */
        > implements IReportBulkUploader {

    private final Long databaseQueryId;
    private final String masterId;
    private final IFileManager fileManager;
    private final ILoggedUser loggedUser;
    private final List<String> excelIdFieldList;
    private final TreeSet<IReportColumnDTO> headers = new TreeSet<>();
    private final TreeSet<IReportColumnDTO> resultHeaders = new TreeSet<>();
    private final Long reportId;
    private final Long slimReportId;
    private final String slimReportCode;
    private final Module module;
    private final ReportDTO reportQueryData;
    private final String appName;
    private final String excelIdFields;
    private final String url;
    private final String entityName;
    private final String schema;
    private final IDynamicTableDAO<Integer, IReportColumnDTO> dao;
    private final DynamicTableBuildDTO factoryBuilded;
    private final Long surveyId;

    public ReportBulkUploader(
            Module module,
            Locale locale,
            Long surveyId, // <-- TODO: Eliminar de aquí!! actualmente se usa para obtener DESDE `FormReportBulkUploader` el `defaultHeaders`. Corregir enviando directamente `defaultHeaders` dentro de `bulkConfig`
            String masterId,
            String entityName,
            String schema,
            String url,
            String appName,
            IReportBulkConfig bulkConfig,
            ILoggedUser loggedUser)
            throws QMSException {
        this(
                module,
                locale,
                surveyId,
                masterId,
                entityName,
                schema,
                url,
                appName,
                bulkConfig,
                null,
                loggedUser
        );
    }


    public ReportBulkUploader(
            Module module,
            Locale locale,
            Long surveyId, // <-- TODO: Eliminar de aquí!! actualmente se usa para obtener DESDE `FormReportBulkUploader` el `defaultHeaders`. Corregir enviando directamente `defaultHeaders` dentro de `bulkConfig`
            String masterId,
            String entityName,
            String schema,
            String url,
            String appName,
            IReportBulkConfig bulkConfig,
            ReportDTO reportQueryData,
            ILoggedUser loggedUser
    )
            throws QMSException {
        super(locale, null, loggedUser);
        this.surveyId = surveyId;
        this.schema = schema;
        this.fileManager = new FileManager();
        this.module = module;
        this.url = url;
        this.masterId = masterId;
        this.appName = appName;
        this.loggedUser = loggedUser;
        this.reportId = bulkConfig.getReportId();
        this.databaseQueryId = bulkConfig.getDatabaseQueryId();
        this.slimReportId = bulkConfig.getSlimReportId();
        this.slimReportCode = bulkConfig.getSlimReportCode();
        if (!entityName.contains("{slimReportCode}")) {
            throw new QMSException("Missing '{slimReportCode}' in entityName");
        }
        this.entityName = entityName.replace("{slimReportCode}", this.slimReportCode
                .replaceAll("-", "")
                .replaceAll(" ", ""));
        this.excelIdFields = bulkConfig.getExcelIdFields();
        this.excelIdFieldList = getExcelIdFieldList(this.excelIdFields);
        Set<String> seen = ConcurrentHashMap.newKeySet();
        ReportHandler reportHandler = new ReportHandler();
        if (reportQueryData == null) {
            this.reportQueryData = reportHandler.reportColumn(this.reportId, false, true, getModule(), SecurityUtils.getLoggedUser(), (c) -> {
                if (c.getDescription() == null || !seen.add(c.getDescription())) {
                    return false; // <-- Se remueven etiquetas duplicadas y nulos
                }
                return ReportColumnType.GHOST_FIELD.equals(c.getType()) || this.excelIdFieldList.contains(c.getQueryColumnCode()); // <-- Se mantienen solo columnas de tipo GHOST_FIELD o ID
            });
        } else {
            this.reportQueryData = reportQueryData;
            seen.addAll(this.reportQueryData.getReportColumns().stream().map(IReportColumnDTO::getQueryColumnCode).collect(Collectors.toList()));
        }
        assert this.reportQueryData != null;
        this.resultHeaders.addAll(this.reportQueryData.getUnfilteredReportColumns().stream().filter(c ->
                !ReportColumnType.SKIP.equals(c.getType()) && !Boolean.TRUE.equals(c.getOnlyFilter())
        ).collect(Collectors.toList()));
        this.headers.addAll(this.reportQueryData.getReportColumns());
        // Columnas por defecto de ID
        this.reportQueryData.getReportColumns().addAll(ImmutableList.of(
            new ReportColumnDTO(reportId, slimReportId, "id", "ID", false, ReportColumnType.BIGINT,0),
            new ReportColumnDTO(reportId, slimReportId, "status", "Status", false, ReportColumnType.INTEGER,0),
            new ReportColumnDTO(reportId, slimReportId, "deleted", "Is Deleted", false, ReportColumnType.INTEGER,0),
            new ReportColumnDTO(reportId, slimReportId, "created_date", "Creation Date", false, ReportColumnType.DATE_TIME,0),
            new ReportColumnDTO(reportId, slimReportId, "last_modified_date", "Last Modfied", false, ReportColumnType.DATE_TIME,0)
        ));
        this.reportQueryData.getReportColumns().addAll(
            getDefaultFields(reportId, slimReportId)
        );
        // Helpers
        this.dao = IReportBulkBaseHandler.getDynamicTableDAO();
        this.factoryBuilded = IReportBulkBaseHandler.getFactoryBuilded(this, loggedUser.getId());
    }

    protected void addHeaders(List<ReportColumnDTO> headers) {
        this.getHeaders().addAll(headers.stream()
                .filter(dh -> this.getHeaders().stream()
                        .map(IReportColumnDTO::getQueryColumnCode)
                        .noneMatch(qcc -> qcc.equals(dh.getQueryColumnCode())))
                .collect(Collectors.toList()));
    }

    public abstract List<ReportColumnDTO> getDefaultFields(Long reportId, Long slimReportId);

    @Override
    @Nonnull
    public List<ReportBulkSaveResultRow> save(
            ILoggedUser loggedUser,
            List<Map<String, Object>> entities
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        final List<ReportBulkSaveResultRow> result = new ArrayList<>(entities.size());
        if (factoryBuilded == null) {
            throw new QMSException("No se pudo crear la tabla de carga de excel para reportId '" + this.reportId + "'");
        }
        if (factoryBuilded.isNewTable()) {
            getLogger().error("La tabla se creó en la misma transacción, no se puede guardar");
            return result;
        }
        final DynamicTableHelper tableHelper = new DynamicTableHelper(dao);
        entities.forEach((e) -> {
            final Long answersTableId = e.get("id") != null ? Long.parseLong(e.get("id").toString()) : -1L;
            try {
                // Se guarda la información del Excel en la tabla dinamica
                DynamicFieldInsertDTO r = tableHelper.persist(
                        answersTableId,
                        e,
                        factoryBuilded.getSchemaName(),
                        factoryBuilded.getTableName()
                );
                // Se traspasan los datos guardados al DTO que se escribirá en la pestaña "Resultado"
                result.add(
                        new ReportBulkSaveResultRow(r, e, getRowsId(e))
                );
            } catch (QMSException ex) {
                result.add(new ReportBulkSaveResultRow(
                        null,
                        e,
                        getRowsId(e)
                ));
            }
        });
        return result;
    }

    private List<String> getRowsId(Map<String, Object> e) {
        final List<String> rowsId = new ArrayList<>();
        for (String excelIdField : this.excelIdFieldList) {
            if (e.get(excelIdField) != null) {
                rowsId.add(e.get(excelIdField).toString());
            }
        }
        return rowsId;
    }

    public IBulkActivityIndex<ReportBulkPlainRow, IReportColumnDTO, ReportBulkSaveResultRow, Map<String, Object>> getNewEmptyBulkIndex() {
        return new ReportBulkIndex();
    }

    @Override
    public ReportBulkPlainRow mapToRow(
            IBulkWorkBook<ReportBulkPlainRow, IReportColumnDTO> wb,
            List<String> value,
            Map<IReportColumnDTO, Integer> headers,
            Map<String, String> variables,
            Integer rowNum
    ) {
        ReportBulkPlainRow row = new ReportBulkPlainRow();
        row.setValid(true);
        row.setRowNum(rowNum);
        row.setValues(value);
        row.setIds(new ArrayList<>(excelIdFieldList.size()));
        headers.forEach((header, index) -> {
            if (excelIdFieldList.contains(header.getQueryColumnCode())) {
                row.getIds().add(value.get(index));
            }
        });
        return row;
    }

    @Override
    public IReportColumnDTO getRowHeaderByTitle(final String title) {
        return this.headers.stream().filter(t -> t.getDescription().equals(title)).findFirst().orElse(null);
    }

    @Override
    public List<IReportColumnDTO> getRowHeaderIds() {
        return this.headers.stream().filter(t -> excelIdFieldList.contains(t.getQueryColumnCode())).collect(Collectors.toList());
    }

    @Override
    public abstract void setCrossReference(Map<String, Object> crossReference, IBulkLocalizedRow<ReportBulkPlainRow> row);

    @Override
    public String getRowHeaderLabel(IReportColumnDTO column) {
        return column.getDescription();
    }

    @Override
    public String getRowVariableHeaderLabel(BulkVariableColumn column) {
        return getTag("ROW_" + column.toString().toUpperCase());
    }

    @Override
    public DataExchangeDTO getHelpData() {
        final DataExchangeDTO data = new DataExchangeDTO();
        final List<List<String>> values = new ArrayList<>();
        values.add(getSaveResultsHeadersLabels());
        data.setNumberLines(values.size());
        data.setValues(values);
        return data;
    }

    @Override
    public List<String> getRowHeadersLabels() {
        return this.headers.stream().map(IReportColumnDTO::getDescription).collect(Collectors.toList());
    }


    @Override
    public BulkVariableColumn getVariableHeaderByTitle(String plainHeader) {
        // variables no soportadas
        return null;
    }

    @Override
    public List<String> getVariableHeadersLabels() {
        // variables no soportadas
        return Utilities.EMPTY_LIST;
    }

    @Override
    public List<String> getSaveResultsHeadersLabels() {
        // TO-DO: Agregar el ID en la primer columna (?)
        return this.resultHeaders.stream().map(IReportColumnDTO::getDescription).collect(Collectors.toList());
    }

    public List<IReportColumnDTO> getSaveResultsHeaders() {
        return new ArrayList<>(this.resultHeaders);
    }

    @Override
    public BulkWorkBook<ReportBulkPlainRow, IReportColumnDTO> defaultWorkbook(ILoggedUser loggedUser) throws QMSException {
        final FormReportBulkWorkBook<IReportColumnDTO> wb = new FormReportBulkWorkBook<>();
        wb.setRowsSheet(new BulkSheetData<>());
        wb.setVariablesSheet(new BulkSheetData<>());
        return wb;
    }

    @Override
    public boolean isWorkbookValid(IBulkActivityIndex<ReportBulkPlainRow, IReportColumnDTO, ReportBulkSaveResultRow, Map<String, Object>> index, ILoggedUser loggedUser) throws BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final IBulkWorkBook<ReportBulkPlainRow, IReportColumnDTO> wb = index.getWorkbook();
            boolean isValid = true;
            if (wb == null || wb.getRowsSheet() == null || wb.getRowsSheet().getValues() == null) {
                logEmptyContentError(wb);
                isValid = false;
            }
            assert wb != null;
            if (!isValidForRepeatedIds(wb, wb.getRowsSheet().getValues())) {
                isValid = false;
            }
            return isValid;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in activity bulk schema validator.");
        }
    }

    private boolean isValidForRepeatedIds(final IBulkWorkBook<ReportBulkPlainRow, IReportColumnDTO> wb, final List<ReportBulkPlainRow> rows) {
        final Set<List<String>> ids = new HashSet<>(rows.size());
        final AtomicBoolean valid = new AtomicBoolean(true);
        // Create a method to log as an error when the ID is repeated
        rows.stream()
                // trae solo los que tienen ID
                .filter(row -> row.getIds().stream().noneMatch(r -> !org.apache.commons.lang3.StringUtils.isAllBlank(r)))
                .forEach((row) -> {
                    if (ids.contains(row.getIds())) {
                        logRepeatedIdError(wb, row);
                        valid.set(false);
                    } else {
                        ids.add(row.getIds());
                    }
                });
        return valid.get();
    }

    @Override
    public IFileManager getFileManager() {
        return this.fileManager;
    }

    @Override
    public abstract BulkWriter<ReportBulkPlainRow, IReportColumnDTO, ReportBulkSaveResultRow, Map<String, Object>> getWritter();

    public String getRowId(ReportBulkSaveResultRow row) {
        return StringUtils.join(row.getRowIds(), "-");
    }

    private Module getModule() {
        return module;
    }

    public ILoggedUser getLoggedUser() {
        return this.loggedUser;
    }

    public String getUrl() {
        return url;
    }

    public String getAppName() {
        return appName;
    }

    @Nonnull
    public static List<String> getExcelIdFieldList(String excelIdFields) {
        return excelIdFields != null ? Arrays.asList(excelIdFields.split(",")) : Collections.emptyList();
    }

    @Override
    public List<String> getExcelIdFieldList() {
        return this.excelIdFieldList;
    }

    @Override
    public IBulkWorkBook<ReportBulkPlainRow, IReportColumnDTO> getNewBulkWorkBook() {
        return new FormReportBulkWorkBook<>();
    }

    @Override
    public List<IReportColumnDTO> getHeaders() {
        return new ArrayList<>(headers);
    }

    @Override
    public List<IReportColumnDTO> getFields() {
        return reportQueryData.getReportColumns();
    }

    @Override
    public Long getReportId() {
        return reportId;
    }

    @Override
    public Long getSlimReportId() {
        return slimReportId;
    }

    @Override
    public String getExcelIdFields() {
        return excelIdFields;
    }

    @Override
    public String getSchema() {
        return schema;
    }

    @Override
    public String getEntityName() {
        return entityName;
    }

    @Override
    public BuilkUploadType getBuilkUploadType() {
        return BuilkUploadType.REPORT;
    }

    @Override
    public String getMasterId() {
        return masterId;
    }

    public IDynamicTableDAO<Integer, IReportColumnDTO> getDynamicTableDAO() {
        return dao;
    }

    public DynamicTableBuildDTO getDynamicTable() {
        return factoryBuilded;
    }

    public Long getDatabaseQueryId() {
        return databaseQueryId;
    }

    public Long getSurveyId() {
        return surveyId;
    }
}
