/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package qms.framework.dao.bean;

import Framework.DAO.IGenericDAO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Component
public abstract class daoFactory {

    public abstract IGenericDAO getSpringBeanDao();

}
