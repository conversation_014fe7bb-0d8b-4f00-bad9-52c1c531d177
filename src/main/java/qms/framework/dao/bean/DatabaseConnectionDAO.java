package qms.framework.dao.bean;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.CodeSequence;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.InvalidCipherDecryption;
import com.google.common.collect.ImmutableMap;
import com.zaxxer.hikari.HikariDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.hibernate.internal.SessionImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.framework.dao.IDatabaseConnectionDAO;
import qms.framework.dto.QueryTestDto;
import qms.framework.entity.DatabaseConnection;
import qms.framework.entity.QueryColumn;
import qms.framework.util.CacheRegion;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.DatabaseDriverClass;
import qms.framework.util.DatabaseQueryCacheHandler;
import qms.framework.util.DatabaseQueryUtil;
import qms.framework.util.DatabaseSourcePool;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.PreferencesUtil;
import qms.util.ModuleUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@Repository(value = "DatabaseConnectionDAO")
@Scope(value = "singleton")
public class DatabaseConnectionDAO extends GenericDAOImpl<DatabaseConnection, Long> implements IDatabaseConnectionDAO {
           
    private String getEncryptedPassword(final String password) throws InvalidCipherDecryption {
        final String cryptedPassword = PreferencesUtil.getEncryptedValue("password", password);
        return cryptedPassword;
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public QueryTestDto testQueryAtQmsConnection(final String sqlSelect) {
        final SessionImpl sessionImpl = (SessionImpl) getSession();
        final Connection connection = sessionImpl.connection();
        return testQuery(connection, sqlSelect, null);
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public QueryTestDto testQuery(final Connection conn, final String sqlSelect, final String sessionStatement) {
        final QueryTestDto test = new QueryTestDto();
        try {
            conn.setReadOnly(true);
            conn.setAutoCommit(false);
            final SingleConnectionDataSource dataSource = new SingleConnectionDataSource(conn, false);
            dataSource.setAutoCommit(false);
            final JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            jdbcTemplate.setFetchSize(5);
            jdbcTemplate.setMaxRows(5);
            if (sessionStatement != null && !sessionStatement.trim().isEmpty()) {
                jdbcTemplate.execute(sessionStatement);
            }
            final List<Map<String, Object>> samples = jdbcTemplate.queryForList(sqlSelect);
            test.setSamples(samples);
            final SqlRowSet rowSet = jdbcTemplate.queryForRowSet(sqlSelect);
            final SqlRowSetMetaData metadata = rowSet.getMetaData();
            final Set<QueryColumn> validColumns = new LinkedHashSet<>(metadata.getColumnCount());
            final Set<String> invalidColumns = new LinkedHashSet<>(metadata.getColumnCount());
            for (int i = 1, l = metadata.getColumnCount(); i <= l; i++) {
                final String columnName = metadata.getColumnName(i);
                final QueryColumn column = new QueryColumn();
                if (DatabaseQueryUtil.validSqlObjectName(columnName)) {
                    column.setCode(columnName);
                    column.setDataType(metadata.getColumnTypeName(i));
                    column.setDescription(columnName);
                    validColumns.add(column);
                } else if (!columnName.isEmpty()) {
                    invalidColumns.add(columnName);
                } else {
                    invalidColumns.add("Missing alias for column at index " + i);
                }
            }
            final DatabaseQueryCacheHandler cacheHandler = new DatabaseQueryCacheHandler();
            final String columnMapJson = cacheHandler.getColumnsAliasesJson(sqlSelect, -1L, validColumns);
            getLogger().debug("Column map json: {}", columnMapJson);
            test.setColumns(validColumns);
            test.setErrorColumns(invalidColumns);
            final boolean isValid = invalidColumns.isEmpty();
            if (!isValid) {
                test.setErrorMessage(DatabaseQueryUtil.INVALID_COLUMN_NAME_ERROR);
                test.setSamples(null);
            }
            test.setOperationEstatus(isValid ? 1 : 0);
        } catch (Exception ex) {
            getLogger().trace("There was an error ", ex);
            final QueryTestDto result = new QueryTestDto();
            result.setSamples(null);
            result.setOperationEstatus(0);
            final Throwable rootCause = ExceptionUtils.getRootCause(ex);
            if (rootCause == null) {
                result.setErrorMessage(ex.getMessage());
                if (ex instanceof SQLException) {
                    result.setErrorCode(((SQLException)ex).getErrorCode());
                }
            } else {
                result.setErrorMessage(rootCause.getMessage());
                if (rootCause instanceof SQLException) {
                    result.setErrorCode(((SQLException)rootCause).getErrorCode());
                }
            }
            return result;
        }
        return test;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle newConnection(
            final DatabaseConnection connection, final ILoggedUser loggedUser
    ) throws QMSException, InvalidCipherDecryption, InvalidCipherDecryption {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (connection.getId() > 0) {
            gsh.setErrorMessage("invalid_edition");
            return gsh;
        }
        final DatabaseDriverClass driver = DatabaseDriverClass.getDriver(
                connection.getDriverClassName()
        );
        if (driver == null) {
            gsh.setErrorMessage("invalid_class_name");
            return gsh;
        }
        connection.setId(-1L);
        connection.setModule(ModuleUtil.fromKey(connection.getModule()).getKey());
        final ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
        connection.setCode("DB-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE));
        final String password = connection.getDbPassword();
        if (password == null || password.isEmpty()) {
            gsh.setErrorMessage("empty_password");
            return gsh;
        }
        final String cryptedPassword = getEncryptedPassword(password);
        connection.setDbPassword(cryptedPassword);
        final DatabaseConnection saved = makePersistent(connection, loggedUser.getId());
        if (saved == null) {
            gsh.setOperationEstatus(0);
        } else {
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DatabaseConnection getDatabaseConnectionReplica() {
        final Long databaseConnectionId = Utilities.getSettings().getDbReplicationDatabaseConnectionId();
        // Get the database connection
        return HQLT_findById(DatabaseConnection.class, databaseConnectionId, true, CacheRegion.REPORT, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle editConnection(final DatabaseConnection connection, final ILoggedUser loggedUser) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (connection.getId() <= 0) {
            gsh.setErrorMessage("invalid_edition");
            return gsh;
        }
        final DatabaseDriverClass driver = DatabaseDriverClass.getDriver(
                connection.getDriverClassName()
        );
        if (driver == null) {
            gsh.setErrorMessage("invalid_class_name");
            return gsh;
        }
        connection.setModule(ModuleUtil.fromKey(connection.getModule()).getKey());
        connection.setDbPassword(HQL_findSimpleString(""
                + " SELECT c.dbPassword"
                + " FROM " + DatabaseConnection.class.getCanonicalName() + " c"
                + " WHERE c.id = :connectionId", 
                ImmutableMap.of("connectionId", connection.getId()),
                true,
                CacheRegion.REPORT,
                0
        ));
        final DatabaseConnection saved = makePersistent(connection, loggedUser.getId());
        if (saved == null) {
            gsh.setOperationEstatus(0);
        } else {
            DatabaseSourcePool.shutdown(saved.getId());
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle changePassword(
            final DatabaseConnection connection, final ILoggedUser loggedUser
    ) throws InvalidCipherDecryption {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final String password = connection.getDbPassword();
        if (password == null || password.isEmpty()) {
            gsh.setErrorMessage("empty_password");
            return gsh;
        }
        final DatabaseDriverClass driver = DatabaseDriverClass.getDriver(
                connection.getDriverClassName()
        );
        if (driver == null) {
            gsh.setErrorMessage("invalid_class_name");
            return gsh;
        }
        final String cryptedPassword = getEncryptedPassword(password);
        connection.setDbPassword(cryptedPassword);
        final DatabaseConnection saved = makePersistent(connection, loggedUser.getId());
        if (saved == null) {
            gsh.setOperationEstatus(0);
        } else {
            DatabaseSourcePool.shutdown(saved.getId());
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle delete(final Long connectionId, final ILoggedUser loggedUser) {
        final DatabaseConnection connection = HQLT_findById(DatabaseConnection.class, connectionId, true, CacheRegion.REPORT, 0);
        if (Boolean.TRUE.equals(connection.getInternal())) {
            return GenericSaveHandle.newFailure("internal_connection");
        }
        connection.setDeleted(1);
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final DatabaseConnection saved = makePersistent(connection, loggedUser.getId());
        if (saved != null) {
            DatabaseSourcePool.shutdown(connectionId);
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle testConnection(final Long connectionId, final ILoggedUser loggedUser)
        throws DataSourceCredentialError {
        final DatabaseConnection connection = HQLT_findById(connectionId);
        try {
            final String password = DatabaseSourcePool.getDencryptedPassword(connection.getDbPassword());
            if (password == null) {
                throw new DataSourceCredentialError(connectionId);
            } else {
                final GenericSaveHandle gsh = testConnection(
                        connection.getDriverClassName(),
                        connection.getConnectionUrl(),
                        connection.getDbUser(),
                        password
                );
                return gsh;
            }
        } catch (InvalidCipherDecryption e) {
            return new GenericSaveHandle("Encriptación incorrecta, actualizar la contraseña de la conexión.");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle testConnection(
            final Long connectionId,
            final String driverClassName,
            final String jdbcUrl,
            final String dbUser
    ) throws DataSourceCredentialError {
        final String dbPassword = HQL_findSimpleString(""
                + " SELECT c.dbPassword"
                + " FROM " + DatabaseConnection.class.getCanonicalName() + " c"
                + " WHERE c.id = :connectionId", 
                ImmutableMap.of("connectionId", connectionId),
                true, 
                CacheRegion.REPORT,
                0
        );
        try {
            final String password = DatabaseSourcePool.getDencryptedPassword(dbPassword);
            if (password == null) {
                throw new DataSourceCredentialError(connectionId);
            } else {
                final GenericSaveHandle gsh = testConnection(driverClassName, jdbcUrl, dbUser, password);
                return gsh;            
            }
        } catch (InvalidCipherDecryption e) {
            return new GenericSaveHandle("Encriptación incorrecta, actualizar la contraseña de la conexión");
        }

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle testConnection(
            final String driverClassName,
            final String jdbcUrl,
            final String dbUser,
            final String dbPassword
    ) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final DatabaseSourcePool pool = new DatabaseSourcePool();
        try (final HikariDataSource datasource = pool.createDataSource(driverClassName, dbUser, dbPassword, jdbcUrl, 1)) {
            if (datasource != null && datasource.getConnection() != null) {
                gsh.setOperationEstatus(1);
            } else {
                gsh.setOperationEstatus(0);
            }
        } catch (InvalidCipherDecryption ex) {
            final String error = "Invalid credentials encription, reset connection password";
            getLogger().error(error, ex);
            gsh.setErrorMessage(error);
        } catch (Exception ex) {
            // Validación credenciales inválidas
            if (ex.getCause() != null && ((java.sql.SQLException)ex.getCause()).getErrorCode() == 18456) {
                getLogger().error("Invalid credentials ", ex);
                gsh.setErrorMessage("Invalid credentials");
            } else {
                getLogger().error("There was an error ", ex);
                gsh.setErrorMessage(ex.getMessage());
            }
        }
        return gsh;
    }
    
}
