package qms.framework.dao.bean;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Pattern;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.Utilities;
import Framework.DAO.EntityInterceptor;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.InvalidCipherDecryption;
import bnext.licensing.LicenseUtil;
import com.google.common.collect.ImmutableMap;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.licensing.License;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.framework.daemon.CacheQueryOperation;
import qms.framework.dao.IDatabaseQueryDAO;
import qms.framework.dto.CacheQueryEventDTO;
import qms.framework.dto.DatabaseQueryDTO;
import qms.framework.dto.QueryColumnConfig;
import qms.framework.dto.QueryColumnDTO;
import qms.framework.dto.QueryDataSourceDto;
import qms.framework.entity.CacheQueryEvent;
import qms.framework.entity.DatabaseConnection;
import qms.framework.entity.DatabaseQuery;
import qms.framework.entity.DatabaseQueryLink;
import qms.framework.entity.QueryColumn;
import qms.framework.enums.CacheSchedulerType;
import qms.framework.enums.SystemEvent;
import qms.framework.mail.BnextStaffMailer;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.CacheRegion;
import qms.framework.util.DatabaseQueryCacheHandler;
import qms.framework.util.DatabaseQueryError;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.IDatabaseQuery;
import qms.util.BindUtil;
import qms.util.ModuleUtil;
import qms.util.QMSException;
import qms.util.interfaces.IPagedQuery;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@Repository(value = "DatabaseQueryDAO")
@Scope(value = "singleton")
@Language(module = "qms.framework.dao.bean.DatabaseQueryDAO")
public class DatabaseQueryDAO extends GenericDAOImpl<DatabaseQuery, Long> implements IDatabaseQueryDAO {

    private final DatabaseQueryCacheHandler cacheHandler = new DatabaseQueryCacheHandler();
    private final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();

    private static final String QUERY_SYNC_BY_SYSTEM_EVENT_COUNT = ""
        + "SELECT "
            + " count(c.id)"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " JOIN c.cacheTriggers trigger"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType IN ( "
            + CacheSchedulerType.EVERY_FIVETEEN_MINUTES.getValue() + ","
            + CacheSchedulerType.EVERY_FIVE_MINUTES.getValue() + ","
            + CacheSchedulerType.THREE_TIMES_A_DAY.getValue() + ","
            + CacheSchedulerType.DAILY.getValue() + ","
            + CacheSchedulerType.HOURLY.getValue() + ","
            + CacheSchedulerType.MANUAL.getValue()
        + " )" 
        + " AND EXISTS ("
            + " SELECT 1 "
            + " FROM c.cacheTriggers trigger"
            + " JOIN trigger.systemEvent event"
            + " WHERE event.code = :systemEvent"
        + " )";
    
    private static final String QUERY_SYNC_BY_SYSTEM_EVENT = ""
        + " SELECT c.id"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType IN ( "
            + CacheSchedulerType.EVERY_FIVETEEN_MINUTES.getValue() + ","
            + CacheSchedulerType.THREE_TIMES_A_DAY.getValue() + ","
            + CacheSchedulerType.EVERY_FIVE_MINUTES.getValue() + ","
            + CacheSchedulerType.DAILY.getValue() + ","
            + CacheSchedulerType.HOURLY.getValue() + ","
            + CacheSchedulerType.MANUAL.getValue()
        + " )"
        + " AND EXISTS ("
            + " SELECT 1 "
            + " FROM c.cacheTriggers trigger"
            + " JOIN trigger.systemEvent event"
            + " WHERE event.code = :systemEvent"
        + " )"
        + " ORDER by c.id ASC";
    
    private static final String QUERY_SYNC_STARTUP_COUNT = ""
        + "SELECT "
            + " count(c.id)"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_BUILDING.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_SYNCING.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType IN ( "
            + CacheSchedulerType.EVERY_FIVETEEN_MINUTES.getValue() + ","
            + CacheSchedulerType.THREE_TIMES_A_DAY.getValue() + ","
            + CacheSchedulerType.EVERY_FIVE_MINUTES.getValue() + ","
            + CacheSchedulerType.DAILY.getValue() + ","
            + CacheSchedulerType.HOURLY.getValue() + ","
            + CacheSchedulerType.MANUAL.getValue()
        + " )";
    
    private static final String QUERY_SYNC_STARTUP = ""
        + " SELECT c.id"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_BUILDING.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_SYNCING.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType IN ( "
            + CacheSchedulerType.EVERY_FIVETEEN_MINUTES.getValue() + ","
            + CacheSchedulerType.THREE_TIMES_A_DAY.getValue() + ","
            + CacheSchedulerType.EVERY_FIVE_MINUTES.getValue() + ","
            + CacheSchedulerType.DAILY.getValue() + ","
            + CacheSchedulerType.HOURLY.getValue() + ","
            + CacheSchedulerType.MANUAL.getValue()
        + " )"
        + " ORDER by c.id ASC";
    
    private static final String QUERY_SYNC_FULL_BY_SCHEDULER_TYPE_COUNT = ""
        + "SELECT "
            + " count(c.id)"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType = :schedulerType";
    
    private static final String QUERY_SYNC_FULL_BY_SCHEDULER_TYPE = ""
        + " SELECT c.id"
        + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
        + " WHERE c.deleted = 0"
        + " AND c.status IN ("
                + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
        + " )"
        + " AND c.enableCache = true"
        + " AND c.builtCache = true"
        + " AND c.cacheSchedulerType = :schedulerType"
        + " ORDER by c.id ASC";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getColumns(Long databaseQueryId) {
        return getStrutsComboList(
                QueryColumn.class,
                "code", 
                " c.query.id = " + databaseQueryId,  
                true,
                CacheRegion.REPORT,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public QueryDataSourceDto getDataSource(final Module module) {
        final QueryDataSourceDto dataSource = new QueryDataSourceDto();
        final List<Map<String, Object>> connections = HQL_findByQuery(""
                + " SELECT new Map("
                    + " c.id as id,"
                    + " c.id as value,"
                    + " c.description as description, "
                    + " c.description as text, "
                    + " c.driverClassName as driverClassName"
                + ")"
                + " FROM " + DatabaseConnection.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status = " + Pattern.STATUS.ACTIVE.getValue()
                + " AND c.module = :module"
                + " ORDER BY c.description ASC",
                ImmutableMap.of("module", module.getKey()),
                true,
                CacheRegion.REPORT,
                0
        );
        dataSource.setConnections(connections);
        return dataSource;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle newQuery(
            final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser
    ) throws QMSException, InvalidCipherDecryption, SQLException {
        final ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
        String code = "QRY-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE);
        return this.newQuery(query, loggedUser, code);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle newQuery(
            final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser, final String code
    ) throws InvalidCipherDecryption, SQLException {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (query.getId() > 0) {
            gsh.setErrorMessage("invalid_edition");
            return gsh;
        }
        validateDuplicateDescription(query);
        // Se valida que la tabla "cacheTable" no exista
        validateCachetable(query);
        final Set<QueryColumn> columns = query.getColumns();
        query.setComputedData(false);
        query.setBuiltCache(false);
        query.setColumns(null);
        query.setId(-1L);
        query.setModule(ModuleUtil.fromKey(query.getModule()).getKey());
        query.setCode(code);
        final QueryColumnConfig queryConfig = cacheHandler.getQueryColumnConfig(query, columns);
        cacheHandler.clearColumnIds(query);
        final DatabaseQuery saved = makePersistent(query, loggedUser.getId());
        if (saved == null) {
            gsh.setOperationEstatus(0);
            return gsh;
        }
        cacheHandler.validateColumnNames(saved, columns);
        cacheHandler.updateColumnDataTypes(saved, columns, loggedUser);
        cacheHandler.updateCacheSettings(saved, queryConfig.getPrimaryKey(), queryConfig.getVersionKey(), columns, loggedUser);
        // Se valida que la columna "deleted" exista y sea de tipo valido
        queryHandler.testDeletedColumn(query.getColumns(), query.getEnableCache() != null && query.getEnableCache());
        saveLinks(saved, loggedUser);
        for (final QueryColumn column : columns) {
            column.setId(-1L);
            column.setQuery(saved);
            makePersistent(column, loggedUser.getId());
        }
        cacheHandler.updateColumnsConfig(saved, queryConfig);
        saved.setColumns(columns);
        cacheHandler.updateQueryComputedData(saved, columns, false, loggedUser);
        makePersistent(saved, loggedUser.getId());
        gsh.setSavedId(saved.getId().toString());
        gsh.setOperationEstatus(1);
        return gsh;
    }

    private void validateDuplicateDescription(DatabaseQuery query) {
        final Integer descriptionCount = HQL_findSimpleInteger(""
                + " SELECT count(1)"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c "
                + " WHERE c.id <> :id"
                + " AND c.description = :description"
                + " AND c.deleted = 0",
                ImmutableMap.of(
                        "id", query.getId(),
                        "description", query.getDescription()
                ),
                true,
                CacheRegion.REPORT,
                0
        );
        if (descriptionCount != 0) {
            throw new DatabaseQueryError("unique_database_query_description");
        }
    }
    
    private void validateCachetable(DatabaseQuery query) {
        if (!Objects.equals(query.getEnableCache(), true) || query.getCacheTable() == null || query.getCacheTable().isEmpty()) {
            return;
        }
        final Integer cacheTableCount = HQL_findSimpleInteger(""
                + " SELECT count(1)"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c "
                + " WHERE c.id <> :id"
                + " AND c.cacheTable = :cacheTable",
                ImmutableMap.of(
                        "id", query.getId(),
                        "cacheTable", query.getCacheTable()
                ),
                true,
                CacheRegion.REPORT,
                0
        );
        
        if (cacheTableCount != 0) {
            throw new DatabaseQueryError("unique_database_query_table");
        }
    }

    private List<QueryColumn> getSavedQueryColumns(final Long databaseQueryId) {
        final List<QueryColumn> columns = HQL_findByQuery(""
                + " SELECT c"
                + " FROM " + QueryColumn.class.getCanonicalName() + " c"
                + " JOIN c.query q"
                + " WHERE q.id = :databaseQueryId",
                ImmutableMap.of("databaseQueryId", databaseQueryId),
                true,
                CacheRegion.REPORT,
                0
        );
        return columns;
    }

    private void saveColumns(final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser) {
        final Set<QueryColumn> columnsToSave = query.getColumns();
        final Map<String, QueryColumn> indexColumns = new HashMap();
        columnsToSave.stream().forEach((column) -> indexColumns.put(column.getCode().toLowerCase(), column));
        final List<String> columns = columnsToSave.stream()
            .map(column -> column.getCode())
            .collect(Collectors.toList());
        final Map<String, Object> params = new HashMap<>();
        final String newColumnsFilter = BindUtil.bindFilterListNotIn("code", 
                columns,
                false, 
                EntityInterceptor.COLLATION_LATIN_CS_AI,
                params
        );
        params.put("databaseQueryId", query.getId());
        if (!Objects.equals(query.getEditableHierarchy(), 0)) {
            final Long countColumns = SQL_findSimpleLong(""
                + " SELECT count(c.query_column_id) as countColumns"
                + " FROM query_column c"
                + " WHERE ("
                        + " c.hierarchy_level_order IS NOT NULL"
                        + " OR c.hierarchy_readonly_order IS NOT NULL"
                    + " ) "
                    + " AND c.database_query_id = :databaseQueryId"
                    + " AND " + newColumnsFilter,
                params
            );
            if (countColumns > 0) {
                throw new DatabaseQueryError("readonlyHierarchyMustKeepSameColumns");
            }
        }
        final Long countColumns = SQL_findSimpleLong(""
            + " SELECT count(q.query_column_id)"
            + " FROM query_column q "
            + " WHERE "
                + " q.database_query_id = :databaseQueryId"
                + " AND " + newColumnsFilter,
            params);
        if (countColumns > 0) {
            SQL_updateByQuery(""
                + " DELETE FROM query_column "
                + " WHERE "
                    + " database_query_id = :databaseQueryId"
                    + " AND " + newColumnsFilter,
                params,
                0,
                Arrays.asList("query_column")
            );
        }
        final List<QueryColumn> savedQueryColumns = getSavedQueryColumns(query.getId());
        final List<String> savedColumns = savedQueryColumns.stream().map(s -> s.getCode().toLowerCase()).collect(Collectors.toList());
        final Set<QueryColumn> newColumns = columnsToSave.stream()
                .filter((column) -> !(savedColumns.contains(column.getCode().toLowerCase()))).map((column) -> {
                column.setId(-1L);
                return column;
            }).map((column) -> {
                column.setQuery(query);
                return makePersistent(column, loggedUser.getId());
            }).collect(Collectors.toSet());
        savedQueryColumns.stream()
                .filter(column -> indexColumns.containsKey(column.getCode()))
                .forEach(column -> {
                    final QueryColumn toSave = indexColumns.get(column.getCode());
                    column.setQuery(query);
                    column.setHierarchyLevelOrder(toSave.getHierarchyLevelOrder());
                    column.setHierarchyLevelLabel(toSave.getHierarchyLevelLabel());
                    column.setHierarchyReadonlyOrder(toSave.getHierarchyReadonlyOrder());
                    column.setHierarchyReadonlyLabel(toSave.getHierarchyReadonlyLabel());
                    column.setHierarchyReadonlyLevel(toSave.getHierarchyReadonlyLevel());
                    column.setHierarchyReadonlyVisible(toSave.getHierarchyReadonlyVisible());
                    makePersistent(column, loggedUser.getId());
                });
        newColumns.addAll(savedQueryColumns);
        query.setColumns(newColumns);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle editQuery(
            final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser
    ) throws InvalidCipherDecryption, SQLException  {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final DatabaseQuery unchanged = HQLT_findById(
                DatabaseQuery.class,
                query.getId(),
                true,
                CacheRegion.REPORT,
                0
        );
        final DatabaseQuery.STATUS status = DatabaseQuery.STATUS.getStatus(unchanged.getStatus());
        
        if (query.getId() <= 0 || Objects.equals(DatabaseQuery.STATUS.WITH_CACHE_ACTIVE, status)) {
            gsh.setErrorMessage("invalid_edition");
            return gsh;
        }
        validateDuplicateDescription(query);
        // Se valida que la tabla "cacheTable" no exista
        validateCachetable(query);
        query.setComputedData(false);
        query.setBuiltCache(unchanged.getBuiltCache());
        query.setEditableHierarchy(unchanged.getEditableHierarchy());
        // Se valida que la columna "deleted" exista y sea de tipo valido
        queryHandler.testDeletedColumn(query.getColumns(), query.getEnableCache() != null && query.getEnableCache());
        try {
            saveColumns(query, loggedUser);
        } catch(Exception e) {
            throw e;
        }
        final Set<QueryColumn> columns = query.getColumns();
        final QueryColumnConfig queryConfig = cacheHandler.getQueryColumnConfig(query, columns);
        cacheHandler.clearColumnIds(query);
        cacheHandler.validateColumnNames(query, columns);
        cacheHandler.updateColumnDataTypes(query, columns, loggedUser);
        query.setModule(ModuleUtil.fromKey(query.getModule()).getKey());
        cacheHandler.updateCacheSettings(query, queryConfig.getPrimaryKey(), queryConfig.getVersionKey(), columns, loggedUser);
        saveLinks(query, loggedUser);
        query.setColumns(null);
        cacheHandler.updateColumnsConfig(query, queryConfig);
        cacheHandler.updateQueryComputedData(query, columns, true, loggedUser);
        final DatabaseQuery saved = makePersistent(query, loggedUser.getId());
        if (saved == null) {
            gsh.setOperationEstatus(0);
            return gsh;
        }
        gsh.setOperationEstatus(1);
        gsh.setSavedId(query.getId().toString());
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle delete(final Long databaseQueryId, @Nonnull final ILoggedUser loggedUser) {
        if (databaseQueryId == null) {
            return GenericSaveHandle.success("unnecesary_delete_no_id");
        }
        final DatabaseQuery query = HQLT_findById(DatabaseQuery.class, databaseQueryId, true, CacheRegion.REPORT, 0);
        final GenericSaveHandle gsh = new GenericSaveHandle();
        query.setDeleted(1);
        final DatabaseQuery saved = makePersistent(query, loggedUser.getId());
        if (saved != null) {
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }
    
    private void saveLinks(final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser) {
        final Set<DatabaseQueryLink> links = query.getLinks();
        if (links != null && !links.isEmpty()) {
            final List<Long> linkIds = links.stream()
                    .map(trigger -> trigger.getId().getTargetQueryId())
                    .collect(Collectors.toList());
            saveLinkedItems(DatabaseQueryLink.class, query.getId(), linkIds, true, loggedUser.getId());
        } else {
            saveLinkedItems(DatabaseQueryLink.class, query.getId(), new ArrayList<>(0), true, loggedUser.getId());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getQueryToSyncCount(
            final CacheSchedulerType schedulerType,
            final SystemEvent systemEvent
    ) {
        switch (schedulerType) {
            case RECORD_INSERT: 
            case RECORD_UPDATE: 
                return HQL_findSimpleLong(
                        QUERY_SYNC_BY_SYSTEM_EVENT_COUNT,
                        ImmutableMap.of("systemEvent", systemEvent.getCode()),
                        true,
                        CacheRegion.REPORT,
                        0
                );
            case STARTUP: 
                return HQL_findSimpleLong(
                        QUERY_SYNC_STARTUP_COUNT,
                        Utilities.EMPTY_MAP,
                        true,
                        CacheRegion.REPORT,
                        0
                );
            default:
                return HQL_findSimpleLong(
                        QUERY_SYNC_FULL_BY_SCHEDULER_TYPE_COUNT, 
                        ImmutableMap.of("schedulerType", schedulerType.getValue()),
                        true,
                        CacheRegion.REPORT,
                        0
                );
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<Long> getQueryIdsToSync(
            final CacheSchedulerType schedulerType,
            final SystemEvent systemEvent, 
            final IPagedQuery pageConfig,
            @Nonnull final ILoggedUser loggedUser
    ) {
        switch (schedulerType) {
            case RECORD_INSERT:
            case RECORD_UPDATE:
                final Map<String, Object> eventParams = new HashMap<>(1);
                eventParams.put("systemEvent", systemEvent.getCode());
                return new HashSet<>(HQL_findByQueryPaged(QUERY_SYNC_BY_SYSTEM_EVENT, eventParams, pageConfig, true, CacheRegion.REPORT, 0));
            case STARTUP: 
                return new HashSet<>(HQL_findByQueryPaged(QUERY_SYNC_STARTUP, null, pageConfig, true, CacheRegion.REPORT, 0));
            default:
                final Map<String, Object> typeParams = new HashMap<>(1);
                typeParams.put("schedulerType", schedulerType.getValue());
                return new HashSet<>(HQL_findByQueryPaged(QUERY_SYNC_FULL_BY_SCHEDULER_TYPE, typeParams, pageConfig, true, CacheRegion.REPORT, 0));
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateQueryAsCacheActive(final Long databaseQueryId, @Nonnull final ILoggedUser loggedUser) {
        return updateQueryAsCache(databaseQueryId, DatabaseQuery.STATUS.WITH_CACHE_ACTIVE, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateQueryAsCache(final Long databaseQueryId, DatabaseQuery.STATUS status, @Nonnull final ILoggedUser loggedUser) {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("id", databaseQueryId);
        params.put("status", status.getValue());
        params.put("userId", loggedUser.getId());
        return HQL_updateByQuery(""
                + " UPDATE " + DatabaseQuery.class.getCanonicalName() + " c"
                + " SET "
                        + " c.status = :status,"
                        + " c.lastModifiedDate = current_timestamp(),"
                        + " c.lastModifiedBy = :userId"
                + " WHERE c.id = :id",
                params,
                true,
                CacheRegion.REPORT,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer disableHierarchyEdition(Long databaseQueryId, @Nonnull final ILoggedUser loggedUser) {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("id", databaseQueryId);
        params.put("editableHierarchy", false);
        params.put("userId", loggedUser.getId());
        return HQL_updateByQuery(""
                + " UPDATE " + DatabaseQuery.class.getCanonicalName() + " c"
                + " SET "
                        + " c.editableHierarchy = :editableHierarchy,"
                        + " c.lastModifiedDate = current_timestamp(),"
                        + " c.lastModifiedBy = :userId"
                + " WHERE c.id = :id",
                params,
                true,
                CacheRegion.REPORT,
                0
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<Long> getQueryIdsToBuild(IPagedQuery pageConfig) {
        final List<Long> ids = HQL_findByQueryPaged(""
                + " SELECT c.id"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status IN ("
                        + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                        + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
                + " )"
                + " AND c.enableCache = true"
                + " AND c.builtCache = false",
                null,
                pageConfig,
                true, 
                CacheRegion.REPORT,
                0
        );
        return new HashSet<>(ids);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long buildPendingCacheCount() {
        final Long count = HQL_findSimpleLong(""
                + "SELECT "
                    + " count(c.id)"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status IN ("
                        + DatabaseQuery.STATUS.ACTIVE.getValue() + ","
                        + DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue()
                + " )"
                + " AND c.enableCache = true"
                + " AND c.builtCache = false",
                Utilities.EMPTY_MAP,
                true,
                CacheRegion.REPORT, 
                0
        );
        return count;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean getEnableCache(final Long databaseQueryId) {
        final Boolean enableCache = (Boolean) HQL_findSimpleObject(""
                + " SELECT c.enableCache"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", 
                ImmutableMap.of("id", databaseQueryId),
                true, 
                CacheRegion.REPORT,
                0
        );
        return enableCache;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle toggleCacheStatus(final Long databaseQueryId, final @Nonnull LoggedUser loggedUser) {
        final Integer status = HQL_findSimpleInteger(""
                + " SELECT c.status"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", 
                ImmutableMap.of("id", databaseQueryId),
                true, 
                CacheRegion.REPORT, 
                0
        );
        final DatabaseQuery query = HQLT_findById(DatabaseQuery.class, databaseQueryId);        
        if (DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue().equals(status)) {
            query.setStatus(DatabaseQuery.STATUS.WITH_CACHE_INACTIVE.getValue());
        } else if (DatabaseQuery.STATUS.WITH_CACHE_INACTIVE.getValue().equals(status)) {
            query.setStatus(DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue());
        } else {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("INVALID_CACHE_STATUS_" + status);
            return gsh;
        }
        final Long userId = SecurityRootUtils.getValidUserOfFirstAdmin(loggedUser.getId());
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final DatabaseQuery saved = makePersistent(query, userId);
        if (saved == null) {
            gsh.setOperationEstatus(0);
        } else {
            gsh.setOperationEstatus(1);
            gsh.setSavedId(saved.getId() + "");
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long buildPendingComputedDataCount() {
        final Long count = HQL_findSimpleLong(""
                + "SELECT "
                    + " count(c.id)"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " c"
                + " WHERE c.computedData = false",
                Utilities.EMPTY_MAP,
                true, 
                CacheRegion.REPORT,
                0
        );
        return count;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getNotifyFailuresCount() {
        final Long count = HQL_findSimpleLong(""
                + "SELECT "
                    + " count(c.id)"
                + " FROM " + CacheQueryEvent.class.getCanonicalName() + " c"
                + " WHERE c.errorMailSend = false"
                + " AND c.status IN ("
                        + CacheQueryEvent.STATUS.BUILD_FAILED.getValue() + ","
                        + CacheQueryEvent.STATUS.SYNC_FAILED.getValue() 
                + " )"
                + " AND c.deleted = 0",
                Utilities.EMPTY_MAP,
                true, 
                CacheRegion.REPORT,
                0
        );
        return count;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public Boolean sendNotifyFailures(final IPagedQuery pageConfig, @Nonnull final ILoggedUser loggedUser) {
        final License lic = LicenseUtil.getCurrentLicense();
        if (lic == null) {
            getLogger().error("License not configured, could not notify database query failures.");
            return false;
        }
        final BnextStaffMailer mailer = new BnextStaffMailer(this);
        final List<CacheQueryEventDTO> events = HQL_findByQueryPaged(""
                    + " SELECT new " + CacheQueryEventDTO.class.getCanonicalName() + "("
                        + " c.id AS id,"
                        + " c.status AS status,"
                        + " c.description AS description,"
                        + " c.createdDate AS createdDate,"
                        + " c.module AS module,"
                        + " q.description AS queryDescription"
                    + " )"
                    + " FROM " + CacheQueryEvent.class.getCanonicalName() + " c "
                    + " JOIN c.query q"
                    + " WHERE c.errorMailSend = false"
                    + " AND c.status IN ("
                            + CacheQueryEvent.STATUS.BUILD_FAILED.getValue() + ","
                            + CacheQueryEvent.STATUS.SYNC_FAILED.getValue()
                    + " )"
                    + " AND c.deleted = 0"
                    + " ORDER BY c.createdDate DESC", pageConfig
        );
        if (events != null && !events.isEmpty()) {
            final Boolean send = mailer.sendDatabaseQueryFailures(events, loggedUser);
            if (send) {
                final Map<String, Object> params = new HashMap<>(2);
                params.put("loggedUserId", loggedUser.getId());
                params.put("errorMailSendTo", lic.getSupportEmail());
                final List<Long> ids = events.stream()
                        .map(event -> event.getId())
                        .collect(Collectors.toList());
                HQL_updateByQuery(""
                        + " UPDATE " + CacheQueryEvent.class.getCanonicalName() + " c"
                        + " SET "
                                + " c.errorMailSend = true,"
                                + " c.errorMailSendDate = current_timestamp(),"
                                + " c.errorMailSendTo = :errorMailSendTo,"
                                + " c.lastModifiedDate = current_timestamp(),"
                                + " c.lastModifiedBy = :loggedUserId"
                        + " WHERE " + BindUtil.parseFilterList("c.id", ids, true),
                        params
                );
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public Boolean releaseBusyQueries(final Integer queryTimeoutSeconds, @Nonnull final ILoggedUser loggedUser) {
        getLogger().debug("Releasing all busy queries");
        final Map<String, Object> params = new HashMap<>(1);
        params.put("status", DatabaseQuery.STATUS.WITH_CACHE_INACTIVE.getValue());
        params.put("userId", loggedUser.getId());
        if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
            //Se resta una hora por el cambio de horario
            final LocalDateTime targetTime = LocalDateTime.now()
                    .minus(Duration.of(queryTimeoutSeconds, ChronoUnit.SECONDS))
                    .minus(Duration.of(1, ChronoUnit.HOURS));
            final Date targetDate = Date.from(targetTime.atZone(ZoneId.systemDefault()).toInstant());
            params.put("targetDate", targetDate);
        } else {
            params.put("targetDate", new Date());            
        }

        
        final Integer buildResult = HQL_updateByQuery(""
                + " UPDATE " + DatabaseQuery.class.getCanonicalName() + " c"
                + " SET "
                        + " c.status = :status,"
                        + " c.lastModifiedDate = current_timestamp(),"
                        + " c.lastModifiedBy = :userId"
                + " WHERE c.deleted = 0"
                + " AND c.lastModifiedDate < :targetDate"
                + " AND c.status IN ("
                        + DatabaseQuery.STATUS.WITH_CACHE_BUILDING.getValue()
                + " )"
                + " AND c.enableCache = true",
                params,
                true,
                CacheRegion.REPORT, 
                0
                
        );
        getLogger().debug("Released {} buiiding cache queries", buildResult);
        params.put("status", DatabaseQuery.STATUS.WITH_CACHE_ACTIVE.getValue());
        final Integer syncResult = HQL_updateByQuery(""
                + " UPDATE " + DatabaseQuery.class.getCanonicalName() + " c"
                + " SET "
                        + " c.status = :status,"
                        + " c.lastModifiedDate = current_timestamp(),"
                        + " c.lastModifiedBy = :userId"
                + " WHERE c.deleted = 0"
                + " AND c.lastModifiedDate < :targetDate"
                + " AND c.status IN ("
                        + DatabaseQuery.STATUS.WITH_CACHE_SYNCING.getValue()
                + " )"
                + " AND c.enableCache = true",
                params,
                true,
                CacheRegion.REPORT, 
                0
                
        );
        getLogger().debug("Released {} syncing cache queries", syncResult);
        return buildResult + syncResult > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public CacheQueryEvent saveFailedEvent(
            final DatabaseQuery query,
            final CacheQueryEvent event,
            final Exception e,
            @Nonnull final ILoggedUser loggedUser
    ) {
        event.setErrorMailSend(false);
        event.setBuiltCache(false);
        final Throwable cause = ExceptionUtils.getRootCause(e);
        if (cause != null) {
            event.setDescription(StringUtils.truncate(cause.getLocalizedMessage(), 3999));
        } else {
            event.setDescription(StringUtils.truncate(e.getLocalizedMessage(), 3999));
        }
        event.setModule(query.getModule());
        event.setQuery(new DatabaseQuery(query.getId()));
        event.setEndDate(Utilities.getNow());
        if (event.getCreatedBy() == null || event.getLastModifiedBy() == null) {
            event.setCreatedBy(loggedUser.getId());
            event.setLastModifiedBy(loggedUser.getId());
        }
        return makePersistent(event, loggedUser.getId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class}) 
    public CacheQueryEvent saveFailedEventByOperation(
            final Long databaseQueryId, 
            final CacheSchedulerType schedulerType, 
            final CacheQueryOperation operation,
            final Long recordId, 
            final Exception ex,
            @Nonnull final ILoggedUser loggedUser
    ) {
        final CacheQueryEvent event = defaultEvent(schedulerType);
        switch (operation) {
            case BUILD_CACHE:
                event.setStatus(CacheQueryEvent.STATUS.BUILD_FAILED.getValue());
                break;
            case SYNC_FULL_CACHE:
            case SYNC_RECORD_CACHE:
                event.setStatus(CacheQueryEvent.STATUS.SYNC_FAILED.getValue());
                break;
        }
        final DatabaseQuery query = HQLT_findById(DatabaseQuery.class, databaseQueryId);
        defaultEventData(event, schedulerType, query);
        event.setSchedulerType(schedulerType.getValue());
        event.setQuery(query);
        return saveFailedEvent(query, event, ex, loggedUser);
    } 
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public CacheQueryEvent defaultEvent(final CacheSchedulerType schedulerType) {
        final CacheQueryEvent event = new CacheQueryEvent();
        event.setId(-1L);
        event.setStartDate(Utilities.getNow());
        event.setSchedulerType(schedulerType.getValue());
        event.setCode(UUID.randomUUID().toString());
        event.setBuiltCache(false);
        event.setModule(Module.TIMESHEET.getKey());
        event.setSystemEvent(null);
        event.setPrimaryKeyValue(null);
        event.setPrimaryKeyType(null);
        event.setNumberRecordsUpdated(0L);
        event.setNumberRecordsInserted(0L);
        return event;
    }    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public void defaultEventData(
            final CacheQueryEvent event,
            final CacheSchedulerType schedulerType,
            final IDatabaseQuery query
    ) {
        event.setSchedulerType(schedulerType.getValue());
        event.setModule(query.getModule());
        event.setQuery(new DatabaseQuery(query.getId()));
        event.setPrimaryKeyId(query.getCachePrimaryKeyId());
        event.setPrimaryKeyCode(query.getCachePrimaryKeyCode());
        event.setVersionKeyId(query.getCacheVersionKeyId());
        event.setVersionKeyCode(query.getCacheVersionKeyCode());
        if (query.getDatabaseConnectionId() != null) {
            final DatabaseConnection connection = HQLT_findById(
                    DatabaseConnection.class, 
                    query.getDatabaseConnectionId(),
                    true,
                    CacheRegion.REPORT,
                    0
            );
            event.setConnection(connection);
        }
        event.setSource(query.getSource());
        event.setDatabaseSchema(query.getCacheSchema());
        event.setDatabaseTable(query.getCacheTable());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public void defaultEventData(
            final CacheQueryEvent event,
            final CacheSchedulerType schedulerType,
            final IDatabaseQuery query,
            final Long cachePrimaryKeyId,
            final Long cacheVersionKeyId
    ) {
        event.setModule(query.getModule());
        event.setSchedulerType(schedulerType.getValue());
        event.setQuery(new DatabaseQuery(query.getId()));
        event.setPrimaryKeyId(cachePrimaryKeyId);
        event.setVersionKeyId(cacheVersionKeyId);
        if (query.getDatabaseConnectionId() != null) {
            final DatabaseConnection connection = HQLT_findById(
                    DatabaseConnection.class, 
                    query.getDatabaseConnectionId(), 
                    true,
                    CacheRegion.REPORT,
                    0
            );
            event.setConnection(connection);
        }
        event.setSource(query.getSource());
        event.setDatabaseSchema(query.getCacheSchema());
        event.setDatabaseTable(query.getCacheTable());
    }
       
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IDatabaseQueryDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IDatabaseQueryDAO.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DatabaseQueryDTO loadQuery(Long databaseQueryId) {
        if (databaseQueryId == null) {
            return null;
        }
        final DatabaseQueryDTO entity =  (DatabaseQueryDTO) HQL_findSimpleObject(
                QUERY_DTO,
                ImmutableMap.of("id", databaseQueryId),
                true,
                CacheRegion.REPORT,
                0
        );
        return entity;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public QueryColumnDTO loadColumn(Long databaseColumnId) {
        if (databaseColumnId == null) {
            return null;
        }
        final QueryColumnDTO entity =  (QueryColumnDTO) HQL_findSimpleObject(
                COLUMN_DTO,
                ImmutableMap.of("id", databaseColumnId),
                true,
                CacheRegion.REPORT,
                0
        );
        return entity;
    }
    
    
}