package qms.framework.dao;

import DPMS.Mapping.Document;
import DPMS.Mapping.Request;
import Framework.Config.Utilities;
import Framework.DAO.IGenericDAO;
import Framework.DAO.IUntypedDAO;
import Framework.DAO.Implementation;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.core.util.Loggable;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.custom.dto.IFlexiFields;
import qms.form.dto.FormSlimReportDTO;
import qms.form.entity.FormPublishMigrationColumn;
import qms.form.util.FixedField;
import qms.form.util.SurveyUtil;
import qms.framework.dao.bean.SlimReportsDAO;
import qms.framework.entity.SlimReports;
import qms.framework.util.CacheRegion;
import qms.util.HQLHandler;
import qms.util.QMSException;
import qms.util.SQLHandler;

@Implementation(name = "SlimReportsDAO")
public interface ISlimReportsDAO extends IGenericDAO<SlimReports, Long> {

    Integer checkSlimReportForRegenerate(
            String masterId,
            List<String> commonColumns,
            ILoggedUser loggedUser
    ) throws QMSException;

    Long getActiveSurveyId(String documentMasterId);

    Integer regenerateSlimReport(String masterId, ILoggedUser loggedUser) throws QMSException;

    ResponseEntity newSlimReport(FormSlimReportDTO options, ILoggedUser loggedUser) throws QMSException;

    ResponseEntity<FormSlimReportDTO> loadSlimReport(Long slimReportId, ILoggedUser loggedUser);

    ResponseEntity<String> updateSlimReport(FormSlimReportDTO options, ILoggedUser loggedUser) throws QMSException;

    ResponseEntity deleteSlimReport(Long reportId, ILoggedUser loggedUser);

    Long migrateSlimReportDefault(FormSlimReportDTO options, Long nodeId, ILoggedUser loggedUser) throws QMSException;
    
    boolean repeatedSlimReportName(String repeatedSlimReportName);
    
    void newSlimReportFromDocument(Request request, Document document, ILoggedUser loggedUser) throws RuntimeException;

    static Long getSlimReportId(
        @Nonnull final IUntypedDAO dao,
        @Nonnull final String documentMasterId,
        @Nonnull final String slimReportCode
    ) {
        final StringBuilder query = new StringBuilder();
        query.append(" "
            + " SELECT max(sr.id) "
            + " FROM ").append(SlimReports.class.getCanonicalName()).append(" sr "
            + " WHERE "
                + " sr.documentMasterId = :documentMasterId "
                + " AND sr.code = :slimReportCode "
        );
        return dao.HQL_findLong(query.toString(), ImmutableMap.of(
                "documentMasterId", documentMasterId,
                "slimReportCode", slimReportCode
        ), true, CacheRegion.REPORT, 0);
    }

    static String getParsedSlimReportQuery(
        final String documentMasterId,
        final String surveyAnswersTable,
        final IFlexiFields options,
        final boolean topOne,
        final boolean includeAllStatus,
        final boolean includeFiltersAsColumns
    ) throws QMSException {
        final String systemLang = Utilities.getSettings().getLang();
        final StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append(" SELECT ");
        if (topOne) {
            sqlSelect.append(" TOP(1) ");
        }
        final LinkedHashSet<String> flexiFields = new LinkedHashSet<>(options.getFlexiFields());
        if (options.getRelatedFields() != null && !options.getRelatedFields().isEmpty()) {
            flexiFields.addAll(options.getRelatedFields());
        }
        if (!flexiFields.isEmpty()) {
            // Se agregan migraciones
            List<FormPublishMigrationColumn> migrationColumns = Utilities.getUntypedDAO().HQLT_findByQuery(FormPublishMigrationColumn.class, " " +
                " SELECT c " +
                " FROM " + FormPublishMigrationColumn.class.getCanonicalName() + " c " +
                " WHERE " +
                    " c.deleted = 0 " +
                    " AND c.status = 1 " +
                    " AND c.rootDocumentMasterId = :documentMasterId" +
                    " AND c.targetFieldColumnName IN (:existingFields) ",
                ImmutableMap.of(
                    "documentMasterId", documentMasterId,
                    "existingFields", flexiFields
                ),
                true,
                CacheRegion.SURVEY,
                0
            );
            final StringBuilder sqlSelectMigrations = new StringBuilder();
            migrationColumns.forEach(column -> {
                String rootFieldColumnName = column.getRootFieldColumnName();
                flexiFields.remove(rootFieldColumnName);
                String targetFieldColumnName = column.getTargetFieldColumnName();
                final boolean escapeTargetColumn = !Objects.equals(HQLHandler.getSafeParameterName(targetFieldColumnName), targetFieldColumnName);
                final boolean escapeRootColumn = !Objects.equals(HQLHandler.getSafeParameterName(rootFieldColumnName), rootFieldColumnName);
                sqlSelectMigrations
                        .append(SurveyUtil.SURVEY_FIELD_ALIAS)
                        .append(escapeTargetColumn ? ".[" : ".").append(targetFieldColumnName).append(escapeTargetColumn ? "]" : "")
                        .append(SQLHandler.STR_AS).append(escapeRootColumn ? "[" : "").append(rootFieldColumnName).append(escapeRootColumn ? "], " : ",");
            });
            if (includeFiltersAsColumns && options.getFlexiFilters() != null && !options.getFlexiFilters().isEmpty()) {
                flexiFields.addAll(options.getFlexiFilters());
            }
            // Se agregan columnas configuradas
            final List<String> safeFlexiFields = flexiFields.stream().map(field -> {
                final boolean escapeColumn = !Objects.equals(HQLHandler.getSafeParameterName(field), field);
                if (escapeColumn) {
                    return SurveyUtil.SURVEY_FIELD_ALIAS + ".[" + field + "]" + SQLHandler.STR_AS +  "[" + field + "]";
                } else {
                    return SurveyUtil.SURVEY_FIELD_ALIAS + "." + field + SQLHandler.STR_AS + field;
                }
            }).collect(Collectors.toList());
            sqlSelect
                    .append(sqlSelectMigrations) // Se colocan las migraciones al final para no interferir con las REGEXP de las columnas originales
            ;
            sqlSelect.append(String.join(", ", safeFlexiFields)).append(", ");
        } else {
            Loggable.getLogger(SlimReportsDAO.class).warn("SlimReport '{}', has been generated with no columns.", surveyAnswersTable);
        }
        sqlSelect.append(" "
                // fixedFields
                + " ").append(FixedField.REQUESTOR.getColumn()).append(" AS ").append(FixedField.REQUESTOR.getAlias()).append(" "
                + ",").append(FixedField.STAGE.getColumn()).append(" AS ").append(FixedField.STAGE.getAlias()).append(" "
                + ",").append(FixedField.PROGRESS.getColumn()).append(" AS ").append(FixedField.PROGRESS.getAlias()).append(" "
                + ",").append(FixedField.CODE.getColumn()).append(" AS ").append(FixedField.CODE.getAlias()).append(" "
                + ",").append(FixedField.STATUS_PROGRESS.getColumn(systemLang)).append(" AS ").append(FixedField.STATUS_PROGRESS.getAlias()).append(" "
                + ",").append(FixedField.STATUS.getColumn(systemLang)).append(" AS ").append(FixedField.STATUS.getAlias()).append(" "
                + ",").append(FixedField.BUSINESS_UNIT.getColumn()).append(" AS ").append(FixedField.BUSINESS_UNIT.getAlias()).append(" "
                + ",").append(FixedField.BUSINESS_UNIT_DEPARTMENT.getColumn()).append(" AS ").append(FixedField.BUSINESS_UNIT_DEPARTMENT.getAlias()).append(" "
                + ",").append(FixedField.AREA.getColumn()).append(" AS ").append(FixedField.AREA.getAlias()).append(" "
                + ",").append(FixedField.LAST_MODIFIED_DATE.getColumn()).append(" AS ").append(FixedField.LAST_MODIFIED_DATE.getAlias()).append(" "
                + ",").append(FixedField.CREATED_DATE.getColumn()).append(" AS ").append(FixedField.CREATED_DATE.getAlias()).append(" "
                + ",").append(FixedField.REQUEST.getColumn()).append(" AS ").append(FixedField.REQUEST.getAlias()).append(" "
                + ",").append(FixedField.OUTSTANDING_SURVEYS.getColumn()).append(" AS ").append(FixedField.OUTSTANDING_SURVEYS.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD1.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD1.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD2.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD2.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD3.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD3.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD4.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD4.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD5.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD5.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD6.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD6.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD7.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD7.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD8.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD8.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD9.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD9.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD10.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD10.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD11.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD11.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD12.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD12.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD13.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD13.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD14.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD14.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD15.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD15.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD16.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD16.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD17.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD17.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD18.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD18.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD19.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD19.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD20.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD20.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD21.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD21.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD22.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD22.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD23.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD23.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD24.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD24.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD25.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD25.getAlias()).append(" "
                + ",").append(FixedField.AREA_CUSTOM_FIELD26.getColumn()).append(" AS ").append(FixedField.AREA_CUSTOM_FIELD26.getAlias()).append(" "
                + ",").append(FixedField.REGION_DESC.getColumn()).append(" AS ").append(FixedField.REGION_DESC.getAlias()).append(" "
                + ",").append(FixedField.REGION_CODE.getColumn()).append(" AS ").append(FixedField.REGION_CODE.getAlias()).append(" "
                + ",o.business_unit_id AS business_unit_id "
                + ",o.business_unit_department_id AS business_unit_department_id "
                + ",o.area_id AS area_id "
                + ",r.id AS request_id "
                + ",o.archived AS archived "
                + ",o.last_modified_date AS last_modified_date "
                + ",o.created_date AS created_date "
                + ",o.outstanding_surveys_id AS outstanding_surveys_id "
                + ",r.reazon AS ").append(FixedField.REQUEST.getAlias()).append(" "
                + ",r.vch_descripcion AS ").append(FixedField.OUTSTANDING_SURVEYS.getAlias()).append(SurveyUtil.EXTRA_COLUMNS_TOKEN).append(" "
            + " FROM ").append(surveyAnswersTable).append(" ").append(SurveyUtil.SURVEY_FIELD_ALIAS).append(" "
            + " JOIN outstanding_surveys o ON o.outstanding_surveys_id = surveyField.outstanding_surveys_id1 ").append(SurveyUtil.EXTRA_JOINS_TOKEN).append(" "
            + " LEFT JOIN request r ON r.id = o.request_id "
            + " LEFT JOIN users requestor ON requestor.user_id = r.autor_id"
            + " LEFT JOIN form_progress_state state ON state.form_progress_state_id = o.form_progress_state_id"
            + " LEFT JOIN business_unit_department bud ON bud.business_unit_department_id = o.business_unit_department_id "
            + " LEFT JOIN department AS dep ON bud.department_id = dep.department_id"
            + " LEFT JOIN business_unit bu ON bu.business_unit_id = o.business_unit_id "
            + " LEFT JOIN area ON area.area_id = o.area_id "
            + " LEFT JOIN region ON region.region_id = bud.region_id "
            + " WHERE "
                + " ").append(SurveyUtil.SURVEY_FIELD_ALIAS).append(".deleted = 0  "
                + " AND ( "
                    + " r.int_borrado = 0 "
                    + " OR r.int_borrado IS NULL " // <-- Necesario para consultar historico
                + " ) ").append(
            includeAllStatus ? "": 
                 " AND o.status NOT IN ("
                    + OutstandingSurveys.STATUS.STAND_BY.getValue()
                    /*
                      No se incluyen los siguientes, en caso de que se requieran para excluir borrador, asegurarse que los borradores no cambien de estatus:
                       1) IN_PROGRESS_FILLED_PARCIALLY / ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA
                       2) IN_PROGRESS_FILL_LATER / ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE
                      **/
                + ")"
            ).append(SurveyUtil.EXTRA_WHERE_TOKEN);
        return sqlSelect.toString();
    }

            
}
