package qms.framework.util;

import Framework.Config.Utilities;
import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import mx.bnext.core.file.FileHandler;
import mx.bnext.core.file.IFileManager;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.WorkbookUtil;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.slf4j.Logger;
import qms.framework.bulk.util.ExcelErrorCoordinateXY;
import qms.framework.dto.BulkCellStyle;
import qms.framework.dto.DataExchangeDTO;

public class ExcelWriter {

    private static final Logger LOGGER = Loggable.getLogger(ExcelWriter.class);
    private static final String EXCEL_WRITER = "excel-writer-";
    private static final Pattern RGB_PATTERN = Pattern.compile("rgb\\(([0-9]+),[\\s]*([0-9]+),[\\s]*([0-9]+)\\)");
    private static final Double MIN_INFLATE_RATIO = 0.001d;
    private static final Integer MIN_COLUMN_WIDTH = 5000;

    private final Map<String, XSSFColor> customColors = new HashMap<>();
    private final DefaultIndexedColorMap indexColor = new DefaultIndexedColorMap();
    private final String appName;
    private final IFileManager fileManager;

    public ExcelWriter(final String appName, final IFileManager fileManager) {
        this.appName = appName;
        this.fileManager = fileManager;
        ZipSecureFile.setMinInflateRatio(MIN_INFLATE_RATIO);
    }

    /**
     * Crea hojas de Excel con los datos y errores proporcionados.
     * - Genera una hoja de Excel con el nombre proporcionado.
     * - Escribe los encabezados y los datos en la hoja.
     * - Configura la hoja con los encabezados, errores y el tamaño de las columnas.
     * - Congela la primera fila y configura el autoajuste de columnas.
     * - Configura el filtro automático.
     * - Configura la celda activa si se indica.
     *
     *
     * @param sheetName: El nombre de la pestaña de la hoja de Excel
     * @param workbook: El libro donde se escribirá la hoja
     * @param data: Los datos a escribir en la hoja
     * @param errors: Errores identificados en la hoja
     * @param configureActiveCell: Indica si se debe configurar la celda activa
     * @return Devuelve la hoja de Excel generada `Sheet`
     */
    public Sheet generateSheet(
            final String sheetName,
            final Workbook workbook,
            final DataExchangeDTO data,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final boolean configureActiveCell
    ) {
        final SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(WorkbookUtil.createSafeSheetName(sheetName));
        sheet.trackAllColumnsForAutoSizing();
        final BulkCellStyle cellStyle = new BulkCellStyle();
        cellStyle.setBackgroundColor("white");
        cellStyle.setColor("black");
        final List<String> headers;
        if (data.getValues() != null) {
            headers = data.getValues().get(0);
        } else {
            headers = new ArrayList<>(0);
        }
        final Integer numberColumns = headers.size();
        if (numberColumns > 0) {
            final Map<Integer, Integer> headersWidth = writeHeaders(workbook, sheet, headers, errors, numberColumns);
            writSheetRecords(workbook, sheet, data, cellStyle, errors, numberColumns);
            configureSheet(sheet, data, headers, errors, headersWidth, configureActiveCell);
        }
        return sheet;
    }

    /**
     * Configura la hoja de Excel con los encabezados, errores y el tamaño de las columnas.
     * - Congela la primera fila
     * - Configura el autoajuste de columnas
     * - Configura el filtro automático
     * - Configura la celda activa (si se indica desde `configureActiveCell`)
     *
     * @param sheet: Hoja de Excel a configurar
     * @param data: Datos a escribir en la hoja, se reciben para validar la cantidad de filas
     * @param headers: Encabezados de la hoja, se reciben para configurar el autoajuste
     * @param errors: Errores que se pintarán en la hoja
     * @param headersWidth: Ancho de las columnas, se reciben para configurar el autoajuste
     * @param configureActiveCell: Indica si se debe configurar la celda activa
     */
    private void configureSheet(
            final Sheet sheet,
            final DataExchangeDTO data,
            final List<String> headers,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final Map<Integer, Integer> headersWidth,
            final boolean configureActiveCell
    ) {
        if (!headers.isEmpty() && hasDataRows(sheet)) {
            // ISSUE-7621: Únicamente SI HAY DATOS, se congela y se configura el filtro automático
            sheet.createFreezePane(0, 1, sheet.getLastRowNum(), headers.size() - 1);
            sheet.setAutoFilter(new CellRangeAddress(0, sheet.getLastRowNum() , 0, headers.size() - 1));
        } else {
            sheet.createFreezePane(0, 1);
        }
        final int numberColumns = headers.size();
        //No cambiar tamaño de columnas cuando solo está el encabezado y un renglón
        if (data.getNumberLines() > 2) {
            for (int columnIndex = 0; columnIndex < numberColumns; columnIndex++) {
                sheet.autoSizeColumn(columnIndex);
                final int currentWidth = sheet.getColumnWidth(columnIndex);
                if (currentWidth < headersWidth.get(columnIndex)) {
                    sheet.setColumnWidth(columnIndex, headersWidth.get(columnIndex));
                }
            }
        }
        if (configureActiveCell) {
            if (errors != null && !errors.isEmpty()) {
                final ExcelErrorCoordinateXY firstError = new ArrayList<>(errors.keySet()).get(0);
                final Integer rowNum = firstError.getRowNum();
                final Integer columnNum = firstError.getColumnNum();
                final CellAddress cellAddress = new CellAddress(rowNum, columnNum);
                final SXSSFSheet sxSheet = (SXSSFSheet) sheet;
                sxSheet.setActiveCell(cellAddress);
                sxSheet.showInPane(rowNum, columnNum);
            } else {
                final CellAddress cellAddress = new CellAddress(1, 0);
                final SXSSFSheet sxSheet = (SXSSFSheet) sheet;
                sxSheet.setActiveCell(cellAddress);
            }
        }
    }

    private boolean hasDataRows(Sheet sheet) {
        // Skipping header at row 0
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (Cell cell : row) {
                    if (cell != null && cell.getCellType() != CellType.BLANK && !cell.toString().isEmpty()) {
                        return true; // Found a non-blank row
                    }
                }
            }
        }
        return false;
    }

    public Path writeWorkbookToFile(final String filename, final Workbook workbook) throws IOException {
        final String workbookName = normalizeFileName(filename);
        final Path tempPath = createTempFile(workbookName);
        try (final OutputStream input = Files.newOutputStream(tempPath)) {
            workbook.write(input);
            return tempPath;
        } catch (Exception e) {
            LOGGER.error("Error writing content to Excel", e);
            return null;
        }
    }

    private Path createTempFile(final String workbookName) throws IOException {
        return FileHandler.createTempFile(
                workbookName + "-" + EXCEL_WRITER,
                ".xlsx",
                fileManager.getTempFolder()
        );
    }

    public DataExchangeDTO getDefaultData(final List<String> headers) {
        final DataExchangeDTO data = new DataExchangeDTO(2);
        final List<List<String>> values = new ArrayList<>(2);
        values.add(headers);
        final List<String> defaultRow = Arrays.asList(new String[headers.size()]);
        Collections.fill(defaultRow, "");
        values.add(defaultRow);
        data.setValues(values);
        return data;
    }

    public XSSFCellStyle generatStyleHeader(final Workbook workbook) {
        final XSSFCellStyle styleHeader = (XSSFCellStyle) workbook.createCellStyle();
        styleHeader.setAlignment(HorizontalAlignment.CENTER_SELECTION);
        styleHeader.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        styleHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleHeader.setBorderBottom(BorderStyle.THICK);
        styleHeader.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styleHeader.setBorderLeft(BorderStyle.THICK);
        styleHeader.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        styleHeader.setBorderRight(BorderStyle.THICK);
        styleHeader.setRightBorderColor(IndexedColors.BLACK.getIndex());
        styleHeader.setBorderTop(BorderStyle.THICK);
        styleHeader.setTopBorderColor(IndexedColors.BLACK.getIndex());
        final Font font = workbook.createFont();
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        styleHeader.setFont(font);
        return styleHeader;
    }

    private String normalizeFileName(final String filename) {
        return filename
                .replaceAll("\\.", "-")
                .replaceAll("xlsx", "-")
                .replaceAll("xls", "-")
                .replaceAll("csv", "-")
                .replaceAll(":", "-")
                .replaceAll("\\\\", "_")
                .replaceAll("/", "_");
    }

    private Map<Integer, Integer> writeHeaders(
            final Workbook workbook,
            final Sheet sheet,
            final List<String> headers,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final Integer numberColumns
    ) {
        final Row row = sheet.createRow(0);
        final Map<Integer, Integer> headersWidth = new HashMap<>();
        for (int columnIndex = 0; columnIndex < numberColumns; columnIndex++) {
            final Cell cell = row.createCell(columnIndex, CellType.STRING);
            final XSSFCellStyle styleHeader = generatStyleHeader(workbook);
            cell.setCellStyle(styleHeader);
            final Integer width = getHeaderMinWidth(columnIndex, cell, headers);
            sheet.setColumnWidth(columnIndex, width);
            headersWidth.put(columnIndex, width);
            addCommentError(workbook, sheet, row, cell, errors, true);
        }
        return headersWidth;
    }

    private Integer getHeaderMinWidth(final Integer columnIndex, final Cell cell, final List<String> headers) {
        final String defaultIfBlank = StringUtils.defaultIfBlank(headers.get(columnIndex), "-");
        cell.setCellValue(defaultIfBlank);
        final Integer width = new Double(
                Math.floor(
                        (defaultIfBlank.length() * Units.DEFAULT_CHARACTER_WIDTH + 5) / Units.DEFAULT_CHARACTER_WIDTH * 256
                ) * 1.2
        ).intValue();
        if (width < MIN_COLUMN_WIDTH) {
            return MIN_COLUMN_WIDTH;
        } else {
            return width;            
        }
    }

    private void writSheetRecords(
            final Workbook workbook,
            final Sheet sheet,
            final DataExchangeDTO data,
            final BulkCellStyle cellStyle,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final Integer numberColumns
    ) {
        if (data.getValues().size() > 1) {
            final AtomicInteger counterRow = new AtomicInteger(0);
            final List<List<String>> rowsExceptHeader = data.getValues().subList(1, data.getNumberLines());
            rowsExceptHeader.forEach((row) -> createRow(
                    row,
                    workbook,
                    sheet,
                    cellStyle,
                    numberColumns,
                    errors,
                    counterRow.addAndGet(1)
            ));
        }
    }

    public SXSSFWorkbook createNewWorkbook() {
        final SXSSFWorkbook workbook = new SXSSFWorkbook();
        workbook.setCompressTempFiles(true);
        return workbook;
    }

    private void addCommentError(
            final Workbook workbook,
            final Sheet sheet,
            final Row row,
            final Cell cell,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final Boolean isHeader
    ) {
        if (errors == null || errors.isEmpty()) {
            return;
        }
        final ExcelErrorCoordinateXY headerKey = new ExcelErrorCoordinateXY(cell.getRowIndex(), cell.getColumnIndex());
        final String cellError = errors.get(headerKey);
        if (cellError != null && !cellError.isEmpty()) {
            final Comment cellComment = generateComment(workbook, cellError, sheet, row.getRowNum(), cell.getColumnIndex());
            cell.setCellComment(cellComment);
            if (isHeader) {
                cell.getCellStyle().setBottomBorderColor(IndexedColors.RED.getIndex());
                cell.getCellStyle().setLeftBorderColor(IndexedColors.RED.getIndex());
                cell.getCellStyle().setRightBorderColor(IndexedColors.RED.getIndex());
                cell.getCellStyle().setTopBorderColor(IndexedColors.RED.getIndex());
            } else {
                final XSSFCellStyle errorStyle = generateStyleErrror(workbook);
                cell.setCellStyle(errorStyle);
            }
        }
    }

    private void createRow(
            final List<String> rowData,
            final Workbook workbook,
            final Sheet sheet,
            final BulkCellStyle cellStyle,
            final Integer numberColumns,
            final Map<ExcelErrorCoordinateXY, String> errors,
            final Integer currentRowIndex
    ) {
        final Row row = sheet.createRow(currentRowIndex);
        for (int columnIndex = 0; columnIndex < numberColumns; columnIndex++) {
            final String valueCell = rowData.get(columnIndex);
            final Cell cell = row.createCell(columnIndex, CellType.STRING);
            final XSSFCellStyle style = generateDefaultStyle(workbook, cellStyle);
            cell.setCellStyle(style);
            if (valueCell == null || valueCell.isEmpty()) {
                cell.setBlank();
            } else if ("-".equals(valueCell)) {
                cell.setCellValue(valueCell);
            } else {
                if (Utilities.isInteger(valueCell)) {
                    if (valueCell.startsWith("0")) {
                        cell.setCellValue(valueCell);
                    } else {
                        setIntegerCell(cell, valueCell);
                    }
                } else if (NumberUtils.isCreatable(valueCell)) {
                    if (valueCell.startsWith("0") || valueCell.endsWith("L")) {
                        cell.setCellValue(valueCell);
                    } else {
                        setDoubleCell(cell, valueCell);
                    }
                } else if (Utilities.isUrl(valueCell)) {
                    setUrlCell(workbook, cell, valueCell);
                } else {
                    cell.setCellValue(valueCell);
                }
            }
            addCommentError(workbook, sheet, row, cell, errors, false);
        }
    }

    private XSSFCellStyle generateDefaultStyle(final Workbook workbook, final BulkCellStyle cellStyle) {
        final XSSFCellStyle styleCell = (XSSFCellStyle) workbook.createCellStyle();
        styleCell.setFillForegroundColor(getCustomColor(cellStyle.getBackgroundColor()));
        styleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleCell.setBorderBottom(BorderStyle.THIN);
        styleCell.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styleCell.setBorderLeft(BorderStyle.THIN);
        styleCell.setWrapText(true);
        styleCell.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        styleCell.setBorderRight(BorderStyle.THIN);
        styleCell.setRightBorderColor(IndexedColors.BLACK.getIndex());
        styleCell.setBorderTop(BorderStyle.THIN);
        styleCell.setTopBorderColor(IndexedColors.BLACK.getIndex());
        final XSSFFont fonCell = (XSSFFont) workbook.createFont();
        fonCell.setColor(getCustomColor(cellStyle.getColor()));
        styleCell.setFont(fonCell);
        styleCell.setWrapText(true);
        styleCell.setShrinkToFit(true);
        return styleCell;
    }

    private XSSFCellStyle generateStyleErrror(final Workbook workbook) {
        final XSSFCellStyle styleError = (XSSFCellStyle) workbook.createCellStyle();
        styleError.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        styleError.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleError.setBorderBottom(BorderStyle.THIN);
        styleError.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styleError.setBorderLeft(BorderStyle.THIN);
        styleError.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        styleError.setBorderRight(BorderStyle.THIN);
        styleError.setRightBorderColor(IndexedColors.BLACK.getIndex());
        styleError.setBorderTop(BorderStyle.THIN);
        styleError.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return styleError;
    }

    private void setDoubleCell(final Cell cell, final String valueCell) {
        try {
            cell.setCellValue(Double.parseDouble(valueCell));
        } catch (final Exception ex) {
            LOGGER.error("There was an error, unable to parse double {}", valueCell);
            cell.setCellValue(valueCell);
        }
    }

    private void setIntegerCell(final Cell cell, final String valueCell) {
        try {
            cell.setCellValue(Integer.parseInt(valueCell));
        } catch (final Exception e) {
            LOGGER.error("There was an error, unable to parse integer {}", valueCell);
            setDoubleCell(cell, valueCell);
        }
    }

    private void setUrlCell(final Workbook workbook, final Cell cell, final String valueCell) {
        try {
            final Hyperlink link = workbook.getCreationHelper().createHyperlink(HyperlinkType.URL);
            link.setAddress(valueCell);
            cell.setHyperlink(link);
            final CellStyle hLinkStyle = workbook.createCellStyle();
            final Font hLinkFont = workbook.createFont();
            hLinkFont.setFontName("Ariel");
            hLinkFont.setUnderline(Font.U_SINGLE);
            hLinkFont.setColor(IndexedColors.BLUE.getIndex());
            hLinkStyle.setFont(hLinkFont);
            hLinkStyle.setAlignment(HorizontalAlignment.FILL);
            cell.setCellStyle(hLinkStyle);
            cell.setCellValue(valueCell);
        } catch (final Exception e) {
            LOGGER.error("There was an error, unable to create link {}", valueCell, e);
            cell.setCellValue(valueCell);
        }
    }

    private Color parseRgb(final String color) {
        final Matcher matcher = RGB_PATTERN.matcher(color);
        if (matcher.matches()) {
            return new Color(Integer.parseInt(matcher.group(1)),
                    Integer.parseInt(matcher.group(2)),
                    Integer.parseInt(matcher.group(3)));
        }
        return null;
    }

    private Color getColorByName(final String color) {
        try {
            final Field fieldColor = Color.class.getField(color.toLowerCase());
            return (Color) fieldColor.get(null);
        } catch (final Exception e) {
            LOGGER.error("Unable to parse color {}", color);
            return null;
        }
    }

    public XSSFColor getCustomColor(final String plainColor) {
        XSSFColor customIndex = customColors.get(plainColor);
        if (customIndex == null) {
            Color color;
            if (plainColor.startsWith("rgb")) {
                color = parseRgb(plainColor);
            } else if (plainColor.startsWith("#")) {
                color = Color.decode(plainColor.replace("#", "0X"));
            } else {
                color = getColorByName(plainColor);
            }
            if (color == null) {
                return null;
            }
            final XSSFColor customColor = new XSSFColor(color, indexColor);
            customColors.put(plainColor, customColor);
            return customColor;
        } else {
            return customIndex;
        }
    }

    private Comment generateComment(
            final Workbook workbook,
            final String comment,
            final Sheet sheet,
            final Integer rowNum,
            final Integer columnNum
    ) {
        final Drawing drawing = sheet.createDrawingPatriarch();
        final CreationHelper factory = workbook.getCreationHelper();
        final ClientAnchor anchor = factory.createClientAnchor();
        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
        anchor.setCol1(columnNum);
        anchor.setCol2(columnNum + 3);
        anchor.setRow1(rowNum);
        anchor.setRow2(rowNum + 4);
        final Comment r = drawing.createCellComment(anchor);
        final RichTextString str = factory.createRichTextString(comment);
        r.setString(str);
        r.setVisible(false);
        r.setAuthor(appName);
        return r;
    }

}
