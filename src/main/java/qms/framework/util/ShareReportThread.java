package qms.framework.util;

import jakarta.servlet.ServletContext;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import mx.bnext.core.daemon.BnextThread;
import mx.bnext.core.daemon.util.IBnextThread;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.Loggable;
import org.glowroot.agent.api.Instrumentation;
import org.slf4j.Logger;

/**
 *
 * <AUTHOR>
 */
public class ShareReportThread extends BnextThread implements IBnextThread {

    private static final Logger LOGGER = Loggable.getLogger(Loggable.LOGGER.DAEMON, ShareReportThread.class);

    private String id;
    private final ShareReportTask task;
    private final ServletContext servletContext;
    private final CompletableFuture<ShareReportTask> future;

    public ShareReportThread(final ShareReportTask task, final ServletContext servletContext, final ISecurityUser admin) {
        this.task = task;
        this.servletContext = servletContext;
        this.future = new CompletableFuture<>();
    }

    @Override
    @Instrumentation.Transaction(
            timer = "background thread",
            traceHeadline = "ShareReportThread {{this.id}}",
            transactionName = "ShareReportThread",
            transactionType = "Background",
            alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
    )
    public void run() {
        initialize(servletContext);
    }

    public void initialize(final ServletContext servletContext) {
        try {
            task.call(future);
        } catch (Throwable ex) {
            if (task != null) {
                LOGGER.error("\033[31m{}/----- [1] Could not rebuild share report ----- \r\n", task.toString());
                LOGGER.error("\033[31m/----- [2] Could not rebuild share report ----- \r\n", ex);
            } else {
                LOGGER.error("\033[31m/----- Could not rebuild share report ----- \r\n", ex);
            }
        } finally {
            future.complete(task);
        }
    }

    public ServletContext getServletContext() {
        return servletContext;
    }

    public ShareReportTask getTask() {
        return task;
    }

    public CompletableFuture<ShareReportTask> getFuture() {
        return future;
    }
    
    @Override
    public void interrupt() {
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.task);
        return hash;
    }

    @Override
    @SuppressWarnings("AccessingNonPublicFieldOfAnotherObject")
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareReportThread other = (ShareReportThread) obj;
        return Objects.equals(this.task, other.getTask());
    }

    @Override
    public String getId() {
        if (id == null) {
            return String.valueOf(hashCode());
        }
        return id;
    }

    @Override
    public String toString() {
        return "ShareReportInitializerThread{" + "task=" + task + '}';
    }

}
