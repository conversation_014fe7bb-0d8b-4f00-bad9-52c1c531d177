package qms.framework.util;

import DPMS.Mapping.IAddressableUser;
import Framework.Config.Mail;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import java.util.Set;
import java.util.stream.Collectors;
import javax.mail.Message;
import qms.access.util.AddressableUserHelper;
import qms.access.util.MailSettingsHandler;
import qms.framework.core.Mailer;

public class ShareReportMailHelper extends MailHelper {

    public ShareReportMailHelper(IUntypedDAO dao) {
        super(dao);
    }
    
    public Set<Mail> getInternalRecipients(final Long shareReportId, final String module) {
        final AddressableUserHelper userHelper = new AddressableUserHelper(dao);
        final Set<IAddressableUser> users = userHelper.getInternalRecipients(shareReportId).stream()
                .filter((user) -> MailSettingsHandler.isMailingAvailableByUser(user.getId(), module, Mailer.TYPE.REPORT))
                .collect(Collectors.toSet());
        final Set<Mail> results = toMailSet(users, Message.RecipientType.BCC);
        return results;
    }
}
