package qms.framework.util;

import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.hibernate.HibernateException;
import org.hibernate.query.SemanticException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import qms.framework.dto.ElapsedDataDTO;
import qms.util.QueryHandler;
import qms.util.SQLHandler;
import qms.util.interfaces.IGridFilter;

/**
 * Clase que implementa la interfaz ISqlDAO
 * Se agrega para crear operaciones de SQL sin @Transactional y no tener conexión duplicada
 * Los métodos utilizan un pool de conexiones independiente al de Spring
 */
public class SqlDAOImpl implements ISqlDAO {
    
    private static final Logger HIBERNATE_SQL_LOGGER = LoggerFactory.getLogger("org.hibernate.SQL.HIBERNATE_SQL_LOGGER");
    private static final Logger LOGGER = Loggable.getLogger(SqlDAOImpl.class);
    private final DAOErrorHandler errorHandler = new DAOErrorHandler();
    private final SessionFilterHandler sessionHandler = new SessionFilterHandler();
    private final DaoParserHandler parserHandler = new DaoParserHandler();

    public NamedParameterJdbcTemplate getJdbcTemplate(final Connection connection) {
        final SingleConnectionDataSource dataSource = new SingleConnectionDataSource(connection, false);
        dataSource.setAutoCommit(false);
        return new NamedParameterJdbcTemplate(dataSource);
    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     *
     * @param sqlSelect        Consulta sql la cual puede tener solo el estamento del WHERE,
     *                         este método genera automaticamente la parte del SELECT utilizando
     *                         las columnas guardadas en SortedPagedFilter
     * @param connection       Database connection
     * @param sessionStatement SET statements that change the current session handling of specific information.
     * @param filter           : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * @since : 2.13.0.9
     */
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String sqlSelect,
            final IGridFilter filter,
            final Connection connection,
            final String sessionStatement
    ) {
        return SQL_getRowsByQuery(null, sqlSelect, filter, connection, sessionStatement);
    }


    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     *
     * @param sqlSelect        Consulta sql la cual puede tener solo el estamento del WHERE,
     *                         este método genera automaticamente la parte del SELECT utilizando
     *                         las columnas guardadas en SortedPagedFilter
     * @param connection       Database connection
     * @param sessionStatement SET statements that change the current session handling of specific information.
     * @param filter           : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * @since : 2.13.0.9
     */
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String withSelect,
            final String sqlSelect,
            final IGridFilter filter,
            final Connection connection,
            final String sessionStatement
    ) {
        if (!filter.canRetriveData()) {
            LOGGER.error(
                "Can't retrieve data, verify the implementation of canRetrieveData()."
            );
            return Utilities.EMPTY_GRID_INFO_NO_ACCESS;
        }
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(GenericDAOImpl.class);
        // filter is saved into HttpSession
        sessionHandler.saveWindowFilter(filter);
        final GridInfo<Map<String, Object>> info = new GridInfo<>();
        info.setGridId(filter.getGridId());
        String fullSelect = null;
        try {
            final SQLHandler sqlHandler = new SQLHandler();
            fullSelect = sqlHandler.buildValidQuery(sqlSelect, filter);
            filter.parseCriteriaMap(fullSelect, true, true);
            filter.setAsumeAlias(false);
            filter.setAsumerNumberAsDot(false);
            final Map<String, String> queryFilter = parserHandler.getMapQueryString(filter, false, false);
            final Map<String, Object> params = parserHandler.getParamsCriteria(filter);
            final NamedParameterJdbcTemplate jdbcTemplate = getJdbcTemplate(connection);
            final String queryTrimed = sqlSelect.trim().toUpperCase();
            if (filter.skipCount()) {
                info.setCount(1L);
            } else {
                Long count;
                if (queryTrimed.startsWith("SELECT") || queryTrimed.startsWith("WITH")) {
                    count = SQL_getRows_Count(withSelect, sqlSelect, params, queryFilter, jdbcTemplate);
                } else {
                    count = SQL_getRows_Count(withSelect, " SELECT c.* " + sqlSelect, params, queryFilter, jdbcTemplate);
                }
                info.setCount(count);
            }
            info.setLastSyncDate(Utilities.getNow());
            if (info.getCount() > 0) {
                final Long disableLoadThreshold = filter.getDisableLoadThreshold();
                if (disableLoadThreshold != null && disableLoadThreshold > 0 && info.getCount() > disableLoadThreshold) {
                    info.setData(new ArrayList<>(0));
                } else {
                    final String where = queryFilter.get(QueryHandler.CRITERIA_WHERE);
                    final String order = queryFilter.get(QueryHandler.CRITERIA_ORDER);
                    if (sessionStatement != null && sessionStatement.trim().isEmpty()) {
                        jdbcTemplate.getJdbcTemplate().execute(sessionStatement.trim());
                    }
                    String executeStatement = sqlHandler.paginateSQL(connection, fullSelect, where, order, filter);
                    if (withSelect != null) {
                        executeStatement = withSelect + " " + executeStatement.replace("WITH ", ", ");
                    }
                    HIBERNATE_SQL_LOGGER.debug(executeStatement);
                    final List<Map<String, Object>> rows = jdbcTemplate.queryForList(executeStatement, params);
                    info.setData(rows);
                    if (filter.skipCount()) {
                        info.setCount((long) rows.size());
                    }
                }
            } else {
                info.setData(Utilities.EMPTY_LIST);
            }
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            if (fullSelect != null) {
                errorHandler.stackTraceHandle(message + "\r\n" + fullSelect, e);
            } else {
                errorHandler.stackTraceHandle(message + "\r\n" + sqlSelect, e);
            }
        } catch (final HibernateException e) {
            if (fullSelect != null) {
                errorHandler.stackTraceHandle(fullSelect, e);
            } else {
                errorHandler.stackTraceHandle(sqlSelect, e);
            }
        } catch (final Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            if (fullSelect != null) {
                errorHandler.stackTraceHandle(message + "\r\n" + fullSelect, e);
            } else {
                /*
                 Si llegaste aquí por la siguiente excepción, probablemente te faltó el "AS" en las columnas del SQL
                   > org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL
                   > An expression of non-boolean type specified in a context where a condition is expected, near ','.
                 */
                errorHandler.stackTraceHandle(message + "\r\n" + sqlSelect, e);
            }
        } finally {
            qms.framework.util.MeasureTime.stop(tStart, "Elapsed time in SQL_getRowsByQuery [" + sqlSelect + "]");
        }
        return info;
    }

    private Long SQL_getRows_Count(
            final String withSelect,
            final String query,
            final Map<String, Object> params,
            final Map<String, String> queryFilter,
            final NamedParameterJdbcTemplate jdbcTemplate
    ) {
        SQLHandler sqlHandler = new SQLHandler();
        String executeSql = null;
        try {
            executeSql = sqlHandler.getQueryCount(withSelect, query, queryFilter);
            HIBERNATE_SQL_LOGGER.debug(executeSql);
            return jdbcTemplate.queryForObject(executeSql, params, Long.class);
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            if (executeSql == null) {
                errorHandler.stackTraceHandle(message + "\r\n" + query, e);
            } else {
                errorHandler.stackTraceHandle(message + "\r\n" + executeSql, e);
            }
        } catch (final Exception e) {
            if (executeSql == null) {
                errorHandler.stackTraceHandle("COUNT - " + query, e);
            } else {
                errorHandler.stackTraceHandle("COUNT - " + executeSql, e);
            }
        }
        return 0L;
    }

}
