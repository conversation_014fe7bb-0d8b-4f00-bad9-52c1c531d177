package qms.framework.util;

// Al agregar/actualizar cambiar también en el archivo query-column-type.js
public enum ReportColumnType {

    TEXT(1), // Texto
    DATE(2), // Fecha
    HIDDEN(3), // Oculto
    INTEGER(4),
    DOUBLE(5),
    PERCENTAJE(6),
    CATALOG_TEXT(7), // Catálogo
    DATE_TIME(8), // Fecha y hora
    CATALOG_HIERARCHY(9), // Catálogo con jerarquía
    TIME(10), // Hora
    WEEK_DAY(11), // Día de la semana
    SKIP(12), // No renderizada
    CURRENCY(13);
    
    private final Integer value;

    ReportColumnType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return this.value;
    }

    public boolean equals(Integer value) {
        return this.value.equals(value);
    }

    public static ReportColumnType getType(Integer value) {
        for (ReportColumnType t : ReportColumnType.class.getEnumConstants()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }

}
