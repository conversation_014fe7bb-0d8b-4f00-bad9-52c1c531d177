package qms.framework.util;

import java.nio.file.Path;
import java.util.Locale;
import java.util.ResourceBundle;
import org.apache.commons.io.FilenameUtils;
import qms.framework.dto.DataExchangeDTO;

public class DataExchangeParser implements IDataParser {

    private final Locale locale;
    private final ResourceBundle tags;

    public DataExchangeParser(final Locale loc) {
        locale = loc;
        tags = LocaleUtil.getI18n(DataExchangeParser.class.getCanonicalName(), loc);
    }

    private String getTag(final String key) {
        return LocaleUtil.getTag(key, tags, locale);
    }

    @Override
    public DataExchangeDTO parse(final Path file, final String originalFilename, final String contentType, final Integer page) 
            throws DataExchangeParseError {
        final String filename;
        if (originalFilename != null && !originalFilename.isEmpty()) {
            filename = originalFilename;
        } else {
            filename = file.toFile().getName();
        }
        final String ext = FilenameUtils.getExtension(filename);
        if (!ext.isEmpty()) {
            switch (ext) {
                case CsvParser.CSV_EXTENSION:
                    final CsvParser csvParser = new CsvParser(locale);
                    return csvParser.parse(file, filename, contentType, page);
                case ExcelParser.XLSX_EXTENSION:
                case ExcelParser.XLS_EXTENSION:
                    final ExcelParser excelParser = new ExcelParser(locale);
                    return excelParser.parse(file, filename, contentType, page);
                default:
                    return throwInvalidExtension(filename, ext);
            }
        } else {
            return throwNullExtension(filename);
        }
    }

    private DataExchangeDTO throwInvalidExtension(final String filename, final String ext)
            throws DataExchangeParseError {
        final String message = getTag("invalidExtension")
                .replace("{extension}", ext);
        throw new DataExchangeParseError(filename, message);
    }

    private DataExchangeDTO throwNullExtension(final String filename)
            throws DataExchangeParseError {
        final String message = getTag("nullExtension");
        throw new DataExchangeParseError(filename, message);
    }

}
