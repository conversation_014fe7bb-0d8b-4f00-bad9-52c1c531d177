package qms.framework.util;

import DPMS.Mapping.Settings;
import Framework.Config.Language;
import Framework.Config.Utilities;
import java.util.Locale;
import java.util.ResourceBundle;
import mx.bnext.core.i18n.LocaleManager;
import mx.bnext.core.util.Loggable;

/**
 *
 * <AUTHOR>
 */
public class LocaleUtil {

    private static LocaleManager manager;

    private static void initLocale() {
        if (manager == null) {
            final Settings settings = Utilities.getSettings(null);
            String lang = settings.getLang();
            String locale = settings.getLocale();
            if (lang == null || lang.isEmpty()) {
                lang = "es";
            }
            if (locale == null || locale.isEmpty()) {
                locale = "MX";
            }
            manager = new LocaleManager(lang, locale);
        }
    }
    
    public static Locale getLocale() {
        initLocale();
        return manager.getSystemLocale();
    }

    public static void reset() {
        manager = null;
    }

    public static ResourceBundle getSystemI18n(final String tagsName) {
        initLocale();
        return manager.getSystemI18n(tagsName);
    }

    public static ResourceBundle getI18n(Class<?> clazz, Locale locale) {
        Language langPath = clazz.getAnnotation(Language.class);
        String bundle;
        if (langPath != null) {
            bundle = langPath.module()[0];
        } else {
            Loggable.getLogger(LocaleUtil.class).error("Language {} IS NOT CONFIGURED", LocaleUtil.class);
            return null;
        }
        if (locale == null) {
            locale = getLocale();
        }
        return getI18n(bundle, locale);
    }

    public static ResourceBundle getI18n(String tagsName, Locale locale) {
        final LocaleManager customManager = new LocaleManager(locale.getLanguage(), locale.getCountry());
        return customManager.getI18n(tagsName, locale);
    }

    public static String getSystemTag(String key, String tagsName) {
        initLocale();
        return BusinessUnitUtil.interpolate(
            DepartmentUnitUtil.interpolate(
                AreaUnitUtil.interpolate(
                    manager.getSystemTag(key, tagsName)
                )
            )
        );
    }

    public static String getTag(String key, String tagsName, Locale locale) {
        final LocaleManager customManager = new LocaleManager(locale.getLanguage(), locale.getCountry());
        return BusinessUnitUtil.interpolate(
            DepartmentUnitUtil.interpolate(
                AreaUnitUtil.interpolate(
                    customManager.getTag(key, tagsName, locale)
                )
            )
        );
    }

    public static String getTag(String key, ResourceBundle tags) {
        initLocale();
        return BusinessUnitUtil.interpolate(
            DepartmentUnitUtil.interpolate(
                AreaUnitUtil.interpolate(
                    manager.getTag(key, tags)
                )
            )
        );
    }
    
    public static String getTag(String key, ResourceBundle tags, Locale locale) {
        final LocaleManager customManager = new LocaleManager(locale.getLanguage(), locale.getCountry());
        return BusinessUnitUtil.interpolate(
            DepartmentUnitUtil.interpolate(
                AreaUnitUtil.interpolate(
                    customManager.getTag(key, tags)
                )
            )
        );
    }
    
    public static String getTagByClass(String key, Class<?> clazz) {
        final ResourceBundle tags = getI18n(clazz.getCanonicalName(), Utilities.getLocale());
        initLocale();
        return BusinessUnitUtil.interpolate(
            DepartmentUnitUtil.interpolate(
                AreaUnitUtil.interpolate(
                    manager.getTag(key, tags)
                )
            )
        );
    }

    private LocaleUtil() {
    }
}
