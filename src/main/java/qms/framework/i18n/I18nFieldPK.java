package qms.framework.i18n;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class I18nFieldPK implements Serializable {

    private String i18nFieldId;
    private String i18nTableId;

    public I18nFieldPK() {
    }

    public I18nFieldPK(String i18nFieldId, String i18nTableId) {
        this.i18nFieldId = i18nFieldId;
        this.i18nTableId = i18nTableId;
    }

    @Basic(optional = false)
    @Column(name = "i18n_field_id")
    public String getI18nFieldId() {
        return i18nFieldId;
    }

    public void setI18nFieldId(String i18nFieldId) {
        this.i18nFieldId = i18nFieldId;
    }

    @Basic(optional = false)
    @Column(name = "i18n_table_id")
    public String getI18nTableId() {
        return i18nTableId;
    }

    public void setI18nTableId(String i18nTableId) {
        this.i18nTableId = i18nTableId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 19 * hash + Objects.hashCode(this.i18nFieldId);
        hash = 19 * hash + Objects.hashCode(this.i18nTableId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final I18nFieldPK other = (I18nFieldPK) obj;
        if (!Objects.equals(this.i18nFieldId, other.i18nFieldId)) {
            return false;
        }
        if (!Objects.equals(this.i18nTableId, other.i18nTableId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "I18nFieldPK{" + "i18nFieldId=" + i18nFieldId + ", i18nTableId=" + i18nTableId + '}';
    }
    
}
