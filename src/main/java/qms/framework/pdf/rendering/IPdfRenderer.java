package qms.framework.pdf.rendering;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.FilePdfImage;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Path;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import qms.framework.pdf.rendering.dto.PdfPageResult;
import qms.util.exception.FileWithPasswordException;

/**
 *
 * <AUTHOR>
 * @param <TYPE>
 */
public interface IPdfRenderer<TYPE> {

    PdfPageResult generatePage(
            FilePdfImage pdfPage,
            IPdfDocument<TYPE> document,
            Integer dpi,
            IFilesDAO dao
    ) throws IOException, InterruptedException;

    <TYPE> IPdfDocument<TYPE> loadDocument(@Nonnull Long fileId,@Nonnull Path content,@Nullable String password) throws FileWithPasswordException;

    BufferedImage renderPage(
            IPdfDocument<TYPE> document,
            Integer pageNum,
            Integer dpi
    ) throws IOException, InterruptedException;

    Path createTemporaryFile(Integer pageNumber, BufferedImage image) throws IOException;

}
