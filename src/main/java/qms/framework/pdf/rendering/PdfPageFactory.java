package qms.framework.pdf.rendering;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.FilePDF;
import DPMS.Mapping.FilePdfImage;
import Framework.Config.Utilities;
import com.google.common.collect.Sets;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import mx.bnext.core.daemon.util.IBnextThread;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.file.TempPath;
import mx.bnext.core.pdf.util.IPdfDaemonConfig;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.glowroot.agent.api.Instrumentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.file.FileManager;
import qms.framework.pdf.rendering.dto.PdfPageResult;
import qms.framework.util.MeasureTime;
import qms.util.exception.FileWithPasswordException;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class PdfPageFactory implements IPdfPageFactory, IBnextThread, Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(PdfPageFactory.class);

    private final IFileData fileData;
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    private final AtomicInteger numberPages = new AtomicInteger(0);
    private final Set<Long> pagesIndex = Sets.newConcurrentHashSet();
    private final PdfPageManager pdfPageManager;
    private final FileManager fileManager;
    private final IPdfDaemonConfig config;
    private final String password;

    public PdfPageFactory(final IFileData fileData, final IPdfDaemonConfig config, final String password) {
        this.fileData = fileData;
        this.config = config;
        this.pdfPageManager = new PdfPageManager();
        this.fileManager = (FileManager) config.getFileManager();
        this.password = password;
    }

    @Override
    public void submitQueue() {
    }

    @Override
    @Instrumentation.Transaction(
            timer = "background thread",
            traceHeadline = "PdfPageFactory {{this.id}} for file {{this.fileData.id}}",
            transactionName = "PdfPageFactory",
            transactionType = "Background",
            alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
    )
    public void run() {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
        try {
            dao.lockFile(fileData.getId());
            executeGeneration(dao);
        } catch (final Exception ex) {
            handleError(dao, ex);
        }
        MeasureTime.stop(tStart, "Elapsed time generating pdf images for file id " + fileData.getId());
    }

    private void handleError(final IFilesDAO dao, final Exception ex) {
        if (ex instanceof FileWithPasswordException) {
            dao.updateHasPassword(fileData.getId(), true);
            dao.updateNumberPages(fileData.getId(), 0, 0);
            dao.updateHasPdfPages(fileData.getId(), 1);
        } else {
            dao.updateNumberPages(fileData.getId(), 0, 0);
            fileData.setNumberPages(0);
            fileData.setNumberSavedPages(0);
            LOGGER.error("Fail while generating pages for pdf document with fileId:{}", fileData.getId(), ex);
        }
    }

    private void generatePdfPages(
            final List<FilePdfImage> pdfPages,
            final IPdfDocument<PDFRenderer> document,
            final Integer dpi,
            final IFilesDAO dao
    ) {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        try {
            if (stopped.get()) {
                return;
            }
            pdfPages.forEach(pdfPage -> {
                if (stopped.get()) {
                    return;
                }
                try {
                    final PdfPageResult pagePath = pdfPageManager.generatePage(pdfPage, document, dpi, dao);
                    fileManager.generatePageThumbnail(pdfPage, pagePath, dao);
                } catch (final IOException | InterruptedException ex) {
                    dao.updateHasPdfPages(fileData.getId(), 0);
                    throw new RuntimeException(ex);
                } catch (FileWithPasswordException e) {
                    dao.updateHasPassword(fileData.getId(), true);
                    dao.updateNumberPages(fileData.getId(), 0, 0);
                    dao.updateHasPdfPages(fileData.getId(), 1);
                    throw new RuntimeException(e);
                }
                pagesIndex.add(pdfPage.getId());
            });
            dao.updateHasPdfPages(fileData.getId(), 1);
        } catch (final Exception e) {
            dao.updateHasPdfPages(fileData.getId(), 0);
            throw e;
        }
    }

    /**
     * Devuelve TRUE en caso de que las banderas para generar PDF e IMÁGENES estén encendidas.
     *
     * @return
     */
    private boolean isPdfImagesEnabled() {
        return Boolean.TRUE.equals(fileData.getPdfImagesEnabled()) && Boolean.TRUE.equals(fileData.getPdfConversionEnabled());
    }

    private void executeGeneration(final IFilesDAO dao) throws IOException, InterruptedException, SQLException, FileWithPasswordException {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        if (FileUtils.isSupportedImage(fileData.getExtension())) {
            saveConvertedImage(dao);
        } else {
            startPdfPagesGeneration(dao);
        }
    }

    private void startPdfPagesGeneration(final IFilesDAO dao) throws IOException, InterruptedException, SQLException, FileWithPasswordException {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        final Path content = fileManager.getFilePdf(fileData, true, config.getSystemUser());
        try (final IPdfDocument<PDFRenderer> document = pdfPageManager.loadDocument(fileData.getId(), content, password)) {
            if (document == null) {
                LOGGER.error(
                        "Can not load PDF document with fileId: {}.",
                        new Object[]{fileData.getId()}
                );
                dao.updateNumberPages(fileData.getId(), 0, 0);
                dao.updateHasPdfPages(fileData.getId(), 1);
                return;
            }
            final FilePDF pdfHeader = dao.HQLT_findById(FilePDF.class, fileData.getId());
            final Integer dpi;
            if (FileManager.CONTEXT_TYPE_DWG.equals(fileData.getContentType())) {
                dpi = Utilities.getSettings().getDwgImageDpi();
            } else {
                dpi = Utilities.getSettings().getPdfImageDpi();
            }
            final Integer maximumPersistedPages = config.getViewerSettings().getPdfMaximumPersistedPages();
            if (maximumPersistedPages == 0) {
                return;
            }
            if (pdfHeader.getPDFImages() != null && !pdfHeader.getPDFImages().isEmpty()) {
                final Integer numberPdfImages = pdfHeader.getPDFImages().size();
                if (fileData.getNumberPages() == null || fileData.getNumberPages().equals(0)) {
                    dao.updateNumberPages(fileData.getId(), numberPdfImages, numberPdfImages);
                    fileData.setNumberPages(numberPdfImages);
                    fileData.setNumberSavedPages(numberPdfImages);
                }
                final List<FilePdfImage> pdfPages = pdfPageManager.getSortedPages(pdfHeader);
                pdfPages.stream()
                        .limit(maximumPersistedPages)
                        .forEach(page -> generatePdfPage(page, document, dpi, dao));
                final boolean generatedPages = pdfPages
                        .stream()
                        .noneMatch(page -> page.getHasImage().equals(0) || page.getHasThumbnail().equals(0));
                if (generatedPages) {
                    dao.updateHasPdfPages(fileData.getId(), 1);
                    fileData.setHasPdfPages(1);
                }
            } else {
                final Integer numberOfPages = document.getNumberOfPages();
                fileData.setNumberPages(numberOfPages);
                fileData.setNumberSavedPages(numberOfPages);
                this.numberPages.set(numberOfPages);
                pdfHeader.setNumberPages(numberOfPages);
                final Integer pagesToSave;
                if (numberOfPages > maximumPersistedPages) {
                    pagesToSave = maximumPersistedPages;
                } else {
                    pagesToSave = numberOfPages;
                }
                dao.updateNumberPages(fileData.getId(), numberOfPages, pagesToSave);
                final FilePDF savedPdfHeader = pdfPageManager.saveHeaderPdfPages(pdfHeader, pagesToSave, config.getSystemUser().getId(), dao);
                final List<FilePdfImage> pdfPages = pdfPageManager.getSortedPages(savedPdfHeader);
                if (!stopped.get()) {
                    generatePdfPages(pdfPages, document, dpi, dao);
                }
            }
        } finally {
            Files.deleteIfExists(content);
            dao.releaseFileBusy(fileData.getId());
        }
    }

    private void generatePdfPage(
        final FilePdfImage page,
        final IPdfDocument<PDFRenderer> document,
        final Integer dpi,
        final IFilesDAO dao            
    ) {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        if (page.getHasImage().equals(1) && page.getHasThumbnail().equals(1)) {
            pagesIndex.add(page.getId());
            return;
        }
        try {
            pagesIndex.add(page.getId());
            if (page.getHasImage().equals(0)) {
                final PdfPageResult pagePath = pdfPageManager.generatePage(page, document, dpi, dao);
                fileManager.generatePageThumbnail(page, pagePath, dao);
            } else {
                try (final TempPath pdfPageFile = new TempPath(fileManager.getFilePdfPage(page.getId()))) {
                    fileManager.generatePageThumbnail(page, pdfPageFile.getPath(), dao);
                }
            }
        } catch (final Exception ex) {
            LOGGER.error(
                    "Fail while generating page {} for pdf document with fileId: {}.",
                    new Object[]{page.getPage(), fileData.getId(), ex}
            );
        }
    }
    
    private void saveConvertedImage(final IFilesDAO dao) throws InterruptedException {
        if (!this.isPdfImagesEnabled()) {
            return;
        }
        // Convierte y guarda la imagen agregada
        try {
            final FilePDF pdfHeader = dao.HQLT_findById(FilePDF.class, fileData.getId());
            this.numberPages.set(1);
            // Convierte la imagen a File
            final Path pngSourceImage = fileManager.getFilePdf(fileData, true, config.getSystemUser());
            // Guarda la imagen convertida
            final FilePDF savedPdf = pdfPageManager.saveConvertedImageHeader(fileData, pdfHeader, config.getSystemUser().getId(), dao);
            if (savedPdf == null) {
                return;
            }
            // Genera Thumbnail de la imagen guardada
            final FilePdfImage pngPdfPage = savedPdf.getPDFImages().iterator().next();
            final Path pngPdfPath = pdfPageManager.saveConvertedImageContent(pngPdfPage, pngSourceImage, dao);
            fileManager.generatePageThumbnail(pngPdfPage, pngPdfPath, dao);
        } catch (final Exception ex) {
            LOGGER.error(
                    "Fail while saving image {} with fileId:{}",
                    new Object[]{fileData.getId()},
                    ex);
        }
    }

    @Override
    public void interrupt() {
    }

    public Long getFileId() {
        return this.fileData.getId();
    }

    @Override
    public String getId() {
        return this.fileData.getId().toString();
    }

    @Override
    public String getTag() {
        return getClass().getSimpleName();
    }

    @Override
    public Set<Long> getPagesIndex() {
        return pagesIndex;
    }

    @Override
    public void shutdown() {
        stopped.set(true);
    }

    @Override
    public Integer getGeneratedPagesNumber() {
        return pagesIndex.size();
    }

    @Override
    public Integer getNumberPages() {
        return numberPages.get();
    }

    @Override
    public String toString() {
        return "PdfPageFactory{"
                + "metadata=" + fileData.getId() + ","
                + " stopped=" + stopped
                + '}';
    }

}
