package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ScheduledTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String type;
    private Date runStart;
    private Date runEnd;
    private String error;

    public ScheduledTaskDTO() {
    }

    public ScheduledTaskDTO(Long id, String type, Date runStart, Date runEnd, String error) {
        this.id = id;
        this.type = type;
        this.runStart = runStart;
        this.runEnd = runEnd;
        this.error = error;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    @Temporal(TemporalType.TIMESTAMP)
    public Date getRunStart() {
        return runStart;
    }

    public void setRunStart(Date runStart) {
        this.runStart = runStart;
    }

    @Temporal(TemporalType.TIMESTAMP)
    public Date getRunEnd() {
        return runEnd;
    }

    public void setRunEnd(Date runEnd) {
        this.runEnd = runEnd;
    }
    
    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.runStart);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ScheduledTaskDTO other = (ScheduledTaskDTO) obj;
        return Objects.equals(this.runStart, other.runStart);
    }

    @Override
    public String toString() {
        return "ScheduledTaskDTO{" + "runStart=" + runStart + ", runEnd=" + runEnd + ", error=" + error + '}';
    }

}