package qms.framework.core;

import Framework.Action.SessionViewer;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableSet;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import jakarta.servlet.ServletException;
import liquibase.exception.LiquibaseException;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.form.util.FormSetup;
import qms.form.util.SurveyUtil;
import qms.framework.util.DatabaseUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
public final class DatabaseService extends SessionViewer {

    public String smd() {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return "noaccess";
        }
        return SUCCESS;
    }

    @SMDMethod
    public boolean resetDataSource()
            throws LiquibaseException, ServletException, InterruptedException, ExecutionException, TimeoutException, QMSException {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return false;
        }
        boolean result = DatabaseUtil.resetDataSource();
        if (result) {
            DatabaseUtil.resetApplicationData(getServletContext());
        }
        return result;
    }

    @SMDMethod
    public GenericSaveHandle resetFreezedSurveyAnswersMigrationRun() throws Exception {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return GenericSaveHandle.newFailure(SessionViewer.NO_ACCESS);
        }
        try {
            return FormSetup.customInitializeAnswersMigration(null, true, getLoggedUserDto());
        } catch (Exception e) {
            getLogger().error("Could not refresh migrations!", e);
            return GenericSaveHandle.newFailure(e);
        }
    }

    @SMDMethod
    public GenericSaveHandle resetFreezedSurveyAnswersRun() throws Exception {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return GenericSaveHandle.newFailure(SessionViewer.NO_ACCESS);
        }
        try {
            return FormSetup.customInitializeAnswers(null, true, getLoggedUserDto());
        } catch (Exception e) {
            getLogger().error("Could not reset freezed survey answerds run !", e);
            return GenericSaveHandle.newFailure(e);
        } finally {
            final SurveyUtil surveyUtil = new SurveyUtil();
            SurveyUtil.notifySaveAnswersFailures(getLoggedUserDto());
        }
    }

    @SMDMethod
    public GenericSaveHandle resetFreezedSurveyAnswersMigrationRun(Long surveyId) throws Exception {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return GenericSaveHandle.newFailure(SessionViewer.NO_ACCESS);
        }
        try {
            return FormSetup.customInitializeAnswersMigration(null, true, getLoggedUserDto());
        } catch (Exception e) {
            getLogger().error("Could not refresh migrations!", e);
            return GenericSaveHandle.newFailure(e);
        }
    }

    @SMDMethod
    public GenericSaveHandle resetFreezedSurveyAnswersRun(Long surveyId) throws Exception {
        if (!isAdmin()) {
            return GenericSaveHandle.newFailure(SessionViewer.NO_ACCESS);
        }
        try {
            return FormSetup.customInitializeAnswers(null, ImmutableSet.of(surveyId), getLoggedUserDto());
        } catch (Exception e) {
            getLogger().error("Could not reset freezed survey answerds run !", e);
            return GenericSaveHandle.newFailure(e);
        } finally {
            SurveyUtil.notifySaveAnswersFailures(getLoggedUserDto());
        }
    }
    
    @SMDMethod
    public boolean preloadHibernateCacheRun() throws Exception {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return false;
        }
        HibernateCacheUtil.initialize(getServletContext());
        return true;
    }

}
