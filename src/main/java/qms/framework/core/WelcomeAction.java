package qms.framework.core;

import DPMS.Mapping.FilesLite;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.opensymphony.xwork2.ActionSupport;
import java.io.IOException;
import java.sql.SQLException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.util.Loggable;
import org.apache.struts2.action.ServletResponseAware;
import org.slf4j.Logger;
import static qms.framework.core.ReportLogoAction.IMAGE_PATTERN;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
public class WelcomeAction extends ActionSupport implements ServletResponseAware {
    
    private static final Logger LOGGER = Loggable.getLogger(WelcomeAction.class);
    private HttpServletResponse response;
    
    @Override
    public String execute() throws IOException, SQLException {
        Long id = Utilities.getSettings().getWelcomeBgId();
        if (id == null) {
            return SUCCESS;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        FilesLite file = dao.HQLT_findById(FilesLite.class, id);
        if (file == null) {
            return SUCCESS;
        }
        if (!IMAGE_PATTERN.matcher(file.getContentType()).matches()) {
            LOGGER.error("Invalid content type '{}' for file with id: {}", file.getContentType(), id);
            return SUCCESS;
        }
        final FileManager fileManager = new FileManager();
        final IFileData metadata = fileManager.newMetadataInstance(file);
        fileManager.writeHeadersWithDailyCache(response, file.getDescription(), file.getContentType(), file.getContentSize());
        try (final ServletOutputStream output = response.getOutputStream()) {
            fileManager.writeFileContentToOutput(metadata, true, output, SecurityUtils.getLoggedUser());
        }
        return null;
    }
    
    @Override
    public void withServletResponse(HttpServletResponse response) {
        this.response = response;
    }

}
