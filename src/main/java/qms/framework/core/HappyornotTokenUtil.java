/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.core;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import jakarta.servlet.ServletContext;
import mx.bnext.cipher.Encryptor;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.SettingsUtil;
import qms.happyornot.dao.IHappyornotDAO;
import qms.happyornot.entity.HappyornotSurvey;
import qms.happyornot.entity.HappyornotUpdateLink;

/**
 *
 * <AUTHOR>
 */
public abstract class HappyornotTokenUtil {

    public static void initialize(final ServletContext servletContext) {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);

        List<Map<String, Object>> surveysInsecure = dao.HQL_findByQuery(" "
                + " SELECT new map("
                    + " t.id as happyornotTargetId"
                    + " , s.allowAnonymousAnswer as allowAnonymousAnswer"
                + " )"
                + " FROM " + HappyornotSurvey.class.getCanonicalName()  + " s"
                + " JOIN s.targetNames t"
                + " LEFT JOIN t.happyornotLinks l"
                + " WHERE l.id IS NULL"
        );

        Long loggedUserId = SecurityRootUtils.getFirstAdminUserId();
        String appUrl = SettingsUtil.getAppUrlNoSlash();
        String surveyUrl = appUrl + "/qms/" + Utilities.getSettings().getLang() + "/menu/legacy/" + IHappyornotDAO.SURVEY_URL + "&nh=1&nm=1&nd=1";
        surveysInsecure.forEach(target -> {
            HappyornotUpdateLink link = new HappyornotUpdateLink();
            link.setId(-1L);
            link.setCode(UUID.randomUUID().toString());
            final String token = Encryptor.encrypt(link.getCode(), Utilities.getSettings().getSystemId());
            link.setToken(token);
            if (Boolean.TRUE.equals(target.get("allowAnonymousAnswer"))) {
                link.setUrl(appUrl + IHappyornotDAO.URL_BASE_TOKEN + link.getToken());
            } else {
                link.setUrl(appUrl + surveyUrl + link.getToken());
            }
            link.setCreatedBy(loggedUserId);
            link.setLastModifiedBy(loggedUserId);
            link.setCreatedDate(new Date());
            link.setLastModifiedDate(new Date());
            link.setStatus(1);
            link.setDeleted(false);
            Long targetId = (Long) target.get("happyornotTargetId");
            link.setHappyornotTargetId(targetId);
            link.setUrlName("survey-" + link.getCode());
            dao.makePersistent(link, loggedUserId);

        });

    }
}
