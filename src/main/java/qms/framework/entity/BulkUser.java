package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.IAuditable;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.configuration.util.IBulkUser;
import qms.util.CodePrefix;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@CodePrefix("BULK-USER")
@Table(name = "bulk_user")
public class BulkUser extends StandardEntity<BulkUser> implements IAuditable, IAuditableEntity, Serializable, IBulkUser {
    private static final long serialVersionUID = 1L;

    private Integer status;
    private Integer deleted = 0;
    private String code;
    private String description;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private UserRef createdByUser;
    private UserRef lastModifiedByUser;
    
    private Date startDate;
    private Date endDate;
    private Long elapsedMinutes;
    private Long totalRecords;
    private Long sourceFileId;
    private Boolean hasSourceFile;
    private Long activeUsersCount;
    private Long inactiveUsersCount;
    private Long requireLicenseCount;

    public BulkUser() {
    }
    
    @Id
    @Column(name = "bulk_user_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            this.deleted = 0;
        } else {
            this.deleted = deleted;
        }
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Column(name = "created_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @CreatedDate
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @CreatedBy
    @Override
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "created_by", updatable = false, insertable = false)
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "last_modified_by", updatable = false, insertable = false)
    public UserRef getLastModifiedByUser() {
        return lastModifiedByUser;
    }

    public void setLastModifiedByUser(UserRef lastModifiedByUser) {
        this.lastModifiedByUser = lastModifiedByUser;
    }

    @Override
    @Column(name = "start_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Override
    @Column(name = "end_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getEndDate() {
        return endDate; 
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    @Generated(value = GenerationTime.ALWAYS)
    @Column(name = "elapsed_minutes", updatable = false, insertable = false)
    public Long getElapsedMinutes() {
        return elapsedMinutes;
    }

    public void setElapsedMinutes(Long elapsedMinutes) {
        this.elapsedMinutes = elapsedMinutes;
    }

    @Override
    @Column(name = "total_records")
    public Long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Long totalRecords) {
        this.totalRecords = totalRecords;
    }

    @Column(name = "source_file_id")
    public Long getSourceFileId() {
        return sourceFileId;
    }

    public void setSourceFileId(Long sourceFileId) {
        this.sourceFileId = sourceFileId;
    }

    @Override
    @Generated(value = GenerationTime.ALWAYS)
    @Column(name = "has_source_file", insertable = false, updatable = false)
    @Type(type = "numeric_boolean")
    public Boolean getHasSourceFile() {
        return hasSourceFile;
    }

    public void setHasSourceFile(Boolean hasSourceFile) {
        this.hasSourceFile = hasSourceFile;
    }

    @Override
    @Column(name = "active_users_count")
    public Long getActiveUsersCount() {
        return activeUsersCount;
    }

    public void setActiveUsersCount(Long activeUsersCount) {
        this.activeUsersCount = activeUsersCount;
    }

    @Override
    @Column(name = "inactive_users_count")
    public Long getInactiveUsersCount() {
        return inactiveUsersCount;
    }

    public void setInactiveUsersCount(Long inactiveUsersCount) {
        this.inactiveUsersCount = inactiveUsersCount;
    }

    @Override
    @Column(name = "require_license_count")
    public Long getRequireLicenseCount() {
        return requireLicenseCount;
    }

    public void setRequireLicenseCount(Long requireLicenseCount) {
        this.requireLicenseCount = requireLicenseCount;
    }

}
