package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.UserWithTeam;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import Framework.Config.Utilities;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.dto.GroupUserDto;
import qms.util.CodePrefix;

/**
 * Entity immutable de "Owner" destinado a tener la relación bideireccional con "Users".
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@CodePrefix("UTEAM")
@Table(name = "owner")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class OwnerTeam extends StandardEntity<OwnerTeam> implements IAuditableEntity, Serializable {

    public static enum VALUE_TYPE {
        MAIN_USERS, GLOBAL_USERS, GROUP_USERS, GROUP
    }

    
    private static final long serialVersionUID = 1L;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Set<UserWithTeam> mainUsers;
    private Set<OwnerTeamUser> users;
    private Set<UserGroup> groups;

    private Integer type;

    public OwnerTeam() {
    }
    
    public OwnerTeam(Long id) {
        this.id = id;
    }
    
    public OwnerTeam(Long id, String description) {
        this.id = id;
        this.description = description;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "owner_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }
    
    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @OneToMany(mappedBy = "mainTeamOwner", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<UserWithTeam> getMainUsers() {
        return mainUsers;
    }

    public void setMainUsers(Set<UserWithTeam> mainUsers) {
        this.mainUsers = mainUsers;
    }

    /**
     * @return the users
     */
    @JsonIgnore
    @OneToMany(mappedBy = "owner", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<OwnerTeamUser> getUsers() {
        return users;
    }

    /**
     * @param users the users to set
     */
    public void setUsers(Set<OwnerTeamUser> users) {
        this.users = users;
    }
    
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinTable(name = "user_group_owner",
        joinColumns = @JoinColumn(name = "owner_id"),
        inverseJoinColumns = @JoinColumn(name = "user_group_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<UserGroup> getGroups() {
        return groups;
    }

    public void setGroups(Set<UserGroup> groups) {
        this.groups = groups;
    }


    @Transient
    public Set<UserWithTeam> getGlobalUsers() {
        if (this.users == null) {
            return Utilities.EMPTY_SET;
        }
        return this.users
                .stream()
                .filter((u) -> u.getType() != null && OwnerTeamUser.TYPE.GLOBAL.getValue() == u.getType())
                .map((u) -> u.getUser())
                .collect(Collectors.toSet());
    }

    public void setGlobalUsers(Set<UserWithTeam> globalUsers) {
        if (globalUsers == null) {
            return;
        }
        if (this.users == null) {
            this.users = new HashSet<>(globalUsers.size());
        }
        this.users.addAll(
            globalUsers.stream().map((u) -> {
                return new OwnerTeamUser(-1L, u.getId(), null, this, OwnerTeamUser.TYPE.GLOBAL);
            }).collect(Collectors.toSet())
        );
    }

    @Transient
    public Set<GroupUserDto> getGroupUsers() {
        if (this.users == null) {
            return Utilities.EMPTY_SET;
        }
        return this.users
                .stream()
                .filter((u) -> u.getType() != null && OwnerTeamUser.TYPE.BY_GROUP.getValue() == u.getType())
                .map((u) -> new GroupUserDto(
                    u.getUser().getId(),
                    u.getGroupId(),
                    u.getUser().getBusinessUnitId(),
                    u.getUser().getBusinessUnitDepartmentId(),
                    u.getUser().getMainTeamOwnerId(),
                    u.getUser().getDescription(),
                    u.getUser().getCorreo(),
                    u.getUser().getCode(),
                    u.getUser().getCuenta(),
                    u.getUser().getBusinessUnitDepartmentName(),
                    u.getUser().getBusinessUnitName(),
                    u.getUser().getMainTeamOwnerName()
                ))
                .collect(Collectors.toSet());
    }

    public void setGroupUsers(Set<UserWithTeam> groupUsers) {
        if (groupUsers == null) {
            return;
        }
        if (this.users == null) {
            this.users = new HashSet<>(groupUsers.size());
        }
        this.users.addAll(
            groupUsers.stream().map((u) -> {
                return new OwnerTeamUser(-1L, u.getId(), u.getGroupId(), this, OwnerTeamUser.TYPE.BY_GROUP);
            }).collect(Collectors.toSet())
        );
    }
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof OwnerTeam)) {
            return false;
        }
        OwnerTeam other = (OwnerTeam) object;
        if((this.id != null && this.id == -1L) || (other.id != null && other.id == -1L)) {
            return false;
        }
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "qms.framework.entity.OwnerTeam{" + "id=" + id + ", code=" + code + ", type=" + type + '}';
    }

}
