package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "bulk_user_record")
public class BulkUserRecord extends CompositeStandardEntity<BulkUserRecordPK>
        implements IAuditableCompositeEntity<BulkUserRecordPK>, ILinkedComposityGrid<BulkUserRecordPK>, Serializable {

    private static final long serialVersionUID = 1L;
    
    private BulkUserRecordPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public BulkUserRecord() {
    }

    public BulkUserRecord(BulkUserRecordPK id) {
        this.id = id;
    }

    public BulkUserRecord(Long bulkUserId, Long bulkLoadId) {
        this.id = new BulkUserRecordPK(bulkUserId, bulkLoadId);
    }

    @Override
    public BulkUserRecordPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public BulkUserRecordPK getId() {
        return id;
    }

    @Override
    public void setId(BulkUserRecordPK id) {
        this.id = id;
    }


    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BulkUserRecord other = (BulkUserRecord) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "BulkUserRecord{" + "id=" + id + '}';
    }

}
