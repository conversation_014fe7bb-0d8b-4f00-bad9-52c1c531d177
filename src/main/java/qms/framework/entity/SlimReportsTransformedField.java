package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "slim_reports_transformed_field")
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SlimReportsTransformedField extends DomainObject implements Serializable, IAuditableEntity {

    private Date createdDate;
    private Date lastModifiedDate;
    private Integer deleted;
    private Long createdBy;
    private Long lastModifiedBy;
    private String code;
    private String description;
    private SlimReports slimReport;
    private List<SlimReportsTransformedFieldRule> rules;
    private String alias;
    private Integer order;

    public SlimReportsTransformedField() {
    }

    public SlimReportsTransformedField(SlimReportsTransformedField dup, Long id, Long lastModifiedBy, Date lastModifiedDate, String alias, Integer order) {
        this.id = id;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.code = dup.getCode();
        this.description = dup.getDescription();
        this.createdDate = dup.getCreatedDate();
        this.createdBy = dup.getCreatedBy();
        this.deleted = dup.getDeleted();
        this.slimReport = dup.getSlimReport();
        this.alias = alias;
        this.order = order;
        if (dup.getRules() == null) {
            this.rules = null;
        } else {
            this.rules = new ArrayList<>();
            dup.getRules().forEach(r -> {
                this.rules.add(new SlimReportsTransformedFieldRule(r, -1L, lastModifiedBy, lastModifiedDate));
            });

        }
    }
    @Id
    @Column(name = "slim_reports_transformed_field_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @CreatedDate
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @CreatedBy
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }


    @JSON(serialize = false)
    @JsonIgnore()
    @JoinColumn(name = "slim_report_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    public SlimReports getSlimReport() {
        return slimReport;
    }

    public void setSlimReport(SlimReports slimReport) {
        this.slimReport = slimReport;
    }


    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "transformedField", fetch = FetchType.LAZY)
    @Fetch(FetchMode.SELECT)
    public List<SlimReportsTransformedFieldRule> getRules() {
        return rules;
    }

    public void setRules(List<SlimReportsTransformedFieldRule> rules) {
        this.rules = rules;
        if (rules == null) {
            return;
        }
        this.rules.forEach((field) -> {
            field.setTransformedField(this);
        });
    }


    @Column(name = "alias")
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    @Column(name = "order_column")
    public Integer getOrder() {
        return order;
    }
    public void setOrder(Integer order) {
        this.order = order;
    }

    @Transient
    @Override
    public Integer getStatus() {
        return 1;
    }

    @Override
    public void setStatus(Integer status) {
        // empty
    }

}
