/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "slim_reports")
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
public class SlimReports extends DomainObject implements Serializable {
    
    private String documentMasterId;
    private String code;
    private String description;
    private String details;
    private Long reportId;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Date lastModifiedDate;
    private Boolean restrictRecordsByDepartment;
    private Boolean whereFillerUserParticipate;
    private Boolean deleted;
    private Boolean excelUploadEnabled = false;
    private String excelIdFields;
    private String bulkTableName;
    private Set<SlimReportsSurveyFields> surveysFields = new LinkedHashSet<>();
    private Set<SlimReportsSurveyFields> surveysFilters = new LinkedHashSet<>();
    private Set<SlimReportsSurveyFieldsFixed> surveyFieldsFixed = new LinkedHashSet<>();
    private Set<SlimReportsSurveyFieldsFixed> surveyFiltersFixed = new LinkedHashSet<>();
    private Set<SlimReportsRelatedField> relatedFields = new LinkedHashSet<>();
    private Set<SlimReportsGhostField> ghostFields = new LinkedHashSet<>();
    private Set<SlimReportsTransformedField> transformedFields = new LinkedHashSet<>();
    private Set<SlimReportsSummaryGroupField> summaryFields = new LinkedHashSet<>();
    private Boolean localTimeForDates = false;

    public SlimReports() {
    }

    @Id
    @Column(name = "slim_report_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "document_master_id")
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @CreatedDate
    @Column(name = "created_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @CreatedBy
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "report_id")
    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Column(name = "details")
    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @SQLRestriction("type = 1")
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFields> getSurveysFields() {
        return surveysFields;
    }

    public void setSurveysFields(Set<SlimReportsSurveyFields> surveysFields) {
        this.surveysFields = surveysFields;
        if (surveysFields == null) {
            return;
        }
        this.surveysFields.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @SQLRestriction("type = 2")
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFields> getSurveysFilters() {
        return surveysFilters;
    }

    public void setSurveysFilters(Set<SlimReportsSurveyFields> surveysFilters) {
        this.surveysFilters = surveysFilters;
        if (surveysFilters == null) {
            return;
        }
        this.surveysFilters.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @SQLRestriction("type = 1")
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFieldsFixed> getSurveyFieldsFixed() {
        return surveyFieldsFixed;
    }

    public void setSurveyFieldsFixed(Set<SlimReportsSurveyFieldsFixed> surveyFieldsFixed) {
        this.surveyFieldsFixed = surveyFieldsFixed;
        if (surveyFieldsFixed == null) {
            return;
        }
        this.surveyFieldsFixed.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @SQLRestriction("type = 2")
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFieldsFixed> getSurveyFiltersFixed() {
        return surveyFiltersFixed;
    }

    public void setSurveyFiltersFixed(Set<SlimReportsSurveyFieldsFixed> surveyFiltersFixed) {
        this.surveyFiltersFixed = surveyFiltersFixed;
        if (surveyFiltersFixed == null) {
            return;
        }
        this.surveyFiltersFixed.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsRelatedField> getRelatedFields() {
        return relatedFields;
    }

    public void setRelatedFields(Set<SlimReportsRelatedField> relatedFields) {
        this.relatedFields = relatedFields;
        if (relatedFields == null) {
            return;
        }
        this.relatedFields.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @JsonIgnore
    @JSON(serialize = false)
    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsGhostField> getGhostFields() {
        return ghostFields;
    }

    public void setGhostFields(Set<SlimReportsGhostField> ghostFields) {
        this.ghostFields = ghostFields;
        if (ghostFields == null) {
            return;
        }
        this.ghostFields.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @JsonIgnore
    @JSON(serialize = false)
    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    @SQLRestriction("deleted = 0")
    public Set<SlimReportsTransformedField> getTransformedFields() {
        return transformedFields;
    }

    public void setTransformedFields(Set<SlimReportsTransformedField> transformedFields) {
        this.transformedFields = transformedFields;
        if (transformedFields == null) {
            return;
        }
        this.transformedFields.forEach((field) -> {
            field.setSlimReport(this);
            if (field.getRules() != null) {
                field.getRules().forEach((rule) -> {
                    rule.setSlimReport(this);
                });
            }
        });
    }


    @JsonIgnore
    @JSON(serialize = false)
    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    @Where(clause = "deleted = 0")
    public Set<SlimReportsSummaryGroupField> getSummaryFields() {
        return summaryFields;
    }

    public void setSummaryFields(Set<SlimReportsSummaryGroupField> summaryFields) {
        this.summaryFields = summaryFields;
        if (summaryFields == null) {
            return;
        }
        this.summaryFields.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Column(name = "restrict_records_by_department")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Column(name = "where_filler_user_participate")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getWhereFillerUserParticipate() {
        return whereFillerUserParticipate;
    }

    public void setWhereFillerUserParticipate(Boolean whereFillerUserParticipate) {
        this.whereFillerUserParticipate = whereFillerUserParticipate;
    }

    @Column(name = "deleted")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Column(name = "local_time_for_dates")
    public Boolean getLocalTimeForDates() {
        return localTimeForDates;
    }


    public void setLocalTimeForDates(Boolean localTimeForDates) {
        this.localTimeForDates = localTimeForDates;
    }
    @Column(name = "excel_upload_enabled")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getExcelUploadEnabled() {
        return excelUploadEnabled;
    }

    public void setExcelUploadEnabled(Boolean excelUploadEnabled) {
        this.excelUploadEnabled = excelUploadEnabled;
    }

    @Column(name = "excel_id_fields")
    public String getExcelIdFields() {
        return excelIdFields;
    }

    public void setExcelIdFields(String excelIdFields) {
        this.excelIdFields = excelIdFields;
    }


    @Column(name = "bulk_table_name")
    public String getBulkTableName() {
        return bulkTableName;
    }

    public void setBulkTableName(String bulkTableName) {
        this.bulkTableName = bulkTableName;
    }
}
