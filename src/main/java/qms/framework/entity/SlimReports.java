/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "slim_reports")
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
public class SlimReports extends DomainObject implements Serializable {
    
    private String documentMasterId;
    private String code;
    private String description;
    private String details;
    private Long reportId;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Date lastModifiedDate;
    private Boolean restrictRecordsByDepartment;
    private Boolean whereFillerUserParticipate;
    private Boolean deleted;
    private Set<SlimReportsSurveyFields> surveysFields = new HashSet<>();
    private Set<SlimReportsSurveyFieldsFixed> surveyFieldsFixed = new HashSet<>();
    private Set<SlimReportsRelatedField> relatedFields = new HashSet<>();
    private Boolean localTimeForDates = false;

    public SlimReports() {
    }

    @Id
    @Column(name = "slim_report_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "document_master_id")
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "created_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "report_id")
    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Column(name = "details")
    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFields> getSurveysFields() {
        return surveysFields;
    }

    public void setSurveysFields(Set<SlimReportsSurveyFields> surveysFields) {
        this.surveysFields = surveysFields;
        if (surveysFields == null) {
            return;
        }
        this.surveysFields.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsSurveyFieldsFixed> getSurveyFieldsFixed() {
        return surveyFieldsFixed;
    }

    public void setSurveyFieldsFixed(Set<SlimReportsSurveyFieldsFixed> surveyFieldsFixed) {
        this.surveyFieldsFixed = surveyFieldsFixed;
        if (surveyFieldsFixed == null) {
            return;
        }
        this.surveyFieldsFixed.forEach((field) -> {
            field.setSlimReport(this);
        });
    }

    @Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, mappedBy = "slimReport")
    public Set<SlimReportsRelatedField> getRelatedFields() {
        return relatedFields;
    }

    public void setRelatedFields(Set<SlimReportsRelatedField> relatedFields) {
        this.relatedFields.forEach((field) -> {
            field.setSlimReport(this);
        });
        this.relatedFields = relatedFields;
    }

    @Column(name = "restrict_records_by_department")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Column(name = "where_filler_user_participate")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getWhereFillerUserParticipate() {
        return whereFillerUserParticipate;
    }

    public void setWhereFillerUserParticipate(Boolean whereFillerUserParticipate) {
        this.whereFillerUserParticipate = whereFillerUserParticipate;
    }

    @Column(name = "deleted")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Column(name = "local_time_for_dates")
    public Boolean getLocalTimeForDates() {
        return localTimeForDates;
    }

    public void setLocalTimeForDates(Boolean localTimeForDates) {
        this.localTimeForDates = localTimeForDates;
    }
}
