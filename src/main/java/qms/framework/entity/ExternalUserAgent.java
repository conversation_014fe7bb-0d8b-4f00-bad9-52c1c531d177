package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "external_user_agent")
public class ExternalUserAgent extends CompositeStandardEntity<ExternalUserAgentPK> 
        implements Serializable, ILinkedComposityGrid<ExternalUserAgentPK> {

    private static final long serialVersionUID = 1L;
    
    private ExternalUserAgentPK id;
    private UserAgent userAgent;

    public ExternalUserAgent() {
    }

    public ExternalUserAgent(ExternalUserAgentPK id) {
        this.id = id;
    }

    public ExternalUserAgent(Long documentTypeId, Long userAgentId) {
        this.id = new ExternalUserAgentPK(documentTypeId, userAgentId);
    }

    @Override
    public ExternalUserAgentPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ExternalUserAgentPK getId() {
        return id;
    }

    @Override
    public void setId(ExternalUserAgentPK id) {
        this.id = id;
    }

    /**
     * @return the userAgent
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_agent_id", insertable = false, updatable = false)
    public UserAgent getUserAgent() {
        return userAgent;
    }

    /**
     * @param userAgent the userAgent to set
     */
    public void setUserAgent(UserAgent userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ExternalUserAgent other = (ExternalUserAgent) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "DocumentTypeUserAgent{" + "id=" + id + '}';
    }

}
