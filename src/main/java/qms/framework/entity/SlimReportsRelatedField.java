package qms.framework.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import qms.framework.util.ISlimReportField;

@Table(name = "slim_rep_related_field")
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SlimReportsRelatedField extends DomainObject implements Serializable, ISlimReportField {
    
    private Long fieldId;
    private String fieldName;
    private SlimReports slimReport;
    
    public SlimReportsRelatedField() {
    }

    public SlimReportsRelatedField(Long id, Long fieldId, String fieldName, SlimReports slimReport) {
        this.id = id;
        this.fieldId = fieldId;
        this.fieldName = fieldName;
        this.slimReport = slimReport;
    }
    
    @Id
    @Column(name = "slim_rep_related_field_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "field_id")
    @Override
    public Long getFieldId() {
        return fieldId;
    }

    @Override
    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @Column(name = "field_name")
    @Override
    public String getFieldName() {
        return fieldName;
    }

    @Override
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    @JoinColumn(name = "slim_report_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    @Override
    public SlimReports getSlimReport() {
        return slimReport;
    }

    @Override
    public void setSlimReport(SlimReports slimReport) {
        this.slimReport = slimReport;
    }

}
