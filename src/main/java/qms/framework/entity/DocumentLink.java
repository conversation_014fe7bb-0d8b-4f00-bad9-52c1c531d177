package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.document.entity.ShareDocument;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "document_link")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class DocumentLink extends StandardEntity<DocumentLink> implements IAuditableEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public static enum TYPE {
        SHARED_LINK(1,"/view/v-document-share-link"),
        EXTERNAL_LINK(2,"/view/v-document-external-link"),
        VIEWER_LINK(3,"/view/v-document-viewer-link"),
        FORM_LINK(4,"/view/v-request-survey-read"),
        FORM_SERVICE(5,"/view/viewfinder-form-link");
        
        
        private final Integer value;
        private final String action;

        private TYPE(Integer value, String action) {
            this.value = value;
            this.action = action;
        }

        public Integer getValue() {
            return this.value;
        }
        
        public String getActionName() {
            return this.action;
        }
        
        public String getAction() {
            return this.action + ".view";
        }
    }

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private Long documentId;
    private Long downloads = 0L;
    private Long views = 0L;
    private Date expiration;
    
    private Long shareDocumentId;
    private ShareDocument shareDocument;

    public DocumentLink() {
    }

    public DocumentLink(Long id) {
        this.id = id;
    }

    @Id
    @Column(name = "document_link_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Column(name = "document_id", updatable = false)
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }
    
    @Column(name = "share_document_id", updatable = false, insertable = false)
    public Long getShareDocumentId() {
        return shareDocumentId;
    }

    public void setShareDocumentId(Long shareDocumentId) {
        this.shareDocumentId = shareDocumentId;
    }
    
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "share_document_id")
    public ShareDocument getShareDocument() {
        return shareDocument;
    }

    public void setShareDocument(ShareDocument shareDocument) {
        this.shareDocument = shareDocument;
    }

    @Column(name = "downloads", updatable = false)
    public Long getDownloads() {
        return downloads;
    }

    public void setDownloads(Long downloads) {
        this.downloads = downloads;
    }
    
    @Column(name = "views", updatable = false)
    public Long getViews() {
        return views;
    }

    public void setViews(Long views) {
        this.views = views;
    }


    @Column(name = "expiration", updatable = false)
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getExpiration() {
        return expiration;
    }

    public void setExpiration(Date expiration) {
        this.expiration = expiration;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof DocumentLink)) {
            return false;
        }
        DocumentLink other = (DocumentLink) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DocumentLink{" + "code=" + code + '}';
    }

}
