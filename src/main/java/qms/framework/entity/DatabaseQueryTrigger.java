package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "database_query_trigger")
public class DatabaseQueryTrigger extends CompositeStandardEntity<DatabaseQueryTriggerPK> 
        implements Serializable, ILinkedComposityGrid<DatabaseQueryTriggerPK> {

    private static final long serialVersionUID = 1L;
    
    private DatabaseQueryTriggerPK id;
    private DatabaseQuery query;
    private SystemEventEntity systemEvent;

    public DatabaseQueryTrigger() {
    }

    public DatabaseQueryTrigger(DatabaseQueryTriggerPK id) {
        this.id = id;
    }

    public DatabaseQueryTrigger(Long databaseQueryId, Long systemEventId) {
        this.id = new DatabaseQueryTriggerPK(databaseQueryId, systemEventId);
    }

    @Override
    public DatabaseQueryTriggerPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public DatabaseQueryTriggerPK getId() {
        return id;
    }

    @Override
    public void setId(DatabaseQueryTriggerPK id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "database_query_id", insertable = false, updatable = false)
    public DatabaseQuery getQuery() {
        return query;
    }

    public void setQuery(DatabaseQuery query) {
        this.query = query;
    }

    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "system_event_id", insertable = false, updatable = false)
    public SystemEventEntity getSystemEvent() {
        return systemEvent;
    }

    public void setSystemEvent(SystemEventEntity systemEvent) {
        this.systemEvent = systemEvent;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DatabaseQueryTrigger other = (DatabaseQueryTrigger) obj;
        return Objects.equals(this.id, other.getId());
    }

    @Override
    public String toString() {
        return "DatabaseQueryTrigger{" + "id=" + id + '}';
    }

}
