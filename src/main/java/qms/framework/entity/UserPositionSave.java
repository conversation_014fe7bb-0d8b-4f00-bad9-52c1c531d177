package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "usuario_puesto")
public class UserPositionSave extends CompositeStandardEntity<UserPositionPK> 
        implements Serializable, ILinkedComposityGrid<UserPositionPK> {

    private static final long serialVersionUID = 1L;

    private UserPositionPK id;

    public UserPositionSave() {
    }

    public UserPositionSave(UserPositionPK id) {
        this.id = id;
    }

    public UserPositionSave(Long userId, Long positionId) {
        this.id = new UserPositionPK(userId, positionId);
    }

    @Override
    public UserPositionPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public UserPositionPK getId() {
        return id;
    }

    @Override
    public void setId(UserPositionPK id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final UserPositionSave other = (UserPositionSave) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.framework.entity.UserPositionSave{" + "id=" + id + '}';
    }

}
