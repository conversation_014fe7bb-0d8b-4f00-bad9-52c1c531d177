package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Cacheable;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.framework.enums.AggregateFunctionType;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.IDescription;
import qms.util.interfaces.IPersistableCode;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "slim_reports_summary_group_field")
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SlimReportsSummaryGroupField extends DomainObject implements Serializable, IAuditableEntity, IPersistableCode, IDescription {

    private Date createdDate;
    private Date lastModifiedDate;
    private Integer deleted;
    private Integer order;
    private Long createdBy;
    private Long lastModifiedBy;
    private Integer aggregateFunctionForElseValue;
    private Integer aggregateFunctionForResultValue;
    private SlimReports slimReport;
    private String alias;
    private String code;
    private String description;
    private String groupByFieldName;
    private String groupByColumnName;
    private String groupValueFieldName;
    private String groupByColumnNameType;
    private String groupByFieldNameType;


    public SlimReportsSummaryGroupField() {
    }

    public SlimReportsSummaryGroupField(SlimReportsSummaryGroupField dup, Long id, Long lastModifiedBy, Date lastModifiedDate, String alias, Integer order) {
        this.id = id;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.code = dup.getCode();
        this.description = dup.getDescription();
        this.createdDate = dup.getCreatedDate();
        this.createdBy = dup.getCreatedBy();
        this.deleted = dup.getDeleted();
        this.slimReport = dup.getSlimReport();
        this.groupByColumnName = dup.getGroupByColumnName();
        this.groupByFieldName = dup.getGroupByFieldName();
        this.groupValueFieldName = dup.getGroupValueFieldName();
        this.aggregateFunctionForElseValue = dup.getAggregateFunctionForElseValue();
        this.aggregateFunctionForResultValue = dup.getAggregateFunctionForResultValue();
        this.groupByFieldNameType = dup.getGroupByFieldNameType();
        this.groupByColumnNameType = dup.getGroupByColumnNameType();
        this.alias = alias;
        this.order = order;
    }
    @Id
    @Column(name = "slim_reports_summary_group_field_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 10)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @CreatedDate
    @Column(name = "created_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @CreatedBy
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }


    @JSON(serialize = false)
    @JsonIgnore()
    @JoinColumn(name = "slim_report_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    public SlimReports getSlimReport() {
        return slimReport;
    }

    public void setSlimReport(SlimReports slimReport) {
        this.slimReport = slimReport;
    }


    @Column(name = "alias")
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    @Column(name = "order_column")
    public Integer getOrder() {
        return order;
    }
    public void setOrder(Integer order) {
        this.order = order;
    }

    @Transient
    @Override
    public Integer getStatus() {
        return 1;
    }

    @Override
    public void setStatus(Integer status) {
        // empty
    }

    @Column(name = "group_value_field_name", length = 128)
    public String getGroupValueFieldName() {
        return groupValueFieldName;
    }

    public void setGroupValueFieldName(String groupValueFieldName) {
        this.groupValueFieldName = groupValueFieldName;
    }

    @Column(name = "group_by_field_name", length = 128)
    public String getGroupByFieldName() {
        return groupByFieldName;
    }

    public void setGroupByFieldName(String groupByFieldName) {
        this.groupByFieldName = groupByFieldName;
    }

    @Column(name = "group_by_column_name", length = 128)
    public String getGroupByColumnName() {
        return groupByColumnName;
    }

    public void setGroupByColumnName(String groupByColumnName) {
        this.groupByColumnName = groupByColumnName;
    }

    @Column(name = "aggregate_function_for_result")
    public Integer getAggregateFunctionForResultValue() {
        return aggregateFunctionForResultValue;
    }

    public void setAggregateFunctionForResultValue(Integer aggregateFunctionForResult) {
        this.aggregateFunctionForResultValue = aggregateFunctionForResult;
    }

    @Column(name = "aggregate_function_for_else")
    public Integer getAggregateFunctionForElseValue() {
        return aggregateFunctionForElseValue;
    }

    public void setAggregateFunctionForElseValue(Integer aggregateFunctionForElse) {
        this.aggregateFunctionForElseValue = aggregateFunctionForElse;
    }

    @Column(name = "group_by_column_name_type")
    public String getGroupByColumnNameType() {
        return groupByColumnNameType;
    }

    public void setGroupByColumnNameType(String groupByColumnNameType) {
        this.groupByColumnNameType = groupByColumnNameType;
    }

    @Column(name = "group_by_field_name_type")
    public String getGroupByFieldNameType() {
        return groupByFieldNameType;
    }

    public void setGroupByFieldNameType(String groupByFieldNameType) {
        this.groupByFieldNameType = groupByFieldNameType;
    }

    // region [transients]
    @Transient
    public String getAggregateFunctionForResult() {
        if (aggregateFunctionForResultValue == null) {
            return null;
        }
        AggregateFunctionType fn = AggregateFunctionType.fromValue(aggregateFunctionForResultValue);
        if (fn == null) {
            return null;
        }
        return fn.name();
    }

    @Transient
    public String getAggregateFunctionForElse() {
        if (aggregateFunctionForElseValue == null) {
            return null;
        }
        AggregateFunctionType fn = AggregateFunctionType.fromValue(aggregateFunctionForElseValue);
        if (fn == null) {
            return null;
        }
        return fn.name();
    }

    public void setAggregateFunctionForResult(String aggregateFunctionForResult) {
        AggregateFunctionType fn = AggregateFunctionType.fromValue(aggregateFunctionForResult);
        if (fn == null) {
            return;
        }
        this.aggregateFunctionForResultValue = fn.getValue();
    }

    public void setAggregateFunctionForElse(String aggregateFunctionForElse) {
        AggregateFunctionType fn = AggregateFunctionType.fromValue(aggregateFunctionForElse);
        if (fn == null) {
            return;
        }
        this.aggregateFunctionForElseValue = fn.getValue();
    }
    // endregion
}
