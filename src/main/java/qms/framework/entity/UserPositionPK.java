package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class UserPositionPK implements Serializable {

    private Long userId;
    private Long positionId;

    public UserPositionPK(Long userId, Long positionId) {
        this.userId = userId;
        this.positionId = positionId;
    }

    public UserPositionPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "usuario_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "puesto_id")
    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 37 * hash + Objects.hashCode(this.userId);
        hash = 37 * hash + Objects.hashCode(this.positionId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final UserPositionPK other = (UserPositionPK) obj;
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        if (!Objects.equals(this.positionId, other.positionId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "UserPositionPK{" + "userId=" + userId + ", positionId=" + positionId + '}';
    }

}
