package qms.framework.daemon;

import Framework.Config.Utilities;
import java.time.Instant;
import java.util.Date;
import java.util.EnumMap;
import java.util.concurrent.ScheduledFuture;
import javax.annotation.Nonnull;
import mx.bnext.core.daemon.util.IBnextExecutor;
import mx.bnext.core.daemon.util.IDaemonConfig;
import mx.bnext.core.util.Loggable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import qms.access.dto.ILoggedUser;
import qms.framework.dto.MainSchedulerTypeDto;
import qms.framework.enums.MainSchedulerType;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.ExceptionUtils;
import qms.util.ReflectionUtil;

/**
 *
 * <AUTHOR> Flores
 */
public class MainScheduler extends Loggable implements IBnextExecutor<IDaemonConfig> {

    private IDaemonConfig config;

    private ThreadPoolTaskScheduler taskScheduler;
    private final MainSchedulerTypeDto every5mCronTrigger;
    private final MainSchedulerTypeDto every15mCronTrigger;
    private final MainSchedulerTypeDto threeTimesCronTrigger;
    private final MainSchedulerTypeDto hourlyCronTrigger;
    private final MainSchedulerTypeDto dailyCronTrigger;
    private final EnumMap<MainSchedulerType, ScheduledFuture<?>> scheduledTasks = new EnumMap<>(MainSchedulerType.class);

    @Override
    public void start() {
        try {
            shutdown();
        } catch (Exception e) {
            getLogger(LOGGER.DAEMON).error(" -- Could not shutdown scheduler. Error: {}", ExceptionUtils.getRootCauseMessage(e));
        }
        try {
            taskScheduler = new ThreadPoolTaskScheduler();
            scheduledTasks.clear();
            taskScheduler.setThreadNamePrefix(getFactoryId() + "-");
            taskScheduler.initialize();
            for (MainSchedulerType type : MainSchedulerType.values()) {
                MainSchedulerTypeDto triggerConfig = getTriggerConfig(type);
                if (triggerConfig == null) {
                    if (getLogger(LOGGER.DAEMON).isDebugEnabled()) {
                        getLogger(LOGGER.DAEMON).debug(" -- Unsupported scheduling type: {}.", type);
                    }
                    continue;
                }
                if (getLogger(LOGGER.DAEMON).isInfoEnabled()) {
                    getLogger(LOGGER.DAEMON).info(
                            " -- Scheduling {} task with cron expression {}.",
                            type,
                            triggerConfig.getTrigger().getExpression()
                    );
                }
                final ScheduledFuture<?> scheduled = taskScheduler.schedule(
                        triggerConfig.getThread(), triggerConfig.getTrigger()
                );
                scheduledTasks.put(type, scheduled);
            }
        } catch (Exception e) {
            getLogger(LOGGER.DAEMON).error(" -- Could not schedule daily task. Error: {}", ExceptionUtils.getRootCauseMessage(e));
        }
    }

    public void execute(MainSchedulerType type, @Nonnull ILoggedUser loggedUser) {
        try {
            if (SchedulerLockUtils.isLocked(type)) {
                getLogger().error("Already executing {} task", type);
                return;
            }
            switch (type) {
                case EVERY_FIVE_MINUTES:
                    new Every5MThread().run();
                    break;
                case EVERY_FIVETEEN_MINUTES:
                    new Every15MThread().run();
                    break;
                case THREE_TIMES_A_DAY:
                    new ThreeTimesThread().run();
                    break;
                case HOURLY:
                    new HourlyThread().run();
                    break;
                case DAILY:
                    new DailyThread().run();
                    break;
            }
        } catch (Exception e) {
            getLogger(LOGGER.DAEMON).error(" -- Could not execute {} task. Error: {}", new Object[]{
                type, ExceptionUtils.getRootCauseMessage(e)
            });
            getLogger(LOGGER.DAEMON).error(" -- Task failed.", e);
        }
    }

    public MainScheduler() {
        every5mCronTrigger = new MainSchedulerTypeDto(
                new CronTrigger(MainSchedulerType.EVERY_FIVE_MINUTES.getCronTriggerExp()),
                new Every5MThread()
        );
        every15mCronTrigger = new MainSchedulerTypeDto(
                new CronTrigger(MainSchedulerType.EVERY_FIVETEEN_MINUTES.getCronTriggerExp()),
                new Every15MThread()
        );
        threeTimesCronTrigger = new MainSchedulerTypeDto(
                new CronTrigger(MainSchedulerType.THREE_TIMES_A_DAY.getCronTriggerExp()),
                new ThreeTimesThread()
        );
        hourlyCronTrigger = new MainSchedulerTypeDto(
                new CronTrigger(MainSchedulerType.HOURLY.getCronTriggerExp()),
                new HourlyThread()
        );
        dailyCronTrigger = new MainSchedulerTypeDto(
                new CronTrigger(MainSchedulerType.DAILY.getCronTriggerExp()),
                new DailyThread()
        );
    }

    @Override
    public void shutdown() {
        try {
            if (!scheduledTasks.isEmpty()) {
                scheduledTasks.values().forEach((task) -> task.cancel(true));
            }
        } catch (Exception e) {
            getLogger(LOGGER.DAEMON).error(" -- Could not shutdown task. Error: {}", ExceptionUtils.getRootCauseMessage(e));
        }
        if (taskScheduler != null) {
            taskScheduler.destroy();
        }
    }

    @Override
    public void logInfoExecutor() {
        for (final MainSchedulerType type : MainSchedulerType.values()) {
            logInfoExecutor(type);
        }
    }

    public void logInfoExecutor(MainSchedulerType type) {
        MainSchedulerTypeDto triggerConfig = getTriggerConfig(type);
        if (taskScheduler != null && triggerConfig != null) {
            final String nextSchedule = Utilities.formatDateWithTime(getScheduledExecutionTime(type));
            getLogger(LOGGER.DAEMON).info(
                    "{} Active tasks: [{}], next execution: [{}]",
                    type,
                    taskScheduler.getScheduledThreadPoolExecutor().getQueue().size(),
                    nextSchedule);
        } else {
            getLogger(LOGGER.DAEMON).error("{} is: [not running]", type);
        }
    }

    private MainSchedulerTypeDto getTriggerConfig(MainSchedulerType type) {
        switch (type) {
            case EVERY_FIVE_MINUTES:
                return every5mCronTrigger;
            case EVERY_FIVETEEN_MINUTES:
                return every15mCronTrigger;
            case THREE_TIMES_A_DAY:
                return threeTimesCronTrigger;
            case HOURLY:
                return hourlyCronTrigger;
            case DAILY:
                return dailyCronTrigger;
            default:
                return null;
        }
    }
    
    public static void  initailize() {
        try {
            final ILoggedUser admin = SecurityRootUtils.getFirstAdminDto();
            final MainScheduler tasks = BnextDaemonUtil.getRunningInstance(MainScheduler.class);
            if (SchedulerLockUtils.isLocked(MainSchedulerType.DAILY)) {
                Loggable.getLogger(MainScheduler.class).error("Already executing daily task");
                return;
            }
            tasks.execute(MainSchedulerType.DAILY, admin);
        } catch(final Exception ex) {
            Loggable.getLogger(MainScheduler.class).error("Failed to rebuild.", ex);
        }
    }
    
    @Override
    public void setConfig(final IDaemonConfig config) {
        this.config = config;
    }

    @Override
    public IDaemonConfig getConfig() {
        return this.config;
    }

    @Override
    public Boolean updateConfig() {
        logInfoExecutor();
        return true;
    }

    public Integer getTaskCounts() {
        return taskScheduler.getActiveCount();
    }
    
    public Date getScheduledExecutionTime(final MainSchedulerType type) {
        if (!scheduledTasks.containsKey(type)) {
            return null;
        }
        final ScheduledFuture<?> future = scheduledTasks.get(type);
        final Instant value = ReflectionUtil.getValue(future, "scheduledExecutionTime");
        return Date.from(value);
    }

    @Override
    public String getFactoryId() {
        return config.getSettings().getSystemId() + "-" + DaemonThread.MAIN_SCHEDULER.getName()
                + "-" + config.getBuildInfo().getBuildVersion();
    }

}
