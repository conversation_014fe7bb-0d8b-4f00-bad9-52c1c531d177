package qms.framework.daemon;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.io.Serializable;
import java.util.Date;
import mx.bnext.core.daemon.BnextThread;
import mx.bnext.core.daemon.util.IBnextThread;
import org.glowroot.agent.api.Instrumentation;
import qms.access.dto.ILoggedUser;
import qms.framework.entity.ScheduledTask;
import qms.framework.enums.MainSchedulerType;
import qms.framework.rest.SecurityRootUtils;

/**
 *
 * <AUTHOR>
 */
public class HourlyThread extends BnextThread implements Runnable, IBnextThread, Serializable {

    private static final String REFRESH_END_DATE = ""
            + " UPDATE " + ScheduledTask.class.getCanonicalName() + " c "
            + " SET c.runEnd = :now"
            + " WHERE"
            + " c.id = :dailyTask "
            + " AND c.runEnd is null";

    private static final MainSchedulerType TYPE = MainSchedulerType.HOURLY;

    @Override
    @Instrumentation.Transaction(
            timer = "background thread",
            traceHeadline = "HourlyThread {{this.id}}",
            transactionName = "HourlyThread",
            transactionType = "Background",
            alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
    )
    public void run() {
        if (SchedulerLockUtils.isLocked(TYPE)) {
            getLogger().error("Already executing hourly task");
            return;
        }
        Object lock = SchedulerLockUtils.lock(TYPE);
        if (lock == null) {
            getLogger().error("Already executing hourly task");
            return;
        }
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final ILoggedUser admin = SecurityRootUtils.getFirstAdminDto();
        ScheduledTask task = new ScheduledTask(-1L);
        try {
            task.setRunStart(new Date());
            final Integer result = dao.getBean(IScheduledTaskBean.class).fireHourlyTask(admin);
            getLogger().debug("Hourly task fired: {}", result);
            task.setType(TYPE.getValue());

            task = dao.makePersistent(task, admin.getId());

            logExecutor();

            task.setRunEnd(new Date());
            int c = dao.HQL_updateByQuery(
                    REFRESH_END_DATE, 
                    ImmutableMap.of("dailyTask", task.getId(), "now", new Date())
            );
            if (c != 1) {
                getLogger(LOGGER.DAEMON).info("Daily task id '{}' updated {} registries endDate (should be one).", new Object[]{
                    task.getId(), c
                });
            }
            SchedulerLockUtils.unlock(TYPE);
        } catch(final Exception ex) {
            SchedulerLockUtils.unlock(TYPE);
            BnextDaemonUtil.configureSendMailError(task, ex);
            dao.makePersistent(task, admin.getId());
            logExecutor();
        }
    }

    private void logExecutor() {
        final MainScheduler runningInstance = BnextDaemonUtil.getRunningInstance(MainScheduler.class);
        if (runningInstance == null) {
            getLogger().error("HourlyThread executor not running");
        } else {
            runningInstance.logInfoExecutor(MainSchedulerType.HOURLY);
    }
    }

    @Override
    public String getId() {
        return TYPE.toString();
    }

    @Override
    public void interrupt() {
    }
    

}
