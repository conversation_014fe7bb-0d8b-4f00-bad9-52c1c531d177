package qms.framework.daemon;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.io.Serializable;
import java.util.Date;
import mx.bnext.core.daemon.BnextThread;
import mx.bnext.core.daemon.util.IBnextThread;
import org.glowroot.agent.api.Instrumentation;
import qms.access.dto.ILoggedUser;
import qms.framework.core.GenericMonitor;
import qms.framework.entity.ScheduledTask;
import qms.framework.enums.MainSchedulerType;
import qms.framework.rest.SecurityRootUtils;

/**
 * <AUTHOR>
 */
public class ThreeTimesThread extends BnextThread implements Runnable, IBnextThread, Serializable {

    private static final MainSchedulerType TYPE = MainSchedulerType.THREE_TIMES_A_DAY;

    @Override
    @Instrumentation.Transaction(
            timer = "background thread",
            traceHeadline = "ThreeTimesThread {{this.id}}",
            transactionName = "ThreeTimesThread",
            transactionType = "Background",
            alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
    )
    public void run() {
        if (SchedulerLockUtils.isLocked(TYPE)) {
            getLogger().error("Already executing three times task");
            return;
        }
        Object lock = SchedulerLockUtils.lock(TYPE);
        if (lock == null) {
            getLogger().error("Already executing three times task");
            return;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        ScheduledTask task = null;
        final ILoggedUser admin = SecurityRootUtils.getFirstAdminDto();
        try {
            task = saveStart(dao);
            /**
             * ToDo: Consultar API y ejecutar envio de notificación de "Actividades cerradas"
             *
             * MaximoMailer mailer = new MaximoMailer(dao);
             * mailer.notifyClosedActivities(ticket, activity);
             *
             * */
            getLogger().debug("-> MAXIMO -> THREE_TIMES_A_DAY! {}", new Date());
            new GenericMonitor().onThreeTimesADayTaskFired(admin);
            saveEnd(dao, task);
            SchedulerLockUtils.unlock(TYPE);
        } catch (final Exception ex) {
            SchedulerLockUtils.unlock(TYPE);
            BnextDaemonUtil.configureSendMailError(task, ex);
            dao.makePersistent(task, admin.getId());
            logExecutor();
        }

    }

    private ScheduledTask saveStart(IUntypedDAO dao) {
        ScheduledTask task = new ScheduledTask(-1L);
        task.setRunStart(new Date());
        task.setType(TYPE.getValue());
        final ILoggedUser admin = SecurityRootUtils.getFirstAdminDto();
        return dao.makePersistent(task, admin.getId());
    }

    private void saveEnd(IUntypedDAO dao, ScheduledTask task) {
        if (task == null) {
            return;
        }
        logExecutor();
        task.setRunEnd(new Date());
        final ILoggedUser admin = SecurityRootUtils.getFirstAdminDto();
        dao.makePersistent(task, admin.getId());
    }

    private void logExecutor() {
        final MainScheduler runningInstance = BnextDaemonUtil.getRunningInstance(MainScheduler.class);
        if (runningInstance == null) {
            getLogger().error("ThreeTimesThread executor not running");
        } else {
            runningInstance.logInfoExecutor(TYPE);
        }
    }

    @Override
    public String getId() {
        return TYPE.toString();
    }

    @Override
    public void interrupt() {
    }


}
