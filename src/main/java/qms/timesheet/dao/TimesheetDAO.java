/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.timesheet.dao;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.Files;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import bnext.dto.FileDataDto;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.TextStringBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.activity.entity.Activity;
import qms.activity.util.CommitmentTask;
import qms.framework.entity.SystemLink;
import qms.framework.entity.TagRegistry;
import qms.framework.entity.TagRegistryTimesheet;
import qms.framework.util.CacheRegion;
import qms.framework.util.DateRangeUtil;
import qms.planner.entity.Planner;
import qms.timesheet.dto.ITimesheetDto;
import qms.timesheet.dto.ITimesheetFileDataDto;
import qms.timesheet.dto.ITimesheetFileDataEntityDto;
import qms.timesheet.dto.ITimesheetFileDataIdDto;
import qms.timesheet.dto.TimesheetDto;
import qms.timesheet.dto.TimesheetWithStopwatchDto;
import qms.timesheet.entity.TaskCategory;
import qms.timesheet.entity.TaskDeliveryType;
import qms.timesheet.entity.Timesheet;
import qms.timesheet.entity.TimesheetActivity;
import qms.timesheet.entity.TimesheetEvent;
import qms.timesheet.entity.TimesheetFile;
import qms.timesheet.enums.FillType;
import qms.timesheet.util.ITimesheetEntity;
import qms.util.BindUtil;
import qms.util.EntityCommon;
import qms.util.GridFilter;
import qms.util.GridOrderBy;
import qms.util.QMSException;
import qms.util.dto.MultiFileDTO;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@Repository(value = "TimesheetDAO")
@Scope(value = "singleton")
public class TimesheetDAO extends GenericDAOImpl<Timesheet, Long> implements ITimesheetDAO {

    private static final String SUM_MAIN_ACTIVITY_ACTUAL_HOURS_HQL = " "
            + " SELECT sum((t.workedMinutes*1.0)/60)  "
            + " FROM " + Timesheet.class.getCanonicalName() + " t"
            + " WHERE t.activityPlannedId IN ( "
                + " SELECT act.id "
                + " FROM " + Activity.class.getCanonicalName() + " act"
                + " WHERE act.recurrenceId = :activityId"
            + " )"
            + " AND t.deleted = 0"
            + " AND ("
                + " t.hasEnableStopwatch IS NULL"
                + " OR t.hasEnableStopwatch = false "
            + " )";

    private static final String SUM_ACTUAL_HOURS_PLANNER_HQL = " "
            + " SELECT sum((t.workedMinutes*1.0)/60) "
            + " FROM " + Timesheet.class.getCanonicalName() + " t"
            + " WHERE t.plannerId = :plannerId"
            + " AND t.deleted = 0";

    private static final String SUM_ACTUAL_HOURS_TASK_HQL = " "
            + " SELECT sum((t.workedMinutes*1.0)/60) "
            + " FROM " + TimesheetActivity.class.getCanonicalName() + " ta"
            + " JOIN " + Timesheet.class.getCanonicalName() + " t"
            + " ON ta.id.timesheetId = t.id"
            + " WHERE ta.id.activityId = :taskId"
            + " AND t.deleted = 0";

    private static final String SUM_ACTUAL_HOURS_ACTIVITY_HQL = " "
            + " SELECT sum((t.workedMinutes*1.0)/60) "
            + " FROM " + Timesheet.class.getCanonicalName() + " t"
            + " WHERE t.activityPlannedId = :activityId"
            + " AND t.deleted = 0";

    private static final String TASK_HOURS_DATA_SELECT_HQL = " "
            + " SELECT new map("
                + " client.description AS client_description "
                + ", client.id AS client_id "
                + ", p.id AS planner_id "
                + ", planner.description AS planner_description "
                + ", task.id AS task_id "
                + ", task.description AS task_description "
                + ", task.actualHours AS task_actualHours "
                + ", SUM(((entity.workedMinutes*1.00)/60)) AS range_workedHours "
            + " ) "
            + " FROM " + Timesheet.class.getCanonicalName() + " entity "
            + " LEFT JOIN " + TimesheetActivity.class.getCanonicalName() + " ta"
                + " ON ta.id.timesheetId = entity.id"
            + " LEFT JOIN  " + Activity.class.getCanonicalName() + " task"
                + " ON task.id = ta.id.activityId"
            + " LEFT JOIN  " + Planner.class.getCanonicalName() + " p "
                + " ON p.id = entity.plannerId"
            + " LEFT JOIN " + Activity.class.getCanonicalName() + " ac"
                + " ON ac.id = p.activityId" + " "
            + " LEFT JOIN ac.events planner"
            + " LEFT JOIN p.client client "
            + " WHERE <condition>"
            + " AND entity.timesheetDate BETWEEN :filterStartDate AND :filterEndDate"
            + " GROUP BY "
                + " client.id"
                + ", client.description"
                + ", p.id"
                + ", planner.description"
                + ", task.id"
                + ", task.description "
                + ", task.actualHours";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TimesheetWithStopwatchDto> isTimewatcherRunning(Long userId) {
        return HQL_findByQuery(" "
                        + " SELECT new " + TimesheetWithStopwatchDto.class.getCanonicalName() + " ("
                        + " t.id,"
                        + " t.hasEnableStopwatch,"
                        + " t.timesheetDate"
                        + ") "
                        + "FROM " + Timesheet.class.getCanonicalName() + " t "
                        + " WHERE t.userId = :userId"
                        + " AND t.hasEnableStopwatch = 1",
                ImmutableMap.of("userId", userId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public @Nonnull ResponseEntity save(
            final Long pendingRecordId,
            final TimesheetDto dto,
            final ILoggedUser loggedUser
    ) throws QMSException {
        List<TimesheetWithStopwatchDto> hasStopwatchRunning = isTimewatcherRunning(loggedUser.getId());
        if (hasStopwatchRunning.isEmpty() || !dto.getHasEnableStopwatch()) {
            final Date timeLimitToModifyTimesheet = ITimesheetDAO.getTimeLimitToModifyTimesheet();

            if (timeLimitToModifyTimesheet.after(dto.getDate())) {
                return ResponseEntity.status(HttpStatus.CONFLICT).body("outof-limit");
            }
            ITimesheetEntity registry = persistEntity(pendingRecordId, dto, loggedUser);

            if (registry == null) {
                return ResponseEntity.status(HttpStatus.CONFLICT).body("dates-spliced");
            }
            boolean isNew = dto.getTimesheetId() == null || Objects.equals(dto.getTimesheetId(), -1L) || Objects.equals(dto.getTimesheetId(), 0L);
            if (isNew) {
                dto.setTimesheetId(registry.getId());
                if (registry.getPendingRecordId() != null && registry.getActivityPlannedId() != null) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("activityPlannedId", registry.getActivityPlannedId());
                    params.put("timesheetId", registry.getId());
                    HQL_updateByQuery( " "
                                    + " UPDATE " + TimesheetActivity.class.getCanonicalName()
                                    + " SET activityPlannedId = :activityPlannedId"
                                    + " WHERE id.timesheetId = :timesheetId",
                            params,
                            true,
                            CacheRegion.TIMESHEET,
                            0
                    );
                }
            }
            TimesheetDto result = ITimesheetDAO.getTimesheetDtoFromEntity(registry, dto);
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(
                    ImmutableMap.of("stopwatch_running_message", STOPWATCH_RUNNING, "registry", hasStopwatchRunning)
            );
        }
    }

    private ITimesheetEntity persistEntity(
        Long pendingRecordId,
        TimesheetDto dto,
        ILoggedUser loggedUser
    ) throws QMSException {
        ITimesheetEntity registry;
        if (dto.getTimesheetId() != null) {
            Timesheet ref = HQLT_findById(Timesheet.class, dto.getTimesheetId(), true, CacheRegion.TIMESHEET, 0);
            String tagValues = StringUtils.join(dto.getTags(), " ");
            registry = update(dto, loggedUser, ref, tagValues);
        } else {
            final Timesheet register = persistNewRecord(pendingRecordId, dto, loggedUser);
            if (register == null) {
                return null;
            }
            setFileDataDto(register);
            registry = register;
        }
        if (registry == null) {
            return null;
        }
        if (dto.getTags() != null && !dto.getTags().isEmpty()) {
            final Set<String> tags = dto.getTags().stream()
                .filter(t -> t != null && !t.trim().isEmpty())
                .map(String::trim)
                .collect(Collectors.toSet())
            ;
            // Se obtienent tags existentes para crear los faltantes
            List<TagRegistry> savedTags = HQLT_findByQuery(
                    TagRegistry.class, ""
                    + " SELECT t "
                    + " FROM " + TagRegistry.class.getCanonicalName() + " t "
                    + " WHERE t.code IN (:tags)",
                    ImmutableMap.of("tags", tags),
                    true,
                    CacheRegion.TIMESHEET,
                    0
            );
            if (tags.size() != savedTags.size()) {
                // Se crean/agregan los que faltan
                saveTags(tags, savedTags, loggedUser);
            }
            // Se elimina relación y se guarda nueva
            final Long timesheetId = registry.getId();
            Map<String, Object> param = ImmutableMap.of("timesheetId", timesheetId);
            HQL_updateByQuery(""
                + " DELETE FROM " + TagRegistryTimesheet.class.getCanonicalName() + " t "
                + " WHERE t.id.timesheetId = :timesheetId", param, true, CacheRegion.TIMESHEET, 0
            );
            // Se guarda relación de TS con TAGS
            savedTags.forEach((tag) -> makePersistent(
                    new TagRegistryTimesheet(tag.getId(), timesheetId), loggedUser.getId()
            ));
        }
        return registry;
    }

    private Timesheet persistNewRecord(
        Long pendingRecordId,
        ITimesheetDto dto,
        ILoggedUser loggedUser
    ) throws QMSException {
        final boolean planned = pendingRecordId != null;
        if (!planned && !loggedUser.getServices().contains(ProfileServices.TS_REGISTER_UNPLANNED)) {
            throw new RuntimeException("Restricted, you need permission to create unplanned registries");
        }
        // Validación para registros empalmados
        if (validateSplicedDates(dto, loggedUser.getId())) {
            return null;
        }
        final String code = getTodaysCode(loggedUser.getAccount());
        Timesheet registry = ITimesheetDAO.getTimesheetFromDto(dto, code, planned, loggedUser);
        final boolean isNew = registry.getId() == null || registry.getId().equals(-1L);
        final Integer stage = isNew ? TimesheetFile.STAGE.CREATION.getValue() : TimesheetFile.STAGE.MODIFICATION.getValue();
        updatePlannedActivity(registry);
        registry.setStopwatchLocalTimeStart(dto.getStopwatchLocalTimeStart());
        registry = makePersistent(registry, loggedUser.getId());
        super.saveLinkedItems(
                TimesheetFile.class,
                registry.getId(), 
                dto.getFileIds(),
                true,
                stage,
                true,
                CacheRegion.TIMESHEET,
                0,
                loggedUser.getId()
        );
        persistTimesheetActivity(dto, registry, TimesheetFile.STAGE.CREATION, loggedUser);
        return registry;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void persistTimesheetActivity(
            final ITimesheetDto dto,
            final Timesheet registry,
            final TimesheetFile.STAGE stage,
            final ILoggedUser loggedUser
    ) {
        if (dto.getClientId() != null) {
            //Para validar si existe un timesheet activity y que no tenga activityId para el escenario de stopwatch desde pendientes
            //con solo cliente o proyecto
            
            TimesheetActivity timesheetAct = HQLT_findSimple(TimesheetActivity.class, ""
                    + "SELECT new " + TimesheetActivity.class.getCanonicalName() + " ( "
                        + "ta.id.timesheetId,"
                        + " ta.id.activityId,"
                        + " ta.activityPlannedId "
                    + " )  "
                    + " FROM " + TimesheetActivity.class.getCanonicalName() + " ta "
                    + " WHERE ta.id.timesheetId = :timesheetId",
                    ImmutableMap.of("timesheetId", registry.getId()),
                    true,
                    CacheRegion.TIMESHEET,
                    30
            );
            
            if (timesheetAct != null && timesheetAct.getId().getActivityId() == null) {
                HQL_updateByQuery(""
                        + "UPDATE " + TimesheetActivity.class.getCanonicalName() + " ta "
                        + " SET ta.id.activityId = :activityId "
                        + " WHERE ta.id.timesheetId = :timesheetId",
                        ImmutableMap.of("activityId", dto.getActivityId(), "timesheetId", registry.getId()));
            } else {
                saveLinkedItems(
                        TimesheetActivity.class,
                        registry.getId(),
                        new ArrayList<>(Collections.singletonList(dto.getActivityId())),
                        true,
                        stage.getValue(),
                        true,
                        CacheRegion.TIMESHEET,
                        0,
                        loggedUser.getId()
                );
                Map<String, Object> props = new HashMap<>();
                props.put("activityPlannedId", dto.getActivityPlannedId());
                HQL_updateByQuery(
                        TimesheetActivity.class,
                        props,
                        loggedUser,
                        registry.getId(),
                        "id.timesheetId",
                        "entity",
                        "",
                        true,
                        CacheRegion.TIMESHEET,
                        0
                );
            }
        } else {
            saveLinkedItems(
                    TimesheetActivity.class,
                    registry.getId(),
                    new ArrayList<>(1),
                    true,
                    stage.getValue(),
                    true,
                    CacheRegion.TIMESHEET,
                    0,
                    loggedUser.getId()
            );
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean updatePlannedActivity(final Timesheet registry) {
        final boolean planned = registry.getPendingRecordId() != null;
        if (planned) {
            if (registry.getActivityPlannedId() != null) {
                Boolean isActivityPlanned =  HQLT_findSimple(
                        Boolean.class, ""
                        + " SELECT "
                            + " c.isPlanned"
                        + " FROM " + Activity.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.id = :activityId",
                        ImmutableMap.of("activityId", registry.getActivityPlannedId()),
                        true,
                        CacheRegion.ACTIVITY,
                        0
                );
                if (isActivityPlanned) {
                    registry.setFillType(FillType.PLANNED.getValue());
                } else {
                    registry.setFillType(FillType.UNPLANNED.getValue());
                }
            }
            return true;
        }
        return false;
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Boolean validateSplicedDates(ITimesheetDto dto, Long userLogged) {
        Date timesheetDate = Utilities.truncDate(dto.getDate());
        String filterEdit = " ";
         
        final Map<String, Object> params = new HashMap<>(2);
        params.put("startTime", dto.getStart());
        params.put("endTime", dto.getEnd());
        params.put("timesheetDate", timesheetDate);
        params.put("userId", userLogged);
        if (dto.getTimesheetId() != null) {
            params.put("timesheetId", dto.getTimesheetId());
            filterEdit = " AND t.id != :timesheetId ";
        }
        List<Long> result = HQLT_findByQuery(
                Long.class, ""
                + " SELECT t.id "
                + " FROM " + Timesheet.class.getCanonicalName() + " t "
                + " WHERE "
                    + " t.userId = :userId " 
                    + " AND t.deleted = 0 "
                    + " AND CAST(t.timesheetDate AS Date) = :timesheetDate "
                    + filterEdit
                    + """
                    AND (
                        ( truncate(t.timesheetStart, second) < :endTime AND truncate(t.timesheetEnd, second) > :startTime)
                        OR ( truncate(t.timesheetStart, second) = :startTime AND truncate(t.timesheetEnd, second) = :endTime )
                        OR ( truncate(t.timesheetStart, second) = :startTime)
                    )
                    """,
                params,
                true,
                CacheRegion.TIMESHEET,
                0
        );
        return !result.isEmpty();
    }
    
    private Timesheet update(
        ITimesheetDto dto, 
        ILoggedUser loggedUser,
        Timesheet registryTime, 
        String tags
    ){
        // Validación para registros empalmados
        if (validateSplicedDates(dto, registryTime.getUserId())) {
            return null;
        }
        
        registryTime.setTimesheetDate(dto.getDate());
        registryTime.setTimesheetStart(dto.getStart());
        registryTime.setTimesheetEnd(dto.getEnd());
        registryTime.setDescription(dto.getRegistry());
        registryTime.setTags(tags);
        registryTime.setPlannerId(dto.getPlannerId());
        registryTime.setSystemLinkId(dto.getSystemLinkId());
        registryTime.setSystemLinkValue(dto.getSystemLinkValue());
        registryTime.setActivityPlannedId(dto.getActivityPlannedId());
        registryTime.setUid(dto.getUid());
        if (dto.getPendingRecordId() != null && dto.getPendingRecordId() == 0) {
            dto.setPendingRecordId(null);
        }
        registryTime.setPendingRecordId(dto.getPendingRecordId());
        if (dto.getActivityPlannedId() == null) {
            registryTime.setRecordLocked(false);
            registryTime.setFillType(FillType.UNPLANNED.getValue());
        } else {
            registryTime.setRecordLocked(dto.getTaskRecordLocked() && dto.getClientRecordLocked() && dto.getPlannerRecordLocked());
            registryTime.setFillType(FillType.PLANNED.getValue());
        }
        registryTime.setHasEnableStopwatch(dto.getHasEnableStopwatch());
        if (dto.getHasEnableStopwatch()) {
            registryTime.setStopwatchLocalTimeStart(dto.getStopwatchLocalTimeStart());
        }
        registryTime = makePersistent(registryTime, loggedUser.getId(), false);
        registryTime.setStopwatchReStarted(registryTime.getHasEnableStopwatch());
        
        // Actualizamos el registro de activity ID
        if (dto.getActivityId() != null) {
            persistTimesheetActivity(dto, registryTime, TimesheetFile.STAGE.MODIFICATION, loggedUser);
            final Map<String, Object> params = new HashMap<>();
            params.put("lastModifiedDate", new Date());
            HQL_updateByQuery(
                    Timesheet.class, 
                    params, 
                    loggedUser.getId(), 
                    registryTime.getId(),
                    true,
                    CacheRegion.TIMESHEET,
                    0,
                    null
            );
        }
        if (dto.getFileIds() != null && !dto.getFileIds().isEmpty()) {
            super.saveLinkedItems(
                    TimesheetFile.class,
                    registryTime.getId(), 
                    new ArrayList<>(dto.getFileIds()), 
                    true, 
                    TimesheetFile.STAGE.MODIFICATION.getValue(), 
                    true,
                    CacheRegion.TIMESHEET,
                    0,
                    loggedUser.getId()
            );
        } else {
            super.saveLinkedItems(
                    TimesheetFile.class,
                    registryTime.getId(), 
                    new ArrayList<>(), 
                    true, 
                    TimesheetFile.STAGE.MODIFICATION.getValue(), 
                    true,
                    CacheRegion.TIMESHEET,
                    0,
                    loggedUser.getId()
            );
            
        }
        setFileDataDto(registryTime);
        return registryTime;
    }

    private void saveTags(Set<String> newTags, List<TagRegistry> savedTags, ILoggedUser loggedUser) {
        newTags.stream()
                .filter((t -> savedTags.stream().noneMatch((TagRegistry st) -> Objects.equals(t.toUpperCase(), st.getCode().toUpperCase()))))
                .forEach((tag) -> {
            TagRegistry t = new TagRegistry(-1L);
            t.setCode(tag);
            t.setDescription(tag);
            savedTags.add(
                makePersistent(t, loggedUser.getId())
            );
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Double getActualHoursPlanner(final Long plannerId) {
        Double actualHours = HQLT_findSimple(
                Double.class,
                SUM_ACTUAL_HOURS_PLANNER_HQL,
                ImmutableMap.of("plannerId", plannerId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (actualHours == null) {
            actualHours = 0.0;
        }
        if (actualHours < 0) {
            getLogger().error(
                    "Planner with id {} has {} negative hours. Value must be positive.",
                    plannerId,
                    actualHours
            );
        }
        return actualHours;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Double getActualHoursTask(final Long taskId) {
        Double actualHours = HQLT_findSimple(
                Double.class,
                SUM_ACTUAL_HOURS_TASK_HQL,
                ImmutableMap.of("taskId", taskId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (actualHours == null) {
            actualHours = 0.0;
        }
        if (actualHours < 0) {
            getLogger().error(
                    "Task with id {} has {} negative hours. Value must be positive.",
                    taskId,
                    actualHours
            );
    }
        return actualHours;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Double getMainActivityActualHours(Long activityId) {
        if (activityId == null) {
            return 0.0;
        }

        Double actualHours = HQLT_findSimple(
                Double.class,
                SUM_MAIN_ACTIVITY_ACTUAL_HOURS_HQL,
                ImmutableMap.of("activityId", activityId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (actualHours == null) {
            actualHours = 0.0;
        }
        if (actualHours < 0) {
            getLogger().error(
                    "Main Activity with id {} has {} negative hours. Value must be positive.",
                    activityId,
                    actualHours
            );
        }
        return actualHours;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Double getActualHoursActivity(Long activityPlannedId) {
        Double actualHours = HQLT_findSimple(
                Double.class,
                SUM_ACTUAL_HOURS_ACTIVITY_HQL,
                ImmutableMap.of("activityId", activityPlannedId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (actualHours == null) {
            actualHours = 0.0;
        }
        if (actualHours < 0) {
            getLogger().error("Activity with id {} has {} negative hours. Value must be positive.", activityPlannedId, actualHours);
        }
        return actualHours;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTimesheetByPlannedActivities(final GridFilter filter, final List<Long> activityIds, final Date start, final Date end) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("t.timesheetStart");
            filter.setDirection((byte) 1);
        }
        return HQL_getRows(new StringBuilder(""
                + " SELECT new map("
                    + " t.activityPlannedId AS activityPlannedId,"
                    + " actPlanned.description AS activityDescription,"
                    + " (t.workedMinutes*1.0)/60 AS workedHours,"
                    + " t.timesheetDate AS timesheetDate,"
                    + " t.timesheetStart AS timesheetStart,"
                    + " t.timesheetEnd AS timesheetEnd,"
                    + " t.description AS description,"
                    + " u.description AS userDescription,"
                    + " client.id AS clientId,"
                    + " client.description AS clientDescription,"
                    + " planner.id AS plannerId,"
                    + " plannerAct.description AS plannerDescription,"
                    + " task.id AS taskId,"
                    + " task.description AS taskDescription"
                + " )" 
                + " FROM " + Timesheet.class.getCanonicalName() + " t"
                + " LEFT JOIN " + Activity.class.getCanonicalName() + " actPlanned"
                    + " ON actPlanned.id = t.activityPlannedId"
                + " LEFT JOIN " + TimesheetActivity.class.getCanonicalName() + " ta"
                    + " ON ta.id.timesheetId = t.id"
                + " LEFT JOIN  " + Activity.class.getCanonicalName() + " task "
                    + " ON task.id = ta.id.activityId"
                + " LEFT JOIN  " + Planner.class.getCanonicalName() + " planner "
                    + " ON planner.id = t.plannerId"
                + " LEFT JOIN planner.activity plannerAct"
                + " LEFT JOIN planner.client client"
                + " LEFT JOIN  " + User.class.getCanonicalName() + " u "
                    + " ON u.id = t.userId"
                + " WHERE " + BindUtil.parseFilterList("t.activityPlannedId", activityIds)
                + " AND t.deleted = 0"
                + " AND t.timesheetDate >= '" + Utilities.formatDateBy(start, "yyyy-MM-dd") + "'"
                + " AND t.timesheetDate <= '" + Utilities.formatDateBy(end, "yyyy-MM-dd") + "'"),
                filter,
                true, CacheRegion.TIMESHEET,
                0
        );
    }
    
    

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public String getTodaysCode(String userAccount) throws QMSException {
        final String code = userAccount + "-" + Utilities.todayDateBy("yyyy");
        final ICodeSequenceDAO dao = getBean(ICodeSequenceDAO.class);
        return (
            code + "-" + dao.next(
                TimesheetDAO.class.getSimpleName() + "." + code
            )
        ).toUpperCase();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity saveTaskDeliveryType(TaskDeliveryType taskDeliveryType, ILoggedUser loggedUser) throws QMSException {
        if (taskDeliveryType == null || taskDeliveryType.getDescription() == null || taskDeliveryType.getBusinessUnitDepartments()== null) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        
        final List<Long> businessUnitIds = taskDeliveryType.getBusinessUnitDepartments().stream().map((b) -> b.getId()).collect(Collectors.toList());
        final Map<String, Boolean> repeated = new HashMap<>(2);
        final boolean isNew = taskDeliveryType.getId() == null || taskDeliveryType.getId().equals(-1L);
        StringBuilder hql = new StringBuilder(800);
        // Si la descripción está repetida, agrega "description = true"
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(TaskDeliveryType.class.getCanonicalName()).append(" c "
            + " JOIN c.businessUnitDepartments bu "
            + " WHERE "
                + " bu.businessUnitId IN (:businessUnitIds)"
                + " AND lower(c.description) = :description");
        if (!isNew) {
            hql.append(" AND c.id != ").append(taskDeliveryType.getId());
        }
        if (HQL_findSimpleInteger(hql.toString(), 
                ImmutableMap.of(
                    "businessUnitIds", businessUnitIds,
                    "description", taskDeliveryType.getDescription().trim().toLowerCase()
                ),
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
        ) > 0) {
            repeated.put("description", true);
        }
        // Si la clave está repetida, agrega "code = true"
        hql = new StringBuilder(800);
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(TaskDeliveryType.class.getCanonicalName()).append(" c "
            + " WHERE lower(c.code) = :code");
        if (!isNew) {
            hql.append(" AND c.id != ").append(taskDeliveryType.getId());
        }
        if (taskDeliveryType.getCode() != null && HQL_findSimpleInteger(hql.toString(),
                ImmutableMap.of("code", taskDeliveryType.getCode().trim().toLowerCase()),
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
        ) > 0) {
            repeated.put("code", true);
        }
        if (Boolean.TRUE.equals(repeated.get("description")) || Boolean.TRUE.equals(repeated.get("code"))) {
            return new ResponseEntity(new Gson().toJson(repeated), HttpStatus.ALREADY_REPORTED);
        }
        try {
            if (isNew) {
                if (taskDeliveryType.getCode() == null) {
                    taskDeliveryType.setCode(
                        EntityCommon.getPrefixNextCode(taskDeliveryType.getClass())
                    );
                }
                taskDeliveryType.setId(-1L);
                if (makePersistent(taskDeliveryType, loggedUser.getId()) == null) {
                    return new ResponseEntity<>(HttpStatus.CONFLICT);
                }
                return new ResponseEntity<>(HttpStatus.CREATED);
            } else {
                if (taskDeliveryType.getGenerateCode()) {
                    taskDeliveryType.setCode(EntityCommon.getPrefixNextCode(taskDeliveryType.getClass()));
                }
                if (makePersistent(taskDeliveryType, loggedUser.getId()) == null) {
                    return new ResponseEntity<>(HttpStatus.CONFLICT);
                }
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: {}", Utilities.getSerializedObj(taskDeliveryType));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity<>("exist_record", HttpStatus.CONFLICT);
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity saveTaskCategory(TaskCategory taskCategory, ILoggedUser loggedUser) throws QMSException {
        if (taskCategory == null || taskCategory.getDescription() == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        final Map<String, Boolean> repeated = new HashMap<>(2);
        final boolean isNew = taskCategory.getId() == null || taskCategory.getId().equals(-1L);
        StringBuilder hql = new StringBuilder(800);
        // Si la descripción está repetida, agrega "description = true"
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(TaskCategory.class.getCanonicalName()).append(" c "
            + " WHERE "
                + " lower(c.description) = :description");
        if (!isNew) {
            hql.append(" AND c.id != ").append(taskCategory.getId());
        }
        if (HQL_findSimpleInteger(hql.toString(), ImmutableMap.of(
            "description", taskCategory.getDescription().trim().toLowerCase()
        )) > 0) {
            repeated.put("description", true);
        }
        // Si la clave está repetida, agrega "code = true"
        hql = new StringBuilder(800);
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(TaskCategory.class.getCanonicalName()).append(" c "
            + " WHERE lower(c.code) = :code");
        if (!isNew) {
            hql.append(" AND c.id != ").append(taskCategory.getId());
        }
        if (taskCategory.getCode() != null && HQL_findSimpleInteger(hql.toString(), 
                ImmutableMap.of("code", taskCategory.getCode().trim().toLowerCase()),
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
        ) > 0) {
            repeated.put("code", true);
        }
        if (Boolean.TRUE.equals(repeated.get("description")) || Boolean.TRUE.equals(repeated.get("code"))) {
            return new ResponseEntity<>(new Gson().toJson(repeated), HttpStatus.ALREADY_REPORTED);
        }
        try {
            if (isNew) {
                if (taskCategory.getCode() == null) {
                    taskCategory.setCode(
                        EntityCommon.getPrefixNextCode(taskCategory.getClass())
                    );
                }
                taskCategory.setId(-1L);
                if (makePersistent(taskCategory, loggedUser.getId()) == null) {
                    return new ResponseEntity<>(HttpStatus.CONFLICT);
                }
                return new ResponseEntity<>(HttpStatus.CREATED);
            } else {
                if (taskCategory.getGenerateCode()) {
                    taskCategory.setCode(EntityCommon.getPrefixNextCode(taskCategory.getClass()));
                }
                if (makePersistent(taskCategory, loggedUser.getId()) == null) {
                    return new ResponseEntity<>(HttpStatus.CONFLICT);
                }
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("Save task failed with error: DataIntegrityViolationException with: {}", Utilities.getSerializedObj(taskCategory));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity<>("exist_record", HttpStatus.CONFLICT);
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> changePlannerId(final Long activityPlannedId, final Long newPlannerId, final ILoggedUser loggedUser) {
        final List<Long> timesheetIds = HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + Timesheet.class.getCanonicalName() + " c"
                + " WHERE c.activityPlannedId = :activityPlannedId",
                ImmutableMap.of("activityPlannedId", activityPlannedId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        final Map<String, Object> params = new HashMap<>(3);
        params.put("newPlannerId", newPlannerId);
        params.put("loggedUserId", loggedUser.getId());
        params.put("activityPlannedId", activityPlannedId);
        HQL_updateByQuery(""
            + " UPDATE " + Timesheet.class.getCanonicalName()
            + " SET plannerId = :newPlannerId"
            + " ,lastModifiedDate = CURRENT_TIMESTAMP "
            + " ,lastModifiedBy = :loggedUserId"
            + " WHERE activityPlannedId = :activityPlannedId",
            params,
            true,
            CacheRegion.TIMESHEET,
            0
        );
        HQL_updateByQuery(""
            + " UPDATE " + Timesheet.class.getCanonicalName()
            + " SET lastModifiedDate = CURRENT_TIMESTAMP "
            + " ,lastModifiedBy = :loggedUserId"
            + " WHERE id IN ("
                + " SELECT c.id.timesheetId"
                + " FROM " + TimesheetActivity.class.getCanonicalName() + " c"
                + " WHERE c.activityPlannedId = :activityPlannedId"
            + ")",
            params,
            true,
            CacheRegion.TIMESHEET,
            0
        );
        return timesheetIds;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> changeParentActivityTaskId(final Long activityPlannedId, final Long newParentActivityTaskId, final ILoggedUser loggedUser) {
        final List<Long> timesheetIds = HQL_findByQuery(""
                + " SELECT c.id.timesheetId"
                + " FROM " + TimesheetActivity.class.getCanonicalName() + " c"
                + " WHERE c.activityPlannedId = :activityPlannedId",
                ImmutableMap.of("activityPlannedId", activityPlannedId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        final Map<String, Object> params = new HashMap<>(3);
        params.put("newParentActivityTaskId", newParentActivityTaskId);
        params.put("loggedUserId", loggedUser.getId());
        params.put("activityPlannedId", activityPlannedId);
        HQL_updateByQuery(""
            + " UPDATE " + TimesheetActivity.class.getCanonicalName()
            + " SET id.activityId = :newParentActivityTaskId"
            + " ,lastModifiedDate = CURRENT_TIMESTAMP "
            + " ,lastModifiedBy = :loggedUserId"
            + " WHERE activityPlannedId = :activityPlannedId",
            params,
            true,
            CacheRegion.TIMESHEET,
            0
        );
        HQL_updateByQuery(""
            + " UPDATE " + Timesheet.class.getCanonicalName()
            + " SET lastModifiedDate = CURRENT_TIMESTAMP "
            + " ,lastModifiedBy = :loggedUserId"
            + " WHERE id IN ("
                + " SELECT c.id.timesheetId"
                + " FROM " + TimesheetActivity.class.getCanonicalName() + " c"
                + " WHERE c.activityPlannedId = :activityPlannedId"
            + ")",
            params,
            true,
            CacheRegion.TIMESHEET,
            0
        );
        return timesheetIds;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public void stopAllStopwatchs() {
        final List<Timesheet> timesheetsWithStopwatchOn = HQL_findByQuery(""
                + " SELECT new " + Timesheet.class.getCanonicalName() + " (t.id, t.hasEnableStopwatch )"
                + " FROM " + Timesheet.class.getCanonicalName() + " t "
                + " WHERE t.hasEnableStopwatch = 1",
                Utilities.EMPTY_MAP,
                true,
                CacheRegion.TIMESHEET,
                0
        );

        timesheetsWithStopwatchOn.forEach((item) -> {
            final Date currentDate = ITimesheetDAO.getTimeLimitToModifyTimesheet();
            SimpleDateFormat time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateFormated = time.format(currentDate);
            HQL_updateByQuery(""
                    + " UPDATE " + Timesheet.class.getCanonicalName() + " t " 
                    + " SET"
                            + " t.hasEnableStopwatch = 0,"
                            + " t.timesheetEnd = '" + dateFormated + "',"
                            + " t.deleted = 1"
                    + " WHERE t.hasEnableStopwatch = 1",
                    Utilities.EMPTY_MAP,
                    true,
                    CacheRegion.TIMESHEET,
                    0
            );
        });
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setFileDataDto(ITimesheetFileDataEntityDto entity) {
        List<ITimesheetFileDataEntityDto> results = new ArrayList<>(1);
        results.add(entity);
        setFileDataDto(entity.getId(), results);
    }

    private void setFileDataDto(Long timesheetId, List<ITimesheetFileDataEntityDto> results) {
        Map<String, Object> params = ImmutableMap.of("entityId", timesheetId);
        String querySetFileData = " "
                + " SELECT new map("
                    + " f.id as fileId  "
                    + ",f.description as description "
                    + ",utf.description as lastModifiedBy "
                    + ",tf.lastModifiedDate as lastModifiedDate "
                    + ",tf.createdBy as createdBy "
                    + ",tf.createdDate as createdDate "
                    + ",tf.stage as stage "
                    + ",t.id as timesheetId "
                    + ",f.contentType as type"
                    + ",act.commitmentTask as activityCommitmentTask"
                + " )"
                + " FROM "
                + TimesheetFile.class.getCanonicalName() + " tf "
                + " JOIN " + Timesheet.class.getCanonicalName() + " t ON t.id = tf.id.timesheetId "
                + " JOIN " + FileRef.class.getCanonicalName() + " f ON f.id = tf.id.fileId"
                + " LEFT JOIN " + Activity.class.getCanonicalName() + " act ON act.id = t.activityPlannedId"
                + " LEFT JOIN " + User.class.getCanonicalName() + " utf ON tf.lastModifiedBy = utf.id"
                        + " WHERE "
                        + " t.id = :entityId "
                        + " AND f.id = tf.id.fileId "
                + " AND t.id = tf.id.timesheetId ";
        setFileDataDto(
                results,
                querySetFileData,
                params
        );
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<FileDataDto> getFiles(Long timesheetId) {
        List<ITimesheetFileDataEntityDto> results = new ArrayList<>(1);
        final Timesheet timesheet = new Timesheet(timesheetId, false);
        results.add(timesheet);
        setFileDataDto(timesheetId, results);
        return timesheet.getFileData();
    }
    
    /**
     * Recibe un listado ya se a de "ITimesheetFileDataIdDto" o "Map", y en ambos casos 
     * llena el atributo "files" con un listado de tipo "List<FileDataDto>" con todos los archivos asociados
     * a "timesheetId"
     * El parametro "query" debe recibir un HQL que devuelva un listado de tipo List<Map> con
     * los atributos necesarios para llenar "FileDataDto":
     *  -> fileId
     *  -> createdBy
     *  -> lastModifiedBy
     *  -> createdDate
     *  -> lastModifiedDate
     *  -> stage
     *  -> description
     *
     */
    private void setFileDataDto(
            List<? extends Object> results,
            String query, 
            Map<String, Object> params
    ) {
        List<Map<String, Object>> files = HQL_findByQuery(query, params, true, CacheRegion.TIMESHEET, 0);
        final String fileDataKey = "fileData";
        Map<Long, Object> found = new HashMap<>(results.size());
        files.forEach(f -> {
            if (!(f.get("timesheetId") instanceof Long)) {
                return;
            }
            Long timesheetId = (Long) f.get("timesheetId");
            if (!found.containsKey(timesheetId)) {
                List<? extends Object> r = results.stream().filter(result -> {
                    Long localTimesheetId = null;
                    if (result instanceof ITimesheetFileDataIdDto) {
                        localTimesheetId = ((ITimesheetFileDataIdDto) result).getTimesheetId();
                    } else if (result instanceof ITimesheetFileDataEntityDto) {
                        localTimesheetId = ((ITimesheetFileDataEntityDto) result).getId();
                    } else if (result instanceof Map) {
                        localTimesheetId = (Long) ((Map) result).get("timesheetId");
                    }
                    return localTimesheetId != null && localTimesheetId.equals(timesheetId);
                }).collect(Collectors.toList());
                if (r.isEmpty()) {
                    return;
                } else {
                    found.put(timesheetId, r.get(0));
                }
            }
            if (!found.containsKey(timesheetId)) {
                return;
            }
            FileDataDto data = new FileDataDto(
                (Long) f.get("fileId")
                ,(Long) f.get("createdBy")
                ,(String) f.get("lastModifiedBy")
                ,(Date) f.get("createdDate")
                ,(Date) f.get("lastModifiedDate")
                ,(Integer) f.get("stage")
                ,(String) f.get("description")
                ,(String) f.get("type")
                ,(Integer) f.get("activityCommitmentTask")
            );
            if (found.get(timesheetId) instanceof ITimesheetFileDataDto) {
                ITimesheetFileDataDto r = (ITimesheetFileDataDto) found.get(timesheetId);
                if (r.getFileData() == null || r.getFileData().isEmpty()) {
                    r.setFileData(new ArrayList<>());
                }
                r.getFileData().add(data);
            } else if (found.get(timesheetId) instanceof Map) {
                Map<String, Object> r = (Map) found.get(timesheetId);
                if (r.get(fileDataKey) == null || ((List) r.get(fileDataKey)).isEmpty()) {
                    r.put(fileDataKey, new ArrayList<>());
                }
                ((List) r.get(fileDataKey)).add(data);
            }
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<MultiFileDTO> getTimesheetFiles(Long activityPlannedId) {
        if (activityPlannedId == null) {
            return null;
        }
        return HQL_findByQuery(""
                + "SELECT new " + MultiFileDTO.class.getCanonicalName() + " ("
                    + " f.id as id, "
                    + " f.contentSize as size, "
                    + " f.description as description, "
                    + " tf.lastModifiedDate as lastModifiedDate, "
                    + " u.description AS lastModifiedBy, "
                    + " tf.stage as stage, "
                    + " f.extension as extension, "
                    + " f.contentType AS contentType,"
                    + " act.code as code, "
                    + " act.commitmentTask as activityCommitmentTask"
                + ") FROM " + Activity.class.getCanonicalName() + " act "
                + " INNER JOIN " + TimesheetActivity.class.getCanonicalName() + " ta ON ta.activityPlannedId = act.id "
                + " INNER JOIN " + Timesheet.class.getCanonicalName() + " t ON t.id = ta.id.timesheetId "
                + " INNER JOIN " + TimesheetFile.class.getCanonicalName() + " tf ON tf.id.timesheetId = ta.id.timesheetId "
                + " INNER JOIN " + Files.class.getCanonicalName() + " f ON f.id = tf.id.fileId "
                + " INNER JOIN " + User.class.getCanonicalName() + " u ON u.id = tf.createdBy "
                + " WHERE ta.activityPlannedId = :activityPlannedId "
                + " AND t.deleted = 0 ",
                ImmutableMap.of("activityPlannedId", activityPlannedId),
                true,
                CacheRegion.TIMESHEET,
                0
        );
    }

    private String getTimesheetCondition(CommitmentTask plannerCommitmentTask, boolean onlyMine, ILoggedUser loggedUser) {
        TextStringBuilder condition = new TextStringBuilder(300);
        return getTimesheetCondition(condition, plannerCommitmentTask, onlyMine, loggedUser);
    }

    private String getTimesheetCondition(TextStringBuilder condition, CommitmentTask plannerCommitmentTask, boolean onlyMine, ILoggedUser loggedUser) {
        if (onlyMine) {
            condition.append(""
            + " entity.deleted = 0"
            + " AND planner.commitmentTask = ").append(plannerCommitmentTask.getValue()).append(""
            + " AND ("
                + " entity.createdBy = ").append(loggedUser.getId()).append(""
                + " OR entity.userId = ").append(loggedUser.getId()).append(""
            + " ) ");
        } else if (loggedUser.isAdmin()) {
            condition.append(""
                + " entity.deleted = 0"
                + " AND planner.commitmentTask = ").append(plannerCommitmentTask.getValue()
            );
        } else {
            condition.append(""
            + " entity.deleted = 0"
            + " AND planner.commitmentTask = ").append(plannerCommitmentTask.getValue()).append(""
            + " AND ( "
                + " entity.createdBy = ").append(loggedUser.getId()).append(""
                + " OR entity.userId = ").append(loggedUser.getId());
            if (
                loggedUser.getServices().contains(ProfileServices.TS_READ_BUSINESS_UNIT)
                || loggedUser.getServices().contains(ProfileServices.TS_EDIT_BUSINESS_UNIT)
            ) {
                boolean isCorporateUser = loggedUser.getServices().contains(ProfileServices.USUARIO_CORPORATIVO);
                // Planta a la que pertenece
                condition.append(" OR entity.businessUnitId = ").append(loggedUser.getBusinessUnitId());
                // Plantas en las que participa
                condition.append(" OR entity.businessUnitId IN ("
                    + " SELECT b.id "
                    + " FROM ").append(User.class.getCanonicalName()).append(" u "
                    + " JOIN u.puestos p "
                    + " JOIN " + (!isCorporateUser ? "p.une b" : BusinessUnit.class.getCanonicalName() + " b ON b.organizationalUnitId = p.corp.id")
                    + " JOIN p.perfil pe "
                    + " WHERE "
                        + " pe.tsReadBusinessUnit = 1 "
                        + " AND u.id = ").append(loggedUser.getId()).append(""
                + " )");
            }
            if (loggedUser.getBusinessUnitDepartmentId() != null && (
                loggedUser.getServices().contains(ProfileServices.TS_READ_BUSINESS_UNIT_DEP)
                || loggedUser.getServices().contains(ProfileServices.TS_EDIT_BUSINESS_UNIT_DEP)
            )) {
                // Departamento al que pertenece
                condition.append(" OR entity.businessUnitDepartmentId = ").append(loggedUser.getBusinessUnitDepartmentId());
                // Departamentos en que participa
                condition.append(" OR entity.businessUnitDepartmentId IN ("
                    + " SELECT d.id "
                    + " FROM ").append(User.class.getCanonicalName()).append(" u "
                    + " JOIN u.puestos p "
                    + " JOIN p.departamentos d "
                    + " JOIN p.perfil pe "
                    + " WHERE "
                        + " pe.tsReadBusinessUnitDep = 1 "
                        + " AND u.id = ").append(loggedUser.getId()).append(""
                + " )");
            }
            condition.append(""
            + " ) ");
        }
        return condition.toString();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTimesheetRegistriesById(
        GridFilter filter,
        Long[] timesheetIds,
        ILoggedUser loggedUser
    ) {
        if (timesheetIds == null || timesheetIds.length == 0){
            getLogger().error("Missing for timesheetIds values at `getTimesheetRegistriesById`! NO DATA HAS BEEN RETRIVED");
            return Utilities.EMPTY_GRID_INFO;
        }
        filter.getCriteria().put("<condition>", "entity.id IN (" + StringUtils.join(timesheetIds, ", ") + ")");
        return this.queryTimesheetList(filter, false, CommitmentTask.IMPLEMENTATION, loggedUser);
    }

    /**
     * ¡Precaución!
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> queryTimesheetList(
            GridFilter filter, 
            boolean onlyMine, 
            CommitmentTask plannerCommitmentTask, 
            ILoggedUser loggedUser
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.timesheetDate desc, entity.timesheetStart");
            filter.setDirection(Byte.parseByte("2"));
        }
        final String condition = getTimesheetCondition(plannerCommitmentTask, onlyMine, loggedUser);
        StringBuilder query = new StringBuilder();
        query.append(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.pendingRecordId AS entity_pendingRecordId "
                + ",entity.code AS entity_code "
                + ",entity.uid AS entity_uid "
                + ",entity.plannerId AS entity_plannerId "
                + ",client.id AS client_id "
                + ",client.description AS client_description "
                + ",planner.id AS planner_id "
                + ",planner.description AS planner_description "
                + ",p.status AS plannerStatus"
                + ",task.description AS task_description "
                + ",task.id AS task_id "
                + ",actplan.id AS actplan_id"
                + ",actplan.description AS actplan_description"
                + ",actplan.code AS actplan_code"
                + ",actplant.module AS actplant_module"                
                + ",entity.systemLinkId AS entity_systemLinkId "
                + ",entity.systemLinkValue AS entity_systemLinkValue "
                + ",systemLink.label AS systemLink_label "
                + ",entity.tags AS entity_tags "
                + ",entity.description AS entity_description "
                + ",entity.timesheetDate AS entity_timesheetDate "
                + ",entity.timesheetStart AS entity_timesheetStart "
                + ",entity.timesheetEnd AS entity_timesheetEnd "
                + ",entity.fillType AS entity_fillType"
                + ",user.id AS user_id "
                + ",user.description AS user_description "
                + ",businessUnit.description AS businessUnit_description "
                + ",businessUnitDepartment.description AS businessUnitDepartment_description "
                + ",userBud.description AS userBud_description "
                + ",entity.createdDate AS entity_createdDate "
                + ",entity.lastModifiedDate AS entity_lastModifiedDate "
                + ",((entity.workedMinutes*1.00)/60) AS entity_workedMinutes "
                + ",entity.recordLocked AS entity_recordLocked "    // recordLocked
                + ",entity.id as timesheetId"
                + ",entity.hasEnableStopwatch AS hasEnableStopwatch"
                /*
                 ¡Cualquier atributo NUEVO a CAMBIO de nombre!

                 Agregarlo a la interfaz de `QueryTimesheetList` en Angular.
                 */
            + " ) "
            + " FROM ").append(Timesheet.class.getCanonicalName()).append(" entity "
            + " LEFT JOIN ").append(TimesheetActivity.class.getCanonicalName()).append(" ta ON ta.id.timesheetId = entity.id"
            + " LEFT JOIN  ").append(Activity.class.getCanonicalName()).append(" task ON task.id = ta.id.activityId"
            + " LEFT JOIN  ").append(Planner.class.getCanonicalName()).append(" p "
            + " ON p.id = entity.plannerId"        
            + " LEFT JOIN ").append(Activity.class.getCanonicalName()).append(" ac ON ac.id = p.activityId").append(""
            + " LEFT JOIN ac.events planner"
            + " LEFT JOIN p.client client "
            + " LEFT JOIN ").append(Activity.class.getCanonicalName()).append(" actplan"
            + " ON actplan.id = entity.activityPlannedId"
            + " LEFT JOIN actplan.type actplant "
            + " LEFT JOIN " ).append(BusinessUnitDepartmentRef.class.getCanonicalName()).append(" businessUnitDepartment"
                + " ON businessUnitDepartment.id = entity.businessUnitDepartmentId"
            + " LEFT JOIN " ).append(User.class.getCanonicalName()).append(" user"
                + " ON user.id = entity.userId"
            + " LEFT JOIN " ).append(BusinessUnitDepartmentRef.class.getCanonicalName()).append(" userBud"
                + " ON userBud.id = user.businessUnitDepartmentId"
            + " LEFT JOIN " ).append(BusinessUnit.class.getCanonicalName()).append(" businessUnit"
                + " ON businessUnit.id = entity.businessUnitId"
            + " LEFT JOIN ").append(SystemLink.class.getCanonicalName()).append(" systemLink ON systemLink.id = entity.systemLinkId ").append(""
            + " WHERE ").append(condition);

        return (GridInfo<Map<String, Object>>) HQL_getRows(query, filter, true, CacheRegion.TIMESHEET, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveTimesheetEvent(TimesheetEvent entity, Long firstAdminUserId) {
        makePersistent(entity, firstAdminUserId);
}


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTaskHoursData(Date start, Date end, ILoggedUser loggedUser, GridFilter filter) {
        if (start == null || end == null) {
            throw new RuntimeException("getTaskHoursData: Start and end filters are required.");
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("client.description");
            filter.setDirection((byte) 1);
            filter.getField().setOrderByMany(new ArrayList<>());
            filter.getField().getOrderByMany().add(new GridOrderBy("planner.description"));
            filter.getField().getOrderByMany().add(new GridOrderBy("task.description"));
        }
        TextStringBuilder condition = new TextStringBuilder(300);
        getTimesheetCondition(condition, CommitmentTask.IMPLEMENTATION, false, loggedUser);
        final TextStringBuilder HQL = new TextStringBuilder(TASK_HOURS_DATA_SELECT_HQL.replace(
                "<condition>",
                condition
        ));
        DateRangeUtil.defineRangeFilters(
                start,
                end,
                HQL
        );
        return HQL_getRows(HQL.toString(), filter, true, CacheRegion.TIMESHEET, 0);
    }
}
