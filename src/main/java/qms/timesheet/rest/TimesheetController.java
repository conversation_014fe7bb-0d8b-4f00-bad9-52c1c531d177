package qms.timesheet.rest;

import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import ape.pending.entities.PendingRecord;
import bnext.dto.FileDataDto;
import bnext.exception.ExplicitRollback;
import bnext.exception.InvalidCipherDecryption;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.entity.Activity;
import qms.activity.util.CommitmentTask;
import qms.form.util.SurveyParserHelper;
import qms.framework.dao.IReportDAO;
import qms.framework.dto.ReportDTO;
import qms.framework.entity.SystemLink;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;
import qms.framework.util.CacheRegion;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.ReportHandler;
import qms.planner.dao.IPlannerDAO;
import qms.planner.entity.Client;
import qms.planner.entity.Planner;
import qms.timesheet.dao.ITimesheetDAO;
import qms.timesheet.dto.ITimesheetDto;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.timesheet.dto.TimesheetDialogDto;
import qms.timesheet.dto.TimesheetDto;
import qms.timesheet.dto.TimesheetMoveDto;
import qms.timesheet.dto.TimesheetWithStopwatchDto;
import qms.timesheet.dto.TstUpdateDTO;
import qms.timesheet.entity.TaskCategory;
import qms.timesheet.entity.TaskDeliveryType;
import qms.timesheet.entity.Timesheet;
import qms.timesheet.entity.TimesheetActivity;
import qms.timesheet.entity.TimesheetFile;
import qms.timesheet.entity.TimesheetFilePK;
import qms.timesheet.util.TsEventManager;
import qms.util.GridFilter;
import qms.util.GridFilterByReportProcessingAccess;
import qms.util.ModuleUtil;
import qms.util.QMSException;
import qms.util.ReportBaseController;
import qms.util.dto.MultiFileDTO;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@RestController
@RequestMapping("timesheet")
public class TimesheetController extends ReportBaseController {

    private final String HAS_SAVE_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'TS_REGISTER_PLANNED', 'TS_REGISTER_UNPLANNED', 'SPECIAL_ACTIVITY_ATTENDANT', 'SPECIAL_ACTIVITY_ATTENDANT_AUDIT', 'TS_READ_BUSINESS_UNIT', 'TS_READ_BUSINESS_UNIT_DEP'"
    + ")";

    private final String HAS_READ_LIST_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'TS_REGISTER_PLANNED', 'TS_REGISTER_UNPLANNED', 'TS_READ_BUSINESS_UNIT', 'TS_READ_BUSINESS_UNIT_DEP'"
    + ")";

    private final String HAS_UNPLANNED_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'TS_REGISTER_UNPLANNED'"
    + ")";
    
    private final String HAS_CONFIG_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'PLANNER_CREATOR'"
    + ")";

    private static final String HAS_DASHBOARD_ACCESS = "hasAnyAuthority('IS_ADMIN', 'REPORTE_TIMESHEET'" + ")";

    private final String HAS_VIEW_ACCESS = "hasAnyAuthority('IS_ADMIN', 'REPORTE_TIMESHEET')"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TS_REGISTER_REPORT'))"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TS_ADMON_REPORT_ACCESS'))"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TS_DELETE_REPORT'))"; 
    
    /* Fecha en formato ISO sin guiones: yyyyMMdd */
    private static final Pattern ISO_DATE_PATTERN = Pattern.compile("^\\d{4}([0][1-9]|1[0-2])([0-2][1-9]|[1-3]0|3[01])$", Pattern.CASE_INSENSITIVE);
    
    private final String HAS_ACCESS_TIMESHEET_ACTIVITIES = "hasAnyAuthority("
        + "'ACTIVITY_CREATOR', 'ACTIVITY_VIEWER', 'ACTIVITY_MANAGER', 'IS_ADMIN',"
        + "'ACTIVITY_TASK_ONLY', 'ACTIVITY_MODIFY_RESPONSIBLE', "
        + "'ACTIVITY_MODIFY_IMPLEMENTATION', 'ACTIVITY_MODIFY_VERIFIER', "
        + "'ACTIVITY_MODIFY_VERIFICATION', 'ACTIVITY_SUPER_FILLER', 'ACTIVITY_SUPER_VERIFIER', "
        + "'ACTIVITY_SET_APPLY', 'ACTIVITY_MAX_OPEN_TIME_IGNORE', 'ACTIVITY_REMOVE_OBJECT_LINKS', "
        + "'ACTIVITY_MODIFY_DESCRIPTION', 'SPECIAL_ACTIVITY_ATTENDANT', "
        + "'SPECIAL_ACTIVITY_VERIFIER', 'SPECIAL_ACTIVITY_RESPONSIBLE',"
        + "'ACTIVITY_BULK_CREATOR', "
        + "'ACTIVITY_REPORTS_ALL', 'TS_REGISTER_PLANNED', 'TS_REGISTER_UNPLANNED'"
    + ")";
    
    @Autowired
    @Qualifier("TimesheetDAO")
    private ITimesheetDAO dao;

    @Autowired
    @Qualifier("PlannerDAO")
    private IPlannerDAO plannerDao;
    
    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO activityDao;
    
    @Autowired
    @Qualifier("ReportDAO")
    private IReportDAO reportDao;
        
    @PostMapping()
    @RequestMapping({"save", "save/{pendingRecordId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public ResponseEntity saveTimesheet(
            final @PathVariable(value = "pendingRecordId", required = false) Long pendingRecordId,
            final @RequestBody TimesheetDto dto
    ) throws QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final boolean isNew = dto.getTimesheetId() == null
                || Objects.equals(dto.getTimesheetId(), -1L)
                || Objects.equals(dto.getTimesheetId(), 0L);
        final ResponseEntity register = dao.save(pendingRecordId, dto, loggedUser);
        if (register.getStatusCode() == HttpStatus.OK) {
            final TsEventManager eventManager = new TsEventManager();
            if (isNew) {
                eventManager.registerTimesheet(dto, loggedUser);
            } else {
                eventManager.updateTimesheet(dto, loggedUser);
            }
        }
        return register;
    }

    @PostMapping()
    @RequestMapping({"delete/{timesheetId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_UNPLANNED_ACCESS)
    public ResponseEntity delete(
            final @PathVariable(value = "timesheetId") Long timesheetId
    ) {
        return deleteTimesheet(timesheetId, true);
    }

    private ResponseEntity deleteTimesheet(Long timesheetId, Boolean successResponse) {
        if (timesheetId == null) {
            return new ResponseEntity(HttpStatus.NO_CONTENT);
        }
        final TstUpdateDTO timesheet = getTimesheetUpdateDto(timesheetId);

        final Date dateRecord = timesheet.getTimesheetDate();
        final Date timeLimitToModifyTimesheet = ITimesheetDAO.getTimeLimitToModifyTimesheet();

        if (timeLimitToModifyTimesheet.after(dateRecord)) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body("outof-limit");
        }

        String extraUpdateParams = "";
        final Map<String, Object> params = new HashMap<>();
        params.put("remove", timesheetId);
        if (Boolean.TRUE.equals(timesheet.getHasEnableStopwatch())) {
            final Date currentDate = getCurrentDateToClosestFiveMinutes();
            extraUpdateParams = " "
                    + ",hasEnableStopwatch = 0"
                    + ",timesheetEnd = :timesheetEnd";
            params.put("timesheetEnd", currentDate);
        }

        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        params.put("userId", loggedUser.getId());

        final Integer result = dao.HQL_updateByQuery(" "
                        + " UPDATE " + Timesheet.class.getCanonicalName()
                        + " SET deleted = 1"
                        + " ,lastModifiedDate = CURRENT_DATE() "
                        + " ,lastModifiedBy = :userId"
                        + extraUpdateParams
                        + " WHERE "
                        + " id = :remove",
                params,
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (Objects.equals(result, 1)) {
            final TsEventManager eventManager = new TsEventManager();
            eventManager.deleteTimesheet(timesheet, loggedUser);
            return new ResponseEntity(successResponse, HttpStatus.OK);
        }
        return new ResponseEntity("Error al eliminar el registro", HttpStatus.CONFLICT);
    }

    @GetMapping()
    @RequestMapping({"main", "main/{isoDate}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public List<ITimesheetDto> mine(
        @PathVariable(value = "isoDate", required = false) String isoDate
    ) {
        if (isoDate != null && !ISO_DATE_PATTERN.matcher(isoDate).matches()) {
            return Utilities.EMPTY_LIST;
        }
        Date timesheetDate;
        if (isoDate == null) {
            timesheetDate = Utilities.today();
        } else {
            timesheetDate = Utilities.parseDateBy(isoDate, "yyyyMMdd");
        }
        if (timesheetDate == null) {
            throw new RuntimeException("Input date '" + isoDate + "' is invalid, format should be 'yyyyMMdd'.");
        }
        return mine(timesheetDate);
    }

    private List<ITimesheetDto> mine(Date timesheetDate){
        return this.mine(timesheetDate, null);
    }

    private List<ITimesheetDto> mine(
        Date rangeStart, Date rangeEnd /* Solo se usa rango al pasar valor de "rangeEnd" */,
        Long ... includedIds
    ){
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final StringBuilder HQL = new StringBuilder(200);
        HQL.append(""
            + " SELECT new ").append(TimesheetDto.class.getCanonicalName()).append("("
                + " t.pendingRecordId " // pendingRecordId
                + ",t.id "              // timesheetId
                + ",isNull(cl.id, onlyClient.id) "              // clientId
                + ",isNull(cl.description, onlyClient.description) "     // clientDescription
                + ",pl.id "             // plannerId
                + ",pla.description "   // plannerDescription
                + ",a.id "              // activityId
                + ",a.description "     // activityDescription
                + ",ap.id "             // activityPlannedId
                + ",ap.description "    // activityPlannedDescription
                + ",ap.code "           // activityPlannedCode
                + ",apt.module "        // activityPlannedModule
                + ",t.systemLinkId "    // systemLinkId
                + ",t.systemLinkValue " // systemLinkValue
                + ",tl.url"    
                + ",tl.label "          // systemLinkLabel
                + ",t.uid "             // uid
                + ",t.tags "            // tags
                + ",t.description "     // registry
                + ",t.timesheetDate "   // date
                + ",t.timesheetStart "  // start
                + ",t.timesheetEnd "    // end
                + ",t.recordLocked "    // recordLocked
                + ",t.workedMinutes "   // workedMinutes
                + ",t.hasEnableStopwatch "
                + ",t.stopwatchType "
                + ",bud.description "
                + ",t.stopwatchLocalTimeStart "
                + ",pl.status "
            + " )  "
            + " FROM ").append(Timesheet.class.getCanonicalName()).append(" t ")
            .append(" LEFT JOIN ").append(TimesheetActivity.class.getCanonicalName()).append(" ta")
                .append(" ON ta.id.timesheetId = t.id")
            .append(" LEFT JOIN  ").append(Activity.class.getCanonicalName()).append(" a ")
                .append(" ON a.id = ta.id.activityId").append(""
            + " LEFT JOIN " ).append(Planner.class.getCanonicalName()).append(" pl"
                + " ON pl.id = t.plannerId"
            + " LEFT JOIN " ).append(BusinessUnitDepartmentRef.class.getCanonicalName()).append(" bud"
                + " ON bud.id = t.businessUnitDepartmentId"
            + " LEFT JOIN pl.activity pla"
            + " LEFT JOIN pl.client cl"
            + " LEFT JOIN ").append(Activity.class.getCanonicalName()).append(" ap"
                + " ON ap.id = t.activityPlannedId"
            + " LEFT JOIN ap.type apt "
            + " LEFT JOIN " ).append(Client.class.getCanonicalName()).append(" onlyClient ON onlyClient.id = ap.clientId " 
            + " LEFT JOIN " ).append(SystemLink.class.getCanonicalName()).append(" tl ON t.systemLinkId = tl.id "
            + " WHERE t.deleted = 0 ");
        final Map<String, Object> params = new HashMap<>(3);
        final StringBuilder WHERE = new StringBuilder();
        WHERE.append(""
                + " AND t.userId = :userId");
        params.put("userId", loggedUser.getId());
        if (includedIds.length > 0) {
            WHERE.append(" AND t.id IN (:includedIds)");
            params.put("includedIds", includedIds);
        }
        List<ITimesheetDto> results;
        if (rangeEnd == null) {
            WHERE.append(" AND trunc(t.timesheetDate) = :timesheetDate");
            params.put("timesheetDate", rangeStart);
        } else {
            WHERE.append(""
                + " AND trunc(t.timesheetDate) >= :rangeStart"
                + " AND trunc(t.timesheetDate) <= :rangeEnd"
            );
            params.put("rangeStart", rangeStart);
            params.put("rangeEnd", rangeEnd);
        }
        results = (List<ITimesheetDto>) dao.HQL_findByQuery(
            HQL + " " + WHERE, params, true, CacheRegion.TIMESHEET, 0
        );
        return results;
    }

    @GetMapping()
    @RequestMapping("timesheet-add-files-count/{timesheetId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public List<Long> timesheetFilesCount (@PathVariable(name = "timesheetId", required = true) Long timesheetId) {
        StringBuilder HQL = new StringBuilder();
        HQL.append(""
            + " SELECT f.id as id  FROM ")
                .append(TimesheetFile.class.getCanonicalName()).append(" tf ")
                .append(", ").append(Timesheet.class.getCanonicalName()).append(" t ")
            .append(" JOIN ").append(FileRef.class.getCanonicalName()).append(" f ")
                .append(" ON f.id = tf.id.fileId").append(""
            + " WHERE "
                + " t.deleted = 0 "
                + " AND f.id = tf.id.fileId "
                + " AND t.id = tf.id.timesheetId ")
                .append(" AND t.id = ")
                .append(timesheetId);
        return dao.HQL_findByQuery(HQL.toString());
    }     
    
    @PostMapping()
    @RequestMapping("list")
    @PreAuthorize(HAS_READ_LIST_ACCESS)
    public GridInfo<Map<String, Object>> list(@RequestBody GridFilter filter) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return this.query(filter, false, loggedUser);
    }

    @PostMapping()
    @RequestMapping("mine")
    @PreAuthorize(HAS_SAVE_ACCESS)
    public GridInfo<Map<String, Object>> mine(@RequestBody GridFilter filter) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return this.query(filter, true, loggedUser);
    }


    /**
     * ¡Precaución!
     *
     */
    private GridInfo<Map<String, Object>> query(GridFilter filter, boolean onlyMine, ILoggedUser loggedUser) {
        return dao.queryTimesheetList(filter, onlyMine, CommitmentTask.IMPLEMENTATION, loggedUser);
    }

    @PostMapping("add/sync-update")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public ResponseEntity<TimesheetDto> syncUpdate(@RequestBody TimesheetDto dto) throws QMSException {
        if (dto == null) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        List<TimesheetWithStopwatchDto> hasStopwatchRunning = dao.isTimewatcherRunning(loggedUser.getId());
        if (!hasStopwatchRunning.isEmpty()) {
            return new ResponseEntity(
                    ImmutableMap.of("stopwatch_running_message", ITimesheetDAO.STOPWATCH_RUNNING, "registry", hasStopwatchRunning),
                    HttpStatus.CONFLICT
            );
        }
        final Date timeLimitToModifyTimesheet = ITimesheetDAO.getTimeLimitToModifyTimesheet();
        if (timeLimitToModifyTimesheet.after(dto.getDate())) {
            return new ResponseEntity("outof-limit", HttpStatus.CONFLICT);
        }
        if (dao.validateSplicedDates(dto, loggedUser.getId())) {
            return new ResponseEntity("dates-spliced", HttpStatus.CONFLICT);
        }

        final boolean planned = dto.getPendingRecordId() != null;
        final String code = dao.getTodaysCode(loggedUser.getAccount());
        final Timesheet newRecord = ITimesheetDAO.getTimesheetFromDto(dto, code, planned, loggedUser);
        final Timesheet registry = dao.makePersistent(newRecord, loggedUser.getId());
        String workedMinutes = dao.HQL_findSimpleString(" "
                + "SELECT t.workedMinutes "
                + "FROM " + Timesheet.class.getCanonicalName()
                + " t WHERE t.id = :id",
                ImmutableMap.of("id", dto.getTimesheetId()),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        if (workedMinutes.isEmpty()) {
            workedMinutes = "0";
        }
        registry.setWorkedMinutes(Double.valueOf(workedMinutes));
        dao.saveLinkedItems(
                TimesheetFile.class,
                registry.getId(),
                dto.getFileIds(),
                true,
                TimesheetFile.STAGE.MODIFICATION.getValue(),
                true,
                CacheRegion.TIMESHEET,
                0,
                loggedUser.getId()
        );
        dao.persistTimesheetActivity(dto, registry, TimesheetFile.STAGE.MODIFICATION, loggedUser);
        dto.setTimesheetId(registry.getId());

        final TsEventManager eventManager = new TsEventManager();
        eventManager.updateTimesheet(dto, loggedUser);

        return ResponseEntity.ok(ITimesheetDAO.getTimesheetDtoFromEntity(registry, dto));
    }

    @GetMapping()
    @RequestMapping("files/{timesheetId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public ResponseEntity<List<FileDataDto>> files(
        @PathVariable(value = "timesheetId", required = true) Long timesheetId
    ) {
        final List<FileDataDto> files = dao.getFiles(timesheetId);
        return ResponseEntity.ok(files);
    }

    @PostMapping("add/sync-remove/{timesheetId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PreAuthorize(HAS_SAVE_ACCESS)
    public ResponseEntity syncRemove(
            @PathVariable(value = "timesheetId") Long timesheetId
    ) {
        return deleteTimesheet(timesheetId, null);
    }

    private TstUpdateDTO getTimesheetUpdateDto(final Long timesheetId) {
        final TstUpdateDTO timeshet =  dao.HQLT_findSimple(
                TstUpdateDTO.class,
                ITimesheetDAO.TIMESHEET_UPDATE_SELECT_HQL,
                ImmutableMap.of("timesheetId", timesheetId),
                true,
                CacheRegion.TIMESHEET,
                0
        );

        final Long taskId = dao.HQL_findLong(" "
                        + "SELECT ta.id.activityId"
                        + " FROM " + TimesheetActivity.class.getCanonicalName() + " ta "
                        + " WHERE ta.id.timesheetId = :timesheetId",
                ImmutableMap.of("timesheetId", timesheetId),
                true,
                CacheRegion.TIMESHEET,
                30
        );
        timeshet.setTaskId(taskId);

        return timeshet;
    }

    private Date getCurrentDateToClosestFiveMinutes() {
        final Date currentDate = new Date();

        double minutes = currentDate.getMinutes();
        final double mod = minutes % 5;

        if (mod > 0) {
            double round = Math.round(minutes / 5);
            minutes = round * 5;
            currentDate.setMinutes((int) minutes);
        }

        return currentDate;
    }

    @GetMapping()
    @PreAuthorize(HAS_SAVE_ACCESS)
    @RequestMapping("add/data-source/{startIsoDate}/{endIsoDate}")
    public ResponseEntity<TimesheetDataSourceDto> dataSourceNew(
        @PathVariable(value = "startIsoDate", required = true) String startIsoDate,
        @PathVariable(value = "endIsoDate", required = true) String endIsoDate
    ){
        if (startIsoDate != null && !ISO_DATE_PATTERN.matcher(startIsoDate).matches()) {
            return null;
        }
        if (endIsoDate != null && !ISO_DATE_PATTERN.matcher(endIsoDate).matches()) {
            return null;
        }
        Date rangeStart = Utilities.parseDateBy(startIsoDate, "yyyyMMdd");
        Date rangeEnd = Utilities.parseDateBy(endIsoDate, "yyyyMMdd");
        if (rangeStart == null || rangeEnd == null) {
            throw new RuntimeException(""
                + "Input dates are invalid, format should be 'yyyyMMdd'. Current values are, start: '" + startIsoDate + "', end: '" + endIsoDate + "'."
            );
        }
        return this.dataSourceNewAction(rangeStart, rangeEnd, true);
    }

    private ResponseEntity<TimesheetDataSourceDto> dataSourceNewAction(
        Date rangeStart, Date rangeEnd, Boolean updateCatalogs, Long ... includedIds
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        TimesheetDataSourceDto result = new TimesheetDataSourceDto();
        if (updateCatalogs) {
            result = plannerDao.geCatalogsByByLoggedUser(loggedUser, false);
        }
        result.setTimesheetRegistries(
            mine(rangeStart, rangeEnd, includedIds)
        );
        result.setRegistryWithStopwatchEnable(dao.isTimewatcherRunning(loggedUser.getId()));
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping()
    @PreAuthorize(HAS_READ_LIST_ACCESS)
    @RequestMapping("data-source/{timesheetId}")
    public Map<String, Object> datasource(
        final @PathVariable(value = "timesheetId", required = true) Long timesheetId
    ){
       String query = " SELECT new map( "
                        + " user.description AS userName "
                        + ",CAST(t.timesheetStart as time) AS start "
                        + ",CAST(t.timesheetEnd as time) AS end "
                    + " )"
                    + " FROM " + Timesheet.class.getCanonicalName() + " t "
                    + " JOIN " + User.class.getCanonicalName() + " user"
                    + " ON user.id = t.userId"
                 + " WHERE "
                     + " t.id = :timesheetId";

       return dao.HQL_findSimpleMap(query, ImmutableMap.of("timesheetId", timesheetId), true, CacheRegion.TIMESHEET, 0);
    }
    
    @GetMapping()
    @PreAuthorize(HAS_SAVE_ACCESS)
    @RequestMapping("record-activity/{pendingRecordId}")
    public Map<String, Object> getActivityRecordId(
        final @PathVariable(value = "pendingRecordId", required = true) Long pendingRecordId
    ){
        Map params = new HashMap();
        params.put("pendingRecordId", pendingRecordId);
        return dao.HQL_findSimpleMap("" 
               + " SELECT new map( "
                + " c.recordId as recordId, "
                + " c.module as module "
                + " )"
               + " FROM " + PendingRecord.class.getCanonicalName() + " c "
               + " WHERE "
               + " c.id = :pendingRecordId"
               , params);
    }
    
    
    @GetMapping()
    @PreAuthorize(HAS_SAVE_ACCESS)
    @RequestMapping({"dialog/task-activities/{activityType}", "dialog/task-activities/{activityType}/{showImp}"})
    public GridInfo<TimesheetDialogDto> taskActivities(
            @RequestBody GridFilter filter,
            final @PathVariable(value = "activityType", required = true) Integer activityType,
            final @PathVariable(value = "showImp", required = false) Boolean showImp) {
        return activityDao.getTaskActivitiesByLoggedUserRowsMap(filter, SecurityUtils.getLoggedUser(), activityType, showImp);
    }

    @GetMapping()
    @PreAuthorize(HAS_SAVE_ACCESS)
    @RequestMapping({"dialog/data-source", "dialog/data-source/{showImp}"})
    public ResponseEntity<TimesheetDataSourceDto> dataSourceNewDialog(@PathVariable(value = "showImp", required = false) Boolean showImp){
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        TimesheetDataSourceDto result;
        result = plannerDao.geCatalogsByByLoggedUser(loggedUser, showImp);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @PostMapping()
    @RequestMapping("taskDeliveryType/save")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveTaskDeliveryType(
        @RequestBody TaskDeliveryType taskCategoryType
    ) throws QMSException {
        return dao.saveTaskDeliveryType(taskCategoryType, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("taskDeliveryType/list")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo taskCategoryTypeList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        // ToDo: Validar permisos
        return dao.HQL_getRows(new StringBuilder(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.status AS entity_status "
                + ",entity.code AS entity_code "
                + ",entity.description AS entity_description "
                + ",entity.taskDeliveryTypeInfo AS entity_taskDeliveryTypeInfo "
                + ",entity.createdDate AS createdDate "
                + ",entity.lastModifiedDate AS lastModifiedDate "
                + ",string_agg(b.description) AS businessUnitDepartmentName"
                + ",createdBy.description AS createdByName "
                + ",lastModifiedBy.description AS lastModifiedByName "
            + " )"
            + " FROM " + TaskDeliveryType.class.getCanonicalName() + " entity "
            + " LEFT JOIN entity.businessUnitDepartments b "
            + " LEFT JOIN entity.createdByUser createdBy "
            + " LEFT JOIN entity.lastModifiedByUser lastModifiedBy "
            + " WHERE " 
                + " entity.deleted = 0"
            + " GROUP BY "
                + " entity.id "
                + ",entity.status "
                + ",entity.code "
                + ",entity.description "
                + ",entity.taskDeliveryTypeInfo "
                + ",entity.createdDate "
                + ",entity.lastModifiedDate "
                + ",createdBy.description "
                + ",lastModifiedBy.description "), filter, true, CacheRegion.CATALOGS_PLANNER, 0
        );
    }
    
    @GetMapping
    @RequestMapping("taskDeliveryType/toggle-status/{id}")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    public ResponseEntity toogleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        Integer status = dao.HQL_findSimpleInteger(""
                + " SELECT t.status"
                + " FROM " + TaskDeliveryType.class.getCanonicalName() + " t"
                + " WHERE t.id = :id",
                ImmutableMap.of("id", id),
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + TaskDeliveryType.class.getCanonicalName() + " t "
                + " SET"
                        + " t.status = :status"
                        + ",t.lastModifiedDate = CURRENT_DATE()"
                        + ",t.lastModifiedBy = :user"
                + " WHERE t.id = :id",
                ImmutableMap.of("id", id, "status", status, "user", loggedUser.getId()),
                true,
                CacheRegion.TIMESHEET,
                0
            ) == 1
        ) {
            return new ResponseEntity<>(
                    ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @GetMapping()
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @RequestMapping("taskDeliveryType/load/{taskDeliveryTypeId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TaskDeliveryType loadTaskDeliveryType(
        @PathVariable(value = "taskDeliveryTypeId", required = true) Long taskDeliveryTypeId
    ) {
        // ToDo: Validar permisos
        return dao.HQLT_findById(TaskDeliveryType.class, taskDeliveryTypeId, true, CacheRegion.CATALOGS_PLANNER, 0);
    }
    
    
    @PostMapping()
    @RequestMapping("taskCategory/save")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveTaskCategory(
        @RequestBody TaskCategory taskCategory
    ) throws QMSException {
        return dao.saveTaskCategory(taskCategory, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("taskCategory/list")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo taskCategoryList(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        // ToDo: Validar permisos
        return dao.HQL_getRows(new StringBuilder(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.status AS entity_status "
                + ",entity.code AS entity_code "
                + ",entity.description AS entity_description "
                + ",entity.taskCategoryInfo AS entity_taskCategoryInfo "
                + ",entity.createdDate AS createdDate "
                + ",entity.lastModifiedDate AS lastModifiedDate "
                + ",createdBy.description AS createdByName "
                + ",lastModifiedBy.description AS lastModifiedByName "
            + " )"
            + " FROM " + TaskCategory.class.getCanonicalName() + " entity "
            + " LEFT JOIN entity.createdByUser createdBy "
            + " LEFT JOIN entity.lastModifiedByUser lastModifiedBy "
            + " WHERE " 
                + " entity.deleted = 0"
            + " GROUP BY "
                + " entity.id "
                + ",entity.status "
                + ",entity.code "
                + ",entity.description "
                + ",entity.taskCategoryInfo "
                + ",entity.createdDate "
                + ",entity.lastModifiedDate "
                + ",createdBy.description "
                + ",lastModifiedBy.description "), filter,
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
        );
    }
    
    @GetMapping
    @RequestMapping("taskCategory/toggle-status/{id}")
    @PreAuthorize(HAS_CONFIG_ACCESS)
    public ResponseEntity taskCategoryToogleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        Integer status = dao.HQL_findSimpleInteger(""
            + " SELECT"
            + " t.status"
            + " FROM " + TaskCategory.class.getCanonicalName() + " t"
            + " WHERE t.id = :id",
            ImmutableMap.of("id", id),
            true,
            CacheRegion.CATALOGS_PLANNER,
            0
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + TaskCategory.class.getCanonicalName() + " t "
                + " SET"
                        + " t.status = :status"
                        + ",t.lastModifiedDate = CURRENT_DATE()"
                        + ",t.lastModifiedBy = :user"
                + " WHERE t.id = :id",
                ImmutableMap.of("id", id, "user", loggedUser.getId(), "status", status),
                true,
                CacheRegion.CATALOGS_PLANNER,
                0
            ) == 1
        ) {
            return new ResponseEntity<>(
                ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @GetMapping()
    @PreAuthorize(HAS_CONFIG_ACCESS)
    @RequestMapping("taskCategory/load/{taskCategoryId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TaskCategory loadTaskCategory(
        @PathVariable(value = "taskCategoryId", required = true) Long taskCategoryId
    ) {
        // ToDo: Validar permisos
        return dao.HQLT_findById(TaskCategory.class, taskCategoryId, true, CacheRegion.CATALOGS_PLANNER, 0);
    }
    
    @Override
    @PostMapping()
    @RequestMapping("reports")
    @PreAuthorize(HAS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> reports(@RequestBody GridFilter filter){
        return reportDao.reports(filter, getModule(), SecurityUtils.getLoggedUser());
    }
    
    @Override
    @GetMapping({
            "reportConfig/c/{reportCode}",
            "reportConfig/c/{reportCode}/{masterId}",
            "reportConfig/{reportId}",
            "reportConfig/{reportId}/{masterId}"
    })
    @PreAuthorize(HAS_VIEW_ACCESS)
    public ResponseEntity<ReportDTO> reportColumn(
        @PathVariable(value = "reportId", required = false) Long reportId,
        final @PathVariable(value = "masterId", required = false) String masterId,
        final @PathVariable(value = "reportCode", required = false) String reportCode
    )  {
        reportId = ReportHandler.getPresetReportId(reportDao, reportId, reportCode);
        final ReportHandler handler = new ReportHandler();
        final ReportDTO config = handler.reportColumn(reportId, getModule(), SecurityUtils.getLoggedUser());
        if (config == null) {
            getLogger().error(
                    "Report config not found for reportId {} and masterId {}",
                    new Object[] {
                        reportId,
                        masterId
                    }
            );
            return new ResponseEntity(HttpStatus.CONFLICT);
        }
        return new ResponseEntity<>(config, HttpStatus.OK);
    }

    @Override
    @PostMapping({"report/{queryId}", "report/{queryId}/{start}/{end}"})
    @PreAuthorize(HAS_VIEW_ACCESS)
    public ResponseEntity<GridInfo<Map<String, Object>>> report(
            @RequestBody GridFilterByReportProcessingAccess filter,
            final @PathVariable(value = "queryId", required = false) Long queryId,
            final @PathVariable(value = "start", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            final @PathVariable(value = "end", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        if (filter == null) {
            filter = new GridFilterByReportProcessingAccess();
        }
        final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
        GridInfo<Map<String, Object>> result = queryHandler.getRowsById(queryId, filter, start, end, SecurityUtils.getLoggedUser());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
      
    @Override
    @PostMapping()
    @RequestMapping(path = "report-hierarchy/{queryId}/{level}", method = RequestMethod.POST)
    @PreAuthorize(HAS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> hierarchLevelRows(
        @RequestBody GridFilter filter,
        final @PathVariable(value = "queryId", required = true) Long queryId,
        final @PathVariable(value = "level", required = true) Integer level
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        if (filter == null) {
            filter = new GridFilter();
        }
        final SurveyParserHelper helper = new SurveyParserHelper();
        return helper.getDatabaseQueryHierarchyByLevelRows(filter, queryId, level, SecurityUtils.getLoggedUser());
    }
    
    
    private Module getModule() {
        return Module.TIMESHEET;
    }
    
    @PostMapping
    @RequestMapping("updateStopwatch/{timesheetId}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> updateHasEnableStopwatch(
            final @PathVariable(value = "timesheetId", required = true) Long timesheetId,
            final @RequestBody TimesheetDto dto
    ) {
        if (dao.HQL_updateByQuery(" "
                + " UPDATE " + Timesheet.class.getCanonicalName() + " t"
                + " SET"
                + " t.hasEnableStopwatch = :hasEnableStopwatch"
                + " ,t.timesheetEnd = :timesheetEnd"
                + " WHERE t.id = :timesheetId",
                ImmutableMap.of(
                        "timesheetId", timesheetId,
                        "timesheetEnd", dto.getEnd(),
                        "hasEnableStopwatch", dto.getHasEnableStopwatch()
                ),
                true,
                CacheRegion.TIMESHEET,
                0
        ) == 1) {
            final TsEventManager eventManager = new TsEventManager();
            eventManager.updateTimesheet(dto, SecurityUtils.getLoggedUser());
            return new ResponseEntity<>(true, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(true, HttpStatus.CONFLICT);
        }
    }

    @GetMapping
    @RequestMapping("addFileFromAppointment/{idFile}/{timesheetId}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity addFileFromAppointment(
            final @PathVariable(value = "idFile", required = true) Long idFile,
            final @PathVariable(value = "timesheetId") Long timesheetId
    ) {
        
        Long loggedUserId = SecurityUtils.getLoggedUserId();
        TimesheetFile timesheetFile = new TimesheetFile();
        timesheetFile.setCreatedBy(loggedUserId);
        timesheetFile.setCreatedDate(new Date());
        timesheetFile.setStage(1);
        timesheetFile.setLastModifiedBy(loggedUserId);
        timesheetFile.setLastModifiedDate(new Date());
        TimesheetFilePK timesheetFilePk = new TimesheetFilePK();
        timesheetFilePk.setFileId(idFile);
        timesheetFilePk.setTimesheetId(timesheetId);
        timesheetFile.setId(timesheetFilePk);
        return new ResponseEntity<>(dao.makePersistent(timesheetFile, loggedUserId), HttpStatus.OK);
        
    }   

    @PostMapping
    @RequestMapping("getTimesheetRegistriesById/{timesheetIds}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getTimesheetRegistriesById(
            final @RequestBody GridFilter filter,
            final @PathVariable(value = "timesheetIds", required = true) Long[] timesheetIds
    ) {
        return dao.getTimesheetRegistriesById(filter, timesheetIds, SecurityUtils.getLoggedUser());
    }

    @PostMapping("moveManyTimesheetRegistries")
    @PreAuthorize(HAS_SAVE_ACCESS)
    public ResponseEntity<Boolean> moveManyTimesheetRegistries(final @RequestBody TimesheetMoveDto dto) {
        final TsEventManager eventManager = new TsEventManager();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        dto.getTimesheets().stream()
                .map(TimesheetDto::getTimesheetId)
                .forEach(timesheetId -> moveTimesheet(dto, timesheetId, eventManager, loggedUser));
        return new ResponseEntity<>(true, HttpStatus.OK);
    }

    private void moveTimesheet(
            final TimesheetMoveDto moveData,
            final Long timesheetId,
            final TsEventManager eventManager,
            final ILoggedUser loggedUser
    ) {
        final Long oldPlannerId = dao.getPlannerId(timesheetId);
        final Long oldTaskId = dao.getTaskId(timesheetId);

        if (moveData.getPlannerTargetId() != null && moveData.getPlannerTargetId() > 0) {
            final Integer resultPlannerCount = dao.updatePlannerId(moveData, timesheetId, loggedUser);
            if (resultPlannerCount == null || resultPlannerCount == 0) {
                throw new ExplicitRollback(
                        "No se pudo actualizar el registro con el siguiente id "
                                + "taskTargetId[" + moveData.getTaskTargetId() + "] del registro "
                                + "timesheetId[" + timesheetId + "]");
            }

        }

        if (moveData.getTaskTargetId() != null && moveData.getTaskTargetId() > 0) {
            final Integer resultTaskCount = dao.updateTaskId(moveData, timesheetId, loggedUser);
            if (resultTaskCount == null || resultTaskCount == 0) {
                throw new ExplicitRollback(
                        "No se pudo actualizar el registro con el siguiente id "
                                + "taskTargetId[" + moveData.getTaskTargetId() + "] del registro "
                                + "timesheetId[" + timesheetId + "]");
            }
        }


        final Long newPlannerId = moveData.getPlannerTargetId();
        if (!Objects.equals(oldPlannerId, newPlannerId)) {
            eventManager.updatePlanner(
                    timesheetId,
                    oldPlannerId,
                    newPlannerId,
                    loggedUser
            );
        }

        final Long newTaskId = moveData.getTaskTargetId();
        if (!Objects.equals(oldTaskId, newTaskId)) {
            eventManager.updateTask(
                    timesheetId,
                    oldTaskId,
                    newTaskId,
                    loggedUser
            );
        }
    }

    @GetMapping()
    @RequestMapping("activity-has-stopwatch-running/{activityId}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> activityHasStopwatchRunning(@PathVariable(name = "activityId", required = true) Long activityId) {

        Map<String, Object> result = dao.HQL_findSimpleMap(""
                + " SELECT new map(t.hasEnableStopwatch AS hasEnableStopwatch) "
                + " FROM " + Timesheet.class.getCanonicalName() + " t"
                + " RIGHT JOIN " + Activity.class.getCanonicalName() + " ap"
                    + " ON ap.id = t.activityPlannedId"
                + " WHERE ap.id = :activityId "
                + " AND t.createdBy = :user"
                + " AND t.hasEnableStopwatch = 1",
                ImmutableMap.of("activityId", activityId, "user", SecurityUtils.getLoggedUserId()),
                true,
                CacheRegion.TIMESHEET,
                0
        );
        Boolean response;
        if (result != null && !result.isEmpty()) {
            response = Boolean.valueOf(result.get("hasEnableStopwatch").toString());
        } else {
            response = false;
        }
        return new ResponseEntity(response, HttpStatus.OK);

    }
    
    @GetMapping()
    @RequestMapping("timesheet-activity-files/data-source/{activityPlannedId}")
    @PreAuthorize(HAS_ACCESS_TIMESHEET_ACTIVITIES)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    public ResponseEntity<List<MultiFileDTO>> timesheetActivityFiles(
            @PathVariable(name = "activityPlannedId", required = true) Long activityPlannedId
    ) {
        final List<MultiFileDTO> timesheetFiles = dao.getTimesheetFiles(activityPlannedId);
        return new ResponseEntity<>(timesheetFiles, HttpStatus.OK);
        
    }

    @GetMapping()
    @RequestMapping("timesheet-activity/{activityId}")
    @PreAuthorize(HAS_ACCESS_TIMESHEET_ACTIVITIES)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    public GridInfo<Map<String, Object>> timesheetActivityRecords(
            final @RequestBody GridFilter filter,
            @PathVariable(name = "activityId", required = true) Long activityId
    ) {
        // Si la condición del filtro cambia - cambiar también en qms.framework.core.HibernateCacheHandler#loadActivityCache
        filter.getCriteria().put("<condition>", "entity.activityPlannedId = " + activityId);
        return this.query(filter, false, SecurityUtils.getLoggedUser());
    }
    
    @GetMapping()
    @RequestMapping("timesheet-stopwatch/check/{userId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    @PreAuthorize(HAS_SAVE_ACCESS)
    public List<TimesheetWithStopwatchDto> timesheetStopwtachCheck(@PathVariable(name = "userId", required = true) Long userId) {
        return dao.isTimewatcherRunning(userId);
    }
    
    @Override
    @GetMapping("report/reportListAvailables/{module}")
    @PreAuthorize(HAS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> reportListAvailables(@PathVariable(name = "module", required = true) String module) {
        GridInfo<Map<String, Object>> report = reportDao.reports(null, ModuleUtil.fromKey(module), SecurityUtils.getLoggedUser());
        return report.getData();
    }
    
    @GetMapping()
    @RequestMapping("timesheet-worked-time-info/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    @PreAuthorize(HAS_ACCESS_TIMESHEET_ACTIVITIES)
    public Map<String, Object> timesheetWorkedTimeInfo(@PathVariable(name = "activityId", required = true) Long activityId) {
        Map<String, Object> workedTimeInfo = dao.HQL_findSimpleMap(""
                + "SELECT new map("
                    + "SUM(t.workedMinutes * 1.00 / 60) AS workedHours,"
                    + " SUM(t.workedMinutes * 1.00) AS workedMinutes)"
                + " FROM " + Timesheet.class.getCanonicalName() + " t "
                + " WHERE "
                        + "t.activityPlannedId = :activityId "
                        + "AND t.deleted = 0", ImmutableMap.of("activityId", activityId));
        return workedTimeInfo;
    }

    /**
     * No se validan permisos por qué solo afecta a su propia sesión.
     *
     * @return
     */
    @GetMapping()
    @RequestMapping("dialog/pendingRefresh")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean pendingRefresh() {
        Long userId = SecurityUtils.getLoggedUser().getId();
        final UserLogin loginInfo = new UserLogin(userId);
        if (loginInfo.hasActiveSession()) {
            boolean result = loginInfo.getRefreshTimesheetDataSource();
            loginInfo.setRefreshTimesheetDataSource(false);
            return result;
        }
        return false;
    }

    @PostMapping()
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @RequestMapping("task-hours/{start}/{end}")
    public GridInfo<Map<String, Object>> taskHoursData(
            @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
            @RequestBody GridFilter filter
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return dao.getTaskHoursData(start, end, loggedUser, filter);
    }

    @GetMapping()
    @RequestMapping("clients/actives")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    public List<ITextHasValue> clientsActives() {
        return plannerDao.getClientActives();
    }

    @GetMapping()
    @RequestMapping("planners/actives")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    public List<ITextHasValue> plannersActives() {
        return plannerDao.getPlannersActives();
    }

    @GetMapping()
    @RequestMapping("tasks/actives")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    public List<ITextHasValue> tasksActives() {
        return plannerDao.getTaskActives();
    }

}
