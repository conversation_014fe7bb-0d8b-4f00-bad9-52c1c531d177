package qms.timesheet.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;
import qms.util.interfaces.ILinkedFilePK;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class TimesheetFilePK implements Serializable, ILinkedFilePK {

    private Long timesheetId;
    private Long fileId;

    public TimesheetFilePK(Long timesheetId, Long fileId) {
        this.timesheetId = timesheetId;
        this.fileId = fileId;
    }

    public TimesheetFilePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "timesheet_id")
    public Long getTimesheetId() {
        return timesheetId;
    }

    public void setTimesheetId(Long timesheetId) {
        this.timesheetId = timesheetId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "file_id")
    @Override
    public Long getFileId() {
        return fileId;
    }

    @Override
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.timesheetId);
        hash = 97 * hash + Objects.hashCode(this.fileId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TimesheetFilePK other = (TimesheetFilePK) obj;
        if (!Objects.equals(this.timesheetId, other.timesheetId)) {
            return false;
        }
        if (!Objects.equals(this.fileId, other.fileId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "TimesheetFilePK{" + "timesheetId=" + timesheetId + ", fileId=" + fileId + '}';
    }

}
