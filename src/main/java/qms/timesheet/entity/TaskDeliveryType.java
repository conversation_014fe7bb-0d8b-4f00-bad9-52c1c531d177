/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.timesheet.entity;

import DPMS.Mapping.DepartmentProcessLoad;
import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.CATALOGS_PLANNER, usage = CacheConcurrencyStrategy.READ_WRITE)
@CodePrefix("TASKDT")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "task_delivery_type")
public class TaskDeliveryType extends StandardEntity<TaskDeliveryType> implements IAuditableEntity {
    
    private static final long serialVersionUID = 1L;

    private String code;
    private String description = "";
    private String help = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long departmentProcessId;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Set<BusinessUnitDepartmentRef> businessUnitDepartments;
    private UserRef createdByUser;
    private UserRef lastModifiedByUser;
    private DepartmentProcessLoad departmentProcess;
    
    private String taskDeliveryTypeInfo;
    private Boolean generateCode;
    
    public static enum STATUS implements IStatusEnum {
        ACTIVE(1, IStatusEnum.COLOR_GREEN),
        INACTIVE(0, IStatusEnum.COLOR_GRAY);

        private final Integer value;
        private final String gridCube;

        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }

        @Override
        public Integer getValue() {
            return this.value;
        }

        @Override
        public String getGridCube() {
            return this.gridCube;
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return ACTIVE;
        }

        public static STATUS getStatus(Integer status) {
            for (STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }
    
    @Id
    @Column(name = "delivery_type_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @OneToMany(cascade = {javax.persistence.CascadeType.ALL}, fetch = FetchType.EAGER)
    @JoinTable(
            name = "delivery_type_business_unitdep",
            joinColumns = @JoinColumn(name = "delivery_type_id"),
            inverseJoinColumns = @JoinColumn(name = "business_unit_department_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitDepartmentRef> getBusinessUnitDepartments() {
        return businessUnitDepartments;
    }

    public void setBusinessUnitDepartments(Set<BusinessUnitDepartmentRef> businessUnitDepartments) {
        this.businessUnitDepartments = businessUnitDepartments;
    }
    
    @Basic(optional = false)
    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        if (description == null) {
            this.description = null;
        } else {
            this.description = description.trim();
        }
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
       if (status == null) {
            this.status = 1;
        } else {
            this.status = status;
        }
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            this.deleted = 0;
        } else {
            this.deleted = deleted;
        }
    }
    
    @Column(name = "created_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @CreatedDate
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @CreatedBy
    @Override
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "department_process_id")
    public Long getDepartmentProcessId() {
        return departmentProcessId;
    }

    public void setDepartmentProcessId(Long departmentProcessId) {
        this.departmentProcessId = departmentProcessId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "department_process_id", referencedColumnName = "department_process_id", updatable = false, insertable = false)
    public DepartmentProcessLoad getDepartmentProcess() {
        return departmentProcess;
    }

    public void setDepartmentProcess(DepartmentProcessLoad departmentProcess) {
        this.departmentProcess = departmentProcess;
    }

    @Column(name = "help")
    public String getHelp() {
        return help;
    }

    public void setHelp(String help) {
        this.help = help;
    }

    @Override
    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "task_delivery_type_info")
    public String getTaskDeliveryTypeInfo() {
        return taskDeliveryTypeInfo;
    }

    public void setTaskDeliveryTypeInfo(String taskDeliveryTypeInfo) {
        this.taskDeliveryTypeInfo = taskDeliveryTypeInfo;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "created_by", updatable = false, insertable = false)
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "last_modified_by", updatable = false, insertable = false)
    public UserRef getLastModifiedByUser() {
        return lastModifiedByUser;
    }

    public void setLastModifiedByUser(UserRef lastModifiedByUser) {
        this.lastModifiedByUser = lastModifiedByUser;
    }
    
    @Transient()
    public Boolean getGenerateCode() {
        return generateCode;
    }

    public void setGenerateCode(Boolean generateCode) {
        this.generateCode = generateCode;
    }
}
