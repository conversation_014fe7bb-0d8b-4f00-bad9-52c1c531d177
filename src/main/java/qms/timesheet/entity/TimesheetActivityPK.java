package qms.timesheet.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class TimesheetActivityPK implements Serializable {

    private Long timesheetId;
    private Long activityId;

    public TimesheetActivityPK(Long timesheetId, Long activityId) {
        this.timesheetId = timesheetId;
        this.activityId = activityId;
    }

    public TimesheetActivityPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "timesheet_id")
    public Long getTimesheetId() {
        return timesheetId;
    }

    public void setTimesheetId(Long timesheetId) {
        this.timesheetId = timesheetId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 61 * hash + Objects.hashCode(this.timesheetId);
        hash = 61 * hash + Objects.hashCode(this.activityId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TimesheetActivityPK other = (TimesheetActivityPK) obj;
        if (!Objects.equals(this.timesheetId, other.timesheetId)) {
            return false;
        }
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "TimesheetActivityPK{" + "timesheetId=" + timesheetId + ", activityId=" + activityId + '}';
    }
    
    

}
