package qms.util;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutionException;
import java.util.regex.Pattern;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.util.KnownExceptions;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.framework.file.FileManager;
import qms.framework.util.StringColorConfig;

public class AvatarUtils {

    private static final Logger LOGGER = Loggable.getLogger(AvatarUtils.class);
    public static final String AVATAR_FULL_PATH = "/qms/util/avatar-initials.svg";
    private static String TEMLATE_CACHE = null;

    private static final Pattern INITIALS_PATTERN = Pattern.compile("(##)", Pattern.MULTILINE);
    private static final Pattern COLOR_PATTERN = Pattern.compile("(#ffffff)", Pattern.MULTILINE);
    private static final Pattern BG_COLORPATTERN = Pattern.compile("(#cccccc)", Pattern.MULTILINE);
    
    private static synchronized String loadFromFile() {
        try {
            if (TEMLATE_CACHE != null) {
                return TEMLATE_CACHE;
            }
            final String content = FilePathUtils.loadFile(AVATAR_FULL_PATH);
            if (content == null || content.isEmpty()) {
                /*
                  TODO: En ocasiones la compilación aun no ha copiados el archivo `avatar-initials.svg`.

                  La solución podría ser hacer algo similar a lo que se hace
                  en `ReportGenerator.getReportPath`.
                  */
                LOGGER.error("Missing file avatar-initials.svg template");
                return null;
            }
            return content;
        } catch (final Exception ex) {
            LOGGER.error("Failed to load avatar-initials.svg template", ex);
            return null;
        }
    }

    private static String getTemplate() {
        if (TEMLATE_CACHE == null) {
            TEMLATE_CACHE = loadFromFile();
        }
        return TEMLATE_CACHE;
    }

    public static String getAvatarImageSrc(String name) {
        if (name == null || StringUtils.trimToEmpty(name).isEmpty()) {
            return null;
        }
        try {
            final String template = getTemplate();
            if (template == null || StringUtils.trimToEmpty(template).isEmpty()) {
                return null;
            }
            final String initials = ColorLib.nameInitials(name, "-");
            final StringColorConfig color = ColorLib.stringColors(name);
            final String resultInitials = INITIALS_PATTERN.matcher(template).replaceAll(initials);
            final String resultColors  = COLOR_PATTERN.matcher(resultInitials).replaceAll(color.getColor());
            return BG_COLORPATTERN.matcher(resultColors).replaceAll(color.getBgColor());
        } catch (ExecutionException ex) {
            LOGGER.error("Failed to parse avatar-initials.svg template", ex);
            return null;
        }
    }
    
    public static Boolean generateWriteAvatar(HttpServletResponse response, String userFullName) throws IOException {
        final String template = getAvatarImageSrc(userFullName);
        if (template == null) {
            return false;
        }
        final byte[] bytes = template.getBytes(StandardCharsets.UTF_8);
        final FileManager fileManager = new FileManager();
        fileManager.writeHeadersCacheShort(response, "avatar-initials.svg", "image/svg+xml", (long) bytes.length);
        try (final ServletOutputStream output = response.getOutputStream()) {
            output.write(bytes, 0, bytes.length);
            output.flush();
        } catch (final Exception ex) {
            final boolean clientAborted = KnownExceptions.hasClientAborted(ex);
            if (clientAborted) {
                LOGGER.debug("Client cancelled request for view avtar with name {}, error: {}",
                        userFullName, ex.getMessage()
                );
            } else {
                LOGGER.error("Failed to write avatar with name {}", userFullName, ex);
        }
            return false;
        }
        return true;
    }

    public static void reset() {
        TEMLATE_CACHE = null;
    }
}
