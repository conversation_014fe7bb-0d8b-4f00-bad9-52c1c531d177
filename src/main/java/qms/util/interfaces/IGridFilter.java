package qms.util.interfaces;

import Framework.Config.GridColumn;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import qms.util.GridOrderBy;
import qms.util.GridShowMore;

/**
 *
 * <AUTHOR>
 */
public interface IGridFilter extends IPagedQuery {

    HashMap<String, GridColumn> getColumns();

    HashMap<String, Object> getRawCriteria();

    HashMap<String, String> getCriteria();

    byte getDirection();

    HashMap<String, String> getDynamicFieldCriteria();

    GridOrderBy getField();

    String getGridId();

    HashMap<String, String> getLikeCriteria();

    HashMap<String, String> getLowerLimit();

    HashMap<String, String> getOptions();

    @Override
    int getPage();

    @Override
    Integer getPageSize();

    List<String> getStatisticsFields();

    HashMap<String, String> getUpperLimit();

    String getWindowPath();
    
    boolean getLikeOrSentence();

    boolean isAsumeAlias();

    boolean isDynamicSearchEnabled();

    boolean isEmptyCriterias();

    boolean isEnableStatistics();

    boolean isFilterInPatch();

    boolean isLegacyMode();

    boolean isShowMoreQuery();

    boolean isShowMoreNoopIds();

    void parseCriteriaMap(String HQL);

    void parseCriteriaMap(String query, boolean sql, boolean ignoreUpdateOfOrderBy);

    void setGridShowMore(GridShowMore gridShowMore);

    GridShowMore getGridShowMore();

    void setAsumeAlias(boolean asumeAlias);

    void setColumns(HashMap<String, GridColumn> columns);

    void setCriteria(HashMap<String, String> criteria);

    void setDirection(byte direction);

    void setDynamicFieldCriteria(HashMap<String, String> dynamicFieldCriteria);

    void setDynamicSearchEnabled(boolean dynamicSearchEnabled);

    void setEnableStatistics(boolean enableStatistics);

    void setField(GridOrderBy field);

    void setFilterInPatch(boolean filterInPatch);

    void setGridId(String gridId);

    void setLikeCriteria(HashMap<String, String> likeCriteria);

    void setLowerLimit(HashMap<String, String> lowerLimit);

    void setOptions(HashMap<String, String> options);

    @Override
    void setPage(int page);

    @Override
    void setPageSize(Integer pageSize);

    void setStatisticsFields(List<String> statisticsFields);

    void setUpperLimit(HashMap<String, String> upperLimit);

    void setWindowPath(String windowPath);

    boolean isAsumerNumberAsDot();
    
    void setAsumerNumberAsDot(boolean asumerNumberAsDot);
    
    void setLikeOrSentence(boolean likeOrSentence);
    
    String getCondition();
    
    String getFilteredEntity();
    
    String getLinkedDialogGrid();
    
    void getAllConditions();
    
    void setSearchStringChainIds(boolean searchStringChainIds);
    
    Boolean getSearchStringChainIds();
    
    Long getDisableLoadThreshold();
    
    void setDisableLoadThreshold(Long disableLoadThreshold);

    boolean canRetriveData();

    boolean skipCount();

    @Nullable
    Map<String, String> getAliasColumns();

    void setAliasColumns(@Nullable Map<String, String> aliasColumns);
}
