/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.util;

import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.Document;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.Settings;
import Framework.Config.PagedQuery;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.exception.ExplicitRollback;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import jakarta.servlet.ServletContext;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.custom.dao.IPrintingFormatDAO;
import qms.custom.dto.AnswerMetadataDTO;
import qms.custom.dto.BaseCustomField;
import qms.custom.dto.DataSourceDTO;
import qms.custom.dto.IFormatFieldDTO;
import qms.form.core.AutoTextHelper;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldAutoTextDTO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.FormRequestRecordDTO;
import qms.form.dto.FormSlimReportDTO;
import qms.form.dto.FormSlimReportDataSourceDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.AnswerPartType;
import qms.form.entity.FormRequest;
import qms.form.entity.SurveyAnswerMetadata;
import qms.form.entity.SurveyData;
import qms.form.util.AutoText;
import qms.form.util.FixedField;
import qms.form.util.FormRequestType;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.framework.dao.IReportDAO;
import qms.framework.dao.ISlimReportsDAO;
import qms.framework.entity.Report;
import qms.framework.entity.SlimReports;
import qms.framework.entity.SlimReportsRelatedField;
import qms.framework.entity.SlimReportsSurveyFields;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.framework.util.ReportType;
import qms.util.dto.AutorizationPoolDataDto;
import qms.util.dto.FieldObjectAttributes;
import qms.util.dto.FormApproverAnalyst;
import qms.util.dto.FormApproverUserInfo;
import qms.util.dto.FormCurrentApprover;
import qms.util.dto.FormatAnswerConfig;
import qms.util.dto.FormatedAnswerConfig;
import qms.util.interfaces.IPagedQuery;

/**
 *
 * <AUTHOR>
 */
public class FormUtil {
    // CACHE
    private static final Logger LOGGER = Loggable.getLogger(FormUtil.class);
    private static final List<String> WEEKDAYS = DateUtil.getWeekDayNames(Utilities.getLocale());
    private static final Pattern DATA_FIELD_KEY_PATTERN = Pattern.compile("data-field-key=\"([^\"]+)\"");
    private final static Double RECALCULATE_RECORDS_PER_PAGE = 20.0;
    private static final Map<String, AutoText> AUTO_TEXTS = AutoText.getValuesMap();
    private static final String PRINTING_DATE_FORMAT = "dd/MM/yyyy";
    private static final String PRINTING_TIME_FORMAT = "HH:mm";

    public static FormApproverUserInfo getFormApproverUser(Long userId, Long businessUnitId) {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class);
        return dao.getFormApproverUser(userId, businessUnitId);
    }
 
    public static Integer getActiveIndex(Long outstandingSurveysId) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        String query = " "
                + " SELECT min(a.indice) "
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " o "
                + " JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " a ON a.requestId = o.requestId "
                + " WHERE "
                + " a.accepted IS NULL "
                + " AND o.id = :outstandingSurveysId";
        final Long index =  dao.HQL_findLong(query, "outstandingSurveysId", outstandingSurveysId);
        if (index == null) {
            return null;
        }
        return index.intValue();
    }
    
    public static Boolean currentSectionCanCancel(Long outstandingSurveysId, Long activeIndex) {
        if (outstandingSurveysId == null || activeIndex == null) {
            return false;
        }
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        return dao.HQL_findSimpleString(" SELECT "
                    + " sfo.canCancel "
                + " FROM " + SurveyFieldObject.class.getCanonicalName() + " sfo "
                + " LEFT JOIN " + OutstandingSurveysAttendant.class.getCanonicalName() + " out ON out.fieldObjectId = sfo.id "
                + " WHERE "
                    + " out.fillAutorizationPoolIndex = :activeIndex AND "
                    + " out.outstandingSurveyId = :outstandingSurveyId AND "
                    + " out.fieldType IN (:fieldTypes)",
                ImmutableMap.of(
                        "activeIndex", activeIndex,
                        "outstandingSurveyId", outstandingSurveysId,
                        "fieldTypes", Arrays.asList(SurveyField.TYPE_SIGNATURE, SurveyField.TYPE_SECCION)
                )
        ).equals("t");
        
    }
    
    private static List<Long> getReadedSectionFieldIds(Long outstandingSurveysId, Integer activeIndex) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        String query = " SELECT f.id AS sectionFieldId "
                + " FROM "
                + OutstandingSurveys.class.getCanonicalName() + " o "
                + " JOIN "
                + AutorizationPoolDetails.class.getCanonicalName() + " a ON a.requestId = o.requestId"
                + " LEFT JOIN "
                + OutstandingSurveysAttendant.class.getCanonicalName() + " att "
                + " ON "
                + " att.requestId = o.requestId "
                + " AND att.fillAutorizationPoolIndex = a.indice "
                + " AND att.fieldType IN ("
                + "'" + SurveyField.TYPE_SECCION + "',"
                + "'" + SurveyField.TYPE_SIGNATURE + "'"
                + " ) "
                + " LEFT JOIN "
                + SurveyField.class.getCanonicalName() + " f "
                + " ON f.fieldObjectId = att.fieldObjectId "
                + " WHERE "
                + " o.id = :outstandingSurveysId"
                + " AND a.indice < :activeIndex";
        return dao.HQL_findByQuery(query, ImmutableMap.of(
            "outstandingSurveysId", outstandingSurveysId,
            "activeIndex", activeIndex
        ));
    }
    
    public static AutorizationPoolDataDto getAutorizationPoolData(Long outstandingSurveysId, Integer activeIndex) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        String query = " SELECT new " + AutorizationPoolDataDto.class.getCanonicalName() + "("
                + " a.id AS autorizationPoolDetailsId "
                + ",a.currentRecurrence AS autorizationPoolDetailsRecurrence "
                + ",f.id AS sectionFieldId "
                + ",f.fieldObjectId AS sectionFieldObjectId "
                + " ) "
                + " FROM "
                + OutstandingSurveys.class.getCanonicalName() + " o "
                + " JOIN "
                + AutorizationPoolDetails.class.getCanonicalName() + " a ON a.requestId = o.requestId"
                + " LEFT JOIN "
                + OutstandingSurveysAttendant.class.getCanonicalName() + " att "
                + " ON "
                + " att.requestId = o.requestId "
                + " AND att.fillAutorizationPoolIndex = a.indice "
                + " AND att.fieldType IN ("
                + "'" + SurveyField.TYPE_SECCION + "',"
                + "'" + SurveyField.TYPE_SIGNATURE + "'"
                + " ) "
                + " LEFT JOIN "
                + SurveyField.class.getCanonicalName() + " f "
                + " ON f.fieldObjectId = att.fieldObjectId "
                + " WHERE "
                + " o.id = :outstandingSurveysId"
                + " AND a.indice = :activeIndex";
        final AutorizationPoolDataDto autorizationPoolDetail =  (AutorizationPoolDataDto) dao.HQL_findSimpleObject(query, ImmutableMap.of(
            "outstandingSurveysId", outstandingSurveysId,
            "activeIndex", activeIndex
        ));
        if (autorizationPoolDetail == null) {
            return null;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("getAutorizationPoolData -> {}, {}, {}", outstandingSurveysId, activeIndex, autorizationPoolDetail);
        }
        autorizationPoolDetail.setReadedSectionFieldIds(
            getReadedSectionFieldIds(outstandingSurveysId, activeIndex)
        );
        return autorizationPoolDetail;
    }
    
    public static List<FormApproverAnalyst> getApproverAnalystData(Long outstandingSurveysId) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        String query = " SELECT new " + FormApproverAnalyst.class.getCanonicalName() + "("
                + " bud.status AS businessUnitDepartmentStatus "
                + ",bud.id AS businessUnitDepartmentId "
                + ",a.id AS analystUserId "
                + ",a.description AS analystUserName "
                + " ) "
                + " FROM "
                + OutstandingSurveys.class.getCanonicalName() + " c "
                + " JOIN "
                + BusinessUnitDepartment.class.getCanonicalName() + " bud ON bud.id = c.businessUnitDepartmentId "
                + " JOIN bud.analystApproverUsers a "
                + " WHERE "
                + " a.status = 1"
                + " AND c.id = :outstandingSurveysId";
        return dao.HQL_findByQuery(query, "outstandingSurveysId", outstandingSurveysId);
    }
    
    public static FormCurrentApprover getApproverData(Long outstandingSurveysId) {
        return getApproverData(null, " WHERE c.id = :outstandingSurveysId ", ImmutableMap.of("outstandingSurveysId", outstandingSurveysId));
    }
    public static FormCurrentApprover getApproverData(Long outstandingSurveysId, FormRequestType formRequestType) {
        return getApproverData(formRequestType, " WHERE c.id = :outstandingSurveysId ", ImmutableMap.of("outstandingSurveysId", outstandingSurveysId));
    }

    private static String getFormRequestName(FormRequestType formRequestType) {
        if (formRequestType != null) {
            switch (formRequestType) {
                case ADJUSTMENT:
                    return "formAdjustRequest";
                case CANCEL:
                    return "formCancelRequest";
                case REOPEN:
                    return "formReopenRequest";
            }
        }
        return null;
    }
    
    public static FieldObjectAttributes getSignatureProperties(Long outstandingSurveysId, Long index) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        FieldObjectAttributes result = (FieldObjectAttributes) dao.HQL_findSimpleObject(" SELECT new " + FieldObjectAttributes.class.getCanonicalName() + "("
                + " f.id AS surveyFieldId "
                + ",obj.id AS surveyFieldObjectId "
                + ",obj.signatureType "
                + ",obj.daysToWait "
                + ",obj.daysToExpire "
                + ",obj.daysToNotifyBeforeExpiration "
                + ",obj.includeInMail "
                + ",obj.includeInSubject "
                + ",obj.businessUnitMainField "
                + ",obj.businessUnitDepartmentMainField "
                + ",obj.areaMainField "
                + ",obj.allowDeleteStopwatchRecord "
                + ",obj.signRejectApproval "
            + " ) "
            + " FROM " 
                + OutstandingSurveysAttendant.class.getCanonicalName() + " c "
                + " CROSS JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                + " CROSS JOIN " + SurveyField.class.getCanonicalName() + " f "
            + " JOIN f.obj obj"
            + " WHERE "
                + " c.outstandingSurvey.requestId = d.requestId"
                + " AND c.fieldObjectId = obj.id"
                + " AND c.fillAutorizationPoolIndex = d.indice "
                + " AND c.outstandingSurvey.id = :outstandingSurveysId"
                + " AND ( "
                        + " c.fieldType IN (:fieldTypes) "
                        + " OR obj.requestAdjust = 1"
                + " ) "
                + " AND c.fillAutorizationPoolIndex = :index ",
            ImmutableMap.of(
                "fieldTypes", ImmutableList.of(SurveyField.TYPE.SECTION.getValue(), SurveyField.TYPE.SIGNATURE.getValue()),
                "outstandingSurveysId", outstandingSurveysId,
                "index", index
            )
        );
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                    "getSignatureProperties -> {}, {}, {}, {}",
                    outstandingSurveysId,
                    result != null ? result.getSurveyFieldId(): "-",
                    result != null ? result.getSurveyFieldObjectId(): "-",
                    index
            );
        }
        return result;
    }
    
    public static boolean isSignatureRejectionApprovalRequired(Long outstandingSurveysId, Long index) {
        FieldObjectAttributes data = getSignatureProperties(outstandingSurveysId, index);
        if (data == null) {
            //TODO: No lanzar RunTimeException
            throw new RuntimeException("No signature available");
        }
        return Boolean.TRUE.equals(data.getSignRejectApproval());
    }
   
   
    public static FormSlimReportDataSourceDTO getSurveyDataFields(
            final String documentMasterId,
            final Boolean includeMetadata,
            final ILoggedUser loggedUser,
            final Boolean allowOptionValue,
            final Boolean includeSections
    ) {
        final IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
        final FormSlimReportDataSourceDTO dto = getSurveyDataFields(documentMasterId, includeMetadata, 0, formCaptureDAO, loggedUser, true, allowOptionValue, includeSections);
        if (dto == null) {
            return null;
        }
        if (includeMetadata) {
            final Set<SurveyDataFieldDTO> fields = dto.getSurveyFields().stream()
                    .filter(f -> f.getSurveyAnswerMetadataId() != null)
                    .collect(Collectors.toSet());
            dto.setSurveyFields(fields);
        }
        return dto;
    }
    
    public static FormSlimReportDataSourceDTO getSurveyDataFields(
            String documentMasterId, 
            Boolean includeMetadata,
            Integer maxResults,
            IFormCaptureDAO formCaptureDAO,
            ILoggedUser loggedUser,
            final Boolean joinOptions,
            final Boolean allowOptionValue,
            final Boolean includeSections
    ) {
        final FormSlimReportDataSourceDTO dto = (FormSlimReportDataSourceDTO) formCaptureDAO.HQL_findSimpleObject(" SELECT new " + FormSlimReportDataSourceDTO.class.getCanonicalName() + "("
                    + " d.surveyId"
                    + ",d.description"
            + " )"
            + " FROM " + Document.class.getCanonicalName() + " d "
            + " WHERE "
                + " d.masterId = :documentMasterId"
                + " AND d.status IN ("
                    + Document.STATUS.ACTIVE.getValue()
                    + "," + Document.STATUS.IN_EDITION.getValue()
                + " )",
            ImmutableMap.of("documentMasterId", documentMasterId)
        );
        if (dto == null) {
            return null;
        }
        final FieldConfigDTO fieldConfig = SurveyUtil.getFields(
                dto.getSurveyId(),
                includeSections,
                false, 
                includeMetadata,
                null,
                maxResults, 
                loggedUser,
                joinOptions,
                allowOptionValue,
                true
        );
        final Set<SurveyDataFieldDTO> fields = fieldConfig.getFields();
        dto.setSurveyFields(fields);
        return dto;
    }
    
    public static Long getSurveyId(
        String documentMasterId
    ) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        return dao.HQL_findLong(" SELECT max(d.surveyId) "
            + " FROM " + Document.class.getCanonicalName() + " d "
            + " WHERE "
                + " d.masterId = :documentMasterId"
                + " AND d.status IN ("
                    + Document.STATUS.ACTIVE.getValue()
                    + "," + Document.STATUS.IN_EDITION.getValue()
                + " )", "documentMasterId", documentMasterId
        );
    }
    
    public static FormCurrentApprover getApproverData(FormRequestType formRequestType, String where, Map params) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final String type = getFormRequestName(formRequestType);
        final StringBuilder query = new StringBuilder();
        query.append(" SELECT DISTINCT new ").append(FormCurrentApprover.class.getCanonicalName()).append("( "
                + " c.code AS code "
                + ",c.status AS status "
                + ",c.id AS outstandingSurveyId "
                + ",bud.status AS businessUnitDepartmentStatus "
                + ",c.stageLatestFillOutUserId AS stageLatestFillOutUserId "
                + ",c.requestId AS requestId "
                + ",c.businessUnitId AS businessUnitId "
                + ",c.businessUnitDepartmentId AS businessUnitDepartmentId "
                + ",c.areaId AS areaId "
                + ",uc.id AS cancelationCurrentUserId "
                + ",uc.description AS cancelationCurrentUserName "
                + ",uc.status AS cancelationCurrentUserStatus "
                + ",uc.correo AS cancelationCurrentUserMail "
                + ",uc.account as cancelationCurrentUserAccount"
                + ",uc.version as cancelationCurrentUserVersion"
                + ",ua.id AS adjustmentCurrentUserId "
                + ",ua.description AS adjustmentCurrentUserName "
                + ",ua.status AS adjustmentCurrentUserStatus "
                + ",ua.correo AS adjustmentCurrentUserMail "
                + ",ua.account AS adjustmentCurrentUserAccount "
                + ",ua.version AS adjustmentCurrentUserVersion "
                + ",ur.id AS reopenCurrentUserId "
                + ",ur.description AS reopenCurrentUserName "
                + ",ur.status AS reopenCurrentUserStatus "
                + ",ur.correo AS reopenCurrentUserMail "
                + ",ur.account as reopenCurrentUserAccount"
                + ",ur.version as reopenCurrentUserVersion"
            );
        if (type != null) {
            query.append( ",fr.id AS formRequestId "
                + ",fr.description AS formRequestReason "
                + ",fr.status AS formRequestStatus "
                + ",fr.rejectedAutorizationPoolIndex AS formRejectedAutorizationPoolIndex "
                + ",fr.createdDate AS createdDate "
                + ",u.id AS createdById "
                + ",u.description AS createdByName "
                + ",u.correo AS createdByMail "
            );
        }
        query.append(" ) "
            + " FROM ").append(OutstandingSurveys.class.getCanonicalName()).append(" c "
            + " LEFT JOIN ").append(BusinessUnitDepartment.class.getCanonicalName()).append(" bud ON bud.id = c.businessUnitDepartmentId "
            + " LEFT JOIN bud.cancelationCurrentUser uc "
            + " LEFT JOIN bud.adjustmentCurrentUser ua "
            + " LEFT JOIN bud.reopenCurrentUser ur ");
        if (type != null) {
            query.append(" LEFT JOIN c.").append(type).append(" fr"
                + " LEFT JOIN fr.createdByUser u ")
            ;
        }
        query.append(" ").append(where);
        return (FormCurrentApprover) dao.HQL_findSimpleObject(query.toString(), params);
    }

    public static String getRejectedSectionDescription(Long outstandingSurveysId, Integer rejectedIndex) {
        if (rejectedIndex == 0) {
            return "";
        }
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        List<String> rejectSectionDescription = dao.HQL_findByQuery("SELECT sd.fieldStage"
                + " FROM " + FormRequest.class.getCanonicalName() + " fr"
                + " INNER JOIN " + OutstandingSurveys.class.getCanonicalName() + " o on o.id = fr.outstandingSurveysId and o.formAdjustRequestId = fr.id"
                + " INNER JOIN " + SurveyData.class.getCanonicalName() + " sd on sd.surveyId = o.surveyId"
                + " WHERE o.id = :outstandingSurveysId"
                + " and sd.fieldStage is not null"
                + " ORDER BY sd.fieldOrder", "outstandingSurveysId", outstandingSurveysId);
        if (rejectSectionDescription != null && !rejectSectionDescription.isEmpty()) {
            return rejectSectionDescription.get(rejectedIndex - 1);
        }
        return "";
    }

    public static void executeBuildAllSlimReports(final ServletContext servlet, final ILoggedUser admin) throws QMSException {
        if (Boolean.FALSE.equals(Utilities.getSettings().getPendingBuildAllSlimReports())) {
            return;
        }
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servlet);
        final List<Map<String, Object>> formularies = dao.HQL_findByQuery(" SELECT new map("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.code AS title"
                    + ", c.description AS description"
                    + ", c.surveyId AS surveyId"
                    + ", c.masterId AS masterId"
                    + ", c.answersNodeId AS answersNodeId"
                    + ", c.restrictRecordsByDepartment as restrictRecordsByDepartment"
                    + ", c.validateAccessFormDepartment as validateAccessFormDepartment"
                    + ", c.restrictRecordsField as restrictRecordsField"
                    + ", c.restrictRecordsObjId as restrictRecordsObjId"
                    + ", c.version AS documentVersion"
                + " )"
                + " FROM " + Document.class.getCanonicalName() + " c "
                + " WHERE c.surveyId IS NOT NULL "
                + " AND c.deleted = 0 "
                + " AND c.status IN ( "
                    + Document.STATUS.ACTIVE.getValue()
                    + "," + Document.STATUS.IN_EDITION.getValue()
                + ")",
                Utilities.EMPTY_MAP
        );
        final Set<Integer> fixedFields = Arrays.stream(FixedField.values())
                .map(FixedField::getValue).collect(Collectors.toSet());
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        final ISlimReportsDAO slimDao = dao.getBean(ISlimReportsDAO.class);
        for (final Map<String, Object> formularie : formularies) {
            
            final String documentMasterId = (String) formularie.get("masterId");
            final String description = (String) formularie.get("description");
            final Long nodeId = (Long) formularie.get("answersNodeId");
            
            final FormSlimReportDTO slimReport = new FormSlimReportDTO();
            
            slimReport.setFixedFields(fixedFields);
                    
            final FormSlimReportDataSourceDTO dataSource = getSurveyDataFields(
                    documentMasterId, 
                    false, 
                    0, 
                    dao, 
                    admin,
                    false,
                    true,
                    false
            );
            
            final Set<SurveyDataFieldDTO> allFields = dataSource != null ? dataSource.getSurveyFields() : new LinkedHashSet<>();
            
            final Set<SurveyDataFieldDTO> surveyFields;
            if (!Objects.equals(maxResults, 0) && allFields.size() > maxResults) {
                surveyFields = new LinkedHashSet<>(new ArrayList<>(allFields).subList(0, maxResults));
            } else {
                surveyFields = new LinkedHashSet<>(allFields);
            }
            final List<String> surveyFieldNames = surveyFields.stream()
                    .map(BaseCustomField::getName)
                    .collect(Collectors.toList());
            slimReport.setSurveyFields(surveyFieldNames);
            final List<String> relatedFieldNames = getRelatedFields(surveyFields, allFields);
            slimReport.setRelatedFields(relatedFieldNames);
            
            slimReport.setDescription(description + " " + new Date().getTime());
            
            slimReport.setDocumentMasterId(documentMasterId);
            slimReport.setId(-1L);
            slimReport.setCode(null);
            slimReport.setDetails(description);
            slimDao.migrateSlimReportDefault(slimReport, nodeId, admin);
        }
        dao.HQL_updateByQuery(" UPDATE " + Settings.class.getCanonicalName() + " s "
                + " SET s.pendingBuildAllSlimReports = :pendingBuildAllSlimReports",
                ImmutableMap.of("pendingBuildAllSlimReports", false)
        );
        Utilities.getSettings().setPendingBuildAllSlimReports(false);
    }
    
    public static void regenerateAllSlimReports(final ServletContext servlet, final ILoggedUser admin) throws QMSException {
        if (Boolean.TRUE.equals(Utilities.getSettings().getPendingRegenerateAllSlimReports()) || Boolean.TRUE.equals(Utilities.getSettings().getRegenerateAllSlimReportsAfterFreeze())) {
            final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servlet);
            final List<String> documentMasterIds = dao.HQL_findByQuery(" SELECT c.masterId"
                            + " FROM " + Document.class.getCanonicalName() + " c "
                            + " WHERE c.surveyId IS NOT NULL "
                            + " AND c.deleted = 0 "
                            + " AND c.status IN ( "
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue()
                            + ")",
                    Utilities.EMPTY_MAP
            );
            final ISlimReportsDAO slimDao = dao.getBean(ISlimReportsDAO.class);
            for (final String masterId : documentMasterIds) {
                slimDao.regenerateSlimReport(masterId, admin);
            }
            dao.HQL_updateByQuery(" UPDATE " + Settings.class.getCanonicalName() + " s "
                            + " SET s.pendingRegenerateAllSlimReports = :pendingRegenerateAllSlimReports, " +
                            "       s.regenerateAllSlimReportsAfterFreeze = :regenerateAllSlimReportsAfterFreeze",
                    ImmutableMap.of(
                        "pendingRegenerateAllSlimReports", false,
                        "regenerateAllSlimReportsAfterFreeze", false
                    )
            );
            IPrintingFormatDAO printingDao = Utilities.getBean(IPrintingFormatDAO.class);
            printingDao.regeneratePrintingFormats(admin);
            Utilities.getSettings().setPendingRegenerateAllSlimReports(false);
            Utilities.getSettings().setRegenerateAllSlimReportsAfterFreeze(false);
        }
    }


    public static List<String> getRelatedFields(final Set<SurveyDataFieldDTO> selectedFields, final Set<SurveyDataFieldDTO> allFields) {
        if (selectedFields == null || selectedFields.isEmpty() || allFields == null || allFields.isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        final List<Long> selectedExternalCatalogs = selectedFields.stream()
                .map(SurveyDataFieldDTO::getExternalCatalogId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (selectedExternalCatalogs.isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        return allFields.stream()
                .filter(m -> selectedExternalCatalogs.contains(m.getExternalCatalogId()))
                .map(BaseCustomField::getName).collect(Collectors.toList());
    }
    
    public static Long getSlimReportSurveyFieldsId(
            final String field,
                final Long surveyId,
            final IReportDAO reportDao
    ) {
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        final List<Long> fieldIds = getSlimReportSurveyFieldsIds(surveyId, Collections.singletonList(field), maxResults, reportDao);
        if (fieldIds == null || fieldIds.isEmpty()) {
            throw new ExplicitRollback("Unable to load fielId for fiel field " + field + " of survey " + surveyId);
        }
        return fieldIds.get(0);
    }

    public static Set<SlimReportsRelatedField> getSlimRelatedFields(
            final SlimReports slimReport, 
            final IFormCaptureDAO dao, 
            final ILoggedUser loggedUser
    ) {
        final String documentMasterId = slimReport.getDocumentMasterId();
        final Set<SlimReportsSurveyFields> surveysFields = slimReport.getSurveysFields();
        if (surveysFields == null || surveysFields.isEmpty()) {
            return Utilities.EMPTY_SET;
        }
        final List<String> selectedFieldsNames = surveysFields.stream()
                .map(SlimReportsSurveyFields::getFieldName)
                .collect(Collectors.toList());
        final FormSlimReportDataSourceDTO dataSource = getSurveyDataFields(
                documentMasterId,
                false,
                0,
                dao,
                loggedUser,
                false,
                true,
                false
        );
        if (dataSource == null) {
            return Utilities.EMPTY_SET;
        }
        final Set<SurveyDataFieldDTO> selectedFields = dataSource.getSurveyFields().stream()
                .filter(field -> selectedFieldsNames.contains(field.getName()))
                .collect(Collectors.toSet());
        if (selectedFields.isEmpty()) {
            return Utilities.EMPTY_SET;
        }
        final List<String> relatedFieldNames = getRelatedFields(selectedFields, dataSource.getSurveyFields());
        if (relatedFieldNames == null || relatedFieldNames.isEmpty()) {
            return Utilities.EMPTY_SET;
        }
        final IReportDAO reportDao = Utilities.getBean(IReportDAO.class);
        return relatedFieldNames.stream()
            .map(field -> {
                final Long fieldId = getSlimReportSurveyFieldsId(field, dataSource.getSurveyId(), reportDao);
                return new SlimReportsRelatedField(-1L, fieldId, field, slimReport);
            })
            .collect(Collectors.toSet());
    }
    
    public static List<Long> getSlimReportSurveyFieldsIds(
            final Long surveyId,
            final List<String> surveyFields,
            final Integer maxResults,
            final IReportDAO reportDao
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("surveyId", surveyId);
        return reportDao.HQL_findByQueryLimit(" SELECT DISTINCT s.fieldId "
                + " FROM " + SurveyAnswerMetadata.class.getCanonicalName() + " s "
                + " WHERE " + BindUtil.bindFilterList("s.surveyAnswerFieldName", surveyFields, false, params)
                + " AND s.surveyId = :surveyId",
                params,
                maxResults,
                true,
                CacheRegion.SURVEY,
                0
        );
    }

    public static Boolean canCancelCurrentSection(
            Long outstandingSurveyId,
            Long currentAuthIndex,
            List<ProfileServices> loggedUserServices,
            Integer documentStatus,
            Integer oustandingSurveyStatus
    ) {
        if (Objects.equals(oustandingSurveyStatus, OutstandingSurveys.STATUS.NEW.getValue())) {
            return false;
        }
        List<Integer> specialCancelStatus = Collections.unmodifiableList(Arrays.asList(
                OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue(),
                OutstandingSurveys.STATUS.IN_PROGRESS_FILLED_PARCIALLY.getValue()));
        return
                currentSectionCanCancel(outstandingSurveyId, currentAuthIndex) ||
                loggedUserServices.contains(ProfileServices.FORM_CANCEL_ANY) ||
                Objects.equals(documentStatus, Document.STATUS.DISCONTINUED.getValue()) ||
                ((currentAuthIndex == null || currentAuthIndex == 1) && specialCancelStatus.contains(oustandingSurveyStatus));
    }

    public static Map<Long, Long> getFillOutIndexes(
            final Long outstandingSurveyId,
            final IUntypedDAO dao
    ) {
        final List<Map<String, Object>> indexes = dao.HQL_findByQuery(" "
            + " SELECT DISTINCT new map("
                + " q.fieldId AS fieldId "                                              // <-- Viene de SURVEY_FIELD
                + ",q.filledAutorizationPoolIndex AS filledAutorizationPoolIndex "
            + " ) "
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
            + " JOIN c.preguntasRespondidas q "
            + " JOIN " + SurveyField.class.getCanonicalName() + " f ON f.id = q.fieldId "
            + " WHERE "
                    + " c.id = :outstandingSurveyId"
                    + " AND q.filledAutorizationPoolIndex IS NOT NULL "
                    + " AND f.type IN ("
                        + "'" + SurveyField.TYPE.SECTION.getValue() + "', '" + SurveyField.TYPE.SIGNATURE.getValue() + "'"
                    + " ) ",
                "outstandingSurveyId", outstandingSurveyId
        );
        return indexes.stream()
                .collect(Collectors.toMap(
                        indexed -> (Long) indexed.get("fieldId"),
                        indexed -> (Long) indexed.get("filledAutorizationPoolIndex")
                ));
    }
    
    public static String getFormatedAnswerSignature(
            final Object value,
            final SurveyDataFieldDTO field,
            final FormatAnswerConfig config,
            final ILoggedUser loggedUser
    ) {
        if (value == null
                && !field.getFieldType().equals(SurveyField.TYPE_SECCION)
                && !field.getFieldType().equals(SurveyField.TYPE_SIGNATURE)) {
            final AnswerPartType partType = SurveyUtil.getAnswerPartType(field);
            if (Objects.equals(field.getFieldType(), SurveyField.TYPE_FILE_UPLOAD)
                    && !AnswerPartType.FILE_NAME.equals(partType)) {
                return null;
            }
            return "";
        }
        if (value == null) {
            return null;
        }
        switch (field.getFieldType()) {
            case SurveyField.TYPE_SIGNATURE:
            case SurveyField.TYPE_SECCION:
                return getSignatureSeccionAnswer(field, config, loggedUser);
            case SurveyField.TYPE_SI_NO_PORQUE:
                return getSiNoPorqueAnswer(value, field, config);
            default:
                return getAnyFieldAnswer(field, value, field.getIncludeTime(), config);
        }
    }

    private static String getAnyFieldAnswer(
            SurveyDataFieldDTO field,
            Object value,
            Boolean includeTime,
            FormatAnswerConfig config
    ) {
        final AnswerPartType partType = SurveyUtil.getAnswerPartType(field);
        final SurveyFieldAnswerType answerType = SurveyUtil.getSurveyFieldAnswerType(field);
        FormatedAnswerConfig answerConfig = new FormatedAnswerConfig(
                value,
                field.getFieldType(),
                includeTime,
                LocaleUtil.getTag("selected", config.getTags()),
                null,
                answerType,
                partType
        );
        return getFormatedAnswer(answerConfig);
    }

    private static String getSiNoPorqueAnswer(
            Object value,
            SurveyDataFieldDTO field,
            FormatAnswerConfig config
    ) {
        if (field.getName().contains("other")) {
            final SurveyFieldAnswerType answerType = SurveyUtil.getSurveyFieldAnswerType(field);
            FormatedAnswerConfig answerConfig = new FormatedAnswerConfig(
                    value,
                    field.getFieldType(),
                    false,
                    LocaleUtil.getTag("selected", config.getTags()),
                    null,
                    answerType,
                    AnswerPartType.OTHER_FIELD
            );
            return getFormatedAnswer(answerConfig);
        }
        return getAnyFieldAnswer(field, value, false, config);
    }

    private static String getSignatureSeccionAnswer(
            SurveyDataFieldDTO field,
            FormatAnswerConfig config,
            ILoggedUser loggedUser
    ) {
        final Matcher dataPatternMatcher = DATA_FIELD_KEY_PATTERN.matcher(field.getFieldQuestionDesc());
        if (!dataPatternMatcher.find()) {
            return "";
        }
        final List<String> dataKeys = new ArrayList<>();
        while (dataPatternMatcher.find()) {
            final String key = getFormattedAutoText(field, config, dataPatternMatcher, loggedUser);
            if (key != null && key.isEmpty()) {
                dataKeys.add(key);
            }
        }
        if (dataKeys.isEmpty()) {
            return "";
        }
        return String.join(" ", dataKeys);

    }

    private static String getFormattedAutoText(
            SurveyDataFieldDTO field,
            FormatAnswerConfig config,
            Matcher dataPatternMatcher,
            ILoggedUser loggedUser
    ) {
        final String key = dataPatternMatcher.group(1);
        if (key == null || key.isEmpty()) {
            return null;
        }
        final Long index;
        final Map<Long, Long> fillOutIndexes = config.getFillOutIndexes();
        if (fillOutIndexes == null || fillOutIndexes.isEmpty() || fillOutIndexes.get(field.getFieldId()) == null) {
            index = 0L;
        } else {
            index = fillOutIndexes.get(field.getFieldId());
        }
        final AutoTextHelper helper = config.getHelper();
        if (Objects.equals(index, 0L) || (!helper.getHasRequest() && !helper.getHasDocument())) {
            return null;
        }
        FieldAutoTextDTO autoTextField = null;
        if (AutoText.FILL_OUT_AUTHORIZER.getName().equals(key) || AutoText.FILL_OUT_TIMESTAMP.getName().equals(key)) {
            autoTextField = new FieldAutoTextDTO();
            autoTextField.setFilledPoolIndex(index);
            autoTextField.setFieldId(field.getFieldId());
        }
        final AutoText autoText = AUTO_TEXTS.get(key);
        String text = config.getSurveyDao().getAutoTextValue(
                helper,
                autoTextField,
                autoText,
                loggedUser.getId()
        );
        final ResourceBundle tags = config.getTags();
        if (key.equals(text)) {
            text = LocaleUtil.getTag("pending", tags);
        }
        final String name = field.getName();
        if (name == null || name.isEmpty()) {
            return LocaleUtil.getTag(key, tags) + ": " + text;
        } else {
            return name + ", " + LocaleUtil.getTag(key, tags) + ": " + text;
        }
    }

    public static String getFormatedAnswer(FormatedAnswerConfig config) {
        if (config.getValue() == null && !config.getFieldType().equals(SurveyField.TYPE_SECCION)
                && !config.getFieldType().equals(SurveyField.TYPE_SIGNATURE)) {
            if (Objects.equals(config.getFieldType(), SurveyField.TYPE_FILE_UPLOAD)
                    && !AnswerPartType.FILE_NAME.equals(config.getPartType())) {
                return null;
            }
            if (config.getFieldType().equals(SurveyField.TYPE_SI_NO_PORQUE)
                    || config.getFieldType().equals(SurveyField.TYPE_OPCION_MULTIPLE)) {
                return config.getUnSelectedLabel() == null ? "" : config.getUnSelectedLabel();
            }
            return "";
        }
        if (config.getValue() == null) {
            return null;
        }
        switch (config.getFieldType()) {
            case SurveyField.TYPE_SIGNATURE:
            case SurveyField.TYPE_SECCION:
                throw new ExplicitRollback("Invalid call of `getFormatedAnswer()`, use `getFormatedAnswerSignature()` instead!!");
            case SurveyField.TYPE_TEXT_TIME:
                return Utilities.formatDateBy((Date) config.getValue(), PRINTING_TIME_FORMAT);
            case SurveyField.TYPE_TEXT_DATE:
                return getFormattedText(config.getValue(), config.getIncludeTime(), config.getPartType());

            case SurveyField.TYPE_SI_NO_PORQUE:
                if (AnswerPartType.OTHER_FIELD.equals(config.getPartType())) {
                    return config.getValue().toString();
                } else if ("1".equals(config.getValue())) {
                    return config.getSelectedLabel();
                }
                return null;
            case SurveyField.TYPE_STOPWATCH:
                return getFormattedStopwatch(config.getValue(), config.getPartType());
            case SurveyField.TYPE_HANDWRITTEN_SIGNATURE:
                return getFormattedHandwrittenSignature(config.getValue(), config.getPartType());
            case SurveyField.TYPE_FORM_WEIGHTING_RESULT:
                return formatNumber(config.getValue());
            case SurveyField.TYPE_ABIERTA:
            case SurveyField.TYPE_ABIERTA_RENGLONES:
                boolean isCurrency = isCurrency(config.getAnswerType(), config.getPartType());
                if (isCurrency) {
                    return formatCurrency(config.getValue());
                }
                return formatNumber(config.getValue());
            case SurveyField.TYPE_OPCION_MULTIPLE:
            case SurveyField.TYPE_MATRIZ:
            case SurveyField.TYPE_MATRIZ_MULTIPLE:
            case SurveyField.TYPE_MATRIZ_VERTICAL:
            case SurveyField.TYPE_MATRIZ_HORIZONTAL:
                if ("1".equals(config.getValue())) {
                    return config.getSelectedLabel();
                }
                return null;
            case SurveyField.TYPE_FILE_UPLOAD:
                if (!AnswerPartType.FILE_NAME.equals(config.getPartType())) {
                    return null;
                } else {
                    return config.getValue().toString();
                }
            default:
                return config.getValue().toString();
        }
    }

    private static String getFormattedText(Object value, Boolean includeTime, AnswerPartType partType) {
        if (partType == null) {
            if (Boolean.TRUE.equals(includeTime)) {
                return Utilities.formatDateWithTime((Date) value);
            }
            return Utilities.formatDateBy((Date) value, PRINTING_DATE_FORMAT);
        }
        if (partType == AnswerPartType.DAY_FIELD) {
            final int weekIndex = ((Number) value).intValue();
            if (weekIndex < 0 || weekIndex >= WEEKDAYS.size()) {
                return value.toString();
            }
            return WEEKDAYS.get(weekIndex);
        }
        if (partType == AnswerPartType.TIMEZONE) {
            return value.toString();
        }
        if (partType == AnswerPartType.REGULAR_FIELD) {
            if (Boolean.TRUE.equals(includeTime)) {
                return Utilities.formatDateWithTime((Date) value);
            }
            return Utilities.formatDateBy((Date) value, PRINTING_DATE_FORMAT);
        }
        return Utilities.formatDateBy((Date) value, PRINTING_DATE_FORMAT);
    }

    private static String getFormattedStopwatch(Object value, AnswerPartType partType) {
        if (partType == null) {
            return value.toString();
        }
        switch (partType) {
            case RANGE_START:
            case RANGE_END:
                return Utilities.formatDateWithTime((Date) value);
            case TIMEWORK_ID:
            case RANGE_SECONDS:
            default:
                return value.toString();
        }
    }

    private static String getFormattedHandwrittenSignature(Object value, AnswerPartType partType) {
        if (partType == null) {
            return value.toString();
        }
        if (AnswerPartType.DATA_CONTENT.equals(partType)) {
            final String answer = value.toString();
            if (answer == null) {
                return null;
            }
            if (HTMLUtil.isImageBase64(answer)) {
                return HTMLUtil.getImageWithBase64(answer);
            }
        }
        return "";
    }

    private static boolean isCurrency(SurveyFieldAnswerType answerType, AnswerPartType partType) {
        return SurveyFieldAnswerType.CURRENCY.equals(answerType)
                || (AnswerPartType.REGULAR_FIELD.equals(partType) && SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.equals(answerType))
                || AnswerPartType.EXCHANGE_RATE_SOURCE.equals(partType)
                || AnswerPartType.EXCHANGE_CONVERSION_RATE.equals(partType);
    }

    private static String formatNumber(final Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Double || value instanceof BigDecimal) {
            Double v = Utilities.formatDouble((Number) value, 2);
            if (v == null) {
                return null;
            }
            return v.toString();
        } else {
            return value.toString();
        }
    }
    
    private static String formatCurrency(final Object value) {
        if (value == null) {
            return null;
        } 
        final DecimalFormat formatter = new DecimalFormat("$#,###.00");
        if (value instanceof Double) {
            return formatter.format(value);
        } else if (value instanceof BigDecimal) {
            return formatter.format(value);
        } else {
            final String valueString = value.toString();
            if (valueString.isEmpty()) {
                return "";
            }
            return formatter.format(Utilities.parseDouble(valueString));
        }
    }

    public static void recalculateWorkflowPreviewRun(ServletContext servlet, ILoggedUser userAdmin) {
        if (Boolean.TRUE.equals(Utilities.getSettings().getRecalculateWorkflowPreviewDataRun())) {
            return;
        }
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servlet);
        final Long count = dao.countOpenFillRequests();
        if (count == null || count == 0) {
            return;
        }
        final double numberPages = Math.ceil(count > RECALCULATE_RECORDS_PER_PAGE ? count / RECALCULATE_RECORDS_PER_PAGE : 1.0);
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECALCULATE_RECORDS_PER_PAGE.intValue());
        for (int currentPage = 0; currentPage < numberPages; currentPage++) {
            page.setPage(currentPage);
            final List<Long> requestIds = dao.getOpenFillRequestIds(page);
            dao.recalculateWorkflowPreviewData(requestIds, userAdmin);
        }
    }

    public static void recalculateWorkflowFormRequestPreviewRun(ServletContext servlet, ILoggedUser userAdmin) {
        if (Boolean.TRUE.equals(Utilities.getSettings().getRecalculateWorkflowFormRequestDataRun())) {
            return;
        }
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servlet);
        final Long count = dao.countOpenFormRequests();
        if (count == null || count == 0) {
            return;
        }
        final double numberPages = Math.ceil(count > RECALCULATE_RECORDS_PER_PAGE ? count / RECALCULATE_RECORDS_PER_PAGE : 1.0);
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECALCULATE_RECORDS_PER_PAGE.intValue());
        for (int currentPage = 0; currentPage < numberPages; currentPage++) {
            page.setPage(currentPage);
            final List<FormRequestRecordDTO> records = dao.getOpenFormRequestRecords(page);
            dao.recalculateWorkflowFormRequestPreviewData(records, userAdmin);
        }
    }

    public static String surveyColumnsAnswersByFieldCode(final FieldConfigDTO fieldConfig, String fieldCode) {
        if (fieldCode == null || fieldCode.isEmpty() || fieldConfig.getFields().isEmpty()) {
            return "";
        }
        return fieldConfig
                    .getFields()
                    .stream()
                    .filter((f) -> f.getFieldCode().equals(fieldCode))
                    .map(BaseCustomField::getName).collect(Collectors.joining(","));
    }

    public static DataSourceDTO fillTitleAndFlexiFields(
            final Module module,
            final String masterId,
            final ILoggedUser loggedUser,
            final Boolean allowOptionValue,
            final Boolean includeSections
    ) {
        DataSourceDTO data = new DataSourceDTO();
        switch (module) {
            case FORMULARIE:
                final FormSlimReportDataSourceDTO dto = FormUtil.getSurveyDataFields(masterId, true, loggedUser, allowOptionValue, includeSections);
                final List<IFormatFieldDTO> fields = dto.getSurveyFields().stream().filter(f -> !f.getFieldType().equals(SurveyField.TYPE_SECCION) && !f.getFieldType().equals(SurveyField.TYPE_SIGNATURE))
                        .map(f -> new AnswerMetadataDTO(
                                f.getSurveyAnswerMetadataId(),
                                f.getSurveyAnswerMetadataId(),
                                f.getName(),
                                f.getTitle(),
                                f.getId(),
                                f.getFieldStage(),
                                f.getFieldObjectId()
                        ))
                        .collect(Collectors.toList());
                data.setFlexiFields(fields);
                final List<IFormatFieldDTO> fieldsSection = dto.getSurveyFields().stream().filter(f -> f.getFieldType().equals(SurveyField.TYPE_SECCION) || f.getFieldType().equals(SurveyField.TYPE_SIGNATURE))
                        .map(f -> new AnswerMetadataDTO(
                                f.getSurveyAnswerMetadataId(),
                                f.getSurveyAnswerMetadataId(),
                                f.getName(),
                                f.getTitle(),
                                f.getId(),
                                f.getFieldStage(),
                                f.getFieldObjectId()
                        ))
                        .collect(Collectors.toList());
                data.setSectionFields(fieldsSection);
                data.setTitle(dto.getTitle());
                break;
            case ACTION:
                // ToDo: Campos de hallazgos, y hacer plantillas de impresión para el hallazgo
                throw new AssertionError();
            case ACTIVITY:
                // ToDo: Campos de actividades, incluidos los dinamicos, y hacer plantillas de impresión para actividades
                throw new AssertionError();
            default:
                throw new AssertionError();
        }
        return data;
    }

    public static String getDocumentMasterId(final Long mainEntityId, IUntypedDAO dao, Class entityCls) {
        return dao.HQL_findSimpleString(" SELECT c.masterId"
                        + " FROM " + entityCls.getCanonicalName() + " c "
                        + " WHERE c.id = :id",
                ImmutableMap.of(
                        "id", mainEntityId
                )
        );
    }

    public static Integer updateCountEntity(final String documentMasterId, final ILoggedUser loggedUser, IUntypedDAO dao, Class entityCls, String countColumnName) {
        if (documentMasterId == null || documentMasterId.isEmpty()) {
            return 0;
        }
        final Integer countEntity = dao.HQL_findSimpleInteger(" SELECT count(c.id) AS countEntity"
                        + " FROM " + entityCls.getCanonicalName() + " c"
                        + " WHERE c.masterId = :masterId"
                        + " AND c.status = " + StandardEntity.STATUS.ACTIVE.getValue(),
                ImmutableMap.of(
                        "masterId", documentMasterId
                )
        );
        final Long documentId = dao.HQL_findLong(" SELECT c.id"
                        + " FROM " + Document.class.getCanonicalName() + " c"
                        + " WHERE c.masterId = :masterId"
                        + " AND c.deleted = 0"
                        + " AND c.status IN ("
                        + Document.STATUS.ACTIVE.getValue()
                        + ", " + Document.STATUS.IN_EDITION.getValue()
                        + " )",
                ImmutableMap.of(
                        "masterId", documentMasterId
                )
        );
        final Date now = new Date();
        if (documentId != null && documentId > 0) {
            dao.HQL_updateByQuery(" UPDATE " + Document.class.getCanonicalName() + " c"
                            + " SET c." + countColumnName + " = :countEntity"
                            + " , c.lastModificationDate = :lastModificationDate"
                            + " WHERE c.id = :documentId"
                            + " AND c.deleted = 0",
                    ImmutableMap.of("documentId", documentId,
                            "countEntity", countEntity,
                            "lastModificationDate", now)
            );
        }
        final List<Long> reportIds = dao.HQL_findByQuery(" SELECT c.id"
                        + " FROM " + Report.class.getCanonicalName() + " c"
                        + " WHERE c.documentMasterId = :masterId"
                        + " AND c.type = " + ReportType.SLIM_REPORT.getValue()
                        + " AND c.deleted = 0",
                ImmutableMap.of(
                        "masterId", documentMasterId
                )
        );
        if (reportIds != null && !reportIds.isEmpty()) {
            reportIds.forEach(reportId -> {
                dao.HQL_updateByQuery(" UPDATE " + Report.class.getCanonicalName() + " c"
                                + " SET c." + countColumnName + " = :countEntity"
                                + " , c.lastModifiedDate = :lastModificationDate"
                                + " , c.lastModifiedBy = :lastModifiedBy"
                                + " WHERE c.id = :id",
                        ImmutableMap.of(
                                "id", reportId,
                                "countEntity", countEntity,
                                "lastModificationDate", now,
                                "lastModifiedBy", loggedUser.getId()
                        )
                );
            });
        }
        return countEntity;

    }

    public static String getStartFillFormStage(final Long surveyId) {
        Map<String, Object> stage = getFillFormStage(surveyId, 0);
        if (stage == null) {
            return null;
        }
        return getFillFormStage(surveyId, 1).get("stage").toString();
    }

    /**
     * Método que devuelve información de la STAGE que se pase por parámetro.
     * @param surveyId
     * @param stage Etapa a considerar, si es 0 o 1 ambas son consideradas etapa 1.
     * @return
     */
    public static Map<String, Object> getFillFormStage(final Long surveyId, final Integer stage) {
        if (stage == null) {
            return null;
        }
        final int authPoolIndexStage = stage == 0 ? 1 : stage;
        List<Map<String, Object>> stages = Utilities.getUntypedDAO().HQL_findByQuery(""
                        + " SELECT new map(c.stage as stage, c.id as fieldObjectId, c.field_id as field_id)"
                        + " FROM " + SurveyField.class.getCanonicalName() + " d "
                        + " JOIN d.obj c "
                        + " WHERE"
                        + " c.surveyId = :surveyId"
                        + " AND (d.type = '" + SurveyField.TYPE_SECCION + "' or d.type = '" + SurveyField.TYPE_SIGNATURE + "')"
                        + " ORDER BY c.order ASC",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        if (stages.isEmpty() || authPoolIndexStage > stages.size()) {
            return null;
        } else {
            Map<String, Object> theStage = stages.get(authPoolIndexStage - 1);
            theStage.put("workflowIndex", Long.parseLong(Integer.toString(authPoolIndexStage))); // Se asume su workflow es igual a la etapa en la que se esta.
            return theStage;
        }
    }

}
