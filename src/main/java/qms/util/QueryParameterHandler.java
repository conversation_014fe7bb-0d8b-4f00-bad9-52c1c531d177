package qms.util;

import java.util.Date;
import org.hibernate.query.Query;
import qms.util.interfaces.IParameterHandler;

/**
 *
 * <AUTHOR>
 */
public class QueryParameterHandler implements IParameterHandler {

    final Query query;
    
    public QueryParameterHandler(final Query query) {
        this.query = query;
    }

    @Override
    public IParameterHandler setParameterType(String name, Object value, Class type) {
        query.setParameter(name, value, type);
        return this;
    }

    @Override
    public IParameterHandler setParameterList(String name, Object[] values, Class type) {
        query.setParameterList(name, values, type);
        return this;
    }

    @Override
    public IParameterHandler setParameter(String name, Object val) {
        query.setParameter(name, val);
        return this;
    }

    @Override
    public IParameterHandler setTimestamp(String name, Date date) {
        query.setParameter(name, date, Date.class);
        return this;
    }
    
}
