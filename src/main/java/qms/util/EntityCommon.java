package qms.util;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.Persistable;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.util.BaseClassParser;
import bnext.reference.IAuditable;
import com.google.gson.Gson;
import com.sun.star.auth.InvalidArgumentException;
import jakarta.persistence.Temporal;
import jakarta.persistence.metamodel.Attribute;
import jakarta.persistence.metamodel.EntityType;
import jakarta.servlet.ServletContext;
import java.beans.Introspector;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import mx.bnext.core.util.EvidencedBoolean;
import mx.bnext.core.util.IStatusEnum;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import qms.util.annotations.PrettyString;
import qms.util.annotations.UserDefinedCode;
import qms.util.annotations.UserDefinedCodePreview;
import qms.util.comparator.IntKeyMapComparator;
import qms.util.interfaces.ICodeValidator;
import qms.util.interfaces.IPersistableCode;
import qms.util.interfaces.IPersistableDescription;
import qms.util.interfaces.IPersistableUserDefinedCode;
import qms.util.interfaces.IUserDefinedCodeConfig;

/**
 * Created on : Aug 20, 2015, 4:51:12 PM
 * 
 * @ToDo: Eliminar el uso de reflection en esta CLASE y preparar todo lo escaneado en un objeto propio al iniciar la aplicación
 *
 * <AUTHOR> Carlos Limas @ Block Networks S.A. de C.V.
 */
public class EntityCommon extends Loggable{

    private static Set<EntityType<?>> ENTITY_TYPES = null;
    private static final ConcurrentHashMap<String, String> TABLE_NAMES = new ConcurrentHashMap<>();
    public static final int MAX_ATTEMPTS_GENERATE_CODE = 20;
    public static final String PREFIX_SEPATOR = "-";

    private static final String NAME_PREFIX = "status_";

    private static String IStatusEnumName(IStatusEnum s) {
        return NAME_PREFIX + s.name().toLowerCase();
    }

    private static final String STATUS_VAR_FINDER = "STATUS";

    private static Map<String, Object> enumToMap(IStatusEnum s) {
        Map<String, Object> m = new HashMap<>();
        m.put("name", IStatusEnumName(s));
        m.put("value", s.getValue());
        m.put("icon", s.getGridCube());
        return m;
    }

    private static List<IStatusEnum> getIStatusEnumList(String auditableEntityImpl) {
        Class<?> enumCls = getStatusClass(auditableEntityImpl);
        //Arroja una excepcion si no se encuentra el enum STATUS de tipo IStatusEnum
        Assert.notNull(enumCls, "La clase " + auditableEntityImpl + " debe tener un enum de nombre STATUS que implemente IStatusEnum");
        Assert.isInstanceOf(IStatusEnum[].class, enumCls.getEnumConstants(), "La clase " + auditableEntityImpl+"$" + STATUS_VAR_FINDER + " no implementa IStatusEnum");
        return Arrays.asList((IStatusEnum[]) enumCls.getEnumConstants());
    }
    
    private static Class<?> getStatusClass(String auditableEntityImpl){
        Class<?> enumClass;
        try {
            //Busca el enum STATUS en la clase base
            enumClass = Class.forName(auditableEntityImpl);
            enumClass = getStatusClass(enumClass);
        } catch (ClassNotFoundException ex) {
            getLogger(EntityCommon.class).error("There was an error ", ex);
            enumClass = null;
        }
        if(enumClass != null){
            return enumClass;
        }
        try {
            //BUSCA el enum STATUS en todas las interfaces
            Class<?>[] allInterfacesForClass = ClassUtils.getAllInterfacesForClass(Class.forName(auditableEntityImpl));
            for(Class<?> cls : allInterfacesForClass){
                enumClass = getStatusClass(cls);
                if(enumClass != null){
                    return enumClass;
                }
            }
        } catch (ClassNotFoundException ex) {
            getLogger(EntityCommon.class).error("There was an error ", ex);
        }
        return null;
    }
    
    private static Class getStatusClass(Class<?> cls){
        for(Class<?> internalClass : cls.getClasses()){
            if(internalClass.getSimpleName().equals(STATUS_VAR_FINDER) && internalClass.isEnum()){
                return internalClass;
            }
        }
        return null;
    }

    public static List getStatusList(String auditableEntityImpl) {
        List<Map> statusList = new ArrayList<>();
        List<IStatusEnum> iStatusEnumList = getIStatusEnumList(auditableEntityImpl);
        for (IStatusEnum s : iStatusEnumList) {
            statusList.add(enumToMap(s));
        }
        return statusList;
    }

    private static final String STATUS_INVALID = "status_invalid";

    public static String getStatusLabelKey(String auditableEntityImpl, Integer status) {
        for (IStatusEnum ar : getIStatusEnumList(auditableEntityImpl)) {
            if (ar.getValue().equals(status)) {
                return IStatusEnumName(ar);
            }
        }
        return STATUS_INVALID;
    }
    
    public static IStatusEnum getActiveStatus(String auditableEntityImpl) {
        return getIStatusEnumList(auditableEntityImpl).get(0).getActiveStatus();
    }
    
    public static IStatusEnum getActiveStatus(Class<? extends IAuditableEntity> auditableEntityImplClass) {
        return getIStatusEnumList(auditableEntityImplClass.getCanonicalName()).get(0).getActiveStatus();
    }
    
    public static String getStatusLabelKey(String auditableEntityImpl) {
        return STATUS_INVALID;
    }
    
    public static String getPrefix(Class<?> cls){
        CodePrefix codePrefix = cls.getAnnotation(CodePrefix.class);
        Assert.notNull(codePrefix, "Para usar el metodo [getPrefix] la clase" + cls + " debe estar anotada con la clase CodePrefix con un value");
        return codePrefix.value();
    }
    
    private static String getPrefix(
            Object entityInstance, 
            Class<?> cls,
            boolean simple,
            ICodeSequenceDAO dao
    ) {
        CodePrefix codePrefix = cls.getAnnotation(CodePrefix.class);
        Assert.notNull(codePrefix, "Para usar el metodo [getPrefix] la clase" + cls + " debe estar anotada con la clase CodePrefix con un value");
        String prefix = PREFIX_SEPATOR + codePrefix.value().trim() + PREFIX_SEPATOR;
        String[] p = prefix.split(PREFIX_SEPATOR);
        return getMappedValues(entityInstance, cls, p, simple, true, true, dao);
    }
    
    private static String getMappedValues(
            Object entityInstance,
            Class<?> cls,
            String[] parts,
            boolean simple,
            boolean useSeparator, 
            boolean prefixCall,
            IUntypedDAO dao
    ) {
        StringBuilder buffer = new StringBuilder();
        String SEPATOR = "";
        if(useSeparator) {
            SEPATOR = PREFIX_SEPATOR;
        }
        for(String prefixPart : parts) {
            prefixPart = prefixPart.trim();
            if(prefixPart.equals("${sequence}")) {
                buffer.append(prefixPart).append(SEPATOR);
            } else if (prefixPart.equals("${freeText}")) {
                buffer.append(SEPATOR);
            } else if(prefixPart.startsWith("${") && prefixPart.endsWith("}")) {
                prefixPart = prefixPart.substring(2, prefixPart.length()-1);
                if(prefixPart.isEmpty()) {
                    throw new RuntimeException("El prefijo definico \"" + prefixPart + "\" para la clase " + cls.getCanonicalName() + " no es valido, verificar idetificadores ${}");
                }
                setFieldValue(buffer, dao, false, entityInstance, prefixPart, prefixPart.toUpperCase(), SEPATOR);
            } else if (prefixPart.startsWith("$") || prefixPart.contains("{") || prefixPart.contains("}")) {
                throw new RuntimeException("El prefijo definido \"" + prefixPart + "\" para la clase " + cls.getCanonicalName() + " no es valido, verificar idetificadores ${}");
            } else {
                buffer.append(prefixPart).append(SEPATOR);
            }
        }
        
        if (prefixCall) {
            if(simple) {
                return buffer.substring(1, buffer.length() - 1).replaceAll("\\s", "").replaceAll("_", "").replaceAll(SEPATOR, "");
            } else {
                return buffer.substring(1, buffer.length() - 1).toUpperCase().replaceAll("\\s", SEPATOR).replaceAll("_", SEPATOR);
            }
        } else {
            if(simple) {
                return buffer.toString().replaceAll("\\s", "").replaceAll("_", "").replaceAll(SEPATOR, "");
            } else {
                return buffer.toString().toUpperCase().replaceAll("\\s", SEPATOR).replaceAll("_", SEPATOR);
            }
        }
    }
    
    public static String getFieldValue(Object entityInstance, String fieldName, IUntypedDAO dao) throws InvalidArgumentException {
        StringBuilder buffer = new StringBuilder(20);
        setFieldValue(buffer, dao, true, entityInstance, fieldName, "-", ", ");
        if(buffer.length() == 0) {
            return "";
        }
        return buffer.delete(buffer.lastIndexOf(",", buffer.length() - 2), buffer.length()).toString();
    }
    
    private static void setFieldValue(
            StringBuilder buffer, 
            IUntypedDAO dao,
            boolean prettyPrint, 
            Object entityInstance, 
            String prefixPart, 
            String resultWhenNull,
            String SEPATOR
    ) {
        Class<?> cls = entityInstance.getClass();
        Method m;
        String temp;
        try {
            m = cls.getMethod("get" + (prefixPart.charAt(0) + "").toUpperCase() + prefixPart.substring(1), (Class<?>[]) null);
            Object result = m.invoke(entityInstance, (Object[]) null);
            if(result == null) {
                result = resultWhenNull;
            }
            if (result instanceof Object[] || result.getClass().isArray()) {
                for(Object obj : (Object[]) result) {
                    buffer.append(((IPersistableCode) obj).getCode());
                    buffer.append(SEPATOR);
                }
            } else if (result instanceof Set) {
                for(Object obj : (Set) result) {
                    if (obj instanceof IPersistableDescription) {
                        temp = ((IPersistableDescription) obj).getDescription();
                        if (temp == null || temp.isEmpty()) {
                            buffer.append(dao.HQL_findSimpleString(""
                                    + " SELECT c.description"
                                    + " FROM " + obj.getClass().getCanonicalName() + " c"
                                    + " WHERE c.id = " + ((IPersistableDescription) obj).getId()
                            )).append(SEPATOR);
                        } else {
                            buffer.append(temp).append(SEPATOR);
                        }
                    } else {
                        temp = ((IPersistableCode) obj).getCode();
                        if (temp == null || temp.isEmpty()) {
                            buffer.append(dao.HQL_findSimpleString(""
                                    + " SELECT c.code"
                                    + " FROM " + obj.getClass().getCanonicalName() + " c"
                                    + " WHERE c.id = " + ((IPersistableCode) obj).getId()
                            )).append(SEPATOR);
                        } else {
                            buffer.append(temp).append(SEPATOR);
                        }
                    }
                }
            } else if (result instanceof Date) {
                if (prettyPrint) {
                    if (m.isAnnotationPresent(Temporal.class)) {
                        switch (m.getAnnotation(Temporal.class).value()) {
                            case TIMESTAMP:
                                buffer.append(Utilities.formatDateBy((Date) result, "dd/MM/yyyy  h:mm a")).append(SEPATOR);
                            break;
                            case TIME:
                                buffer.append(Utilities.formatDateBy((Date) result, "h:mm a")).append(SEPATOR);
                            break;
                            default:
                            case DATE:
                                buffer.append(Utilities.formatDateBy((Date) result, "dd/MM/yyyy")).append(SEPATOR);
                            break;
                        }
                    } else {
                        buffer.append(Utilities.formatDateBy((Date) result, "dd/MM/yyyy")).append(SEPATOR);
                    }
                } else {
                    buffer.append(Utilities.formatDateBy((Date) result, "yyyyMMdd")).append(SEPATOR);
                }
            } else if (result instanceof Boolean) {
                buffer.append((Boolean) result ? Utilities.getTag("yes") : Utilities.getTag("no")).append(SEPATOR);
            } else if (prettyPrint && result instanceof Persistable && result.getClass().isAnnotationPresent(PrettyString.class)) {
                buffer.append(dao.HQL_findSimpleString(" "
                    + " SELECT " + Arrays.toString(result.getClass().getAnnotation(PrettyString.class).mappableAttributes())
                            .replaceAll("[\\[\\]]", "")
                            .replace(",", ", c.")
                    + " FROM " + result.getClass().getCanonicalName() + " c"
                    + " WHERE c.id = " + ((Persistable) result).getId()
                )).append(SEPATOR);
            } else if(prettyPrint && result instanceof IPersistableDescription) {
                temp = ((IPersistableDescription) result).getDescription();
                if(temp == null || temp.isEmpty()) {
                    buffer.append(dao.HQL_findSimpleString(""
                        + " SELECT c.description"
                        + " FROM " + result.getClass().getCanonicalName() + " c"
                        + " WHERE c.id = " + ((IPersistableDescription) result).getId()
                    )).append(SEPATOR);
                } else {
                    buffer.append(temp).append(SEPATOR);
                }
            } else if(result instanceof IPersistableUserDefinedCode) {
                temp = ((IPersistableUserDefinedCode) result).getUserDefinedCode();
                if(temp == null || temp.isEmpty()) {
                    buffer.append(dao.HQL_findSimpleString(""
                        + " SELECT c.userDefinedCode"
                        + " FROM " + result.getClass().getCanonicalName() + " c"
                        + " WHERE c.id = " + ((IPersistableCode) result).getId()
                    )).append(SEPATOR);
                } else {
                    buffer.append(temp).append(SEPATOR);
                }
            } else if(result instanceof IPersistableCode) {
                temp = ((IPersistableCode) result).getCode();
                if(temp == null || temp.isEmpty()) {
                    buffer.append(dao.HQL_findSimpleString(""
                        + " SELECT c.code"
                        + " FROM " + result.getClass().getCanonicalName() + " c"
                        + " WHERE c.id = " + ((IPersistableCode) result).getId()
                    )).append(SEPATOR);
                } else {
                    buffer.append(temp).append(SEPATOR);
                }
            } else {
                buffer.append(result).append(SEPATOR);
            }
        } catch (NoSuchMethodException | SecurityException | IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
            throw new RuntimeException("El valor definido \"" + prefixPart + "\" para la clase " + cls.getCanonicalName() + " no es valido.", ex);
        }
    }
    
    public static void setNextCode(StandardEntity entityInstance) throws QMSException {
        //@ToDo: Traer la anotación del modelo "getEntityModel"
        UserDefinedCode d = entityInstance.getClass().getAnnotation(UserDefinedCode.class);
        if(d == null) {
            //el entity no tiene configurado la anotación @UserDefinedCode
            return;
        }
        ICodeSequenceDAO dao = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
        String map = getMapNextCode(entityInstance, new UserDefinedCodeManager(d), dao), code;
        if(map == null) {
            return;
        }
        try {
            final ICodeValidator validator = d.codeValidator().newInstance();
            String codeKey = null; 
            switch(validator.getCodeScopeLevel()) {
                case LEVEL_APPLICATION:
                    codeKey = entityInstance.getClass().getCanonicalName() + map;
                    break;
                case LEVEL_BUSINESS_UNIT:
                    codeKey = entityInstance.getClass().getCanonicalName() + map + "-" + validator.getBusinessUnitId(dao, entityInstance);
                    break;
            }
            String sequence = dao.next(codeKey, entityInstance, null, true);
            code = map.replace("${sequence}", sequence);
            EvidencedBoolean e = validator.isValid(entityInstance, code);
            while(!e.isValid()) {
                sequence = dao.next(codeKey, null, true);
                code = map.replace("${sequence}", sequence);
                e = d.codeValidator().newInstance().isValid(entityInstance, code);
            }
            entityInstance
                    .getClass()
                    .getMethod("set" + ((d.id().charAt(0)) + "").toUpperCase() + d.id().substring(1), String.class)
                    .invoke(entityInstance, code);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException | SecurityException | InstantiationException ex) {
            throw new QMSException("El atributo configurado para guardar el codigo autogenerado (id = \"" + d.id() + "\") no tiene metodo SET(String)", ex);
        }
    }
    
    public static String getCodePreviewPattern() {
        return getCodePreviewPattern(Utilities.getSettings().getMinimumCodeDigits());
    }

    public static String getCodePreviewPattern(Integer digits) {
        return StringUtils.repeat('#', digits);
    }

    
    public static String getNextCodePreview(StandardEntity entityInstance) throws QMSException {
        UserDefinedCodeManager builder;
        UserDefinedCodePreview d = entityInstance.getClass().getAnnotation(UserDefinedCodePreview.class);
        if(d == null) {
            UserDefinedCode c = entityInstance.getClass().getAnnotation(UserDefinedCode.class);
            if(c == null) {
                //el entity no tiene configurado la anotación @UserDefinedCode ni @UserDefinedCodePreview
                return null;
            }
            builder = new UserDefinedCodeManager(c);
        } else {
            builder = new UserDefinedCodeManager(d);
        }
        builder.setPreview(true);
        String map = getMapNextCode(entityInstance, builder, Utilities.getUntypedDAO());
        if(map == null) {
            return null;
        }
        return map.replace("${sequence}", getCodePreviewPattern());
    }
    
    public static String getMapNextCode(StandardEntity entityInstance, UserDefinedCodeManager d, IUntypedDAO dao) throws QMSException {
        if(!d.isPreview()) {
            try {
                String c = (String) entityInstance
                        .getClass()
                        .getMethod(d.getIdGetMethodName())
                        .invoke(entityInstance);
                if(c != null && !c.isEmpty()) {
                    //el entity ya tiene un codigo
                    return null;
                }
            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException | SecurityException ex) {
                getLogger(EntityCommon.class).error("There was an error ", ex);
                throw new QMSException("El atributo configurado para guardar el codigo autogenerado (id = \"" + d.getIdValue()+ "\") no tiene metodo GET", ex);
            }
        }
        Object sourceConfigCode;
        try {
            sourceConfigCode = entityInstance
                    .getClass()
                    .getMethod(d.getMappedByGetMethodName())
                    .invoke(entityInstance);
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException | SecurityException ex) {
            getLogger(EntityCommon.class).error("There was an error ", ex);
            throw new QMSException("El atributo configurado mappedBy = \"" + d.getMappedByValue() + "\" no tiene metodo GET", ex);
        }
        try {
            if (sourceConfigCode instanceof IUserDefinedCodeConfig) {
                String userDefCodeConfig = ((IUserDefinedCodeConfig) sourceConfigCode).configCode();
                if (userDefCodeConfig == null) {
                    //si no lo trae se intenta inicializar el objeto
                    sourceConfigCode = dao.HQLT_findById(sourceConfigCode.getClass(), ((IUserDefinedCodeConfig) sourceConfigCode).getId());
                    userDefCodeConfig = ((IUserDefinedCodeConfig) sourceConfigCode).configCode();
                }
                if (userDefCodeConfig == null) {
                    throw new QMSException("La configuración del atributo " + d.getMappedByValue() + " no pudo ser obtenida"); 
                }
                Map m = new Gson().fromJson(userDefCodeConfig, Map.class);
                TreeMap<String, String> sortedMap = new TreeMap<>(new IntKeyMapComparator());
                sortedMap.putAll(m);
                List<String> parts = new ArrayList<>();
                for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
                    parts.add(entry.getValue());
                }
                Class<?> cls = entityInstance.getClass();
                return getMappedValues(entityInstance, cls, parts.toArray(new String[0]), true, false, false, dao);
            } else {
                throw new QMSException("Falta implementar la interfaz 'IUserDefCodeConfig' al atributo '" + d.getMappedByValue() + "' ");
            }
        } catch (SecurityException | IllegalArgumentException ex) {
            throw new QMSException("UserDefinedCode didnt worked... ", ex);
        }
    }

    /**
     * Usarse con @UserDefinedCode.
     *
     * Utilizado para obtener siguiente FOLIO de entities anotados con @UserDefinedCode
     */
    public static <TYPE extends IAuditableEntity> String getNextCode(TYPE entityInstance) throws QMSException {
        return EntityCommon.getNextCode(entityInstance, null);
    }
    
    /**
     * Usarse con @UserDefinedCode.
     * 
     * Utilizado para obtener siguiente FOLIO de entities anotados con @UserDefinedCode
     */
    public static <TYPE extends IAuditableEntity> String getNextCode(TYPE entityInstance, String prefix) throws QMSException {
        final Class<? extends IAuditableEntity> entityClazz = entityInstance.getClass();
        return getNextCode(entityInstance, entityClazz, prefix);
    }
    
    /** * 
     * Usarse con @UserDefiUtilizado para obtener siguiente FOLIO de entities anotados con @UserDefinedCodenedCode.
     * @param <TYPE> TYPE
     * @param entityInstance entityInstance
     * @param entityClazz entityClazz
     * @param prefix prefix
     * @return 
     */
    public static <TYPE extends IAuditableEntity> String getNextCode(
            final TYPE entityInstance, 
            final Class<? extends IAuditableEntity> entityClazz,
            String prefix
    ) throws QMSException {
        final ICodeSequenceDAO dao = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
        Class<?> cls = entityInstance.getClass();
        prefix = prefix == null ? getPrefix(entityInstance, cls, false, dao) : prefix;
        final String code = generateNextCode(entityInstance, prefix, 0, dao);
        return code;
    }
    
    private static <TYPE extends IAuditableEntity> String generateNextCode(
        final TYPE entityInstance,
        final String prefix,
        Integer tryCount,
        final ICodeSequenceDAO dao
    ) throws QMSException {
        final Class<? extends IAuditableEntity> entityClazz = entityInstance.getClass();
        String code;
        if (prefix == null || prefix.isEmpty()) {
            code = dao.nextRequiresNew(entityInstance);
        } else {
            code = prefix + PREFIX_SEPATOR + dao.nextRequiresNew(entityInstance);
        }
        final boolean isRepeated = dao.HQL_findSimpleInteger(" "
                + " SELECT 1 "
                + " FROM " + entityClazz.getCanonicalName() + " c"
                + " WHERE c.code = :code", "code", code) == 1;
        if(!isRepeated) {
            return code;
        }
        if(tryCount > MAX_ATTEMPTS_GENERATE_CODE) {
            Loggable.getLogger(EntityCommon.class).error("La generación de la clave de {} pasó por {} claves repetidas.", new Object[]{
                entityClazz.getCanonicalName(),
                tryCount
            });
            return code;
        }
        tryCount++;
        Loggable.getLogger(EntityCommon.class).debug("La generación de la clave de {} ya existe. Aumentando la secuencia.", new Object[]{
            entityClazz.getCanonicalName(),
            tryCount
        });
        return generateNextCode(entityInstance, prefix, tryCount, dao);
    }
    
    
    /**
     * Usarse con @CodePrefix.
     *
     * Utilizado para obtener siguiente FOLIO de entities anotados con @CodePrefix
     */
    public static <TYPE extends IAuditable> String getPrefixNextCode(Class<TYPE> entityClass) throws QMSException {
        return getPrefixNextCode(entityClass, null);
    }
    /**
     * Usarse con @CodePrefix.
     *
     * Utilizado para obtener siguiente FOLIO de entities anotados con @CodePrefix
     * 
     * @param <TYPE>
     * @param entityClass
     * @param prefix
     * @return 
     */
    public static <TYPE extends IAuditable> String getPrefixNextCode(Class<TYPE> entityClass, String prefix) throws QMSException {
        ICodeSequenceDAO dao = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
        prefix = prefix == null || prefix.equals("") ? getPrefix(entityClass) : prefix;
        return prefix + PREFIX_SEPATOR + dao.next(entityClass);
    }
    
    public static <TYPE extends IAuditable> String getSimpleNextCode(TYPE entityInstance) throws QMSException {
        ICodeSequenceDAO dao = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
        Class<?> cls = entityInstance.getClass();
        return getPrefix(entityInstance, cls, true, dao) + dao.next(BaseClassParser.getTableName(entityInstance.getClass()), null, false);
    }
    
    public static String fieldToGetter(Attribute a) {
        return fieldToGetter(a.getName());
    }
    
    public static String fieldToGetter(String name) {
        return "get" + (name.charAt(0) + "").toUpperCase() + name.substring(1);
    }
    
    public static String methodToAttributeName(String methodName) {
        return Introspector.decapitalize(methodName.substring(methodName.startsWith("is") ? 2 : 3));
    }
    
    public static Set<EntityType<?>> loadEntityTypes(ServletContext servletContext) {
        if (ENTITY_TYPES == null) {
            ENTITY_TYPES = Utilities.getUntypedDAO(servletContext).getEntityManager().getEntityManagerFactory().getMetamodel().getEntities();
        }
        return ENTITY_TYPES;
    }
    
    public static void resetEntityTypes(ServletContext servletContext) {
        ENTITY_TYPES = null;
        loadEntityTypes(servletContext);
    }
    
    public static String loadTableName(final Class mappedClass) {
        final String entityName = mappedClass.toString();
        if (TABLE_NAMES.contains(entityName)) {
            return TABLE_NAMES.get(entityName);
        }
        final String tableName = BaseClassParser.getTableName(mappedClass);
        if (tableName == null) {
            return null;
        }
        TABLE_NAMES.putIfAbsent(entityName, tableName.toLowerCase());
        return tableName;
        
    }
}