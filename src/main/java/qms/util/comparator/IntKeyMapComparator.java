package qms.util.comparator;

import Framework.Config.Utilities;
import java.util.Comparator;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class IntKeyMapComparator implements Comparator {

    
    public IntKeyMapComparator() {
    
    }
    
    @Override
    public int compare(Object a, Object b) {
        if(a == null && b == null) {
            return 0;
        } else if(a == null) {
            return -1;
        } else if(b == null) {
            return 1;
        }
        if(Utilities.isInteger(a.toString()) && Utilities.isInteger(b.toString())) {
            return Integer.compare(Integer.parseInt(a.toString()),Integer.parseInt(b.toString()));
        }
        return a.toString().compareTo(b.toString());
    }
    
}
