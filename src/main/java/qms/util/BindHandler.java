package qms.util;

import java.util.Date;
import org.hibernate.type.Type;
import qms.util.interfaces.IParameterHandler;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class BindHandler {

    private String hqlFilter = null;
    private IParameterHandler parameterHandler = null;

    public BindHandler() {
    }

    public BindHandler(IParameterHandler paramHandler, String text) {
        this.hqlFilter = text;
        this.parameterHandler = paramHandler;
    }

    public String getHqlFilter() {
        return hqlFilter;
    }

    public void setHqlFilter(String hqlFilter) {
        this.hqlFilter = hqlFilter;
    }

    public void replaceAll(String regExp, String replacement) {
        if (this.hqlFilter == null || this.hqlFilter.isEmpty()) {
            return;
        }
        replacement = replacement.replace("\\", "\\\\");
        this.hqlFilter = this.hqlFilter.replaceAll(regExp + "\\)", replacement + ")");
        this.hqlFilter = this.hqlFilter.replaceAll(regExp + "\\s", replacement + " ");
        this.hqlFilter = this.hqlFilter.replaceAll(regExp + "$", replacement + " ");
    }

    public void replace(String target, String replacement) {
        if (this.hqlFilter == null) {
            return;
        }
        this.hqlFilter = this.hqlFilter.replace(target, replacement);
    }

    public IParameterHandler setParameterStrList(String string, Long[] os, String str, Type type) {
        this.replaceAll(":" + string, str);
        if (this.parameterHandler == null) {
            return null;
        }
        return this.parameterHandler.setParameterList(string, os, type);
    }

    public IParameterHandler setParameterStrList(String string, String[] os, String str, Type type) {
        this.replaceAll(":" + string, str);
        if (this.parameterHandler == null) {
            return null;
        }
        return this.parameterHandler.setParameterList(string, os, type);
    }

    public IParameterHandler setParameter(String string, Object o, String s) {
        this.replaceAll(":" + string, s);
        if (this.parameterHandler == null) {
            return null;
        }
        return this.parameterHandler.setParameter(string, o);
    }

    public IParameterHandler setBoolean(String string, boolean o, String s) {
        this.replaceAll(":" + string, s);
        if (this.parameterHandler == null) {
            return null;
        }
        return this.parameterHandler.setParameter(string, o);
    }

    public IParameterHandler setTimestamp(String string, Date date, String s) {
        this.replaceAll(":" + string, s);
        if (this.parameterHandler == null) {
            return null;
        }
        return this.parameterHandler.setTimestamp(string, date);
    }
}
