
package qms.util;

import Framework.Config.Utilities;
import ape.pending.core.APE;
import ape.pending.core.PendingHelper;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import qms.framework.util.LocaleUtil;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class ModuleUtil {
    public static Module fromKey(String key) {
        if (key == null) {
            return null;
        }
        for (Module module : Module.allModules()) {
            if (module.getKey().equalsIgnoreCase(key)) {
                return module;
            }
        }
        return null;
    }
    
    public static List<Module> getAvailableModules() {
        final List<Module> allModules = Module.allModules();
        return allModules.stream().filter((module) -> Utilities.isModuleAvailable(module)).collect(Collectors.toList());
    }
    
    public static List<String> getAvailableModuleKeys() {
        final List<Module> modules = getAvailableModules();
        final List<String> keys = modules.stream()
                .map((module) -> module.getKey())
                .collect(Collectors.toList());   
        return keys;
    }
    
    
    public static APE getApeFromModule(String moduleName, String pendingCamelTypeName) {
        if (moduleName.isEmpty()) {
            Loggable.getLogger(ModuleUtil.class).error("Please set parameter 'module'!!", moduleName, new RuntimeException());
            return null;
        }
        Module module;
        try {
            module = fromKey(moduleName);
        } catch(IllegalArgumentException ex) {
            Loggable.getLogger(ModuleUtil.class).error("Invalid module!!, module: '{}'", moduleName, ex);
            return null;
        }
        
        List<APE> apes = PendingHelper.getTypesByModule(module);
        APE ape = null;
        for (APE p : apes) {
            if (p.camelCase().equalsIgnoreCase(pendingCamelTypeName)) {
                ape = p;
                break;
            }
        }
        if (ape == null) {
            Loggable.getLogger(ModuleUtil.class).error("Missing APE {}", pendingCamelTypeName);
        }
        return ape;
    }    

    public static String getModulesDetails(String[] modules, Locale locale) {
        if (modules == null || Objects.equals(modules.length, 0)) {
            return "";
        }
        final ResourceBundle tags = LocaleUtil.getSystemI18n(Module.class.getCanonicalName());
        final String detailError = Arrays.asList(modules).stream()
                .map(module -> ModuleUtil.fromKey(module))
                .filter(module -> module != null)
                .map(module -> LocaleUtil.getTag(module.getKey(), tags, locale))
                .filter(module -> module != null && !module.isEmpty())
                .collect(Collectors.joining(", "));
        return detailError;
    }
    
}
