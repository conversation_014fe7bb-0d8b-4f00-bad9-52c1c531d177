package DPMS.Action;

import DPMS.DAOInterface.IAuditIndividualDAO;
import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.ActionSources;
import DPMS.Mapping.Area;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualPending;
import DPMS.Mapping.AuditType;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveysRef;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.struts2.action.SurveyCaptureAction;
import java.util.Date;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR>
 */
public class Action_AuditSurveyCapture extends SurveyCaptureAction {

    public static String TASK_GOING_TO_ACCEPT = "accept";

    private boolean auditlLiderGeneralAccess = false;
    private boolean auditLiderAccess = false;
    private boolean auditHelperAccess = false;
    private String windowTitle;

    @Override
    public String execute() throws Exception {
        String result = super.defaultActionExecute();
        if (!SUCCESS.equals(result)) {
            return result;
        }
        if (!parameter("pendingCamelName").equals("") && !isPendingValid()) {
            return "attended";
        }
        windowTitle = parameter("windowTitle");
        if (windowTitle == null || windowTitle.isEmpty()) {
            windowTitle = "Cuestionario";
        }
        if (Utilities.isInteger(getId()) && isAudit()) {
            //si es una auditoria
            getLogger().trace("es auditoria! ", getId());
            //si tengo permiso de responder
            IOutstandingSurveysDAO daoPendiente
                    = Utilities.getBean(IOutstandingSurveysDAO.class);
            IAuditIndividualDAO daoAuditInd = Utilities.getBean(IAuditIndividualDAO.class);
            AuditIndividualPending aud = daoAuditInd.HQLT_findById(AuditIndividualPending.class, Long.valueOf(getId()));
            if (aud == null) {
                getLogger().warn("ID de auditoria invalido!");
                return NONE;
            }
            setAuditIndividualId(getId());
            setAuditIndividualStatus(aud.getStatus().toString());
            setAuditIndividualCode(aud.getCode());
            if (AuditType.PROCESS_SCOPE.equals(aud.getAudit().getType().getScope())) {
                setAuditIndividualDepartment(aud.getBusinessUnitDepartment().getDescription());
                setBusinessUnitDepartmentId(aud.getBusinessUnitDepartment().getId());
                setBusinessUnitId(aud.getBusinessUnitDepartment().getBusinessUnitId());
            } else if (AuditType.AREA_SCOPE.equals(aud.getAudit().getType().getScope())) {
                if (aud.getAreaId() == null) {
                    getLogger().error("Missing area id for audit {}", aud.getId());
                } else {
                    Map<String, Object> d = daoAuditInd.HQL_findSimpleMap(""
                        + " SELECT new map("
                            + "d.id AS businessUnitDepartmentId"
                            + ", d.description AS description"
                            + ", d.businessUnitId AS businessUnitId"
                        + " )"
                        + " FROM " + Area.class.getCanonicalName() + " c "
                        + " JOIN c.department d "
                        + " WHERE c.id = :areaId",
                        ImmutableMap.of("areaId", aud.getAreaId())
                    );
                    setAuditIndividualDepartment((String) d.get("description"));
                    setBusinessUnitDepartmentId((Long) d.get("businessUnitDepartmentId"));
                    setBusinessUnitId((Long) d.get("businessUnitId"));
                }
            }
            getLogger().trace("-- getLoggedUserId : ", getLoggedUserId());
            getLogger().trace("-- aud : ", aud);
            if (getSession().get("superAudit"+aud.getAudit().getBusinessUnit().getId()) == null) {
                getSession().put("superAudit"+aud.getAudit().getBusinessUnit().getId(),
                        Utilities.getBean(IUserDAO.class).getUserServicesByUne(getLoggedUserId(),aud.getAudit().getBusinessUnit().getId())
                        .contains(ProfileServices.AUDIT_QUALITY_MANAGER));
            }
            boolean superUser = (Boolean) getSession().get("superAudit"+aud.getAudit().getBusinessUnit().getId());
            auditlLiderGeneralAccess =  getLoggedUserId().equals(aud.getAudit().getAttendantId())
                    || superUser;
            auditLiderAccess = getLoggedUserId().equals(aud.getAttendant().getId());
            auditHelperAccess = daoAuditInd.isAuditHelper(aud.getId(), getLoggedUserId());
            if(AuditType.PROCESS_SCOPE.equals(aud.getAudit().getType().getScope())){
                super.setAuditedAccess(getLoggedUserId().equals(aud.getDepartmentProcess().getAttendantId()));
            }
            if (AuditType.AREA_SCOPE.equals(aud.getAudit().getType().getScope())) {
                if (aud.getArea() != null) {
                    super.setAuditedAccess(getLoggedUserId().equals(aud.getArea().getAttendantId()));
                } else {
                    getLogger().error("Missing area id for audit {}", aud.getId());
                }
            }
            handleAccessRules(aud);
            if (isAuditedAccess()) {
                if(Utilities.getSettings().getAuditedViewSurvey().equals(0)){
                    if(!aud.getStatus().equals(AuditIndividual.STATUS.DONE.getValue())
                        && !aud.getStatus().equals(AuditIndividual.STATUS.ACCEPTED.getValue())){
                        setStatus(AuditIndividual.STATUS.PLANNED_TO_CONFIRM.toString());
                        return getStatus();
                    }
                }else{
                    super.setPreviewMode(true);
                }
            }
            if (aud.getStatus().equals(AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue()) && aud.getActualStart() == null) {
                aud.update("actualStart", new Date());
                if  (aud.getAudit().getActualStart() == null) {
                    aud.getAudit().update("actualStart", aud.getActualStart());
                }
            }
            setFindingCatalogsJSON(ActionSources.MODULE_AUDITS);
            if (isAuditedAccess() || (
                    (auditlLiderGeneralAccess || auditLiderAccess || auditHelperAccess)
                    && (
                        aud.getStatus().equals(AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue())
                        || aud.getStatus().equals(AuditIndividual.STATUS.DONE.getValue())
                        || aud.getStatus().equals(AuditIndividual.STATUS.ACCEPTED.getValue())))) {
                //si la auditoria se encuentra en un status digno de auditarse...
                if (aud.getFill() == null) {
                    getLogger().debug("Creando nuevo registro de llenado para '" + aud.getCode() + "' ... ");
                    final OutstandingSurveys outstandingSurveys = new OutstandingSurveys(new Survey(aud.getSurveyId()), getLoggedUserId());
                    setPendiente(outstandingSurveys);
                    setPendiente(daoPendiente.makePersistent(getPendiente(), getLoggedUserId()));
                    aud.setFill(new OutstandingSurveysRef(getPendiente().getId()));
                    daoAuditInd.makePersistent(aud, getLoggedUserId());
                } else {
                    getLogger().trace("Ya existe un registro de llenado para '{}' :) ... es {}", aud.getCode(), aud.getFill().getId());
                    setPendiente(daoPendiente.HQLT_findById(aud.getFill().getId()));
                }
                setLoadAction("Audit.Survey.Capture");
                setId("O" + getPendiente().getId());
                setSerializedOutstandingSurvey(Utilities.getSerializedObj(getPendiente()));
            } else {
                if (TASK_GOING_TO_PREVIEW.equals(getTask())) {
                    setDisabled(true);
                }
                //si la auditoria NO se encuentra en un status digno de auditarse, muestra el cuestionario vacio (vista previa)...
                setId("" + aud.getAudit().getSurveyId());
            }
        }
        return super.execute();
    }
    private void handleAccessRules(AuditIndividualPending aud) {

        if((auditLiderAccess || auditHelperAccess) && isAuditedAccess()) {
            switch(aud.getStatus()) {
                case AuditIndividual.STATUS_ACTIVE_CONFIRMED:   //confirmada / por realizar
                    if(getTask().equals(TASK_GOING_TO_FILL)) {
                        setAuditedAccess(false);
                    }
                    break;
                case AuditIndividual.STATUS_DONE:               //aceptada / por aceptar resultados
                    if(getTask().equals(TASK_GOING_TO_ACCEPT)) {
                        auditlLiderGeneralAccess = false; //<--- useless
                        auditLiderAccess = false; //<--- useless
                        auditHelperAccess = false;//<--- useless
                    }
                    break;
            }
        }
        if (getTask().equals(TASK_GOING_TO_PREVIEW) && aud.getStatus() < AuditIndividual.STATUS.DONE.getValue()) {
            //Si se accede como vista previa no se puede responder
            auditlLiderGeneralAccess = false;
            auditLiderAccess = false;
            auditHelperAccess = false;
        } else if (getTask().equals(TASK_GOING_TO_PREVIEW)) {
            //si el cuestionario ya se lleno quita el mensaje de "vista previa"
            setTask(TASK_GOING_TO_READONLY_ANSWERS);
        } else if (getTask().equals(TASK_GOING_TO_READONLY_ANSWERS)) {
            //si se accesede directa a "progress" puedes ver el avance sin importar si eres auditado
            setAuditedAccess(true);
        }
    }

    @Override
    protected Module getModule() {
        return Module.AUDIT;
    }

    /**
     * @return the windowTitle
     */
    public String getWindowTitle() {
      return windowTitle;
    }

    /**
     * @param windowTitle the windowTitle to set
     */
    @StrutsParameter
    public void setWindowTitle(String windowTitle) {
      this.windowTitle = windowTitle;
    }
    
}
