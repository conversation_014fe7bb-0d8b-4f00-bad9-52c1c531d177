package DPMS.Action;

import DPMS.Mapping.SupportedLanguages;
import DPMS.Mapping.User;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

public class Action_Preferences extends DefaultAction {
    
    private List<ITextHasValue> supportedLanguages;
    private boolean allowSwitchSearchInSubfolders;
    private boolean searchInSubfolders;
    private boolean showExternalDialog;
    private boolean showWelcomeDialog;
    private boolean isAway;
    private Integer legacyOpenFilterSelected;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        supportedLanguages = dao.getStrutsComboList(SupportedLanguages.class, "description", "c.id IN (" + LicenseUtil.filterSupportedLanguages() + ")");
        supportedLanguages.add(0, new TextHasValue(getTag("selectAnOption"), ""));
        allowSwitchSearchInSubfolders = Utilities.getSettings().getAllowSwitchSearchInSubfolders() == 1;
        legacyOpenFilterSelected = Integer.valueOf(getSession().get("legacyOpenFilters") + "");
        searchInSubfolders = dao.HQL_findSimpleInteger(
                "SELECT c.searchInSubfolders FROM " + User.class.getCanonicalName() + " c WHERE c.id = " + getLoggedUserId()
        ).equals(1);
        showExternalDialog = dao.HQL_findSimpleInteger(
                "SELECT c.showExternalDialog FROM " + User.class.getCanonicalName() + " c WHERE c.id = " + getLoggedUserId()
        ).equals(1);
        showWelcomeDialog = dao.HQL_findSimpleInteger(
                "SELECT c.showWelcomeDialog FROM " + User.class.getCanonicalName() + " c WHERE c.id = " + getLoggedUserId()
        ).equals(1);
        isAway = dao.HQL_findSimpleBoolean(""
            + " SELECT u.isAway"
            + " FROM " + User.class.getCanonicalName() + " u "
            + " WHERE u.id = :userId", "userId", getLoggedUserId()
        );
        return super.execute();
    }

    public boolean isIsAway() {
        return isAway;
    }
    
    public List<ITextHasValue> getSupportedLanguages() {
        return supportedLanguages;
    }

    @StrutsParameter
    public void setSupportedLanguages(List<ITextHasValue> supportedLanguages) {
        this.supportedLanguages = supportedLanguages;
    }
    
    public boolean getAllowSwitchSearchInSubfolders() {
        return allowSwitchSearchInSubfolders;
    }
    public boolean getSearchInSubfolders() {
        return searchInSubfolders;
    }
    
    public boolean getShowExternalDialog() {
        return showExternalDialog;
    }
    
    public boolean getShowWelcomeDialog() {
        return showWelcomeDialog;
    }
    
    
    public List<ITextHasValue> getPritingDateFormats() {
        List<ITextHasValue> formats = new ArrayList<>(2);
        formats.add(new TextHasValue("dd/MM/yyyy", "dd/MM/yyyy"));
        formats.add(new TextHasValue("MM/dd/yyyy", "MM/dd/yyyy"));
        return formats;
 }
    
    public List<ITextHasValue> getPritingDateTimeFormats() {
        List<ITextHasValue> formats = new ArrayList<>(2);
        formats.add(new TextHasValue("dd/MM/yyyy HH:mm:ss", "dd/MM/yyyy HH:mm:ss"));
        formats.add(new TextHasValue("MM/dd/yyyy HH:mm:ss", "MM/dd/yyyy HH:mm:ss"));
        return formats;
    }

    public Integer getDocumentCodeScope() {
        return Utilities.getSettings().getDocumentCodeScope();
    }

    public Integer getLegacyOpenFilterSelected() {
        return legacyOpenFilterSelected;
    }

 }
