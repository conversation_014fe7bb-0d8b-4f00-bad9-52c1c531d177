package DPMS.Action;

import DPMS.DAOInterface.IOrganizationalUnitDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.OrganizationalUnit;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.TextLongValue;
import Framework.Config.UserHasValue;
import Framework.Config.Utilities;
import java.util.List;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class Action_OrganizationalUnit extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> predecessor;
    private List<UserHasValue> documentManager;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        predecessor = Utilities.getBean(IOrganizationalUnitDAO.class).getStrutsComboList(""
            + " c.status = "+OrganizationalUnit.ACTIVE_STATUS);
        predecessor.addFirst(new TextLongValue(SELECCIONE, null));
        
        documentManager = Utilities.getBean(IUserDAO.class).getUsersWithPosition();
        documentManager.addFirst(new UserHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<ITextHasValue> getPredecessor() {
        return predecessor;
    }

    @StrutsParameter
    public void setPredecessor(List<ITextHasValue> predecessor) {
        this.predecessor = predecessor;
    }

    public List<UserHasValue> getDocumentManager() {
        return documentManager;
    }

    @StrutsParameter
    public void setDocumentManager(List<UserHasValue> documentManager) {
        this.documentManager = documentManager;
    }
    
 }
