/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.Action;

import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.SequenceDetail;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.dto.draw2d.CustomElement;
import bnext.dto.draw2d.DetailSequenceDescriptor;
import bnext.dto.draw2d.Element;
import bnext.dto.draw2d.RecurrenceDescription;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Action_SequenceDiagram extends DefaultAction {

    private String diagramDescription;
    DetailSequenceDescriptor dsd = new DetailSequenceDescriptor();

    @Override
    public String execute() throws Exception {
        if (getId() == null) {
            return DefaultAction.ERROR;
        }
        List<Element> diagram = new ArrayList<>();
        List<SequenceDetail> list = Utilities.getUntypedDAO().HQLT_findByQuery(SequenceDetail.class, "FROM SequenceDetail c WHERE c.id.requestId = " + getId() + " ORDER BY c.id.indice");
        CustomElement e;
        int displacementCounter = 50;
        for (SequenceDetail tmp : list) {
            if (!containsSequenceDetail(tmp, diagram)) {
                e = CustomElementFromSequenceDetail(tmp);
                e.setX(displacementCounter);
                displacementCounter += 110;
                diagram.add(e);
            }
        }
        
        Integer size = diagram.size();
        
        dsd.setMaxIndex(size);
        dsd.setRecurenceDescriptions(this.getRecurrenceDescription(size.longValue()));
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        dsd.setHeaders(diagram);
        diagramDescription = mapper.writeValueAsString(dsd);
        return super.execute();
    }
    
    private static final String 
        QUERY_AUTORIZACIONES = ""
            + " SELECT apd.indice " 
            + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " apd "
            + " WHERE"
                + " apd.request.id = :rId " 
                + " AND ("
                    + " apd.status = " + AutorizationPoolDetails.ACCEPTED 
                    + " OR apd.status = " + AutorizationPoolDetails.SKIPPED 
                + " )" 
            + " ORDER BY apd.indice",
        // Si hay rechazos entonces generar dibujado con recurrencias 
        QUERY_DE_RECHAZOS = ""
            + " SELECT NEW MAP( "
                + "c.autorizationPoolIndex AS index, "
                + "c.invalidAutorizationPoolIndex AS invalidIndex )"
            + " FROM " 
                + AutorizationPoolDetailComment.class.getCanonicalName() + " c"
                + "," + AutorizationPoolDetails.class.getCanonicalName() + " apd " 
            + " WHERE "
                + " c.autorizationPoolDetailsId = apd.id " 
                + " AND apd.request.id = :rId "
                + " AND c.invalidAutorizationPoolIndex IS NOT NULL "
            + " ORDER BY c.recurrence,c.autorizationPoolIndex";

    private List<RecurrenceDescription> getRecurrenceDescription(Long maxIndex) {
        
        List<RecurrenceDescription> description = new ArrayList<>();
        IUntypedDAO dao = getUntypedDAO();
        Map<String,Object> params = new HashMap<>();
        params.put("rId", Long.parseLong(getId()));
        
        
        
        // Lista filtrada por rechazos
        List<Map> listaRechazos = dao.HQL_findByQuery(QUERY_DE_RECHAZOS,params);
        // Se rescatan las autorizaciones sin los comentarios sobre ellas
        List<Integer> listaAutorizaciones = dao.HQL_findByQuery(QUERY_AUTORIZACIONES,params);
        
        
        int rejectStart;
        int rejectEnd;
        
        if(!listaRechazos.isEmpty()){
  
            // Genera la primera linea de Autorizaciones
            rejectEnd = 1;
            
            // Este ciclo solo dibuja las autorizaciones intermedias basadas en rechazos
            for(int i=0,l=listaRechazos.size();i<l;i++){
                rejectStart = (int) listaRechazos.get(i).get("index");
                description.add(new RecurrenceDescription(rejectEnd, rejectStart));
                rejectEnd = (int) listaRechazos.get(i).get("invalidIndex");
                description.add(new RecurrenceDescription(rejectStart, rejectEnd));
            }
            
            /* VALIDAR QUE EL ULTIMO RECHAZO NO SEA EL MISMO */
            int start;
            int end;
            end = listaAutorizaciones.get(listaAutorizaciones.size() - 1);
            if(end > rejectEnd) {
                start = listaAutorizaciones.get(0);
                description.add(new RecurrenceDescription(start,end));
            }
            
        } else if (!listaAutorizaciones.isEmpty()) {
            // Dibujar sin el pool de comentarios AutorizationPoolDetailComment
            int start;
            int end;
            start = listaAutorizaciones.get(0);//3
            end = listaAutorizaciones.get(listaAutorizaciones.size() - 1);//3
            description.add(new RecurrenceDescription(start,end));
        }
        return description;
    }

    private CustomElement CustomElementFromSequenceDetail(SequenceDetail detail) {
        CustomElement element = new CustomElement();
        element.setId(makeId(detail));
        element.setType(CustomElement.OUTPUT_BOTTOM);
        element.setWidth(100);
        element.setHeight(50);
        element.setLabel(detail.getUserDescription());
        // Cuando las autorizaciones son por puesto entonces dejar etiqueta de PositionDescription
        if (element.getLabel() != null && element.getLabel().length() == 0 ){
            element.setLabel(detail.getPositionDescription());
        }
        
        return element;
    }

    private String makeId(SequenceDetail det) {
        return det.getId().getIndice() + "-" + det.getId().getRequestId();
    }

    private boolean containsSequenceDetail(SequenceDetail det, List<Element> list) {
        String id = makeId(det);
        for (Element e : list) {
            if (e.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }

    public String getDiagramDescription() {
        return diagramDescription;
    }

    public void setDiagramDescription(String diagramDescription) {
        this.diagramDescription = diagramDescription;
    }
}
