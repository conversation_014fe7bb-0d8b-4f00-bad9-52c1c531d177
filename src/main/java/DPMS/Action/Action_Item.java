package DPMS.Action;

import DPMS.DAOInterface.IItemDAO;
import DPMS.DAOInterface.ISectionDAO;
import DPMS.Mapping.Item;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR>
 */
public class Action_Item extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> sections;
    private List<ITextHasValue> parents;
    
    @Override
    public String execute() throws Exception {
        ISectionDAO dao = getBean(ISectionDAO.class);
        IItemDAO itemDAO = getBean(IItemDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long sectionId = null;
        Long parentId = null;
        if (getId() != null && !getId().equals("-1")) {
            Item item = itemDAO.HQLT_findById(Long.parseLong(getId()));
            sectionId = item.getSectionId();
            parentId = item.getParentId();
        }
        sections = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, sectionId, getServiciosActivos());
        parents = itemDAO.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, parentId, getServiciosActivos());
        sections.add(0, new TextHasValue(SELECCIONE, ""));
        parents.add(0, new TextHasValue(SELECCIONE, ""));

        return super.execute();
    }

    public List<ITextHasValue> getSections() {
        return sections;
    }

    @StrutsParameter
    public void setSections(List<ITextHasValue> sections) {
        this.sections = sections;
    }

    public List<ITextHasValue> getParents() {
        return parents;
    }

    @StrutsParameter
    public void setParents(List<ITextHasValue> parents) {
        this.parents = parents;
    }
    

}
