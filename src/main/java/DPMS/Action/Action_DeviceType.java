package DPMS.Action;

import DPMS.Mapping.DeviceGroup;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.DAO.IUntypedDAO;
import java.util.List;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

public class Action_DeviceType extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> deviceGroup;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IUntypedDAO DAO = getUntypedDAO();
        deviceGroup = DAO.getStrutsComboList(DeviceGroup.class, "description", ""
            + " c.status = "+DeviceGroup.ACTIVE_STATUS);
        deviceGroup.add(0, new TextHasValue(SELECCIONE, ""));        
        return super.execute();
    }

    public List<ITextHasValue> getDeviceGroup() {
        return deviceGroup;
    }

    @StrutsParameter
    public void setDeviceGroup(List<ITextHasValue> deviceGroup) {
        this.deviceGroup = deviceGroup;
    }

    }
