package DPMS.Action;

import Framework.Config.Utilities;
import Framework.Config.jasperreport;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.util.ClassLoaderUtil;
import net.sf.jasperreports.engine.DefaultJasperReportsContext;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.JasperRunManager;
import net.sf.jasperreports.engine.export.HtmlExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleHtmlExporterOutput;
import net.sf.jasperreports.j2ee.servlets.ImageServlet;
import net.sf.jasperreports.web.util.WebHtmlResourceHandler;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import qms.activity.entity.Activity;
import qms.framework.util.AboutApp;
import qms.framework.util.BnextResourceBundle;
import qms.framework.util.BnextResourceBundleFactory;
import qms.framework.util.SettingsUtil;
import qms.framework.util.URLUtils;
import qms.util.FileDownloader;

/**
 *
 * <AUTHOR>
 */
public class Action_Report extends FileDownloader {

    private ClassPathResource bnextClasspathResource = null;
    private InputStream bnextClasspath = null;
    private String ruta;
    private String bnextPath;
    private String intTipoExtencionReporte;
    private String html;
    private final jasperreport jasperReport;
    byte[] bytes = new byte[0];
    private String xlsFileName;
    private Map<String, Object> params;
    private Connection conn;
    private boolean disableInline;
    
    public static final String STATUS_REPORT =  "status_actions.jasper";
    
    static {
        DefaultJasperReportsContext.getInstance().setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        DefaultJasperReportsContext.getInstance().setProperty("net.sf.jasperreports.style.isPdfEmbedded", "true");
    }
    public Action_Report() {
        jasperReport = new jasperreport();
    }

    private boolean isValidParam(String name) {
        Map<String, String[]> map = getParameterMap();
        return map.containsKey(name)
                && map.get(name).length > 0
                && !map.get(name)[0].isEmpty();
    }

    private void toHTML(ByteArrayOutputStream byteArrayOutputStream, JasperPrint jasperPrint) throws JRException {
        final HtmlExporter exporter = new HtmlExporter();
	exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
	final SimpleHtmlExporterOutput output = new SimpleHtmlExporterOutput(byteArrayOutputStream);
	output.setImageHandler(new WebHtmlResourceHandler("../jasper-servlets/image?image={0}&uuid=" + UUID.randomUUID().toString()));
	exporter.setExporterOutput(output);
        exporter.exportReport();
        exporter.reset();
        bytes = byteArrayOutputStream.toByteArray();
    }

    private void toExcel(ByteArrayOutputStream byteArrayOutputStream, JasperPrint jasperPrint) throws JRException {
        JRXlsExporter exporterXLS = new JRXlsExporter();
        exporterXLS.setParameter(JRXlsExporterParameter.JASPER_PRINT, jasperPrint);
        exporterXLS.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
        exporterXLS.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
        exporterXLS.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);

        // This'll allows users to directly download the XLS report without
        // having to save XLS report on server.
        exporterXLS.setParameter(JRXlsExporterParameter.OUTPUT_STREAM, byteArrayOutputStream);
        exporterXLS.exportReport();
        exporterXLS.reset();
        bytes = byteArrayOutputStream.toByteArray();
    }
    
    private void toPDF(Map parameters) throws JRException {
        JasperReport jr = this.getJasperReport(params);
        if (jr == null) {
            String rutaArchivo = getFullReportPath();
            bytes = JasperRunManager.runReportToPdf(rutaArchivo, parameters, this.getConn());
        } else {
            bytes = JasperRunManager.runReportToPdf(jr, parameters, this.getConn());
        }
    }

    public Map<String, String[]> getParameterMap() {
        return request.getParameterMap();
    }

    private Map<String, Object> getParams() throws IllegalAccessException, InvocationTargetException, MalformedURLException {
        if (params == null) {
            params = new HashMap();
            org.apache.commons.beanutils.BeanUtils.populate(jasperReport, getParameterMap());
            jasperReport.fillParams();
            String[] llaves = jasperReport.getParamArrayKeysFromKeyHolder("jasperReport");

            getLogger().debug("------ llaves.length: {}", llaves.length);
            for (String llave : llaves) {
                getLogger().debug("------ llaves[i]: {}, {}", llave, jasperReport.getParamValue(llave));
                params.put(llave, jasperReport.getParamValue(llave));
            }
        }
        java.util.Date fecha = new java.util.Date();
        params.put("LEYENDA", "Powered by Bnext QMS, Block Networks S.A. de C.V.");
        params.put("FECHA_GENERADA", jasperReport.formatDate2(fecha));
        String bnext_filter = "";
        Map bnextParams = new HashMap();
        if (isValidParam("dteStart")) {
                String dateField = Framework.Config.Utilities.formatDateBy(
                    Framework.Config.Utilities.parseDateBy(getParameterMap().get("dteStart")[0], "yyyy-MM-dd"), "dd/MM/yyyy");
                bnext_filter += " AND "+ getDateField() +" >= CONVERT(DATE, '" + dateField + "', 103)";
                bnextParams.put("dteStart", dateField);
            }
        if (isValidParam("dteEnd")) {
                String dateField = Framework.Config.Utilities.formatDateBy(
                        Framework.Config.Utilities.parseDateBy(getParameterMap().get("dteEnd")[0], "yyyy-MM-dd"), "dd/MM/yyyy");
                bnext_filter += " AND " + getDateField() + " <= CONVERT(DATE, '" + dateField + "', 103)";
                bnextParams.put("dteEnd", Framework.Config.Utilities.formatDateBy(
                        Framework.Config.Utilities.parseDateBy(getParameterMap().get("dteEnd")[0], "yyyy-MM-dd"), "dd/MM/yyyy"));
            }
        if (isValidParam("businessUnitId")) {
            bnext_filter += " AND business_unit_id = " + getParameterMap().get("businessUnitId")[0];
            bnextParams.put("businessUnitId", getParameterMap().get("businessUnitId")[0]);
        }
        if (isValidParam("departmentId") && !getParameterMap().get("departmentId")[0].equals("-1")) {
            bnext_filter += " AND department_id = " + getParameterMap().get("departmentId")[0];
            bnextParams.put("departmentId", getParameterMap().get("departmentId")[0]);
        }
        if (isValidParam("processId")) {
            bnext_filter += " AND process_id = " + getParameterMap().get("processId")[0];
            bnextParams.put("processId", getParameterMap().get("processId")[0]);
        }
        if (isValidParam("sourceId") && !getParameterMap().get("sourceId")[0].equals("-1")) {
            bnext_filter += " AND source_id = " + getParameterMap().get("sourceId")[0];
            bnextParams.put("sourceId", getParameterMap().get("sourceId")[0]);
        }
        if(isValidParam("findingCode") &&  !getParameterMap().get("findingCode")[0].equals("")) {
            bnext_filter += " AND reference = '" + getParameterMap().get("findingCode")[0] + "'";
            bnextParams.put("findingCode", getParameterMap().get("findingCode")[0]);
        }
        if (ruta.endsWith(STATUS_REPORT)) {
            params = this.getParamStatusReport(bnext_filter);
        }
        
        params.put("bnext_params", bnextParams);
        final String url = SettingsUtil.getAppUrlNoSlash();
        final String bnextUrl = url + "/images/logo/bnext.png";
        /**QUITAR EL IF AL RESOLVER PROBLEMA DE: opening input stream from URL***/
        if (URLUtils.isReachable(bnextUrl)) {
            final Long reportLogoId = Utilities.getSettings().getReportLogoId();
            if (reportLogoId != null && reportLogoId > 0) {
                params.put("MAIN_LOGO_URL", url + "/view/v-default-report-logo.view");
            } else {
                params.put("MAIN_LOGO_URL", url + "/images/logo/qms-hd.png");
            }
            params.put("BNEXT_LOGO_URL", bnextUrl);
        } else if (URLUtils.isHttps(bnextUrl)) {
            getLogger().error(""
                    + "Error al cargar el logo de los reportes, no se soportan certificados inválidos"
                    + " o autofirmados en la URL del sitio. {}",
                    url
            );
        } else {
            getLogger().debug("Error opening input stream from URL: {}/view/v-report-logo.view", url);
        }
        params.put("auditoriareportegeneral.sistemacalidad", getTag("auditoriareportegeneral.sistemacalidad"));
        params.put("auditoriareportegeneral.page", getTag("auditoriareportegeneral.page"));
        params.put("auditoriareportegeneral.of", getTag("auditoriareportegeneral.of"));
        setResourceBundle();
        getLogger().debug("params < {} >", bnext_filter);

        return params;
    }

    private void setResourceBundle() {
        String resourceName = getReportName();
        String resourceFilePath = getFullReportPath().replace(".jasper", ".properties");
        File file = new File(resourceFilePath);
        if (file.exists()) {
            try {
                URL[] urls = {file.getParentFile().toURI().toURL()};
                ClassLoader loader = new URLClassLoader(urls);
                params.put(JRParameter.REPORT_RESOURCE_BUNDLE,
                    BnextResourceBundle.getBundle(
                        resourceName,
                        new Locale(getSession().get("lang").toString(), getSession().get("locale").toString()),
                        loader,
                        new BnextResourceBundleFactory()
                    )
                );
            } catch (MalformedURLException ex) {
                getLogger().error("Error al cargar el archivo de propiedades del reporte con nombre: {}",
                        new Object[]{
                                resourceName, ex
                        }
                );
            }
        }
    }

    private String getReportName() {
        return (ruta != null ? ruta.substring(ruta.replaceAll("\\\\", "/").lastIndexOf("/") + 1): Utilities.EMPTY_STRING).replace(".jasper", Utilities.EMPTY_STRING);
    }

    public Map<String, Object> getParamStatusReport(String bnext_filter){
        String actionType = getParameterMap().get("selectedReportType")[0];
        if(actionType.equals("completed")){
            bnext_filter  = bnext_filter + " "
            + " AND c.activity_status IN ("
                + Activity.STATUS.IMPLEMENTED.getValue() + ","
                + Activity.STATUS.VERIFIED.getValue()
            + ") ";
        }
        final String appUrl = SettingsUtil.getAppUrlNoSlash();
        final String fullUrl = appUrl + "/view/v-default-report-logo.view";
        /**QUITAR EL IF AL RESOLVER PROBLEMA DE: opening input stream from URL***/
        if (URLUtils.isReachable(fullUrl)) {
            final Long reportLogoId = Utilities.getSettings().getReportLogoId();
            if (reportLogoId != null && reportLogoId > 0) {
                params.put("param_logo_url", fullUrl);
            }
        } else if (URLUtils.isHttps(fullUrl)) {
            getLogger().error(""
                    + "Error al cargar el logo de los reportes, no se soportan certificados inválidos"
                    + " o autofirmados en la URL del sitio. {}",
                    appUrl
            );
        }  else {
            getLogger().error("Error al abrir el logo");
        }
        params.put("param_system_color", Utilities.getSettings().getSystemColor());
        params.put("param_dateStart", getParameterMap().get("dteStart")[0]);
        params.put("param_dateEnd", getParameterMap().get("dteEnd")[0]);
        params.put("param_filterQuery", bnext_filter);
        return params;
    }

    public String getDateField(){
        return "dte_start";
    }
    
    public String pathUrl(){
        return "DPMS";
    }    
    
    public Connection getConn() {
        if (conn == null) {
            jasperReport.connect1();
            conn = jasperReport.getConnection();
        }
        return conn;
    }

    private void makeReport() throws IOException, JRException, IllegalAccessException, InvocationTargetException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        Map parameters = this.getParams();
        JasperPrint jasperPrint = null;
        getLogger().debug("Inicia generacion de reporte en Jasper");
        if ("HTML".equals(intTipoExtencionReporte)) {
            jasperPrint = this.getJasperPrint(parameters);
            jasperPrint.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
            jasperPrint.setProperty("net.sf.jasperreports.export.html.frames.as.nested.tables", "false");
            jasperPrint.setProperty("net.sf.jasperreports.export.html.using.images.to.align", "false");
            getSession().put(ImageServlet.DEFAULT_JASPER_PRINT_SESSION_ATTRIBUTE, jasperPrint);
            this.toHTML(byteArrayOutputStream, jasperPrint);
        } else if ("PDF".equals(intTipoExtencionReporte)) {
            this.toPDF(parameters);
        } else if ("EXCEL".equals(intTipoExtencionReporte)) {
            jasperPrint = this.getJasperPrint(parameters);
            jasperPrint.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
            this.toExcel(byteArrayOutputStream, jasperPrint);
        }
        String filename = ruta != null ? ruta.substring(ruta.lastIndexOf("/") + 1): "";
        filename = filename.isEmpty() ? "" : filename.substring(0, filename.lastIndexOf("."));
        filename = "Report_" + Utilities.todayDateBy("dd-MM-yyyy-HH:mm:SS") + " (" + filename + ")";
        xlsFileName = filename;
        getLogger().debug("Termina generacion de reporte en Jasper");
        if (!response.isCommitted()) {
            response.resetBuffer();
        }
    }

    private String printReport() throws IOException {
        getLogger().debug("Imprimiendo reporte");
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("bytes.length: {}", bytes.length);
        }
        if (bytes.length < 1001) {
            getLogger().error("El tamaño del archivo generado indica un reporte vacio: {} bytes", bytes.length);
            return ERROR;
        }
        if ("HTML".equals(intTipoExtencionReporte)) {
            response.setContentType("text/html");
            response.setCharacterEncoding("UTF-8");
            this.html = new String(bytes, StandardCharsets.UTF_8);
        } else if ("PDF".equals(intTipoExtencionReporte)) {
            response.setContentType(FileUtils.CONTENT_TYPE_PDF);
            final ContentDisposition contentHeader = ContentDisposition
                    .builder(this.disableInline ? "attachment": "inline;attachment")
                    .filename(xlsFileName + ".pdf")
                    .build();
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentHeader.toString());
        } else if ("EXCEL".equals(intTipoExtencionReporte)) {
            if (bytes.length < 4610) {
                getLogger().error("El tamaño del archivo generado indica un reporte vacio: {} bytes", bytes.length);
                return ERROR;
            } else {
                response.setContentType("application/vnd.ms-excel");
                final ContentDisposition contentHeader = ContentDisposition
                        .builder("attachment")
                        .filename(xlsFileName + ".xls")
                        .build();
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, contentHeader.toString());
            }
        }
        response.setContentLength(bytes.length);
        response.resetBuffer();
        try (final ServletOutputStream ouputStream = response.getOutputStream()) {
            ouputStream.write(bytes, 0, bytes.length);
            ouputStream.flush();
        }
        return null;
    }

    @Override
    public String execute() throws Exception {
        Cookie cookie = null;
        if (getToken() != null) {
            handleCookie(cookie);
        }
        try {
            String r = super.execute();
            if (!SUCCESS.equals(r)) {
                return r;
            }
            this.makeReport();
            return this.printReport();
        } catch (Exception e) {
            getDownloadResponse().put("status", ERROR);
            getDownloadResponse().put("error", e.getLocalizedMessage());
            getLogger().error("Falla al momento de generar el reporte ", e);
        } finally {
            if(getBnextIreportResource() != null) {
                getBnextIreportResource().close();
            }
            if (conn != null){
                conn.close();
            }
        }
        return ERROR;
    }

    protected JasperPrint getJasperPrint(Map<String, Object> parameters) throws JRException {
        if (getBnextIreportResource() == null) {
            getLogger().info("JasperPrint by fullpath!");
            return JasperFillManager.fillReport(getFullReportPath(), parameters, getConn());
        } else {
            getLogger().info("JasperPrint by inputstream!");
            return JasperFillManager.fillReport(getBnextIreportResource(), parameters, getConn());
        }
    }
     protected JasperReport getJasperReport(Map<String,Object> parameters) throws JRException{
        return null;
     }
    
    protected String getFullReportPath() {
        final String classPath = ClassLoaderUtil.getAppFolder(AboutApp.getAppName());
        String filePath = (classPath + "\\" + ruta).replace("/", "\\").replace("\\\\", "\\");
        getLogger().debug("/La ruta del archivo es = {}", filePath);
        return filePath;
    }

    public String getRuta() {
        return ruta;
    }

    public void setRuta(String ruta) {
        this.ruta = ruta;
    }

    public String getIntTipoExtencionReporte() {
        return intTipoExtencionReporte;
    }

    public void setIntTipoExtencionReporte(String intTipoExtencionReporte) {
        this.intTipoExtencionReporte = intTipoExtencionReporte;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public void setConn(Connection conn) {
        this.conn = conn;
    }

    public InputStream getBnextIreportResource() {
        if(bnextPath == null) {
            return null;
        }
        if(bnextClasspath == null) {
            try {
                bnextClasspath = getBnextClasspathResource().getInputStream();
            } catch(Exception e) {
                getLogger().error("Falla al obtener recurso de reporte {} {}", new Object[] {bnextPath, e});
                return null;
            }
        }
        return bnextClasspath;
    }

    public ClassPathResource getBnextClasspathResource() {
        if(bnextClasspathResource == null) {
            bnextClasspathResource = new ClassPathResource("bnext/ireports/" + bnextPath);
        }
        return bnextClasspathResource;
    }
    
    /**
     * Settear con la ruta relativa (Classpath) del archivo JASPER,
     * implementar cuando no se encuentren los archivos JASPER en la ruta normal del servidor
     * 
     * @param bnextPath 
     */
    public void setBnextPath(String bnextPath) {
        this.bnextPath = bnextPath;
    }

    public Boolean setDisableInline(Boolean disableInline) {
        return this.disableInline = disableInline;
    }

    public Boolean getDisableInline() {
        return this.disableInline;
    }
    
}
