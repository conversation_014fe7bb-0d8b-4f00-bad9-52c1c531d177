package DPMS.Action;

import DPMS.DAOInterface.IDocumentDAO;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import qms.document.core.DocumentSimpleAction;

/**
 *
 * <AUTHOR>
 */
public class Action_DocumentListAE extends DocumentSimpleAction {

    private String businessUnitsArray;
    private Long nodo;
    
    private Boolean documentEditor;
    private Boolean documentManager;
    private Boolean documentReader;

    @Override
    public String execute() throws Exception {
        IDocumentDAO dao = getBean(IDocumentDAO.class);
        final Boolean adminAccess = isAdmin()
                || getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        documentManager = getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO);
        documentReader = getLoggedUserServices().contains(ProfileServices.DOCUMENTO_LECTOR);
        documentEditor = getLoggedUserServices().contains(ProfileServices.DOCUMENTO_EDITOR);
        if(!dao.hasPermission(getLoggedUserId(), nodo, adminAccess, documentManager)) {
            return NO_ACCESS;
        }
        List<Long> businessUnits = dao.HQL_findByQuery("SELECT c.id FROM DPMS.Mapping.BusinessUnit c WHERE c.documentManagerId = :id", "id", this.getUserId());
        this.businessUnitsArray = businessUnits.toString();
        return super.execute();
    }

    /**
     * @return the businessUnitsArray
     */
    public String getBusinessUnitsArray() {
        return businessUnitsArray;
    }

    /**
     * @param businessUnitsArray the businessUnitsArray to set
     */
    @StrutsParameter
    public void setBusinessUnitsArray(String businessUnitsArray) {
        this.businessUnitsArray = businessUnitsArray;
    }

    @StrutsParameter
    public void setNodo(Long nodo) {
        this.nodo = nodo;
    }

    public Long getNodoPadre() {
        IUntypedDAO DAO = Utilities.getUntypedDAO();
        return DAO.HQL_findSimpleLong("SELECT c.parent FROM DPMS.Mapping.NodeSimple c WHERE c.id = " + this.nodo);
    } 

    public Boolean getDocumentEditor() {
        return documentEditor;
    }

    public void setDocumentEditor(Boolean documentEditor) {
        this.documentEditor = documentEditor;
    }

    public Boolean getDocumentManager() {
        return documentManager;
    }

    public void setDocumenManager(Boolean documenManager) {
        this.documentManager = documenManager;
    }

    public Boolean getDocumentReader() {
        return documentReader;
    }

    public void setDocumentReader(Boolean documenReader) {
        this.documentReader = documenReader;
    }
    
}
