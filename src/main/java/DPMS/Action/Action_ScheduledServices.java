package DPMS.Action;

import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR>
 */
public class Action_ScheduledServices extends DefaultAction {
    private String status = "";
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        return super.execute();
    }

    public String getStatus() {
        return status;
    }

    @StrutsParameter
    public void setStatus(String status) {
        this.status = status;
    }
    
    public boolean isSchedulingToApprove() {
        return Utilities.getSettings().getSchedulingToApprove().equals(1);
    }
 }
