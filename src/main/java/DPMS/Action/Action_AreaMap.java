package DPMS.Action;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.Mapping.AreaMap;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR> Lares
 */
public class Action_AreaMap extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> areas;
    @Override
    public String execute() throws Exception {
        IAreaDAO dao = getBean(IAreaDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long areaId = null;
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            AreaMap areaMap = dao.HQLT_findById(AreaMap.class,Long.decode(getId()));
            areaId = areaMap.getAreaId();
        }
        areas = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, areaId, getServiciosActivos());
        areas.add(0, new TextHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<ITextHasValue> getAreas() {
        return areas;
    }

    @StrutsParameter
    public void setAreas(List<ITextHasValue> areas) {
        this.areas = areas;
    }
    

}
