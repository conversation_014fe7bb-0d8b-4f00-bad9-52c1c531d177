package DPMS.Action;

import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import java.io.IOException;
import java.sql.SQLException;
import mx.bnext.core.errors.ClientAbortException;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import qms.framework.file.FileManager;

/**
 *
 * <AUTHOR>
 */
public class Action_FilePdfPage extends SessionViewer {

    private static final long serialVersionUID = 1L;

    private Long fileId = null;
    private Integer pageNum;
    private Long pdfPageId = null;
    private Integer rotationDegrees = 0;

    @Override
    public String execute() throws IOException, InterruptedException, SQLException {
        final FileManager fileManager = new FileManager();
        if (null != pdfPageId && pdfPageId > 0) {
            try {
                fileManager.writePageToResponse(
                        pdfPageId,
                        fileId,
                        rotationDegrees,
                        response,
                        getLoggedUserId()
                );
                return null;
            } catch (final ClientAbortException ex) {
                getLogger().debug(
                        "Client cancelled request for download pdf page with id {} and file id {}. Details: {}",
                        pdfPageId, fileId, ex.getMessage()
                );
                return null;
            }
        }
        if (fileId == null || fileId == -1L) {
            fileId = Utilities.getSettings().getDefaultFileId();
        }
        try {
            fileManager.writePageToResponse(
                    fileId,
                    pageNum,
                    rotationDegrees,
                    response,
                    getLoggedUserId()
            );
        } catch (final ClientAbortException ex) {
            getLogger().debug(
                    "Client cancelled request for download file with id {}. Details: {}",
                    fileId, ex.getMessage()
            );
            return null;
        }
        return null;
    }

    /**
     * @param fileId the fileId to set
     */
    @StrutsParameter
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }


    /**
     * @param pageNum the pagepageNumNumber to set
     */
    @StrutsParameter
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    @StrutsParameter
    public void setPdfPageId(Long pdfPageId) {
        this.pdfPageId = pdfPageId;
    }
    
    @StrutsParameter
    public void setRotationDegrees(Integer rotationDegrees) {
        this.rotationDegrees = rotationDegrees;
    }

}
