/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.Mapping;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@LocalizedEntity
@Table(name = "internal_document_catalog")
public class InternalDocumentCatalog extends StandardEntity<InternalDocumentCatalog> implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String CODE_PREFIX = "IDC-";
    private String description;
    private Integer status;
    private Integer deleted;
    private Set<InternalDocumentCatalogItem> internalDocumentCatalogItems;
    private String code;

    public InternalDocumentCatalog() {
    }

    public InternalDocumentCatalog(Long internalDocumentCatalogId) {
        this.id = internalDocumentCatalogId;
    }

    public InternalDocumentCatalog(Long internalDocumentCatalogId, String vchDescription, Integer intStatus, Integer intDeleted) {
        this.id = internalDocumentCatalogId;
        this.description = vchDescription;
        this.status = intStatus;
        this.deleted = intDeleted;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "internal_document_catalog_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long internalDocumentCatalogId) {
        this.id = internalDocumentCatalogId;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "vch_description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String vchDescription) {
        this.description = vchDescription;
    }

    @Override
    @Basic(optional = false)
    @Column(name = "vch_code")
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic(optional = false)
    @Column(name = "int_status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer intStatus) {
        this.status = intStatus;
    }

    @Basic(optional = false)
    @Column(name = "int_deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer intDeleted) {
        this.deleted = intDeleted;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(mappedBy = "internalDocumentCatalog",fetch = FetchType.EAGER)
    public Set<InternalDocumentCatalogItem> getInternalDocumentCatalogItems() {
        return internalDocumentCatalogItems;
    }

    public void setInternalDocumentCatalogItems(Set<InternalDocumentCatalogItem> internalDocumentCatalogItems) {
        this.internalDocumentCatalogItems = internalDocumentCatalogItems;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof InternalDocumentCatalog)) {
            return false;
        }
        InternalDocumentCatalog other = (InternalDocumentCatalog) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.InternalDocumentCatalog[ internalDocumentCatalogId=" + id + " ]";
    }

}
