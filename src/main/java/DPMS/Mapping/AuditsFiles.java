/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "audits_files")
public class AuditsFiles extends CompositeStandardEntity<AuditsFilesPK> implements ILinkedCompositeEntity<AuditsFilesPK>, ILinkedComposityGrid<AuditsFilesPK>, Serializable{

    private AuditsFilesPK id;

    public AuditsFiles() {
    }

    public AuditsFiles(AuditsFilesPK id) {
        this.id = id;
    }
    
    public AuditsFiles(Long fileId, Long auditId) {
        this.id = new AuditsFilesPK(fileId, auditId);
    }
    
    @Override
    public AuditsFilesPK identifuerValue() {
        return id;
    }

    @Override
    @EmbeddedId
    public AuditsFilesPK getId() {
       return id;
    }

    @Override
    public void setId(AuditsFilesPK id) {
        this.id = id;
    }
    
}
