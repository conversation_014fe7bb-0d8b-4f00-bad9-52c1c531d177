package DPMS.Mapping;

import Framework.Config.StandardEntity;
import ape.pending.core.ApeTable;
import ape.pending.core.StrongBaseTypedAPE;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import qms.audit.entity.IAuditIndividual;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = AuditIndividualList.AUDIT_INDIVIDUAL_LIST)
@ApeTable(name = "audit_individual")
public class AuditIndividualList extends StandardEntity<AuditIndividual> implements StrongBaseTypedAPE<AuditType>, Serializable, IAuditIndividual {

    public static final String AUDIT_INDIVIDUAL_LIST = "("
            + " SELECT"
                + " pro_att.first_name AS audited,"
                + " pro.attendant_id AS audited_id,"
                + " team_leader.first_name AS audit_team_leader,"
                + " au.attendant_id AS audit_team_leader_id,"
                + " lider.first_name AS lider,"
                + " ind.attendant_id AS lider_id,"
                + " au.anticipation,"
                + " bu.description AS bud_description,"
                + " bu.business_unit_id,"
                + " pro.title AS department_process_description,"
                + " NULL AS area_description,"
                + " typ.scope,"
                + " typ.description AS audit_type_description,"
                + " fill.STATUS AS fill_status,"
                + " ind.audit_individual_id,"
                + " ind.status,"
                + " ind.deleted,"
                + " ind.code,"
                + " ind.description,"
                + " ind.dte_start,"
                + " ind.dte_end,"
                + " ind.tmp_start,"
                + " ind.tmp_end,"
                + " ind.audit_id,"
                + " ind.business_unit_department_id,"
                + " ind.OUTSTANDING_SURVEYS_ID,"
                + " ind.department_process_id,"
                + " ind.dte_start_request,"
                + " ind.dte_end_request,"
                + " ind.tmp_start_request,"
                + " ind.tmp_end_request,"
                + " ind.reason,"
                + " dbo.GROUP_CONCAT_D(DISTINCT help_user.user_id, ',') AS helpers_ids,"
                + " dbo.GROUP_CONCAT_D(DISTINCT help_user.first_name, ',') AS helpers_names,"
                + " ind.decline_reason,"
                + " ind.building_id,"
                + " ind.area_id,"
                + " ind.minimum_score,"
                + " ind.survey_id,"
                + " ind.support_staff,"
                + " ind.technical_experts,"
                + " ind.auditors_in_training,"
                + " ind.actual_start,"
                + " ind.actual_end,"
                + " ind.audit_type_id"
            + " FROM audit_individual ind"
            + " INNER JOIN department_process_view pro ON ind.department_process_id = pro.department_process_id"
            + " AND ind.department_process_id IS NOT NULL"
            + " INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id"
            + " INNER JOIN audits au ON ind.audit_id = au.audit_id"
            + " INNER JOIN users pro_att ON pro.attendant_id = pro_att.user_id"
            + " INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id"
            + " INNER JOIN users lider ON ind.attendant_id = lider.user_id"
            + " LEFT JOIN audit_individual_helpers help ON help.audit_individual_id = ind.audit_individual_id"
            + " LEFT JOIN users help_user ON help_user.user_id = help.helper_id"
            + " LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id"
            + " LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID"
            + " GROUP BY" 
                + " pro_att.first_name,"
                + " pro.attendant_id,"
                + " team_leader.first_name,"
                + " au.attendant_id,"
                + " lider.first_name,"
                + " ind.attendant_id,"
                + " au.anticipation,"
                + " bu.description,"
                + " bu.business_unit_id,"
                + " pro.title,"
                + " typ.scope,"
                + " typ.description,"
                + " fill.STATUS,"
                + " ind.audit_individual_id,"
                + " ind.status,"
                + " ind.deleted,"
                + " ind.code,"
                + " ind.description,"
                + " ind.dte_start,"
                + " ind.dte_end,"
                + " ind.tmp_start,"
                + " ind.tmp_end,"
                + " ind.audit_id,"
                + " ind.business_unit_department_id,"
                + " ind.OUTSTANDING_SURVEYS_ID,"
                + " ind.department_process_id,"
                + " ind.dte_start_request,"
                + " ind.dte_end_request,"
                + " ind.tmp_start_request,"
                + " ind.tmp_end_request,"
                + " ind.reason,"
                + " ind.decline_reason,"
                + " ind.building_id,"
                + " ind.area_id,"
                + " ind.minimum_score,"
                + " ind.survey_id,"
                + " ind.support_staff,"
                + " ind.technical_experts,"
                + " ind.auditors_in_training,"
                + " ind.actual_start,"
                + " ind.actual_end,"
                + " ind.audit_type_id"
            + " UNION ALL"
            + " SELECT "
                + " are_att.first_name AS audited,"
                + " are.attendant_id AS audited_id,"
                + " team_leader.first_name AS audit_team_leader,"
                + " au.attendant_id AS audit_team_leader_id,"
                + " lider.first_name AS lider,"
                + " ind.attendant_id AS lider_id,"
                + " au.anticipation,"
                + " bu.description AS bud_description,"
                + " bu.business_unit_id,"
                + " NULL AS department_process_description,"
                + " are.description AS area_description,"
                + " typ.scope,"
                + " typ.description AS audit_type_description,"
                + " fill.STATUS AS fill_status,"
                + " ind.audit_individual_id,"
                + " ind.status,"
                + " ind.deleted,"
                + " ind.code,"
                + " ind.description,"
                + " ind.dte_start,"
                + " ind.dte_end,"
                + " ind.tmp_start,"
                + " ind.tmp_end,"
                + " ind.audit_id,"
                + " ind.business_unit_department_id,"
                + " ind.OUTSTANDING_SURVEYS_ID,"
                + " ind.department_process_id,"
                + " ind.dte_start_request,"
                + " ind.dte_end_request,"
                + " ind.tmp_start_request,"
                + " ind.tmp_end_request,"
                + " ind.reason,"
                + " dbo.GROUP_CONCAT_D(DISTINCT help_user.user_id, ',') AS helpers_ids,"
                + " dbo.GROUP_CONCAT_D(DISTINCT help_user.first_name, ',') AS helpers_names,"
                + " ind.decline_reason,"
                + " ind.building_id,"
                + " ind.area_id,"
                + " ind.minimum_score,"
                + " ind.survey_id,"
                + " ind.support_staff,"
                + " ind.technical_experts,"
                + " ind.auditors_in_training,"
                + " ind.actual_start,"
                + " ind.actual_end,"
                + " ind.audit_type_id"
            + " FROM audit_individual ind"
            + " INNER JOIN area are ON ind.area_id = are.area_id AND ind.area_id IS NOT NULL"
            + " INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id"
            + " INNER JOIN audits au ON ind.audit_id = au.audit_id"
            + " INNER JOIN users are_att ON are.attendant_id = are_att.user_id"
            + " INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id"
            + " INNER JOIN users lider ON ind.attendant_id = lider.user_id"
            + " LEFT JOIN audit_individual_helpers help ON help.audit_individual_id = ind.audit_individual_id"
            + " LEFT JOIN users help_user ON help_user.user_id = help.helper_id"
            + " LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id"
            + " LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID"
            + " GROUP BY" 
                + " are_att.first_name,"
                + " are.attendant_id,"
                + " team_leader.first_name,"
                + " au.attendant_id,"
                + " lider.first_name,"
                + " ind.attendant_id,"
                + " au.anticipation,"
                + " bu.description,"
                + " bu.business_unit_id,"
                + " are.description,"
                + " typ.scope,"
                + " typ.description,"
                + " fill.STATUS,"
                + " ind.audit_individual_id,"
                + " ind.status,"
                + " ind.deleted,"
                + " ind.code,"
                + " ind.description,"
                + " ind.dte_start,"
                + " ind.dte_end,"
                + " ind.tmp_start,"
                + " ind.tmp_end,"
                + " ind.audit_id,"
                + " ind.business_unit_department_id,"
                + " ind.OUTSTANDING_SURVEYS_ID,"
                + " ind.department_process_id,"
                + " ind.dte_start_request,"
                + " ind.dte_end_request,"
                + " ind.tmp_start_request,"
                + " ind.tmp_end_request,"
                + " ind.reason,"
                + " ind.decline_reason,"
                + " ind.building_id,"
                + " ind.area_id,"
                + " ind.minimum_score,"
                + " ind.survey_id,"
                + " ind.support_staff,"
                + " ind.technical_experts,"
                + " ind.auditors_in_training,"
                + " ind.actual_start,"
                + " ind.actual_end,"
                + " ind.audit_type_id"
            + ")";
    
    private static final long serialVersionUID = 1L;
    
    private Date dteStart;
    private Date dteEnd;
    private Date tmpStart;
    private Date tmpEnd;
    
    private Date dteStartRequest;
    private Date dteEndRequest;
    private Date tmpStartRequest;
    private Date tmpEndRequest;
    private String reason;
    private String helpersIds;
    private String helpersNames;
    private String declineReason;
    
    private Long auditedId;
    private String audited;
    private Long liderId;
    private String lider;
    private Long auditTeamLeaderId;
    private String auditTeamLeader;
    private String businessUnitDepartmentDescription;
    private Long businessUnitId;
    private String departmentProcessDescription;
    private String areaDescription;
    private Integer scope;
    private String auditTypeDescription;
    private Integer fillStatus;
    private Short anticipation;
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Long buildingId;
    private Long areaId;
    private Date actualStart;
    private Date actualEnd;
    private Long typeId;
    
    
    

    public AuditIndividualList() {
    }

    public AuditIndividualList(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "audit_individual_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

     @Column(name = "audited")
    public String getAudited() {
        return audited;
    }

    public void setAudited(String audited) {
        this.audited = audited;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "building_id")
    public Long getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }

    @Basic(optional = true)
    @Column(name = "area_id")
    @Override
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Basic(optional = true)
    @Column(name = "dte_start")
    @Temporal(TemporalType.DATE)
    @Override
    public Date getDteStart() {
        return dteStart;
    }

    public void setDteStart(Date dteStart) {
        if(dteStart == null) {
            dteStart = this.dteStart;
        }
        this.dteStart = dteStart;
    }

    @Basic(optional = true)
    @Column(name = "dte_end")
    @Temporal(TemporalType.DATE)
    public Date getDteEnd() {
        return dteEnd;
    }

    public void setDteEnd(Date dteEnd) {
        if(dteEnd == null) {
            dteEnd = this.dteEnd;
        }
        this.dteEnd = dteEnd;
    }

    @Basic(optional = true)
    @Column(name = "tmp_start")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpStart() {
        return tmpStart;
    }

    public void setTmpStart(Date tmpStart) {
        if(tmpStart == null) {
            tmpStart = this.tmpStart;
        }
        this.tmpStart = tmpStart;
    }

    @Basic(optional = true)
    @Column(name = "tmp_end")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpEnd() {
        return tmpEnd;
    }

    public void setTmpEnd(Date tmpEnd) {
        if(tmpEnd == null) {
            tmpEnd = this.tmpEnd;
        }
        this.tmpEnd = tmpEnd;
    }

    @Basic(optional = true)
    @Column(name = "dte_start_request")
    @Temporal(TemporalType.DATE)
    public Date getDteStartRequest() {
        return dteStartRequest;
    }

    public void setDteStartRequest(Date dteStartRequest) {
        this.dteStartRequest = dteStartRequest;
    }

    @Basic(optional = true)
    @Column(name = "dte_end_request")
    @Temporal(TemporalType.DATE)
    public Date getDteEndRequest() {
        return dteEndRequest;
    }

    public void setDteEndRequest(Date dteEndRequest) {
        this.dteEndRequest = dteEndRequest;
    }

    @Basic(optional = true)
    @Column(name = "tmp_start_request")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpStartRequest() {
        return tmpStartRequest;
    }

    public void setTmpStartRequest(Date tmpStartRequest) {
        this.tmpStartRequest = tmpStartRequest;
    }

    @Basic(optional = true)
    @Column(name = "tmp_end_request")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpEndRequest() {
        return tmpEndRequest;
    }

    public void setTmpEndRequest(Date tmpEndRequest) {
        this.tmpEndRequest = tmpEndRequest;
    }
    
    @Basic(optional = true)
    @Column(name = "reason")
    public String getReason(){
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }

    @Column(name = "helpers_ids")
    public String getHelpersIds() {
        return helpersIds;
    }

    public void setHelpersIds(String helpersIds) {
        this.helpersIds = helpersIds;
    }

    @Column(name = "helpers_names")
    public String getHelpersNames() {
        return helpersNames;
    }

    public void setHelpersNames(String helpersNames) {
        this.helpersNames = helpersNames;
    }
    
    @Basic(optional = true)
    @Column(name = "decline_reason")
    public String getDeclineReason(){
        return declineReason;
    }
    
    public void setDeclineReason(String declineReason) {
        this.declineReason = declineReason;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof AuditIndividualList)) {
            return false;
        }
        AuditIndividualList other = (AuditIndividualList) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.AuditIndividualList[ id=" + id + " ]";
    }

    @Column(name = "audited_id")
    public Long getAuditedId() {
        return auditedId;
    }

    public void setAuditedId(Long auditedId) {
        this.auditedId = auditedId;
    }
    
    @Column(name = "lider_id")
    public Long getLiderId() {
        return liderId;
    }

    public void setLiderId(Long liderId) {
        this.liderId = liderId;
    }

    public String getLider() {
        return lider;
    }

    public void setLider(String lider) {
        this.lider = lider;
    }

    @Column(name = "audit_team_leader_id")
    public Long getAuditTeamLeaderId() {
        return auditTeamLeaderId;
    }

    public void setAuditTeamLeaderId(Long auditTeamLeaderId) {
        this.auditTeamLeaderId = auditTeamLeaderId;
    }

    @Column(name = "audit_team_leader")
    public String getAuditTeamLeader() {
        return auditTeamLeader;
    }

    public void setAuditTeamLeader(String auditTeamLeader) {
        this.auditTeamLeader = auditTeamLeader;
    }

    @Column(name = "bud_description")
    public String getBusinessUnitDepartmentDescription() {
        return businessUnitDepartmentDescription;
    }

    public void setBusinessUnitDepartmentDescription(String businessUnitDepartmentDescription) {
        this.businessUnitDepartmentDescription = businessUnitDepartmentDescription;
    }

    @Column(name = "department_process_description")
    public String getDepartmentProcessDescription() {
        return departmentProcessDescription;
    }

    public void setDepartmentProcessDescription(String departmentProcessDescription) {
        this.departmentProcessDescription = departmentProcessDescription;
    }

    @Column(name = "area_description")
    public String getAreaDescription() {
        return areaDescription;
    }

    public void setAreaDescription(String areaDescription) {
        this.areaDescription = areaDescription;
    }

    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    @Column(name = "fill_status")
    public Integer getFillStatus() {
        return fillStatus;
    }

    public void setFillStatus(Integer fillStatus) {
        this.fillStatus = fillStatus;
    }

    public Short getAnticipation() {
        return anticipation;
    }

    public void setAnticipation(Short anticipation) {
        this.anticipation = anticipation;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "audit_type_description")
    public String getAuditTypeDescription() {
        return auditTypeDescription;
    }

    public void setAuditTypeDescription(String auditTypeDescription) {
        this.auditTypeDescription = auditTypeDescription;
    }
    
    /**
     * @return the actualStart
     */
    @Column(name = "actual_start")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualStart() {
        return actualStart;
    }

    /**
     * @param actualStart the actualStart to set
     */
    public void setActualStart(Date actualStart) {
        this.actualStart = actualStart;
    }

    /**
     * @return the actualEnd
     */
    @Column(name = "actual_end")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualEnd() {
        return actualEnd;
    }

    /**
     * @param actualEnd the actualEnd to set
     */
    public void setActualEnd(Date actualEnd) {
        this.actualEnd = actualEnd;
    }

    @Column(name = "audit_type_id")
    @Override
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    @Transient
    @Override
    public Object getBusinessUnitDepartment() {
        return null;
    }

    @Transient
    @Override
    public Object getAudit() {
        return null;
    }

    @Transient
    @Override
    public AuditType getType() {
        return null;
    }
  
}
