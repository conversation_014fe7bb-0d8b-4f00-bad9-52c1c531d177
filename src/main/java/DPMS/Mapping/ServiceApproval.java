package DPMS.Mapping;
// Generated 26/07/2012 01:31:24 PM by Hibernate Tools 3.2.1.GA

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "service_approval")
public class ServiceApproval extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    public static final Integer NEWLY_CREATED = 0;
    public static final Integer APPROVED = 1;
    public static final Integer REJECTED = 2;
    
    private Long serviceId;
    private Long approvedBy;
    private Date approvedOn;
    private Integer status;
    private String comment;

    public ServiceApproval() {
    }

    public ServiceApproval(Long serviceId, Long approvedBy, Date approvedOn) {
        this.serviceId = serviceId;
        this.approvedBy = approvedBy;
        this.approvedOn = approvedOn;
    }

    @Id
    @Basic
    @Column(name = "service_approval_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long validationTypeId) {
        this.id = validationTypeId;
    }

    @Column(name = "service_id")
    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long scheduleId) {
        this.serviceId = scheduleId;
    }

    @Column(name = "approved_by")
    public Long getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Long approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Column(name = "approved_on")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getApprovedOn() {
        return approvedOn;
    }

    public void setApprovedOn(Date approvedOn) {
        this.approvedOn = approvedOn;
    }
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    @Column(name = "comment")
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3326
        if (!(object instanceof ServiceApproval)) {
            return false;
        }
        ServiceApproval other = (ServiceApproval) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.ServiceApproval[ Id=" + id + " ]";
    }

}
