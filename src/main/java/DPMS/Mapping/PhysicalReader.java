package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "physical_readers")
public class PhysicalReader extends CompositeStandardEntity<PhysicalReaderPK> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private PhysicalReaderPK id;
    private Integer status;
    private Date assignedDate = new Date();

    public PhysicalReader() {
    }

    public PhysicalReader(PhysicalReaderPK id) {
        this.id = id;
    }

    public PhysicalReader(Long documentId, Long positionId) {
        this.id = new PhysicalReaderPK(documentId, positionId);
    }

    @Override
    @EmbeddedId
    public PhysicalReaderPK getId() {
        return id;
    }

    @Override
    public PhysicalReaderPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(PhysicalReaderPK id) {
        this.id = id;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic(optional = true)
    @Column(name = "assigned_date", nullable = true)
    @Temporal(TemporalType.DATE)
    public Date getAssignedDate() {
        return assignedDate;
    }

    public void setAssignedDate(Date assignedDate) {
        this.assignedDate = assignedDate;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PhysicalReader other = (PhysicalReader) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PhysicalReader{" + "id=" + id + '}';
    }

}
