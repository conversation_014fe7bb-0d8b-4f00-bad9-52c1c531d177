package DPMS.Mapping;

import Framework.Config.FlujoPoolComparator;
import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.SQLRestriction;

import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "workflow")
public class Workflow extends StandardEntity<Workflow> implements Serializable {

    private Set<WorkflowPool> flujoPoolList;
    private BusinessUnitRef businessUnit;
    private OrganizationalUnitSimple organizationalUnit;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer scope;
    private Integer deleted = 0;
    private String module;
    private Long organizationalUnitId;

    public Workflow() {
    }

    public Workflow(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "workflow_id", nullable = false, precision = 19)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "scope")
    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(mappedBy = "flujo", fetch = FetchType.EAGER)
    @SQLRestriction("deleted=0")
    public Set<WorkflowPool> getFlujoPoolList() {
        if (flujoPoolList == null) {
            return null;
        }
        List<WorkflowPool> d = new ArrayList<>(flujoPoolList.size());
        d.addAll(flujoPoolList);
        Collections.sort(d, new FlujoPoolComparator());
        LinkedHashSet<WorkflowPool> s = new LinkedHashSet<>();
        s.addAll(d);
        return s;
    }

    public void setFlujoPoolList(Set<WorkflowPool> flujoPoolList) {
        this.flujoPoolList = flujoPoolList;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Workflow)) {
            return false;
        }
        Workflow other = (Workflow) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Workflow[ id=" + id + " ]";
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    public BusinessUnitRef getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitRef businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToOne
    @JoinColumn(name = "organizational_unit_id", referencedColumnName = "organizational_unit_id", updatable = false, insertable = false)
    public OrganizationalUnitSimple getOrganizationalUnit() {
        return organizationalUnit;
    }

    public void setOrganizationalUnit(OrganizationalUnitSimple organizationalUnit) {
        this.organizationalUnit = organizationalUnit;
        if (organizationalUnitId == null && organizationalUnit != null) {
            organizationalUnitId = organizationalUnit.getId();
        }
    }

    @Column(name = "organizational_unit_id")
    public Long getOrganizationalUnitId() {
        return organizationalUnitId;
    }

    public void setOrganizationalUnitId(Long organizationalUnitId) {
        this.organizationalUnitId = organizationalUnitId;
    }
    
    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }
    
}
