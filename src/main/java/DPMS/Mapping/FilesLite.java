package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.aspect.IExcludeLogging;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import mx.bnext.core.file.IFileData;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "files")
public class FilesLite extends DomainObject implements Serializable, IFileData, IAuditable, IExcludeLogging {

    private static final long serialVersionUID = 1L;

    private String description;
    private String extension;
    private String contentType;
    private Integer numberPages;
    private Integer busy = 0;
    private Long contentSize;   // <-- bytes
    private Long thumbnailSize;
    private Long pdfSize;
    private String contentSha512;
    private String thumbnailSha512;
    private String pdfSha512;
    private String docGuid;
    private String code;
    private Integer hasPdf = 0;
    private Integer hasPdfPages = 0;
    private Integer numberSavedPages;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Boolean hasPassword;
    private Boolean pdfConversionEnabled = true;
    private Boolean pdfImagesEnabled = true;

    public FilesLite() {
    }

    public FilesLite(Long id) {
        this.id = id;
    }

    public FilesLite(Long id, String fileName) {
        this.id = id;
        this.description = fileName;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "file_name")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String fileName) {
        this.description = fileName;
    }

    @Override
    @Basic(optional = false)
    @Column(name = "content_type")
    public String getContentType() {
        return contentType;
    }

    @Override
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Override
    @Column(name = "number_pages")
    public Integer getNumberPages() {
        return numberPages;
    }

    @Override
    public void setNumberPages(Integer numberPages) {
        this.numberPages = numberPages;
    }
    
    @Override
    @Column(name = "busy")
    public Integer getBusy() {
        return busy;
    }

    @Override
    public void setBusy(Integer busy) {
        this.busy = busy;
    }
    
    @Override
    @Column(name = "extension")
    public String getExtension() {
        return extension;
    }

    @Override
    public void setExtension(String extension) {
        this.extension = extension;
    }
    
    @Column(name = "content_size")
    @Override
    public Long getContentSize() {
        return contentSize;
    }

    @Override
    public void setContentSize(Long contentSize) {
        this.contentSize = contentSize;
    }

    @Column(name = "thumbnail_size")
    @Override
    public Long getThumbnailSize() {
        return thumbnailSize;
    }

    @Override
    public void setThumbnailSize(Long thumbnailSize) {
        this.thumbnailSize = thumbnailSize;
    }

    @Column(name = "pdf_size")
    @Override
    public Long getPdfSize() {
        return pdfSize;
    }

    @Override
    public void setPdfSize(Long pdfSize) {
        this.pdfSize = pdfSize;
    }

    @Column(name = "content_sha512")
    @Override
    public String getContentSha512() {
        return contentSha512;
    }

    @Override
    public void setContentSha512(String contentSha512) {
        this.contentSha512 = contentSha512;
    }

    @Column(name = "thumbnail_sha512")
    @Override
    public String getThumbnailSha512() {
        return thumbnailSha512;
    }

    @Override
    public void setThumbnailSha512(String thumbnailSha512) {
        this.thumbnailSha512 = thumbnailSha512;
    }

    @Column(name = "pdf_sha512")
    @Override
    public String getPdfSha512() {
        return pdfSha512;
    }

    @Override
    public void setPdfSha512(String pdfSha512) {
        this.pdfSha512 = pdfSha512;
    }
    
    @Column(name = "doc_guid", updatable = false, insertable = false)
    public String getDocGuid() {
        return docGuid;
    }

    public void setDocGuid(String docGuid) {
        this.docGuid = docGuid;
    }

    @Column(name = "doc_guid", updatable = false, insertable = false)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }    

    @Column(name = "has_pdf")
    @Override
    public Integer getHasPdf() {
        return hasPdf;
    }

    @Override
    public void setHasPdf(Integer hasPdf) {
        this.hasPdf = hasPdf;
    }

    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
    
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }
    
    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "has_pdf_pages")
    @Override
    public Integer getHasPdfPages() {
        return hasPdfPages;
    }

    @Override
    public void setHasPdfPages(Integer hasPdfPages) {
        this.hasPdfPages = hasPdfPages;
    }

    @Column(name = "number_saved_pages")
    @Override
    public Integer getNumberSavedPages() {
        return numberSavedPages;
    }
    
    @Override
    public void setNumberSavedPages(Integer numberSavedPages) {
        this.numberSavedPages = numberSavedPages;
    }

    @Override
    @Column(name = "has_password")
    @Type(type = "numeric_boolean")
    public Boolean getHasPassword() {
        return hasPassword;
    }

    @Override
    public void setHasPassword(Boolean hasPassword) {
        this.hasPassword = hasPassword;
    }

    @Column(name = "is_pdf_images_enabled")
    @Type(type = "numeric_boolean")
    public Boolean getPdfImagesEnabled() {
        return pdfImagesEnabled;
    }

    public void setPdfImagesEnabled(Boolean pdfImagesEnabled) {
        this.pdfImagesEnabled = pdfImagesEnabled;
    }

    @Column(name = "is_pdf_conversion_enabled")
    @Type(type = "numeric_boolean")
    public Boolean getPdfConversionEnabled() {
        return pdfConversionEnabled;
    }

    public void setPdfConversionEnabled(Boolean pdfConversionEnabled) {
        this.pdfConversionEnabled = pdfConversionEnabled;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Files)) {
            return false;
        }
        FilesLite other = (FilesLite) object;
        return !(this.id == null && other.id != null)
                || (this.id != null && !this.id.equals(other.id));
        }

    @Override
    public String toString() {
        return "DPMS.Mapping.FilesLite[ id=" + id + " ]";
    }

}