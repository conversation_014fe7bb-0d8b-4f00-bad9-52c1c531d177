package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import Framework.Config.StandardEntity;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "printable_section")
public class PrintableSection extends CompositeStandardEntity<PrintableSectionPK> implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer ACTIVE_STATUS = 1;

    private Printable printable;
    private Section section;

    private Integer status = 1;
    private Integer deleted = 0;

    private PrintableSectionPK id;

    public PrintableSection() {
    }

    public PrintableSection(PrintableSectionPK id) {
        this();
        this.id = id;
    }

    public PrintableSection(Long printableId, Long sectionId) {
        this();
        this.id = new PrintableSectionPK(printableId, sectionId);
    }

    @Override
    @EmbeddedId
    public PrintableSectionPK getId() {
        return id;
    }

    @Override
    public PrintableSectionPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(PrintableSectionPK id) {
        this.id = id;
    }
    
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = StandardEntity.IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "printable_id", insertable = false, updatable = false)
    public Printable getPrintable() {
        return printable;
    }

    public void setPrintable(Printable printable) {
        this.printable = printable;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "section_id", insertable = false, updatable = false)
    public Section getSection() {
        return section;
    }

    public void setSection(Section section) {
        this.section = section;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PrintableSection)) {
            return false;
        }
        PrintableSection other = (PrintableSection) object;
        return !((this.id == null && other.id != null)
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.PrintableSection[ id=" + id + " ]";
    }
}
