package DPMS.Mapping;

import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "organizational_unit")
public class OrganizationalUnit extends StandardEntity<OrganizationalUnit> implements ICreationTypeAware {
    
    private static final long serialVersionUID = 1L;
    
    private Long predecessorId;
    private OrganizationalUnit predecessor;
    private Long documentManagerId;
    private UserRef documentManager;
    private Long fileId;

    private String code = "";
    private String description = "";
    private Integer status = ACTIVE_STATUS;
    private Integer deleted = IS_NOT_DELETED;
    private Integer creationType;
    

    public OrganizationalUnit() {
    }

    public OrganizationalUnit(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "organizational_unit_id", precision = 19)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Column(name = "predecessor")
    public Long getPredecessorId() {
        return predecessorId;
    }

    public void setPredecessorId(Long predecessorId) {
        this.predecessorId = predecessorId;
    }

    @JsonIgnore
    @JoinColumn(name = "predecessor", referencedColumnName = "organizational_unit_id", insertable = false, updatable = false)
    @OneToOne(fetch = FetchType.EAGER)
    public OrganizationalUnit getPredecessor() {
        return predecessor;
    }

    public void setPredecessor(OrganizationalUnit predecessor) {
        this.predecessor = predecessor;
    }

    @Column(name = "document_manager_id")
    public Long getDocumentManagerId() {
        return documentManagerId;
    }

    public void setDocumentManagerId(Long documentManagerId) {
        this.documentManagerId = documentManagerId;
    }

    @JoinColumn(name = "document_manager_id", referencedColumnName = "user_id",
            insertable = false, updatable = false)
    @OneToOne(optional = false, fetch = FetchType.EAGER)
    public UserRef getDocumentManager() {
        return documentManager;
    }

    public void setDocumentManager(UserRef documentManager) {
        this.documentManager = documentManager;
    }

    /**
     * @return the fileId
     */
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    /**
     * @param fileId the fileId to set
     */
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }
    
    @Override
    public boolean equals(Object object) {
        // Req 3341
        if (!(object instanceof DPMS.Mapping.OrganizationalUnit)) {
            return false;
        }
        DPMS.Mapping.OrganizationalUnit other = (DPMS.Mapping.OrganizationalUnit) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Organizational Unit[ id=" + id + " ]";
    }
}