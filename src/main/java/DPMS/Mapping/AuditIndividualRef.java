package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import isoblock.surveys.dao.hibernate.OutstandingSurveysRef;
import isoblock.surveys.dao.hibernate.SurveySearchRef;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import bnext.reference.BusinessUnitDepartmentRef;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "audit_individual")
public class AuditIndividualRef extends StandardEntity<AuditIndividualRef> implements DomainObjectInterface {

    private static final long serialVersionUID = 1L;
    
    private Date dteStart;
    private Date dteEnd;
    private Date tmpStart;
    private Date tmpEnd;
    
    private Date dteStartRequest;
    private Date dteEndRequest;
    private Date tmpStartRequest;
    private Date tmpEndRequest;
    
    private BusinessUnitDepartmentRef businessUnitDepartment;
    private AuditRef audit;
    private OutstandingSurveysRef fill;
    private SurveySearchRef survey;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Long areaId;
    private UserRef attendant;
    private Double minimumScore;
    private Date actualStart;
    private Date actualEnd;
            
    
    public AuditIndividualRef() {
    }

    public AuditIndividualRef(Long id) {
        this.id = id;
    }
    @Id
    @Basic(optional = false)
    @Column(name = "audit_individual_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "area_id")
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Basic(optional = false)
    @Column(name = "dte_start", nullable = false)
    @Temporal(TemporalType.DATE)
    public Date getDteStart() {
        return dteStart;
    }

    public void setDteStart(Date dteStart) {
        if(dteStart == null) {
            dteStart = this.dteStart;
        }
        this.dteStart = dteStart;
    }

    @Basic(optional = false)
    @Column(name = "dte_end", nullable = false)
    @Temporal(TemporalType.DATE)
    public Date getDteEnd() {
        return dteEnd;
    }

    public void setDteEnd(Date dteEnd) {
        if(dteEnd == null) {
            dteEnd = this.dteEnd;
        }
        this.dteEnd = dteEnd;
    }

    @Basic(optional = false)
    @Column(name = "tmp_start", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpStart() {
        return tmpStart;
    }

    public void setTmpStart(Date tmpStart) {
        if(tmpStart == null) {
            tmpStart = this.tmpStart;
        }
        this.tmpStart = tmpStart;
    }

    @Basic(optional = false)
    @Column(name = "tmp_end", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpEnd() {
        return tmpEnd;
    }

    public void setTmpEnd(Date tmpEnd) {
        if(tmpEnd == null) {
            tmpEnd = this.tmpEnd;
        }
        this.tmpEnd = tmpEnd;
    }

    @Column(name = "dte_start_request")
    @Temporal(TemporalType.DATE)
    public Date getDteStartRequest() {
        return dteStartRequest;
    }

    public void setDteStartRequest(Date dteStartRequest) {
        this.dteStartRequest = dteStartRequest;
    }

    @Column(name = "dte_end_request")
    @Temporal(TemporalType.DATE)
    public Date getDteEndRequest() {
        return dteEndRequest;
    }

    public void setDteEndRequest(Date dteEndRequest) {
        this.dteEndRequest = dteEndRequest;
    }

    @Column(name = "tmp_start_request")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpStartRequest() {
        return tmpStartRequest;
    }

    public void setTmpStartRequest(Date tmpStartRequest) {
        this.tmpStartRequest = tmpStartRequest;
    }

    @Column(name = "tmp_end_request")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpEndRequest() {
        return tmpEndRequest;
    }

    public void setTmpEndRequest(Date tmpEndRequest) {
        this.tmpEndRequest = tmpEndRequest;
    }
    
    @Basic(optional = true)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_department_id", referencedColumnName = "business_unit_department_id")
    public BusinessUnitDepartmentRef getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartmentRef businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "audit_id", referencedColumnName = "audit_id")
    public AuditRef getAudit() {
        return audit;
    }

    public void setAudit(AuditRef audit) {
        this.audit = audit;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "OUTSTANDING_SURVEYS_ID")
    public OutstandingSurveysRef getFill() {
        return fill;
    }

    public void setFill(OutstandingSurveysRef fill) {
        this.fill = fill;
    }
   
    /**
     * @return the survey
     */
    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "survey_id", referencedColumnName = "survey_id")
    public SurveySearchRef getSurvey() {
        return survey;
    }

    /**
     * @param survey the survey to set
     */
    public void setSurvey(SurveySearchRef survey) {
        this.survey = survey;
    }
    
    /**
     * @return the attendant
     */
    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "attendant_id", referencedColumnName = "user_id")
    public UserRef getAttendant() {
        return attendant;
    }

    /**
     * @param attendant the attendant to set
     */
    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }
    
    /**
     * @return the minimumScore
     */
    @Column(name = "minimum_score")
    public Double getMinimumScore() {
        return minimumScore;
    }

    /**
     * @param minimumScore the minimumScore to set
     */
    public void setMinimumScore(Double minimumScore) {
        this.minimumScore = minimumScore;
    }

    /**
     * @return the actualStart
     */
    @Column(name = "actual_start")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualStart() {
        return actualStart;
    }

    /**
     * @param actualStart the actualStart to set
     */
    public void setActualStart(Date actualStart) {
        this.actualStart = actualStart;
    }

    /**
     * @return the actualEnd
     */
    @Column(name = "actual_end")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualEnd() {
        return actualEnd;
    }

    /**
     * @param actualEnd the actualEnd to set
     */
    public void setActualEnd(Date actualEnd) {
        this.actualEnd = actualEnd;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof AuditIndividualRef)) {
            return false;
        }
        AuditIndividualRef other = (AuditIndividualRef) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.AuditIndividualRef[ id=" + id + " ]";
    }
}
