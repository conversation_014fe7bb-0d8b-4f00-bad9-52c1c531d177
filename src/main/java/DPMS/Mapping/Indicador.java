package DPMS.Mapping;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblindicador")

public class Indicador extends StandardEntity<Indicador> implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long intindicadorid;
    private String txtformula;
    private String vchminmax;
    private Double intmeta;
    private Long intubicacionid;
    private String vchfuenteinformacion;
    private Date dtefecha;
    private String vchtipoagrupamiento;
    private String txtmeta;
    private Long intperiodicidadid;
    private Long intperiodicidadrevision;
    private Date dtefechaprimerarevision;
    private Long intfacultadid;
    private Short intreportarpor;
    private Objective objetivo;
    private Process process; //process haace mucho tiempo era area.
    private BusinessUnitDepartment ubicacion;
    private BusinessUnitDepartmentLoad ubicacionLoad;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public Indicador() {
    }

    public Indicador(Long intindicadorid) {
        this.intindicadorid = intindicadorid;
    }
    //Req 3212

    @Id
    @Basic(optional = false)
    @Column(name = "intindicadorid")
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return intindicadorid;
    }

    @Override
    public void setId(Long intindicadorid) {
        this.intindicadorid = intindicadorid;
    }


    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intstatusindicador")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Column(name = "txtformula")
    public String getFormula() {
        return txtformula;
    }

    public void setFormula(String txtformula) {
        this.txtformula = txtformula;
    }

    @Column(name = "vchminmax")
    public String getMinMax() {
        return vchminmax;
    }

    public void setMinMax(String vchminmax) {
        this.vchminmax = vchminmax;
    }

    @Column(name = "intmeta")
    public Double getMeta() {
        return intmeta;
    }

    public void setMeta(Double intmeta) {
        this.intmeta = intmeta;
    }

    @Column(name = "intubicacionid")
    public Long getUbicacionId() {
        return intubicacionid;
    }

    public void setUbicacionId(Long intubicacionid) {
        this.intubicacionid = intubicacionid;
    }

    @Column(name = "vchfuenteinformacion")
    public String getFuenteInformacion() {
        return vchfuenteinformacion;
    }

    public void setFuenteInformacion(String vchfuenteinformacion) {
        this.vchfuenteinformacion = vchfuenteinformacion;
    }

    @Column(name = "dtefecha")
    @Temporal(TemporalType.DATE)
    public Date getFecha() {
        return dtefecha;
    }

    public void setFecha(Date dtefecha) {
        this.dtefecha = dtefecha;
    }

    @Column(name = "vchtipoagrupamiento")
    public String getTipoAgrupamiento() {
        return vchtipoagrupamiento;
    }

    public void setTipoAgrupamiento(String vchtipoagrupamiento) {
        this.vchtipoagrupamiento = vchtipoagrupamiento;
    }

    @Column(name = "txtmeta")
    public String getTxtMeta() {
        return txtmeta;
    }

    public void setTxtMeta(String txtmeta) {
        this.txtmeta = txtmeta;
    }

    @Column(name = "intperiodicidadid")
    public Long getPeriodicidadId() {
        return intperiodicidadid;
    }

    public void setPeriodicidadId(Long intperiodicidadid) {
        this.intperiodicidadid = intperiodicidadid;
    }

    @Column(name = "intperiodicidadrevision")
    public Long getPeriodicidadRevision() {
        return intperiodicidadrevision;
    }

    public void setPeriodicidadRevision(Long intperiodicidadrevision) {
        this.intperiodicidadrevision = intperiodicidadrevision;
    }

    @Column(name = "dtefechaprimerarevision")
    @Temporal(TemporalType.DATE)
    public Date getFechaPrimeraRevision() {
        return dtefechaprimerarevision;
    }

    public void setFechaPrimeraRevision(Date dtefechaprimerarevision) {
        this.dtefechaprimerarevision = dtefechaprimerarevision;
    }

    @Column(name = "intfacultadid")
    public Long getFacultadId() {
        return intfacultadid;
    }

    public void setFacultadId(Long intfacultadid) {
        this.intfacultadid = intfacultadid;
    }

    @Column(name = "intreportarpor")
    public Short getReportarPor() {
        return intreportarpor;
    }

    public void setReportarPor(Short intreportarpor) {
        this.intreportarpor = intreportarpor;
    }

    @JoinColumn(name = "intobjetivoid", referencedColumnName = "intobjetivoid")
    @ManyToOne
    public Objective getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(Objective objetivo) {
        this.objetivo = objetivo;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (intindicadorid != null ? intindicadorid.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3213
        if (!(object instanceof Indicador)) {
            return false;
        }
        Indicador other = (Indicador) object;
        if ((this.intindicadorid == null && other.intindicadorid != null) || (this.intindicadorid != null && !this.intindicadorid.equals(other.intindicadorid))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Indicador[ intindicadorid=" + intindicadorid + " ]";
    }

    @JoinColumn(name = "intareaid", referencedColumnName = "process_id")
    @ManyToOne
    public Process getProcess() {
        return process;
    }

    /**
     * @param process the process to set
     */
    public void setProcess(Process process) {
        this.process = process;
    }

    /**
     * @return the ubicacion
     */
    @JoinColumn(name = "intubicacionmonitoreoid", referencedColumnName = "business_unit_department_id")
    @ManyToOne
    public BusinessUnitDepartment getUbicacion() {
        return ubicacion;
    }

    /**
     * @param ubicacion the ubicacion to set
     */
    public void setUbicacion(BusinessUnitDepartment ubicacion) {
        this.ubicacion = ubicacion;
    }

    @JsonIgnore
    @JoinColumn(name = "intubicacionmonitoreoid", referencedColumnName = "business_unit_department_id", insertable = false, updatable = false)
    @ManyToOne
    public BusinessUnitDepartmentLoad getUbicacionLoad() {
        return ubicacionLoad;
    }

    public void setUbicacionLoad(BusinessUnitDepartmentLoad ubicacionLoad) {
        this.ubicacionLoad = ubicacionLoad;
    }
}
