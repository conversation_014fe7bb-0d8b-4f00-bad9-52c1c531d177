package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "related_document")
public class RelatedDocument extends CompositeStandardEntity<RelatedDocumentPK> implements ILinkedComposityGrid<RelatedDocumentPK> {

    private static final long serialVersionUID = 1L;

    private RelatedDocumentPK id;

    public RelatedDocument() {
    }

    public RelatedDocument(RelatedDocumentPK id) {
        this.id = id;
    }

    public RelatedDocument(Long intdocumentoid, Long intpadreid) {
        this.id = new RelatedDocumentPK(intdocumentoid, intpadreid);
    }

    @Override
    @EmbeddedId
    public RelatedDocumentPK getId() {
        return id;
    }

    @Override
    public RelatedDocumentPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(RelatedDocumentPK id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3307
        if (!(object instanceof RelatedDocument)) {
            return false;
        }
        RelatedDocument other = (RelatedDocument) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.RelatedDocument[ id=" + id + " ]";
    }
}
