package DPMS.Mapping;

import Framework.Config.DomainObject;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "tblobjetivo")

public class Objective extends DomainObject implements Serializable {

    public static final Integer ACTIVE_STATUS = 1;
    public static final Integer INACTIVE_STATUS = 0;
    private static final long serialVersionUID = 1L;
    
    private String title;
    private String descripcion;
    private Integer intestado = 1;

    public Objective() {
    }

    public Objective(Long intobjetivoid) {
        this.id = intobjetivoid;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intobjetivoid")
    @GeneratedValue(generator="increment",strategy=GenerationType.SEQUENCE)
    @GenericGenerator(name="increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Basic(optional = false)
    @Column(name = "int_estado")
    public Integer getEstado() {
        return intestado;
    }

    public void setEstado(Integer intestado) {
        this.intestado = intestado;
    }

    @LocalizedField    
    @Column(name = "vchtitulo")
    public String getTitle() {
        return title;
    }

    public void setTitle(String vchtitulo) {
        this.title = vchtitulo;
    }

    @LocalizedField
    @Column(name = "vchdescripcion")
    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3339
        if (!(object instanceof Objective)) {
            return false;
        }
        Objective other = (Objective) object;
        return (this.id != null || other.id == null) && (this.id == null || this.id.equals(other.id));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Objective[ id=" + id + " ]";
    }
}
