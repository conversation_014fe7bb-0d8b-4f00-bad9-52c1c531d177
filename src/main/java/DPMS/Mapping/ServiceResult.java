package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "service_result")
public class ServiceResult extends StandardEntity<ServiceResult> implements DomainObjectInterface, ICreationTypeAware {

    private static final long serialVersionUID = 1L;
    
    private Long serviceTypeId;
    private ServiceType serviceType;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer creationType;
    
    public ServiceResult() {
        this.setDeleted(IS_NOT_DELETED);
        this.setStatus(ACTIVE_STATUS);
    }

    public ServiceResult(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "service_result_id", precision = 19)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Column(name = "service_type_id")
    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "service_type_id", name = "service_type_id",
            insertable = false, updatable = false)
    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }
}
