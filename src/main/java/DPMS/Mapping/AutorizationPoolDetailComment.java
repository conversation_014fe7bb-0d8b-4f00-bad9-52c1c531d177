/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import qms.document.dto.CommentHistoryDTO;

/**
 * Created on : Jul 3, 2015, 11:24:33 AM
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "auth_pool_detail_comment")
public class AutorizationPoolDetailComment extends StandardEntity<AutorizationPoolDetailComment> implements DomainObjectInterface {
    private static final long serialVersionUID = 1L;
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Integer recurrence;
    private Long autorizationPoolDetailsId;
    
    private String rejectionComment;
    private Long rejectionUserId;
    private Long rejectedOutstandingSurveysId;
    private String rejectionUserName;
    private Integer invalidAutorizationPoolIndex;
    private Integer autorizationPoolIndex;
    
    
    private String authorizationComment;
    private Long authorizationUserId;
    private String authorizationUserName;
    
    public AutorizationPoolDetailComment() {
    }

    public AutorizationPoolDetailComment(CommentHistoryDTO detail) {
        this.recurrence = detail.getCurrentRecurrence();
        this.authorizationUserId = detail.getUserId();
        this.autorizationPoolDetailsId = detail.getAutorizationPoolDetailsId();
        this.authorizationComment = detail.getAutorizationPoolDetailsDescription();
    }

    public AutorizationPoolDetailComment(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 50)
    @Column(name = "auth_pool_detail_comment_id", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "auth_pool_details_id")
    public Long getAutorizationPoolDetailsId() {
        return autorizationPoolDetailsId;
    }

    public void setAutorizationPoolDetailsId(Long autorizationPoolDetailsId) {
        this.autorizationPoolDetailsId = autorizationPoolDetailsId;
    }
    
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "authorization_comment")
    public String getAuthorizationComment() {
        return authorizationComment;
    }

    public void setAuthorizationComment(String authorizationComment) {
        this.authorizationComment = authorizationComment;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getRecurrence() {
        return recurrence;
    }

    public void setRecurrence(Integer recurrence) {
        this.recurrence = recurrence;
    }

    @Column(name = "rejection_comment")
    public String getRejectionComment() {
        return rejectionComment;
    }

    public void setRejectionComment(String rejectionComment) {
        this.rejectionComment = rejectionComment;
    }

    @Column(name = "rejection_user_id")
    public Long getRejectionUserId() {
        return rejectionUserId;
    }

    public void setRejectionUserId(Long rejectionUserId) {
        this.rejectionUserId = rejectionUserId;
    }

    @Column(name = "rejection_user_name")
    public String getRejectionUserName() {
        return rejectionUserName;
    }

    public void setRejectionUserName(String rejectionUserName) {
        this.rejectionUserName = rejectionUserName;
    }

    @Column(name = "authorization_user_id")
    public Long getAuthorizationUserId() {
        return authorizationUserId;
    }

    public void setAuthorizationUserId(Long authorizationUserId) {
        this.authorizationUserId = authorizationUserId;
    }

    @Column(name = "authorization_user_name")
    public String getAuthorizationUserName() {
        return authorizationUserName;
    }

    public void setAuthorizationUserName(String authorizationUserName) {
        this.authorizationUserName = authorizationUserName;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 61 * hash + Objects.hashCode(this.description);
        hash = 61 * hash + Objects.hashCode(this.recurrence);
        hash = 61 * hash + Objects.hashCode(this.rejectionComment);
        hash = 61 * hash + Objects.hashCode(this.rejectionUserId);
        hash = 61 * hash + Objects.hashCode(this.rejectionUserName);
        hash = 61 * hash + Objects.hashCode(this.authorizationUserId);
        hash = 61 * hash + Objects.hashCode(this.authorizationUserName);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AutorizationPoolDetailComment other = (AutorizationPoolDetailComment) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "AutorizationPoolDetailComment{" + "id=" + id + '}';
    }

    @Column(name = "rejected_out_surveys_id")
    public Long getRejectedOutstandingSurveysId() {
        return rejectedOutstandingSurveysId;
    }

    public void setRejectedOutstandingSurveysId(Long rejectedOutstandingSurveysId) {
        this.rejectedOutstandingSurveysId = rejectedOutstandingSurveysId;
    }

    @Column(name = "invalid_auth_pool_index")
    public Integer getInvalidAutorizationPoolIndex() {
        return invalidAutorizationPoolIndex;
    }

    public void setInvalidAutorizationPoolIndex(Integer invalidAutorizationPoolIndex) {
        this.invalidAutorizationPoolIndex = invalidAutorizationPoolIndex;
    }
    
    @Column(name = "auth_pool_index")
    public Integer getAutorizationPoolIndex() {
        return autorizationPoolIndex;
    }

    public void setAutorizationPoolIndex(Integer autorizationPoolIndex) {
        this.autorizationPoolIndex = autorizationPoolIndex;
    }
 
    
    
}