package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "variable")
public class Variable extends StandardEntity<Variable> implements DomainObjectInterface {
    private static final long serialVersionUID = 1L;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    
    public Variable() {
        this.setDeleted(IS_NOT_DELETED);
        this.setStatus(ACTIVE_STATUS);
    }

    public Variable(Long device_group_id) {
        this.id = device_group_id;
    }

    @Id
    @Basic
    @Column(name = "variable_id", precision = 19)
   @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

}
