package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import qms.framework.entity.GeolocationCoordinates;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblhistorialacceso")
public class AccessHistory extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long userId;
    private Date entrance;
    private Date exit;
    private String userAgent;
    private String ipAddress;
    private String token;
    private UserRef user;
    private Long locationId;
    private GeolocationCoordinates location;
    private Integer loginSource;
    private Integer oidcProvider;
    private String oidcId;
    private Double pageWidth;
    private Double pageHeight;
    private Double screenWidth;
    private Double screenHeight;
    private Double devicePixelRatio;
    private Double colorDepth;
    private Double pixelDepth;
    private String deviceOrientation;

    public AccessHistory() {
    }

    public AccessHistory(Long id) {
        this.id = id;
    }

    public AccessHistory(
            Long id,
            Long user_id,
            Date entrance,
            Date exit,
            String userAgent,
            String ipAddress) {
        this.id = id;
        this.userId = user_id;
        this.entrance = entrance;
        this.exit = exit;
        this.userAgent = userAgent;
        this.ipAddress = ipAddress;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "inthistorialaccesoid", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "intusuarioid", length = 255)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "tspfechaentrada")
    public Date getEntrance() {
        return entrance;
    }

    public void setEntrance(Date entrance) {
        this.entrance = entrance;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "tspfechasalida")
    public Date getExit() {
        return exit;
    }

    public void setExit(Date exit) {
        this.exit = exit;
    }
    
    @Column(name = "token")
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "intusuarioid", insertable = false ,updatable = false)
    public UserRef getUser() {
        return user;
    }

    public void setUser(UserRef user) {
        this.user = user;
    }

    @Column(name = "login_source")
    public Integer getLoginSource() {
        return loginSource;
    }

    public void setLoginSource(Integer loginSource) {
        this.loginSource = loginSource;
    }

    @Column(name = "oidc_provider")
    public Integer getOidcProvider() {
        return oidcProvider;
    }

    public void setOidcProvider(Integer oidcProvider) {
        this.oidcProvider = oidcProvider;
    }

    @Column(name = "oidc_id")
    public String getOidcId() {
        return oidcId;
    }

    public void setOidcId(String oidcId) {
        this.oidcId = oidcId;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3230 
        if (!(object instanceof AccessHistory)) {
            return false;
        }
        AccessHistory other = (AccessHistory) object;
        return (this.id != null || other.id == null) && (this.id == null || this.id.equals(other.id));
    }

    @Override
    public String toString() {
        return "DPMS.AccessHistory[ id=" + id + " ]";
    }

    /**
     * @return the userAgent
     */
    @Column(name = "user_agent")
    public String getUserAgent() {
        return userAgent;
    }

    /**
     * @param userAgent the userAgent to set
     */
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    /**
     * @return the ipAddress
     */
    @Column(name = "ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    /**
     * @param ipAddress the ipAddress to set
     */
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    @Column(name = "location_id")
    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "location_id", insertable = false ,updatable = false)
    public GeolocationCoordinates getLocation() {
        return location;
    }

    public void setLocation(GeolocationCoordinates location) {
        this.location = location;
    }

    @Column(name = "page_width", precision = 6, scale = 2)
    public Double getPageWidth() {
        return pageWidth;
    }

    public void setPageWidth(Double pageWidth) {
        this.pageWidth = pageWidth;
    }

    @Column(name = "page_height", precision = 6, scale = 2)
    public Double getPageHeight() {
        return pageHeight;
    }

    public void setPageHeight(Double pageHeight) {
        this.pageHeight = pageHeight;
    }

    @Column(name = "screen_width", precision = 6, scale = 2)
    public Double getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(Double screenWidth) {
        this.screenWidth = screenWidth;
    }

    @Column(name = "screen_height", precision = 6, scale = 2)
    public Double getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(Double screenHeight) {
        this.screenHeight = screenHeight;
    }

    @Column(name = "device_pixel_ratio", precision = 4, scale = 2)
    public Double getDevicePixelRatio() {
        return devicePixelRatio;
    }

    public void setDevicePixelRatio(Double devicePixelRatio) {
        this.devicePixelRatio = devicePixelRatio;
    }

    @Column(name = "color_depth", precision = 4, scale = 2)
    public Double getColorDepth() {
        return colorDepth;
    }

    public void setColorDepth(Double colorDepth) {
        this.colorDepth = colorDepth;
    }

    @Column(name = "pixel_depth", precision = 4, scale = 2)
    public Double getPixelDepth() {
        return pixelDepth;
    }

    public void setPixelDepth(Double pixelDepth) {
        this.pixelDepth = pixelDepth;
    }

    @Column(name = "device_orientation", length = 20)
    public String getDeviceOrientation() {
        return deviceOrientation;
    }

    public void setDeviceOrientation(String orientation) {
        this.deviceOrientation = orientation;
    }
}