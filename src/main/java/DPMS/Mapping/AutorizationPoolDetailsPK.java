package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class AutorizationPoolDetailsPK implements Serializable {

    private Long documentId;
    private Long requestId;
    private Long index;
    private Long userId;
    private Long puestoId;
    private Long boss;

    public AutorizationPoolDetailsPK() {
    }

    public AutorizationPoolDetailsPK(Long documentId, Long requestId, Long index, Long userId, Long puestoId, Long boss) {
        this.documentId = documentId;
        this.requestId = requestId;
        this.index = index;
        this.userId = userId;
        this.puestoId = puestoId;
        this.boss = boss;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "indice")
    @Basic(optional = false)
    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "puesto_id")
    public Long getPuestoId() {
        return puestoId;
    }

    public void setPuestoId(Long puestoId) {
        this.puestoId = puestoId;
    }

    public Long getBoss() {
        return boss;
    }

    public void setBoss(Long boss) {
        this.boss = boss;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 79 * hash + Objects.hashCode(this.documentId);
        hash = 79 * hash + Objects.hashCode(this.requestId);
        hash = 79 * hash + Objects.hashCode(this.index);
        hash = 79 * hash + Objects.hashCode(this.userId);
        hash = 79 * hash + Objects.hashCode(this.puestoId);
        hash = 79 * hash + Objects.hashCode(this.boss);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AutorizationPoolDetailsPK other = (AutorizationPoolDetailsPK) obj;
        if (!Objects.equals(this.documentId, other.documentId)) {
            return false;
        }
        if (!Objects.equals(this.requestId, other.requestId)) {
            return false;
        }
        if (!Objects.equals(this.index, other.index)) {
            return false;
        }
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        if (!Objects.equals(this.puestoId, other.puestoId)) {
            return false;
        }
        if (!Objects.equals(this.boss, other.boss)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "AutorizationPoolDetailsPK{" + "documentId=" + documentId
                + ", requestId=" + requestId
                + ", index=" + index + ", userId=" + userId
                + ", puestoId=" + puestoId + ", boss=" + boss + '}';
    }

}
