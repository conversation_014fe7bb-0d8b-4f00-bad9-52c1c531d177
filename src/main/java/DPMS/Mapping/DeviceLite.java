package DPMS.Mapping;

import bnext.reference.BusinessUnitDepartmentRef;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "device")
public class DeviceLite extends StandardEntity<DeviceLite> implements DomainObjectInterface, IDevice {

    private static final long serialVersionUID = 1L;
    
    private Long deviceGroupId;
    private Long deviceTypeId;
    private String modelBrand;
    private String serial;
    private String localization;
    private Long departmentId;
    private Long areaId;
    private Long responsibleId;
    private Date purchaseDate;
    private Date useDate;
    private Date changeDate;
    private Date disposeDate;
    private String operationRange;
    private String precision;
    private String treshold;
    private String uncertainty;
    private String calibrationMethod;
    private String calibrationMethodText;
    private String disposeComment;
    private BusinessUnitDepartmentRef department;
    private DeviceGroup deviceGroup;
    private UserRef attendant;
    private Long priorityId;
    private Long classificationId;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public DeviceLite() {
        this.deleted = IS_NOT_DELETED;
        this.status = ACTIVE_STATUS;
        this.deviceGroupId = 1L;
        this.deviceTypeId = 1L;
        this.modelBrand = "";
        this.serial = "";
        this.localization = "";
        this.departmentId = 1L;
        this.areaId = 1L;
        this.responsibleId = 1L;
        this.purchaseDate = new Date();
        this.useDate = new Date();
        this.changeDate = new Date();
        this.operationRange = "";
        this.precision = "";
        this.treshold = "";
        this.uncertainty = "";
        this.calibrationMethod = "";
        this.calibrationMethodText = "";
    }

    public DeviceLite(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "device_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Column(name = "device_group_id")
    @Override
    public Long getDeviceGroupId() {
        return deviceGroupId;
    }

    @Override
    public void setDeviceGroupId(Long deviceGroupId) {
        this.deviceGroupId = deviceGroupId;
    }

    @Column(name = "device_type_id")
    @Override
    public Long getDeviceTypeId() {
        return deviceTypeId;
    }

    @Override
    public void setDeviceTypeId(Long deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    @Column(name = "model_brand")
    @Override
    public String getModelBrand() {
        return modelBrand;
    }

    @Override
    public void setModelBrand(String modelBrand) {
        this.modelBrand = modelBrand;
    }

    @Column(name = "serial")
    @Override
    public String getSerial() {
        return serial;
    }

    @Override
    public void setSerial(String serial) {
        this.serial = serial;
    }

    @Column(name = "localization")
    @Override
    public String getLocalization() {
        return localization;
    }

    @Override
    public void setLocalization(String localization) {
        this.localization = localization;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "purchase_date")
    @Override
    public Date getPurchaseDate() {
        return purchaseDate;
    }

    @Override
    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "use_date")
    @Override
    public Date getUseDate() {
        return useDate;
    }

    @Override
    public void setUseDate(Date useDate) {
        this.useDate = useDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "change_date")
    @Override
    public Date getChangeDate() {
        return changeDate;
    }

    @Override
    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    @Column(name = "operation_range")
    @Override
    public String getOperationRange() {
        return operationRange;
    }

    @Override
    public void setOperationRange(String operationRange) {
        this.operationRange = operationRange;
    }

    @Column(name = "precision")
    @Override
    public String getPrecision() {
        return precision;
    }

    @Override
    public void setPrecision(String precision) {
        this.precision = precision;
    }

    @Column(name = "treshold")
    @Override
    public String getTreshold() {
        return treshold;
    }

    @Override
    public void setTreshold(String treshold) {
        this.treshold = treshold;
    }

    @Column(name = "uncertainty")
    @Override
    public String getUncertainty() {
        return uncertainty;
    }

    @Override
    public void setUncertainty(String uncertainty) {
        this.uncertainty = uncertainty;
    }

    @Column(name = "calibration_method")
    @Override
    public String getCalibrationMethod() {
        return calibrationMethod;
    }

    @Override
    public void setCalibrationMethod(String calibrationMethod) {
        this.calibrationMethod = calibrationMethod;
    }

    @Column(name = "calibration_method_text")
    @Override
    public String getCalibrationMethodText() {
        return calibrationMethodText;
    }

    @Override
    public void setCalibrationMethodText(String calibrationMethodText) {
        this.calibrationMethodText = calibrationMethodText;
    }

    @Column(name = "department_id")
    @Override
    public Long getDepartmentId() {
        return departmentId;
    }

    @Override
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    @Column(name = "area_id")
    @Override
    public Long getAreaId() {
        return areaId;
    }

    @Override
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    @Column(name = "responsible_id")
    @Override
    public Long getResponsibleId() {
        return responsibleId;
    }

    @Override
    public void setResponsibleId(Long responsibleId) {
        this.responsibleId = responsibleId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "dispose_date")
    @Override
    public Date getDisposeDate() {
        return disposeDate;
    }

    @Override
    public void setDisposeDate(Date disposeDate) {
        this.disposeDate = disposeDate;
    }

    @Column(name = "dispose_comment")
    @Override
    public String getDisposeComment() {
        return disposeComment;
    }

    @Override
    public void setDisposeComment(String disposeComment) {
        this.disposeComment = disposeComment;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "responsible_id",
            insertable = false, updatable = false)
    @Override
    public UserRef getAttendant() {
        return attendant;
    }

    @Override
    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }
    
    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "department_id",
            insertable = false, updatable = false)
    @Override
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    @Override
    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "device_group_id", name = "device_group_id",
            insertable = false, updatable = false)
    @Override
    public DeviceGroup getDeviceGroup() {
        return deviceGroup;
    }

    @Override
    public void setDeviceGroup(DeviceGroup deviceGroup) {
        this.deviceGroup = deviceGroup;
    }

    @Column(name = "priority_id")
    @Override
    public Long getPriorityId() {
        return priorityId;
    }

    @Override
    public void setPriorityId(Long priorityId) {
        this.priorityId = priorityId;
    }

    @Column(name = "device_classification_id")
    @Override
    public Long getClassificationId() {
        return classificationId;
    }

    @Override
    public void setClassificationId(Long classificationId) {
        this.classificationId = classificationId;
    }


    @Override
    public int hashCode() {
      int hash = 7;
      return hash;
    }

    @Override
    public boolean equals(Object obj) {
      if (obj == null) {
        return false;
      }
      if (getClass() != obj.getClass()) {
        return false;
      }
      final DeviceLite other = (DeviceLite) obj;
      return Objects.equals(this.id, other.id) || (this.id != null && this.id.equals(other.id));
    }

    @Override
    public String toString() {
      return "DPMS.Mapping.DeviceLite [id=" + id + "]";
    }
    
    /**
     * Los datos del correo de equipos
     *
     * @return una lista de TextHasValue con los datos del correo electronico
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    public List<ITextHasValue> MailData() {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        String tag, desc;
        tag = Utilities.getTag("device.mail.data.code"); // "Código:"
        desc = Utilities.getTag("device.mail.data.description"); // "Descripción:"
        List<ITextHasValue> list = new ArrayList<>(Arrays.asList(new TextHasValue[]{
            new TextHasValue(tag, getCode()),
            new TextHasValue(desc, getDescription())
        }));
        if (modelBrand != null) {
            if (!modelBrand.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.modelBrand"); // "Modelo/Marca:"
                list.add(new TextHasValue(tag, modelBrand));
            }
        }
        if (serial != null) {
            if (!serial.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.serial"); // "Número de serie:"
                list.add(new TextHasValue(tag, serial));
            }
        }
        if (getStatus() != null) {
            tag = Utilities.getTag("device.mail.data.status"); // Estatus
            list.add(new TextHasValue(tag, dao.HQL_findSimpleString("SELECT c.description FROM " + DeviceStatus.class.getCanonicalName() + " c WHERE c.id = :id", "id", getStatus().longValue())));
        }
        if (disposeDate != null) {
            tag = Utilities.getTag("device.mail.data.disposeDate"); // "Fecha de desecho:"
            list.add(new TextHasValue(tag, Utilities.formatDateBy(disposeDate, "dd/MM/yyyy")));
        }
        if (disposeComment != null) {
            if (!disposeComment.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.disposeComment"); // "Comentario de desecho:"
                list.add(new TextHasValue(tag, disposeComment));
            }
        }
        if (deviceGroup != null) {
            tag = Utilities.getTag("device.mail.data.groupId"); // "Grupo de equipo:"
            list.add(new TextHasValue(tag, deviceGroup.getDescription()));
        }
        if (deviceTypeId != null) {
            tag = Utilities.getTag("device.mail.data.deviceType"); // "Tipo de equipo:"
            list.add(new TextHasValue(tag, dao.HQL_findSimpleString("SELECT c.description FROM " + DeviceType.class.getCanonicalName() + " c WHERE c.id = :id", "id", deviceTypeId)));
        }
        if (department != null) {
            tag = Utilities.getTag("device.mail.data.department"); // "Departamento:"
            list.add(new TextHasValue(tag, department.getDescription()));
        }
        if (areaId != null) {
            tag = Utilities.getTag("device.mail.data.aera"); // "&Aacute;rea:"
            list.add(new TextHasValue(tag, dao.HQL_findSimpleString("SELECT c.description FROM " + Area.class.getCanonicalName() + " c WHERE c.id = :id", "id", areaId)));
        }
        if (attendant != null) {
            tag = Utilities.getTag("device.mail.data.responsible"); // "Encargado del equipo:"
            list.add(new TextHasValue(tag, attendant.getDescription()));
        }
        if (purchaseDate != null) {
            tag = Utilities.getTag("device.mail.data.purchaseDate"); // "Fecha de compra:"
            list.add(new TextHasValue(tag, Utilities.formatDateBy(purchaseDate, "dd/MM/yyyy")));
        }
        if (useDate != null) {
            tag = Utilities.getTag("device.mail.data.useDate"); // "Fecha de inicio de operación:"
            list.add(new TextHasValue(tag, Utilities.formatDateBy(useDate, "dd/MM/yyyy")));
        }
        if (changeDate != null) {
            tag = Utilities.getTag("device.mail.data.replaceDate"); // "Fecha de reemplazo:"
            list.add(new TextHasValue(tag, Utilities.formatDateBy(changeDate, "dd/MM/yyyy")));
        }
        if (localization != null) {
            if (!localization.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.localization"); // "Localización:"
                list.add(new TextHasValue(tag, localization));
            }
        }
        if (operationRange != null) {
            if (!operationRange.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.operationRange"); // "Rango de operación:"
                list.add(new TextHasValue(tag, operationRange));
            }
        }
        if (precision != null) {
            if (!precision.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.precision"); // "Exactitud:"
                list.add(new TextHasValue(tag, precision));
            }
        }
        if (uncertainty != null) {
            if (!uncertainty.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.uncertainty"); // "Incertidumbre:"
                list.add(new TextHasValue(tag, uncertainty));
            }
        }
        if (treshold != null) {
            if (!treshold.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.treshold"); // "Tolerancia:"
                list.add(new TextHasValue(tag, treshold));
            }
        }
        if (calibrationMethod != null) {
            if (!calibrationMethod.isEmpty()) {
                tag = Utilities.getTag("device.mail.data.calibration"); // "Método de calibración:"
                list.add(new TextHasValue(tag, calibrationMethod));
            }
        }
        Long documentSize = dao.HQL_findSimpleLong("SELECT count(doc.id) FROM " + Device.class.getCanonicalName() + " c JOIN c.documents doc WHERE c.id = :id", "id", id);
        if (documentSize != null) {
            tag = Utilities.getTag("device.mail.data.documents"); 
            list.add(new TextHasValue(tag, String.valueOf(documentSize)));
        }

        return list;
    }
    
}
