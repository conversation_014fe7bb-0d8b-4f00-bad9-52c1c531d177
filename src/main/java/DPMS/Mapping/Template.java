package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "template")
public class Template extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String title;
    private Set<TemplateList> list;

    public Template() {
    }

    public Template(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "template_id", nullable = false)
    @GeneratedValue(generator="increment",strategy=GenerationType.SEQUENCE)
    @GenericGenerator(name="increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    @Column(name = "title")
    public String getTitle() {
        return this.title;
    }
    
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3237
        if (!(object instanceof Template)) {
            return false;
        }
        Template other = (Template) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Template[ Id=" + id + " ]";
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(fetch= FetchType.EAGER)
    @JoinColumn(referencedColumnName="template_id", name="template_id")
    public Set<TemplateList> getList() {
        return list;
    }

    
    public void setList(Set<TemplateList> list) {
        this.list = list;
    }
    public void strList(HashSet<TemplateList> list) {
        this.list = list;
    }
}
