package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.aspect.IExcludeLogging;
import isoblock.surveys.dao.hibernate.OutstandingSurveysLite;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import qms.framework.entity.Owner;

/**
 * Created on : Mar 11, 2015, 12:03:21 PM
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "outstanding_surveys_attendant")
public class OutstandingSurveysAttendant extends DomainObject implements Serializable, IExcludeLogging {

    private static final long serialVersionUID = 1L;
        
    private OutstandingSurveysLite outstandingSurvey;
    private Long outstandingSurveyId;
    private Long requestId;
    private Long fieldObjectId;
    private Long userDefinedToFillId;  //<-- usuario que fue seleccionado para llenar
    /**
     * @deprecated {@link HibernateDAO_SurveyCapture#getNewPoolAttendants} No se esta llenando
     */
    private Long fillerUserId;  //<-- usuario que al final llenó (del pool dentro de OWNER)
    private Long ownerId;       //<-- responsable de llenado
    private Owner owner;        //<-- responsable de llenado
    private Long workflowPoolId;
    private Long fillAutorizationPoolIndex;
    private String fieldType;
    

    public OutstandingSurveysAttendant() {
    }

    public OutstandingSurveysAttendant(Long id) {
        this.id = id;
    }

    public OutstandingSurveysAttendant(Long outstandingSurveyId, Long fieldObjectId, String fieldType) {
        this.fieldObjectId = fieldObjectId;
        this.fieldType = fieldType;
        this.outstandingSurveyId = outstandingSurveyId;
    }
    
    public OutstandingSurveysAttendant(Long id, OutstandingSurveysLite outstandingSurvey, Long fieldObjectId, Long userId) {
        this.id = id;
        this.outstandingSurvey = outstandingSurvey;
        this.fieldObjectId = fieldObjectId;
        this.fillerUserId = userId;
    }
    public OutstandingSurveysAttendant(Long id, OutstandingSurveysLite outstandingSurvey, Long fieldObjectId, Long userId, String fieldType) {
        this.id = id;
        this.outstandingSurvey = outstandingSurvey;
        this.fieldObjectId = fieldObjectId;
        this.fillerUserId = userId;
        this.fieldType = fieldType;
    }

    /**
     *
     * @return
     */
    @Id
    @Basic(optional = false)
    @Column(name = "outstanding_sur_attendant_id", nullable = false)
    @Override
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne
    @JoinColumn(name="outstanding_surveys_id", insertable = false, updatable = false)
    public OutstandingSurveysLite getOutstandingSurvey() {
        return outstandingSurvey;
    }

    public void setOutstandingSurvey(OutstandingSurveysLite outstandingSurvey) {
        this.outstandingSurvey = outstandingSurvey;
    }

    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "field_object_id")
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }
    
    @Column(name = "user_defined_to_fill_id")
    public Long getUserDefinedToFillId() {
        return userDefinedToFillId;
    }

    public void setUserDefinedToFillId(Long userDefinedToFillId) {
        this.userDefinedToFillId = userDefinedToFillId;
    }
   
    @Column(name = "user_id")
    public Long getFillerUserId() {
        return fillerUserId;
    }

    public void setFillerUserId(Long fillerUserId) {
        this.fillerUserId = fillerUserId;
    }

    @Column(name = "fill_autorization_pool_index")
    public Long getFillAutorizationPoolIndex() {
        return fillAutorizationPoolIndex;
    }

    public void setFillAutorizationPoolIndex(Long fillAutorizationPoolIndex) {
        this.fillAutorizationPoolIndex = fillAutorizationPoolIndex;
    }

    @Column(name = "field_type")
    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }
    
    @ManyToOne
    @JoinColumn(referencedColumnName = "owner_id", name = "owner_id", insertable = false, updatable = false)
    public Owner getOwner() {
        return owner;
    }

    public void setOwner(Owner owner) {
        this.owner = owner;
    }

    @Column(name = "owner_id")
    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    @Column(name = "workflow_pool_id")
    public Long getWorkflowPoolId() {
        return workflowPoolId;
    }

    public void setWorkflowPoolId(Long workflowPoolId) {
        this.workflowPoolId = workflowPoolId;
    }
    

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + Objects.hashCode(this.id);
        hash = 67 * hash + Objects.hashCode(this.outstandingSurvey);
        hash = 67 * hash + Objects.hashCode(this.fieldObjectId);
        hash = 67 * hash + Objects.hashCode(this.fillerUserId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysAttendant other = (OutstandingSurveysAttendant) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        if (!Objects.equals(this.outstandingSurvey, other.outstandingSurvey)) {
            return false;
        }
        if (!Objects.equals(this.fieldObjectId, other.fieldObjectId)) {
            return false;
        }
        return Objects.equals(this.fillerUserId, other.fillerUserId);
    }

    @Override
    public String toString() {
        return "OutstandingSurveysAttendant{" 
                + " outstandingSurvey=" + outstandingSurvey + ", outstandingSurveyId=" + outstandingSurveyId 
                + ", fieldObjectId=" + fieldObjectId + ", userDefinedToFillId=" + userDefinedToFillId 
                + ", fillerUserId=" + fillerUserId + ", ownerId=" + ownerId + ", owner=" + owner
                + ", fillAutorizationPoolIndex=" + fillAutorizationPoolIndex + ", fieldType=" + fieldType + '}';
    }
    
}

