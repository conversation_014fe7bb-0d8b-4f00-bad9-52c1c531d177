package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class OutstandingSurveysAttendantLoadPK implements Serializable {
    
    private Long outstandingSurveyAttendantId;
    private Long userId;

    public OutstandingSurveysAttendantLoadPK(final Long outstandingSurveyAttendantId, final Long userId) {
        this.outstandingSurveyAttendantId = outstandingSurveyAttendantId;
        this.userId = userId;
    }

    public OutstandingSurveysAttendantLoadPK() {
    }

    @Basic(optional = false)
    @Column(name = "outstanding_sur_attendant_id")
    public Long getOutstandingSurveyAttendantId() {
        return outstandingSurveyAttendantId;
    }

    public void setOutstandingSurveyAttendantId(Long outstandingSurveyAttendantId) {
        this.outstandingSurveyAttendantId = outstandingSurveyAttendantId;
    }

    @Basic(optional = false)
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 79 * hash + Objects.hashCode(this.outstandingSurveyAttendantId);
        hash = 79 * hash + Objects.hashCode(this.userId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysAttendantLoadPK other = (OutstandingSurveysAttendantLoadPK) obj;
        if (!Objects.equals(this.outstandingSurveyAttendantId, other.outstandingSurveyAttendantId)) {
            return false;
        }
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "OutstandingSurveysAttendantLoadPK{" + "outstandingSurveyAttendantId=" + outstandingSurveyAttendantId + ", userId=" + userId + '}';
    }

}

