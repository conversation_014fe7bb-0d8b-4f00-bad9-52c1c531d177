package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 * <AUTHOR>
 */
@Embeddable
public class SequenceDetailPK implements Serializable {

    private Long requestId;
    private Long indice;

    public SequenceDetailPK() {
    }

    public SequenceDetailPK(Long requestId, Long indice, Long userId) {
        this.requestId = requestId;
        this.indice = indice;
    }

    @Basic(optional = false)
    @Column(name = "request_id", precision = 19)
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Basic(optional = false)
    @Column(name = "indice")
    public Long getIndice() {
        return indice;
    }

    public void setIndice(Long indice) {
        this.indice = indice;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.requestId);
        hash = 97 * hash + Objects.hashCode(this.indice);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SequenceDetailPK other = (SequenceDetailPK) obj;
        if (!Objects.equals(this.requestId, other.requestId)) {
            return false;
        }
        if (!Objects.equals(this.indice, other.indice)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "SequenceDetailPK{" + "requestId=" + requestId + ", indice=" + indice + '}';
    }

}
