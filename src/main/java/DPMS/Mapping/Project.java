package DPMS.Mapping;

import Framework.Config.ITextHasValue;
import Framework.Config.StandardEntity;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column; 
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblproyecto")
public class Project extends StandardEntity<Project> implements Serializable {

    private static final long serialVersionUID = 1L;
     public static final Integer ACTIVE_STATUS  = 2;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private String vchnombre;
    private Date dtefechainicio;
    private Date dtefechafin;
    private Date fechaCreacion;
    private Integer boolconfidencial;
    private String vchfuncion;
    private String vchnumerodeconvenio;
    private String vchfolio;
    private Integer boolproyectobase;
    private Integer boolproyectodesarrollo;
    private BigInteger intpresupuesto;
    private Set<ProjectGoal> proyectGoalList;
    private UserRef author;
    private UserRef responsable;
    private ProjectDetail detail;
    private Long intnodoid;
    private Set<FilesLite> documents;
    private BusinessUnitLite businessUnit;
    
    public static final String PRO_CREADO = "1";
    public static final String PRO_LIBERADO = "2";
    public static final String PRO_CONCLUIDO = "3";
    public static final String PRO_CERRADO = "4";
    public static final String PRO_CANCELADO = "5";

    @Override
    public List<ITextHasValue> MailData() {
        List<ITextHasValue> eMail = new ArrayList<ITextHasValue>();
        eMail.add(new TextHasValue("Clave", this.getCode()));
        eMail.add(new TextHasValue("Estado", getStringEstado(this.getStatus())));
        eMail.add(new TextHasValue("Nombre", this.getTitle()));
        eMail.add(new TextHasValue("Descripción", this.getDescription()));
        eMail.add(new TextHasValue("Fecha de creación", Utilities.formatDateBy(this.getFechaCreacion(), "dd/MM/yyyy HH:mm")));
        eMail.add(new TextHasValue("Fecha de inicio del proyecto", Utilities.formatDateBy(this.getFechaInicio(), "dd/MM/yyyy")));
        eMail.add(new TextHasValue("Fecha final del proyecto", Utilities.formatDateBy(this.getFechaFin(), "dd/MM/yyyy")));
        eMail.add(new TextHasValue("Confidencial", this.getConfidencial() == 0 ? "No" : "Si"));
        return eMail;
    }

    private String getStringEstado(int i) {
        switch (i) {
            case 1:
                return "Creado";
            case 2:
                return "Liberado";
            case 3:
                return "Concluido";
            case 4:
                return "Cerrado";
            case 5:
                return "Cancelado";
            default:
                return "No definido";
        }
    }

    public Project() {
    }

    public Project(Long intproyectoid) {
        this.id = intproyectoid;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intproyectoid")
    @Override
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long intproyectoid) {
        this.id = intproyectoid;
    }


    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "txtdescripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intestado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Basic(optional = false)
    @Column(name = "vchnombre")
    public String getTitle() {
        return vchnombre;
    }

    public void setTitle(String vchnombre) {
        this.vchnombre = vchnombre;
    }

    @Column(name = "dtefechainicio")
    @Temporal(TemporalType.DATE)
    public Date getFechaInicio() {
        return dtefechainicio;
    }

    public void setFechaInicio(Date dtefechainicio) {
        this.dtefechainicio = dtefechainicio;
    }

    @Column(name = "dtefechafin")
    @Temporal(TemporalType.DATE)
    public Date getFechaFin() {
        return dtefechafin;
    }

    public void setFechaFin(Date dtefechafin) {
        this.dtefechafin = dtefechafin;
    }

    @Basic(optional = false)
    @Column(name = "tspfechahoracreacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    @Basic(optional = false)
    @Column(name = "boolconfidencial")
    public Integer getConfidencial() {
        return boolconfidencial;
    }

    public void setConfidencial(Integer boolconfidencial) {
        this.boolconfidencial = boolconfidencial;
    }

    @Column(name = "vchfuncion")
    public String getFuncion() {
        return vchfuncion;
    }

    public void setFuncion(String vchfuncion) {
        this.vchfuncion = vchfuncion;
    }

    @Column(name = "vchnumerodeconvenio")
    public String getNumeroConvenio() {
        return vchnumerodeconvenio;
    }

    public void setNumeroConvenio(String vchnumerodeconvenio) {
        this.vchnumerodeconvenio = vchnumerodeconvenio;
    }

    @Column(name = "vchfolio")
    public String getFolio() {
        return vchfolio;
    }

    public void setFolio(String vchfolio) {
        this.vchfolio = vchfolio;
    }

    @Column(name = "boolproyectobase")
    public Integer getProyectoBase() {
        return boolproyectobase;
    }

    public void setProyectoBase(Integer boolproyectobase) {
        this.boolproyectobase = boolproyectobase;
    }

    @Column(name = "boolproyectodesarrollo")
    public Integer getProyectoDesarrollo() {
        return boolproyectodesarrollo;
    }

    public void setProyectoDesarrollo(Integer boolproyectodesarrollo) {
        this.boolproyectodesarrollo = boolproyectodesarrollo;
    }

    @Column(name = "intpresupuesto")
    public BigInteger getPresupuesto() {
        return intpresupuesto;
    }

    public void setPresupuesto(BigInteger intpresupuesto) {
        this.intpresupuesto = intpresupuesto;
    }

    /**
     * @return the intnodoid
     */
    @Column(name = "intnodoid")
    public Long getNodo() {
        return intnodoid;
    }

    /**
     * @param intnodoid the intnodoid to set
     */
    public void setNodo(Long intnodoid) {
        this.intnodoid = intnodoid;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "proyect")
    public Set<ProjectGoal> getProyectGoalList() {
        return proyectGoalList;
    }

    public void setProyectGoalList(Set<ProjectGoal> proyectActivityList) {
        this.proyectGoalList = proyectActivityList;
    }
    public void strProyectGoalList(HashSet<ProjectGoal> proyectActivityList) {
        this.proyectGoalList = proyectActivityList;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Project other = (Project) obj;
        if (this.id != other.id && (this.id == null || !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Proyect[ intproyectoid=" + id + " ]";
    }

    /**
     * @return the author
     */
    @JoinColumn(name = "intautorid", referencedColumnName = "user_id")
    @ManyToOne
    public UserRef getAuthor() {
        return author;
    }

    /**
     * @param author the author to set
     */
    public void setAuthor(UserRef author) {
        this.author = author;
    }

    /**
     * @return the responsable
     */
    @JoinColumn(name = "intresponsableid", referencedColumnName = "user_id")
    @ManyToOne
    public UserRef getResponsable() {
        return responsable;
    }

    /**
     * @param responsable the responsable to set
     */
    public void setResponsable(UserRef responsable) {
        this.responsable = responsable;
    }

    /**
     * @return the detail
     */
    @JsonIgnore
    @JoinColumn(name = "intproyectoid", referencedColumnName = "intproyectoid", insertable = false, updatable = false)
    @ManyToOne
    public ProjectDetail getDetail() {
        return detail;
    }

    /**
     * @param detail the detail to set
     */
    public void setDetail(ProjectDetail detail) {
        this.detail = detail;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "project_files",
            joinColumns = {
        @JoinColumn(name = "project_id", referencedColumnName = "intproyectoid")},
            inverseJoinColumns =
            @JoinColumn(name = "file_id", referencedColumnName = "id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<FilesLite> getDocuments() {
        return documents;
    }

    public void setDocuments(Set<FilesLite> documents) {
        this.documents = documents;
    }
    public void strDocuments(HashSet<FilesLite> documents) {
        this.documents = documents;
    }

    /**
     * @return the businessUnit
     */
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    @ManyToOne
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    /**
     * @param businessUnit the businessUnit to set
     */
    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }
}