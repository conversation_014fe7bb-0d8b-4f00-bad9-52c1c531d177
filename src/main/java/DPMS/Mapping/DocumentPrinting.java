package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "document_printing")
public class DocumentPrinting extends DomainObject implements Serializable {

    public static enum printingType {
        CONTROLLED(1), UNCONTROLLED(2), NONE(0);
        int type;
        printingType(int type) {
            this.type = type;
        }
        public int getValue() {
            return this.type;
        }
        public static printingType getType(int t) {
            for (printingType ar : DocumentPrinting.printingType.class.getEnumConstants()) {
                if(ar.getValue() == t) {
                    return ar;
                }
            }
            return NONE;
        }
    }

    public static String PREFIX = "DOC-PRINT";
    public static Integer QUEUE = 0; //Documento en cola de impresión
    public static Integer PRINTED = 1; //Documeto impreso
    private static final long serialVersionUID = 1L;

    private Integer status;
    private String code;
    private Long createdBy; //Req 3199
    private Date createdDate; //Req 3200
    private Long printBy;
    private Date printDate;
    private Integer printTimes = 1;
    private Integer printingType = 1;
    private String printLayout;
    private Integer printRotation;
    private List<DocumentPrintingLog> log;

    public DocumentPrinting() {
    }

    public DocumentPrinting(Long id) {
        this.id = id;
    }

    @Id
    @Override
    @Basic(optional = false)
    @Column(name = "id", nullable = false, precision = 19)
    @GenericGenerator(name = "increment", strategy = "increment")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "code", length = 255, nullable = false)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "created_by")
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_date")
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "print_by", nullable = false)
    public Long getPrintBy() {
        return printBy;
    }

    public void setPrintBy(Long printBy) {
        this.printBy = printBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "print_date", nullable = false)
    public Date getPrintDate() {
        return printDate;
    }

    public void setPrintDate(Date printDate) {
        this.printDate = printDate;
    }

    @Column(name = "print_times")
    public Integer getPrintTimes() {
        return printTimes;
    }

    public void setPrintTimes(Integer printTimes) {
        this.printTimes = printTimes;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @JsonIgnore(true)
    @Basic(optional = false)
    @OneToMany(mappedBy = "documentPrinting", fetch = FetchType.EAGER)
    public List<DocumentPrintingLog> getLog() {
        return log;
    }

    public void setLog(List<DocumentPrintingLog> log) {
        this.log = log;
    }

    @Column(name = "printing_type")
    public Integer getPrintingType() {
        return printingType;
    }

    public void setPrintingType(Integer printingType) {
        this.printingType = printingType;
    }

    @Column(name = "print_layout")
    public String getPrintLayout() {
        return printLayout;
    }

    public void setPrintLayout(String printLayout) {
        this.printLayout = printLayout;
    }

    @Column(name = "print_rotation")
    public Integer getPrintRotation() {
        return printRotation;
    }

    public void setPrintRotation(Integer printRotation) {
        this.printRotation = printRotation;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 41 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DocumentPrinting other = (DocumentPrinting) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }
    
    

    
}
