package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import bnext.licensing.LicenseUtil;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Convert;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitId;
import qms.util.interfaces.IDepartmentId;

/**
 *
 * <AUTHOR> Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "users")
public class UserLite extends StandardEntity<UserLite> implements DomainObjectInterface, IUser, IAddressableUser,
        IBusinessUnitDepartmentId, IDepartmentId, IBusinessUnitId, IAuditableEntity {

    private static final long serialVersionUID = 1L;
    public static final Integer TO_REGISTER_STATUS = 2;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Integer detailGridSize;
    private Integer floatingGridSize;
    private Integer gridSize;
    private Integer searchInSubfolders;
    private Integer showExternalDialog = 0;
    private Integer showWelcomeDialog = 1;
    private Integer superUser;
    private Integer version = 0;

    private Long bossId;
    private Long businessUnitDepartmentId;
    private Long businessUnitId;
    private Long departmentId;

    private Boolean isAway;
    private Date isAwaySince;
    private String isAwayReason;
    
    private Set<SimplePuesto> puestos;
    private SimplePuesto defaultWorkflowPosition;

    private String certificate;
    private String contrasena;
    private String correo;
    private String cuenta;
    private String hashedPassword;
    private String lang;
    private String licenseCode = LicenseUtil.DEFAULT_LICENSE_SCHEMA;
    private String locale;
    private String login;
    
    private Boolean accessEditProfile;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long avatarId;
    private Integer creationSource;
    private Integer oidcProvider;
    private Integer authTypeIntegrated = 1;
    private Integer authTypeLdap = 0;
    private Integer authTypeLandingPage = 0;
    private Integer authTypeOidc = 0;
    
    public UserLite(User user) {
        this.id = user.getId();
        if (user.getPuestos() != null) {
            this.puestos = new HashSet<>();
            for (Position p : user.getPuestos()) {
                this.puestos.add(new SimplePuesto(p.getId()));
            }
        }
        this.cuenta = user.getCuenta();
        this.contrasena = user.getContrasena();
        this.correo = user.getCorreo();
        this.lang = user.getLang();
        this.locale = user.getLocale();
        if (user.getDefaultWorkflowPosition() != null) {
            this.defaultWorkflowPosition = new SimplePuesto(user.getDefaultWorkflowPosition().getId());
        }
        //standar entty
        this.setCode(user.getCode());
        this.setDescription(user.getDescription());
        this.setStatus(user.getStatus());
        this.setDeleted(user.getDeleted());
        this.setBossId(user.getBossId());
        this.accessEditProfile = user.getAccessEditProfile();
    }

    public UserLite() {
    }

    public UserLite(Long id) {
        this.id = id;
    }

    public UserLite(Long id, String lang, String locale) {
        this.id = id;
        this.lang = lang;
        this.locale = locale;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 50)
    @Column(name = "user_id", nullable = false, precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "first_name", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "is_away", insertable = false, updatable = false)
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getIsAway() {
        return isAway;
    }

    public void setIsAway(Boolean isAway) {
        this.isAway = isAway;
    }

    @Column(name = "is_away_reason", insertable = false, updatable = false)
    public String getIsAwayReason() {
        return isAwayReason;
    }

    public void setIsAwayReason(String isAwayReason) {
        this.isAwayReason = isAwayReason;
    }

    @Column(name = "is_away_since", insertable = false, updatable = false)
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getIsAwaySince() {
        return isAwaySince;
    }

    public void setIsAwaySince(Date isAwaySince) {
        this.isAwaySince = isAwaySince;
    }
    
    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Basic(optional = false)
    @Column(name = "account", nullable = false)
    public String getCuenta() {
        return cuenta;
    }

    public void setCuenta(String cuenta) {
        this.cuenta = cuenta;
    }
    
    @Override
    @Column(name = "account", insertable = false, updatable = false)
    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "usuario_puesto",
            joinColumns
            = @JoinColumn(name = "usuario_id", referencedColumnName = "user_id"),
            inverseJoinColumns
            = @JoinColumn(name = "puesto_id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<SimplePuesto> getPuestos() {
        return puestos;
    }

    public void setPuestos(Set<SimplePuesto> puestos) {
        this.puestos = puestos;
    }

    public void strPuestos(HashSet<SimplePuesto> puestos) {
        this.puestos = puestos;
    }

    @Column(name = "mail")
    @Override
    public String getCorreo() {
        return correo;
    }

    public void setCorreo(String correo) {
        this.correo = correo;
    }

    @JsonIgnore
    @Column(name = "[password]")
    public String getContrasena() {
        return contrasena;
    }

    public void setContrasena(String contrasena) {
        this.contrasena = contrasena;
    }

    @Column(name = "lang")
    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    @Column(name = "locale")
    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "default_workflow_position", referencedColumnName = "puesto_id", nullable = true)
    public SimplePuesto getDefaultWorkflowPosition() {
        return defaultWorkflowPosition;
    }

    public void setDefaultWorkflowPosition(SimplePuesto defaultWorkflowPosition) {
        this.defaultWorkflowPosition = defaultWorkflowPosition;
    }

    @Column(name = "root", insertable = false, updatable = false)
    public Integer getSuperUser() {
        return superUser;
    }

    public void setSuperUser(Integer superUser) {
        this.superUser = superUser;
    }

    @Column(name = "boss_id")
    public Long getBossId() {
          return bossId;
    }

    public void setBossId(Long bossId) {
          this.bossId = bossId;
    }
    
    @Column(name = "certificate")
    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }
    
    @Column(name = "grid_size")
    public Integer getGridSize() {
        return gridSize;
    }

    public void setGridSize(Integer gridSize) {
        this.gridSize = gridSize;
    }

    @Column(name = "detail_grid_size")
    public Integer getDetailGridSize() {
        return detailGridSize;
    }

    public void setDetailGridSize(Integer detailGridSize) {
        this.detailGridSize = detailGridSize;
    }

    @Column(name = "floating_grid_size")
    public Integer getFloatingGridSize() {
        return floatingGridSize;
    }

    public void setFloatingGridSize(Integer floatingGridSize) {
        this.floatingGridSize = floatingGridSize;
    }

    @Column(name = "search_in_subfolders")
    public Integer getSearchInSubfolders() {
        return searchInSubfolders;
    }

    public void setSearchInSubfolders(Integer searchInSubfolders) {
        this.searchInSubfolders = searchInSubfolders;
    }
    
    @JsonIgnore
    @Column(name = "hashedpwd")
    public String getHashedPassword() {
        return hashedPassword;
    }

    public void setHashedPassword(String hashedPassword) {
        this.hashedPassword = hashedPassword;
    }
    
    @Override
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Override
    @Column(name = "department_id")
    public Long getDepartmentId() {
        return departmentId;
    }

    @Override
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "show_external_dialog")
    public Integer getShowExternalDialog() {
        return showExternalDialog;
    }

    public void setShowExternalDialog(Integer showExternalDialog) {
        this.showExternalDialog = showExternalDialog;
    }
    
    @Column(name = "show_welcome_dialog")
    public Integer getShowWelcomeDialog() {
        return showWelcomeDialog;
    }

    public void setShowWelcomeDialog(Integer showWelcomeDialog) {
        this.showWelcomeDialog = showWelcomeDialog;
    }
    
    /**
     * @return the licenseCode
     */   
    @Column(name = "license_code")
    public String getLicenseCode() {
        return licenseCode;
    }

    /**
     * @param licenseCode the licenseCode to set
     */
    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }
    
    @Column(name = "version")
    @Override
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Column(name = "access_edit_profile")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAccessEditProfile() {
        return accessEditProfile;
    }

    public void setAccessEditProfile(Boolean accessEditProfile) {
        this.accessEditProfile = accessEditProfile;
    }
    
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "avatar_id")
    public Long getAvatarId() {
        return avatarId;
    }

    public void setAvatarId(Long avatarId) {
        this.avatarId = avatarId;
    }
    
    @Column(name = "creation_source")
    public Integer getCreationSource() {
        return creationSource;
    }

    public void setCreationSource(Integer creationSource) {
        this.creationSource = creationSource;
    }

    @Column(name = "oidc_provider")
    public Integer getOidcProvider() {
        return oidcProvider;
    }

    public void setOidcProvider(Integer oidcProvider) {
        this.oidcProvider = oidcProvider;
    }
    
    @Column(name = "auth_type_integrated")
    public Integer getAuthTypeIntegrated() {
        return authTypeIntegrated;
    }

    public void setAuthTypeIntegrated(Integer authTypeIntegrated) {
        this.authTypeIntegrated = authTypeIntegrated;
    }

    @Column(name = "auth_type_ldap")
    public Integer getAuthTypeLdap() {
        return authTypeLdap;
    }

    public void setAuthTypeLdap(Integer authTypeLdap) {
        this.authTypeLdap = authTypeLdap;
    }

    @Column(name = "auth_type_landing_page")
    public Integer getAuthTypeLandingPage() {
        return authTypeLandingPage;
    }

    public void setAuthTypeLandingPage(Integer authTypeLandingPage) {
        this.authTypeLandingPage = authTypeLandingPage;
    }

    @Column(name = "auth_type_oidc")
    public Integer getAuthTypeOidc() {
        return authTypeOidc;
    }

    public void setAuthTypeOidc(Integer authTypeOidc) {
        this.authTypeOidc = authTypeOidc;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3242
        if (!(object instanceof UserRef)
                && !(object instanceof User)
                && !(object instanceof UserLite)
                && !(object instanceof UserSimple)
                && !(object instanceof User_old)) {
            return false;
        }
        IUser other = (IUser) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "UserLite{" + "id=" + id + ", cuenta=" + cuenta + '}';
    }
    
}
