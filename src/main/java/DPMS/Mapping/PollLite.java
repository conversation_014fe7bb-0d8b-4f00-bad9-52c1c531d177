package DPMS.Mapping;

import Framework.Config.StandardEntity;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.StrongBaseAPE;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import isoblock.surveys.dao.hibernate.SurveySearchRef;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import qms.poll.core.IPoll;
import qms.poll.entity.OutstandingSurveysPoll;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@RootSoftAPE( 
    mappedBy = "pollId",
    mappedByClass = OutstandingSurveysPoll.class
)
@Table(name = "poll")
public class PollLite extends StandardEntity<PollLite> implements Serializable, StrongBaseAPE, IPoll {

    private static final long serialVersionUID = 1L;
    
    public static final Integer STATUS_PLANNED = 1;
    public static final Integer STATUS_IN_PROCESS = 2;
    public static final Integer STATUS_DONE = 3;
    public static final Integer STATUS_CANCELED = 4;
    
    private Short anticipation;
    private Date dteAnticipation;
    private Date dteStart;
    private Date dteEnd;
    private Long authorId;
    private Long surveyId;
    private UserRef author;
    private SurveySearchRef survey;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public PollLite() {
    }

    public PollLite(Long id) {
        this.id = id;
    }

    public PollLite(Long id, String description, String code, Short status, Short deleted, Short anticipation, Date dteAnticipation, Date dteStart, Date dteEnd, Long authorId, Long surveyId) {
        this.id = id;
        this.anticipation = anticipation;
        this.dteAnticipation = dteAnticipation;
        this.dteStart = dteStart;
        this.dteEnd = dteEnd;
        this.authorId = authorId;
        this.surveyId = surveyId;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "poll_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            this.deleted = IS_DELETED;
        } else {
            this.deleted = borrado;
        }
    }
    
    @Basic(optional = false)
    @Column(nullable = false)
    @Override
    public Short getAnticipation() {
        return anticipation;
    }

    @Override
    public void setAnticipation(Short anticipation) {
        this.anticipation = anticipation;
    }

    @Basic(optional = false)
    @Column(name = "dte_anticipation", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getDteAnticipation() {
        return dteAnticipation;
    }

    @Override
    public void setDteAnticipation(Date dteAnticipation) {
        this.dteAnticipation = dteAnticipation;
    }

    @Basic(optional = false)
    @Column(name = "dte_start", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getDteStart() {
        return dteStart;
    }

    @Override
    public void setDteStart(Date dteStart) {
        this.dteStart = dteStart;
    }

    @Basic(optional = false)
    @Column(name = "dte_end", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getDteEnd() {
        return dteEnd;
    }

    @Override
    public void setDteEnd(Date dteEnd) {
        this.dteEnd = dteEnd;
    }

    @Basic(optional = false)
    @Column(name = "author_id", nullable = false)
    @Override
    public Long getAuthorId() {
        return authorId;
    }

    @Override
    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    @Basic(optional = false)
    @Column(name = "survey_id", nullable = false)
    @Override
    public Long getSurveyId() {
        return surveyId;
    }

    @Override
    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "author_id", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "survey_id", referencedColumnName = "survey_id", insertable = false, updatable = false)
    public SurveySearchRef getSurvey() {
        return survey;
    }

    public void setSurvey(SurveySearchRef survey) {
        this.survey = survey;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3348
        if (!(object instanceof PollLite)) {
            return false;
        }
        PollLite other = (PollLite) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PollLite{"
                + " id=" + id 
                + ", code=" + code
                + ", status=" + status 
                + ", dteStart=" + dteStart
                + ", dteEnd=" + dteEnd 
                + ", survey=" + (survey != null ? survey.getDescription(): null)
                + '}';
    }
}
