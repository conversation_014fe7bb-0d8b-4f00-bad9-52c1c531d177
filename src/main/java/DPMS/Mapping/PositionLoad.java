package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR> Limas Utilizado unicamente para cargar puestos
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "puesto")
public class PositionLoad extends StandardEntity<PositionLoad> implements DomainObjectInterface {

    public static final Integer ESTATUS_INACTIVO = 0;
    public static final Integer ESTATUS_ACTIVO = 1;
    private static final long serialVersionUID = 1L;
    
    private Profile perfil;
    private OrganizationalUnitSimple corp;
    private BusinessUnitLite une;
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    public PositionLoad() {
    }

    public PositionLoad(Long id) {
        this.id = id;
    }

    @Override
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "puesto_id", nullable = false, precision = 19, scale = 0)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @JoinColumn(name = "perfil_id", referencedColumnName = "perfil_id")
    @OneToOne(optional = false, fetch = FetchType.EAGER)
    public Profile getPerfil() {
        return perfil;
    }

    public void setPerfil(Profile perfil) {
        this.perfil = perfil;
    }

    @JoinColumn(name = "organizational_unit_id", referencedColumnName = "organizational_unit_id")
    @ManyToOne
    public OrganizationalUnitSimple getCorp() {
        return corp;
    }

    public void setCorp(OrganizationalUnitSimple corp) {
        this.corp = corp;
    }

    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    @OneToOne(fetch = FetchType.EAGER)
    public BusinessUnitLite getUne() {
        return une;
    }

    public void setUne(BusinessUnitLite une) {
        this.une = une;
    }
}
