package DPMS.Mapping;

import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLRestriction;

import qms.finding.entity.FindingActivityView;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblacciongenerica")
public class ActionLite extends StandardEntity<ActionLite> implements Serializable, IAuditableEntity {

    private static final long serialVersionUID = 1L;
    private String situacion;
    private String consecuencia;
    private Long responsableid;
    private BusinessUnitDepartmentRef department;
    private UserRef attendant;
    private UserRef author;
    private ActionSources fuente;
    private Set<FindingActivityView> activities;
    
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer findingsStatus = 0;
    private Integer deleted = 0;
    
    public ActionLite() {
    }

    public ActionLite(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intacciongenericaid", nullable = false)
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intestado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "findingsStatus")
    public Integer getFindingsStatus() {
        return findingsStatus;
    }

    public void setFindingsStatus(Integer findingsStatus) {
        if(findingsStatus == null) {
            findingsStatus = 0;
        }
        this.findingsStatus = findingsStatus;
    }
    
    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Basic(optional = false)
    @Column(name = "txtsituacion", nullable = false, length = 2147483647)
    public String getSituacion() {
        return situacion;
    }

    public void setSituacion(String txtsituacion) {
        this.situacion = txtsituacion;
    }

    @Basic(optional = false)
    @Column(name = "txtconsecuencia", nullable = false, length = 2147483647)
    public String getConsecuencia() {
        return consecuencia;
    }

    public void setConsecuencia(String txtconsecuencia) {
        this.consecuencia = txtconsecuencia;
    }

    @Column(name = "intresponsableid")
    public Long getResponsableId() {
        return responsableid;
    }

    public void setResponsableId(Long intresponsableid) {
        this.responsableid = intresponsableid;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "intubicacionid",
            insertable = false, updatable = false)
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "intresponsableid",
            insertable = false, updatable = false)
    public UserRef getAttendant() {
        return attendant;
    }

    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 37 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActionLite other = (ActionLite) obj;
        return Objects.equals(this.id, other.id);
    }
    
    @Override
    public String toString() {
        return "DPMS.Mapping.ActionLite[ id=" + id + " ]";
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "intautorid",
            insertable = false, updatable = false)
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    @JoinColumn(name = "intfuente", referencedColumnName = "inttipoid")
    @ManyToOne(fetch = FetchType.EAGER)
    public ActionSources getFuente() {
        return this.fuente;
    }

    public void setFuente(ActionSources fuente) {
        this.fuente = fuente;
    }
  
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "finding")
    @SQLRestriction("deleted=0")
    @OrderBy("activity_id ASC")
    public Set<FindingActivityView> getActivities() {
        return activities;
    }

    public void setActivities(Set<FindingActivityView> activities) {
        this.activities = activities;
    }
     
        
    @Override
    @Transient
    public Date getCreatedDate() {
        return createdDate;
    }
    
    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Transient
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Transient
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Transient
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
}
