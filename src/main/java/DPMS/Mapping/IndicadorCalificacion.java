package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import DPMS.Mapping.UserLite;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblindicadorcalificacion")
public class IndicadorCalificacion extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final Integer STATUS_PROGRAMADA = 1;
    public static final Integer STATUS_POR_CALIFICAR = 2;
    public static final Integer STATUS_CALIFICADO = 3;
    public static final Integer STATUS_REVISADO = 4;
    
    private Date fechaProgramada;
    private Date fechaLevantamiento;
    private Date fechaEntrega;
    private BigDecimal resultado;
    private Integer status;
    //private long intcarreraid;
    private Integer deleted;
    private Indicador indicador;
    private AreaLite area;
    private Long auditadoId;
    private UserLite auditado;

    public IndicadorCalificacion() {
    }

    public IndicadorCalificacion(Long id) {
        this.id = id;
    }

    //TODO: si se decide que se va a inserar cambiar los generated value por secuencias
    @Id
    @Basic(optional = false)
    @Column(name = "intcalificacionid")
    @GeneratedValue(generator="increment",strategy=GenerationType.SEQUENCE)
    @GenericGenerator(name="increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "dtefechaprogramada")
    @Temporal(TemporalType.DATE)
    public Date getFechaProgramada() {
        return fechaProgramada;
    }

    public void setFechaProgramada(Date fechaProgramada) {
        this.fechaProgramada = fechaProgramada;
    }

    @Column(name = "dtefechalevantamiento")
    @Temporal(TemporalType.DATE)
    public Date getFechaLevantamiento() {
        return fechaLevantamiento;
    }

    public void setFechaLevantamiento(Date fechaLevantamiento) {
        this.fechaLevantamiento = fechaLevantamiento;
    }

    @Column(name = "dtefechaentrega")
    @Temporal(TemporalType.DATE)
    public Date getFechaEntrega() {
        return fechaEntrega;
    }

    public void setFechaEntrega(Date fechaEntrega) {
        this.fechaEntrega = fechaEntrega;
    }

    @Column(name = "intresultado")
    public BigDecimal getResultado() {
        return resultado;
    }

    public void setResultado(BigDecimal resultado) {
        this.resultado = resultado;
    }

    @Column(name = "intestado")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic(optional = false)
    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @JoinColumn(name = "intindicadorid", referencedColumnName = "intindicadorid")
    @ManyToOne
    public Indicador getIndicador() {
        return indicador;
    }

    public void setIndicador(Indicador indicador) {
        this.indicador = indicador;
    }

    @Column(name = "intusuarioid", insertable = false, updatable = false)
    public Long getAuditadoId() {
        return auditadoId;
    }

    public void setAuditadoId(Long auditadoId) {
        this.auditadoId = auditadoId;
    }

    /**
     * @return the area
     */
    @JoinColumn(name = "intcarreraid", referencedColumnName = "area_id")
    @ManyToOne
    public AreaLite getArea() {
        return area;
    }

    /**
     * @param area the area to set
     */
    public void setArea(AreaLite area) {
        this.area = area;
    }

    /**
     * @return the auditado
     */
    @JoinColumn(name="intusuarioid",referencedColumnName="user_id")
    @ManyToOne
    public UserLite getAuditado() {
        return auditado;
    }

    /**
     * @param auditado the auditado to set
     */
    public void setAuditado(UserLite auditado) {
        this.auditado = auditado;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "IndicadorCalificacion[ id=" + id + " ]";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final IndicadorCalificacion other = (IndicadorCalificacion) obj;
        return Objects.equals(this.id, other.id);
    }
}
