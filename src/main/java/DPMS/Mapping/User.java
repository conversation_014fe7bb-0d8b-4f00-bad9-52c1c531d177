package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import ape.pending.core.BaseAPE;
import bnext.ibm.maximo.entity.IMailableContact;
import bnext.licensing.LicenseUtil;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.hibernate.annotations.WhereJoinTable;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.access.dto.TeamUserSaveDTO;
import qms.access.interfaces.IPlainUser;
import qms.framework.entity.OwnerTeam;
import qms.framework.initialload.ICreationTypeAware;
import qms.util.dto.FormApproverAnalystSelection;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitId;
import qms.util.interfaces.IDepartmentId;

/*
 * <AUTHOR> Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "users")
public class User extends StandardEntity<User> implements
        IUser, IUserLdap, ICreationTypeAware, IAddressableUser, DomainObjectInterface, 
        IAuditableEntity,
        BaseAPE, IMailableContact, IBusinessUnitDepartmentId, IDepartmentId, IBusinessUnitId, IPlainUser {

    private static final long serialVersionUID = 1L;
    public static final Integer TO_REGISTER_STATUS = 2;
    public static final Integer LOCKED_STATUS = 3;

    public static enum STATUS implements IStatusEnum {
        ACTIVE(ACTIVE_STATUS, IStatusEnum.COLOR_GREEN),
        INACTIVE(INACTIVE_STATUS, IStatusEnum.COLOR_GRAY),
        TO_ACTIVATE(TO_REGISTER_STATUS, IStatusEnum.COLOR_RED),
        LOCKED(LOCKED_STATUS, IStatusEnum.COLOR_ORANGE);
        private final Integer value;
        private final String gridCube;

        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }

        @Override
        public Integer getValue() {
            return this.value;
        }

        @Override
        public String getGridCube() {
            return this.gridCube;
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return ACTIVE;
        }
        
        @Override
        public String toString() {
            return this.value.toString();
        }
        
    }
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Integer creationType;
    private String contacto;
    private Integer detailGridSize;
    private Integer floatingGridSize;
    private Integer gridSize;
    private Integer inactiveBySystem;
    private Integer root;
    private Integer scalable = 1;
    private Integer searchInSubfolders = 0;
    private Integer showExternalDialog = 0;
    private Integer showWelcomeDialog = 1;
    private Integer version = 0;
    private Boolean askToRenewLocation;
    private Integer canEditMail;
    private Integer canEditName;
    
    private Long bossId;
    private UserRef boss;
    private Long businessUnitId;
    private Long departmentId;
    private Long businessUnitDepartmentId;
    
    private BusinessUnitDepartmentLite businessUnitDepartment;
    private BusinessUnitLite businessUnit;
    private OwnerTeam mainTeamOwner;
    private Position defaultWorkflowPosition;
    private Long defaultWorkflowPositionId;
    private Set<Position> puestos;
    
    private String certificate;
    private String contrasena;
    private String correo;
    private String cuenta;
    private String hashedPassword;
    private String lang = "";
    private String licenseCode = LicenseUtil.DEFAULT_LICENSE_SCHEMA;
    private String locale = "";
    private String login;

    private Boolean askToRenewPassword;
    private Boolean askToRenewTimezone;
    private Boolean accessEditProfile;
    private Boolean isAnonymous;
    private Boolean isAway;
    private Date isAwaySince;
    private String isAwayReason;
    
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private List<TeamUserSaveDTO> teams;
    private Set<OwnerTeam> userTeams;
    private Integer legacyOpenFilters = 1;
    private Long avatarId;
    // Configuraciones de widgets
    private Long widgetConfigurationLayoutId;
    private Long widgetConfigurationHiddenId;

    private Boolean closeSession;
    private String myActivitiesTabSelected;
    private Integer creationSource;
    private Integer oidcProvider;
    private Integer authTypeIntegrated = 1;
    private Integer authTypeLdap = 0;
    private Integer authTypeLandingPage = 0;
    private Integer authTypeOidc = 0;
    private String timezone;
    
    // Transient
    private List<FormApproverAnalystSelection> analystPerBusinessUnitDepartment;
    private Integer changeAwayReplacement;

    /*To Go!*/
    public User() {
    }

    public User(Long id) {
        this.id = id;
    }

    public User(String cuenta) {
        this.cuenta = cuenta;
    }
    
    public User(String userName, String userMail, Long userId) {
        if (userName != null) {
            userName = userName.trim();
        }
        if (userMail != null) {
            userMail = userMail.trim();
        }
        this.id = userId;
        this.correo = userMail;
        this.description = userName;
    }
    
    public User(Long id, String vchnombrecompleto, String vchcorreo, String vchNombre) {
        if (vchnombrecompleto != null) {
            vchnombrecompleto = vchnombrecompleto.trim();
        }
        if (vchNombre != null) {
            vchNombre = vchNombre.trim();
        }
        if (vchcorreo != null) {
            vchcorreo = vchcorreo.trim();
        }
        this.id = id;
        this.description = vchnombrecompleto;
        this.correo = vchcorreo;
        this.cuenta = vchNombre;
    }

    public User(Long id, String lang, String locale) {
        if (lang != null) {
            lang = lang.trim();
        }
        if (locale != null) {
            locale = locale.trim();
        }
        this.id = id;
        this.lang = lang;
        this.locale = locale;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 50)
    @Column(name = "user_id", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "main_team_owner_id", referencedColumnName = "owner_id")
    public OwnerTeam getMainTeamOwner() {
        return mainTeamOwner;
    }

    public void setMainTeamOwner(OwnerTeam mainTeamOwner) {
        this.mainTeamOwner = mainTeamOwner;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }
    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "first_name", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        if (descripcion != null) {
            descripcion = descripcion.trim();
        }
        this.description = descripcion;
    }

    @Column(name = "can_edit_mail")
    public Integer getCanEditMail() {
        return canEditMail;
    }

    public void setCanEditMail(Integer canEditMail) {
        this.canEditMail = canEditMail;
    }

    @Column(name = "can_edit_name")
    public Integer getCanEditName() {
        return canEditName;
    }

    public void setCanEditName(Integer canEditName) {
        this.canEditName = canEditName;
    }
    
    @Column(name = "ask_to_renew_location")
    @Type(type = "numeric_boolean")
    public Boolean getAskToRenewLocation() {
        return askToRenewLocation;
    }

    public void setAskToRenewLocation(Boolean askToRenewLocation) {
        this.askToRenewLocation = askToRenewLocation;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id", insertable = false, updatable = false)
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }

    @Override
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Override
    @Column(name = "department_id")
    public Long getDepartmentId() {
        return departmentId;
    }

    @Override
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "search_in_subfolders")
    public Integer getSearchInSubfolders() {
        return searchInSubfolders;
    }

    public void setSearchInSubfolders(Integer searchInSubfolders) {
        this.searchInSubfolders = searchInSubfolders;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            this.deleted = IS_DELETED;
        } else {
            this.deleted = borrado;
        }
    }

    @Override
    @Basic(optional = false)
    @Column(name = "account", nullable = false)
    public String getCuenta() {
        return cuenta;
    }

    public void setCuenta(String cuenta) {
        if (cuenta != null) {
            cuenta = cuenta.trim();
        }
        this.cuenta = cuenta;
        this.setLogin(cuenta);
    }

    @Override
    @Column(name = "account", insertable = false, updatable = false)
    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "usuario_puesto",
            joinColumns
            = {
                @JoinColumn(name = "usuario_id", referencedColumnName = "user_id")},
            inverseJoinColumns
            = {
                @JoinColumn(name = "puesto_id")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<Position> getPuestos() {
        return puestos;
    }

    public void setPuestos(Set<Position> puestos) {
        this.puestos = puestos;
    }

    public void strPuestos(HashSet<Position> puestos) {
        this.puestos = puestos;
    }

    @Column(name = "mail")
    @Override
    public String getCorreo() {
        if (correo != null) {
            correo = correo.trim();
        }
        return correo;
    }

    public void setCorreo(String correo) {
        this.correo = correo;
    }

    @Column(name = "[password]")
    @JsonIgnore
    public String getContrasena() {
        return contrasena;
    }

    public void setContrasena(String contrasena) {
        this.contrasena = contrasena;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "default_workflow_position", referencedColumnName = "puesto_id")
    public Position getDefaultWorkflowPosition() {
        return defaultWorkflowPosition;
    }

    public void setDefaultWorkflowPosition(Position defaultWorkflowPosition) {
        this.defaultWorkflowPosition = defaultWorkflowPosition;
    }

    @Column(name = "default_workflow_position", insertable = false, updatable = false)
    public Long getDefaultWorkflowPositionId() {
        return defaultWorkflowPositionId;
    }

    public void setDefaultWorkflowPositionId(Long defaultWorkflowPositionId) {
        this.defaultWorkflowPositionId = defaultWorkflowPositionId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_department_id", referencedColumnName = "business_unit_department_id", insertable = false, updatable = false)
    public BusinessUnitDepartmentLite getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartmentLite businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    /**
     * @return the lang
     */
    @Column(name = "lang")
    public String getLang() {
        return lang;
    }

    /**
     * @param lang the lang to set
     */
    public void setLang(String lang) {
        this.lang = lang;
    }

    /**
     * @return the locale
     */
    @Column(name = "locale")
    public String getLocale() {
        return locale;
    }

    /**
     * @param locale the locale to set
     */
    public void setLocale(String locale) {
        this.locale = locale;
    }

    @Column(name = "grid_size")
    public Integer getGridSize() {
        return gridSize;
    }

    public void setGridSize(Integer gridSize) {
        this.gridSize = gridSize;
    }

    @Column(name = "detail_grid_size")
    public Integer getDetailGridSize() {
        return detailGridSize;
    }

    public void setDetailGridSize(Integer detailGridSize) {
        this.detailGridSize = detailGridSize;
    }

    @Column(name = "floating_grid_size")
    public Integer getFloatingGridSize() {
        return floatingGridSize;
    }

    public void setFloatingGridSize(Integer floatingGridSize) {
        this.floatingGridSize = floatingGridSize;
    }

    @JsonIgnore
    @Column(name = "root", insertable = false, updatable = false)
    public Integer getRoot() {
        return root;
    }

    public void setRoot(Integer root) {
        this.root = root;
    }

    @Column(name = "boss_id")
    public Long getBossId() {
        return bossId;
    }

    public void setBossId(Long bossId) {
        this.bossId = bossId;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "boss_id", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getBoss() {
        return boss;
    }

    public void setBoss(UserRef boss) {
        this.boss = boss;
    }
    
    @Column(name = "show_external_dialog")
    public Integer getShowExternalDialog() {
        return showExternalDialog;
    }

    public void setShowExternalDialog(Integer showExternalDialog) {
        this.showExternalDialog = showExternalDialog;
    }
  
    @Column(name = "show_welcome_dialog")
    public Integer getShowWelcomeDialog() {
        return showWelcomeDialog;
    }

    public void setShowWelcomeDialog(Integer showWelcomeDialog) {
        this.showWelcomeDialog = showWelcomeDialog;
    }
  
    @JsonIgnore
    @Column(name = "hashedpwd")
    public String getHashedPassword() {
        return hashedPassword;
    }

    public void setHashedPassword(String hashedPassword) {
        this.hashedPassword = hashedPassword;
    }
  
    /**
     * @return the licenseCode
     */   
    @Column(name = "license_code")
    public String getLicenseCode() {
        return licenseCode;
    }

    /**
     * @param licenseCode the licenseCode to set
     */
    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public Integer getScalable() {
        return scalable;
    }

    public void setScalable(Integer scalable) {
        this.scalable = scalable;
    }
    
    @Column(name = "certificate")
    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }
    
    @Column(name = "inactive_by_system")
    public Integer getInactiveBySystem() {
        return inactiveBySystem;
    }

    public void setInactiveBySystem(Integer inactiveBySystem) {
        this.inactiveBySystem = inactiveBySystem;
    }

    /**
     * Es un string separado por comas con varios telefonos.
     * @return 
     */
    @Column(name = "telephones")
    @Override
    public String getContacto() {
        return contacto;
    }

    public void setContacto(String contacto) {
        this.contacto = contacto;
    } 

    @Column(name = "version")
    @Override
    public Integer getVersion() {
        return version;
    }


    public void setVersion(Integer version) {
        this.version = version;
    }
    
    @Column(name = "ask_to_renew_password")
    @Type(type = "numeric_boolean")
    public Boolean getAskToRenewPassword() {
        return askToRenewPassword;
    }

    public void setAskToRenewPassword(Boolean askToRenewPassword) {
        this.askToRenewPassword = askToRenewPassword;
    }
    
    @Column(name = "access_edit_profile")
    @Type(type = "numeric_boolean")
    public Boolean getAccessEditProfile() {
        return accessEditProfile;
    }

    public void setAccessEditProfile(Boolean accessEditProfile) {
        this.accessEditProfile = accessEditProfile;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }    

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "owner_user",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "owner_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Where(clause="type IN (6,7)")
    @WhereJoinTable(clause="type IN (1,2)")
    public Set<OwnerTeam> getUserTeams() {
        return userTeams;
    }

    public void setUserTeams(Set<OwnerTeam> userTeams) {
        this.userTeams = userTeams;
    }
    
    @Transient
    public List<TeamUserSaveDTO> getTeams() {
        return teams;
    }

    public void setTeams(List<TeamUserSaveDTO> teams) {
        this.teams = teams;
    }
    
    @Column(name = "legacy_open_filters")
    public Integer getLegacyOpenFilters() {
        return legacyOpenFilters;
    }

    public void setLegacyOpenFilters(Integer legacyOpenFilters) {
        if (legacyOpenFilters == null) {
            this.legacyOpenFilters = 1;
        } else {
            this.legacyOpenFilters = legacyOpenFilters;
        }
    }

    @Column(name = "avatar_id")
    public Long getAvatarId() {
        return avatarId;
    }

    public void setAvatarId(Long avatarId) {
        this.avatarId = avatarId;
    }

    @Column(name = "widget_config_layout_id")
    public Long getWidgetConfigurationLayoutId() {
        return widgetConfigurationLayoutId;
    }

    public void setWidgetConfigurationLayoutId(Long widgetConfigurationLayoutId) {
        this.widgetConfigurationLayoutId = widgetConfigurationLayoutId;
    }
    
    @Column(name = "widget_config_hidden_id")
    public Long getWidgetConfigurationHiddenId() {
        return widgetConfigurationHiddenId;
    }

    public void setWidgetConfigurationHiddenId(Long widgetConfigurationHiddenId) {
        this.widgetConfigurationHiddenId = widgetConfigurationHiddenId;
    }
    
    @Transient
    public Boolean getCloseSession() {
        return closeSession;
    }

    public void setCloseSession(Boolean closeSession) {
        this.closeSession = closeSession;
    }

    @Column(name = "my_activities_tab_selected")
    public String getMyActivitiesTabSelected() {
        return myActivitiesTabSelected;
    }

    public void setMyActivitiesTabSelected(String myActivitiesTabSelected) {
        this.myActivitiesTabSelected = myActivitiesTabSelected;
    }

    @Column(name = "creation_source")
    public Integer getCreationSource() {
        return creationSource;
    }

    public void setCreationSource(Integer creationSource) {
        this.creationSource = creationSource;
    }

    @Column(name = "oidc_provider")
    public Integer getOidcProvider() {
        return oidcProvider;
    }

    public void setOidcProvider(Integer oidcProvider) {
        this.oidcProvider = oidcProvider;
    }

    @Column(name = "auth_type_integrated")
    public Integer getAuthTypeIntegrated() {
        return authTypeIntegrated;
    }

    public void setAuthTypeIntegrated(Integer authTypeIntegrated) {
        this.authTypeIntegrated = authTypeIntegrated;
    }

    @Column(name = "auth_type_ldap")
    public Integer getAuthTypeLdap() {
        return authTypeLdap;
    }

    public void setAuthTypeLdap(Integer authTypeLdap) {
        this.authTypeLdap = authTypeLdap;
    }

    @Column(name = "auth_type_landing_page")
    public Integer getAuthTypeLandingPage() {
        return authTypeLandingPage;
    }

    public void setAuthTypeLandingPage(Integer authTypeLandingPage) {
        this.authTypeLandingPage = authTypeLandingPage;
    }

    @Column(name = "auth_type_oidc")
    public Integer getAuthTypeOidc() {
        return authTypeOidc;
    }

    public void setAuthTypeOidc(Integer authTypeOidc) {
        this.authTypeOidc = authTypeOidc;
    }

    @Column(name = "is_anonymous")
    @Type(type = "numeric_boolean")
    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    @Column(name = "is_away")
    @Type(type = "numeric_boolean")
    public Boolean getIsAway() {
        return isAway;
    }

    public void setIsAway(Boolean isAway) {
        this.isAway = isAway;
    }

    @Column(name = "is_away_reason")
    public String getIsAwayReason() {
        return isAwayReason;
    }

    public void setIsAwayReason(String isAwayReason) {
        this.isAwayReason = isAwayReason;
    }

    @Column(name = "is_away_since")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getIsAwaySince() {
        return isAwaySince;
    }

    public void setIsAwaySince(Date isAwaySince) {
        this.isAwaySince = isAwaySince;
    }

    @Column(name = "ask_to_renew_timezone")
    @Type(type = "numeric_boolean")
    public Boolean getAskToRenewTimezone() {
        return askToRenewTimezone;
    }

    public void setAskToRenewTimezone(Boolean askToRenewTimezone) {
        this.askToRenewTimezone = askToRenewTimezone;
    }

    @Column(name = "timezone")
    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
    
    @Override
    public int hashCode() {
        int hash = 3;
        hash = 59 * hash + Objects.hashCode(this.code);
        hash = 59 * hash + Objects.hashCode(this.cuenta);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final User other = (User) obj;
        if (!Objects.equals(this.code, other.getCode())) {
            return false;
        }
        return Objects.equals(this.cuenta, other.getCuenta());
    }

    @Override
    public String toString() {
        return "User{" + "description=" + description + ", cuenta=" + cuenta + ", correo=" + correo + '}';
    }

    @Transient
    public List<FormApproverAnalystSelection> getAnalystPerBusinessUnitDepartment() {
        return analystPerBusinessUnitDepartment;
    }

    public void setAnalystPerBusinessUnitDepartment(List<FormApproverAnalystSelection> analystPerBusinessUnitDepartment) {
        this.analystPerBusinessUnitDepartment = analystPerBusinessUnitDepartment;
    }

    @Transient
    public Integer getChangeAwayReplacement() {
        return changeAwayReplacement;
    }

    public void setChangeAwayReplacement(Integer changeAwayReplacement) {
        this.changeAwayReplacement = changeAwayReplacement;
    }

}
