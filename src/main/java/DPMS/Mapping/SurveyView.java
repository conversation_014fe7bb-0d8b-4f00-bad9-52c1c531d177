/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;

/**
 * Created on : Feb 10, 2015, 1:53:42 PM
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "SURVEY_SEARCH")
@Immutable
public class SurveyView extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Integer estatus;
    private String vchTexto;
    private String plainText;
    private String type;
    private Integer intQuestions;
    private Date dteCreacion;
    private Long globalObjId;
    private String code = "";
    private Long authorId;
    private Short rolSi;
    private Short rolNo;
    private Long requestId;
    private RequestLite request;    
    private List<OutstandingSurveysSimple> outstandingSurveysList;
    private Set<BusinessUnitLite> unes;
    
    private Integer isTemplateUseAvailable;
    
    private Long businessUnitCount;
    private Integer addActivitiesEnabled;

    public SurveyView() {
    }

    @Id
    @Basic(optional = false)
    @Column(name = "SURVEY_ID")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "DTE_CREATED")
    @Temporal(javax.persistence.TemporalType.DATE)
    public Date getDteCreacion() {
        return dteCreacion;
    }

    public void setDteCreacion(Date dteCreacion) {
        this.dteCreacion = dteCreacion;
    }
    @Basic(optional = false)
    @Column(name = "STATUS")
    public Integer getEstatus() {
        return estatus;
    }

    public void setEstatus(Integer estatus) {
        this.estatus = estatus;
    }

    @Column(name = "CODE")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "GLOBAL_OBJECT_ID")
    public Long getGlobalObjId() {
        return globalObjId;
    }

    public void setGlobalObjId(Long globalObjId) {
        this.globalObjId = globalObjId;
    }
    
    @Column(name = "ROL_NO")
    public Short getRolNo() {
        return rolNo;
    }

    public void setRolNo(Short rolNo) {
        this.rolNo = rolNo;
    }

    @Column(name = "ROL_SI")
    public Short getRolSi() {
        return rolSi;
    }

    public void setRolSi(Short rolSi) {
        this.rolSi = rolSi;
    }
    
    @Column(name = "vch_texto")
    public String getVchTexto() {
        return vchTexto;
    }

    public void setVchTexto(String vchTexto) {
        this.vchTexto = vchTexto;
    }

    @Column(name = "plain_title")
    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }
    
    @Column(name = "INT_QUESTIONS")
    public Integer getIntQuestions() {
        return intQuestions;
    }

    public void setIntQuestions(Integer intQuestions) {
        this.intQuestions = intQuestions;
    }

    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    @Column(name = "user_id")
    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }
    
    @OneToOne(optional=true)
    @JoinColumn(name = "request_id", referencedColumnName = "id",
            insertable = false, updatable = false)
    public RequestLite getRequest() {
        return request;
    }

    public void setRequest(RequestLite request) {
        this.request = request;
    }

    @ManyToMany(mappedBy = "cuestionario", fetch = FetchType.EAGER)  
    public List<OutstandingSurveysSimple> getOutstandingSurveysList() {
        return outstandingSurveysList;
    }

    public void setOutstandingSurveysList(List<OutstandingSurveysSimple> outstandingSurveysList) {
        this.outstandingSurveysList = outstandingSurveysList;
    }
    
    
    @Column(name = "is_template_use_available")
    public Integer getIsTemplateUseAvailable() {
        return isTemplateUseAvailable;
    }

    public void setIsTemplateUseAvailable(Integer isTemplateUseAvailable) {
        this.isTemplateUseAvailable = isTemplateUseAvailable;
    }

    @Column(name = "business_unit_count")
    public Long getBusinessUnitCount() {
        return businessUnitCount;
    }

    public void setBusinessUnitCount(Long businessUnitCount) {
        this.businessUnitCount = businessUnitCount;
    }

    @Column(name = "add_activities_enabled ")
    public Integer getAddActivitiesEnabled() {
        return addActivitiesEnabled;
    }

    public void setAddActivitiesEnabled(Integer addActivitiesEnabled) {
        this.addActivitiesEnabled = addActivitiesEnabled;
    }

    @ManyToMany
    @JoinTable(name = "SURVEY_GLOBAL_OBJECT_BU", 
        joinColumns = { @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID")}, 
        inverseJoinColumns = { @JoinColumn(name = "BUSINESS_UNIT_ID", referencedColumnName = "business_unit_id")})
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public Set<BusinessUnitLite> getUnes() {
        return unes;
    }
    
    public void setUnes(Set<BusinessUnitLite> unes) {
        this.unes = unes;
    }
}
