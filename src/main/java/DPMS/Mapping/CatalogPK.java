package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class CatalogPK implements Serializable {
    
    private Long catalogoId;
    private Long tipoId;
    
    public CatalogPK(){
      
    }
    
    public CatalogPK(Long catId,Long tipoId){
      this.catalogoId = catId;
      this.tipoId = tipoId;
    }

    /**
     * @return the catalogoId
     */
    @Basic(optional = false)
    @Column(name = "intcatalogoid")
    public Long getCatalogoId() {
        return catalogoId;
    }

    /**
     * @param catalogoId the catalogoId to set
     */
    public void setCatalogoId(Long catalogoId) {
        this.catalogoId = catalogoId;
    }

    /**
     * @return the tipoId
     */
    @Basic(optional = false)
    @Column(name = "inttipoid")
    public Long getTipoId() {
        return tipoId;
    }

    /**
     * @param tipoId the tipoId to set
     */
    public void setTipoId(Long tipoId) {
        this.tipoId = tipoId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.catalogoId);
        hash = 97 * hash + Objects.hashCode(this.tipoId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CatalogPK other = (CatalogPK) obj;
        if (!Objects.equals(this.catalogoId, other.catalogoId)) {
            return false;
        }
        if (!Objects.equals(this.tipoId, other.tipoId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "CatalogPK{" + "catalogoId=" + catalogoId + ", tipoId=" + tipoId + '}';
    }
    
}
