package DPMS.Mapping;

import Framework.Config.StandardEntity;
import ape.pending.core.StrongBaseTypedAPE;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import bnext.reference.document.FileRef;
import jakarta.persistence.Convert;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.hibernate.SurveySimple;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import mx.bnext.core.util.IStatusEnum;
import mx.bnext.core.util.IValueEnum;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.custom.core.EntityDynamicFields;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.document.entity.IRequest;
import qms.document.interfaces.IJoinableRequest;
import qms.document.interfaces.IPlainRequest;
import qms.document.logic.DocumentCodeValidator;
import qms.util.annotations.UserDefinedCode;
import qms.util.interfaces.IDepartmentLocation;
import qms.workflow.util.IWorkflowRequest;
import qms.workflow.util.WorkflowRequestStatus;

/**
 *
 * <AUTHOR> Garza Verastegui
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "request")
@DynamicUpdate
@UserDefinedCode(
    id = "documentCode", 
    mappedBy = "documentType", 
    mappableAttributes = {
        "author", "businessUnit", "department", "organizationalUnit", "documentType"
    },
    codeValidator = DocumentCodeValidator.class
)
public class Request extends StandardEntity<Request>
        implements IWorkflowRequest, IRequest, IDepartmentLocation, EntityDynamicFields,
        StrongBaseTypedAPE<DocumentType>, IJoinableRequest, IPlainRequest {

    private static final long serialVersionUID = 1L;
    private DynamicFieldInsertDTO dynamicFieldInsertDTO;
    private String surveyRequestMode;
    private String conditionalValidatorCacheId;


    @Override
    public void setDynamicFieldInsertDTO(DynamicFieldInsertDTO dynamicFieldInsertDTO) {
        this.dynamicFieldInsertDTO = dynamicFieldInsertDTO;
    }

    @Override
    @Transient
    public DynamicFieldInsertDTO getDynamicFieldInsertDTO() {
        return this.dynamicFieldInsertDTO;
    }

    public static enum SCOPE {     
        DEPARTMENT(0),
        BUSINESS_UNIT(1),
        ORGANIZATIONAL_UNIT(2);
        private final Integer value;
        private SCOPE(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return this.value;
        }
    }
    
    private String documentMasterId;
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private String documentCode;
    private Date creationDate;
    private Date rejectionDate;
    private Integer creationType;
    private Integer type;
    private String reazon;
    private DocumentType documentType;
    private Long typeId;
    private Integer retentionTime;
    private Integer retentionText;
    private Integer storagePlaceId;
    private String version;
    private Long documentId;
    private Document document;
    private Document oldDocument;
    private Long oldDocumentId;
    private AutorizationPool autorizationPool;
    private Long fileId;
    private Long autorId;
    private Long surveyId;
    private SurveySimple survey;
    private Long outstandingSurveysId;
    private UserRef author;
    private BusinessUnitLite businessUnit;
    private OrganizationalUnitLite organizationalUnit;
    private Long organizationalUnitId;
    private NodeSimple nodo;
    private Long nodoId;
    private Long flujoId;
    private Long workflowId;
    private Long createdBy;
    private BusinessUnitDepartmentRef department;
    private BusinessUnitDepartmentRef businessUnitDepartment;

    private Integer isBusy;
    private Integer timeToFreeMinutes;
    private Long blockedBy;
    private String blockedByName;
    private DocumentRef fillOutDocument;
    private Long fillOutDocumentId;
    private Long templateSurveyId;
    private Long surveyThemeId;
    private Integer scope;
    private Integer generateCode = 0;
    private String dynamicTableName = "";
    private FileRef fileContent;
    private UserRef rejectedByUser;
    private Long rejectedByUserId;
    private UserRef impersonatedBy;
    private Long impersonatedById;

    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Integer enablePdfViewer = 1;
    private String authorizersNames;
    private Boolean restrictRecordsByDepartment = false;
    private Boolean validateAccessFormDepartment = false;
    private String restrictRecordsField;
    private Long restrictRecordsObjId;
    
    private UserRef collectingAndStoreResponsibleUser;
    private Long collectingAndStoreResponsible;
    private String collectingAndStoreResponsibleDescription;
    private Long informationClassification;
    private Long disposition;
    private String slimReportName;
    
    // LAZY!
    private Set<AutorizationPoolDetails> autorizationPoolDetails;

// tipos de solicitud (type) //
    public static final Integer NEW = 1;
    public static final Integer MODIFY = 2;
    public static final Integer APROVE = 3;
    public static final Integer CANCEL = 4;
    public static final Integer FILL = 7;
    public static final Integer TIME = 8;
    public static final Integer DISPOSITION = 9;
    // estados de la solicitud (status) //
    public static final int STATUS_STANDBY = 9;
    public static final int STATUS_EXPIRED = 10;
    public static final int STATUS_VERIFING = WorkflowRequestStatus.VERIFING.getValue();
    public static final int STATUS_RETURNED = WorkflowRequestStatus.RETURNED.getValue();
    public static final int STATUS_CANCELED = WorkflowRequestStatus.CANCELED.getValue();
    public static final int STATUS_APROVING = WorkflowRequestStatus.APROVING.getValue();
    public static final int STATUS_CLOSED = WorkflowRequestStatus.CLOSED.getValue();

    public static enum TYPE implements IValueEnum {
        NEW(1),
        MODIFY(2), 
        APROVE(3), 
        CANCEL(4),
        FILL(7),
        TIME(8),
        DISPOSITION(9);
        private final Integer value;
        TYPE(Integer value) {
            this.value = value;
        }
        
        @Override
        public Integer getValue() {
            return this.value;
        }
        public boolean equals(Integer value) {
            return this.value.equals(value);
        }
        public static TYPE getType(Integer value) {
            for (TYPE t : TYPE.class.getEnumConstants()) {
                if(t.getValue().equals(value)) {
                    return t;
                }
            }
            return null;
        }

        private static Map<String, String> enumToMap(final TYPE type) {
            final Map<String, String> m = new HashMap<>(2);
            m.put("name", "type_" + type.name().toLowerCase());
            m.put("value", type.getValue().toString());
            return m;
        }
    
        public static List<Map<String, String>> getTypes() {
            final TYPE[] types = TYPE.values();
            final List<Map<String, String>> result = new ArrayList<>(types.length);
            for (final Request.TYPE type : types) {
                result.add(enumToMap(type));
            }
            return result;
        }

    }
    
    public static enum STATUS implements IStatusEnum {
        STAND_BY(STATUS_STANDBY, IStatusEnum.COLOR_RED),
        VERIFING(STATUS_VERIFING, IStatusEnum.COLOR_YELLOW),
        RETURNED(STATUS_RETURNED, IStatusEnum.COLOR_DEEP_BLUE),
        CANCELED(STATUS_CANCELED, IStatusEnum.COLOR_BLACK),
        APROVING(STATUS_APROVING, IStatusEnum.COLOR_ORANGE),
        EXPIRED(STATUS_EXPIRED, IStatusEnum.COLOR_RED),
        CLOSED(STATUS_CLOSED, IStatusEnum.COLOR_BEIGE)
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return APROVING;
        }

        public String intValue() {
            return value.toString();
        }
        public static STATUS fromValue(Integer value) {
            for (Request.STATUS e : STATUS.values()) {
                if (Objects.equals(e.getValue(), value)) {
                    return e;
                }
            }
            return null;
        }
    }
    public final static String CANCELED_REQUEST_PREFIX = "__CNCLR__";


    public Request() {
    }

    public Request(Long id) {
        this.id = id;
    }

    public Request(
            DocumentType documentType,
            Integer retentionText,
            Integer retentionTime,
            Integer scope,
            Integer storagePlaceId,
            Long businessUnitId,
            Long departmentId,
            Long nodoId,
            Long organizationalUnitId, 
            Long outstandingSurveysId,
            Long surveyId,
            Long templateSurveyId,
            Boolean restrictRecordsByDepartment,
            Boolean validateAccessFormDepartment,
            String restrictRecordsField,
            Long restrictRecordsObjId,
            String documentMasterId,
            String documentDescription,
            String documentCode,
            String version
    ) {
        this.documentMasterId = documentMasterId;
        this.documentType = documentType;
        this.retentionTime = retentionTime;
        this.retentionText = retentionText;
        this.storagePlaceId = storagePlaceId;
        this.version = version;
        this.surveyId = surveyId;
        this.outstandingSurveysId = outstandingSurveysId;
        if (businessUnitId != null) {
            this.businessUnit = new BusinessUnitLite(businessUnitId);
        }
        if (organizationalUnitId != null) {
            this.organizationalUnit = new OrganizationalUnitLite(organizationalUnitId);
        }
        if (nodoId != null) {
            this.nodo = new NodeSimple(nodoId);
        }
        if (departmentId != null) {
            this.department = new BusinessUnitDepartmentRef(departmentId);
        }
        if (documentDescription != null) {
            this.document = new Document(documentDescription);
        }
        this.documentCode = documentCode;
        this.templateSurveyId = templateSurveyId;
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
        this.validateAccessFormDepartment = validateAccessFormDepartment;
        this.restrictRecordsField = restrictRecordsField;
        this.restrictRecordsObjId = restrictRecordsObjId;
        this.scope = scope;
    }

    public Request(Long id, String description, Long outstandingSurveysId, Long workflowId) {
        this.id = id;
        this.description = description;
        this.outstandingSurveysId = outstandingSurveysId;
        this.workflowId = workflowId;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    /**
     * No se serializa a JSON!
     *
     * @return
     */
    @JSON(serialize = false)
    @JsonIgnore()
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(mappedBy = "request", fetch = FetchType.LAZY)
    public Set<AutorizationPoolDetails> getAutorizationPoolDetails() {
        return autorizationPoolDetails;
    }

    public void setAutorizationPoolDetails(Set<AutorizationPoolDetails> autorizationPoolDetails) {
        this.autorizationPoolDetails = autorizationPoolDetails;
    }


    @Basic
    @Column(name = "document_master_id", length = 36)
    @Override
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    @Override
    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Basic(optional = false)
    @Column(name = "document_code")
    @Override
    public String getDocumentCode() {
        return documentCode;
    }

    @Override
    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Basic(optional = false)
    @Column(name = "creation_date", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getCreationDate() {
        return creationDate;
    }

    @Override
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Basic(optional = false)
    @Column(name = "type")
    @Override
    public Integer getType() {
        return type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Basic(optional = false)
    @Override
    public String getReazon() {
        return reazon;
    }

    @Override
    public void setReazon(String reazon) {
        this.reazon = reazon;
    }

    @ManyToOne
    @JoinColumn(name = "document_type", referencedColumnName = "document_type_id")
    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    @Column(name = "document_type", updatable = false, insertable = false)
    @Override
    public Long getTypeId() {
        return typeId;
    }

    @Override
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    @Column(name = "retention_time")
    @Override
    public Integer getRetentionTime() {
        return retentionTime;
    }

    @Override
    public void setRetentionTime(Integer retentionTime) {
        this.retentionTime = retentionTime;
    }

    @Column(name = "retention_text")
    @Override
    public Integer getRetentionText() {
        return retentionText;
    }

    @Override
    public void setRetentionText(Integer retentionText) {
        this.retentionText = retentionText;
    }

    @Column(name = "storage_place_id")
    @Override
    public Integer getStoragePlaceId() {
        return storagePlaceId;
    }

    @Override
    public void setStoragePlaceId(Integer storagePlaceId) {
        this.storagePlaceId = storagePlaceId;
    }

    @Override
    public String getVersion() {
        return version;
    }

    @Override
    public void setVersion(String version) {
        this.version = version;
    }

    @Column(name = "document_id", insertable = false, updatable = false)
    @Override
    public Long getDocumentId() {
        return documentId;
    }

    @Override
    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @JoinColumn(name = "document_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    public Document getDocument() {
        return document;
    }

    public void setDocument(Document document) {
        this.document = document;
    }

    @JoinColumn(name = "old_document_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    public Document getOldDocument() {
        return oldDocument;
    }

    public void setOldDocument(Document oldDocument) {
        this.oldDocument = oldDocument;
    }

    @Column(name = "old_document_id", insertable = false, updatable = false)
    @Override
    public Long getOldDocumentId() {
        return oldDocumentId;
    }

    @Override
    public void setOldDocumentId(Long oldDocumentId) {
        this.oldDocumentId = oldDocumentId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3312
        if (!(object instanceof Request)) {
            return false;
        }
        Request other = (Request) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
        }

    @Override
    public String toString() {
        return "DPMS.Mapping.Request[ id=" + id + " ]";
    }

    @Column(name = "autor_id", updatable = false, insertable = false)
    @Override
    public Long getAutorId() {
        return autorId;
    }

    @Override
    public void setAutorId(Long autorId) {
        this.autorId = autorId;
    }
    

    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "autor_id", referencedColumnName = "user_id")
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    @Column(name = "autor_id", updatable = false, insertable = false)
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    @Override
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "organizational_unit_id", referencedColumnName = "organizational_unit_id")
    public OrganizationalUnitLite getOrganizationalUnit() {
        return organizationalUnit;
    }

    public void setOrganizationalUnit(OrganizationalUnitLite organizationalUnit) {
        this.organizationalUnit = organizationalUnit;
    }

    @Override
    @Column(name = "organizational_unit_id", updatable = false, insertable = false)
    public Long getOrganizationalUnitId() {
        return organizationalUnitId;
    }

    @Override
    public void setOrganizationalUnitId(Long organizationalUnitId) {
        this.organizationalUnitId = organizationalUnitId;
    }

    @Column(name = "file_id")
    @Override
    public Long getFileId() {
        return fileId;
    }

    @Override
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "survey_id")
    @Override
    public Long getSurveyId() {
        return surveyId;
    }

    @Override
    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "node_id", referencedColumnName = "intnodoid")
    public NodeSimple getNodo() {
        return nodo;
    }

    public void setNodo(NodeSimple nodo) {
        this.nodo = nodo;
    }

    @Column(name = "node_id", updatable = false, insertable = false)
    @Override
    public Long getNodoId() {
        return nodoId;
    }

    @Override
    public void setNodoId(Long nodoId) {
        this.nodoId = nodoId;
    }

    @OneToOne(mappedBy = "request")
    @Override
    public AutorizationPool getAutorizationPool() {
        return autorizationPool;
    }

    @Override
    public void setAutorizationPool(AutorizationPool autorizationPool) {
        this.autorizationPool = autorizationPool;
    }

    @Override
    @Transient
    public Long getAutorizationPoolId() {
        if (this.autorizationPool == null) {
            return null;
        }
        return this.autorizationPool.getId();
    }

    @Override
    public void setAutorizationPoolId(Long autorizationPoolId) {
        // trasient!
    }

    
    /**
     * Apunta al entity `WorkflowSimple`
     * @return 
     */
    @Column(name = "flujo_id")
    @Override
    public Long getFlujoId() {
        return flujoId;
    }

    @Override
    public void setFlujoId(Long flujoId) {
        this.flujoId = flujoId;
    }


    @ManyToOne
    @Override
    @JoinColumn(name = "business_unit_department_id")
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartmentRef businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }
    
    @ManyToOne
    @Override
    @JoinColumn(name = "business_unit_department_id", updatable = false, insertable = false)
    public BusinessUnitDepartmentRef getBusinessUnitDepartment(){
        if (this.businessUnitDepartment == null) {
            return this.department;
        }
        return businessUnitDepartment;
    }

    /**
     * Apunta al entity `WorkflowSimple`
     * @return 
     */
    @Column(name = "flujo_id", updatable = false, insertable = false)
    @Override
    public Long getWorkflowId() {
        return workflowId;
    }

    @Override
    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @Column(name = "is_busy")
    @Override
    public Integer getIsBusy() {
        return isBusy;
    }

    @Override
    public void setIsBusy(Integer isBusy) {
        this.isBusy = isBusy;
    }

    @Column(name = "time_to_free_minutes")
    @Override
    public Integer getTimeToFreeMinutes() {
        return timeToFreeMinutes;
    }

    @Override
    public void setTimeToFreeMinutes(Integer timeToFreeMinutes) {
        this.timeToFreeMinutes = timeToFreeMinutes;
    }

    @Column(name = "blocked_by")
    @Override
    public Long getBlockedBy() {
        return blockedBy;
    }

    @Override
    public void setBlockedBy(Long blockedBy) {
        this.blockedBy = blockedBy;
    }

    @Column(name = "blocked_by_user_name")
    @Override
    public String getBlockedByName() {
        return blockedByName;
    }

    @Override
    public void setBlockedByName(String blockedByName) {
        this.blockedByName = blockedByName;
    }

    @Column(name = "outstanding_surveys_id")
    @Override
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    @Override
    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "survey_id", insertable = false, updatable = false)
    public SurveySimple getSurvey() {
        return survey;
    }

    public void setSurvey(SurveySimple survey) {
        this.survey = survey;
    }

    @JoinColumn(name = "fill_document_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    public DocumentRef getFillOutDocument() {
        return fillOutDocument;
    }

    public void setFillOutDocument(DocumentRef fillOutDocument) {
        this.fillOutDocument = fillOutDocument;
    }

    @Column(name = "fill_document_id", insertable = false, updatable = false)
    @Override
    public Long getFillOutDocumentId() {
        return fillOutDocumentId;
    }

    @Override
    public void setFillOutDocumentId(Long fillOutDocumentId) {
        this.fillOutDocumentId = fillOutDocumentId;
    }

    @Column(name = "template_survey_id")
    @Override
    public Long getTemplateSurveyId() {
        return templateSurveyId;
    }

    @Override
    public void setTemplateSurveyId(Long templateSurveyId) {
        this.templateSurveyId = templateSurveyId;
    }
    
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Column(name = "restrict_records_department")
    @Override
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    @Override
    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Column(name = "validate_access_department")
    @Override
    public Boolean getValidateAccessFormDepartment() {
        return validateAccessFormDepartment;
    }

    @Override
    public void setValidateAccessFormDepartment(Boolean validateAccessFormDepartment) {
        this.validateAccessFormDepartment = validateAccessFormDepartment;
    }

    @Column(name = "restrict_records_field")
    @Override
    public String getRestrictRecordsField() {
        return restrictRecordsField;
    }

    @Override
    public void setRestrictRecordsField(String restrictRecordsField) {
        this.restrictRecordsField = restrictRecordsField;
    }

    @Column(name = "restrict_records_obj_id")
    @Override
    public Long getRestrictRecordsObjId() {
        return restrictRecordsObjId;
    }

    @Override
    public void setRestrictRecordsObjId(Long restrictRecordsObjId) {
        this.restrictRecordsObjId = restrictRecordsObjId;
    }
    
    @Column(name = "survey_theme_id")
    @Override
    public Long getSurveyThemeId() {
        return surveyThemeId;
    }

    @Override
    public void setSurveyThemeId(Long surveyThemeId) {
        this.surveyThemeId = surveyThemeId;
    }

    @Column(name = "rejection_date")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getRejectionDate() {
        return rejectionDate;
    }

    @Override
    public void setRejectionDate(Date rejectionDate) {
        this.rejectionDate = rejectionDate;
    }
    

    /**
     * @return the scope
     */
    @Column(name = "scope")
    @Override
    public Integer getScope() {
        return scope;
    }

    /**
     * @param scope the scope to set
     */
    @Override
    public void setScope(Integer scope) {
        this.scope = scope;
    }

    @Column(name = "generate_code")
    @Override
    public Integer getGenerateCode() {
        return generateCode;
    }

    @Override
    public void setGenerateCode(Integer generateCode) {
        this.generateCode = generateCode;
    } 

    @Column(name = "dynamic_table_name")
    @Override
    public String getDynamicTableName() {
        return dynamicTableName;
}

    @Override
    public void setDynamicTableName(String dynamicTableName) {
        this.dynamicTableName = dynamicTableName;
    }
    
    @JoinColumn(name = "file_id", referencedColumnName = "id", updatable = false, insertable = false)
    @ManyToOne
    public FileRef getFileContent() {
        return fileContent;
}

    public void setFileContent(FileRef fileContent) {
        this.fileContent = fileContent;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "rejected_by_user", referencedColumnName = "user_id")
    public UserRef getRejectedByUser() {
        return rejectedByUser;
    }


    public void setRejectedByUser(UserRef rejectedByUser) {
        this.rejectedByUser = rejectedByUser;
    }
    
    @Column(name = "rejected_by_user", updatable = false, insertable = false)
    @Override
    public Long getRejectedByUserId() {
        return rejectedByUserId;
    }


    @Override
    public void setRejectedByUserId(Long rejectedByUserId) {
        this.rejectedByUserId = rejectedByUserId;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "impersonated_by", referencedColumnName = "user_id")
    public UserRef getImpersonatedBy() {
        return impersonatedBy;
    }

    public void setImpersonatedBy(UserRef impersonatedBy) {
        this.impersonatedBy = impersonatedBy;
    }
    
    @Column(name = "impersonated_by", updatable = false, insertable = false)
    @Override
    public Long getImpersonatedById() {
        return this.impersonatedById;
    }
    
    @Override
    public void setImpersonatedById(Long impersonatedById) {
        this.impersonatedById = impersonatedById;
    }

    @Column(name = "business_unit_id", updatable = false, insertable = false)
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id", updatable = false, insertable = false)
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    @Column(name = "enable_pdf_viewer")
    @Override
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    @Override
    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }

    @Column(name = "authorizers_names")
    @Override
    public String getAuthorizersNames() {
        return authorizersNames;
    }

    @Override
    public void setAuthorizersNames(String authorizersNames) {
        this.authorizersNames = authorizersNames;
    }
    
    @JoinColumn(name = "collecting_store_responsible", referencedColumnName = "user_id", updatable = false, insertable = false)
    @ManyToOne
    public UserRef getCollectingAndStoreResponsibleUser() {
        return collectingAndStoreResponsibleUser;
    }

    public void setCollectingAndStoreResponsibleUser(UserRef collectingAndStoreResponsibleUser) {
        this.collectingAndStoreResponsibleUser = collectingAndStoreResponsibleUser;
    }
    
    @Column(name = "collecting_store_responsible")
    @Override
    public Long getCollectingAndStoreResponsible() {
        return collectingAndStoreResponsible;
    }

    @Override
    public void setCollectingAndStoreResponsible(Long collectingAndStoreResponsible) {
        this.collectingAndStoreResponsible = collectingAndStoreResponsible;
    }
    
    @Column(name = "collecting_store_resp_desc")
    @Override
    public String getCollectingAndStoreResponsibleDescription() {
        return collectingAndStoreResponsibleDescription;
    }

    @Override
    public void setCollectingAndStoreResponsibleDescription(String collectingAndStoreResponsibleDescription) {
        this.collectingAndStoreResponsibleDescription = collectingAndStoreResponsibleDescription;
    }    
    
    @Column(name = "information_classification")
    @Override
    public Long getInformationClassification() {
        return informationClassification;
    }

    @Override
    public void setInformationClassification(Long informationClassification) {
        this.informationClassification = informationClassification;
    }
    
    @Column(name = "disposition")
    @Override
    public Long getDisposition() {
        return disposition;
    }

    @Override
    public void setDisposition(Long disposition) {
        this.disposition = disposition;
    }

    @Column(name = "slimReportName")
    @Override
    public String getSlimReportName() {
        return slimReportName;
    }

    @Override
    public void setSlimReportName(String slimReportName) {
        this.slimReportName = slimReportName;
    }

    @Transient
    @Override
    public String getSurveyRequestMode() {
        return surveyRequestMode;
    }

    @Override
    public void setSurveyRequestMode(String surveyRequestMode) {
        this.surveyRequestMode = surveyRequestMode;
    }

    @Transient
    @Override
    public String getConditionalValidatorCacheId() {
        return conditionalValidatorCacheId;
    }

    @Override
    public void setConditionalValidatorCacheId(String conditionalValidatorCacheId) {
        this.conditionalValidatorCacheId = conditionalValidatorCacheId;
    }
    
}
