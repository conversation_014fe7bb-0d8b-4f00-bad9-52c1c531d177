package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "node_profile")
public class NodeProfile extends CompositeStandardEntity<NodeProfilePk> implements Serializable, ILinkedComposityGrid<NodeProfilePk> {

    private NodeProfilePk id;

    public NodeProfile() {}

    // Constructor con argumentos
    public NodeProfile(NodeProfilePk id) {
        this.id = id;
    }

    @Override
    @EmbeddedId
    public NodeProfilePk getId() {
        return id;
    }

    @Override
    public void setId(NodeProfilePk id) {
        this.id = id;
    }
    @Override
    public NodeProfilePk identifuerValue() {
        return getId();
    }
}
