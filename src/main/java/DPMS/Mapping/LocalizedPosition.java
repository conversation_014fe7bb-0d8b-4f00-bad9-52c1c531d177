package DPMS.Mapping;

import Framework.Config.BaseDomainObject;
import bnext.reference.BusinessUnitRef;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "puesto")
public class LocalizedPosition extends BaseDomainObject<Long> implements Serializable {
  
  private static final long serialVersionUID = 1L;
  
  private Long id;
  private String code;
  private String description;
  private Integer status;
  
  private OrganizationalUnitSimple corp;
  private BusinessUnitRef une;

  public LocalizedPosition() {
  }

  public LocalizedPosition(Long id) {
    this.id = id;
  }

  @Id
  @Basic(optional = false)
  @Column(name = "puesto_id", nullable = false, precision = 19)
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  @Override
  public Long identifuerValue() {
      return id;
  }

  
  @Column(name = "vch_clave", nullable = false)
  public String getCode() {
    return code;
  }
  
  public void setCode(String code) {
    this.code = code;
  }
  
  @LocalizedField
  @Column(name = "vch_descripcion", nullable = false)
  public String getDescription() {
    return description;
  }
  
  public void setDescription(String description) {
    this.description = description;
  }
  
  @Column(name = "int_estado")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  @JoinColumn(name = "organizational_unit_id", referencedColumnName = "organizational_unit_id")
  @ManyToOne
  public OrganizationalUnitSimple getCorp() {
    return corp;
  }

  public void setCorp(OrganizationalUnitSimple corp) {
    this.corp = corp;
  }

  @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
  @OneToOne(fetch = FetchType.EAGER)
  public BusinessUnitRef getUne() {
    return une;
  }

  public void setUne(BusinessUnitRef une) {
    this.une = une;
  }
  
}