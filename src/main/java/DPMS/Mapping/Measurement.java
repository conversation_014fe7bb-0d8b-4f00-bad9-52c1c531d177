package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "measurement")
public class Measurement extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String PREFIX = "MEA-";
    
    private Long patternId;
    private Long variableId;
    private Long unitId;
    private String point;
    private Integer more;
    private Integer less;
    private Double treshold;
    private Double indicatorMeasurement;
    private Double patternMeasurement;
    private String absoluteError;
    private String secondMeasurement;
    private Pattern pattern;
    private Unit unit;
    private Variable variable;

    public Measurement() {
        id=-1L;
    }

    @Id
    @Basic
    @Column(name = "measurement_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long Id) {
        this.id = Id;
    }

    @Column(name = "pattern_id")
    public Long getPatternId() {
        return patternId;
    }

    public void setPatternId(Long patternId) {
        this.patternId = patternId;
    }

    @Column(name = "variable_id")
    public Long getVariableId() {
        return variableId;
    }

    public void setVariableId(Long variableID) {
        this.variableId = variableID;
    }

    @Column(name = "unit_id")
    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    @Column(name = "point")
    public String getPoint() {
        return point;
    }

    public void setPoint(String point) {
        this.point = point;
    }

    @Column(name = "more")
    public Integer getMore() {
        return more;
    }

    public void setMore(Integer more) {
        this.more = more;
    }

    @Column(name = "less")
    public Integer getLess() {
        return less;
    }

    public void setLess(Integer less) {
        this.less = less;
    }

    @Column(name = "treshold")
    public Double getTreshold() {
        return treshold;
    }

    public void setTreshold(Double treshold) {
        this.treshold = treshold;
    }

    @Column(name = "indicator_measurement")
    public Double getIndicatorMeasurement() {
        return indicatorMeasurement;
    }

    public void setIndicatorMeasurement(Double indicatorMeasurement) {
        this.indicatorMeasurement = indicatorMeasurement;
    }

    @Column(name = "pattern_measurement")
    public Double getPatternMeasurement() {
        return patternMeasurement;
    }

    public void setPatternMeasurement(Double patternMeasurement) {
        this.patternMeasurement = patternMeasurement;
    }

    @Column(name = "absolute_error")
    public String getAbsoluteError() {
        return absoluteError;
    }

    public void setAbsoluteError(String absoluteError) {
        this.absoluteError = absoluteError;
    }

    @Column(name = "second_measurement")
    public String getSecondMeasurement() {
        return secondMeasurement;
    }

    public void setSecondMeasurement(String secondMeasurement) {
        this.secondMeasurement = secondMeasurement;
    }

    @Override
    public String toString() {
        return "DPMS.Measurement[ Id=" + id + " ]";
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "pattern_id", name = "pattern_id", insertable = false, updatable = false)
    public Pattern getPattern() {
        return pattern;
    }

    public void setPattern(Pattern pattern) {
        this.pattern = pattern;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "unit_id", name = "unit_id", insertable = false, updatable = false)
    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "variable_id", name = "variable_id", insertable = false, updatable = false)
    public Variable getVariable() {
        return variable;
    }

    public void setVariable(Variable variable) {
        this.variable = variable;
    }
}
