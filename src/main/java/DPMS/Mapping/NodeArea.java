package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import qms.util.interfaces.ILinkedComposityGrid;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblcarpetaarea")

public class NodeArea extends CompositeStandardEntity<NodeAreaPK> implements Serializable, ILinkedComposityGrid<NodeAreaPK> {

    private static final long serialVersionUID = 1L;
    
    private NodeAreaPK id;
    private Date tspfechahoracreacion;

    public NodeArea() {
        this.tspfechahoracreacion = new Date();
    }

    public NodeArea(NodeAreaPK id) {
        this();
        this.id = id;
    }

    public NodeArea(Long intnodoid, Long intareaid) {
        this();
        this.id = new NodeAreaPK(intnodoid, intareaid);
    }

    @Override
    @EmbeddedId
    public NodeAreaPK getId() {
        return id;
    }

    @Override
    public NodeAreaPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(NodeAreaPK id) {
        this.id = id;
    }

    @Column(name = "tspfechahoracreacion", insertable = false, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return tspfechahoracreacion;
    }

    public void setFechaCreacion(Date tspfechahoracreacion) {
        this.tspfechahoracreacion = tspfechahoracreacion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3329
        if (!(object instanceof NodeArea)) {
            return false;
        }
        NodeArea other = (NodeArea) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.NodeArea[ id=" + id + " ]";
    }
}
