package DPMS.Mapping;

import Framework.Config.StandardEntity;
import bnext.aspect.IExcludeLogging;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "audit_score")
public class AuditScore extends StandardEntity<AuditScore> implements Serializable, IExcludeLogging {

    private static final long serialVersionUID = 1L;
    private Double score;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer addActivity;

    public AuditScore() {
    }

    public AuditScore(Long id) {
        this.id = id;
    }


    @Id
    @Basic(optional = false)
    @Column(name = "audit_score_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override 
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof AuditScore)) {
            return false;
        }
        AuditScore other = (AuditScore) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.AuditScore[ id=" + id + " ]";
    }


    @Column(name = "score")
    public Double getScore() { 
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    /**
     * @return the addActivity
     */
    @Column(name = "add_activity")
    public Integer getAddActivity() {
        return addActivity;
    }

    /**
     * @param addActivity the addActivity to set
     */
    public void setAddActivity(Integer addActivity) {
        this.addActivity = addActivity;
    }
    
    
}
