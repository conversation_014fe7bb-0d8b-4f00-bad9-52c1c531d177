package DPMS.Mapping;

import Framework.Config.StandardEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Immutable;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "poll_list")
@Immutable
public class PollList extends StandardEntity<PollList> implements Serializable {

    private static final Long serialVersionUID = 1L;
    public static final Integer STATUS_PLANNED = 1;
    public static final Integer STATUS_IN_PROCESS = 2;
    public static final Integer STATUS_DONE = 3;
    public static final Integer STATUS_CANCELED = 4;
    
    private Date dteStart;
    private Date dteEnd;
    private Long total;
    private Long finished;
    private Long started;
    private double percentagefinished;
    private double percentagestarted;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Long surveyId;
    

    public PollList() {
    }

    public PollList(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "poll_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Basic(optional = false)
    @Column(name = "dte_start", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getDteStart() {
        return dteStart;
    }

    public void setDteStart(Date dteStart) {
        this.dteStart = dteStart;
    }

    @Basic(optional = false)
    @Column(name = "dte_end", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getDteEnd() {
        return dteEnd;
    }

    public void setDteEnd(Date dteEnd) {
        this.dteEnd = dteEnd;
    }

    @Column(name = "total")
    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    @Column(name = "finished")
    public Long getFinished() {
        return finished;
    }

    public void setFinished(Long finished) {
        this.finished = finished;
    }

    @Column(name = "started")
    public Long getStarted() {
        return started;
    }

    public void setStarted(Long started) {
        this.started = started;
    }

    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }
    
    @Transient
    public double getPercentagefinished() {
        float f = this.finished;
        float t = this.total;
        BigDecimal percentageFinished = new BigDecimal(f/t*100);
        return percentageFinished.setScale(2,RoundingMode.CEILING).doubleValue();
    }

    public void setPercentagefinished(double percentagefinished) {
        this.percentagefinished = percentagefinished;
    }

    @Transient
    public double getPercentagestarted() {
        double s = this.started;
        double t = this.total;
        BigDecimal precentageStarted = new BigDecimal(s/t*100);
        return precentageStarted.setScale(2,RoundingMode.CEILING).doubleValue();
    }

    public void setPercentagestarted(double percentagestarted) {
        this.percentagestarted = percentagestarted;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PollList)) {
            return false;
        }
        PollList other = (PollList) object;
        return !((this.id == null && other.id != null) 
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Poll[ id=" + id + " ]";
    }
}