package DPMS.Mapping;

import Framework.Config.BaseDomainObject;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 *
 * <AUTHOR>
 * @since 28/05/2014
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "supported_languages")
public class SupportedLanguages  extends BaseDomainObject<String> implements Serializable{
    private static final long serialVersionUID = 1L;
    
    private String id;
    private String description;

    public SupportedLanguages() {
    }

    public SupportedLanguages(String id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String identifuerValue() {
        return id;
    }

 
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String name) {
        this.description = name;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3236
        if (!(object instanceof SupportedLanguages)) {
            return false;
        }
        SupportedLanguages other = (SupportedLanguages) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.SupportedLanguages[ Id=" + id + " ]";
    }
    
}
