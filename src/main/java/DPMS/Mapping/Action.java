package DPMS.Mapping;

import Framework.Config.ITextHasValue;
import Framework.Config.StandardEntity;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericHibernateDAO;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.StrongBaseSourceAPE;
import ape.pending.core.StrongBaseTypedAPE;
import bnext.reference.PriorityRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.common.Properties;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;
import qms.document.entity.DocumentLite;
import qms.finding.entity.FindingActivity;
import qms.finding.entity.FindingType;
import qms.util.HTMLEncoding;
import qms.util.interfaces.IBusinessUnitDepartmentId;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblacciongenerica")
@RootSoftAPE( 
    mappedBy = "id.findingId",
    mappedByClass = FindingActivity.class
)
public class Action extends StandardEntity<Action> implements StrongBaseTypedAPE<FindingType>, StrongBaseSourceAPE<ActionSources>, IBusinessUnitDepartmentId, Serializable, IAuditableEntity {

    public enum REQUIRED_ANALYSIS {
        REQUIRE_YES(1), REQUIRE_NO(0), REQUIRE_SELECT(-1);
        private int value;
        private REQUIRED_ANALYSIS(Integer value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
        
    }
    
    public static enum findingsStatus {
        ONTIME(0),
        EXPIRESOON(1),
        ATTENDED(2),
        OVERDUE(3)
        ;
        private final Integer value;
        private findingsStatus(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return this.value;
        }
    }
    
    /**
     * @Reportada por asignar resposnabel de analizar causas, REPORTED(1),
     * @Asignada por analizar causas, ASSIGNED(2),
     * @Analizada y en implementación, ANALIZED(3),
     * @Implementada y verificada, IMPLEMENTED(4),
     * @Aprobada, APPROVED(5),
     * @Cerrada, CLOSED(6),
     * @Cerrada efectiva, CLOSED_EFFECTIVE(12),
     * @Cerrada no efectiva, CLOSED_UNEFFECTIVE(13),
     * @Cerrada cancelada, CANCELED(14)
     */
    public enum STATUS implements IStatusEnum {
        REPORTED(1,IStatusEnum.COLOR_RED),//Reportada por asignar resposnabel de analizar causas, value 1
        ASSIGNED(2, IStatusEnum.COLOR_YELLOW),//Asignada por analizar causas, value 2
        ANALIZED(3, IStatusEnum.COLOR_ORANGE),//Analizada y en implementación, value 3
        IMPLEMENTED(4, IStatusEnum.COLOR_GREEN),//Implementada y verificada, value 4
        APPROVED(5, IStatusEnum.COLOR_CYAN),//Aprobada, value 5
        CLOSED(6, IStatusEnum.COLOR_LOCK),//Cerrada, value 6
        CLOSED_EFFECTIVE(12, IStatusEnum.COLOR_BEIGE),//Cerrada efectiva, value 12
        CLOSED_UNEFFECTIVE(13, IStatusEnum.COLOR_GRAY),//Cerrada no efectiva, value 13
        CANCELED(14, IStatusEnum.COLOR_BLACK);//Cerrada cancelada, value 14;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return REPORTED;
        }

        public String intValue() {
            return value.toString();
        }
    }
    
    /**
     * Req 3208
     */
    public static final Integer STATUS_REPORTED = 1;
    public static final String ACTION_TYPE_CORRECTIVE = "ACC";    //Correctiva
    public static final String ACTION_TYPE_PREVENTIVE = "APP";    //Preventiva
    public static final String ACTION_TYPE_IMPROVEMENT = "AMC";   //Mejora continua
    public static final String ACTION_TYPE_PRODUCT = "APD";       //Producto no conforme
    public static final String ACTION_TYPE_PROJECT = "APY";       //Sobre proyecto(s)
    public static final String ALL = "%";                         //Todas
    public static final Integer CLOSED = 1;
    public static final Integer OPENED = 2;
    
    private static final long serialVersionUID = 1L;
    
    private Long autorid;
    private Date fechahoracreacion = new Date();
    private String situacion;
    private String detalleFuente;
    private String consecuencia;
    private Long ubicacionId;
    private Long priorityId;
    private Long typeId;
    private Long systemId;
    private Long aceptadorId;
    private Long responsableId;
    private Integer procede;
    private Date fechaAceptacion;
    private String analisisCausas;
    private Date evaluacion;
    private Integer resultadoEvaluacion;
    private Long aprobadorId;
    private Integer aprobacion;
    private Integer cerradaAbierta = OPENED;
    private Date fechaAprobacion;
    private Date fechaCerrada;
    private String fechaCumplimiento;
    private String razonNoProcede;
    private String resultadosEfectividad;
    private String clavePadre;
    private String contingencia;
    private Long intDetalleFuente;
    private BusinessUnitDepartmentLoad department;
    private Long businessUnitDepartmentId;
    private UserRef attendant;
    private UserRef author;
    private NodeSimple nodo;
    private ActionSources fuente;
    private PriorityRef priority;
    private Long sourceId;
    private Set<DocumentLite> documents;
    private Set<FilesLite> evidence;
    private Set<FilesLite> files;
    private ClauseType system;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer findingsStatus = 0;
    private Integer deleted = 0;
    private Long outstandingSurveyId;
    private Integer requireAnalysis;
    private String planeTextSituation;
    private String planeTextConsequence;
    private String findingsDocumentIds;
    private String findingsEvidenceDocumentIds;
    private Date analysisDate;
    
    public Action() {
    }

    public Action(Long id) {
        this.id = id;
    }

    public Action(Long id, Long intautorid, Integer intestado, Date tspfechahoracreacion, String txtsituacion, String vchdetallefuente, String txtconsecuencia, Long intubicacionid) {
        this.id = id;
        this.autorid = intautorid;
        this.setStatus(intestado);
        this.fechahoracreacion = tspfechahoracreacion;
        this.situacion = txtsituacion;
        this.detalleFuente = vchdetallefuente;
        this.consecuencia = txtconsecuencia;
        this.ubicacionId = intubicacionid;
    }

    @Basic(optional = true)
    @Column(name = "type_id", nullable = true)
    @Override
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intacciongenericaid", nullable = false)
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intestado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }
    
    @Column(name = "findingsStatus")
    public Integer getFindingsStatus() {
        return findingsStatus;
    }

    public void setFindingsStatus(Integer findingsStatus) {
        if(findingsStatus == null) {
            findingsStatus = 0;
        }
        this.findingsStatus = findingsStatus;
    }
    
    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Basic(optional = false)
    @Column(name = "intautorid", nullable = false)
    public Long getAutorId() {
        return autorid;
    }

    public void setAutorId(Long intautorid) {
        this.autorid = intautorid;
    }

    @Basic(optional = false)
    @Column(name = "tspfechahoracreacion", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return fechahoracreacion;
    }

    public void setFechaCreacion(Date tspfechahoracreacion) {
        this.fechahoracreacion = tspfechahoracreacion;
    }

    @Basic(optional = false)
    @Column(name = "txtsituacion", nullable = false, length = 255)
    public String getSituacion() {
        return situacion;
    }

    public void setSituacion(String txtsituacion) {
        this.situacion = txtsituacion;
    }

    @Basic(optional = false)
    @Column(name = "vchdetallefuente", nullable = false, length = 255)
    public String getDetalleFuente() {
        return detalleFuente;
    }

    public void setDetalleFuente(String vchdetallefuente) {
        this.detalleFuente = vchdetallefuente;
    }

    @Basic(optional = false)
    @Column(name = "txtconsecuencia", nullable = false, length = 7999)
    public String getConsecuencia() {
        return consecuencia;
    }

    public void setConsecuencia(String txtconsecuencia) {
        this.consecuencia = txtconsecuencia;
    }

    @Basic(optional = false)
    @Column(name = "intubicacionid", nullable = false)
    public Long getUbicacionId() {
        return ubicacionId;
    }

    public void setUbicacionId(Long ubicacionid) {
        this.ubicacionId = ubicacionid;
    }

    @Column(name = "intaceptadorid")
    public Long getAceptadorId() {
        return aceptadorId;
    }

    public void setAceptadorId(Long intaceptadorid) {
        this.aceptadorId = intaceptadorid;
    }

    @Column(name = "intresponsableid")
    public Long getResponsableId() {
        return responsableId;
    }

    public void setResponsableId(Long intresponsableid) {
        this.responsableId = intresponsableid;
    }

    @Column(name = "intprocede")
    public Integer getProcede() {
        return procede;
    }

    public void setProcede(Integer intprocede) {
        this.procede = intprocede;
    }

    @Column(name = "tspfechahoraaceptacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAceptacion() {
        return fechaAceptacion;
    }

    public void setFechaAceptacion(Date tspfechahoraaceptacion) {
        this.fechaAceptacion = tspfechahoraaceptacion;
    }

    @Column(name = "txtanalisiscausas", length = 7999)
    public String getAnalisisCausas() {
        return analisisCausas;
    }

    public void setAnalisisCausas(String txtanalisiscausas) {
        this.analisisCausas = txtanalisiscausas;
    }

    @Column(name = "tspevaluacion")
    @Temporal(TemporalType.DATE)
    public Date getEvaluacion() {
        return evaluacion;
    }

    public void setEvaluacion(Date tspevaluacion) {
        this.evaluacion = tspevaluacion;
    }

    @Column(name = "intresultadoevaluacion")
    public Integer getResultadoEvaluacion() {
        return resultadoEvaluacion;
    }

    public void setResultadoEvaluacion(Integer intresultadoevaluacion) {
        this.resultadoEvaluacion = intresultadoevaluacion;
    }

    @Column(name = "intaprobadorid")
    public Long getAprobadorId() {
        return aprobadorId;
    }

    public void setAprobadorId(Long intaprobadorid) {
        this.aprobadorId = intaprobadorid;
    }

    @Column(name = "intaprobacion")
    public Integer getAprobacion() {
        return aprobacion;
    }

    public void setAprobacion(Integer intaprobacion) {
        this.aprobacion = intaprobacion;
    }

    @Column(name = "intcerradaabierta")
    public Integer getCerradaAbierta() {
        return cerradaAbierta;
    }

    public void setCerradaAbierta(Integer intcerradaabierta) {
        this.cerradaAbierta = intcerradaabierta;
    }

    @Column(name = "tspfechahoraaprobacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAprobacion() {
        return fechaAprobacion;
    }

    public void setFechaAprobacion(Date tspfechahoraaprobacion) {
        this.fechaAprobacion = tspfechahoraaprobacion;
    }

    @Column(name = "tspfechahoracerrada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCerrada() {
        return fechaCerrada;
    }

    public void setFechaCerrada(Date tspfechahoracerrada) {
        this.fechaCerrada = tspfechahoracerrada;
    }

    @Column(name = "vchfechacumplimiento", length = 255)
    public String getFechaCumplimiento() {
        return fechaCumplimiento;
    }

    public void setFechaCumplimiento(String vchfechacumplimiento) {
        this.fechaCumplimiento = vchfechacumplimiento;
    }

    @Column(name = "txtrazonnoprocede", length = 7999)
    public String getRazonNoProcede() {
        return razonNoProcede;
    }

    public void setRazonNoProcede(String txtrazonnoprocede) {
        this.razonNoProcede = txtrazonnoprocede;
    }

    @Column(name = "txtresultadosefectividad", length = 7999)
    public String getResultadosEfectividad() {
        return resultadosEfectividad;
    }

    public void setResultadosEfectividad(String txtresultadosefectividad) {
        this.resultadosEfectividad = txtresultadosefectividad;
    }

    @Column(name = "vchclavepadre", length = 255)
    public String getClavePadre() {
        return clavePadre;
    }

    public void setClavePadre(String vchclavepadre) {
        this.clavePadre = vchclavepadre;
    }

    @Column(name = "txtcontingencia", length = 7999)
    public String getContingencia() {
        return contingencia;
    }

    public void setContingencia(String txtcontingencia) {
        this.contingencia = txtcontingencia;
    }

    @Column(name = "intdetallefuente")
    public Long getIntDetalleFuente() {
        return intDetalleFuente;
    }

    public void setIntDetalleFuente(Long intdetallefuente) {
        this.intDetalleFuente = intdetallefuente;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "intubicacionid",
            insertable = false, updatable = false)
    public BusinessUnitDepartmentLoad getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentLoad department) {
        this.department = department;
    }

    @Column(name = "intubicacionid", insertable = false, updatable = false)
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "intresponsableid",
            insertable = false, updatable = false)
    public UserRef getAttendant() {
        return attendant;
    }

    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }
    
    @OneToOne
    @JoinColumn(referencedColumnName = "clause_type_id", name = "clause_type_id",
            insertable = false, updatable = false)
    public ClauseType getSystem() {
        return system;
    }
    
    public void setSystem(ClauseType system) {
        this.system = system;
    }

    @Basic(optional = true)
    @Column(name = "priority_id", nullable = true)
    public Long getPriorityId() {
        if(priorityId == null) {
            priorityId = -2L;
        }
        return priorityId;
    }

    public void setPriorityId(Long priorityId) {
        if(priorityId == null) {
            priorityId = -2L;
        }
        this.priorityId = priorityId;
    }

    @Basic(optional = true)
    @Column(name = "clause_type_id")
    public Long getSystemId() {
        if(systemId == null) {
            systemId = -2L;
        }
        return systemId;
    }

    public void setSystemId(Long systemId) {
        if(systemId == null) {
            systemId = -2L;
        }
        this.systemId = systemId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3209
        if (!(object instanceof Action)) {
            return false;
        }
        Action other = (Action) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.GenericAction[ intacciongenericaid=" + id + " ]";
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "intautorid",
            insertable = false, updatable = false)
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    @JoinColumn(name = "intfuente", referencedColumnName = "inttipoid")
    @ManyToOne(fetch = FetchType.EAGER)
    public ActionSources getFuente() {
        return this.fuente;
    }

    public void setFuente(ActionSources fuente) {
        this.fuente = fuente;
    }

    @JoinColumn(name = "priority_id", referencedColumnName = "priority_id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public PriorityRef getPriority() {
        return priority;
    }

    public void setPriority(PriorityRef priority) {
        this.priority = priority;
    }

    @Override
    @Column(name = "intfuente", updatable= false, insertable = false)
    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * @return the nodo
     */
    @OneToOne
    @JoinColumn(referencedColumnName = "intnodoid", name = "intnodoid")
    public NodeSimple getNodo() {
        return nodo;
    }

    /**
     * @param nodo the nodo to set
     */
    public void setNodo(NodeSimple nodo) {
        this.nodo = nodo;
    }

    @Transient
    public Set<DocumentLite> getDocuments() {
        return documents;
    }

    public void setDocuments(Set<DocumentLite> documents) {
        this.documents = documents;
    }
    public void strDocuments(HashSet<DocumentLite> documents) {
        this.documents = documents;
    }

    public void setFiles(Set<FilesLite> files) {
        this.files = files;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "action_files", joinColumns = {
        @JoinColumn(name = "action_id", referencedColumnName = "intacciongenericaid")},
        inverseJoinColumns = @JoinColumn(name = "file_id", referencedColumnName = "id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<FilesLite> getFiles() {
        return files;
    }
    
    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "action_evidence_files", joinColumns = {
        @JoinColumn(name = "action_id", referencedColumnName = "intacciongenericaid")},
        inverseJoinColumns = @JoinColumn(name = "file_id", referencedColumnName = "id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<FilesLite> getEvidence() {
        return evidence;
    }

    public void setEvidence(Set<FilesLite> evidence) {
        this.evidence = evidence;
    }
    public void strEvidence(HashSet<FilesLite> evidence) {
        this.evidence = evidence;
    }
    
    @Override
    @Column(name = "tspfechahoracreacion", updatable = false, insertable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }
    
    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Transient
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "intautorid", updatable = false, insertable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Transient
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Column(name = "outstanding_survey_id")
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }
    
    @Column(name = "require_analysis")
    public Integer getRequireAnalysis() {
        return requireAnalysis;
    }

    public void setRequireAnalysis(Integer requireAnalysis) {
        this.requireAnalysis = requireAnalysis;
    }
    @Column(name = "plane_text_situation")
    public String getPlaneTextSituation() {
        return planeTextSituation;
    }

    public void setPlaneTextSituation(String planeTextSituation) {
        this.planeTextSituation = planeTextSituation;
    }

    @Column(name = "plane_text_consequence")
    public String getPlaneTextConsequence() {
        return planeTextConsequence;
    }

    public void setPlaneTextConsequence(String planeTextConsequence) {
        this.planeTextConsequence = planeTextConsequence;
    }

    @Column(name = "analysis_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getAnalysisDate() {
        return analysisDate;
    }

    public void setAnalysisDate(Date analysisDate) {
        this.analysisDate = analysisDate;
    }

    @Transient
    public String getFindingsDocumentIds() {
        return findingsDocumentIds;
    }

    public void setFindingsDocumentIds(String findingsDocumentIds) {
        this.findingsDocumentIds = findingsDocumentIds;
    }
    
    @Transient
    public String getFindingsEvidenceDocumentIds() {
        return findingsEvidenceDocumentIds;
    }

    public void setFindingsEvidenceDocumentIds(String findingsEvidenceDocumentIds) {
        this.findingsEvidenceDocumentIds = findingsEvidenceDocumentIds;
    }

    /**
     * Los datos del correo de equipos
     *
     * @return una lista de TextHasValue con los datos del correo electronico
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    public List<ITextHasValue> MailData() {
        Properties p = new Properties();
        GenericHibernateDAO<BusinessUnitDepartmentLoad, Long> daoBUD = new GenericHibernateDAO<BusinessUnitDepartmentLoad, Long>() {
        };
        GenericHibernateDAO<UserRef, Long> daoUIN = new GenericHibernateDAO<UserRef, Long>() {
        };
        List<ITextHasValue> list = new ArrayList<ITextHasValue>(Arrays.asList(new TextHasValue[]{
            new TextHasValue(p.tags2.getString("action.mail.code"), getCode()),
            new TextHasValue(p.tags2.getString("action.mail.title"), HTMLEncoding.escapeAccentsHTML(getDescription()))
        }));

        if (autorid != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.author"), daoUIN.HQLT_findById(autorid).getDescription()));
        }
        if (fechahoracreacion != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.created_on"), Utilities.formatDateBy(fechahoracreacion, "dd/MM/yyyy")));
        }
        if (detalleFuente != null) {
            if (!detalleFuente.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.source"), detalleFuente));
            }
        }
        if (situacion != null) {
            if (!situacion.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.action"), situacion));
            }
        }
        if (consecuencia != null) {
            if (!consecuencia.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.consequence"), consecuencia));
            }
        }
        if (ubicacionId != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.department"), daoBUD.HQLT_findById(ubicacionId).getDescription()));
        }
        if (aceptadorId != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.accepted_by"), daoUIN.HQLT_findById(autorid).getDescription()));
        }
        if (fechaAceptacion != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.accepted_on"), Utilities.formatDateBy(fechaAceptacion, "dd/MM/yyyy")));
        }
        if (responsableId != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.attendant"), daoUIN.HQLT_findById(responsableId).getDescription()));
        }
        if (procede != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.does_it_proceed"), procede == 0 ? p.tags2.getString("action.mail.not_proceed") : p.tags2.getString("action.mail.proceed")));
        }
        if (analisisCausas != null) {
            if (!analisisCausas.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.root_causes_analisys"), analisisCausas));
            }
        }
        if (evaluacion != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.evaluation_date"), Utilities.formatDateBy(evaluacion, "dd/MM/yyyy")));
        }
        if (resultadoEvaluacion != null) {
            if (!resultadoEvaluacion.toString().isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.evaluation_result"), resultadoEvaluacion.toString()));
            }
        }
        if (aprobadorId != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.approved_by"), daoUIN.HQLT_findById(aprobadorId).getDescription()));
        }
        if (aprobacion != null) {
            if (!aprobacion.toString().isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.approval"), aprobacion.toString()));
            }
        }
        if (fechaAprobacion != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.approved_on"), Utilities.formatDateBy(fechaAprobacion, "dd/MM/yyyy")));
        }
        if (cerradaAbierta != null) {
            if (!cerradaAbierta.toString().isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.status"), cerradaAbierta == 2 ? p.tags2.getString("action.mail.open") :p.tags2.getString("action.mail.closed")));
            }
        }
        if (fechaCerrada != null) {
            list.add(new TextHasValue(p.tags2.getString("action.mail.closed_on"), Utilities.formatDateBy(fechaCerrada, "dd/MM/yyyy")));
        }
        if (documents != null) {
            if (evidence != null) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.attachment_number"), String.valueOf(evidence.size() + documents.size())));
            } else {
                list.add(new TextHasValue(p.tags2.getString("action.mail.attachment_number"), String.valueOf(documents.size())));
            }
        }
        if (fechaCumplimiento != null) {
            if (!fechaCumplimiento.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.accomplished_on"), fechaCumplimiento));
            }
        }
        if (razonNoProcede != null) {
            if (!razonNoProcede.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.not_proceeding_reazon"), razonNoProcede));
            }
        }
        if (resultadosEfectividad != null) {
            if (!resultadosEfectividad.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.effectiveness_result"), resultadosEfectividad));
            }
        }
        if (clavePadre != null) {
            if (!clavePadre.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.parent_action"), clavePadre));
            }
        }
        if (contingencia != null) {
            if (!contingencia.isEmpty()) {
                list.add(new TextHasValue(p.tags2.getString("action.mail.contingency"), contingencia));
            }
        }
        return list;
    }
    
}
