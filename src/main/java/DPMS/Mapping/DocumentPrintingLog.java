package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name="document_printing_log")
public class DocumentPrintingLog extends DomainObject implements Serializable {
  
  private static final long serialVersionUID = 1L;
  
  private DocumentPrinting documentPrinting;
  private Long printBy;
  private Date printDate;
  private UserRef user;

  @Id
  @Override
  @Basic(optional=false)
  @Column(name="id",nullable=false,precision=19,scale=0)
  @GenericGenerator(name="increment",strategy="increment")
  @GeneratedValue(strategy=GenerationType.SEQUENCE,generator="increment")
  public Long getId() {
    return id;
  }

  @Override
  public void setId(Long id) {
    this.id = id;
  }
  
  @ManyToOne
  @JoinColumn(name="document_printing_id",referencedColumnName="id")
  public DocumentPrinting getDocumentPrinting() {
    return documentPrinting;
  }

  public void setDocumentPrinting(DocumentPrinting documentPrinting) {
    this.documentPrinting = documentPrinting;
  }

  @Column(name="print_by")
  public Long getPrintBy() {
    return printBy;
  }

  public void setPrintBy(Long printBy) {
    this.printBy = printBy;
  }

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name="print_date")
  public Date getPrintDate() {
    return printDate;
  }

  public void setPrintDate(Date printDate) {
    this.printDate = printDate;
  }

  @JsonIgnore(true)
  @OneToOne(optional=true,fetch=FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(name="print_by",referencedColumnName="user_id",insertable=false,updatable=false)
  })
  public UserRef getUser() {
    return user;
  }

  public void setUser(UserRef user) {
    this.user = user;
  }
  
}
