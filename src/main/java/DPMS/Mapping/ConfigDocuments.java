/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.Mapping;

import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "config_documents")
public class ConfigDocuments extends StandardEntity<ConfigDocuments> implements Serializable {

    private static final long serialVersionUID = 1L;

    private String version;
    private Date creationDate;
    private Date lastModificationDate;
    private String firstName;
    private Integer isBackup;
    private String type;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long nodoId;
    private Long fileId;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private BusinessUnitRef businessUnit;
    private BusinessUnitDepartmentRef department;

    public ConfigDocuments() {
    }

    @Basic(optional = false)
    @Column(name = "id")
    @Id
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Column(name = "version")
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Basic(optional = false)
    @Column(name = "creation_date", updatable = false)
    @Temporal(TemporalType.DATE)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Basic(optional = false)
    @Column(name = "last_modification_date")
    @Temporal(TemporalType.DATE)
    public Date getLastModificationDate() {
        return lastModificationDate;
    }

    public void setLastModificationDate(Date lastModificationDate) {
        this.lastModificationDate = lastModificationDate;
    }

    @Column(name = "first_name")
    public String getAuthor() {
        return firstName;
    }

    public void setAuthor(String firstName) {
        this.firstName = firstName;
    }

    @Column(name = "is_backup")
    public Integer getIsBackup() {
        return isBackup;
    }

    public void setIsBackup(Integer isBackup) {
        this.isBackup = isBackup;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return the businessUnitId
     */
    @Basic(optional = false)
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    /**
     * @param businessUnitId the businessUnitId to set
     */
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    /**
     * @return the businessUnitDepartmentId
     */
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    /**
     * @param businessUnitDepartmentId the businessUnitDepartmentId to set
     */
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    /**
     * @return the nodoId
     */
    @Basic(optional = false)
    @Column(name = "node_id")
    public Long getNodoId() {
        return nodoId;
    }

    /**
     * @param nodoId the nodoId to set
     */
    public void setNodoId(Long nodoId) {
        this.nodoId = nodoId;
    }

    /**
     * @return the fileId
     */
    @Basic(optional = false)
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    /**
     * @param fileId the fileId to set
     */
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id", updatable = false, insertable = false)
    public BusinessUnitRef getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitRef businessUnit) {
        this.businessUnit = businessUnit;
    }
    
    @ManyToOne
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "business_unit_department_id",
            insertable = false, updatable = false)
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

}
