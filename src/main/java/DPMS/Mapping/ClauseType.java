package DPMS.Mapping;

import Framework.Config.StandardEntity;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR> <PERSON>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@LocalizedEntity
@Table(name = "clause_type")
public class ClauseType extends StandardEntity<ClauseType> implements Serializable {

    private static final long serialVersionUID = 1L;

    public final String PREFIX = "CLT-";
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    

    public ClauseType() {
    }

    public ClauseType(Long id) {
        this.id = id;
    }


    @Id
    @Basic(optional = false)
    @Column(name = "clause_type_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof ClauseType)) {
            return false;
        }
        ClauseType other = (ClauseType) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.ClauseType[ id=" + id + " ]";
    }
}
