package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.apache.struts2.json.annotations.JSON;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.core.DynamicSearchColumn;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@LocalizedEntity
@Table(name = "department")
public class Department extends StandardEntity<Department> implements DomainObjectInterface, IAuditableEntity {

    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer ACTIVE_STATUS = 1;
    private static final long serialVersionUID = 1L;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    private String smallCode;
    private String largeCode;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private User user;

    private SurveyLite formTemplate;

    public Department() {
        this.setDeleted(IS_NOT_DELETED);
        this.setStatus(ACTIVE_STATUS);
    }

    public Department(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "department_id", precision = 19)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @DynamicSearchColumn
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255, insertable = false, updatable = false)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Column(name = "small_code", nullable = false, length = 20)
    public String getSmallCode() {
        return smallCode;
    }

    public void setSmallCode(String smallCode) {
        this.smallCode = smallCode;
    }

    @Column(name = "large_code", length = 255)
    public String getLargeCode() {
        return largeCode;
    }

    public void setLargeCode(String largeCode) {
        this.largeCode = largeCode;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @JSON(serialize = false)
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_id", referencedColumnName = "id")
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof DPMS.Mapping.Department)) {
            return false;
        }
        DPMS.Mapping.Department other = (DPMS.Mapping.Department) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Department[ id=" + id + " ]";
    }

    @ManyToOne
    @JoinColumn(name = "form_template_id", referencedColumnName = "survey_id")
    public SurveyLite getFormTemplate() {
        return formTemplate;
    }

    public void setFormTemplate(SurveyLite formTemplate) {
        this.formTemplate = formTemplate;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
}
