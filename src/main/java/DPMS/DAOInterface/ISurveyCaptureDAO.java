package DPMS.DAOInterface;

import DPMS.Mapping.Area;
import DPMS.Mapping.Audit;
import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Document;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import DPMS.Mapping.Request;
import DPMS.Mapping.SurveyView;
import Framework.Config.TextLongValue;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import isoblock.surveys.dao.hibernate.IOutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveysLogging;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.type.OutstandingSurveysLoggingType;
import java.io.IOException;
import java.util.List;
import mx.bnext.access.Module;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.form.core.FillHelper;
import qms.form.dto.FillInfoDTO;
import qms.form.dto.FormRequestorDTO;
import qms.form.dto.OutstandingSurveyDTO;
import qms.form.dto.OutstandingSurveyLoadAnswersDTO;
import qms.form.dto.OutstandingSurveysAttendantInfo;
import qms.form.dto.OutstandingSurveysSignDTO;
import qms.form.entity.FormProgressState;
import qms.form.entity.FormRequest;
import qms.form.entity.SequenceDetailRequest;
import qms.timework.dto.TimeworkDoCheckData;
import qms.util.QMSException;
import qms.workflow.util.WorkflowAuthRole;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Implementation(name = "HibernateDAO_SurveyCapture")
public interface ISurveyCaptureDAO extends IGenericDAO<OutstandingSurveys, Long> {

    String MY_REQUEST_SURVEY_ROWS_QUERY = " "
            + " SELECT new map("
                + " c.id as id "
                + ",c.estatus as estatus "
                + ",c.requestId as surveyRequestId "
                + ",c.plainText as vchTexto "
                + ",c.intQuestions as intQuestions "
                + ",c.dteCreacion as dteCreacion "
                + ",d.dteFechaInicio as outstandingFillStartDate "
                + ",d.id as outstandingSurveyId "
                + ",d.requestId as outstandingSurveyRequestId "
                + ",d.stage as outstandingSurveyStage "
                + ",fp.id as progressStateId "
                + ",fp.description as progressStateDescription "
                + ",req.creationDate as outstandingRequestCreationDate "
                + ",req.status as outstandingRequestStatus "
                + ",req.version as outstandingRequesVersion "
                + ",req.documentCode as outstandingDocumentCode "
                + ",d.documentId as documentId "
                + ",doc.countPrintFormats as countPrintFormats "
                + ",doc.masterId as masterId "
                + ",d.estatus as outstandingSurveyStatus "
                + ",e.recurrence as rejectionRecurrence "
                + ",max(f.currentRecurrence) as currentRecurrence "
                + ",req.author.description as author "
                + ",i.description as impersonatedBy"
                + ",bu.id AS businessUnitId "
                + ",bu.description AS businessUnitName "
                + ",bud.id AS businessUnitDepartmentId "
                + ",bud.description AS businessUnitDepartmentName "
                + ",a.id AS areaId "
                + ",a.description AS areaName "
                + ",bud.cancelationCurrentUserId as cancelationCurrentUserId "
                + ",d.archived as archived "
                + ",cancelRequest.description as cancelRequestDescription"
            + " )"
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " d "
            + " JOIN " + SurveyView.class.getCanonicalName() + " c ON c.id = d.surveyId "
            + " LEFT JOIN " + AutorizationPoolDetailComment.class.getCanonicalName() + " e ON d.id = e.rejectedOutstandingSurveysId"
            + " LEFT JOIN " + Request.class.getCanonicalName() + " req ON req.id = d.requestId "
            + " LEFT JOIN req.autorizationPoolDetails f"
            + " LEFT JOIN req.impersonatedBy i"
            + " LEFT JOIN " + Document.class.getCanonicalName() + " doc ON doc.id = d.documentId "
            + " LEFT JOIN " + FormProgressState.class.getCanonicalName() + " fp ON fp.id = d.progressStateId "
            + " LEFT JOIN " + BusinessUnit.class.getCanonicalName() + " bu ON bu.id = d.businessUnitId "
            + " LEFT JOIN " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " bud ON bud.id = d.businessUnitDepartmentId "
            + " LEFT JOIN " + Area.class.getCanonicalName() + " a ON a.id = d.areaId "
            + " LEFT JOIN " + FormRequest.class.getCanonicalName() + " cancelRequest ON cancelRequest.id = d.formCancelRequestId "
            + " WHERE"
            + " req.deleted = 0 "
            + " AND e.rejectedOutstandingSurveysId IS NULL "
            + " AND d.estatus NOT IN ( " + OutstandingSurveys.ESTATUS_NUEVO + ") "
            + " GROUP BY"
                + " c.id "
                + ",c.estatus "
                + ",c.requestId "
                + ",c.plainText "
                + ",c.intQuestions "
                + ",d.stage "
                + ",fp.id "
                + ",fp.description "
                + ",c.dteCreacion "
                + ",d.dteFechaInicio "
                + ",d.id "
                + ",d.requestId "
                + ",c.isTemplateUseAvailable "
                + ",req.creationDate "
                + ",req.status "
                + ",req.version "
                + ",req.documentCode "
                + ",d.documentId "
                + ",doc.countPrintFormats "
                + ",doc.masterId "
                + ",d.estatus "
                + ",e.recurrence "
                + ",d.creatorUserId "
                + ",req.author.description "
                + ",i.description"
                + ",bu.id "
                + ",bu.description "
                + ",bud.id "
                + ",bud.description "
                + ",a.id "
                + ",a.description "
                + ",cancelRequest.description"
                + ",bud.cancelationCurrentUserId"
                + ",d.archived";

    String MY_SIGNATURES_FILTER = " "
            + " d.creatorUserId != :userId"
            + " AND :userId"
            + " IN ("
                + " SELECT CASE WHEN osa.fillerUserId IS NOT NULL THEN osa.fillerUserId ELSE seq.userId END AS participant "
                + " FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " osa "
                + " LEFT JOIN " + SequenceDetailRequest.class.getCanonicalName() + " seq "
                + " ON osa.fillerUserId IS NULL"
                + " AND osa.fillAutorizationPoolIndex = seq.sequenceIndex"
                + " AND osa.requestId = seq.requestId "
                + " WHERE osa.requestId = d.requestId "
                + " AND osa.fieldType = 'signature' "
            + " )";

    String MY_SECTIONS_FILTER = " "
            + " d.creatorUserId != :userId"
            + " AND :userId"
            + " IN ("
                + " SELECT CASE WHEN osa.fillerUserId IS NOT NULL THEN osa.fillerUserId ELSE seq.userId END AS participant "
                + " FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " osa "
                + " LEFT JOIN " + SequenceDetailRequest.class.getCanonicalName() + " seq "
                + " ON osa.fillerUserId IS NULL"
                + " AND osa.fillAutorizationPoolIndex = seq.sequenceIndex"
                + " AND osa.requestId = seq.requestId "
                + " WHERE osa.requestId = d.requestId "
                + " AND osa.fieldType = 'seccion' "
            + " )";

    String MY_FILL_AS_CREATOR_FILTER = " d.creatorUserId = :userId";

    String MY_FILLS_COUNT_ACCESS = " "
            + " SELECT count(d.id)"
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " d"
            + " WHERE d.requestId = :id"
            + " AND ("
                + " ("
                + MY_SECTIONS_FILTER
                + " ) OR ("
                + MY_SIGNATURES_FILTER
                + " ) OR ("
                + MY_FILL_AS_CREATOR_FILTER
                + " )"
            + " )";
            ;

    OutstandingSurveys calculateAverageIndividual(OutstandingSurveys ent);

    Audit calculateAveragePlan(OutstandingSurveys ent);
    
    GenericSaveHandle save(OutstandingSurveys ent, Module module, ILoggedUser loggedUser) throws IOException, QMSException;

    GenericSaveHandle saveRequestCopy(OutstandingSurveys ent, ILoggedUser loggedUser) throws IOException, QMSException;
    
    WorkflowAuthRole getSurveyAuthRole(Long outstandingSurveyId, Long autorizationIndex, ILoggedUser loggedUser);

    WorkflowAuthRole getSurveyAuthRole(Long outstandingSurveyId, ILoggedUser loggedUser);
    
    Long getOutstandingAutorizationPoolIndex(Long outstandingSurveyId);
    /**
     * Lógica propia:
     * 1. Solo actualiza el estado de OutstandingSurveys a CLOSED
     * 
     * Monitores:
     * 1. Dispara el monitor de "FormMailMonitor" y envia los mails de "completedFillFormFromField" o "completedFillForm"
     * 2. Dispara el monitor de "FormPendingMonitor" y cierra su actividad (si existe)
     * 
     * @param sol
     * @param outstandingSurveyId
     * @param loggedUser
     * @return 
     */
    Integer finishFillForm(Request sol, Long outstandingSurveyId, ILoggedUser loggedUser);

    Integer cancellFillForm(Long requestId, String reason, Long outstandingSurveyId, Boolean sendMail, ILoggedUser loggedUser);

    Integer cancelledForm(FormRequestorDTO cancelledFiller, String cancelByUser, String cancelByReason, Long outstandingSurveyId, ILoggedUser loggedUser);
    
    GenericSaveHandle makePollAttended(OutstandingSurveys outstandingSurveys, ILoggedUser loggedUser) throws IOException, QMSException;

    GenericSaveHandle saveRequest(
            IOutstandingSurveys ent,
            Long autorizationIndex,
            WorkflowAuthRole authRole,
            Boolean refreshExpiration, 
            FillHelper validator,
            ILoggedUser loggedUser
    ) throws IOException, QMSException;
    
    OutstandingSurveysAttendantInfo getPoolAttendantsInfo(Long surveyId, Long outstandingSurveyId, Long requestId);

    GenericSaveHandle signTheForm(OutstandingSurveysSignDTO data, ILoggedUser loggedUser);
    
    Boolean cancellFillForms(List<FillInfoDTO> fills, String comment, ILoggedUser loggedUser);

    List<TextLongValue> getRequestPartialProgressStatuses(Long requestId, Long currentAutorizationPoolIndex);
    
    OutstandingSurveysLogging createOutstandingSurveysLog(
            Long outstandingSurveysId,
            OutstandingSurveysLoggingType type,
            Boolean skipLog, 
            ISecurityUser user
    );

    Boolean isArchived(Long outstandingSurveyId);

    Boolean isDeleted(Long outstandingSurveyId);

    Survey surveyLazyData(Long surveyId);

    List<OutstandingSurveysAttendantLoad> outstandingSurveysAttendantLoadLazy(Long outstandingSurveyId);

    List<OutstandingSurveysAttendant> getFilteredAttendants(List<OutstandingSurveysAttendant> attendants);

    OutstandingSurveyLoadAnswersDTO loadDataFromSurveyAnswers(
            Long outstandingSurveyId,
            String masterId,
            ILoggedUser loggedUser
    );

    OutstandingSurveyDTO getOutstandingSurveyDTO(Long outstandingSurveyId);

    boolean canDeleteTimework(TimeworkDoCheckData data, ILoggedUser loggedUser);
}
