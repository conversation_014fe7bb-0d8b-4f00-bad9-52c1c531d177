package DPMS.DAOInterface;

import DPMS.Mapping.Files;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;
import jakarta.mail.Multipart;
import mx.bnext.core.file.IFileRepository;
import mx.bnext.core.security.ISecurityUser;
import qms.framework.dto.UploadFileResult;
import qms.framework.entity.FileLogging;
import qms.framework.interfaces.IPlainFile;
import qms.framework.util.FileLoggingType;

@Implementation(name = "HibernateDAO_Files")
public interface IFilesDAO extends IGenericDAO<Files, Long>, IFileRepository  {
    
    /**
     * Gets number of pages of file
     * @param fileId Id of the file
     * @return Number of pages of file
     */
    Integer findNumberPages(Long fileId);
    
    /**
     * Finds if file is busy
     * @param fileId Id of the file
     * @return True if the file is busy, false otherwise
     */
    Boolean busyFile(final Long fileId);

    /**
     *  Set number of pages of a PDF file
     * @param fileId Id of the file
     * @param numberPages Number of total pages
     * @param numberSavedPages Number saved pages
     * @return 1 if the file was updated
     */
    Integer updateNumberPages(final Long fileId, final Integer numberPages, final Integer numberSavedPages);
    Integer updateHasPassword(final Long fileId, final Boolean hasPassword);

    Long getPdfPageId(final Long fileId, final Integer pageNumber);
    
    Integer attachFileContent(Multipart mp, Set<Long> attachmentFileIds);
 
    UploadFileResult parseUploadException(String fileName, Path file, Exception e);

    Integer markFileBusy(Long fileId);
    
    Integer releaseFileBusy(Long fileId);
    
    Integer lockFile(Long fileId);
    
    boolean isImage(Long fileId);
    
    Long getFirstPdfPage(final Long fileId);
    
    String getFileCode(final Long fileId);
    
    List<Long> getPdfPageIds(final Long fileId);

    Integer updateHasPdfPages(final Long fileId, final Integer hasPdfPages);
    
    Integer updateHasPdf(Long fileId);

    Integer updateFileThumbnailFromPdfPages(final Long fileId);

    Long getFileIdByCode(String fileCode);
    
    String getFileContentSha512ByCode(final String fileCode);

    FileLogging insertFileLogging(Long fileId, FileLoggingType type, Boolean skipLog, ISecurityUser user);

    IPlainFile loadPlainFile(Long fileId);
}
