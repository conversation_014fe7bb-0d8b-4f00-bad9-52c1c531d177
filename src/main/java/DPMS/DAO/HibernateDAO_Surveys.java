package DPMS.DAO;

import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.Request;
import DPMS.Mapping.SurveyLite;
import Framework.Config.GridColumn;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.ExplicitRollback;
import bnext.reference.document.DocumentRef;
import com.google.common.collect.ImmutableMap;
import isoblock.common.beanGeneric;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyConditional;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldAreaPlaza;
import isoblock.surveys.dao.hibernate.SurveyFieldAreaPlazaPK;
import isoblock.surveys.dao.hibernate.SurveyFieldEditable;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.hibernate.SurveyGlobal;
import isoblock.surveys.dao.hibernate.SurveyGlobalObject;
import isoblock.surveys.dao.hibernate.SurveyHeader;
import isoblock.surveys.dao.hibernate.SurveyItem;
import isoblock.surveys.dao.hibernate.SurveyMatrixOptions;
import isoblock.surveys.dao.hibernate.SurveyProgressStateCancel;
import isoblock.surveys.dao.hibernate.SurveyProgressStatePartial;
import isoblock.surveys.dao.hibernate.SurveyProgressStateReject;
import isoblock.surveys.dao.hibernate.SurveyTextItem;
import isoblock.surveys.struts2.components.HeaderComparator;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.SortedSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.custom.dto.WebhookAnswerMetadataBK;
import qms.document.dto.RequestSaveHandle;
import qms.document.entity.RequestRef;
import qms.form.core.AutoTextHelper;
import qms.form.core.LazyAutoTextDocumentFolio;
import qms.form.dto.ConfigAutoTextDTO;
import qms.form.dto.FieldAutoTextDTO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.FieldItemKeyDTO;
import qms.form.dto.FormConfigFieldType;
import qms.form.dto.FormMailConfigurationDTO;
import qms.form.dto.FormPendingConfigDTO;
import qms.form.dto.ResultAutoTextDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.FormPendingConfig;
import qms.form.entity.FormProgressState;
import qms.form.entity.SurveyData;
import qms.form.entity.SurveyFieldConfigMail;
import qms.form.entity.SurveyMailConfiguration;
import qms.form.entity.SurveySubjectConfiguration;
import qms.form.entity.Webhook;
import qms.form.util.AutoText;
import qms.form.util.AutoTextUtil;
import qms.form.util.SurveyConditionalMode;
import qms.form.util.SurveySetup;
import qms.form.util.SurveyUtil;
import qms.framework.util.CacheRegion;
import qms.framework.util.SurveyFillEntity;
import qms.survey.dto.SearchPlainDTO;
import qms.survey.interfaces.IPlainSurvey;
import qms.survey.logic.SurveytoHtmlRenderer;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_Surveys")
@Scope(value = "singleton")
public class HibernateDAO_Surveys extends GenericDAOImpl<Survey, Long> implements ISurveysDAO {

    private Boolean doCopy = false;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Survey loadSurvey(Long id) {
        Survey survey = super.HQLT_findById(Survey.class, id, true, CacheRegion.SURVEY, 0);
        if (survey == null) {
            return null;
        }
        getEntityManager().detach(survey);
        SurveytoHtmlRenderer.remapRenderSurvey(survey);
        return survey;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainSurvey loadPlainSurvey(Long surveyId) {
        if (surveyId == null || surveyId <= 0) {
            return null;
        }
        return HQLT_findSimple(IPlainSurvey.class, " "
                + " SELECT new " + SearchPlainDTO.class.getCanonicalName() + "("
                    + " c.id"
                    + ", c.code"
                    + ", c.description"
                    + ", c.status"
                    + ", c.deleted"
                    + ", c.type"
                    + ", c.authorId"
                    + ", c.requestId"
                    + ", c.isFillRequestAvailable"
                    + ", c.isTemplateUseAvailable"
                    + ", c.answersTable"
                + ")"
                + " FROM " + SurveyLite.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", surveyId);
    }

    /**
     * Implementacion del metodo para guardar/actualizar cuestionarios
     *
     * @param ent cuestionario que será guardado
     * @param copy bandera que establece si se hará una copia del cuestionario
     * @param loggedUser Logged user
     * @return Survey con los datos del cuestionario
     * <AUTHOR> Cavazos Galindo
     * @throws qms.util.QMSException
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(Survey ent, Boolean copy, LoggedUser loggedUser) throws IOException, QMSException {
        GenericSaveHandle result = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("saveSurvey: {}", Utilities.getSerializedObj(ent));
        }
        this.doCopy = copy;
        SurveyFieldObject sFieldObj;
        SurveyGlobalObject sGlobalObj;
        SurveyGlobal sGlobal;
        SurveyHeader sHeader;
        SurveyMatrixOptions sMatrixOpts;
        SurveyItem sItem;
        SurveyField sField;
        SurveyFieldAreaPlaza sFieldAreaPl;
        Long objId;
        Long lastSavedSectionId = null;
        Map<Long, SurveyFieldObject> savedSectionObj = new LinkedHashMap<>();

        Set<SurveyFieldAreaPlaza> fieldAreaPlazaSet;
        Set<SurveyFieldAreaPlaza> fieldAreaPlazaSetToSave;
        List<SurveyField> lstFields = new ArrayList<>();
        List<SurveyHeader> lstHeaders;
        List<SurveyMatrixOptions> lstMatrixOpts;
        List<SurveyItem> lstItems;

        Iterator<SurveyFieldAreaPlaza> iFieldAreaPl;
        Iterator<SurveyField> iField = ent.getFields().iterator();
        Iterator<SurveyHeader> iHeader;
        Iterator<SurveyMatrixOptions> iMatrixOp;
        Iterator<SurveyItem> iItem;

        Integer pageNumber = 1;
        Map<String, Long> fieldObjectIds = new LinkedHashMap<>();
        Map<String, List<SurveyFieldEditable>> editablesFields = new LinkedHashMap<>();
        Map<String, Set<SurveyProgressStatePartial>> partialProgressStatuses = new LinkedHashMap<>();
        Map<String, Set<SurveyProgressStateCancel>> cancelProgressStatuses = new LinkedHashMap<>();
        Map<String, Set<SurveyProgressStateReject>> rejectProgressStatuses = new LinkedHashMap<>();
        Map<String, SortedSet<SurveyConditional>> conditionals = new LinkedHashMap<>();
        final Map<FieldItemKeyDTO, Long> itemsIds = new LinkedHashMap<>();
        while (iField.hasNext()) {
            sField = iField.next();
            sFieldObj = sField.getObj();
            if (sFieldObj.getId() == null) {
                sFieldObj.setId(-1L);
            }
            if (!SurveyField.TYPE_SECCION.equals(sField.getType()) && !SurveyField.TYPE_SIGNATURE.equals(sField.getType())) {
                sFieldObj.setSectionFieldId(lastSavedSectionId);
                if (lastSavedSectionId != null
                        //tipos que no tienen configuracion de responsable o se debe respetar el que venga desde su formulacion
                        && !sField.getType().equals(SurveyField.TYPE_PAGEBREAK)
                        && !sField.getType().equals(SurveyField.TYPE_INSTRUCCIONES)) {
                    sFieldObj.setFillEntity(savedSectionObj.get(lastSavedSectionId).getFillEntity());

                    sFieldObj.setBusinessUnitPosition(savedSectionObj.get(lastSavedSectionId).getBusinessUnitPosition());
                    sFieldObj.setOrganizationalUnitPosition(savedSectionObj.get(lastSavedSectionId).getOrganizationalUnitPosition());
                    sFieldObj.setUser(savedSectionObj.get(lastSavedSectionId).getUser());

                    sFieldObj.setBusinessUnitPositionId(savedSectionObj.get(lastSavedSectionId).getBusinessUnitPositionId());
                    sFieldObj.setOrganizationalUnitPositionId(savedSectionObj.get(lastSavedSectionId).getOrganizationalUnitPositionId());
                    sFieldObj.setUserId(savedSectionObj.get(lastSavedSectionId).getUserId());
                }
                if (sField.getType().equals(SurveyField.TYPE_PAGEBREAK)) {
                    pageNumber++;
                }
            }
            
            //Guardo etiquetas
            sFieldObj.setTitle(saveLabel(sFieldObj.getTitle(), loggedUser));
            sFieldObj.setSubTitle(saveLabel(sFieldObj.getSubTitle(), loggedUser));
            sFieldObj.setOtherOptionText(saveLabel(sFieldObj.getOtherOptionText(), loggedUser));
            /**
             * Para el caso de hiddenMatrixItems no se trata de un arreglo debido a que tenga varios idiomas , es porque
             * guarda varios ID's de cosas
             */
            sFieldObj.setHiddenMatrixItems(saveLabel(sFieldObj.getHiddenMatrixItems(), loggedUser));
            //Numero de pagina
            sFieldObj.setPageNumber(pageNumber);
            //Guardo header de columnas de preguntas matriz
            iHeader = sFieldObj.getHeaderTitles().iterator();
            lstHeaders = new ArrayList<>();
            while (iHeader.hasNext()) {
                sHeader = iHeader.next();
                sHeader.setTitle(saveLabel(sHeader.getTitle(), loggedUser));
                lstHeaders.add(surveySaveOrUpdate(sHeader, loggedUser));
            }
            if (!lstHeaders.isEmpty()) {
                Collections.sort(lstHeaders, new HeaderComparator());
            }
            sFieldObj.setHeaderTitles(lstHeaders);

            //Guardo opciones de celdas de preguntas matriz
            iMatrixOp = sFieldObj.getMatrixOptions().iterator();
            lstMatrixOpts = new ArrayList<>();
            while (iMatrixOp.hasNext()) {
                sMatrixOpts = iMatrixOp.next();
                sMatrixOpts.setTitle(saveLabel(sMatrixOpts.getTitle(), loggedUser));
                lstMatrixOpts.add(surveySaveOrUpdate(sMatrixOpts, loggedUser));
            }
            sFieldObj.setMatrixOptions(lstMatrixOpts);

            iItem = sFieldObj.getItems().iterator();
            lstItems = new ArrayList<>();
            while (iItem.hasNext()) {
                sItem = iItem.next();
                sItem.setItemText(saveLabel(sItem.getItemText(), loggedUser));
                sItem.setReportActivity(sItem.getReportActivity());
                lstItems.add(surveySaveOrUpdate(sItem, loggedUser));
            }
            sFieldObj.setItems(lstItems);
            fieldAreaPlazaSetToSave = new HashSet<>();
            fieldAreaPlazaSet = sFieldObj.getMatrixAreasPlaza();
            sFieldObj.setMatrixAreasPlaza(null);
            if (isCopy()) {
                sFieldObj.setId(-1L);
            }
            objId = sFieldObj.getId();
            processEditableFields(sFieldObj, editablesFields);
            processPartialProgressStatuses(sFieldObj, partialProgressStatuses);
            processCancelProgressStatuses(sFieldObj, cancelProgressStatuses);
            processRejectProgressStatuses(sFieldObj, rejectProgressStatuses);
            processConditionals(sFieldObj, conditionals);
            sFieldObj = surveySaveOrUpdate(sFieldObj, loggedUser);
            fieldObjectIds.put(sFieldObj.getField_id(), sFieldObj.getId());
            if (sFieldObj.getItems() != null && !sFieldObj.getItems().isEmpty()) {
                for (final SurveyItem item : sFieldObj.getItems()) {
                    final FieldItemKeyDTO itemKey = new FieldItemKeyDTO(sFieldObj.getField_id(), item.getCode());
                    itemsIds.put(itemKey, item.getId());
                }
            }
            iFieldAreaPl = fieldAreaPlazaSet.iterator();
            if (!fieldAreaPlazaSet.isEmpty()) {
                String query = "DELETE FROM SURVEY_FIELD_AREA_PLAZA WHERE FIELD_OBJECT_ID = :fieldObjId";
                int registros = SQL_updateByQuery(
                        query, 
                        ImmutableMap.of("fieldObjId", sFieldObj.getId()),
                        0,
                        Collections.singletonList("SURVEY_FIELD_AREA_PLAZA")
                );
                getLogger().debug("La cantidad de registro eliminados:{}", registros);
            }
            while (iFieldAreaPl.hasNext()) {
                sFieldAreaPl = iFieldAreaPl.next();
                sFieldAreaPl.setSurveyFieldObject(sFieldObj);
                if (objId == -1) {
                    sFieldAreaPl.setSurveyFieldAreaPlazaPK(new SurveyFieldAreaPlazaPK(sFieldAreaPl.getAreaPlaza().getId(), sFieldObj.getId()));
                }
                fieldAreaPlazaSetToSave.add(surveySaveOrUpdate(sFieldAreaPl, loggedUser));
            }
            sFieldObj.setMatrixAreasPlaza(fieldAreaPlazaSetToSave);
            sField.setObj(sFieldObj);
            if (sFieldObj.getLocation() != null && !sFieldObj.getLocation().isEmpty()) {
                sFieldObj.setParentFieldId(getDbIdOfId(sFieldObj.getParentId(), lstFields));
            }
            if (sField.getId() == null) {
                sField.setId(-1L);
            }
            sField = surveySaveOrUpdate(sField, loggedUser);
            lstFields.add(sField);

            if (SurveyField.TYPE_SECCION.equals(sField.getType()) || SurveyField.TYPE_SIGNATURE.equals(sField.getType())) {
                lastSavedSectionId = sField.getId();
                savedSectionObj.put(lastSavedSectionId, sField.getObj());
            }
        }
        ent.setFields(lstFields);
        sGlobal = ent.getGlobals();
        sGlobalObj = sGlobal.getObj();
        sGlobalObj.setLanguages(saveLabel(sGlobalObj.getLanguages(), loggedUser));
        sGlobalObj.setPages(saveLabel(sGlobalObj.getPages(), loggedUser));
        sGlobalObj.setTitle(saveLabel(sGlobalObj.getTitle(), loggedUser));
        sGlobalObj = surveySaveOrUpdate(sGlobalObj, loggedUser);
        sGlobal.setObj(sGlobalObj);
        sGlobal = surveySaveOrUpdate(sGlobal, loggedUser);
        ent.setGlobals(sGlobal);
        if (isCopy()) {
            ent.setEstatus(Survey.ESTATUS_ACTIVO);
        }
        if (ent.configGet().containsKey("isSurveyTemplate")) {
            //para templates no lleva nunca request
        } else {
            switch (Survey.SURVEY_TYPE.getSurveyType(ent.getType())) {
                case REQUEST:
                    result = fillRequestInfo(ent, loggedUser, result);
                    Object res = result.getJsonEntityData().get("RepeatRequest");
                    if(res != null && (boolean)res) {
                        return result;
                    }
                break;
                case AUDIT: 
                case POLL: 
                break;
            }
        }
        ent = surveySaveOrUpdate(ent, loggedUser);
        try {
            //#DocsElec-2.4.13.0-20150410-LLIMAS-1#
            for (SurveyField f : lstFields) {
                SurveyFieldObject fo = f.getObj();
                fo.setSurveyId(ent.getId());
                makePersistent(fo, loggedUser.getId());
            }
        } catch (Exception e) {
            getLogger().error("Falla al ", e);
        }
        saveEditableSections(ent, editablesFields, fieldObjectIds, loggedUser);
        savePartialProgressStatuses(ent, partialProgressStatuses, loggedUser);
        saveCancelProgressStatuses(ent, cancelProgressStatuses, loggedUser);
        saveRejectProgressStatuses(ent, rejectProgressStatuses, loggedUser);
        saveConditionals(ent, fieldObjectIds, itemsIds, conditionals, loggedUser);
        if (ent != null) {
            if (ent.getRequestFull() != null) {
                Long restrictRecordsObjId = null;
                if (ent.getRequestFull().getRestrictRecordsField() != null && !ent.getRequestFull().getRestrictRecordsField().isEmpty()) {
                    restrictRecordsObjId = fieldObjectIds.get(ent.getRequestFull().getRestrictRecordsField());
                }
                ent.getRequestFull().setRestrictRecordsObjId(restrictRecordsObjId);
                ent.getRequestFull().update("restrictRecordsObjId", restrictRecordsObjId, this);
            }
            result.setOperationEstatus(1);
            result.getJsonEntityData().put("surveyCode", ent.getCode());
            result.setSavedId(ent.getId().toString());
        }
        return result;
    }

    private void updateItemConditionalData(
            final Survey ent,
            final SurveyFieldObject fo,
            final SurveyConditional conditional,
            final Map<String, Long> fieldObjectIds,
            final Map<FieldItemKeyDTO, Long> itemsIds
    ) {
        if (conditional.getConditionalQuestionCode() == null
                || conditional.getConditionalQuestionCode().isEmpty()
                || SurveyConditionalMode.getType(conditional.getConditionalType()) == null) {
            getLogger().warn(
                    "Invalid conditional data, type: {}, question code: {}",
                    conditional.getConditionalType(),
                    conditional.getConditionalQuestionCode());
            return;
        }
        final Long questionObjId = fieldObjectIds.get(conditional.getConditionalQuestionCode());
        if (questionObjId == null) {
            getLogger().warn(
                    "Invalid conditional data, type: {}, question code: {}. Can not find question field obj id.",
                    conditional.getConditionalType(),
                    conditional.getConditionalQuestionCode());
            return;
        }
        conditional.setConditionalQuestionId(questionObjId);
        if (SurveyConditionalMode.SURVEY_ITEM.getValue().equals(conditional.getConditionalType())) {
            if (conditional.getConditionalAnswerCode() == null) {
                getLogger().warn(
                        "Invalid conditional data due, type: {}, question code: {}, answer: {}",
                        conditional.getConditionalType(),
                        conditional.getConditionalQuestionCode(),
                        conditional.getConditionalAnswerCode());
                return;
            }
            final FieldItemKeyDTO itemKey = new FieldItemKeyDTO(conditional.getConditionalQuestionCode(), conditional.getConditionalAnswerCode());
            final Long itemId = itemsIds.get(itemKey);
            if (itemId == null) {
                getLogger().warn(
                        "Invalid conditional data, type: {}, question code: {}. Can not find question answer obj id {}.",
                        new Object[]{
                            conditional.getConditionalType(),
                            conditional.getConditionalQuestionCode(),
                            conditional.getConditionalAnswerCode()
                        }
                );
                return;
            }
            conditional.setConditionalAnswerId(itemId);
        }
        conditional.setId(-1L);
        conditional.setSurveyId(ent.getId());
        conditional.setFieldObjectId(fo.getId());
    }


    private GenericSaveHandle fillRequestInfo(
            Survey ent, LoggedUser loggedUser, GenericSaveHandle gsh
    ) throws NumberFormatException, IOException, QMSException {
        Request sol = ent.getRequestFull();
        if (sol == null) {
            sol = (Request) HQL_findSimpleObject(""
                + " SELECT r"
                + " FROM " + Survey.class.getCanonicalName() + " c"
                + " LEFT JOIN " + Request.class.getCanonicalName() + " r "
                + " ON r.id = c.request.id"
                + " WHERE c.id = :id",
                "id", ent.getId());
        }
        if (sol == null) {
            return gsh;
        }
        if (ent.getId() != -1) {
            sol.setSurveyId(ent.getId());
        }
        IRequestDAO daoRequest = getBean(IRequestDAO.class);
        RequestSaveHandle tempGSH = daoRequest.save(sol, null, sol.getType(), loggedUser, true, true);
        if (tempGSH.getOperationEstatus() == 0) {
            gsh.setErrorMessage(tempGSH.getErrorMessage());
            tempGSH.setRequest(null);
            tempGSH.getJsonEntityData().put("RepeatRequest", true);
            return tempGSH;
        }
        sol = tempGSH.getRequest();
        sol.update("status", Request.STATUS_STANDBY);
        sol.setStatus(Request.STATUS_STANDBY);
        ent.setRequestFull(sol);
        ent.setRequest(new RequestRef(sol.getId()));
        gsh.getJsonEntityData().put("request", ent.getRequestFull());
        return gsh;
    }

    private void processEditableFields(SurveyFieldObject sFieldObj, Map<String, List<SurveyFieldEditable>> editablesFields) {
        if (sFieldObj.getSurveyFieldObjectEditables() != null) {
            editablesFields.put(sFieldObj.getField_id(), sFieldObj.getSurveyFieldObjectEditables());
            sFieldObj.setSurveyFieldObjectEditables(null);
        }
    }
    
    private void processPartialProgressStatuses(
            final SurveyFieldObject sFieldObj,
            final Map<String, Set<SurveyProgressStatePartial>> partialProgressStatuses
    ) {
        if (sFieldObj.getPartialProgressStatuses() != null) {
            partialProgressStatuses.put(sFieldObj.getField_id(), sFieldObj.getPartialProgressStatuses());
            sFieldObj.setPartialProgressStatuses(null);
        }
    }
    
    private void processCancelProgressStatuses(
            final SurveyFieldObject sFieldObj,
            final Map<String, Set<SurveyProgressStateCancel>> cancelProgressStatuses
    ) {
        if (sFieldObj.getCancelProgressStatuses() != null) {
            cancelProgressStatuses.put(sFieldObj.getField_id(), sFieldObj.getCancelProgressStatuses());
            sFieldObj.setCancelProgressStatuses(null);
        }
    }
    
    private void processRejectProgressStatuses(
            final SurveyFieldObject sFieldObj,
            final Map<String, Set<SurveyProgressStateReject>> rejectProgressStatuses
    ) {
        if (sFieldObj.getRejectProgressStatuses() != null) {
            rejectProgressStatuses.put(sFieldObj.getField_id(), sFieldObj.getRejectProgressStatuses());
            sFieldObj.setRejectProgressStatuses(null);
        }
    }
    
    private void processConditionals(
            final SurveyFieldObject sFieldObj,
            final Map<String, SortedSet<SurveyConditional>> conditionals
    ) {
        if (validConditionals(sFieldObj)) {
            if (sFieldObj.getConditionals() != null) {
                conditionals.put(sFieldObj.getField_id(), sFieldObj.getConditionals());
                sFieldObj.setConditionals(null);
            }
        }
    }

    private void saveEditableSections(
            final Survey ent, 
            final Map<String, List<SurveyFieldEditable>> editablesFields,
            final Map<String, Long> fieldObjectIds,
            final LoggedUser loggedUser
    ) {
        for (final SurveyField field : ent.getFields()) {
            if (!SurveyField.TYPE_SIGNATURE.equals(field.getType())
                    && !SurveyField.TYPE_SECCION.equals(field.getType())) {
                continue;
            }
            final Long objId = field.getObj().getId();
            final List<SurveyFieldEditable> editables = editablesFields.get(field.getObj().getField_id());
            if (!isCopy()) {
                deleteEditableSections(objId);
            }
            if (editables == null) {
                continue;
            }
            for (final SurveyFieldEditable fieldEditable : editables) {
                fieldEditable.getId().setFieldObjectId(objId);
                final Long editableSectionId = fieldObjectIds.get(fieldEditable.getFieldId());
                fieldEditable.getId().setEditableSectionId(editableSectionId);
                fieldEditable.setInsert(true);
                makePersistent(fieldEditable, loggedUser.getId());
            }
        }
    }
    
    private void savePartialProgressStatuses(
            final Survey ent, 
            final Map<String, Set<SurveyProgressStatePartial>> partialProgressStatuses,
            final LoggedUser loggedUser
    ) {
        for (final SurveyField field : ent.getFields()) {
            if (!SurveyField.TYPE_SIGNATURE.equals(field.getType())
                    && !SurveyField.TYPE_SECCION.equals(field.getType())) {
                continue;
            }
            final Long objId = field.getObj().getId();
            final Set<SurveyProgressStatePartial> statuses = partialProgressStatuses.get(field.getObj().getField_id());
            if (!isCopy()) {
                deletePartialProgressStatuses(objId);
            }
            if (statuses == null) {
                continue;
            }
            for (final SurveyProgressStatePartial status : statuses) {
                status.getId().setFieldObjectId(objId);
                status.setInsert(true);
                makePersistent(status, loggedUser.getId());
            }
        }
    }

    private void saveCancelProgressStatuses(
            final Survey ent, 
            final Map<String, Set<SurveyProgressStateCancel>> cancelProgressStatuses,
            final LoggedUser loggedUser
    ) {
        for (final SurveyField field : ent.getFields()) {
            if (!SurveyField.TYPE_SIGNATURE.equals(field.getType())
                    && !SurveyField.TYPE_SECCION.equals(field.getType())) {
                continue;
            }
            final Long objId = field.getObj().getId();
            final Set<SurveyProgressStateCancel> statuses = cancelProgressStatuses.get(field.getObj().getField_id());
            if (!isCopy()) {
                deleteCancelProgressStatuses(objId);
            }
            if (statuses == null) {
                continue;
            }
            for (final SurveyProgressStateCancel status : statuses) {
                status.getId().setFieldObjectId(objId);
                status.setInsert(true);
                makePersistent(status, loggedUser.getId());
            }
        }
    }

    private void saveRejectProgressStatuses(
            final Survey ent, 
            final Map<String, Set<SurveyProgressStateReject>> rejectProgressStatuses,
            final LoggedUser loggedUser
    ) {
        for (final SurveyField field : ent.getFields()) {
            if (!SurveyField.TYPE_SIGNATURE.equals(field.getType())
                    && !SurveyField.TYPE_SECCION.equals(field.getType())) {
                continue;
            }
            final Long objId = field.getObj().getId();
            final Set<SurveyProgressStateReject> statuses = rejectProgressStatuses.get(field.getObj().getField_id());
            if (!isCopy()) {
                deleteRejectProgressStatuses(objId);
            }
            if (statuses == null) {
                continue;
            }
            for (final SurveyProgressStateReject status : statuses) {
                status.getId().setFieldObjectId(objId);
                status.setInsert(true);
                makePersistent(status, loggedUser.getId());
            }
        }
    }
    
    private void saveConditionals(
            final Survey ent, 
            final Map<String, Long> fieldObjectIds,
            final Map<FieldItemKeyDTO, Long> itemsIds,
            final Map<String, SortedSet<SurveyConditional>> conditionalsIndex,
            final LoggedUser loggedUser
    ) {
        for (final SurveyField field : ent.getFields()) {
            final SurveyFieldObject fo = field.getObj();
            if (validConditionals(fo)) {
                if (!isCopy()) {
                    deleteConditionals(field.getObj().getId());
                }
                final SortedSet<SurveyConditional> conditionals = conditionalsIndex.get(field.getObj().getField_id());
                if (conditionals == null || conditionals.isEmpty()) {
                    continue;
                }
                conditionals.forEach(conditional -> {
                    updateItemConditionalData(ent, fo, conditional, fieldObjectIds, itemsIds);
                    makePersistent(conditional, loggedUser.getId());
                });
            } else {
                fo.setConditionals(null);
            }
        }
    }
    
    private boolean validConditionals(SurveyFieldObject sfo) {
        return Objects.equals(sfo.getConditionalQuestion(), "t") || Objects.equals(sfo.getSelectedWeightedField(), "t") || Objects.equals(sfo.getSummationQuestion(), "t") || sfo.getConditionalExpirationQuestion();
    }

    private Integer deleteEditableSections(final Long objId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", objId);
        final String hql = ""
                + " DELETE FROM " + SurveyFieldEditable.class.getCanonicalName() + " c"
                + " WHERE c.id.fieldObjectId = :id";
        return HQL_updateByQuery(hql, params);
    }
    
    private Integer deletePartialProgressStatuses(final Long objId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", objId);
        final String hql = ""
                + " DELETE FROM " + SurveyProgressStatePartial.class.getCanonicalName() + " c"
                + " WHERE c.id.fieldObjectId = :id";
        return HQL_updateByQuery(hql, params);
    }
    
    private Integer deleteCancelProgressStatuses(final Long objId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", objId);
        final String hql = ""
                + " DELETE FROM " + SurveyProgressStateCancel.class.getCanonicalName() + " c"
                + " WHERE c.id.fieldObjectId = :id";
        return HQL_updateByQuery(hql, params);
    }

    private Integer deleteRejectProgressStatuses(final Long objId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", objId);
        final String hql = ""
                + " DELETE FROM " + SurveyProgressStateReject.class.getCanonicalName() + " c"
                + " WHERE c.id.fieldObjectId = :id";
        return HQL_updateByQuery(hql, params);
    }
    
    private Integer deleteConditionals(final Long objId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", objId);
        final String hql = ""
                + " DELETE FROM " + SurveyConditional.class.getCanonicalName() + " c"
                + " WHERE c.id.fieldObjectId = :id";
        return HQL_updateByQuery(hql, params);
    }

    private Long getDbIdOfId(String id, List<SurveyField> fields) {
        for (SurveyField field : fields) {
            if (field.getObj().getField_id().equals(id)) {
                return field.getObj().getId();
            }
        }
        return null;
    }

    /**
     *
     * @param entity
     * @return
     */
    private <TYPE> TYPE surveySaveOrUpdate(final TYPE entity, final LoggedUser loggedUser) {
        if (isCopy()) {
            beanGeneric.callMethodSetLong(entity, "setId", -1L);
        }
        final TYPE saveEntity = this.makePersistent(entity, loggedUser.getId());
        return saveEntity;
    }

    /**
     *
     * @param s
     * @return
     */
    private List<SurveyTextItem> saveLabel(final List<SurveyTextItem> s, final LoggedUser loggedUser) {
        List<SurveyTextItem> lstTextItems = new ArrayList<>();
        SurveyTextItem sTextItem;

        Iterator<SurveyTextItem> iTextItem = s.iterator();
        while (iTextItem.hasNext()) {
            sTextItem = new SurveyTextItem(iTextItem.next());
            lstTextItems.add(surveySaveOrUpdate(sTextItem, loggedUser));
        }
        return lstTextItems;
    }

    private boolean isCopy() {
        return doCopy;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getAutoTextValue(
            final AutoTextHelper helper,
            final FieldAutoTextDTO field,
            final AutoText key, 
            final Long loggedUserId
    ) {
        final String keyName = key.getName();
        final String value;
        switch (key) {
            case REQUEST_USER:
                value = helper.getRequestUser();
                break;
            case ORIGINATOR_USER:
                value = helper.getOriginatorUser();
                break;
            case AUTHORIZER_USER:
                value = helper.getAuthorizerUser();
                break;
            case DOCUMENT_VERSION:
                value = helper.getDocumentVersion();
                break;
            case DOCUMENT_TITLE:
                value = helper.getDocumentTitle();
                break;
            case DOCUMENT_CODE:
                value = helper.getDocumentCode();
                break;
            case DOCUMENT_FOLIO:
                value = helper.getDocumentFolio();
                break;
            case FILLING_REQUEST_DATE:
                value = helper.getFillingRequestDate();
                break;
            case APPROVAL_FORM_DATE:
                value = helper.getApprovalFormDate();
                break;
            case REQUEST_TIMESTAMP:
                value = helper.getRequestTimestamp();
                break;
            case FILL_OUT_AUTHORIZER:
                if (loggedUserId == null) {
                    value = keyName;
                } else {
                    value = helper.getFillOutAuthorizer(field, keyName);
                }
                break;
            case FILL_OUT_TIMESTAMP:
                value = helper.getFillOutTimestamp(field, keyName);
                break;
            case FILL_START_TIMESTAMP:
                value = helper.getFillStartTimestamp();
                break;
            default:
                value = keyName;
        }
        if (value == null) {
            return keyName;
        }
        return value;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getRequestCreationDate(Long outstandingSurveyId) {
        if (outstandingSurveyId == null) {
            return null;
        }
        Date creationDate = this.HQL_findSimpleDate(""
                + " SELECT r.creationDate "
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " JOIN c.request r "
                + " WHERE c.id = " + outstandingSurveyId
        );
        return creationDate;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getAnswerRows(final SortedPagedFilter filter, final String formMasterId, final String formVersion, LoggedUser loggedUser) {
        final Long requestId = SurveyUtil.getRequestId(this, formMasterId, formVersion);
        
        final Boolean restrictRecordsByDepartment = HQL_findSimpleBoolean(""
                + " SELECT r.restrictRecordsByDepartment"
                + " FROM " + Request.class.getCanonicalName() + " r "
                + " WHERE r.id =:requestId", "requestId", requestId);
        StringBuilder condition = new StringBuilder(" (r.int_borrado = 0 OR r.int_borrado is null) ");
        
        // Se agregan valores por defecto cuando no hay request (archivados)
        filter.getColumns().put("r#int_borrado", new GridColumn("r#int_borrado", "o.deleted"));
        filter.getColumns().put("r#creation_date", new GridColumn("r#creation_date", "o.created_date"));
        filter.getColumns().put("r#int_estado", new GridColumn("r#int_estado", OutstandingSurveys.STATUS.ARCHIVED.getValue().toString()));
        
        // Se agrega valor por defecto a filtro de estado de solicitud para poder mostrar historicos
        if (filter.getCriteria().get("r#int_estado") != null) {
            filter.getCriteria().put("CAST(isnull(r#int_estado," + OutstandingSurveys.STATUS.ARCHIVED.getValue() + ") as int)", filter.getCriteria().remove("r#int_estado"));
        }
        
        // Se agregan filtros cuando no es admin
        if (Boolean.TRUE.equals(restrictRecordsByDepartment) && !loggedUser.isAdmin()) {
            final String departmentIdColumn = "o#business_unit_department_id";
            filter.getColumns().put(departmentIdColumn, new GridColumn(departmentIdColumn));
            final String userIdColumn = "r#id";
            filter.getColumns().put(userIdColumn, new GridColumn(userIdColumn));
            condition
                    .append(" AND (")
                        .append("o.business_unit_department_id = ").append(loggedUser.getBusinessUnitDepartmentId())
                        .append(" OR o.business_unit_department_id IN ("
                                + " SELECT pd.ubicacion_id "
                                + " FROM users u"
                                + " INNER JOIN usuario_puesto up on u.user_id = up.usuario_id"
                                + " INNER JOIN puesto_departamento  pd on pd.puesto_id = up.puesto_id"
                                + " WHERE u.user_id = ").append(loggedUser.getId()).append(""
                            + ") OR r.id IN ("
                                + " SELECT request_id "
                                + " FROM request r"
                                + " INNER JOIN autorization_pool_details ap ON r.id = ap.request_id "
                                + " WHERE ap.user_id = ").append(loggedUser.getId()).append(""
                        + ")"
                    + ")");
        }
        filter.getCriteria().put("<condition>", condition.toString());
        //Las columnas que aplican en todos los formularios
        final String table;
        if (formVersion == null || formVersion.isEmpty()) {
            table = SurveyUtil.generateTableSynonymName(formMasterId);
        } else {
            table = SurveyUtil.generateTablSynonymWithVersionName(formMasterId, formVersion);
        }
        if (table.isEmpty()) {
            getLogger().error(" "
                    + " Table answers does not exists for document "
                    + " with master id {}.", formMasterId
            );
            return Utilities.EMPTY_GRID_INFO;
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            final String defaultOrderColumn = "o#outstanding_surveys_id";
            filter.getColumns().put(defaultOrderColumn, new GridColumn(defaultOrderColumn));
            filter.getField().setOrderBy(defaultOrderColumn);
            filter.setDirection(Byte.parseByte("2"));
        }
        return SQL_getRowsByQuery(""
                + " FROM " + table + " c"
                + " JOIN outstanding_surveys o ON o.outstanding_surveys_id = c.outstanding_surveys_id1"
                + " LEFT JOIN users u ON u.user_id = o.creator_user_id"
                + " LEFT JOIN request r ON r.id = o.request_id"
                + " LEFT JOIN form_progress_state ps ON ps.form_progress_state_id = o.form_progress_state_id"
                , 
                filter
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<SurveyData> getMetadataRows(final SortedPagedFilter filter, final String formMasterId, final String formVersion) {
        final Long surveyId = SurveyUtil.getSurveyId(this, formMasterId, formVersion);
        return getMetadataRows(filter, surveyId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<SurveyData> getMetadataRows(final SortedPagedFilter filter, final Long surveyId) {
        if(filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.fieldOrder, c.fieldCode");
        }
        filter.getCriteria().put("<condition>", "c.surveyId = " + surveyId);
        return HQL_getRowsByQuery(""
                + " SELECT c"
                + " FROM " + SurveyData.class.getCanonicalName() + " c ", 
                filter,
                true,
                CacheRegion.SURVEY,
                0
        );
    }
    
    private Long getFirstSeccionObjId(final Long surveyId) {
        final List<Long> objIds = HQL_findByQueryLimit(""
                + " SELECT fo.id"
                + " FROM " + Survey.class.getCanonicalName() + " s"
                + " JOIN s.fields f"
                + " JOIN f.obj fo"
                + " WHERE s.id = :id"
                + " AND f.type = '" + SurveyField.TYPE_SECCION + "'"
                + " AND fo.fillEntity = '" + SurveyFillEntity.REQUESTOR.toString() + "'"
                + " ORDER BY fo.order ASC",  // <-- TODO: El atributo order no sirve!! revisar
                ImmutableMap.of(
                    "id", surveyId
                ),
                1,
                true,
                CacheRegion.SURVEY,
                0
        );
        final Long surveyFieldObjId;
        if (objIds != null && !objIds.isEmpty()) {
            surveyFieldObjId = objIds.get(0);
        } else {
            surveyFieldObjId = null;
        }
        return surveyFieldObjId;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextLongValue> getRequestorPartialProgressStatuses(final Long surveyId, final Long currentAutorizationPoolIndex) {
        final Long surveyFieldObjId = getFirstSeccionObjId(surveyId);
        if (surveyFieldObjId == null || Objects.equals(surveyFieldObjId, 0l)) {
            return new ArrayList<>(0);
        }
        final List<TextLongValue> statuses = getSurveyFieldPartialProgressStatuses(surveyFieldObjId, currentAutorizationPoolIndex);
        return statuses;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextLongValue> getSurveyFieldPartialProgressStatuses(final Long surveyFieldObjId, final Long currentAutorizationPoolIndex) {
        final List<TextLongValue> statuses = HQL_findByQuery(""
            + " SELECT "
                + " new " + TextLongValue.class.getCanonicalName() + " ("
                    + " c.description AS text,"
                    + " c.id AS value "
                + " )"
            + " FROM " + SurveyFieldObject.class.getCanonicalName() + " s"
            + " JOIN s.partialProgressStatuses ps"
            + " JOIN " + FormProgressState.class.getCanonicalName() + " c"
            + " ON c.id = ps.id.formProgressStateId"
            + " WHERE"
                + " c.status = " + FormProgressState.STATUS.ACTIVE.getValue()
                + " AND c.deleted = 0"
                + " AND s.id = :surveyFieldObjId"
            + " ORDER BY c.description ASC",
            ImmutableMap.of("surveyFieldObjId", surveyFieldObjId),
            true,
            CacheRegion.SURVEY,
            0
        );
        return statuses;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOutstandingSurveyRequestPrefix(Long documentId) {
        if (documentId != null) {
            DocumentRef d = HQLT_findById(DocumentRef.class, documentId);
            return LazyAutoTextDocumentFolio.getOutstandingSurveyRequestPrefix(d.getCode(), d.getVersion());
        }
        return LazyAutoTextDocumentFolio.getOutstandingSurveyRequestPrefix(null, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> getAutoTextValues(final ConfigAutoTextDTO config, final LoggedUser loggedUser) {
        final AutoTextHelper helper = new AutoTextHelper(
                config.getRequestId(), 
                config.getDocumentId(), 
                config.getOutstandingid(), 
                this,
                loggedUser
        );
        final List<ResultAutoTextDTO> results = new ArrayList<>(config.getFields().size());
        final Map<String, AutoText> autoTexts = AutoText.getValuesMap();
        for (final FieldAutoTextDTO field : config.getFields()) {
            for (final String autoText : field.getAutoTexts()) {
                final AutoText key = autoTexts.get(autoText);
                final ResultAutoTextDTO result = AutoTextUtil.getInstance(field, autoText);
                if (key == null || (!helper.getHasRequest() && !helper.getHasDocument())) {
                    result.setValue(autoText);
                } else {
                    result.setValue(getAutoTextValue(helper, field, key, loggedUser.getId()).trim());
                }
                results.add(result);
            }
        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("title", helper.getDocumentTitle());
        result.put("results", results);
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void migrateFormConfig(
            final Long newSurveyId,
            final String newDocumentVersion,
            final Long oldDocumentId,
            final ILoggedUser loggedUser
    ) {
        if (newSurveyId == null || oldDocumentId == null) {
            getLogger().warn(
                    "Missing newSurveyId {} or oldDocumentId {} at to migrate form config",
                    newSurveyId,
                    oldDocumentId
            );
            return;
        }
        final Long oldSurveyId = getSurveyId(oldDocumentId);

        generateSurveySubjectConfigFromOld(oldSurveyId, newSurveyId, loggedUser);
        generateSurveyFieldConfigFromOld(oldSurveyId, newSurveyId, loggedUser);
        generateFormPendingConfigFromOld(oldSurveyId, newSurveyId, loggedUser);
        generateWebhookConfigFromOld(oldSurveyId, newSurveyId, newDocumentVersion, loggedUser);

        migrateSurveyDataSurveyConfig(oldSurveyId, newSurveyId);
    }

    private Long getSurveyId(Long oldDocumentId) {
        return HQL_findSimpleLong(" " +
                        "SELECT d.surveyId " +
                        "FROM " + Document.class.getCanonicalName() + " d " +
                        "WHERE d.id = :documentId",
                ImmutableMap.of("documentId", oldDocumentId)
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveMailConfigurationForm(List<FormMailConfigurationDTO> dto, Long surveyId, ILoggedUser loggedUser) {
        final AtomicInteger result = new AtomicInteger(0);
        dto.forEach((FormMailConfigurationDTO d) -> {
            Integer update = 0;
            switch (d.getFieldType()) {
                case SECCION:
                    // ToDo: Actualizar también SurveyDataPreview, actualmente la configuración se pierde al refrescar `SurveyData`
                    update = HQL_updateByQuery(""
                                    + " UPDATE " + SurveyData.class.getCanonicalName() + " c "
                                    + " SET"
                                    + " c.includeInMail = :includeInMail"
                                    + " WHERE c.id = :id OR c.sectionId = :sectionId",
                            ImmutableMap.of(
                                    "id", d.getId(),
                                    "sectionId", d.getSeccionId(),
                                    "includeInMail", d.getIncludeInMail()
                            ),
                            true,
                            CacheRegion.SURVEY,
                            0
                    );
                    break;
                case FIELD:
                    // ToDo: Actualizar también SurveyDataPreview, actualmente la configuración se pierde al refrescar `SurveyData`
                    if (!d.getIsAdditionalData()) {
                        update = HQL_updateByQuery(""
                                        + " UPDATE " + SurveyData.class.getCanonicalName() + " c "
                                        + " SET"
                                        + " c.includeInSubject = :includeInMail"
                                        + " WHERE c.id = :id ",
                                ImmutableMap.of(
                                        "id", d.getId(),
                                        "includeInMail", d.getIncludeInMail()
                                ),
                                true,
                                CacheRegion.SURVEY,
                                0
                        );
                        if (d.getAssociatedFields() != null && !d.getAssociatedFields().isEmpty()) { // Update assocciated options ids
                            update = HQL_updateByQuery(""
                                            + " UPDATE " + SurveyData.class.getCanonicalName() + " c "
                                            + " SET"
                                            + " c.includeInSubject = :includeInMail"
                                            + " WHERE c.optionId IN (:ids) ",
                                    ImmutableMap.of(
                                            "ids", d.getAssociatedFields(),
                                            "includeInMail", d.getIncludeInMail()
                                    ),
                                    true,
                                    CacheRegion.SURVEY,
                                    0
                            );
                        }
                    } else { // Update additional data
                        Object old = HQL_findSimpleObject(""
                                        + " SELECT new " + SurveyFieldConfigMail.class.getCanonicalName() + "("
                                        + " c.id,"
                                        + " c.surveyId,"
                                        + " c.fieldColumn,"
                                        + " c.includeInSubject"
                                        + ") "
                                        + " FROM " + SurveyFieldConfigMail.class.getCanonicalName() + " c "
                                        + " WHERE c.surveyId = :id and c.fieldColumn = :fieldColumn",
                                ImmutableMap.of(
                                        "id", surveyId,
                                        "fieldColumn", d.getName()
                                ),
                                true,
                                CacheRegion.SURVEY,
                                0);
                        // Deactivate includeInSubject in main field, since configuration its now by column.
                        update = HQL_updateByQuery(""
                                        + " UPDATE " + SurveyData.class.getCanonicalName() + " c "
                                        + " SET"
                                        + " c.includeInSubject = 0"
                                        + " WHERE c.id = :id ",
                                ImmutableMap.of(
                                        "id", d.getId()
                                ),
                                true,
                                CacheRegion.SURVEY,
                                0
                        );
                        if (old == null) {
                            SurveyFieldConfigMail obj = new SurveyFieldConfigMail(surveyId, d.getName(), d.getIncludeInMail());
                            makePersistent(obj, loggedUser.getId());
                        } else {
                            update = HQL_updateByQuery(""
                                            + " UPDATE " + SurveyFieldConfigMail.class.getCanonicalName() + " c "
                                            + " SET"
                                            + " c.includeInSubject = :includeInMail"
                                            + " WHERE c.surveyId = :id and c.fieldColumn = :fieldColumn",
                                    ImmutableMap.of(
                                            "id", surveyId,
                                            "includeInMail", d.getIncludeInMail(),
                                            "fieldColumn", d.getName()
                                    ),
                                    true,
                                    CacheRegion.SURVEY,
                                    0
                            );
                        }
                    }
                    break;
                case INFO:
                    if (d.getId() == -1) {
                        SurveySubjectConfiguration obj = new SurveySubjectConfiguration(surveyId, d.getTitle(), d.getIncludeInMail());
                        makePersistent(obj, loggedUser.getId());
                        update = 1;
                    } else {
                        update = HQL_updateByQuery(""
                                        + " UPDATE " + SurveySubjectConfiguration.class.getCanonicalName() + " c "
                                        + " SET "
                                        + " c.surveyId = :id, "
                                        + " c.fieldDescription = :description, "
                                        + " c.includeInSubject = :includeInMail "
                                        + " WHERE c.id = :sscId ",
                                ImmutableMap.of(
                                        "id", surveyId,
                                        "description", d.getTitle(),
                                        "includeInMail", d.getIncludeInMail(),
                                        "sscId", d.getId()
                                )
                        );
                    }
                    break;
                case CONFIGURATION:
                    if (d.getId() == -1) {
                        SurveyMailConfiguration obj = new SurveyMailConfiguration(surveyId, d.getTitle(), d.getIncludeInMail());
                        makePersistent(obj, loggedUser.getId());
                        update = 1;
                    } else {
                        update = HQL_updateByQuery(""
                                        + " UPDATE " + SurveyMailConfiguration.class.getCanonicalName() + " c "
                                        + " SET "
                                        + " c.surveyId = :id, "
                                        + " c.configDescription = :description, "
                                        + " c.status = :includeInMail "
                                        + " WHERE c.id = :sscId ",
                                ImmutableMap.of(
                                        "id", surveyId,
                                        "description", d.getTitle(),
                                        "includeInMail", d.getIncludeInMail(),
                                        "sscId", d.getId()
                                )
                        );
                    }
                    break;
            }
            if (update > 0) {
                result.incrementAndGet();
            }
        });
        return result.get();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer savePendingConfigs(List<FormPendingConfigDTO> dto, Long surveyId, ILoggedUser loggedUser) {
        final AtomicInteger result = new AtomicInteger(0);
        dto.forEach(d -> savePendingConfig(d, surveyId, result, loggedUser));
        return result.get();
    }

    private void savePendingConfig(
            final FormPendingConfigDTO field,
            final Long surveyId,
            final AtomicInteger result,
            final ILoggedUser loggedUser
    ) {
        if (field.getFieldType() == null) {
            getLogger().trace("Missing field type for pending config: {}", field);
            return;
        }
        if (field.getFieldType() != FormConfigFieldType.FIELD) {
            getLogger().trace("Save pending config not implemented for field type: {}", field.getFieldType());
            return;
        }
        if (field.getId() == -1) {
            final FormPendingConfig obj = new FormPendingConfig(
                    surveyId,
                    field.getName(),
                    field.getShowInChips(),
                    field.getShowInDetails()
            );
            makePersistent(obj, loggedUser.getId());
        } else {
            updatePendingShowInShips(field);
            updatePendingShowInDetails(field);
        }
        result.incrementAndGet();
    }

    private void updatePendingShowInShips(final FormPendingConfigDTO field) {
        final Integer currentValue = HQL_findSimpleInteger(" "
                        + " SELECT c.showInChips "
                        + " FROM " + FormPendingConfig.class.getCanonicalName() + " c "
                        + " WHERE c.id = :id",
                "id", field.getId()
        );
        if (Objects.equals(currentValue, field.getShowInChips())) {
            return;
        }
        if (field.getShowInChips() == null) {
            getLogger().error("Missing showInChips for pending config: {}", field);
            return;
        }
        HQL_updateByQuery(" "
                        + " UPDATE " + FormPendingConfig.class.getCanonicalName() + " c "
                        + " SET c.showInChips = :showInChips "
                        + " WHERE c.id = :id ",
                ImmutableMap.of(
                        "showInChips", field.getShowInChips(),
                        "id", field.getId()
                )
        );

    }

    private void updatePendingShowInDetails(final FormPendingConfigDTO field) {
        final Integer currentValue = HQL_findSimpleInteger(" "
                        + " SELECT c.showInDetails "
                        + " FROM " + FormPendingConfig.class.getCanonicalName() + " c "
                        + " WHERE c.id = :id",
                "id", field.getId()
        );
        if (Objects.equals(currentValue, field.getShowInDetails())) {
            return;
        }
        if (field.getShowInDetails() == null) {
            getLogger().error("Missing showInDetails for pending config: {}", field);
            return;
        }
        HQL_updateByQuery(" "
                        + " UPDATE " + FormPendingConfig.class.getCanonicalName() + " c "
                        + " SET c.showInDetails = :showInDetails "
                        + " WHERE c.id = :id ",
                ImmutableMap.of(
                        "showInDetails", field.getShowInDetails(),
                        "id", field.getId()
                )
        );

    }

    private void generateSurveySubjectConfigFromOld(
            final Long oldSurveyId,
            final Long newSurveyId,
            final ISecurityUser loggedUser
    ) {
        if (oldSurveyId == null || newSurveyId == null) {
            getLogger().warn(
                    "Missing oldSurveyId {} or newSurveyId {} at generate new subject config ",
                    oldSurveyId,
                    newSurveyId
            );
            return;
        }
        final List<SurveySubjectConfiguration> oldConfigurations = HQL_findByQuery(" "
                        + " SELECT c"
                        + " FROM " + SurveySubjectConfiguration.class.getCanonicalName() + " c "
                        + " WHERE c.surveyId = :oldSurveyId"
                , ImmutableMap.of("oldSurveyId", oldSurveyId)
                , true
                , CacheRegion.SURVEY
                ,0
        );

        //TODO: Validar que el campo siga existiendo en la nueva versión del formulario
        oldConfigurations.forEach((conf) -> {
            SurveySubjectConfiguration config = new SurveySubjectConfiguration(
                    newSurveyId, conf.getFieldDescription(),
                    conf.getIncludeInSubject()
            );
            makePersistent(config, loggedUser.getId());
        });
    }

    private void generateSurveyFieldConfigFromOld(
            final Long oldSurveyId,
            final Long newSurveyId,
            final ISecurityUser loggedUser
    ) {
        if (oldSurveyId == null || newSurveyId == null) {
            getLogger().warn(
                    "Missing oldSurveyId {} or newSurveyId {} at generate new field config ",
                    oldSurveyId,
                    newSurveyId
            );
            return;
        }
        final List<SurveyFieldConfigMail> oldConfigurations = HQL_findByQuery(" " +
                        "SELECT c FROM " + SurveyFieldConfigMail.class.getCanonicalName() + " c " +
                        "WHERE c.surveyId = :oldSurveyId"
                , ImmutableMap.of("oldSurveyId", oldSurveyId)
                ,true
                , CacheRegion.SURVEY
                ,0
        );

        //TODO: Validar que el campo siga existiendo en la nueva versión del formulario
        oldConfigurations.forEach((conf) -> {
            final SurveyFieldConfigMail config = new SurveyFieldConfigMail(
                    newSurveyId,
                    conf.getFieldColumn(),
                    conf.getIncludeInSubject()
            );
            makePersistent(config, loggedUser.getId());
        });
    }

    private void generateFormPendingConfigFromOld(
            final Long oldSurveyId,
            final Long newSurveyId,
            final ISecurityUser loggedUser
    ) {
        if (oldSurveyId == null || newSurveyId == null) {
            getLogger().warn(
                    "Missing oldSurveyId {} or newSurveyId {} at generate new pending config ",
                    oldSurveyId,
                    newSurveyId
            );
            return;
        }
        final List<FormPendingConfig> oldConfigurations = HQL_findByQuery(" "
                        + " SELECT c"
                        + " FROM " + FormPendingConfig.class.getCanonicalName() + " c "
                        + " WHERE c.surveyId = :oldSurveyId"
                , ImmutableMap.of("oldSurveyId", oldSurveyId)
                ,true
                , CacheRegion.SURVEY
                ,0
        );
        //TODO: Validar que el campo siga existiendo en la nueva versión del formulario
        oldConfigurations.forEach((oldConf) -> {
            final FormPendingConfig config = new FormPendingConfig(
                    newSurveyId,
                    oldConf.getFieldName(),
                    oldConf.getShowInChips(),
                    oldConf.getShowInDetails()
            );
            makePersistent(config, loggedUser.getId());
        });
    }

    private void generateWebhookConfigFromOld(
            final Long oldSurveyId,
            final Long newSurveyId,
            final String newDocumentVersion,
            final ILoggedUser loggedUser
    ) {
        if (oldSurveyId == null || newSurveyId == null) {
            getLogger().warn(
                    "Missing oldSurveyId {} or newSurveyId {} at generate new webhook config ",
                    oldSurveyId,
                    newSurveyId
            );
            return;
        }
        final List<WebhookAnswerMetadataBK> oldConfigurations = HQL_findByQuery(" "
                + " SELECT new " + WebhookAnswerMetadataBK.class.getCanonicalName() + "("
                    + " m.surveyId "
                    + ",m.surveyAnswerFieldName "
                    + ",wh.id AS webhookId"
                + " ) "
                + " FROM " + Webhook.class.getCanonicalName() + " wh"
                + " JOIN wh.surveyFieldsValues m"
                + " WHERE m.surveyId = :surveyId"
                , ImmutableMap.of("surveyId", oldSurveyId)
                , true
                , CacheRegion.SURVEY
                , 0
        );
        final Map<String, Long> metaIds = SurveyUtil.getSurveyAnswerMetadataIds(oldSurveyId);
        final FieldConfigDTO fieldsConfig = SurveyUtil.getFields(
                newSurveyId,
                true,
                false,
                false,
                null,
                0,
                loggedUser,
                false,
                true,
                false
        );
        final Set<String> savedFields = new LinkedHashSet<>();
        final Set<SurveyDataFieldDTO> currentFields = fieldsConfig.getFields();
        final SurveySetup surveySetup = new SurveySetup();
        final Map<Long, Long> newWebhookIdsConf = new LinkedHashMap<>();
        final Set<Long> webhookIds = oldConfigurations.stream()
                .map(WebhookAnswerMetadataBK::getWebhookId)
                .collect(Collectors.toSet());
        webhookIds.forEach((webhookId) -> saveCopyWebhook(
                newSurveyId,
                newDocumentVersion,
                webhookId,
                newWebhookIdsConf,
                surveySetup,
                currentFields,
                loggedUser
        ));
        getSession().flush();
        oldConfigurations.forEach((oldConf) -> {
            oldConf.setWebhookId(newWebhookIdsConf.get(oldConf.getWebhookId()));
            surveySetup.restoreWebhookMetadata(oldConf, currentFields, metaIds, savedFields, this);
        });
    }

    private void saveCopyWebhook(
            final Long newSurveyId,
            final String newDocumentVersion,
            final Long webhookId,
            final Map<Long, Long> newWebhookIdsConf,
            final SurveySetup surveySetup,
            final Set<SurveyDataFieldDTO> currentFields,
            final ILoggedUser loggedUser
    ) {
        final Webhook webhook = HQLT_findById(Webhook.class, webhookId);
        webhook.setId(-1L);
        webhook.setSurveyId(newSurveyId);
        webhook.setDocumentVersion(newDocumentVersion);
        webhook.setSurveyFieldsValues(null);
        final String stageFieldCode = webhook.getStageFieldCode();
        final SurveyDataFieldDTO stageField = surveySetup.findSurveyFieldByFieldName(stageFieldCode, currentFields);
        if (stageField == null) {
            getLogger().warn("The stage field {} does not exist in the new survey", stageFieldCode);
            webhook.setStageFieldObjectId(null);
        } else {
            webhook.setStageFieldObjectId(stageField.getFieldObjectId());
        }
        final Webhook savedWebhook = makePersistent(webhook, loggedUser.getId());
        newWebhookIdsConf.put(webhookId, savedWebhook.getId());
    }


    private void migrateSurveyDataSurveyConfig(
            final Long oldSurveyId,
            final Long newSurveyId
    ) {
        if (oldSurveyId == null || newSurveyId == null) {
            getLogger().warn(
                    "Missing oldSurveyId {} or newSurveyId {} at migrate survey data ",
                    oldSurveyId,
                    newSurveyId
            );
            return;
        }
        final String surveyDataInfo = " " +
                "SELECT new map ( " +
                    " sd.id as id" +
                    ",sd.fieldId as fieldId" +
                    ",sd.fieldObjectId as fieldObjectId" +
                    ",sd.includeInSubject as includeInSubject" +
                    ",sd.includeInMail as includeInMail " +
                    ",sd.fieldCode as fieldCode " +
                ") FROM " + SurveyData.class.getCanonicalName() + " sd " +
                "WHERE sd.surveyId = :surveyId";

        final List<Map<String, Object>> oldSurveyData = HQL_findByQuery(
                surveyDataInfo,
                ImmutableMap.of("surveyId", oldSurveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        final List<Map<String, Object>> newSurveyData = HQL_findByQuery(
                surveyDataInfo,
                ImmutableMap.of("surveyId", newSurveyId),
                true,
                CacheRegion.SURVEY,
                0
        );

        newSurveyData.forEach(surveyData -> {
            final Map<String, Object> currentOldSurveyData = oldSurveyData
                    .stream().filter(f -> f.get("fieldCode").equals(surveyData.get("fieldCode")))
                    .findAny()
                    .orElse(null);
            if (currentOldSurveyData != null) {
                final Integer sourceUpdated = HQL_updateByQuery("" +
                                " UPDATE " + SurveyFieldObject.class.getCanonicalName() + " sfo " +
                                " SET " +
                                    " sfo.includeInSubject = :includeInSubject" +
                                    ",sfo.includeInMail = :includeInMail " +
                                " WHERE sfo.id = :fieldObjectId",
                        ImmutableMap.of(
                                "includeInSubject", currentOldSurveyData.get("includeInSubject"),
                                "includeInMail", currentOldSurveyData.get("includeInMail"),
                                "fieldObjectId", surveyData.get("fieldObjectId")
                        )
                );
                if (sourceUpdated == 0) {
                    throw new ExplicitRollback("El formulario se encuentra en mantenimiento, intente más tarde");
                }
                HQL_updateByQuery(" " +
                                " UPDATE " + SurveyData.class.getCanonicalName() + " sd " +
                                " SET " +
                                " sd.includeInSubject = :includeInSubject" +
                                ",sd.includeInMail = :includeInMail " +
                                " WHERE sd.id = :surveyDataId",
                        ImmutableMap.of(
                                "includeInSubject", currentOldSurveyData.get("includeInSubject"),
                                "includeInMail", currentOldSurveyData.get("includeInMail"),
                                "surveyDataId", surveyData.get("id")
                        )
                );
            }
        });
    }

}
