package DPMS.DAO;

import DPMS.DAOInterface.IAuditIndividualDAO;
import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.Audit;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualComment;
import DPMS.Mapping.AuditIndividualLite;
import DPMS.Mapping.AuditIndividualPending;
import DPMS.Mapping.AuditSave;
import DPMS.Mapping.AuditType;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Persistable;
import DPMS.Mapping.User;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.login.dto.ServicesDTO;
import bnext.reference.AreaRef;
import bnext.reference.UserRef;
import com.google.common.collect.Sets;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveySearch;
import isoblock.surveys.dao.hibernate.SurveySimple;
import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.audit.entity.IAuditIndividual;
import qms.audit.listeners.OnAcceptedAuditResult;
import qms.audit.listeners.OnAcceptedAuditResultAutomatically;
import qms.audit.listeners.OnAddedComment;
import qms.audit.listeners.OnCanceledAudit;
import qms.audit.listeners.OnCanceledAudits;
import qms.audit.listeners.OnChangeDeniedByLeader;
import qms.audit.listeners.OnChangeDeniedByManager;
import qms.audit.listeners.OnConfirmedAudit;
import qms.audit.listeners.OnDeletedAllAudits;
import qms.audit.listeners.OnDeletedAudit;
import qms.audit.listeners.OnDeletedAudits;
import qms.audit.listeners.OnFinishedAuditFill;
import qms.audit.listeners.OnPlannedAudit;
import qms.audit.listeners.OnPlannedToConfirmAudit;
import qms.audit.listeners.OnRequestedChangeToLeader;
import qms.audit.listeners.OnRequestedChangeToManager;
import qms.audit.listeners.OnStartedAuditFill;
import qms.audit.pending.imp.ToConfirmDate;
import qms.framework.core.AuditProgramDto;
import qms.framework.util.SessionFilterHandler;
import qms.util.BindUtil;
import qms.util.GridOrderBy;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_AuditIndividual")
@Scope(value = "singleton")
@Language(module = "Framework.Config.Lang.DAO.HibernateDAO_AuditIndividual")
public class HibernateDAO_AuditIndividual extends GenericDAOImpl<AuditIndividual, Long> implements IAuditIndividualDAO {
    
    private static final List<Integer> VALID_COMMENT_STATUS = Arrays.asList(new Integer[]{
        AuditIndividual.STATUS.PLANNED.getValue(), AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue(), 
        AuditIndividual.STATUS.PLANED_MAILS_SENT.getValue(), AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue(),
        AuditIndividual.STATUS.DONE.getValue(), 
        AuditIndividual.STATUS.WAITNG_FOR_DATE_CHANGE_BY_MANAGER.getValue(),
        AuditIndividual.STATUS.WAITNG_FOR_DATE_CHANGE_BY_LEADER.getValue()        
    });
    
    
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IAuditIndividualDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IAuditIndividualDAO.class);
    }
    
  /**
   * Implementacion del metodo para guardar/actualizar auditorias
   * El objeto que se guarda es 'auditBk', 'ent' no trae todas las cosas (solo trae las cosas que se van a cambiar)
   * 
   * @param ent objeto AuditIndividual que se va a guardar/actualizar
   * @param loggedUser usuario logueado en el sistema
   * @return GenericSaveHandle con los resultados de la operacion
   * <AUTHOR> Cavazos Galindo
   * @since 2.3.2.132
   */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(AuditIndividual ent, ILoggedUser loggedUser) throws QMSException {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_AuditIndividual @ save: {}", Utilities.getSerializedObj(ent));
        }
        StringBuilder comment = new StringBuilder(getTag("modifiedProgrammingAudit"));
        boolean nuevo = (ent.getId() == -1);
        if (nuevo) {
            gsh.setOperationEstatus(0);
            getLogger().error("fail! no se pueden crear nuevos registros de auditorias!");
            return gsh;
        }
        AuditIndividual auditBk = HQLT_findById(ent.getId());
        //Solo el auditor lider general, el auditor líder y los auditores de apoyo, y el encargado del modulo pueden modificar los datos
        IUserDAO daoUsr = getBean(IUserDAO.class);
        Set<ProfileServices> userServicesByUne = daoUsr.getUserServicesByUne(loggedUser.getId(), auditBk.getAudit().getBusinessUnit().getId());
        if (isAuditAccessDenied(loggedUser, auditBk, userServicesByUne)) {
            gsh.setOperationEstatus(0);
            getLogger().warn("No access to audits for user: {}", loggedUser.getDescription());
            gsh.setErrorMessage(getTag("no_access_save"));
            gsh.setSuccessMessage("{'tipo':'no_access'}");
            return gsh;
        }
        if(ent.getHelpers() != null && auditBk.getHelpers() != null && !auditBk.getHelpers().equals(ent.getHelpers())) {
            auditBk.setHelpers(ent.getHelpers());
            if(ent.getHelpers().isEmpty()) {
                comment = new StringBuilder(getTag("deletedAuditors"));
            } else {
                comment = new StringBuilder(getTag("modifiedAuditors"));
                boolean firstHelper = true;
                for(UserRef helper : ent.getHelpers()) {
                    if(!firstHelper) {
                        comment.append(", ");
                    } else {
                        firstHelper = false;
                    }
                    comment.append(
                        SQL_findSimpleString("SELECT c.first_name FROM users c WHERE c.user_id = " + helper.getId())
                    );
                }
            }
        } else {
            auditBk.setHelpers(ent.getHelpers());
        }
        if (auditBk.getStatus() < AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue()) {
            auditBk.setDteStart(ent.getDteStart()); //La fecha de inicio solo puede ser modificada si no ha iniciado
        }
        auditBk.setDteEnd(ent.getDteEnd());
        auditBk.setTmpStart(ent.getTmpStart());
        auditBk.setTmpEnd(ent.getTmpEnd());
        auditBk.setSupportStaff(ent.getSupportStaff());
        auditBk.setTechnicalExperts(ent.getTechnicalExperts());
        auditBk.setAuditorsInTraining(ent.getAuditorsInTraining());
        Date anticipationDate = Utilities.sumarFechasDias(auditBk.getDteStart(), -auditBk.getAudit().getAnticipation());
        if (
            anticipationDate.before(Utilities.sumarFechasDias(new Date(), 1))
            && auditBk.getStatus() < AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue()
        ) {
            IUntypedDAO dao = getUntypedDAO();
            SurveySimple ss = auditBk.getAudit().getSurvey();
            ss.setEstatus(Survey.ESTATUS_BLOQUEADO);
            ss = dao.makePersistent(ss);
            if (ss != null) {
                auditBk.getAudit().setSurvey(ss);
            }
            auditBk = getAspectJAutoProxy().saveAuditAsPlannedToConfirm(auditBk, "", loggedUser);
        } else {
            //Guardando registro
            auditBk = makePersistent(auditBk);       
            getAspectJAutoProxy().addComment(auditBk.getId(), comment.toString(), loggedUser);
        }
        
        if (auditBk != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":\"edit_success\"}");
            gsh.setSavedId(ent.getId().toString());
            
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    private boolean isAuditAccessDenied(ILoggedUser loggedUser, AuditIndividual audit, Set<ProfileServices> userServicesByUne) {
        if (loggedUser.isAdmin()) {
            return false;
        }
        if (userServicesByUne.contains(ProfileServices.AUDIT_QUALITY_MANAGER)) {
            return false;
        }
        if (userServicesByUne.contains(ProfileServices.AUDIT_LIDER)) {
            return false;
        }
        if (userServicesByUne.contains(ProfileServices.AUDIT_HELPER)) {
            return false;
        }
        if (audit.getAudit().getAttendant().getId().equals(loggedUser.getId())) {
            return false;
        }
        if (audit.getAttendant().getId().equals(loggedUser.getId())) {
            return false;
        }
        Integer countPendingToConfirmDate = new ToConfirmDate(this).getCountByUser(loggedUser.getId());
        return Objects.equals(countPendingToConfirmDate, 0);
    }

    /**
     * Implementacion del metodo para cancelar auditorias
     * 
     * @param auditIndividualId id de la auditoria a cancelar
     * @param auditId id del plan de auditoria
     * @param loggedUser
     * @return Integer 0 ó 1 para saber si la cancelación se realizó correctamente
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.132
     */
    @Override
    @OnCanceledAudit
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer cancel(Long auditIndividualId, Long auditId, ILoggedUser loggedUser) {
        getLogger().trace("DPMS.DAO.HibernateDAO_AuditIndividual @ delete: [auditIndividualId={}, auditId={}]", auditIndividualId, auditId);
        Integer update = this.HQL_updateByQuery(""
                + " UPDATE DPMS.Mapping.AuditIndividual ai "
                + " SET ai.status = " + AuditIndividual.STATUS.CANCELED + " "
                + " WHERE ai.id = " + auditIndividualId);
        Long count = this.HQL_findSimpleLong(""
                + " SELECT COUNT(*) "
                + " FROM DPMS.Mapping.AuditIndividual ai "
                + " WHERE ai.status = " + AuditIndividual.STATUS.CANCELED + " "
                + " AND ai.audit.id = " + auditId);
        if (count == 0L && update != 0) {
            update = this.HQL_updateByQuery(""
                    + " UPDATE DPMS.Mapping.Audit au "
                    + " SET au.status = " + Audit.STATUS_CANCELED + " "
                    + " WHERE au.id = " + auditId);
        } else if (count > 0L && update != 0) {
            Long countNotAcceptedOrCancelledOrDeleted = this.HQL_findSimpleLong(""
                    + " SELECT COUNT(*) "
                    + " FROM DPMS.Mapping.AuditIndividual ai "
                    + " WHERE ai.status not in (" + AuditIndividual.STATUS.ACCEPTED + ", " + AuditIndividual.STATUS.CANCELED + ")"
                    + " AND ai.deleted not in (" + AuditIndividual.IS_DELETED + ")"
                    + " AND ai.audit.id = " + auditId);
            Long countAccepted = this.HQL_findSimpleLong(""
                    + " SELECT COUNT(*) "
                    + " FROM DPMS.Mapping.AuditIndividual ai "
                    + " WHERE ai.status in (" + AuditIndividual.STATUS.ACCEPTED + ")"
                    + " AND ai.audit.id = " + auditId);
            Long countCanceled = this.HQL_findSimpleLong(""
                    + " SELECT COUNT(*) "
                    + " FROM DPMS.Mapping.AuditIndividual ai "
                    + " WHERE ai.status in (" + AuditIndividual.STATUS.CANCELED + ")"
                    + " AND ai.audit.id = " + auditId);
            if (countNotAcceptedOrCancelledOrDeleted == 0 && countAccepted > 0) {
                update = this.HQL_updateByQuery(""
                        + " UPDATE DPMS.Mapping.Audit au "
                        + " SET au.status = " + Audit.STATUS_DONE + " "
                        + " WHERE au.id = " + auditId);
            } else if (countNotAcceptedOrCancelledOrDeleted == 0 && countCanceled > 0) {
                update = this.HQL_updateByQuery(""
                        + " UPDATE DPMS.Mapping.Audit au "
                        + " SET au.status = " + Audit.STATUS_CANCELED + " "
                        + " WHERE au.id = " + auditId);
            }
        }
        return update;
    }
 
  /**
   * Implementacion del metodo para cancelar auditorias
   * 
   * @param auditIndividualId id de la auditoria a cancelar
   * @param auditId id del plan de auditoria
   * @param loggedUser
   * @return Integer 0 ó 1 para saber si la cancelación se realizó correctamente
   * <AUTHOR> Cavazos Galindo
   * @since 2.3.2.132
   */
  @Override
  @OnDeletedAudit
  @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
  public Integer delete(Long auditIndividualId, Long auditId, ILoggedUser loggedUser) {
    getLogger().trace("DPMS.DAO.HibernateDAO_AuditIndividual @ delete: [auditIndividualId={}, auditId={}]",auditIndividualId,auditId);
    Integer update = this.HQL_updateByQuery(""
            + "UPDATE DPMS.Mapping.AuditIndividual ai "
            + "SET ai.deleted = " + AuditIndividual.IS_DELETED + " "
            + "WHERE ai.id = " + auditIndividualId);
    Long count = this.HQL_findSimpleLong(""
            + "SELECT COUNT(*) "
            + "FROM DPMS.Mapping.AuditIndividual ai "
            + "WHERE ai.deleted = " + AuditIndividual.IS_NOT_DELETED + " "
            + "AND ai.audit.id = " + auditId);
    if (count == 0L && update != 0) {
      update = this.HQL_updateByQuery(""
              + "UPDATE DPMS.Mapping.Audit au "
              + "SET au.deleted = " + Audit.IS_DELETED + " "
              + "WHERE au.id = " + auditId);
    } else if (count > 0L && update != 0) {
      count = this.HQL_findSimpleLong(""
              + "SELECT COUNT(*) "
              + "FROM DPMS.Mapping.AuditIndividual ai "
              + "WHERE ai.deleted = " + AuditIndividual.IS_NOT_DELETED + " "
              + "AND ai.status not in (" + AuditIndividual.STATUS.ACCEPTED + ", " + AuditIndividual.STATUS.CANCELED +")"
              + "AND ai.audit.id = " + auditId);
      if (count == 0L) {
        update = this.HQL_updateByQuery(""
                + "UPDATE DPMS.Mapping.Audit au "
                + "SET au.status = " + Audit.STATUS_DONE + " "
                + "WHERE au.id = " + auditId);
      }
    }
    return update;
  }
    
    @Override
    @OnAddedComment
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle addComment(Long auditIndividualId, String comment, ILoggedUser loggedUser) throws QMSException {  
        return saveComment(auditIndividualId, comment, loggedUser);
    }
    
    /**
     * Implementacion del metodo para guardar comentarios en una auditoria
     * 
     * @param auditIndividualId id de la auditoria a la cuál se le agregaran comentarios
     * @param comment comentarios de la auditoria
     * @param loggedUser usuario logueado en el sistema
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.132
   */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle saveComment(Long auditIndividualId, String comment, ILoggedUser loggedUser) throws QMSException {  
        GenericSaveHandle gsh = new GenericSaveHandle();
        getLogger().trace("DPMS.DAO.HibernateDAO_AuditIndividual @ saveComment: [auditIndividualId={}]", auditIndividualId);
        Integer auditStatus = HQL_findSimpleInteger(""
                + " SELECT c.status"
                + " FROM " + AuditIndividualPending.class.getCanonicalName() + " c"
                        + " WHERE c.id = " + auditIndividualId);
        AuditIndividualComment cm = new AuditIndividualComment(auditIndividualId, "" + comment, loggedUser.getId(), loggedUser.getDescription());
        if (VALID_COMMENT_STATUS.contains(auditStatus) && makePersistent(cm) != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{'tipo':'add_success'}");
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isAuditHelper(Long auditIndividualId, Long loggedUserId) {
        Map params = new HashMap();
        params.put("id", auditIndividualId);
        params.put("loggedUserId", loggedUserId);
        return HQL_findSimpleLong(""
                + " SELECT 1"
                + " FROM " + AuditIndividual.class.getCanonicalName() + " c"
                + " JOIN c.helpers h"
                + " WHERE c.id = :id AND h.id = :loggedUserId", params) == 1L;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<UserRef> getHelpers(Long auditIndividualId) {
        return Sets.newHashSet(HQL_findByQuery(""
                + " SELECT h"
                + " FROM " + AuditIndividual.class.getCanonicalName() + " c"
                + " JOIN c.helpers h"
                + " WHERE c.id = :id", "id", auditIndividualId));
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean save(ILoggedUser loggedUser, AuditSave audit, Set<AuditIndividual> audits) throws QMSException {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Audit @ audits: {}", Utilities.getSerializedObj(audit));
        }

        List<Long> auditIndividualsIds = HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + AuditIndividual.class.getCanonicalName() + " c"
                + " WHERE c.audit.id = " + audit.getId() + " AND status =" + AuditIndividual.STATUS.PLANNED);
        getLogger().info("Hay {} auditorias planeadas que se van borrar.", auditIndividualsIds.size());
        if (auditIndividualsIds.size() > 0) {
            getAspectJAutoProxy().deleteAudits(auditIndividualsIds, "", loggedUser);
        }
        List<AuditIndividual> auditIndividuals = null;
        AuditType type = (AuditType) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + AuditType.class.getCanonicalName() + " c "
                + " WHERE c.id = " + audit.getTypeId());
        if (AuditType.PROCESS_SCOPE.equals(type.getScope())) {
            for(AuditIndividual auditIndividual : audits) {
                auditIndividual.setId(-1L);
                auditIndividual.setDteStart(audit.getDteStart());
                auditIndividual.setTmpStart(audit.getTmpStart());
                auditIndividual.setDteEnd(audit.getDteEnd());
                auditIndividual.setTmpEnd(audit.getTmpEnd());
                auditIndividual.setTypeId(type.getId());
                auditIndividual.setType(type);
                auditIndividual.setHelpers(new HashSet<UserRef>(audit.getHelpers()));
                auditIndividual.setDescription(getAuditIndividualDescription(auditIndividual));
            }
            //Generando registros de auditorias (1 por departamento)
            auditIndividuals = new ArrayList<>(audits);
        }
        if (AuditType.AREA_SCOPE.equals(type.getScope())) {
            //si es de alcance de areas
            String query = ""
                    + " SELECT new DPMS.Mapping.AuditIndividual ("
                        + " au.dteStart,au.dteEnd,au.tmpStart,au.tmpEnd,au.buildingId, au.area.id"
                    + " ) "
                    + " FROM DPMS.Mapping.Audit au "
                    + " WHERE au.id = " + audit.getId() + " "
                    //No doy de alta si ya tiene un departamento relacionado
                    + " AND not exists ("
                        + " SELECT aus.areaId FROM au.audits aus "
                        + " WHERE aus.areaId.id = au.area.id"
                    + " ) ";
            auditIndividuals = HQL_findByQuery(query);
            for(AuditIndividual auditIndividual : auditIndividuals) {
                auditIndividual.setAttendant(audit.getAttendant());
                auditIndividual.setTypeId(type.getId());
                auditIndividual.setType(type);
                auditIndividual.setSurvey(new SurveySearch(audit.getSurvey().getId()));
                auditIndividual.setHelpers(new HashSet<UserRef>(audit.getHelpers()));
                auditIndividual.setDescription(getAuditIndividualDescription(auditIndividual));
            }
        }
        if (auditIndividuals == null) {
            getLogger().warn("Sin auditorias para {}.", audit.getCode());
            return true;
        } else {
            getLogger().trace("Son un total de {} auditorias para {}.", new Object[] {auditIndividuals.size(), audit.getCode()});
        }     
        final Audit auditPlan = new  Audit(audit.getId());
        auditPlan.setType(type);
        final ICodeSequenceDAO codeSequenceDao = (ICodeSequenceDAO) getBean("CodeSequence");
        for (AuditIndividual ent : auditIndividuals) {
            ent.setCode("AUDIT-"
                    + Utilities.todayDateBy("yy")
                    + "" + codeSequenceDao.next(CodeSequence.type.CODE_AUDIT_INDIVIDUAL));
            ent.setDescription(ent.getDescription());
            ent.setType(type);
            ent.setDeleted(AuditIndividual.IS_NOT_DELETED);
            if (AuditType.AREA_SCOPE.equals(type.getScope())) {
                Long departmentId = HQL_findSimpleLong(""
                        + " SELECT c.departmentId"
                        + " FROM " + AreaRef.class.getCanonicalName() + " c"
                        + " WHERE c.id = " + ent.getAreaId());
                BusinessUnitDepartmentLite dept = new BusinessUnitDepartmentLite(departmentId);
                ent.setBusinessUnitDepartment(dept);
            }  
            ent.setAudit(auditPlan);
            if (audit.getStatus().equals(Audit.STATUS_PLANNED)) {
                ent = getAspectJAutoProxy().saveAuditAsPlanned(ent, "", loggedUser);
            } else if (type.getConfirmedByAudited().equals(1)) {
                ent = getAspectJAutoProxy().saveAuditAsPlannedToConfirm(ent, "", loggedUser);
            } else {
                ent = getAspectJAutoProxy().startAuditFill(ent, "", loggedUser);
            }
        }
        return true;
    }
    
    private String getAuditIndividualDescription(IAuditIndividual audit) {
        try {
            Integer scope = 0;
            if(audit.getId() != -1L) {
                scope = HQL_findSimpleInteger(""
                    + " SELECT t.scope"
                    + " FROM " + AuditIndividual.class.getCanonicalName() + " c "
                    + " JOIN c.type t "
                    + " WHERE c.id = " + audit.getId()
                );
            }
            if(scope == 0 && audit.getType() != null && audit.getType().getScope() != null) {
                scope = audit.getType().getScope();
            } else if(scope == 0) {
                scope = AuditType.PROCESS_SCOPE;
            }
            Date startDate = audit.getDteStart();
            if(startDate == null && audit.getId() != -1L) {
                startDate = HQL_findSimpleDate(""
                    + " SELECT c.dteStart"
                    + " FROM " + AuditIndividual.class.getCanonicalName() + " c "
                    + " WHERE c.id = " + audit.getId()
                );
            }
            if(startDate == null && audit.getAudit() != null && ((Persistable) audit.getAudit()).getId() != null) {
                startDate = HQL_findSimpleDate(""
                    + " SELECT c.dteStart"
                    + " FROM " + Audit.class.getCanonicalName() + " c "
                    + " WHERE c.id = " + ((Persistable) audit.getAudit()).getId()
                );
            } else if(startDate == null && audit.getAudit() == null && audit.getId() != -1L) {
                startDate = HQL_findSimpleDate(""
                    + " SELECT a.dteStart"
                    + " FROM " + AuditIndividual.class.getCanonicalName() + " c "
                    + " JOIN c.audit a "
                    + " WHERE c.id = " + audit.getId()
                );
            }
            Long budId = null, areaId = audit.getAreaId();
            if(audit.getBusinessUnitDepartment() != null && ((Persistable) audit.getBusinessUnitDepartment()).getId() != null) {
                budId = ((Persistable) audit.getBusinessUnitDepartment()).getId();
            } else if(audit.getId() != -1L) {
                budId = HQL_findSimpleLong(""
                    + " SELECT bud.id"
                    + " FROM " + AuditIndividual.class.getCanonicalName() + " c "
                    + " JOIN c.businessUnitDepartment bud "
                    + " WHERE c.id = " + audit.getId()
                );
            }
            String dteStart = Utilities.formatDateBy(startDate, "dd/MM/yyyy");
            String description = dteStart;
            if (AuditType.AREA_SCOPE.equals(scope) && areaId != null && areaId != 0L) {
                description = dteStart + ": " + HQL_findSimpleString(""
                    + " SELECT c.description"
                    + " FROM " + Area.class.getCanonicalName() + " c "
                    + " WHERE c.id = " + areaId
                );
            } else if (AuditType.PROCESS_SCOPE.equals(scope) && budId != null && budId != 0L) {
                description = dteStart + ": " + HQL_findSimpleString(""
                    + " SELECT c.description"
                    + " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " c "
                    + " WHERE c.id = " + budId
                );
            }
            return description;
        } catch(Exception e) {
            getLogger().error("No se pudo calcular el nuevo nombre de la auditoría.", e);
            return Utilities.getTag("module.audit");
        }
    }
    
    @Override
    @OnPlannedAudit
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividual saveAuditAsPlanned(AuditIndividual audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.PLANNED.getValue());
        return makePersistent(audit);
    }

    @Override
    @OnPlannedToConfirmAudit
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividual saveAuditAsPlannedToConfirm(AuditIndividual audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue());
        return makePersistent(audit);
    }
   
    @Override
    @OnChangeDeniedByLeader
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending denyChangeDateByLeader(AuditIndividualPending audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue());
        return makePersistent(audit);        
    }
   
    @Override
    @OnChangeDeniedByManager
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public AuditIndividualPending denyChangeDateByManager(AuditIndividualPending audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue());
        return makePersistent(audit);        
    }

    @Override
    @OnStartedAuditFill
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending startAuditFill(AuditIndividualPending audit, String comment, ILoggedUser loggedUserDto) {
        audit.setStatus(AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue());
        audit.setDescription(getAuditIndividualDescription(audit));
        return makePersistent(audit);
    }
    
    @Override
    @OnConfirmedAudit
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending confirmAudit(AuditIndividualPending audit, String comment, ILoggedUser loggedUserDto) {
        audit.setStatus(AuditIndividual.STATUS.PLANED_MAILS_SENT.getValue());
        audit.setDescription(getAuditIndividualDescription(audit));
        return makePersistent(audit);
    }
    
    
    @Override
    @OnStartedAuditFill
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividual startAuditFill(AuditIndividual audit, String comment, ILoggedUser loggedUserDto) {
        audit.setStatus(AuditIndividual.STATUS.ACTIVE_CONFIRMED.getValue());
        return makePersistent(audit);
    }

    @Override
    @OnFinishedAuditFill
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer finishFill(Long auditIndividualId, String comment, ILoggedUser loggedUser) {
        Date actualEnd = new Date();
        Map<String, Object> params = new HashMap();
        params.put("status", AuditIndividual.STATUS.DONE.getValue());
        params.put("actualEnd", actualEnd);
        params.put("auditIndividualId", auditIndividualId);
        return HQL_updateByQuery(""
                + " UPDATE " + AuditIndividual.class.getCanonicalName() + " "
                + " SET status = :status,"
                + " actualEnd = :actualEnd"
                + " WHERE id = :auditIndividualId", params);    
    }

    @Override
    @OnAcceptedAuditResult
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending acceptResults(AuditIndividualPending audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.ACCEPTED.getValue());
        return makePersistent(audit);
    }

    @Override
    @OnAcceptedAuditResultAutomatically
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer acceptResultsAutomatically(Long auditIndividualId, String comment, ILoggedUser loggedUser) {
        return HQL_updateByQuery(""
            + " UPDATE " + AuditIndividual.class.getName() + " "
            + " SET status = " + AuditIndividual.STATUS.ACCEPTED + " "
            + " WHERE id = " + auditIndividualId);
    }

    @Override
    @OnCanceledAudits
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> cancelAudit(Long auditId, String comment, ILoggedUser loggedUser) {        
        List<Long> audits = HQL_findByQuery(""
            + " SELECT aui.id"
            + " FROM " + AuditIndividual.class.getCanonicalName() + " aui "
            + " WHERE aui.audit.id = " + auditId
            + " AND aui.deleted = 0 ");  
        HQL_updateByQuery(""
                + "UPDATE " + AuditIndividual.class.getCanonicalName() + " aui "
                + "SET aui.status = " + AuditIndividual.STATUS_CANCELED + " "
                + "WHERE aui.audit.id = " + auditId);
        return audits;
    }

    @Override
    @OnDeletedAllAudits
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> deleteAudit(Long auditId, String comment, ILoggedUser loggedUser) {     
        List<Long> audits = HQL_findByQuery(""
            + " SELECT aui.id"
            + " FROM " + AuditIndividual.class.getCanonicalName() + " aui "
            + " WHERE aui.audit.id = " + auditId
            + " AND aui.deleted = 0 ");
        HQL_updateByQuery(""
            + " UPDATE " + AuditIndividual.class.getCanonicalName() + " aui "
            + " SET aui.deleted = 1 "
            + " WHERE aui.audit.id = " + auditId
            + " AND aui.deleted = 0 " );
        return audits;
    }

    @Override
    @OnRequestedChangeToManager
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending requestChangeToManager(AuditIndividualPending audit, String comment, ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.WAITNG_FOR_DATE_CHANGE_BY_MANAGER.getValue());
        return makePersistent(audit);
    }

    @Override
    @OnRequestedChangeToLeader
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AuditIndividualPending requestChangeToLeader(AuditIndividualPending audit, String comment,  ILoggedUser loggedUser) {
        audit.setStatus(AuditIndividual.STATUS.WAITNG_FOR_DATE_CHANGE_BY_LEADER.getValue());
        return makePersistent(audit);
    }

    @Override
    @OnDeletedAudits
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer deleteAudits(List<Long> auditIndividualsIds, String comment, ILoggedUser loggedUser) {
        return HQL_updateByQuery(""
                + " DELETE FROM " + AuditIndividual.class.getCanonicalName() + " c "
                + " WHERE " + BindUtil.parseFilterList("c.id", auditIndividualsIds));
    }
   
    /**
     * Regresa el Gridinfo en base al filtro y al dao especificado Asume que va a realizar un filtro en particular
     *
     * @param filter Filter
     * @param years Años de los registros
     * @param loggedUser Usuario con sesión en el sistema
     * @return Gridinfo con los resultados
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<AuditProgramDto> getAuditProgramRows(final SortedPagedFilter filter, final List<Integer> years, final ILoggedUser loggedUser) {
        final SessionFilterHandler sessionHandler = new SessionFilterHandler();
        sessionHandler.saveWindowFilter(filter);
        transformFilter(filter);
        filter.setField(new GridOrderBy("businessUnitDepartmentId"));
        final String condition;
        if (!loggedUser.isAdmin()) {
            String businessUnitCondition = ""
                  //las auditorias que pertenecen a mi unidad de negocio
                  + " EXISTS ("
                    + " SELECT 1 "
                    + " FROM " + User.class.getCanonicalName() + " u "
                    + " JOIN u.puestos p "
                    + " WHERE"
                      + " u.id = " + loggedUser.getId()
                      + " AND ("
                          + " c.audit.businessUnit.id = p.une.id"
                          + " OR c.audit.businessUnit.id = u.businessUnitId "
                      + " ) "
                  + " ) ";
            String myAuditsCondition = "";
            final IUserDAO userDao = getBean(IUserDAO.class);
            final ServicesDTO userServicesData = userDao.getUserServices(loggedUser.getId(), loggedUser.isAdmin());
            Set<ProfileServices> userServices = userServicesData.getUserServices();
            if (!userServices.contains(ProfileServices.AUDIT_QUALITY_MANAGER)) {
                myAuditsCondition = " AND " 
                    + " c.id IN ("
                        + " SELECT a.id "
                        + " FROM " + AuditIndividual.class.getCanonicalName() + " a "
                        + " LEFT JOIN a.helpers h "
                        + " WHERE"
                            + " a.attendant = :userId"
                            + " OR "
                            + " h.id = :userId "
                    + " ) ";
                myAuditsCondition = myAuditsCondition.replaceAll(":userId", loggedUser.getId().toString());
            }
            condition = businessUnitCondition + myAuditsCondition;
        } else {
            condition = "";
        }
        filter.setPageSize(0);
        filter.setField(null);
        filter.getCriteria().put("<condition>", ""
                + " ("
                    + BindUtil.parseFilterList("YEAR(c.dteStart)", years, true)
                    + " OR " + BindUtil.parseFilterList("YEAR(c.dteEnd)", years, true)
                + ")"
                + (condition.isEmpty() ? "" : " AND " + condition));
        final GridInfo<AuditIndividualLite> audits = getRows(AuditIndividualLite.class, filter);
        return transformRows(audits);
    }

    /**
     * Transforma los filtros de AuditProgramDto a AuditIndividualLite
     *
     * @param filter
     */
    private void transformFilter(final SortedPagedFilter filter) {
        final HashMap<String, String> likeCriteria = new HashMap<>(filter.getLikeCriteria().size());
        filter.getLikeCriteria().entrySet()
                .stream()
                .filter(entry -> entry.getKey().equals("audits") || entry.getKey().startsWith("audits@"))
                .map(entry -> entry.getValue())
                .filter(value -> value.contains("<text-in>"))
                .map(value -> value.split("<text-in>"))
                .forEach(textInd -> {
                    likeCriteria.put(textInd[0].replaceAll("\\.", "#"), textInd[1]);
                });
        filter.setLikeCriteria(likeCriteria);

        final HashMap<String, String> lowerLimit = new HashMap<>(filter.getLowerLimit().size());
        filter.getLowerLimit().forEach((key, value) -> {
            final String customKey = key.replace("audits#", "");
            lowerLimit.put(customKey, value);
        });
        filter.setLowerLimit(lowerLimit);

        final HashMap<String, String> upperLimit = new HashMap<>(filter.getUpperLimit().size());
        filter.getUpperLimit().forEach((key, value) -> {
            final String customKey = key.replace("audits#", "");
            upperLimit.put(customKey, value);
        });
        filter.setUpperLimit(upperLimit);
    }

    /**
     * Transforma los resultados de AuditIndividualLite a AuditProgramDto
     *
     * @param audits
     * @return
     */
    private GridInfo<AuditProgramDto> transformRows(GridInfo<AuditIndividualLite> audits) {
        final GridInfo<AuditProgramDto> auditPrograms = new GridInfo<>();
        if (audits == null || audits.getCount() <= 0) {
            return auditPrograms;
        }
        audits.getData()
                .stream()
                .filter(audit -> audit.getDepartment() != null)
                .forEachOrdered(audit -> {
                    AuditProgramDto auditProgram = new AuditProgramDto(audit.getDepartment().getId());
                    final Integer index = auditPrograms.getData().indexOf(auditProgram);
                    if (index == -1) {
                        auditPrograms.getData().add(auditProgram);
                    } else {
                        auditProgram = auditPrograms.getData().get(index);
                    }
                    auditProgram.getAudits().add(audit);
                });
        auditPrograms.setCount(Long.valueOf(auditPrograms.getData().size()));
        return auditPrograms;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<Integer> getAuditProgramYears() {
        final List<Integer> dates = HQL_findByQuery(""
                + " SELECT YEAR(dteStart)"
                + " FROM " + AuditIndividualLite.class.getCanonicalName() + " c "
                + " WHERE c.deleted = 0"
                + " GROUP BY YEAR(dteStart)");
        dates.addAll(HQL_findByQuery(""
                + " SELECT YEAR(dteEnd)"
                + " FROM " + AuditIndividualLite.class.getCanonicalName() + " c "
                + " WHERE c.deleted = 0"
                + " GROUP BY YEAR(dteEnd)"));
        dates.add(Year.now().getValue());
        return new HashSet<>(dates);
    }
    
}
