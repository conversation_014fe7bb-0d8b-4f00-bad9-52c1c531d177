package DPMS.Survey;

import DPMS.Action.Action_RequestSurveyCapture;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.PositionLite;
import DPMS.Mapping.Request;
import DPMS.Mapping.SurveyLite;
import DPMS.Mapping.SurveyView;
import DPMS.Mapping.User;
import Framework.Action.MultiThreadManager;
import Framework.Action.MultiThreadOptions;
import Framework.Config.ITextHasValue;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.util.ApeUtil;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveysFiles;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveySearch;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.access.logic.SessionHelper;
import qms.document.pending.imp.ToFillForm;
import qms.document.util.DocumentPublicationUtils;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.CancelOutstandingDTO;
import qms.form.dto.FieldCommentDTO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.ReadableFieldDTO;
import qms.form.dto.SurveyDataDetailsDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.SurveyData;
import qms.form.util.SurveyUtil;
import qms.framework.rest.SecurityUtils;
import qms.survey.dao.ISurveyFieldHistoryDAO;
import qms.util.QMSException;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowRequestStatus;

/**
 *
 * <AUTHOR> Limas
 */
public class CRUD_Survey extends CRUD_Generic<SurveyView> {

    private String type = "none";
    private String rowsContition = null;
    private Long businessUnitId = 0L;
    private String extraJoin = "";
    private String extraCondition = "";
    private String FORMS_QUERY = " SELECT new Map("
                    + " c.id as id, "
                    + " d.code as code, "
                    + " d.masterId as masterId, "
                    + " c.estatus as estatus, "
                    + " c.vchTexto as vchTexto,"
                    + " c.plainText as plainText,"
                    + " c.intQuestions as intQuestions,"
                    + " c.dteCreacion as dteCreacion,"
                    + " c.isTemplateUseAvailable as isTemplateUseAvailable,"
                    + " c.requestId as requestId,"
                    + " r.status as requestStatus,"
                    + " d.version as requestDocumentVersion,"
                    + " d.restrictRecordsByDepartment as restrictRecordsByDepartment,"
                    + " d.validateAccessFormDepartment as validateAccessFormDepartment,"
                    + " d.restrictRecordsField as restrictRecordsField,"
                    + " d.restrictRecordsObjId as restrictRecordsObjId,"
                    + " r.version as requestVersion,"
                    + " c.businessUnitCount as businessUnitCount,"
                    + " c.addActivitiesEnabled as addActivitiesEnabled"
                + " )"
                + " FROM " + SurveyView.class.getCanonicalName() + " c"
                + " LEFT JOIN c.request r"
                + " LEFT JOIN r.document d"
                + " {extraJoin}"
                + " {extraCondition}";

    private static enum getRowsCondition {
        /* Encuestas / Cuestionarios / Control */
        SURVEY_POLL_CONTROL(""),
        /* Auditorias / Cuestionarios / Control */
        SURVEY_AUDIT_CONTROL(""),
        /* Documentos / Formularios / Control */
        SURVEY_FORM_CONTROL(""),
        /* Documentos / Formularios / Mis Formularios */
        SURVEY_MY_FORMS("c.authorId = ${authorId}");
        String filter;

        private getRowsCondition(String filter) {
            this.filter = filter;
        }

        public String getFilter() {
            return this.filter;
        }
    }

    @SMDMethod
    public SurveyDataDetailsDTO getFormInfoDetails(final String formMasterId, final String formVersion) {
        final IUntypedDAO dao = getUntypedDAO();
        final Long documentId = SurveyUtil.getDocumentId(formMasterId, formVersion);
        if (documentId <= 0) {
            throw new NoSuchElementException("Form not found for masterId '" + formMasterId + "' and version " + formVersion);
        }
        final Long surveyId = SurveyUtil.getSurveyId(formMasterId, formVersion);
        if (surveyId <= 0) {
            throw new NoSuchElementException("Survey not found for masterId '" + formMasterId + "' and version " + formVersion);
        }
        final FieldConfigDTO fieldConfig = SurveyUtil.getTableFields(surveyId, getLoggedUserDto());
        final Set<SurveyDataFieldDTO> fields = fieldConfig.getFields();

        final SurveyDataDetailsDTO info = new SurveyDataDetailsDTO();
        final DocumentSimple form = dao.HQLT_findById(DocumentSimple.class, documentId);
        if (form == null) {
            throw new NoSuchElementException("Document not found for masterId '" + formMasterId + "' and version " + formVersion);
        }
        info.setForm(form);
        final String synonymDynamicTableName = SurveyUtil.generateTableSynonymName(form.getMasterId());
        info.setSynonymDynamicTableName(synonymDynamicTableName);
        final String synonymVersionDynamicTableName = SurveyUtil.generateTablSynonymWithVersionName(form.getMasterId(), form.getVersion());
        info.setSynonymVersionDynamicTableName(synonymVersionDynamicTableName);
        info.setFieldDetails(new ArrayList<>(fields));
        return info;
    }

    @SMDMethod
    public DocumentSimple getFormInfo(final String formMasterId, final String formVersion) {
        final IUntypedDAO dao = getUntypedDAO();
        final Long documentId = SurveyUtil.getDocumentId(formMasterId, formVersion);
        return dao.HQLT_findById(DocumentSimple.class, documentId);
    }
    

    @SMDMethod
    public void releaseFromMultiThreadManager() {
        if (Utilities.getSettings().getAllowManyTabsPerPending().equals(0)) {
            MultiThreadOptions options = new MultiThreadOptions(
                getLoggedUserId(),
                null /* threadId */,
                null /* tabId */,
                "fill" /* contextId */, 
                "v.request.survey.fill" /* actionName */,
                Action_RequestSurveyCapture.class.getCanonicalName()
            );
            MultiThreadManager.release(options);
        }
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getAnswerRows(
            final SortedPagedFilter filter,
            final String formMasterId,
            final String formVersion
    ) throws SQLException {
        final ISurveysDAO dao = getBean(ISurveysDAO.class);
        return dao.getAnswerRows(filter, formMasterId, formVersion, getLoggedUserDto());
    }
    
    @SMDMethod
    public GridInfo<SurveyData> getMetadataRows(
            final SortedPagedFilter filter,
            final String formMasterId,
            final String formVersion
    ) throws SQLException {
        final ISurveysDAO dao = getBean(ISurveysDAO.class);
        return dao.getMetadataRows(filter, formMasterId, formVersion);
    }

    @Override
    public String smd() {
        if ((!isAuditAccess() && getType().equals(Survey.TYPE_AUDIT))
                || (!isPollAccess() && getType().equals(Survey.TYPE_POLL))) {
            return NONE;//<---- Si no ningun acceso al modulo no hace nada
        }
        return SUCCESS;
    }

    @SMDMethod
    public List<ITextHasValue> getPollSurveys(Long surveyId) {
        String filtro = ""
                + " exists ("
                + " SELECT u.id "
                + " FROM c.unes u "
                + " WHERE c.type = '" + Survey.TYPE_POLL + "'"
                + " AND c.estatus IN (" + Survey.ESTATUS_ACTIVO + ", " + Survey.ESTATUS_BLOQUEADO + ")"
                + " ) OR c.id = " + surveyId
                + "";
        return getUntypedDAO().getStrutsComboList(SurveySearch.class, "vchTexto", filtro, surveyId, false);
    }
    
    @SMDMethod
    public List<TextHasValue> getRestrictRecordsFieldValues(Long surveyId) {
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        final List<TextHasValue> results = dao.getRestrictRecordsFieldValues(surveyId);
        return results;
    }

    @Override
    @SMDMethod
    public GridInfo<SurveyView> getRows(SortedPagedFilter filter) {
        StringBuilder condition = new StringBuilder(" c.type = '" + getType() + "' ");
        if (getRowsContition() != null) {
            boolean valid = false;
            for (getRowsCondition c : getRowsCondition.values()) {
                if (c.name().equals(getRowsContition())) {
                    valid = true;
                    break;
                }
            }
            if (valid) {
                if (getRowsCondition.valueOf(getRowsContition()).getFilter().isEmpty()) {
                    //empty, does nothing
                    getLogger().trace("La condicion llave {} esta definida como vacia en el ENUM 'getRowsCondition'", rowsContition);
                } else {
                    condition
                            .append(" AND ")
                            .append(getRowsCondition.valueOf(getRowsContition()).getFilter()
                                    .replace("${authorId}", String.valueOf(getLoggedUserId())));
                }
            } else {
                getLogger().error("La condicion llave {} no esta definida en el ENUM 'getRowsCondition'", rowsContition);
            }
        }

        if (getType().equals("request")) {
            condition.append(" "
                + " AND ("
                    + " r.id IN ( "
                        + " SELECT request.id "
                        + " FROM ").append(Document.class.getCanonicalName()).append(" "
                        + " WHERE status =  ").append(Document.STATUS.ACTIVE.getValue()).append(" "
                    + " ) "
                    + " OR c.isTemplateUseAvailable = 1 "
                    + " OR r.status IN (")
                        .append(Request.STATUS.STAND_BY.getValue()).append(""
                        + ",").append(WorkflowRequestStatus.VERIFING.getValue()).append(""
                        + ",").append(WorkflowRequestStatus.RETURNED.getValue()).append(""
                        + ",").append(WorkflowRequestStatus.APROVING.getValue()).append(""
                    + " )"
                + " )");
        }

        this.businessUnitId = Utilities.getFirstParamLongValue("businessUnitId", getRequest());
        
        GridInfo<SurveyView> result;
        if (businessUnitId != null && businessUnitId > 0) {
            extraJoin = " LEFT JOIN c.unes u ";
            extraCondition = " WHERE u.id = " + businessUnitId + " AND c.estatus IN (" + Survey.ESTATUS_ACTIVO.toString() + ", " + Survey.ESTATUS_BLOQUEADO.toString() + ") " ;
            FORMS_QUERY = FORMS_QUERY.replace("{extraJoin}", extraJoin);
            FORMS_QUERY = FORMS_QUERY.replace("{extraCondition}", extraCondition);
            filter.getCriteria().put("<condition>", condition.toString());
            result = getUntypedDAO().HQL_getRows(FORMS_QUERY, filter);  
        } else {
            extraJoin = "";
            extraCondition = "";
            FORMS_QUERY = FORMS_QUERY.replace("{extraJoin}", extraJoin);
            FORMS_QUERY = FORMS_QUERY.replace("{extraCondition}", extraCondition);
            filter.getCriteria().put("<condition>", condition.toString());
            result = getUntypedDAO().HQL_getRows(FORMS_QUERY, filter);
        }
        return result;
    }

    @SMDMethod
    public GridInfo<Map<String, Object>> getMyRequestSurveyRows(SortedPagedFilter filter) {
        StringBuilder where = new StringBuilder(" c.type = '" + getType() + "' ");
        if (getRowsContition() != null) {
            boolean valid = false;
            for (getRowsCondition c : getRowsCondition.values()) {
                if (c.name().equals(getRowsContition())) {
                    valid = true;
                    break;
                }
            }
            if (valid) {
                if (getRowsCondition.valueOf(getRowsContition()).getFilter().isEmpty()) {
                    //empty, does nothing
                    getLogger().trace("La condicion llave {} esta definida como vacia en el ENUM 'getRowsCondition'", rowsContition);
                } else {
                    where
                            .append(" AND ")
                            .append(getRowsCondition.valueOf(getRowsContition()).getFilter()
                                    .replace("${authorId}", String.valueOf(getLoggedUserId())));
                }
            } else {
                getLogger().error("La condicion llave {} no esta definida en el ENUM 'getRowsCondition'", rowsContition);
            }
        }
        filter.getCriteria().put("<condition>", where.toString());
        return getUntypedDAO().HQL_getRows(""
                + " SELECT new map("
                    + " c.id as id,"
                    + " c.estatus as estatus,"
                    + " c.isTemplateUseAvailable as isTemplateUseAvailable,"
                    + " r.type as requestType,"
                    + " r.status as requestStatus,"
                    + " d.id as documentId,"
                    + " d.masterId as documentMasterId,"
                    + " d.version as documentVersion,"
                    + " d.restrictRecordsByDepartment as restrictRecordsByDepartment,"
                    + " d.validateAccessFormDepartment as validateAccessFormDepartment,"
                    + " d.restrictRecordsField as restrictRecordsField,"
                    + " d.restrictRecordsObjId as restrictRecordsObjId,"
                    + " r.version as requestVersion,"
                    + " c.plainText as vchTexto,"
                    + " c.intQuestions as intQuestions,"
                    + " c.dteCreacion as dteCreacion,"
                    + " c.requestId as requestId"
                + " ) FROM " + SurveyView.class.getCanonicalName() + " c"
                + " LEFT JOIN c.request r"
                + " LEFT JOIN r.document d", filter);
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getMyRequestOutstandingSurveyRowsRequests(final SortedPagedFilter filter) {
        final String tabFilter = " AND " + ISurveyCaptureDAO.MY_FILL_AS_CREATOR_FILTER
                    .replaceAll(":userId", getLoggedUserId().toString())
                + " AND d.estatus <> " + OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue();
        return getMyRequestOutstandingSurveyRows(
                filter,
                tabFilter
        );
    }

    @SMDMethod
    public GridInfo<Map<String, Object>> getMyRequestOutstandingSurveyRowsDrafts(final SortedPagedFilter filter) {
        final String tabFilter = (" AND " + ISurveyCaptureDAO.MY_FILL_AS_CREATOR_FILTER
                .replaceAll(":userId", getLoggedUserId().toString())
                + " AND d.estatus = " + OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue());
        return getMyRequestOutstandingSurveyRows(
                filter,
                tabFilter
        );
    }

    @SMDMethod
    public WorkflowAuthRole getWorkflowAuthRole(Long outstandingSurveyId) {
        final ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
        return dao.getSurveyAuthRole(outstandingSurveyId, getLoggedUserDto());
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getMyRequestOutstandingSurveyRowsSignatures(final SortedPagedFilter filter) {
        final String tabFilter = " AND " + ISurveyCaptureDAO.MY_SIGNATURES_FILTER
                .replaceAll(":userId", getLoggedUserId().toString());
        return getMyRequestOutstandingSurveyRows(
                filter,
                tabFilter
        );
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getMyRequestOutstandingSurveyRowsSections(final SortedPagedFilter filter) {
        final String tabFilter = " AND " + ISurveyCaptureDAO.MY_SECTIONS_FILTER
                .replaceAll(":userId", getLoggedUserId().toString());
        return getMyRequestOutstandingSurveyRows(
                filter,
                tabFilter
        );
    }
    
    private GridInfo<Map<String, Object>> getMyRequestOutstandingSurveyRows(
            final SortedPagedFilter filter,
            final String tabFilter
    ) {
        filter.getCriteria().put("<condition>", " c.type = '" + getType() +  "' " + tabFilter);
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("outstandingSurveyId");
            filter.setDirection((byte) 2);
        }
        final GridInfo<Map<String, Object>> info = getUntypedDAO().HQL_getRows(ISurveyCaptureDAO.MY_REQUEST_SURVEY_ROWS_QUERY, filter);
        addCanRetriveData(info);
        return info;
    }

    private void addCanRetriveData(GridInfo<Map<String, Object>> result) {
        if (result == null || result.getData() == null || result.getData().isEmpty()) {
            return;
        }
        for (final Map<String, Object> data : result.getData()) {
            final String masterId = (String) data.get("masterId");
            final boolean canRetriveData = DocumentPublicationUtils.canRetrieveData(masterId);
            data.put("canRetriveData", canRetriveData);
        }
    }

    @SMDMethod
    public GridInfo getPropertyBusinessUnitPosition(SortedPagedFilter filter) {
        String selectFrom = ""
                + " SELECT "
                + " new map("
                + " c.id as id, "
                + " c.code as code,"
                + " c.description as description,"
                + " u.description as une"
                + " )"
                + " FROM " + PositionLite.class.getCanonicalName() + " c"
                + " JOIN c.une u ",
                where = ""
                + " c.status = " + PositionLite.ACTIVE_STATUS
                + " AND u is not null ";
        filter.getCriteria().put("<condition>", where);
        return getUntypedDAO().HQL_getRowsByQuery(selectFrom, filter);
    }

    @SMDMethod
    public GridInfo getPropertyOrganizationalUnitPosition(SortedPagedFilter filter) {
        String selectFrom = ""
                + " SELECT"
                + " new map("
                + " c.id as id, "
                + " c.code as code,"
                + " c.description as description,"
                + " p.description as corp"
                + " )"
                + " FROM " + PositionLite.class.getCanonicalName() + " c"
                + " JOIN c.corp p ",
                where = ""
                + " c.status = " + PositionLite.ACTIVE_STATUS
                + " AND c.une is null "
                + " AND p is not null ";
        filter.getCriteria().put("<condition>", where);
        return getUntypedDAO().HQL_getRowsByQuery(selectFrom, filter);
    }

    @SMDMethod
    public GridInfo<Map<String, Object>> getPropertyUser(SortedPagedFilter filter) {
        String selectFrom = ""
                + " SELECT new map("
                    + " c.id as id,"
                    + " c.cuenta as code,"
                    + " c.description as description,"
                    + " d.description as position"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " JOIN c.defaultWorkflowPosition d"
                + " WHERE c.status = " + PositionLite.ACTIVE_STATUS;
        return getUntypedDAO().HQL_getRows(selectFrom, filter);
    }

    @SMDMethod
    public boolean changeIsTemplateUseAvailable(Long surveyId, boolean clickedYes) {
        if (clickedYes) {
            //si se da clic en el icono "yes" (el que ya tiene habilitado usarse como plantilla)
            return new Survey(surveyId).update("isTemplateUseAvailable", 0) > 0;
        } else {
            //si se da clic en el icono "no" (el que no tiene habilitado usarse como plantilla)
            return new Survey(surveyId).update("isTemplateUseAvailable", 1) > 0;
        }
    }
    
    @SMDMethod
    public List<ReadableFieldDTO> readableFields(
            final Long outstandingSurveyId,
            final Long currentAutorizationPoolIndex,
            final Integer currentRecurrence
    ) {
        final IRequestDAO dao = getBean(IRequestDAO.class);
        return dao.readableFields(outstandingSurveyId, currentAutorizationPoolIndex, currentRecurrence);
    }

    @SMDMethod
    public GenericSaveHandle rejectFilledSection(
            Long outstandingSurveyId,
            Long surveyProgressStateReopenId,
            Long invalidAutorizationPoolIndex,
            Long currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId,
            Integer currentRecurrence,
            List<Long> readedFieldSectionsId,
            Long rejectedFieldSeccionId,
            Long rejectedFieldObjSeccionId,
            ArrayList<FieldCommentDTO> rejectionCommentList,
            ArrayList<FieldCommentDTO> acceptedCommentlist) throws IOException, QMSException {
        final IRequestDAO dao = getBean(IRequestDAO.class);
        if (surveyProgressStateReopenId != null) {
            dao.HQL_updateByQuery(""
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " o "
                        + " SET o.surveyProgressStateReopenId = :surveyProgressStateReopenId,"
                        + " o.progressStateId = :surveyProgressStateReopenId"
                    + " WHERE o.id = :outstandingSurveysId",
                    ImmutableMap.of(
                            "surveyProgressStateReopenId", surveyProgressStateReopenId,
                            "outstandingSurveysId", outstandingSurveyId
                    )
            );
        }
        final GenericSaveHandle gsh = dao.rejectFilledSectionAndCreateCopy(
                outstandingSurveyId,
                invalidAutorizationPoolIndex,
                currentAutorizationPoolIndex, 
                currentAutorizationPoolDetailId,
                currentRecurrence,
                readedFieldSectionsId, 
                rejectedFieldSeccionId,
                rejectedFieldObjSeccionId,
                rejectionCommentList, 
                acceptedCommentlist, 
                getLoggedUserDto()
        );
        return gsh;
    }
    
    @SMDMethod
    public boolean cancelFillForm(Long outstandingSurveyId, Long requestId, String comment, Long surveyProgressStateCancelId) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Document.CRUD_Survey @ cancelFillForm: [outstandingSurveyId={}, requestId={}]", outstandingSurveyId, requestId);
        }
        IRequestDAO daoRequest = getBean(IRequestDAO.class);
        final String documentMasterId = daoRequest.getDocumentMasterIdByRequestId(requestId);
        final boolean canRetriveData = DocumentPublicationUtils.canRetrieveData(documentMasterId);
        if (!canRetriveData) {
            getLogger().debug("Document is being published, masterId: {}", documentMasterId);
            return false;
        }
        IOutstandingSurveysDAO daoSurveys = getBean(IOutstandingSurveysDAO.class);
        String razon;
        if (daoRequest.isUserTheAuthor(requestId, getLoggedUserId())) {
            razon = getTag("cancelFillByAuthor").replace(":user", getLoggedUserName());
        } else {
            razon = getTag("cancelFillGeneric").replace(":user", getLoggedUserName());
        }
        razon = razon.replace(":comment", comment).replace(":user", getLoggedUserName());
        if (surveyProgressStateCancelId != null) {
            daoSurveys.HQL_updateByQuery(""
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName()
                    + " SET surveyProgressStateCancelId = :progressStateId "
                    + " WHERE id = :id",
                    ImmutableMap.of(
                            "progressStateId", surveyProgressStateCancelId,
                            "id", outstandingSurveyId
                    )
            );
        }
        return SurveyUtil.cancelFillForm(daoRequest, daoSurveys, outstandingSurveyId, requestId, comment, razon, SecurityUtils.getLoggedUser());
    }
    
    @SMDMethod
    public boolean destroyFillForm(Long outstandingSurveyId, Long requestId, String comment) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Document.CRUD_Survey @ cancelFillForm: [outstandingSurveyId={}, requestId={}]", outstandingSurveyId, requestId);
        }
        IRequestDAO daoRequest = getBean(IRequestDAO.class);
        boolean isAuthor = daoRequest.isUserTheAuthor(requestId, getLoggedUserId());
        boolean sendMail = !(daoRequest.isRequestInStandBy(requestId) && isAuthor);
        List<Long> nodesIds = SessionHelper.getFolderAccess(getLoggedUserId(), daoRequest);
        Long requestNodeId = daoRequest.HQL_findSimpleLong(" "
                + "SELECT "
                    + " r.nodoId "
                + "FROM " + Request.class.getCanonicalName() + " r "
                    + "WHERE r.id = :requestId "
                    , ImmutableMap.of("requestId", requestId));
        boolean hasFolderAccess = nodesIds.contains(requestNodeId);
        if (daoRequest.isDocumentManager(requestId, getLoggedUserId(), isAdmin()) || isAuthor || hasFolderAccess) {
            String razon = getTag("destroyFillReazon").replace(":user", getLoggedUserName());
            razon = razon.replace(":comment", comment).replace(":user", getLoggedUserName());
            IOutstandingSurveysDAO daoSurveys = getBean(IOutstandingSurveysDAO.class);
            if (daoRequest.destroyRequest(requestId, razon)) {
                return daoSurveys.destroyOutstandingSurvey(requestId, outstandingSurveyId, razon, sendMail, getLoggedUserDto()) == 1;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRowsContition() {
        return rowsContition;
    }

    public void setRowsContition(String rowsContition) {
        this.rowsContition = rowsContition;
    }

    @SMDMethod
    public GridInfo getTemmplateFormRows(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + " c.type = '" + Survey.TYPE_REQUEST + "' "
                + " AND c.isTemplateUseAvailable = 1"
                + " AND c.status = " + SurveyLite.STATUS.ACTIVE.getValue());
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_getRowsByQuery(""
                + " SELECT new map(c.id as id, c.description as description, c.dteCreacion as dteCreacion)"
                + " FROM " + SurveyLite.class.getCanonicalName() + " c", filter);
    }

    @SMDMethod
    public List<Map<String, Object>> getFieldHistory(Long outstandingSurveyId, Long fieldObjectId) {
        ISurveyFieldHistoryDAO daoHistoryComment = getBean(ISurveyFieldHistoryDAO.class);
        return daoHistoryComment.getFieldHistory(outstandingSurveyId, fieldObjectId);
    }

    @SMDMethod
    public List<Map<String, Object>> getSignFieldHistory(Long outstandingSurveyId) {
        ISurveyFieldHistoryDAO daoHistoryComment = getBean(ISurveyFieldHistoryDAO.class);
        return daoHistoryComment.getSignFieldHistory(outstandingSurveyId);
    }
    
    @SMDMethod
    public GenericSaveHandle saveFiles(final Long outstandingSurveyId, final Long[] addedFileIds) {
        IUntypedDAO dao = getUntypedDAO();
        Set<FilesLite> currentFiles = new HashSet(dao.HQL_findByQuery(""
            + " SELECT f "
            + " FROM " + OutstandingSurveysFiles.class.getCanonicalName() + " o "
            + " JOIN o.files f "
            + " WHERE o.id = " + outstandingSurveyId
        ));
        OutstandingSurveysFiles o = new OutstandingSurveysFiles(outstandingSurveyId);
        o.setFiles(
            new HashSet<>(addedFileIds.length)
        );
        for(Long fileId : addedFileIds) {
            o.getFiles().add(
                new FilesLite(fileId)
            );
        }
        GenericSaveHandle result = new GenericSaveHandle();
        if (dao.makePersistent(o, getLoggedUserId()) == null) {
            getLogger().error("Added files could not be saved. {}", new Object[] {
                addedFileIds
            });
            result.setOperationEstatus(0);
            result.setErrorMessage("error-save");
            return result;
        }
        Long size = dao.HQL_findLong(""
            + " SELECT sum(f.contentSize) "
            + " FROM " + OutstandingSurveysFiles.class.getCanonicalName() + " o "
            + " JOIN o.files f "
            + " WHERE o.id = " + outstandingSurveyId
        );
        result.setOperationEstatus(1);
        // ToDo: El valor 15,000,000 debe ser configurable por formulario y debe considerar el limite de archivos adjuntos en el envio de correos
        if (size > 15000000) {
            getLogger().error("Added files exceed the 15MB limit, las file could not be saved, old: {}, new: {}", new Object[] {
                currentFiles, addedFileIds
            });
            o.setFiles(currentFiles);
            dao.makePersistent(o, getLoggedUserId());
            result.setOperationEstatus(0);
            result.setErrorMessage("exceeded-limit");
        }
        return result;
    }
    
    @SMDMethod
    public GridInfo<FilesLite> getFiles(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
            + " c.id IN ("
                + " SELECT f.id "
                + " FROM " + OutstandingSurveysFiles.class.getCanonicalName() + " o "
                + " JOIN o.files f "
                + " WHERE o.id = " + firstParamCurrentEntityId()
            + " )"
        );
        return Utilities.getUntypedDAO().getRows(FilesLite.class, filter);
    }
    
    @SMDMethod
    public String getFillFormPending(final Long outstandingSurveyId, Boolean isContinueFill) {
        IOutstandingSurveysDAO dao = getBean(IOutstandingSurveysDAO.class);
        Long requestId = dao.HQL_findLong(""
            + " SELECT o.requestId "
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " WHERE o.id = :outstandingSurveyId", "outstandingSurveyId", outstandingSurveyId
        );
        Long pendingRecordId = new ToFillForm(dao).getPendingRecordIdFromRecordId(getLoggedUserDto(), requestId, APE.DOCUMENT_TO_FILL_FORM.getCode());
        if (pendingRecordId != null) {
            IFormCaptureDAO formCaptureDAO = getBean(IFormCaptureDAO.class);
            return ApeUtil.getAttenderFillFormWithoutRedirectLink(requestId, isContinueFill ,formCaptureDAO.getFillFormDTO(requestId));
        } else {
            final ISurveyCaptureDAO surveyDao = getBean(ISurveyCaptureDAO.class);
            final WorkflowAuthRole authRole = surveyDao.getSurveyAuthRole(outstandingSurveyId, -1l, getLoggedUserDto());
            if (WorkflowAuthRole.ADMIN.equals(authRole)) {
                return WorkflowAuthRole.ADMIN.toString();
            }
        }
        return null;
    }

    @SMDMethod
    public Integer getStatusOfOutstandingSurveyId(Long outstandingSurveysId) {
        return getUntypedDAO().HQL_findSimpleInteger(""
            + " SELECT c.status "
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
            + " WHERE c.id = :outstandingSurveysId", "outstandingSurveysId", outstandingSurveysId
        );
    }
    
    private Boolean isCancelAvailable(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return false;
        }     
        IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
        return formCaptureDAO.isCancelAvailable(outstandingSurveyId, SecurityUtils.getLoggedUser());
    }

    private Boolean hasFormPending(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return false;
        }      
        IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
        return formCaptureDAO.hasFormPending(outstandingSurveyId, SecurityUtils.getLoggedUser());
    }

    @SMDMethod
    public CancelOutstandingDTO getCancelOutstandingData(Long outstandingSurveyId, Long cancelationCurrentUserId) {
        CancelOutstandingDTO data = new CancelOutstandingDTO();
        data.setCanCancelSurvey(canCancelSurvey(outstandingSurveyId));
        data.setHasCancelRequestInProgress(surveyHasCancelRequestInProgress(outstandingSurveyId));
        if (cancelationCurrentUserId != null && cancelationCurrentUserId > 0) {
            IUntypedDAO dao = Utilities.getUntypedDAO();
            String cancelationCurrentUserName = dao.HQL_findSimpleString(" "
                    + " SELECT c.description"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id",
                    ImmutableMap.of("id", cancelationCurrentUserId)
            );
            data.setCancelationCurrentUserName(StringUtils.trim(cancelationCurrentUserName));
        }
        return data;
    }
    
    private Boolean canCancelSurvey(Long outstandingSurveyId) {
        final boolean hasAuthority = isAdmin() || hasFormPending(outstandingSurveyId) || getLoggedUserServices().contains(ProfileServices.FORM_CANCEL_ANY);
        if (!hasAuthority) {
            return false;
        }
        return isCancelAvailable(outstandingSurveyId);
    }

    private Boolean surveyHasCancelRequestInProgress(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId == 0) {
            return false;
        }
        try {
            IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
            return formCaptureDAO.surveyHasCancelRequestInProgress(outstandingSurveyId);
        } catch (QMSException e) {
            throw new RuntimeException(e);
        }
    }
}
