package bnext.reference.document;

import DPMS.Mapping.DocumentBusinessUnit;
import DPMS.Mapping.PositionRef;
import bnext.reference.UserRef;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "receipt_acknowledgment")
@Immutable
public class HardDocumentSearch implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String code;
    private DocumentBusinessUnit document;
    private UserRef readerUser;
    private PositionRef readerPosition;
    private UserRef printer;
    private UserRef assigner;
    private Integer status;
    private Integer filePages;
    private Long documentPrintingId;
    private Long fileId;
    private UserRef collectedBy;
    private Date collectedAt;
    private UserRef reportedLostBy;
    private Date reportedLostAt;
    private Date printDate;
    private DocumentPrintingRef documentPrinting;
    private String cancellationReason;

    public HardDocumentSearch() {
    }

    @Id
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long validationTypeId) {
        this.id = validationTypeId;
    }
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "file_pages")
    public Integer getFilePages() {
        return filePages;
    }

    public void setFilePages(Integer filePages) {
        this.filePages = filePages;
    }

    @Column(name = "document_printing_id")
    public Long getDocumentPrintingId() {
        return documentPrintingId;
    }

    public void setDocumentPrintingId(Long documentPrintingId) {
        this.documentPrintingId = documentPrintingId;
    }
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @ManyToOne
    @JoinColumn(name = "document_id", referencedColumnName = "id", insertable = false, updatable = false)
    public DocumentBusinessUnit getDocument() {
        return document;
    }

    public void setDocument(DocumentBusinessUnit document) {
        this.document = document;
    }

    @ManyToOne
    @JoinColumn(name = "reader_id", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getReaderUser() {
        return readerUser;
    }

    public void setReaderUser(UserRef readerUser) {
        this.readerUser = readerUser;
    }

    @ManyToOne
    @JoinColumn(name = "print_by", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getPrinter() {
        return printer;
    }

    public void setPrinter(UserRef printer) {
        this.printer = printer;
    }

    @ManyToOne
    @JoinColumn(name = "created_by", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getAssigner() {
        return assigner;
    }

    public void setAssigner(UserRef assigner) {
        this.assigner = assigner;
    }

    
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @ManyToOne(optional=true, fetch = FetchType.EAGER)
    @JoinColumn(name="position_id", referencedColumnName="puesto_id", insertable=false,updatable=false)
    public PositionRef getReaderPosition() {
        return readerPosition;
}

    public void setReaderPosition(PositionRef readerPosition) {
        this.readerPosition = readerPosition;
    }

    @ManyToOne
    @JoinColumn(name = "collected_by", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getCollectedBy() {
        return collectedBy;
    }

    public void setCollectedBy(UserRef collectedBy) {
        this.collectedBy = collectedBy;
    }

    @Column(name = "collected_at")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCollectedAt() {
        return collectedAt;
    }

    public void setCollectedAt(Date collectedAt) {
        this.collectedAt = collectedAt;
    }

    @ManyToOne
    @JoinColumn(name = "reported_lost_by", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getReportedLostBy() {
        return reportedLostBy;
    }

    public void setReportedLostBy(UserRef reportedLostBy) {
        this.reportedLostBy = reportedLostBy;
    }

    @Column(name = "reported_lost_at")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getReportedLostAt() {
        return reportedLostAt;
    }

    public void setReportedLostAt(Date reportedLostAt) {
        this.reportedLostAt = reportedLostAt;
    }
    
    @Column(name = "print_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getPrintDate() {
        return printDate;
    }
    
    public void setPrintDate(Date printDate) {
        this.printDate = printDate;
    }
    
    @JoinColumn(name = "document_printing_id", referencedColumnName = "id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public DocumentPrintingRef getDocumentPrinting() {
        return documentPrinting;
    }


    public void setDocumentPrinting(DocumentPrintingRef documentPrinting) {
        this.documentPrinting = documentPrinting;
    }
 

    @Column(name = "cancellation_reason")
    public String getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }
   
}
