package bnext.licensing;

import DPMS.DAOInterface.ISettingsDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Profile;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.dto.LocaleDTO;
import bnext.dto.ProfileSchemaDTO;
import bnext.dto.ProfileScopeDTO;
import bnext.dto.ProfileServiceDTO;
import com.google.gson.Gson;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import mx.bnext.access.ProfileServices;
import mx.bnext.cipher.HexUtil;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.security.CryptoUtils;
import mx.bnext.core.util.ClassLoaderUtil;
import mx.bnext.core.util.Loggable;
import mx.bnext.licencing.reader.LicencingReader;
import mx.bnext.licensing.License;
import mx.bnext.licensing.Schema;
import mx.bnext.licensing.config.LicensingConfig;
import mx.bnext.licensing.config.RequiredSchema;
import mx.bnext.licensing.util.LicenseType;
import mx.bnext.licensing.util.SchemaType;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.slf4j.Logger;
import qms.access.entity.ProfileLicense;
import qms.access.util.ProfileServicesUtil;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.mail.SchemaDto;
import qms.framework.mail.SchemaInformationDTO;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.AboutApp;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.LocaleUtil;
import qms.framework.util.MeasureTime;
import qms.util.BindUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
@Language(module = "Framework.Config.Lang.common")
public class LicenseUtil {

    private static final String KEY = "cuemEse";
    private static final Class<LicenseUtil> LICENCE_UTIL_CLASS = LicenseUtil.class;
    private static final Logger LOGGER = Loggable.getLogger(LICENCE_UTIL_CLASS);

    public static final String DEFAULT_LICENSE_SCHEMA = "ALL-SERVICES";
    private static final String LICENCE_FOLDER_PATH = "/isoblock/common/";
    private static final String LICENCE_FILE_NAME = "installation.bnext";

    private static final LicenseFileCache FILE_CACHE = new LicenseFileCache();

    private static License loadLicenseFromFile() throws IOException, QMSException {
        final InputStream fileStream = LICENCE_UTIL_CLASS.getResourceAsStream(LICENCE_FOLDER_PATH + LICENCE_FILE_NAME);
        if (fileStream == null) {
            return null;
        }
        final String licenseResource = FileUtils.inputStreamToString(fileStream);
        return loadLicense(licenseResource, true);
    }
    
    private static License loadLicense(
            final String encriptedLicense,
            final boolean update
    ) throws IOException, QMSException {
        ElapsedDataDTO startTime = MeasureTime.start(LICENCE_UTIL_CLASS);
        final String licenseChecksum = CryptoUtils.sha512(encriptedLicense);
        if (FILE_CACHE.hasInvalid(licenseChecksum)) {
            final License newLicense = FILE_CACHE.getInvalid(licenseChecksum);
            generateConfiguration(newLicense, null, update);
            return null;
        }
        License newLicense = null;
        try {
            final License configLic = new License();
            final LicencingReader tool = new LicencingReader();
            configLic.setSystemId(Utilities.getSettings().getSystemId());
            configLic.setVersion(AboutApp.getProjectVersion());
            newLicense = tool.translate(encriptedLicense, configLic, KEY);
            final License previousLic = CurrentLicense.getValue();
            generateConfiguration(newLicense, previousLic, update);
            if (newLicense == null) {
                FILE_CACHE.addInvalid(licenseChecksum, newLicense);
                persistError(newLicense, licenseChecksum, null);
                return null;
            }
            FILE_CACHE.remove(licenseChecksum);
            persistNewLicense(newLicense, licenseChecksum);
            return newLicense;
        } catch (final Exception e) {
            final Throwable error = ExceptionUtils.getRootCause(e);
            if (error != null) {
                final String errorMessage = error.getMessage();
                FILE_CACHE.addInvalid(licenseChecksum, newLicense);
                persistError(newLicense, licenseChecksum, errorMessage);
            } else {
                final String errorMessage = e.getMessage();
                FILE_CACHE.addInvalid(licenseChecksum, newLicense);
                persistError(newLicense, licenseChecksum, errorMessage);
            }
            throw e;
        } finally {
            MeasureTime.stop(
                    startTime,
                    "Elapsed time loading license"
            );
        }
    }

    private static List<String> getSupportedLanguages(final License license) {
        if (license == null) {
            return Utilities.EMPTY_LIST;
        }
        try {
            return Arrays.asList(license.getI18n().split("\\s*,\\s*"));
        } catch (final Exception e) {
            LOGGER.error("Fail loading supported languages", e);
            return Utilities.EMPTY_LIST;
        }
    }

    public static boolean isLicenseInvalid() {
        try {
            return !CurrentLicense.isValid();
        } catch (final Exception e) {
            return true;
        }
    }

    private static Integer daysToSuspend() {
        return daysToSuspend(CurrentLicense.getValue());
    }

    private static Integer daysToSuspend(final License license) {
        if (license == null) {
            return -9999;
        }
        final Integer days = daysToEndSupport(license);
        final Integer grace = daysOfGrace(license);
        return days + grace;
    }

    private static List<String> countEmptySchemas(final License lic, final StringBuilder sb) {
        final List<String> schemas = new ArrayList<>(lic.getSchemas().size());
        for (final Schema schema : lic.getSchemas()) {
            if (schema.getServices().isEmpty()) {
                sb.append("Missing services for schema with code: ").append(schema.getCode()).append("\n");
                CurrentLicense.invalidate();
            }
            schemas.add(schema.getCode());
        }
        return schemas;
    }

    private static Integer getSchemaSeats(final Schema schema) throws NumberFormatException {
        if (schema == null) {
            return 0;
        }
        return Integer.valueOf(schema.getSeats());
    }

    private static Long countInvalidSchemas(final IUntypedDAO dao, final List<String> missingSchemas) {
        return dao.HQL_findSimpleLong(
                " SELECT count(c.licenseCode)"
                + " FROM " + Profile.class.getCanonicalName() + " c"
                + " WHERE " + BindUtil.parseFilterListNotIn("c.licenseCode", missingSchemas, false));
    }

    private static String generateConfiguration(
            final License newLicense,
            final License previousLic,
            final boolean update
    ) throws IOException {
        final StringBuilder sb = new StringBuilder(200);
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        if (newLicense == null) {
            if (update) {
                CurrentLicense.invalidate();
            }
        } else {
            if (update) {
                CurrentLicense.renew(newLicense);
                final ILicenseUserDAO userDao = Utilities.getBean(ILicenseUserDAO.class);
                final List<SchemaInformationDTO> schemas = userDao.initalizeSchemaLicences(newLicense);
                userDao.invalidSchemeLicense(schemas);
            }
            final List<String> emptySchemas = countEmptySchemas(newLicense, sb);
            final Long invalidSchemas = countInvalidSchemas(dao, emptySchemas);
            if (invalidSchemas > 0 && update) {
                CurrentLicense.invalidate();
            }
        }
        final List<String> allSchemas = loadExistingSchemas(dao);
        LicenseUtil lu = new LicenseUtil();
        for (final String schema : allSchemas) {
            final List<String> services = getServicesBySchema(newLicense, schema);
            final String filter = services.isEmpty() ? ""
                    : " AND " + BindUtil.parseFilterListNotIn("c.id.licenseService", services, false);
            final Long missingServices = dao.HQL_findSimpleLong(""
                    + " SELECT count(c.id.licenseService)"
                    + " FROM " + ProfileLicense.class.getCanonicalName() + " c"
                    + " WHERE c.id.licenseCode = '" + schema + "'" + filter);
            if (missingServices > 0) {
                final List<String> allSchemaServices = dao.HQL_findByQuery(
                        " SELECT c.id.licenseService"
                        + " FROM " + ProfileLicense.class.getCanonicalName() + " c"
                        + " WHERE c.id.licenseCode = '" + schema + "'");
                sb.append(Utilities.getSafeTag(lu.getTags(), "schemaCode")).append(schema).append(" " + Utilities.getSafeTag(lu.getTags(), "followingServices"))
                        .append(StringUtils.join(allSchemaServices, ", ")).append("<br>");
                if (update) {
                    CurrentLicense.invalidate();
                }
            }
        }
        if (sb.length() > 0) {
            if (newLicense != null) {
                sb.append(lu.getTag("licenceEnd")).append(" ").append(newLicense.getEndOfSupport()).append("<br>");
            }
            if (previousLic != null) {
                sb.append(lu.getTag("licenceEnd")).append(" ").append(previousLic.getEndOfSupport()).append("<br>");
            }
            final List<RequiredSchema> schemasList = new ArrayList<>();
            for (final String schema : allSchemas) {
                if (schema.equals(DEFAULT_LICENSE_SCHEMA)) {
                    continue;
                }
                final RequiredSchema reqSchema = new RequiredSchema();
                final List<String> services = dao.HQL_findByQuery(
                        " SELECT c.id.licenseService"
                        + " FROM " + ProfileLicense.class.getCanonicalName() + " c"
                        + " WHERE c.id.licenseCode = '" + schema + "'");
                reqSchema.setCode(schema);
                reqSchema.setServices(services);
                reqSchema.setSeats(dao.HQL_findSimpleString(""
                        + " SELECT count(*)"
                        + " FROM " + User.class.getCanonicalName() + " usr "
                        + " WHERE usr.status = " + User.STATUS.ACTIVE.getValue()
                        + " AND usr.licenseCode = '" + schema + "'"));
                schemasList.add(reqSchema);
            }
            final LicensingConfig licConfig = new LicensingConfig();
            licConfig.setSchemas(schemasList);
            if (previousLic != null) {
                // Date Init
                licConfig.setStartOfSupport(previousLic.getStartOfSupport());
                // Date end
                licConfig.setEndOfSupport(previousLic.getStartOfSupport());
                // Language
                licConfig.setI18n(previousLic.getI18n());
                // SystemID
                licConfig.setSystemId(previousLic.getSystemId());
                // Version
                licConfig.setVersion(previousLic.getVersion());
                // Grace in months
                licConfig.setGrace(previousLic.getGrace());
                // Licence type
                licConfig.setLicenseType(previousLic.getLicenseType());
                // Licence limit
                licConfig.setLimitFileStorageBytes(previousLic.getLimitFileStorageBytes());
            } else {
                // SystemID
                licConfig.setSystemId(Utilities.getSettings().getSystemId());
                // Version
                licConfig.setVersion(AboutApp.getProjectVersion());
            }
            final String tex = HexUtil.hexEncoder(new Gson().toJson(licConfig));
            sb.append("<label>").append(lu.getTag("requiredLicenceConfig")).append("</label>");
            sb.append("<textarea id='requiredConfigDetail'>").append(tex).append("</textarea>");
            sb.append("<label>").append(lu.getTag("newLicenceConfig")).append("</label>");
            if (update) {
                CurrentLicense.setError(sb.toString());
            }
            return sb.toString();
        }
        return null;
    }

    public static boolean isDevelopment() {
        if (CurrentLicense.getValue() == null) {
            return false;
        }
        return LicenseType.DEVELOPMENT.getValue().equals(CurrentLicense.getValue().getLicenseType());
    }

    public static String generateSubmitButtonLabel(){
        LicenseUtil lu = new LicenseUtil();
        return Utilities.getSafeTag(lu.getTags(), "sendButtonLabel");
    }
    
    public static String generateCopyConfigButtonLabel(){
        LicenseUtil lu = new LicenseUtil();
        return Utilities.getSafeTag(lu.getTags(), "copyConfig");
    }
    
    public static String generateReloadButtonLabel(){
        LicenseUtil lu = new LicenseUtil();
        return Utilities.getSafeTag(lu.getTags(), "reloadLicense");
    }
    
    public static String generateSystemLicenseTitle(){
        LicenseUtil lu = new LicenseUtil();
        return Utilities.getSafeTag(lu.getTags(), "updateSystemLicenseTitle");
    }
    
    public static String generateCancelButtonLabel(){
        LicenseUtil lu = new LicenseUtil();
        return Utilities.getSafeTag(lu.getTags(), "cancelButtonLabel");
    }
    
    private static List<String> loadExistingSchemas(IUntypedDAO dao) {
        return dao.HQL_findByQuery(" "
            + " SELECT distinct c.id.licenseCode "
            + " FROM " + ProfileLicense.class.getCanonicalName() + " c "
        );
    }

    private static SchemaDto getSchemaInfo(final Schema schema, final Integer licenses, final Integer active) {
        final SchemaDto dto = new SchemaDto();
        dto.setCode(schema.getCode());
        dto.setName(schema.getName());
        dto.setSeats(licenses);
        dto.setUsers(active);
        dto.setServices(new ArrayList<>(schema.getServices()));
        return dto;
    }

    private static List<String> getServicesBySchema(final License lic, final String schemaCode) {
        if (lic == null) {
            return Utilities.EMPTY_LIST;
        }
        for (final Schema schema : lic.getSchemas()) {
            if (!schema.getCode().equals(schemaCode)) {
                continue;
            }
            return new ArrayList<>(schema.getServices());
        }
        return Utilities.EMPTY_LIST;
    }

    private static void overWriteLicense(final String encriptedLicense) throws FileNotFoundException, IOException {
        final File licenseFile = new File(ClassLoaderUtil.getClassPathFolder() + LICENCE_FOLDER_PATH + LICENCE_FILE_NAME);
        FileUtils.writeByteArrayToFile(licenseFile.toPath(), encriptedLicense);
    }

    private static void generateBackupLicense() throws IOException {
        final String licBackup = new SimpleDateFormat("[yyyy-MM-dd][HH_mm_ss]").format(new Date()) + "-" + LICENCE_FILE_NAME;
        final String licenceFolderPath = ClassLoaderUtil.getClassPathFolder() + LICENCE_FOLDER_PATH;
        final File currentLic = new File(licenceFolderPath, LICENCE_FILE_NAME);
        final File copyLicenseFile = new File(licenceFolderPath, licBackup);
        org.apache.commons.io.FileUtils.copyFile(currentLic, copyLicenseFile);
    }

    public static void reset() throws QMSException {
        try {
            FILE_CACHE.clear();
            clearCurrent();
            forceLoadLicense();
        } catch (IOException ex) {
            LOGGER.error("Failed to reset license.", ex);
        }
    }

    public static void clearCurrent() {
        CurrentLicense.reset();
    }

    public static License forceLoadLicense() throws IOException, QMSException {
        if (CurrentLicense.getValue() == null) {
            return loadLicenseFromFile();
        }
        return CurrentLicense.getValue();
    }

    public static License getCurrentLicense() {
        try {
            return forceLoadLicense();
        } catch (final Exception e) {
            LOGGER.error("Failed to load license.", e);
            return null;
        }
    }

    public static List<Schema> getCurrentLicenseSchemas() throws IOException, QMSException {
        License lic = forceLoadLicense();
        if (lic == null) {
            return Utilities.EMPTY_LIST;
        }
        return CurrentLicense.getSchemas();
    }

    public static String getError() {
        return CurrentLicense.getError();
    }

    public static String getConfiguration() throws IOException {
        final License previousLic = CurrentLicense.getValue();
        return generateConfiguration(null, previousLic, false);
    }

    public static Boolean hasSupportedLanguages(final String locale) {
        return hasSupportedLanguages(CurrentLicense.getValue(), locale);
    }

    public static Boolean hasSupportedLanguages(final License license, final String locale) {
        if (license == null) {
            return false;
        }
        final List<String> supportedLanguages = getSupportedLanguages(license);
        if (supportedLanguages == null || supportedLanguages.isEmpty()) {
            return false;
        }
        return supportedLanguages.contains(locale);
    }
    
    public static String filterSupportedLanguages(final License license) {
        String filter = StringUtils.join(getSupportedLanguages(license), "','");
        if (filter == null || filter.trim().isEmpty()) {
            filter = "'es-MX'";
        } else {
            filter = "'" + filter + "'";
        }
        return filter;
    }
    
    public static String filterSupportedLanguages() {
        return filterSupportedLanguages(CurrentLicense.getValue());
    }

    public static Integer daysToEndSupport() {
        return daysToEndSupport(CurrentLicense.getValue());
    }

    public static Integer daysToEndSupport(final License license) {
        if (license == null) {
            return -9999;
        }
        final DateTime today = new DateTime();
        String[] date = license.getEndOfSupport().split("/");
        DateTime eos = new DateTime(Integer.parseInt(date[2]), Integer.parseInt(date[1]), Integer.parseInt(date[0]), 0, 0);
        return Days.daysBetween(today, eos).getDays();
    }

    public static Integer daysOfGrace() {
        return daysOfGrace(CurrentLicense.getValue());
    }

    public static Integer daysOfGrace(final License license) {
        if (license == null) {
            return -9999;
        }
        return Integer.parseInt(license.getGrace()) * 30;
    }

    public static boolean quickValidLicense() {
        try {
            return CurrentLicense.isValid();
        } catch (final Exception e) {
            return false;
        }
    }

    public static boolean validLicense() {
        try {
            if (isLicenseInvalid()) {
                try {
                    final License newLicense = loadLicenseFromFile();
                    if (newLicense == null) {
                        return false;
                    }
                } catch (final Exception e) {
                    return false;
                }
            }
            final SchemaType schema = getSchema(CurrentLicense.getValue());
            if (!schema.equals(SchemaType.ON_PREMISE_SALE)) {
                if (daysToSuspend() < 0) {
                    clearCurrent();
                    return false;
                }
            }
            return CurrentLicense.isValid();
        } catch (final Exception e) {
            LOGGER.error("Could not rebuild license.", e);
            return false;
        }
    }

    public static SchemaType getSchema(final License license) {
        try {
            return SchemaType.byName(license.getScheme());
        } catch (final Exception ex) {
            return SchemaType.ON_PREMISE_RENTAL;
        }
    }

    public static boolean precheckLicense(final License license) {
        if (license == null) {
            return false;
        }
        final SchemaType schema = getSchema(license);
        if (!schema.equals(SchemaType.ON_PREMISE_SALE)) {
            if (daysToSuspend(license) < 0) {
                clearCurrent();
                return false;
            }
        }
        return true;
    }

    public static List<ITextHasValue> getLicenseCodes() {
        if (isLicenseInvalid()) {
            return Utilities.EMPTY_LIST;
        }
        final List<Schema> schemas = CurrentLicense.getSchemas();
        final List<ITextHasValue> licences = new ArrayList<>(schemas.size());
        for (final Schema schema : schemas) {
            licences.add(new TextHasValue(schema.getName(), schema.getCode()));
        }
        return licences;
    }

    public static List<ProfileSchemaDTO> getLicenseSchemas() {
        if (isLicenseInvalid()) {
            return Utilities.EMPTY_LIST;
        }
        final List<ProfileSchemaDTO> schemas = new ArrayList<>(2);
        for (final Schema schema : CurrentLicense.getSchemas()) {
            final ProfileSchemaDTO schemaDto = new ProfileSchemaDTO();
            final List<ProfileServiceDTO> services = new ArrayList<>(schema.getServices().size());
            if (schema.getServices().contains(ProfileServices.ALL.name())) {
                for (ProfileServices item : ProfileServicesUtil.getAllServices()) {
                    if (item.getCode() == null
                            || item.getCode().isEmpty()
                            || item.getModule() == null
                            || item.getSelection() == null) {
                        continue;
                    }
                    ProfileServiceDTO dto = buldService(item);
                    services.add(dto);
                }
            } else {
                for (final String service : schema.getServices()) {
                    final ProfileServices value = ProfileServices.valueOf(service);
                    if (value.getCode() == null
                            || value.getCode().isEmpty()
                            || value.getModule() == null
                            || value.getSelection() == null) {
                        continue;
                    }
                    ProfileServiceDTO dto = buldService(value);
                    services.add(dto);
                }
            }
            schemaDto.setCode(schema.getCode());
            schemaDto.setName(schema.getName());
            schemaDto.setServices(services);
            schemas.add(schemaDto);
        }
        return schemas;
    }

    private static ProfileServiceDTO buldService(final ProfileServices value) {
        final ProfileServiceDTO dto = new ProfileServiceDTO();
        dto.setCode(value.getCode());
        dto.setModule(value.getModule().getKey());
        if (value.getSubModule() != null) {
            dto.setSubModule(value.getSubModule().getKey());
        }
        dto.setSelection(value.getSelection().name());
        dto.setName(value.name());
        dto.setType(value.getType().name());
        return dto;
    }

    public static List<ProfileScopeDTO> getProfileScopes() {
        final List<ProfileScopeDTO> scopes = new ArrayList<>(2);
        // Nivel de permisos de planta
        final ProfileServices businessProfile = ProfileServices.USUARIO_PLANTA;
        final ProfileScopeDTO businessScope = new ProfileScopeDTO();
        businessScope.setCode(businessProfile.getCode());
        businessScope.setAllowedTypes(new ArrayList<>());
        businessScope.getAllowedTypes().add(ProfileServices.ServicesType.SIMPLE.name());
        businessScope.getAllowedTypes().add(ProfileServices.ServicesType.REPORT.name());
        scopes.add(businessScope);
        // Nivel de permisos de corporativo
        final ProfileServices corporateProfile = ProfileServices.USUARIO_CORPORATIVO;
        final ProfileScopeDTO corporateScope = new ProfileScopeDTO();
        corporateScope.setCode(corporateProfile.getCode());
        corporateScope.setAllowedTypes(new ArrayList<>());
        corporateScope.getAllowedTypes().add(ProfileServices.ServicesType.SIMPLE.name());
        corporateScope.getAllowedTypes().add(ProfileServices.ServicesType.REPORT.name());
        corporateScope.getAllowedTypes().add(ProfileServices.ServicesType.CATALOG.name());
        scopes.add(corporateScope);
        // Nivel de permisos sin servicios
        final ProfileScopeDTO onlyTaskScope = new ProfileScopeDTO();
        onlyTaskScope.setCode(ProfileServices.ServicesType.TASK.name());
        onlyTaskScope.setAllowedTypes(new ArrayList<>());
        onlyTaskScope.getAllowedTypes().add(ProfileServices.ServicesType.TASK.name());
        scopes.add(onlyTaskScope);
        return scopes;
    }

    public static List<ProfileServiceDTO> getServices() {
        final List<ProfileServices> values = ProfileServicesUtil.getAllServices();
        final List<ProfileServiceDTO> services = new ArrayList<>(values.size());
        for (final ProfileServices value : values) {
            if (value.getCode() == null
                    || value.getCode().isEmpty()
                    || value.getModule() == null
                    || value.getSelection() == null) {
                continue;
            }
            ProfileServiceDTO service = buldService(value);
            services.add(service);
        }
        return services;
    }

    public static String getSerializedSchemas() {
        if (isLicenseInvalid()) {
            return Utilities.EMPTY_STRING;
        }
        final Map<String, Map<String, Object>> schemas = new HashMap<>(2);
        for (final Schema schema : CurrentLicense.getSchemas()) {
            final Map<String, Object> item = new HashMap<>();
            final List<String> services = new ArrayList<>(schema.getServices().size());
            for (final String service : schema.getServices()) {
                services.add(ProfileServices.valueOf(service).getCode());
            }
            item.put("name", schema.getName());
            item.put("services", services);
            schemas.put(schema.getCode(), item);
        }
        return new Gson().toJson(schemas);
    }

    public static List<String> getSchemaServiceNames(final String schemaCode) {
        return getServicesBySchema(CurrentLicense.getValue(), schemaCode);
    }

    public static Schema getSchema(final String schemaCode) {
        if (isLicenseInvalid()) {
            return null;
        }
        for (final Schema schema : CurrentLicense.getSchemas()) {
            if (!schema.getCode().equals(schemaCode)) {
                continue;
            }
            return schema;
        }
        final Schema schema = new Schema();
        schema.setCode("");
        schema.setName("");
        schema.setSeats("0");
        return schema;
    }

    public static Integer exceededLicences(String licenseCode, IUserDAO dao) {
        final Integer licenses = getLicenses(licenseCode);
        final Integer active = dao.getActiveUsers(licenseCode);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.warn(
                    "> exceededLicences, licenses: {}, active: {}, exceeded: {} {} {}",
                    licenses,
                    active,
                    (active - licenses),
                    Utilities.getSettings().getSystemId(),
                    AboutApp.getProjectVersion()
            );
        }
        if (licenses >= 0) {
            return active - licenses;
        } else {
            LOGGER.error(
                    "Configurar licensias {}, {}",
                    Utilities.getSettings().getSystemId(),
                    AboutApp.getProjectVersion()
            );
            return -1;
        }
    }
    
    public static Integer usedLicences(String licenseCode, IUserDAO dao) {
        return dao.getActiveUsers(licenseCode);       
    }
    
    public static Set<SchemaDto> neededLicences(final IUserDAO dao) throws IOException, QMSException {
        return neededNewLicences(forceLoadLicense(), dao);
    }
        
    public static Set<SchemaDto> neededNewLicences(final License currentLicense, final IUserDAO dao) throws IOException {
        if (currentLicense == null || currentLicense.getSchemas() == null) {
            return Utilities.EMPTY_SET;
        }
        final Set<SchemaDto> schemas = new HashSet<>(2);
        for (final Schema schema : currentLicense.getSchemas()) {
            final Integer active = dao.getActiveUsers(schema.getCode());
            final Integer licenses = getSchemaSeats(schema);
            final Integer neededLicenses = dao.getInactiveBySystemUsers(schema.getCode());
            if ((licenses >= 0 && active - licenses > 0) || neededLicenses > 0) {
                final SchemaDto dto = getSchemaInfo(schema, licenses, active);
                dto.setRequiredSeats(neededLicenses);
                schemas.add(dto);
            }
        }
        return schemas;
    }

    public static SchemaDto getSchemaInformation(final String schemaCode, final IUserDAO dao) throws IOException, QMSException {
        final License currentLicense = forceLoadLicense();
        if (currentLicense == null) {
            return null;
        }
        for (final Schema schema : CurrentLicense.getSchemas()) {
            if (schema.getCode().equals(schemaCode)) {
                final Integer active = dao.getActiveUsers(schema.getCode());
                final Integer licenses = getSchemaSeats(schema);
                return getSchemaInfo(schema, licenses, active);
            }
        }
        return null;
    }

    public static Integer getLicenses(final String licenseCode) {
        if (isLicenseInvalid()) {
            LOGGER.error("License is null.");
            return 0;
        }
        for (final Schema schema : CurrentLicense.getSchemas()) {
            if (licenseCode.equals(schema.getCode())) {
                return getSchemaSeats(schema);
            }
        }
        return 0;
    }

    public static boolean updateLicenseFile(final String encriptedLicense) throws IOException {
        try {
            final License license = loadLicense(encriptedLicense, false);
            if (license == null) {
                CurrentLicense.reset();
                LOGGER.error("Configuración de licencia invalida {}", encriptedLicense);
                return false;
            }
            if (!precheckLicense(license)) {
                CurrentLicense.reset();
                LOGGER.error("Configuración de licencia invalida {}", license);
                return false;
            }
            CurrentLicense.renew(license);
            generateBackupLicense();
            overWriteLicense(encriptedLicense);
            enforceSettingsLocale(license);
            Utilities.getBean(ILicenseUserDAO.class).initalizeSchemaLicences(license);
            return true;
        } catch (final Exception e) {
            CurrentLicense.reset();
            LOGGER.error("Configurar licencias {}, {}.", Utilities.getSettings().getSystemId(), AboutApp.getProjectVersion(), e);
            return false;
        }
    }
    
    public static void enforceSettingsLocale() {
        enforceSettingsLocale(CurrentLicense.getValue());
    }

    public static void enforceSettingsLocale(final License license) {
        Settings settings = Utilities.getSettings();
        if (settings != null && !hasSupportedLanguages(license, settings.getLang() + "-" + settings.getLocale())) {
            updateSettingsLocale(license);
        }
    }

    private static void updateSettingsLocale(final License license) {
        ISettingsDAO dao = Utilities.getBean(ISettingsDAO.class);
        final LocaleDTO systemLocale = getDefaultLicenseLocale(license);
        dao.updateSettingsLocale(systemLocale);
        Utilities.resetSettings(Utilities.getServletContext());
    }
    
    public static LocaleDTO getDefaultLicenseLocale() {
        return getDefaultLicenseLocale(CurrentLicense.getValue());
    }
    
    public static LocaleDTO getDefaultLicenseLocale(final License license) {
        if (license == null) {
            return new LocaleDTO("es", "MX");            
        }
        final List<String> supportedLanguages = getSupportedLanguages(license);
        if (supportedLanguages != null && !supportedLanguages.isEmpty()) {
            final String defaultLanguage = supportedLanguages.get(0);
            if (defaultLanguage.contains("-")) {
                final String lang = defaultLanguage.split("-")[0];
                final String locale = defaultLanguage.split("-")[1];
                return new LocaleDTO(lang, locale);
            } else {
                return new LocaleDTO(defaultLanguage, "MX");
            }
        } else {
            return new LocaleDTO("es", "MX");
        }
    }

    public static boolean isLicenseReloaded(String newLicense, Boolean reloadLicense) {
        return null != newLicense && !newLicense.trim().isEmpty()
                && null != reloadLicense && reloadLicense
                && validLicense();
    }
    
    private static void persistError(final License license, final String licenseChecksum, final String error) throws QMSException {
        final ILicenseUserDAO dao = Utilities.getBean(ILicenseUserDAO.class);
        dao.saveNewLicenseError(license, licenseChecksum, error, SecurityRootUtils.getFirstAdminUserId());
    }
    
    private static void persistNewLicense(final License license, final String licenseChecksum) throws QMSException {
        final ILicenseUserDAO dao = Utilities.getBean(ILicenseUserDAO.class);
        dao.saveNewLicense(license, licenseChecksum, SecurityRootUtils.getFirstAdminUserId());
    }

    private LicenseUtil() {
    }
    
    public ResourceBundle getTags() {
        ResourceBundle tags = null;
        Language lang = getClass().getAnnotation(Language.class);
        String bundle;
        if (lang != null) {
            bundle = lang.module()[0];
            tags = LocaleUtil.getSystemI18n(bundle);
        }
        return tags;
    }
    public String getTag(String key) {
        ResourceBundle tags = getTags();
        return LocaleUtil.getTag(key, tags);
    }
    
    public static Long getLimitFileStorageBytes() {
        if (CurrentLicense.getValue() == null) {
            License license = getCurrentLicense();
            return limitFileStorageBytes(license);
        }
        return limitFileStorageBytes(CurrentLicense.getValue());
    }

    public static Long limitFileStorageBytes(final License license) {
        if (license == null ) {
            LOGGER.error("License not configured");
            return null;
        }
        if (license.getLimitFileStorageBytes() == null ) {
            LOGGER.error("License File Storage Limit no configured {}", license.getLimitFileStorageBytes());
            return null;
        }
        return license.getLimitFileStorageBytes();
    }
}
