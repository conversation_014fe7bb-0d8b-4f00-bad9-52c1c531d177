package bnext.login.logic;

import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.login.Login;
import bnext.login.util.AuthenticationUtils;
import com.google.common.collect.ImmutableMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.framework.core.PasswordEncoder;

/**
 *
 * <AUTHOR>
 */
public class RenewDataService extends SessionViewer {
    
    final String SELECT_BUD_FROM = ""
     + " SELECT"
         + " new " + TextHasValue.class.getCanonicalName() + "(d.description, d.id)"
     + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
     + " JOIN bud.department d "
     + " JOIN bud.businessUnit p "
     + " WHERE "
         // Excluir registros inactivos
         + " bud.status = 1 "
         + " AND d.status = 1 "
         + " AND p.status = 1 "
         // Excluir registros borrados
         + " AND d.deleted = 0 "
         + " AND p.deleted = 0 ";
    
    public String smd() {
        return SUCCESS;
    }

    @SMDMethod 
    public Map<String, Object> dataSource() {
        if(!isValidSession()) {
            return null;
        }
        String positionFilter = getPositionFilter();
        List departments, businessUnits, timezones;
        IUntypedDAO dao = getUntypedDAO();
        
        departments = dao.HQL_findByQuery(""
            + SELECT_BUD_FROM + " " + positionFilter + " GROUP BY d.description, d.id"
        );
        timezones = Utilities.allTimezones();
        if (departments.isEmpty()) {
            departments = dao.HQL_findByQuery(""
                + SELECT_BUD_FROM + " GROUP BY d.description, d.id"
            );
        }
        final String SELECT_BU_FROM = ""
            + " SELECT"
                + " new " + TextHasValue.class.getCanonicalName() + "(p.description, p.id)"
            + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
            + " JOIN bud.department d "
            + " JOIN bud.businessUnit p "
            + " WHERE "
                // Excluir registros inactivos
                + " bud.status = 1 "
                + " AND d.status = 1 "
                + " AND p.status = 1 "
                // Excluir registros borrados
                + " AND d.deleted = 0 "
                + " AND p.deleted = 0 ";
        businessUnits = dao.HQL_findByQuery(""
            + SELECT_BU_FROM + " " + positionFilter + " GROUP BY p.description, p.id"
        );
        if (businessUnits.isEmpty()) {
            businessUnits = dao.HQL_findByQuery(""
                + SELECT_BU_FROM + " GROUP BY p.description, p.id"
            );
        }
        return ImmutableMap.of(
            "businessUnits", businessUnits,
            "departments", departments,
            "timezones", timezones
        );
    }
    
    @SMDMethod 
    public Map<String, Object> dataSourceDepartments(Long businessUnitId) {
        if(!isValidSession()) {
            return null;
        }
        String positionFilter = getPositionFilter();
        List departments;
        IUntypedDAO dao = getUntypedDAO();
        departments = dao.HQL_findByQuery(""
            + SELECT_BUD_FROM + " AND p.id = " + businessUnitId + " " + positionFilter + " GROUP BY d.description, d.id"
        );
        if (departments.isEmpty()) {
            departments = dao.HQL_findByQuery(""
                + SELECT_BUD_FROM + " GROUP BY d.description, d.id"
            );
        }
        return ImmutableMap.of(
            "departments", departments
        );
    }
    
    private String getPositionFilter(){
        if (Utilities.getSettings().getRenewByPosition() == 1) {
            return ""
                + " AND bud.id IN ("
                    + " SELECT de.id"
                    + " FROM " + User.class.getCanonicalName() + " u "
                    + " JOIN u.puestos pu "
                    + " JOIN pu.departamentos de "
                    + " WHERE u.id = " + getLoggedUserId()
                + " )";
        }
        return "";
    }
    
    @SMDMethod 
    public GenericSaveHandle changeMyPassword(String currentPassword, String newPassword) {
        final GenericSaveHandle gsh = AuthenticationUtils.validateCurrentPassword(getRequest(), currentPassword);
        if (Objects.equals(gsh.getOperationEstatus(), 0)) {
            return gsh;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        
        String newHashed = PasswordEncoder.encode(newPassword);
        //Actualizamos la contraseña y el campo para solicitar renovar información
        String query = ""
                + " UPDATE " + User.class.getCanonicalName() + ""
                + " SET"
                    + " hashedPassword = :hashed,"
                    + " version = version + 1,"
                    + " askToRenewPassword = :renewed "
                + " WHERE id = :account";
        Map params = new HashMap();
        params.put("hashed", newHashed);
        params.put("renewed", RenewDataStatus.DATA_RENEWED.value());
        params.put("account", getLoggedUserId());
        gsh.setOperationEstatus(dao.HQL_updateByQuery(query, params));

        // Se refresca la sesión con los nuevos valores
        if (gsh.getOperationEstatus() == 1) {
            // Struts Session
            getSession().put(Login.RENEW_DATA_PASSWORD, false);
            // Web Session 
            getRequest().getSession().setAttribute(Login.RENEW_DATA_PASSWORD, false);
        } 
        return gsh;
    }
    
    private GenericSaveHandle handleRenewPassword(String currentPassword, String newPassword) {
        final GenericSaveHandle gsh = AuthenticationUtils.validateCurrentPassword(getRequest(), currentPassword);
        if (Objects.equals(gsh.getOperationEstatus(), 0)) {
            return gsh;
        }
        final IUntypedDAO dao = getUntypedDAO();
        gsh.setErrorMessage("fail");
        gsh.setOperationEstatus(0);
        String newHashed = PasswordEncoder.encode(newPassword);
        //Actualizamos la contraseña y el campo para solicitar renovar información
        String query = ""
                + " UPDATE " + User.class.getCanonicalName() 
                + " SET"
                    + " hashedPassword = :hashed,"
                    + " version = version + 1,"
                    + " askToRenewPassword = :renewed"
                + " WHERE id = :account";
        Map params = new HashMap();
        params.put("hashed", newHashed);
        params.put("renewed", RenewDataStatus.DATA_RENEWED.value());
        params.put("account", getLoggedUserId());
        gsh.setOperationEstatus(dao.HQL_updateByQuery(query, params));
        return gsh;
    }
    
    private GenericSaveHandle handleRenewLocation(Long businessUnitId, Long departmentId, Long businessUnitDepartmentId) {
        final IUntypedDAO dao = getUntypedDAO();
        final GenericSaveHandle result = new GenericSaveHandle();
        if (departmentId != null) {
            if (businessUnitId != null) {
                businessUnitDepartmentId = dao.HQL_findLong(""
                        + " SELECT bud.id "
                        + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
                        + " WHERE "
                        + " bud.businessUnitId = " + businessUnitId
                        + " AND bud.departmentId = " + departmentId
                );
            }
            if (businessUnitDepartmentId == null) {
                BusinessUnitDepartment bud = dao.HQLT_findSimple(BusinessUnitDepartment.class, ""
                        + " SELECT bud "
                        + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
                        + " WHERE bud.id = ("
                        + " SELECT max(d.id) "
                        + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " d "
                        + " WHERE "
                        + " d.status = 1 "
                        + " AND d.departmentId = " + departmentId
                        + " )"
                );
                bud.setId(-1L);
                bud.setBusinessUnitId(businessUnitId);
                businessUnitDepartmentId = dao.makePersistent(bud, getLoggedUserId()).getId();
            }
        }
        Map<String, Object> params = new HashMap();
        if (departmentId != null && businessUnitId != null) {
            params.put("askToRenewLocation", RenewDataStatus.DATA_RENEWED.value());
        } else {
            params.put("askToRenewLocation", RenewDataStatus.RENEW_DATA.value());
        }
        params.put("departmentId", departmentId);
        params.put("businessUnitId", businessUnitId);
        params.put("businessUnitDepartmentId", businessUnitDepartmentId);
        params.put("id", getLoggedUserId());
        result.setOperationEstatus(
                dao.HQL_updateByQuery(""
                        + " UPDATE " + User.class.getCanonicalName() + " u "
                        + " SET "
                        + " u.askToRenewLocation = :askToRenewLocation"
                        + ",u.departmentId = :departmentId"
                        + ",u.businessUnitId = :businessUnitId"
                        + ",u.businessUnitDepartmentId = :businessUnitDepartmentId"
                        + " WHERE u.id = :id",
                         params)
        );
        return result;
    }
    
    public GenericSaveHandle handleRenewTimezone(String timezone) {
        GenericSaveHandle result = new GenericSaveHandle();
        IUntypedDAO dao = getUntypedDAO();
        List<TextHasValue> timezones = Utilities.allTimezones().stream().filter(f -> f.getValue().equals(timezone)).collect(Collectors.toList());
        String timezoneName;
        if (timezones.isEmpty()) {
            timezoneName = Utilities.getSettings().getTimeZone();
        } else {
            timezoneName = timezones.get(0).getText();
        }
        Map<String, Object> params = new HashMap();
        params.put("timezone", timezoneName);
        params.put("askToRenewTimezone", false);
        params.put("id", getLoggedUserId());
        result.setOperationEstatus(
            dao.HQL_updateByQuery(""
                + " UPDATE " + User.class.getCanonicalName() + " u "
                + " SET "
                    + "u.timezone = :timezone"
                    + ",u.askToRenewTimezone = :askToRenewTimezone"
                + " WHERE u.id = :id"
            , params)
        );
        return result;
    }
    
    @SMDMethod 
    public GenericSaveHandle saveAll(String currentPassword, String newPassword, Long businessUnitId, Long departmentId, String timezone) {
        final IUntypedDAO dao = getUntypedDAO();
        //GenericSaveHandle result = new GenericSaveHandle();
        boolean renewPassword = (Boolean) getRequest().getSession().getAttribute(Login.RENEW_DATA_PASSWORD);
        boolean renewLocation = (Boolean) getRequest().getSession().getAttribute(Login.RENEW_DATA_LOCATION);
        boolean renewTimezone = (Boolean) getRequest().getSession().getAttribute(Login.RENEW_DATA_TIMEZONE);
        if (renewPassword) {
            GenericSaveHandle result = this.handleRenewPassword(currentPassword, newPassword);
            if (result.getOperationEstatus() == 0) { // Falla al actualizar la contraseña, no se continua.
                return result;
            }
            // Se refresca sesión con los nuevos valores
            getSession().put(Login.RENEW_DATA_PASSWORD, RenewDataStatus.DATA_RENEWED.value()); // Struts Session
            getRequest().getSession().setAttribute(Login.RENEW_DATA_PASSWORD, RenewDataStatus.DATA_RENEWED.value()); // Web Session 
        }
        if (renewLocation) {
            Long businessUnitDepartmentId = null;
            GenericSaveHandle result = this.handleRenewLocation(businessUnitId, departmentId, businessUnitDepartmentId);
            if (result.getOperationEstatus() == 0) { // Falla la planta y departamento, no se continua.
                return result;
            }
            // Se refresca sesión con los nuevos valores
            // Struts Session
            getSession().put(Login.RENEW_DATA_LOCATION, RenewDataStatus.DATA_RENEWED.value());
            getSession().put(Login.DEPARTMENT_ID_ATTRIBUTE, departmentId);
            getSession().put(Login.BUSINESS_UNIT_ID_ATTRIBUTE, businessUnitId);
            getSession().put(Login.BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE, businessUnitDepartmentId);
            // Web Session 
            getRequest().getSession().setAttribute(Login.RENEW_DATA_LOCATION, RenewDataStatus.DATA_RENEWED.value());
            getRequest().getSession().setAttribute(Login.DEPARTMENT_ID_ATTRIBUTE, departmentId);
            getRequest().getSession().setAttribute(Login.BUSINESS_UNIT_ID_ATTRIBUTE, businessUnitId);
            getRequest().getSession().setAttribute(Login.BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE, businessUnitDepartmentId);
            // Nombre de la planta
            String businessUnitName = dao.HQL_findSimpleString(""
                + " SELECT bu.description "
                + " FROM " + BusinessUnit.class.getCanonicalName() + " bu "
                + " WHERE id = " + businessUnitId
            );
            getRequest().getSession().setAttribute(Login.BUSINESS_UNIT_NAME_ATTRIBUTE, businessUnitName);
        }
        if (renewTimezone) {
            GenericSaveHandle result = this.handleRenewTimezone(timezone);
            if (result.getOperationEstatus() == 0) { // Falla la zona horaria, no se continua.
                return result;
            }
            // Se refresca sesión con los nuevos valores
            getSession().put(Login.RENEW_DATA_TIMEZONE, RenewDataStatus.DATA_RENEWED.value()); // Struts Session
            getRequest().getSession().setAttribute(Login.RENEW_DATA_TIMEZONE, RenewDataStatus.DATA_RENEWED.value()); // Web Session 
        }
        // Se llega aca por que todo se hizo correctamente.
        final GenericSaveHandle finalResult = new GenericSaveHandle();
        finalResult.setErrorMessage("success");
        finalResult.setOperationEstatus(1);
        return finalResult;
    }

}
