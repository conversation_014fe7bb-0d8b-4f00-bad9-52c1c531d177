package bnext.login.util;

import bnext.login.dto.ClientBrowserDTO;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import ua_parser.Client;
import ua_parser.Device;
import ua_parser.OS;
import ua_parser.Parser;
import ua_parser.UserAgent;

public class ClientBrowserUtils {

    private static final Logger LOGGER = Loggable.getLogger(ClientBrowserUtils.class);

    public static ClientBrowserDTO parseBrowser(final HttpServletRequest request, final Boolean parseUserAgent) {
        final ClientBrowserDTO info = new ClientBrowserDTO();
        info.setUserAgent(request.getHeader("User-Agent"));
        info.setIpAddress(getIpAddress(request));
        info.setBrowserOs(parseBrowserOs(request.getHeader("Sec-CH-UA-Platform")));
        info.setBrowserOsVersion(parseBrowserOsVersion(request.getHeader("Sec-CH-UA-Platform-Version")));
        info.setMobile(request.getHeader("Sec-CH-UA-Mobile"));
        if (parseUserAgent) {
            loadClientDetails(info);
        }
        return info;
    }
    
    private static String parseBrowserOs(String browserOs) {
        if (browserOs != null && !browserOs.isEmpty()) {
            browserOs = browserOs.replaceAll("\"", "");
        }
        return browserOs;
    }


    private static String parseBrowserOsVersion(String browserOsVersion) {
        if (browserOsVersion != null && !browserOsVersion.isEmpty()) {
            browserOsVersion = browserOsVersion.replaceAll("\"",  "");
            // Ver https://learn.microsoft.com/en-us/microsoft-edge/web-platform/how-to-detect-win11
            if (browserOsVersion.startsWith("12.")
                    || browserOsVersion.startsWith("13.") 
                    || browserOsVersion.startsWith("14.")
                    || browserOsVersion.startsWith("15.")
            ) {
                browserOsVersion = "11";
            }
        }
        return browserOsVersion;
    }
    private static void loadClientDetails(final ClientBrowserDTO info) {
        try {
            final Client client = parseClient(info);
            info.setClient(client);
            if (client == null) {
                return;
            }
            info.setBrowser(client.userAgent.family);
            if (info.getBrowserOs() == null || info.getBrowserOs().isEmpty()) {
                info.setBrowserOs(parseBrowserOs(client.os.family));
            }
            if (info.getBrowserOsVersion() != null && !info.getBrowserOsVersion().isEmpty()) {
                return;
            }
            final String plattaformVersion = readPlattormVersionFromClient(client);
            info.setBrowserOsVersion(parseBrowserOsVersion(plattaformVersion));
        } catch (final Exception e) {
            LOGGER.error("Failed to parse client with User Agent {}", info.getUserAgent());
        }
    }

    private static String readPlattormVersionFromClient(final Client client) {
        final StringBuilder version = new StringBuilder();
        if (client.os.major != null) {
            version.append(".");
            version.append(client.os.major);
        }
        if (client.os.minor != null) {
            if (version.length() > 0) {
                version.append(".");
            }
            version.append(client.os.minor);
        }
        if (client.os.patch != null) {
            if (version.length() > 0) {
                version.append(".");
            }
            version.append(client.os.patch);
        }
        if (client.os.patchMinor != null) {
            if (version.length() > 0) {
                version.append(".");
            }
            version.append(client.os.patchMinor);
        }
        return version.toString();
    }

    private static String getIpAddress(final HttpServletRequest request) {
        final String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress != null) {
            return ipAddress;
        } else {
            return request.getRemoteAddr();
        }
    }

    private static Client parseClient(final ClientBrowserDTO info) {
        final String userAgent = info.getUserAgent();
        if (userAgent == null) {
            final Client client = new Client(UserAgent.OTHER, OS.OTHER, Device.OTHER);
            return client;
        }
        final Parser uaParser = new Parser();
        final Client client = uaParser.parse(userAgent);
        final String plattaform = info.getBrowserOs();
        if (plattaform == null || plattaform.isEmpty()) {
            return client;
        }
        if (!"Windows".equals(plattaform)) {
            return client;
        }
        final String plattaformVersion = info.getBrowserOsVersion();
        if (plattaformVersion == null || plattaformVersion.isEmpty()) {
            return client;
        }
        final OS osInfo = uaParser.parseOS(plattaform + " " + plattaformVersion);
        if (osInfo == null || Objects.equals(osInfo, client.os)) {
            return client;
        }
        final Client newClient = new Client(client.userAgent, osInfo, client.device);
        return newClient;
    }
}
