package bnext.login.util;

import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.login.DAOInterface.IUserDetailsService;
import bnext.login.Login;
import bnext.login.dto.ClientBrowserDTO;
import bnext.login.dto.UserDetailDTO;
import bnext.login.error.AlreadyAnonymousSessionStartedException;
import bnext.login.error.AlreadySessionStartedException;
import bnext.login.error.ExpiredTokenAndSessionStartedException;
import bnext.login.error.FailedLegacySessionException;
import bnext.login.error.LoginFormIsDisabledException;
import bnext.login.error.OidcCreationAccountException;
import bnext.login.error.OidcNotFoundAccountException;
import bnext.login.error.UserInactiveSessionException;
import bnext.login.error.UserIntegratedLoginDisabledException;
import bnext.login.error.UserLockedException;
import bnext.login.error.UserOidcLoginDisabledException;
import bnext.login.error.UserToActivateException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.util.Date;
import mx.bnext.core.util.Loggable;
import org.springframework.security.core.Authentication;
import qms.framework.core.PasswordEncoder;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.TokenDetails;
import qms.framework.security.UserLogin;

public class AuthenticationUtils {
    
    private static final org.slf4j.Logger LOGGER = Loggable.getLogger(AuthenticationUtils.class);
    
    public static void throwDuplicateSessionException(final Long userId, final String account, final TokenDetails tokenDetails)
            throws AlreadySessionStartedException {
        final UserLogin loginInfo = new UserLogin(userId);
        final HttpSession activeSession = loginInfo.getActiveSession();
        final boolean validSession = Login.validSession(activeSession);
        if (!validSession) {
            return;
        }
        loginInfo.setAccount(account);
        if (!Utilities.isAnonymous(account)) {
            final Object browser = activeSession.getAttribute(Login.BROWSER_ATTRIBUTE);
            if (browser != null) {
                loginInfo.setBrowser(browser.toString());
            } else {
                loginInfo.setBrowser("-");
            }
            final Object browserOs = activeSession.getAttribute(Login.BROWSER_OS_ATTRIBUTE);
            if (browserOs != null) {
                loginInfo.setBrowserOs(browserOs.toString());
            } else {
                loginInfo.setBrowserOs("-");
            }
            final Object browserOsVersion = activeSession.getAttribute(Login.BROWSER_OS_VERSION_ATTRIBUTE);
            if (browserOsVersion != null) {
                loginInfo.setBrowserOsVersion(browserOsVersion.toString());
            } else {
                loginInfo.setBrowserOsVersion("-");
            }
            final Object ipAddress = activeSession.getAttribute(Login.IP_ADDRESS_ATTRIBUTE);
            if (ipAddress != null) {
                loginInfo.setIpAddress(ipAddress.toString());
            } else {
                loginInfo.setIpAddress("-");
            }
            loginInfo.setCreatedDate(Utilities.formatDateWithTime(new Date(activeSession.getCreationTime())));
            loginInfo.setAnonymous(false);
        } else {
            loginInfo.setAnonymous(true);
        }
        final String newToken = Login.generateToken(account);
        loginInfo.setToken(newToken);
        LOGGER.info("User has an active session: {}", account);
        if (tokenDetails != null && tokenDetails.isExpired()) {
            throw new ExpiredTokenAndSessionStartedException("Token provided is expired and user has an active session", loginInfo);            
        } else if (Utilities.isAnonymous(account)) {
            throw new AlreadyAnonymousSessionStartedException("Anonymous user has an active session", loginInfo);
        } else {
            throw new AlreadySessionStartedException("User has an active session", loginInfo);
        }
    }
    
    public static void throwInactiveUserSessionException(final UserDetailDTO user)
            throws UserInactiveSessionException, UserToActivateException {
        final String userAccount = user.getAccount();
        final Integer userStatus = user.getStatus(); 
        String value;
        if (userStatus.equals(User.STATUS.INACTIVE.getValue())) {
            value = "inactive";
            LOGGER.info("The User {} has an {} account", new Object[]{userAccount , value});
            throw new UserInactiveSessionException("User has a inactive user session", user);
        } else if (userStatus.equals(User.STATUS.TO_ACTIVATE.getValue())) {
            value = "toActive";
            LOGGER.info("The User {} has an {} account", new Object[]{userAccount , value});
            throw new UserToActivateException("User has a inactive user session", user);
        } 
    }
    
    public static void throwUserLockedException(final UserDetailDTO user) throws UserLockedException {
        final String userAccount = user.getAccount();
        final Integer userStatus = user.getStatus(); 
        String value;
        if (userStatus.equals(User.STATUS.LOCKED.getValue())) {
            value = "locked";
        } else {
            value = "active";
        }
        LOGGER.info("The User {} has an {} account", new Object[]{userAccount , value});
        throw new UserLockedException("User account is locked", user);
    }

    public static void throwNewAccountException(GenericSaveHandle gsh, final String account) {
        final String errorMessage = gsh.getErrorMessage();
        if (errorMessage != null && !errorMessage.isEmpty()) {
            throw new OidcCreationAccountException(errorMessage, gsh);
        } else {
            throw new OidcCreationAccountException("Can not register account " + account, gsh);
        }
    }

    public static void throwNotFoundAccountException(String account) {
        throw new OidcNotFoundAccountException("Can not register account " + account);
    }
    
    public static void throwLoginFormIsDisabled() {
        throw new LoginFormIsDisabledException("Login form is disabled from application settings");
    } 
    
    public static void throwUserOidcLoginDisabled() {
        throw new UserOidcLoginDisabledException("OIDC login is disabled for the user");
    }
    
    public static void throwUserIntegratedLoginDisabled() {
        throw new UserIntegratedLoginDisabledException("Integrated login is disabled for the user");
    }

    public static void throwFailedLegacySessionException(Authentication authentication, Exception exception) {
        final String error = "Failed to initalize legacy session variables";
        LOGGER.error(error, exception);
        throw new FailedLegacySessionException(error, exception);
    }
    
    
    public static GenericSaveHandle validateCurrentPassword(final HttpServletRequest request, final String currentPassword) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final Long userId = SecurityUtils.getLoggedUserId();
        if (userId == null || userId <= 0) {
            gsh.setErrorMessage("fail");
            gsh.setOperationEstatus(0);
            return gsh;
        }
        final IUserDetailsService service = Utilities.getBean(IUserDetailsService.class);
        final String account = SecurityUtils.getLoggedUserAccount();
        final UserDetailDTO userData = service.findUserByUsername(account);
        if (User.STATUS.LOCKED.getValue().equals(userData.getStatus()) && !service.validateLockedUser(userData.getUserId())) {
            AuthenticationUtils.throwUserLockedException(userData);
        } 
        
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        
        final String query = ""
                + " SELECT user.hashedPassword"
                + " FROM " + User.class.getCanonicalName() + " user "
                + " WHERE user.cuenta = :account ";
        final String hashed = dao.HQL_findSimpleString(query, "account", account);
        
        final Boolean matches = PasswordEncoder.matches(currentPassword, hashed);
        LOGGER.debug("matches {}", matches);
        
        gsh.setErrorMessage("fail");
        gsh.setOperationEstatus(0);
        if (matches) {
            gsh.setOperationEstatus(1);
        } else {
            final ClientBrowserDTO browser = ClientBrowserUtils.parseBrowser(request, true);
            final Integer status = service.updateFailAttempts(
                    userData.getUserId(), 
                    userData.getName(),
                    browser.getUserAgent(),
                    browser.getIpAddress()
            );
            if (status.equals(User.STATUS.LOCKED.getValue())) { //Está bloqueado
                AuthenticationUtils.throwUserLockedException(userData);
            }
        }
        return gsh;
    }
    
}
