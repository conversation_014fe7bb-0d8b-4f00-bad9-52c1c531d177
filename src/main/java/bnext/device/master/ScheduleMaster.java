package bnext.device.master;

import DPMS.Mapping.ServiceType;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Immutable;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "service_schedule")
public class ScheduleMaster implements Serializable {
    private static final long serialVersionUID = 1L;
 
    private Long id;
    private String code;
    private Date nextService;
    private Integer status;
    private Integer deleted;
    private DeviceMaster device;
    private ServiceType serviceType;
    private UserRef responsible;
    private Set<ServiceMaster> services;

    public ScheduleMaster() {
    }

    @Id
    @Basic
    @Column(name = "service_schedule_id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Temporal(TemporalType.DATE)
    @Column(name = "next_service")
    public Date getNextService() {
        return nextService;
    }

    public void setNextService(Date nextService) {
        this.nextService = nextService;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @ManyToOne
    @JoinColumn(name = "device_id", insertable = false, updatable = false, nullable = false)
    public DeviceMaster getDevice() {
        return device;
    }

    public void setDevice(DeviceMaster device) {
        this.device = device;
    }

    @OneToOne    
    @JoinColumn(referencedColumnName = "service_type_id", name = "service_type_id", insertable = false, updatable = false)
    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "responsible_id", insertable = false, updatable = false)
    public UserRef getResponsible() {
        return responsible;
    }

    public void setResponsible(UserRef responsible) {
        this.responsible = responsible;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "schedule", fetch = FetchType.EAGER)
    public Set<ServiceMaster> getServices() {
        return services;
    }

    public void setServices(Set<ServiceMaster> services) {
        this.services = services;
    }
    
}
