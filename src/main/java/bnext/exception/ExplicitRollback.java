
package bnext.exception;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class ExplicitRollback extends RuntimeException {

    public ExplicitRollback() {
    }

    public ExplicitRollback(String message) {
        super(message);
    }

    public ExplicitRollback(String message, Throwable cause) {
        super(message, cause);
    }

    public ExplicitRollback(Throwable cause) {
        super(cause);
    }

    public ExplicitRollback(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
