package bnext.resources;

import Framework.Config.Utilities;
import bnext.util.PwaHelper;
import com.google.common.base.Objects;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.google.common.io.ByteSource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.Map;
import javax.annotation.Nonnull;
import mx.bnext.core.util.Loggable;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import qms.framework.util.AboutApp;
import qms.framework.util.ExceptionUtils;

@Service
public class PwaService implements ResourceLoaderAware {

    private static final Logger LOGGER = Loggable.getLogger(PwaService.class);
    private static final String DEV_APP_QMS_DEFAULT_LOCATION = "/app/qms/";

    private ResourceLoader resourceLoader;

    private String sha1(final Path source) throws IOException {
        final ByteSource sourceBytes = com.google.common.io.Files.asByteSource(source.toFile());
        // Sha1 definido por angular
        final HashCode hash = sourceBytes.hash(Hashing.sha1());
        return hash.toString();
    }

    public synchronized boolean overridePwaManifest(
            final String systemId,
            final String systemUrl,
            final String systemColor,
            final String lang
    ) {
        try {
            final Path pwaPath = getResource(PwaHelper.MANIFEST_FILE_NAME);
            if (pwaPath == null) {
                LOGGER.error("Can not override PWA manifest {} at it does not exist.", PwaHelper.MANIFEST_FILE_NAME);
                return false;
            }
            final String oldManifest = new String(Files.readAllBytes(pwaPath), StandardCharsets.UTF_8);
            final Map<String, Object> manifest = (Map<String, Object>) Utilities.parse(oldManifest);
            if (manifest == null) {
                LOGGER.error("PWA manifest is not valid. {}", oldManifest);
                return false;
            }
            manifest.put("name", "Bnext QMS para " + systemId);
            manifest.put("short_name", systemId);
            manifest.put("theme_color", systemColor);
            manifest.put("id", systemUrl);
            manifest.put("lang", lang);
            String newManifest = Utilities.getSerializedObj(manifest);
            newManifest = newManifest.replaceAll("\\\\/", "/");
            if (Files.isWritable(pwaPath) && Files.isReadable(pwaPath)) {
                Files.delete(pwaPath);
                FileUtils.writeStringToFile(pwaPath.toFile(), newManifest, "UTF-8");
                return true;
            } else {
                LOGGER.error("PWA manifest is not writable {}", pwaPath);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("Unable to update PWA manifest. {}", e.getMessage());
            return false;
        }
    }

    public synchronized boolean overrideNsgw(final String appName) {
        try {
            final Path nsgwPath = getResource(PwaHelper.NGSW_FILE_NAME);
            if (nsgwPath == null || !Files.exists(nsgwPath)) {
                LOGGER.error("PWA manifest {} does not exist.", PwaHelper.NGSW_FILE_NAME);
                return false;
            }
            final String oldManifest = new String(Files.readAllBytes(nsgwPath), StandardCharsets.UTF_8);
            final Map<String, Object> manifest = (Map<String, Object>) Utilities.parse(oldManifest);
            if (manifest == null) {
                LOGGER.error("ngsw file is not valid. {}", oldManifest);
                return false;
            }
            final Map<String, String> hashTable = (Map<String, String>) manifest.get("hashTable");
            final boolean notModified = hashTable.keySet().stream().allMatch((key) -> {
                String fileName;
                String appTarget = "/" + appName + "/qms/";
                if (key.contains(appTarget)) {
                    fileName = key.replace(appTarget, "");
                } else if (key.contains(DEV_APP_QMS_DEFAULT_LOCATION)) {
                    fileName = key.replace("/app/qms/", "");
                } else {
                    LOGGER.error("Failed to override file. Invalid key: {}", key);
                    return true;
                }
                try {
                    final Path path = getResource(fileName);
                    if (path == null) {
                        LOGGER.error("Failed to override file. Not found: {}", fileName);
                        return true;
                    }
                    final String sha1 = sha1(path);
                    if (!Objects.equal(hashTable.get(key), sha1)) {
                        hashTable.put(key, sha1);
                        return false;
                    }
                    return true;
                } catch (final IOException ex) {
                    LOGGER.error("Unable to generate sha1 for file {}", fileName, ex);
                    return true;
                }
            });
            manifest.put("timestamp", new Date().getTime());
            if (notModified) {
                return true;
            }
            String newNsgw = Utilities.getSerializedObj(manifest);
            newNsgw = newNsgw.replaceAll("\\\\/", "/");
            if (Files.isWritable(nsgwPath) && Files.isReadable(nsgwPath)) {
                Files.delete(nsgwPath);
                FileUtils.writeStringToFile(nsgwPath.toFile(), newNsgw, "UTF-8");
                final String buildAngular = sha1(nsgwPath);
                AboutApp.setBuildAngularVersion(buildAngular);
                return true;
            } else {
                LOGGER.error("ngsw file is not writable.");
                return false;
            }
        } catch (Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace("Unable to update ngsw file. {}", message, e);
            } else {
                LOGGER.error("Unable to update ngsw file. {}", message);
            }
            return false;
        }
    }

    private Path getResource(final String file) {
        try {
            return Paths.get(resourceLoader.getResource("/qms/" + file).getURI());
        } catch (IOException ex) {
            if (LOGGER.isTraceEnabled()) {
                final String message = qms.framework.util.ExceptionUtils.getRootCauseMessage(ex);
                LOGGER.error(
                        "Failed to load ngapp file {}. Error: {}",
                        file, message
                );
            }
            return null;
        }
    }

    @Override
    public void setResourceLoader(@Nonnull ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
        


}
