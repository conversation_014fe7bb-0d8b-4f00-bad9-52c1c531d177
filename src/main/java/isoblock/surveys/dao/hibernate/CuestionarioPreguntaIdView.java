package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Id;
import jakarta.persistence.Table;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "CUESTIONARIO_PREGUNTA_ID")
public class CuestionarioPreguntaIdView extends DomainObject implements Serializable {
    
    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    
    private Long surveyId;
    private Long titleId;
    private Integer orden;
    private String descripcion;

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }



    @Basic
    @Column(name = "pregunta")
    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    @Basic
    @Column(name = "orden")
    public Integer getOrden() {
        return orden;
    }

    public void setOrden(Integer orden) {
        this.orden = orden;
    }


    @Basic
    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Basic
    @Column(name = "title_id")
    public Long getTitleId() {
        return titleId;
    }

    public void setTitleId(Long titleId) {
        this.titleId = titleId;
    }

 

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof CuestionarioPreguntaIdView)) {
            return false;
        }
        CuestionarioPreguntaIdView other = (CuestionarioPreguntaIdView) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.CuestionarioPreguntaView[ id=" + id + " ]";
    }
}
