
package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import qms.form.util.SurveyUtil;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "SURVEY_MATRIX_OPTION")
public class SurveyMatrixOptions extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    
    private List<SurveyTextItem> title;
    private String ponderacion;
    
    public SurveyMatrixOptions() {
    }

    public SurveyMatrixOptions(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "MATRIX_OPTION_ID", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return Esta ponderacion debe tener un valor difernete de nulo unicamente cuando se trate de preguntas tipo:
     *          - Matriz menu
     */
    @Column(name = "VCH_PONDERACION")
    public String getPonderacion() {
        return ponderacion;
    }

    public void setPonderacion(String ponderacion) {
        this.ponderacion = ponderacion;
    }

    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_MATRIX_OPTION_ITEM", joinColumns = {
        @JoinColumn(name = "MATRIX_OPTION_ID", referencedColumnName = "MATRIX_OPTION_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TEXT_ITEM_ID", referencedColumnName = "TEXT_ITEM_ID", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<SurveyTextItem> getTitle() {
        return title;
    }

    public void setTitle(List<SurveyTextItem> title) {
        this.title = title;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SurveyMatrixOptions)) {
            return false;
        }
        SurveyMatrixOptions other = (SurveyMatrixOptions) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.SurveyMatrixOptions[ id=" + id + " ]";
    }
    
    public String zeroLabel(){
        return SurveyUtil.getZeroLabel(title);
    }
}
