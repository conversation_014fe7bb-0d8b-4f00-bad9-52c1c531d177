package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import bnext.reference.BusinessUnitRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import qms.form.util.ISurveyObject;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "SURVEY_GLOBAL_OBJECT")
public class SurveyGlobalObject extends DomainObject implements Serializable, ISurveyObject {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    
    private Integer currentId;
    private Integer questions;
    private Short multiPage;
    private Short multiLang;
    private Short fileUpload;
    private Short mapSelect;
    private Short videoField;
    private Short imageField;
    private Short rol_si;
    private Short rol_no;
    private Short hasWeight;
    private List<BusinessUnitRef> unes;
    private List<TypeSurveyed> esquemaTienda;
    private List<SurveyTextItem> pages;
    private List<SurveyTextItem> languages;
    private List<SurveyTextItem> title;
    private boolean editableTitle;
    
    public SurveyGlobalObject() {
    }

    public SurveyGlobalObject(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "GLOBAL_OBJECT_ID", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_GLOBAL_PAGES", joinColumns = {
        @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TEXT_ITEM_ID", referencedColumnName = "TEXT_ITEM_ID", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<SurveyTextItem> getPages() {
        return pages;
    }

    public void setPages(List<SurveyTextItem> pages) {
        this.pages = pages;
    }

    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_GLOBAL_LANGUAGE", joinColumns = {
        @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TEXT_ITEM_ID", referencedColumnName = "TEXT_ITEM_ID", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<SurveyTextItem> getLanguages() {
        return languages;
    }

    public void setLanguages(List<SurveyTextItem> languages) {
        this.languages = languages;
    }

    @Override
    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_GLOBAL_TITLE", joinColumns = {
        @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TEXT_ITEM_ID", referencedColumnName = "TEXT_ITEM_ID", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<SurveyTextItem> getTitle() {
        return title;
    }

    public void setTitle(List<SurveyTextItem> title) {
        this.title = title;
    }
    
    @Column(name = "INT_CURRENT_ID")
    public Integer getCurrentId() {
        return currentId;
    }

    public void setCurrentId(Integer currentId) {
        this.currentId = currentId;
    }

    @Column(name = "INT_QUESTIONS")
    public Integer getQuestions() {
        return questions;
    }

    public void setQuestions(Integer questions) {
        this.questions = questions;
    }

    @Column(name = "INT_B_MULTIPAGE")
    public Short getMultiPage() {
        return multiPage;
    }

    public void setMultiPage(Short multiPage) {
        this.multiPage = multiPage;
    }

    @Column(name = "INT_B_MULTILANG")
    public Short getMultiLang() {
        return multiLang;
    }

    public void setMultiLang(Short multiLang) {
        this.multiLang = multiLang;
    }

    @Column(name = "INT_B_FILE_UPLOAD")
    public Short getFileUpload() {
        return fileUpload;
    }

    public void setFileUpload(Short fileUpload) {
        this.fileUpload = fileUpload;
    }

    @Column(name = "INT_B_MAP_SELECT")
    public Short getMapSelect() {
        return mapSelect;
    }

    public void setMapSelect(Short mapSelect) {
        this.mapSelect = mapSelect;
    }

    @Column(name = "INT_B_VIDEO_FIELD")
    public Short getVideoField() {
        return videoField;
    }

    public void setVideoField(Short videoField) {
        this.videoField = videoField;
    }

    @Column(name = "INT_B_IMAGE_FIELD")
    public Short getImageField() {
        return imageField;
    }

    public void setImageField(Short imageField) {
        this.imageField = imageField;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }



    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_GLOBAL_OBJECT_TYP_ENC", joinColumns = {
        @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TYPE_SURVEYED_ID", referencedColumnName = "TYPE_SURVEYED_ID", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE) 
    public List<TypeSurveyed> getEsquemaTienda() {
        return esquemaTienda;
    }

    public void setEsquemaTienda(List<TypeSurveyed> esquemaTienda) {
        this.esquemaTienda = esquemaTienda;
    }
    
    @Column(name = "INT_B_ROL_SI")
    public Short getRol_si() {
        return rol_si;
    }

    public void setRol_si(Short rol_si) {
        this.rol_si = rol_si;
    }
    @Column(name = "INT_B_ROL_NO")
    public Short getRol_no() {
        return rol_no;
    }

    public void setRol_no(Short rol_no) {
        this.rol_no = rol_no;
    }

    @Column(name = "INT_B_HAS_WEIGHT")
    public Short getHasWeight() {
        return hasWeight;
    }

    public void setHasWeight(Short hasWeight) {
        this.hasWeight = hasWeight;
    }

    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_GLOBAL_OBJECT_BU", joinColumns = {
        @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "BUSINESS_UNIT_ID", referencedColumnName = "business_unit_id", nullable = false)})
    @ManyToMany
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<BusinessUnitRef> getUnes() {
        return unes;
    }

    public void setUnes(List<BusinessUnitRef> unes) {
        this.unes = unes;
    }

    @Column(name = "editable_title")
    public boolean isEditableTitle() {
        return editableTitle;
    }

    public void setEditableTitle(boolean editableTitle) {
        this.editableTitle = editableTitle;
    }
    

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SurveyGlobalObject)) {
            return false;
        }
        SurveyGlobalObject other = (SurveyGlobalObject) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.SurveyGlobalObject[ id=" + id + " ]";
    }
}
