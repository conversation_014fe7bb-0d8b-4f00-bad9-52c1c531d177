package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Convert;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.springframework.data.annotation.CreatedDate;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "OUTSTANDING_SURVEYS_ERROR_LOG")
public class OutstandingSurveysErrorLog extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Date createdDate;
    private Long outstandingSurveyId;
    private String error;
    private Boolean errorMailSend;
    private Date errorMailSendDate;
    private String errorMailSendTo;


    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "id", nullable = false, precision = 19)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Basic
    @Column(name = "outstanding_survey_id", nullable = false)
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }
    
    @Basic
    @Column(name = "error")
    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Column(name = "error_mail_send")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getErrorMailSend() {
        return errorMailSend;
    }

    public void setErrorMailSend(Boolean errorMailSend) {
        this.errorMailSend = errorMailSend;
    }

    @Column(name = "error_mail_send_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getErrorMailSendDate() {
        return errorMailSendDate;
    }

    public void setErrorMailSendDate(Date errorMailSendDate) {
        this.errorMailSendDate = errorMailSendDate;
    }

    @Column(name = "error_mail_send_to")
    public String getErrorMailSendTo() {
        return errorMailSendTo;
    }

    public void setErrorMailSendTo(String errorMailSendTo) {
        this.errorMailSendTo = errorMailSendTo;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof OutstandingSurveysErrorLog)) {
            return false;
        }
        OutstandingSurveysErrorLog other = (OutstandingSurveysErrorLog) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.OutstandingSurveysErrorLog[ id=" + id + " ]";
    }
}
