
package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import isoblock.surveys.dao.interfaces.IOutstandingQuestion;
import java.util.Date;
import java.util.List;
import qms.form.interfaces.IArchivedOutstandingSurveys;
import qms.form.interfaces.IDeletedOutstandingSurveys;
import qms.util.interfaces.ICommonEntity;
import qms.util.interfaces.IPersistableCode;

/**
 *
 * <AUTHOR> @Block Networks
 */
public interface IOutstandingSurveys extends ICommonEntity, IArchivedOutstandingSurveys, IPersistableCode, IDeletedOutstandingSurveys {

    Long getSurveyId();

    Long getRequestId();

    Long getDocumentId();

    Date getDteFechaInicio();

    Long getBusinessUnitDepartmentId();

    Long getOutstandingSurveyId();

    String getSurveyRequestMode();

    String getConditionalValidatorCacheId();

    String getEditedOutstandingidTitle();

    DomainObject getCuestionario();

    Boolean isSignatureAsSaveBehavior();

    Short getEstatus();

    Integer getSaveAction();

    Long getNextUserToFill();

    List<? extends IOutstandingQuestion> getPreguntasRespondidas();

    Long getProgressStateId();
}
