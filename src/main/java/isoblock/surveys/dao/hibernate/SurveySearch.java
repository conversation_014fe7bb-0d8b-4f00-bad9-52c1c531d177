package isoblock.surveys.dao.hibernate;

import DPMS.Mapping.BusinessUnitLite;
import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Immutable;
import qms.util.interfaces.IPersistableDescription;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "SURVEY_SEARCH")
@Immutable
public class SurveySearch extends DomainObject implements Serializable, IPersistableDescription {

    private static final long serialVersionUID = 1L;
    
    private Integer estatus;
    private String vchTexto;
    private String plainText;
    private String description;
    private String type;
    private Integer intQuestions;
    private Date dteCreacion;
    private Set<BusinessUnitLite> unes;
    private Long globalObjId;
    private String code = "";
    private Long authorId;
    private Short rolSi;
    private Short rolNo;
    private Long requestId;
    private String answersTable;
    
    public SurveySearch() {
    }
    
    public SurveySearch(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "SURVEY_ID")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "DTE_CREATED")
    @Temporal(jakarta.persistence.TemporalType.DATE)
    public Date getDteCreacion() {
        return dteCreacion;
    }

    public void setDteCreacion(Date dteCreacion) {
        this.dteCreacion = dteCreacion;
    }
    @Basic(optional = false)
    @Column(name = "STATUS")
    public Integer getEstatus() {
        return estatus;
    }

    public void setEstatus(Integer estatus) {
        this.estatus = estatus;
    }
      
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "SURVEY_GLOBAL_OBJECT_BU", 
        joinColumns = { @JoinColumn(name = "GLOBAL_OBJECT_ID", referencedColumnName = "GLOBAL_OBJECT_ID")}, 
        inverseJoinColumns = { @JoinColumn(name = "BUSINESS_UNIT_ID", referencedColumnName = "business_unit_id")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitLite> getUnes() {
        return unes;
    }

    public void setUnes(Set<BusinessUnitLite> unes) {
        this.unes = unes;
    }

    @Column(name = "CODE")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "GLOBAL_OBJECT_ID")
    public Long getGlobalObjId() {
        return globalObjId;
    }

    public void setGlobalObjId(Long globalObjId) {
        this.globalObjId = globalObjId;
    }
    
    @Column(name = "ROL_NO")
    public Short getRolNo() {
        return rolNo;
    }

    public void setRolNo(Short rolNo) {
        this.rolNo = rolNo;
    }

    @Column(name = "ROL_SI")
    public Short getRolSi() {
        return rolSi;
    }

    public void setRolSi(Short rolSi) {
        this.rolSi = rolSi;
    }
    
    @Column(name = "vch_texto")
    public String getVchTexto() {
        return vchTexto;
    }

    public void setVchTexto(String vchTexto) {
        this.vchTexto = vchTexto;
    }

    @Column(name = "plain_title")
    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }
    
    @Column(name = "plain_title", updatable = false, insertable = false)
    @Override
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Column(name = "INT_QUESTIONS")
    public Integer getIntQuestions() {
        return intQuestions;
    }

    public void setIntQuestions(Integer intQuestions) {
        this.intQuestions = intQuestions;
    }

    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    @Column(name = "user_id")
    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }
    
    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "answers_table")
    public String getAnswersTable() {
        return answersTable;
    }

    public void setAnswersTable(String answersTable) {
        this.answersTable = answersTable;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveySearch other = (SurveySearch) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "SurveySearch{ id=" + id +  ",estatus=" + estatus + ", code=" + code + '}';
    }
}
