package isoblock.surveys.dao.hibernate;
import Framework.Config.DomainObject;
import qms.framework.util.CacheConstants;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;
/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name="TYPE_SURVEYED")
public class TypeSurveyed extends DomainObject implements java.io.Serializable {
    
    private String clave;    
    private String descripcion;
    private Integer estatus;

    public TypeSurveyed() {
    }

    public TypeSurveyed(Long id) {
        this.id = id;
    }
    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name="TYPE_SURVEYED_ID", unique=true, nullable=false)
    public Long getId(){
        return this.id;
    }
    
    @Override
    public void setId(Long id){
        this.id=id;
    }
    
    @Basic
    @Column(name="DESCRIPTION", length=50, unique=true)
    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String titulo) {
        this.descripcion = titulo;
    }

    @Basic
    @Column(name="CODE", length=50, unique=true)
    public String getClave() {
        return clave;
    }

    public void setClave(String clave) {
        this.clave = clave;
    }

    @Basic
    @Column(name="STATUS")
    public Integer getEstatus() {
        return estatus;
    }

    public void setEstatus(Integer estatus) {
        this.estatus = estatus;
    }

}

