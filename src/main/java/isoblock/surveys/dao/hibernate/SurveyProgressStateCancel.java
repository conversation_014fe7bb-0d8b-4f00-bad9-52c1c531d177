package isoblock.surveys.dao.hibernate;

import jakarta.annotation.Nonnull;
import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Cacheable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.form.util.ISurveyProgressStateCancel;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_progress_state_cancel")
public class SurveyProgressStateCancel extends CompositeStandardEntity<SurveyProgressStateCancelPK> 
        implements Serializable, ILinkedComposityGrid<SurveyProgressStateCancelPK>,
        ISurveyProgressStateCancel, Comparable<SurveyProgressStateCancel> {

    private static final long serialVersionUID = 1L;

    private SurveyProgressStateCancelPK id;
    private SurveyFieldObject fieldObject;

    public SurveyProgressStateCancel() {
    }

    public SurveyProgressStateCancel(SurveyProgressStateCancelPK id) {
        this.id = id;
    }

    public SurveyProgressStateCancel(Long fieldObjectId, Long formProgressStateId) {
        this.id = new SurveyProgressStateCancelPK(fieldObjectId, formProgressStateId);
    }

    @Override
    public SurveyProgressStateCancelPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public SurveyProgressStateCancelPK getId() {
        return id;
    }

    @Override
    public void setId(SurveyProgressStateCancelPK id) {
        this.id = id;
    }

    @JoinColumn(name = "field_object_id", referencedColumnName = "field_object_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public SurveyFieldObject getFieldObject() {
        return fieldObject;
    }

    public void setFieldObject(SurveyFieldObject fieldObject) {
        this.fieldObject = fieldObject;
    }

    @Override
    public int compareTo(@Nonnull SurveyProgressStateCancel other) {
        if (this.getId() == null) {
            return -1;
        }
        return this.getId().compareTo(other.getId());
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyProgressStateCancel other = (SurveyProgressStateCancel) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "SurveyProgressStateCancel{" + "id=" + id + '}';
    }


}