package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "SURVEY_SEARCH")
@Immutable
public class SurveySearchRef extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String code;
    private String description;
    private String answersTable;
    private String plainText;
    
    
    public SurveySearchRef() {
    }
    
    public SurveySearchRef(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "SURVEY_ID")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    
    @Column(name = "VCH_PLAINTEXTO")
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "answers_table")
    public String getAnswersTable() {
        return answersTable;
    }
  
    public void setAnswersTable(String answersTable) {
        this.answersTable = answersTable;
    }
  
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SurveySearchRef)) {
            return false;
        }
        SurveySearchRef other = (SurveySearchRef) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "SurveySearchRef[ id=" + id + " ]";
    }
    
    @Column(name = "plain_title")
    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }
}
