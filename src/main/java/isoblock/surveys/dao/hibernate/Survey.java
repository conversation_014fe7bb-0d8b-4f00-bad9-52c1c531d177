package isoblock.surveys.dao.hibernate;

import DPMS.Mapping.Request;
import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.hibernate.annotations.Type;
import qms.document.entity.RequestRef;
import qms.document.entity.SurveyTheme;
import qms.form.util.ISurvey;
import qms.framework.util.CacheConstants;

import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "SURVEY")
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@XmlRootElement
public class Survey extends DomainObject implements Serializable, ISurvey {

    public enum SURVEY_TYPE {
        POLL(TYPE_POLL), AUDIT(TYPE_AUDIT), REQUEST(TYPE_REQUEST);
        private final String type;
        private SURVEY_TYPE (String type) {
            this.type = type;            
        }
        private String getType () {
            return this.type;
        }
        public String getValue () {
            return this.type;
        }
        public static SURVEY_TYPE getSurveyType(String value) {
            for (SURVEY_TYPE stype : SURVEY_TYPE.values()) {
                if(stype.getType().equals(value)) {
                    return stype;
                }
            }
            return null;
        }
    }
    
    private static final long serialVersionUID = 1L;
    public static final Integer ESTATUS_INACTIVO = 0;
    public static final Integer ESTATUS_ACTIVO = 1;
    public static final Integer ESTATUS_BLOQUEADO = 2;
    public static final String TYPE_POLL = "poll";
    public static final String TYPE_AUDIT = "audit";
    public static final String TYPE_REQUEST = "request";    // <-- formularios

    private Boolean signRejectApproval = false;             // <-- Al estar en TRUE, la funcionalidad de "RETORNAR" al firmar no se inicía hasta que el AUTORIZADOR DE RETORNO autoriza
    private Long addAttachmentTotalLimit;                   // <-- bytes
    private Long authorId;
    private String type;
    private String code;
    private String answersTable;
    private String mailsToOnCompletedFillForm;
    private String mailsSubjectOnCompletedFill;
    private Integer isAddAttachmentEnabled = 0;
    private Integer isMailIncludeAnswers = 0;
    private Integer addFindingsEnabled = 0;
    private Integer addActivitiesEnabled = 0;
    private Integer estatus;
    private Integer hasAnswers;
    private Integer isFillRequestAvailable;
    private Integer isTemplateUseAvailable;
    private Integer isFreezed = 0;
    private SurveyGlobal globals;
    private RequestRef request;
    private Request requestFull;
    private List<SurveyField> fields;
    private transient Map<String,Object> config;
    private SurveyTheme surveyTheme;
    private Boolean computeOptionalCodeColumns = false;

    public Survey() {
    }

    public Survey(Long id) {
        this.id = id;
    }

    public Survey(
            Long id,
            Boolean signRejectApproval,
            Long addAttachmentTotalLimit,
            Long authorId,
            String type,
            String code,
            String answersTable,
            String mailsToOnCompletedFillForm,
            String mailsSubjectOnCompletedFill,
            Integer isAddAttachmentEnabled,
            Integer isMailIncludeAnswers,
            Integer addFindingsEnabled,
            Integer addActivitiesEnabled,
            Integer estatus,
            Integer hasAnswers,
            Integer isFillRequestAvailable,
            Integer isTemplateUseAvailable,
            Integer isFreezed
    ) {
        this.id = id;
        this.signRejectApproval = signRejectApproval;
        this.addAttachmentTotalLimit = addAttachmentTotalLimit;
        this.authorId = authorId;
        this.type = type;
        this.code = code;
        this.answersTable = answersTable;
        this.mailsToOnCompletedFillForm = mailsToOnCompletedFillForm;
        this.mailsSubjectOnCompletedFill = mailsSubjectOnCompletedFill;
        this.isAddAttachmentEnabled = isAddAttachmentEnabled;
        this.isMailIncludeAnswers = isMailIncludeAnswers;
        this.addFindingsEnabled = addFindingsEnabled;
        this.addActivitiesEnabled = addActivitiesEnabled;
        this.estatus = estatus;
        this.hasAnswers = hasAnswers;
        this.isFillRequestAvailable = isFillRequestAvailable;
        this.isTemplateUseAvailable = isTemplateUseAvailable;
        this.isFreezed = isFreezed;
        this.globals = globals;
        this.request = request;
        this.requestFull = requestFull;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "SURVEY_ID", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "INT_B_HAS_ANSWER")
    public Integer getHasAnswers() {
        return hasAnswers;
    }

    public void setHasAnswers(Integer hasAnswers) {
        if (hasAnswers == null) {
            hasAnswers = 0;
        }
        this.hasAnswers = hasAnswers;
    }

    @JoinColumn(name = "REQUEST_ID", referencedColumnName = "ID")
    @ManyToOne
    @JsonIgnore
    @LazyCollection(LazyCollectionOption.FALSE)
    public RequestRef getRequest() {
        return request;
    }

    public void setRequest(RequestRef request) {
        this.request = request;
    }
    
    @JsonIgnore
    @Transient
    public Request getRequestFull() {
        return requestFull;
    }
    
    @JoinColumn(name = "theme_id", referencedColumnName = "theme_id", nullable = false)
    @ManyToOne
    @LazyCollection(LazyCollectionOption.FALSE)
    public SurveyTheme getSurveyTheme() {
        return surveyTheme;
    }

    public void setRequestFull(Request requestFull) {
        this.requestFull = requestFull; 
    }

    public void setSurveyTheme(SurveyTheme surveyTheme) {
        this.surveyTheme = surveyTheme;
    }

    @Column(name = "STATUS")
    public Integer getEstatus() {
        return estatus;
    }

    public void setEstatus(Integer estatus) {
        this.estatus = estatus;
    }

    @Override
    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "SURVEY_RELATED_FIELDS",
            joinColumns = {
                @JoinColumn(name = "SURVEY_ID", referencedColumnName = "SURVEY_ID", nullable = false)
            },
            inverseJoinColumns = {
                @JoinColumn(name = "FIELD_ID", referencedColumnName = "FIELD_ID", nullable = false)
            }
    )
    @ManyToMany
    @JsonIgnore
    @Fetch(value = FetchMode.SUBSELECT)
    @LazyCollection(LazyCollectionOption.FALSE)
    public List<SurveyField> getFields() {
        return fields;
    }

    public void setFields(List<SurveyField> fields) {
        this.fields = fields;
    }
    
    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinColumn(name = "GLOBAL_ID", referencedColumnName = "GLOBAL_ID")
    @ManyToOne
    @JsonIgnore
    @LazyCollection(LazyCollectionOption.FALSE)
    public SurveyGlobal getGlobals() {
        return globals;
    }

    public void setGlobals(SurveyGlobal globals) {
        this.globals = globals;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }
    
    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    @Column(name = "vch_code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Survey)) {
            return false;
        }
        Survey other = (Survey) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.Survey[ id=" + id + " ]";
    }
    
    @Column(name = "user_id")
    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    @Column(name = "is_fill_request_available")
    public Integer getIsFillRequestAvailable() {
        return isFillRequestAvailable;
    }

    public void setIsFillRequestAvailable(Integer isFillRequestAvailable) {
        this.isFillRequestAvailable = isFillRequestAvailable;
    }

    @Column(name = "is_template_use_available")
    public Integer getIsTemplateUseAvailable() {
        return isTemplateUseAvailable;
    }

    public void setIsTemplateUseAvailable(Integer isTemplateUseAvailable) {
        this.isTemplateUseAvailable = isTemplateUseAvailable;
    }
    
    @JsonIgnore
    public Map configGet() {
        if (config == null) {
            config = new HashMap<>();
        }
        return config;
    }

    public void configSet(Map config) {
        this.config = config;
    }

    @Column(name = "freezed")
    public Integer getIsFreezed() {
        return isFreezed;
    }

    public void setIsFreezed(Integer isFreezed) {
        this.isFreezed = isFreezed;
    }

    @Column(name = "answers_table")
    public String getAnswersTable() {
        return answersTable;
    }

    public void setAnswersTable(String answersTable) {
        this.answersTable = answersTable;
    }
    
    @Column(name = "mails_to_oncompletedfillform")
    public String getMailsToOnCompletedFillForm() {
        return mailsToOnCompletedFillForm;
    }

    public void setMailsToOnCompletedFillForm(String mailsToOnCompletedFillForm) {
        this.mailsToOnCompletedFillForm = mailsToOnCompletedFillForm;
    }

    @Column(name = "add_attachment_enabled")
    public Integer getIsAddAttachmentEnabled() {
        return isAddAttachmentEnabled;
    }

    public void setIsAddAttachmentEnabled(Integer isAddAttachmentEnabled) {
        this.isAddAttachmentEnabled = isAddAttachmentEnabled;
    }

    @Column(name = "mail_include_answers")
    public Integer getIsMailIncludeAnswers() {
        return isMailIncludeAnswers;
    }

    public void setIsMailIncludeAnswers(Integer isMailIncludeAnswers) {
        this.isMailIncludeAnswers = isMailIncludeAnswers;
    }

    @Column(name = "add_findings_enabled")
    public Integer getAddFindingsEnabled() {
        return addFindingsEnabled;
    }

    public void setAddFindingsEnabled(Integer addFindingsEnabled) {
        this.addFindingsEnabled = addFindingsEnabled;
    }

    @Column(name = "add_activities_enabled")
    public Integer getAddActivitiesEnabled() {
        return addActivitiesEnabled;
    }

    public void setAddActivitiesEnabled(Integer addActivitiesEnabled) {
        this.addActivitiesEnabled = addActivitiesEnabled;
    }

    @Column(name = "add_attachment_total_limit")
    public Long getAddAttachmentTotalLimit() {
        return addAttachmentTotalLimit;
    }

    public void setAddAttachmentTotalLimit(Long addAttachmentTotalLimit) {
        this.addAttachmentTotalLimit = addAttachmentTotalLimit;
    }
    
    @Column(name = "mails_subject_oncompletedfill")
    public String getMailsSubjectOnCompletedFill() {
        return mailsSubjectOnCompletedFill;
    }

    public void setMailsSubjectOnCompletedFill(String mailsSubjectOnCompletedFill) {
        this.mailsSubjectOnCompletedFill = mailsSubjectOnCompletedFill;
    }

    @Column(name = "sign_reject_approval")
    @Type(type = "numeric_boolean")
    public Boolean getSignRejectApproval() {
        return signRejectApproval;
    }

    public void setSignRejectApproval(Boolean signRejectApproval) {
        this.signRejectApproval = signRejectApproval;
    }

    @Column(name = "compute_optional_code_columns")
    @Type(type = "numeric_boolean")
    public Boolean getComputeOptionalCodeColumns() {
        return computeOptionalCodeColumns;
    }

    public void setComputeOptionalCodeColumns(Boolean computeOptionalCodeColumns) {
        this.computeOptionalCodeColumns = computeOptionalCodeColumns;
    }
}
