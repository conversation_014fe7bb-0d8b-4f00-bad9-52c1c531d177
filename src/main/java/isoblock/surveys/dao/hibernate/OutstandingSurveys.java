package isoblock.surveys.dao.hibernate;

import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import ape.pending.core.BaseAPE;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.StrongBaseAPE;
import bnext.aspect.IExcludeLogging;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.document.entity.RequestRef;
import qms.form.entity.FormRequest;
import qms.form.entity.OutstandingSurveyBase;
import qms.form.entity.OutstandingSurveysActivity;
import qms.form.interfaces.IArchivedOutstandingSurveys;
import qms.form.interfaces.IDeletedOutstandingSurveys;
import qms.util.interfaces.IAreaId;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitId;

/**
 *
 * <AUTHOR> Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "OUTSTANDING_SURVEYS")
@RootSoftAPE( 
    mappedBy = "id.outstandingSurveysId",
    mappedByClass = OutstandingSurveysActivity.class
)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class OutstandingSurveys extends OutstandingSurveyBase implements
        StrongBaseAPE, Serializable, BaseAPE,
        IOutstandingSurveys, IExcludeLogging,
        IBusinessUnitId, IBusinessUnitDepartmentId,
        IArchivedOutstandingSurveys, IDeletedOutstandingSurveys,
        IAreaId, IAuditable
{

    public static final short ESTATUS_BORRADOR = 0;
    public static final short ESTATUS_PROGRAMADA = 1;   // <-- No se usa
    public static final short ESTATUS_EN_PROCESO = 2;
    public static final short ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA = 3;
    public static final short ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE = 4;
    public static final short ESTATUS_CERRADA = 5;
    public static final short ESTATUS_CANCELADA = 6;
    public static final short ESTATUS_INCONCLUSA = 7;           // <-- Se llega a este estatus cuando se cierra una ENCUESTA antes de ser respondida manualemtne
    public static final short ESTATUS_RECHAZADO = 8;            // <-- Se llega a este estatus cuando se rechaza una SECCIÓN desde una FIRMA ya que se genera una copia de OUTSTANDING_SURVEYS
    public static final short ESTATUS_NUEVO = 9;                // <-- Se usa cuando se inicia el llenado de un formulario, pero se sobre escribe por STAND_BY
    public static final short ESTATUS_EXPIRADO = 10;            // <-- Se usa cuando existe una configuración de `daysToExpire` y que se exeden los días configrados
    public static final short STATUS_RETURN_APPROVAL = 11;      // <-- Se usa cuando la opción `Survey.signRejectApproval` está en `true`, se marca en 11 mientras que el registro pasa por el flujo de autorización
    public static final short STATUS_ARCHIVED = 12;             // <-- Se usa cuando solo hay infomración en OUTSTANDING_SURVEYS y su tabla de respuestas SURVEY_ANSWER_
    public static final short STATUS_CANCEL_APPROVAL = 13;      // <-- Se usa cuando se está en autorización la cancelación, se marca en 13 mientras que el registro pasa por el flujo de autorización
    
    public static enum STATUS implements IStatusEnum {
        STAND_BY(ESTATUS_BORRADOR, IStatusEnum.COLOR_RED),
        PROGRAMED(ESTATUS_PROGRAMADA, IStatusEnum.COLOR_YELLOW),
        IN_PROGRESS(ESTATUS_EN_PROCESO, IStatusEnum.COLOR_GREEN),
        IN_PROGRESS_FILLED_PARCIALLY(ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA, IStatusEnum.COLOR_GREEN),
        IN_PROGRESS_FILL_LATER(ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE, IStatusEnum.COLOR_GREEN),
        CLOSED(ESTATUS_CERRADA, IStatusEnum.COLOR_GRAY),
        CANCELLED(ESTATUS_CANCELADA, IStatusEnum.COLOR_BLACK),
        UNFINISHED(ESTATUS_INCONCLUSA, IStatusEnum.COLOR_DEEP_BLUE), // <-- Se utiliza solo en ENCUESTAS legacy
        REJECTED(ESTATUS_RECHAZADO, IStatusEnum.COLOR_BLUE),
        NEW(ESTATUS_NUEVO, null),
        EXPIRED(ESTATUS_EXPIRADO, IStatusEnum.COLOR_RED),
        ARCHIVED(STATUS_ARCHIVED, IStatusEnum.COLOR_GRAY), 
        RETURN_APPROVAL(STATUS_RETURN_APPROVAL, IStatusEnum.COLOR_RED),
        CANCEL_APPROVAL(STATUS_CANCEL_APPROVAL, IStatusEnum.COLOR_RED);
        private final Integer value;
        private final String gridCube;

        private STATUS(Short value, String gridCube) {
            this.value = value.intValue();
            this.gridCube = gridCube;
        }

        @Override
        public Integer getValue() {
            return this.value;
        }

        @Override
        public String getGridCube() {
            return this.gridCube;
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return IN_PROGRESS;
        }

        public String intValue() {
            return value.toString();
        }

        public boolean equals(String value) {
            return this.value.toString().equals(value);
        }

        public static STATUS fromValue(Integer value) {
            for (STATUS v : STATUS.values()) {
                if (Objects.equals(v.getValue(), value)) {
                    return v;
                }
            }
            return null;
        }

    }
    private static final long serialVersionUID = 1L;
    
    private Short estatus;
    private Short frozen;
    private Integer status;
    private Integer deleted = 0;
    private Long requestId;
    private Long documentId;
    private Long creatorUserId;
    private Long answersTableId;
    private Double score;
    private String code;
    private Date dteFechaInicio;
    private Date dteFechaCierre;
    private RequestRef request;
    private Survey cuestionario;
    private Long surveyId;
    private Long surveyAnswerMigrationId;
    private List<OutstandingSurveysAttendantLoad> questionAttendants;
    private List<OutstandingQuestion> preguntasRespondidas;
    private Long nextUserToFill;
    private Long progressStateId;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long areaId;
    
    private Long formReopenRequestId;
    private Long formCancelRequestId;
    private Long formAdjustRequestId;
    private FormRequest formReopenRequest;
    private FormRequest formCancelRequest;
    private FormRequest formAdjustRequest;

    private String stage;
    private Integer stageDaysToExpire;
    private Integer stageDaysToNotice;
    private Long stageLatestFillOutUserId;
    private Date stageLatestFillOut;
    private Date stageExpirationDate;
    private Date stageNoticeDate;
    private Date expiredDate;
    private Long surveyProgressStateCancelId;
    private Long surveyProgressStateReopenId;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private AutorizationPoolDetailComment autorizationPoolDetailComment;

    //Transient
    private boolean signatureAsSaveBehavior = false;//Transient
    private boolean createCopy = false;//Transient
    private String editedOutstandingidTitle;
    private Boolean archived = false;
    private Integer saveAction;

    private String surveyRequestMode;
    private String conditionalValidatorCacheId;

    public OutstandingSurveys() {
        this.deleted = 0;
    }

    public OutstandingSurveys(Survey cuestionario, Long userId) {
        this.id = -1L;
        this.estatus = ESTATUS_EN_PROCESO;
        this.cuestionario = cuestionario;
        this.deleted = 0;
        this.createdBy = userId;
        this.createdDate = new Date();
        this.lastModifiedBy = userId;
        this.lastModifiedDate = new Date();
    }

    public OutstandingSurveys(Long id) {
        this.id = id;
        this.deleted = 0;
    }

    public OutstandingSurveys(IOutstandingSurveys entity) {
        this.id = entity.getId();
        this.archived = entity.getArchived();
        this.businessUnitDepartmentId = entity.getBusinessUnitDepartmentId();
        this.code = entity.getCode();
        this.conditionalValidatorCacheId = entity.getConditionalValidatorCacheId();
        this.deleted = entity.getDeleted();
        this.documentId = entity.getDocumentId();
        this.editedOutstandingidTitle = entity.getEditedOutstandingidTitle();
        this.estatus = entity.getEstatus();
        this.nextUserToFill = entity.getNextUserToFill();
        this.progressStateId = entity.getProgressStateId();
        if (entity.getEstatus() != null) {
            this.status = entity.getEstatus().intValue();
        }
        this.surveyId = entity.getSurveyId();
        this.surveyRequestMode = entity.getSurveyRequestMode();
        this.requestId = entity.getRequestId();
        if (entity.getRequestId() != null) {
            this.request = new RequestRef(entity.getRequestId());
        }
        if (this.surveyId != null) {
            this.cuestionario = new Survey(this.surveyId);
        }
        this.preguntasRespondidas = new ArrayList<>();
        if (entity.getPreguntasRespondidas() != null) {
            entity.getPreguntasRespondidas().forEach((pregunta) -> {
                this.preguntasRespondidas.add(new OutstandingQuestion(pregunta));
            });
        }
        //this.answersTableId = entity.getAnswersTableId();
        //this.areaId = entity.getAreaId();
        //this.businessUnitId = entity.getBusinessUnitId();
        //this.createCopy = entity.getCreateCopy();
        //this.createdBy = entity.getCreatedBy();
        //this.createdDate = entity.getCreatedDate();
        //this.creatorUserId = entity.getCreatorUserId();
        //this.dteFechaCierre = entity.getDteFechaCierre();
        //this.dteFechaInicio = entity.getDteFechaInicio();
        //this.expiredDate = entity.getExpiredDate();
        //this.formAdjustRequest = entity.getFormAdjustRequest();
        //this.formAdjustRequestId = entity.getFormAdjustRequestId();
        //this.formCancelRequest = entity.getFormCancelRequest();
        //this.formCancelRequestId = entity.getFormCancelRequestId();
        //this.formReopenRequest = entity.getFormReopenRequest();
        //this.formReopenRequestId = entity.getFormReopenRequestId();
        //this.frozen = entity.getFrozen();
        //this.lastModifiedBy = entity.getLastModifiedBy();
        //this.lastModifiedDate = entity.getLastModifiedDate();
        //this.questionAttendants = entity.getQuestionAttendants();
        //this.stage = entity.getStage();
        //this.signatureAsSaveBehavior = entity.isSignatureAsSaveBehavior();
        //this.stageDaysToExpire = entity.getStageDaysToExpire();
        //this.stageDaysToNotice = entity.getStageDaysToNotice();
        //this.stageExpirationDate = entity.getStageExpirationDate();
        //this.stageLatestFillOut = entity.getStageLatestFillOut();
        //this.stageLatestFillOutUserId = entity.getStageLatestFillOutUserId();
        //this.stageNoticeDate = entity.getStageNoticeDate();
        //this.surveyAnswerMigrationId = entity.getSurveyAnswerMigrationId();
        //this.surveyProgressStateCancelId = entity.getSurveyProgressStateCancelId();
        //this.surveyProgressStateReopenId = entity.getSurveyProgressStateReopenId();
    }

    public OutstandingSurveys(Long id, Short estatus) {
        this.id = id;
        this.estatus = estatus;
        this.deleted = 0;
    }
    
    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "outstanding_surveys_id", nullable = false, precision = 19, scale = 0)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "form_reopen_request_id")
    public Long getFormReopenRequestId() {
        return formReopenRequestId;
    }

    public void setFormReopenRequestId(Long formReopenRequestId) {
        this.formReopenRequestId = formReopenRequestId;
    }

    @Column(name = "form_cancel_request_id")
    public Long getFormCancelRequestId() {
        return formCancelRequestId;
    }

    public void setFormCancelRequestId(Long formCancelRequestId) {
        this.formCancelRequestId = formCancelRequestId;
    }

    @Column(name = "form_adjust_request_id")
    public Long getFormAdjustRequestId() {
        return formAdjustRequestId;
    }

    public void setFormAdjustRequestId(Long formAdjustRequestId) {
        this.formAdjustRequestId = formAdjustRequestId;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    @Column(name = "survey_answer_migration_id")
    public Long getSurveyAnswerMigrationId() {
        return surveyAnswerMigrationId;
    }

    public void setSurveyAnswerMigrationId(Long surveyAnswerMigrationId) {
        this.surveyAnswerMigrationId = surveyAnswerMigrationId;
    }

    /**
     * No se serializa a JSON!
     *
     * @return the autorizationPoolDetailComment
     */
    @JSON(serialize = false)
    @JsonIgnore()
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "outstanding_surveys_id", referencedColumnName = "rejected_out_surveys_id",
            insertable = false, updatable = false)
    public AutorizationPoolDetailComment getAutorizationPoolDetailComment() {
        return autorizationPoolDetailComment;
    }

    /**
     * @param autorizationPoolDetailComment the autorizationPoolDetailComment to set
     */
    public void setAutorizationPoolDetailComment(AutorizationPoolDetailComment autorizationPoolDetailComment) {
        this.autorizationPoolDetailComment = autorizationPoolDetailComment;
    }

    @ManyToOne
    @JoinColumn(name = "form_reopen_request_id", referencedColumnName = "id", updatable = false, insertable = false)
    public FormRequest getFormReopenRequest() {
        return formReopenRequest;
    }

    @ManyToOne
    @JoinColumn(name = "form_cancel_request_id", referencedColumnName = "id", updatable = false, insertable = false)
    public FormRequest getFormCancelRequest() {
        return formCancelRequest;
    }

    @ManyToOne
    @JoinColumn(name = "form_adjust_request_id", referencedColumnName = "id", updatable = false, insertable = false)
    public FormRequest getFormAdjustRequest() {
        return formAdjustRequest;
    }

    public void setFormReopenRequest(FormRequest formReopenRequest) {
        this.formReopenRequest = formReopenRequest;
    }

    public void setFormCancelRequest(FormRequest formCancelRequest) {
        this.formCancelRequest = formCancelRequest;
    }

    public void setFormAdjustRequest(FormRequest formAdjustRequest) {
        this.formAdjustRequest = formAdjustRequest;
    }

    @Column(name = "expired_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getExpiredDate() {
        return expiredDate;
    }

    @Column(name = "stage_days_to_expire")
    public Integer getStageDaysToExpire() {
        return stageDaysToExpire;
    }

    public void setStageDaysToExpire(Integer stageDaysToExpire) {
        this.stageDaysToExpire = stageDaysToExpire;
    }

    @Column(name = "stage_days_to_notice")
    public Integer getStageDaysToNotice() {
        return stageDaysToNotice;
    }

    public void setStageDaysToNotice(Integer stageDaysToNotice) {
        this.stageDaysToNotice = stageDaysToNotice;
    }

    @Column(name = "stage_latest_fill_out")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStageLatestFillOut() {
        return stageLatestFillOut;
    }

    public void setStageLatestFillOut(Date stageLatestFillOut) {
        this.stageLatestFillOut = stageLatestFillOut;
    }

    @Column(name = "stage_latest_fill_out_user_id")
    public Long getStageLatestFillOutUserId() {
        return stageLatestFillOutUserId;
    }

    public void setStageLatestFillOutUserId(Long stageLatestFillOutUserId) {
        this.stageLatestFillOutUserId = stageLatestFillOutUserId;
    }

    /**
     * Se calcula en BDD
     * @return 
     */
    @Column(name = "stage_expiration_date", insertable = false, updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStageExpirationDate() {
        return stageExpirationDate;
    }

    public void setStageExpirationDate(Date stageExpirationDate) {
        this.stageExpirationDate = stageExpirationDate;
    }

    /**
     * Se calcula en BDD
     * @return 
     */
    @Column(name = "stage_notice_date", insertable = false, updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStageNoticeDate() {
        return stageNoticeDate;
    }

    public void setStageNoticeDate(Date stageNoticeDate) {
        this.stageNoticeDate = stageNoticeDate;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    @Column(name = "code", updatable = false, insertable = false)
    public String getDescription() {
        return this.code;
    }

    public void setDescription(String _description) {
        // do nothing
    }
    
    @Basic(optional = false)
    @Column(name = "status", nullable = false)
    public Short getEstatus() {
        return estatus;
    }

    public void setEstatus(Short estatus) {
        this.estatus = estatus;
    }

    @Column(name = "status", updatable = false, insertable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "INT_B_IS_FROZEN")
    public Short getFrozen() {
        return frozen;
    }

    public void setFrozen(Short frozen) {
        this.frozen = frozen;
    }

    @JSON(serialize = false)
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name="SURVEY_ID")
    public Survey getCuestionario() {
        return cuestionario;
    }

    public void setCuestionario(Survey cuestionario) {
        this.cuestionario = cuestionario;
    }
    
    @Column(name = "SURVEY_ID", updatable = false, insertable = false)
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @OneToMany
    @JoinTable(
            name="OUTSTANDING_SURVEYS_QUESTION",
            joinColumns = {
                @JoinColumn(name = "OUTSTANDING_SURVEYS_ID")},
            inverseJoinColumns = @JoinColumn( name="QUESTION_ID")
    )
    @LazyCollection(LazyCollectionOption.FALSE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OrderBy(value="order")
    @BatchSize(size = 5)
    public List<OutstandingQuestion> getPreguntasRespondidas() {
        return preguntasRespondidas;
    }

    public void setPreguntasRespondidas(List<OutstandingQuestion> preguntasRespondidas) {
        this.preguntasRespondidas = preguntasRespondidas;
    }
    
    @LazyCollection(LazyCollectionOption.FALSE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(mappedBy = "outstandingSurvey", fetch = FetchType.EAGER)
    public List<OutstandingSurveysAttendantLoad> getQuestionAttendants() {
        return questionAttendants;
    }

    public void setQuestionAttendants(List<OutstandingSurveysAttendantLoad> questionAttendants) {
        this.questionAttendants = questionAttendants;
    }

    @Column(name = "DTE_start_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getDteFechaInicio() {
        return dteFechaInicio;
    }

    public void setDteFechaInicio(Date dteFechaInicio) {
        this.dteFechaInicio = dteFechaInicio;
    }
    
    @Column(name = "DTE_CLOSING_DATE")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getDteFechaCierre() {
        return dteFechaCierre;
    }

    public void setDteFechaCierre(Date dteFechaCierre) {
        this.dteFechaCierre = dteFechaCierre;
    }

    @Column(name = "score")
    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Column(name = "creator_user_id")
    public Long getCreatorUserId() {
        return creatorUserId;
    }

    public void setCreatorUserId(Long creatorUserId) {
        this.creatorUserId = creatorUserId;
    }
    
    @ManyToOne
    @LazyCollection(LazyCollectionOption.FALSE)
    @JoinColumn(name="request_id", insertable = false, updatable = false)
    @JsonIgnore
    public RequestRef getRequest() {
        return request;
    }

    public void setRequest(RequestRef request) {
        this.request = request;
    }

    @Transient
    public Boolean isSignatureAsSaveBehavior() {
        return signatureAsSaveBehavior;
    }

    public void setSignatureAsSaveBehavior(boolean signaturAsSaveBehavior) {
        this.signatureAsSaveBehavior = signaturAsSaveBehavior;
    }

    @Transient
    @Override
    public Boolean isCreateCopy() {
        return createCopy;
    }

    public void setCreateCopy(boolean createCopy) {
        this.createCopy = createCopy;
    }

    @Override
    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "answers_table_id")
    public Long getAnswersTableId() {
        return answersTableId;
    }

    public void setAnswersTableId(Long answersTableId) {
        this.answersTableId = answersTableId;
    }    

    @Transient
    public Long getNextUserToFill() {
        return nextUserToFill;
    }

    public void setNextUserToFill(Long nextUserToFill) {
        this.nextUserToFill = nextUserToFill;
    }

    @Column(name = "form_progress_state_id")
    public Long getProgressStateId() {
        return progressStateId;
    }

    public void setProgressStateId(Long progressStateId) {
        this.progressStateId = progressStateId;
    }
    
    @Column(name = "stage")
    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "area_id")
    @Override
    public Long getAreaId() {
        return areaId;
    }

    @Override
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    @Column(name = "survey_progress_state_cancel_id")
    public Long getSurveyProgressStateCancelId() {
        return surveyProgressStateCancelId;
    }

    public void setSurveyProgressStateCancelId(Long surveyProgressStateCancelId) {
        this.surveyProgressStateCancelId = surveyProgressStateCancelId;
    }

    @Column(name = "survey_progress_state_reopen_id")
    public Long getSurveyProgressStateReopenId() {
        return surveyProgressStateReopenId;
    }

    public void setSurveyProgressStateReopenId(Long surveyProgressStateReopenId) {
        this.surveyProgressStateReopenId = surveyProgressStateReopenId;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    @Column(name = "archived")
    @Type(type = "numeric_boolean")
    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 31 * hash + Objects.hashCode(this.id);
        hash = 31 * hash + Objects.hashCode(this.code);
        hash = 31 * hash + Objects.hashCode(this.cuestionario);
        return hash;
}

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveys other = (OutstandingSurveys) obj;
        if (!Objects.equals(this.code, other.getId())) {
            return false;
        }
        if (!Objects.equals(this.estatus, other.getEstatus())) {
            return false;
        }
        return Objects.equals(this.cuestionario, other.getCuestionario());
    }

    @Transient
    public String getEditedOutstandingidTitle() {
        return editedOutstandingidTitle;
    }

    public void setEditedOutstandingidTitle(String editedOutstandingidTitle) {
        this.editedOutstandingidTitle = editedOutstandingidTitle;
    }

    @Transient
    public String getSurveyRequestMode() {
        return surveyRequestMode;
    }

    public void setSurveyRequestMode(String surveyRequestMode) {
        this.surveyRequestMode = surveyRequestMode;
    }

    @Transient
    public String getConditionalValidatorCacheId() {
        return conditionalValidatorCacheId;
    }

    public void setConditionalValidatorCacheId(String conditionalValidatorCacheId) {
        this.conditionalValidatorCacheId = conditionalValidatorCacheId;
    }

    @Override
    @Transient
    public Long getOutstandingSurveyId() {
        return this.id;
    }

    @Transient
    @Override
    public Integer getSaveAction() {
        return saveAction;
    }

    @Override
    public void setSaveAction(Integer saveAction) {
        this.saveAction = saveAction;
    }

    @Override
    public String toString() {
        return "OutstandingSurveys{" 
                + "id=" + id 
                + ",estatus=" + estatus 
                + ", code=" + code 
                + '}';
    }

    
}
