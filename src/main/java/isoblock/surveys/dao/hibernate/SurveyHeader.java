package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import bnext.aspect.IExcludeLogging;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.form.util.ISurveyHeader;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.IPersistableCode;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "SURVEY_HEADER")
public class SurveyHeader extends DomainObject implements Serializable, ISurveyHeader, IExcludeLogging, IPersistableCode {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    
    private String code;
    private String ponderacion;
    private Integer frozenTemaClave;
    private Integer order;
    private List<SurveyTextItem> title;
    
    public SurveyHeader() {
    }

    public SurveyHeader(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "HEADER_ID", nullable = false, precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    @Column(name = "INT_ORDER")
    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    @Column(name = "INT_FROZEN_QUESTION_THEME_CODE")
    public Integer getFrozenTemaClave() {
        return frozenTemaClave;
    }

    public void setFrozenTemaClave(Integer frozenTemaClave) {
        this.frozenTemaClave = frozenTemaClave;
    }


    /**
     * @return Esta ponderacion debe tener un valor difernete de nulo unicamente cuando se trate de preguntas tipo:
     *          - Matriz multiple
     *          - Matriz
     */
    @Column(name = "VCH_PONDERACION")
    public String getPonderacion() {
        return ponderacion;
    }

    public void setPonderacion(String ponderacion) {
        this.ponderacion = ponderacion;
    }
    
    @Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(name = "SURVEY_HEADER_TITLE", joinColumns = {
        @JoinColumn(name = "HEADER_ID", referencedColumnName = "HEADER_ID", nullable = false)}, inverseJoinColumns = {
        @JoinColumn(name = "TEXT_ITEM_ID", referencedColumnName = "TEXT_ITEM_ID", nullable = false)})
    @ManyToMany(fetch = FetchType.EAGER)
    @Fetch(value = FetchMode.SUBSELECT)
    public List<SurveyTextItem> getTitle() {
        return title;
    }

    public void setTitle(List<SurveyTextItem> title) {
        this.title = title;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SurveyHeader)) {
            return false;
        }
        SurveyHeader other = (SurveyHeader) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.SurveyHeader[ id=" + id + " ]";
    }
}
