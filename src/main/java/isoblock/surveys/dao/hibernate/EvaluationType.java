package isoblock.surveys.dao.hibernate;
import DPMS.Mapping.ClauseType;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.util.CacheConstants;
/**
 *
 * <AUTHOR>
 * 
 * @ISLT    :   <deprecated/> Esta es la tabla que se refiere al TEMA (TEMA_ID) (Agrupador de preguntas OXXO)
 * @RH      :   <deprecated/> Tipo de recurrencia
 * @Producto:   Clausulas, la clausula de la norma relacionada a la pregunta
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@LocalizedEntity
@Table(name="evaluation_type")
public class EvaluationType extends StandardEntity<EvaluationType> implements DomainObjectInterface {
    
    public final String PREFIX = "CLT-";
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;
    private Long typeId;
    private ClauseType type;

    public EvaluationType() {
    }

    public EvaluationType(Long id) {
        this.id = id;
    }
    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name="EVALUATION_TYPE_ID", unique=true, nullable=false)
    public Long getId(){
        return this.id;
    }
    
    @Override
    public void setId(Long id){
        this.id=id;
    }
    
    @LocalizedField
    @Basic
    @Column(name="description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Basic
    @Column(name="code", length=255, unique=true)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic
    @Column(name="STATUS")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name="type")
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "type", referencedColumnName = "clause_type_id", insertable = false, updatable = false)
    
    public ClauseType getType() {
        return type;
    }

    public void setType(ClauseType type) {
        this.type = type;
    }

    @Override
    @Column(name="deleted")
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
}

