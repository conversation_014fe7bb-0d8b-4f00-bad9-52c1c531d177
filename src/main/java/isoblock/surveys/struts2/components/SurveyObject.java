package isoblock.surveys.struts2.components;

import bnext.reference.BusinessUnitRef;
import isoblock.surveys.dao.hibernate.AreaDepartment;
import isoblock.surveys.dao.hibernate.EvaluationType;
import isoblock.surveys.dao.hibernate.QuestionTheme;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyConditional;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldAreaPlaza;
import isoblock.surveys.dao.hibernate.SurveyFieldEditable;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.hibernate.SurveyGlobal;
import isoblock.surveys.dao.hibernate.SurveyGlobalObject;
import isoblock.surveys.dao.hibernate.SurveyHeader;
import isoblock.surveys.dao.hibernate.SurveyItem;
import isoblock.surveys.dao.hibernate.SurveyMatrixOptions;
import isoblock.surveys.dao.hibernate.SurveyProgressStateCancel;
import isoblock.surveys.dao.hibernate.SurveyProgressStatePartial;
import isoblock.surveys.dao.hibernate.SurveyProgressStateReject;
import isoblock.surveys.dao.hibernate.SurveyTextItem;
import isoblock.surveys.dao.hibernate.TypeSurveyed;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.document.entity.RequestRef;
import qms.document.entity.SurveyTheme;
import qms.form.util.SurveyConditionalType;

/**
 *
 * <AUTHOR> Limas
 * @coment Este objeto solo es utilizado para recibir datos
 * - Se incluyo metodo para 'parsear' de 'SurveyObject' a 'Survey'
 */
public class SurveyObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private global globals;
    private field[] items;    
    private transient Map config;
    private transient Logger logger = null;
    
    protected final Logger getLogger() {
        if(logger == null) {
            logger = Loggable.getLogger(this.getClass());
        }
        return logger;
    }

    public Survey parseToSurvey() {
        Survey survey = new Survey();
        SurveyGlobal parsedGlobals = new SurveyGlobal();
        SurveyGlobalObject obj = new SurveyGlobalObject();

        obj.setId(globals.getGlobal_obj_id());
        obj.setCurrentId(globals.getCurrentId());
        obj.setLanguages(stringArrayToListSurveyTextItem(globals.getLanguages()));
        obj.setPages(integerArrayToListSurveyTextItem(globals.getPages()));
        obj.setFileUpload(Short.valueOf("1"));
        obj.setImageField(Short.valueOf("1"));
        obj.setMapSelect(Short.valueOf("1"));
        obj.setMultiLang(Short.valueOf("1"));
        obj.setMultiPage(Short.valueOf("1"));
        obj.setVideoField(Short.valueOf("1"));
        obj.setHasWeight(Short.valueOf(globals.getHasWeight()+""));
        obj.setRol_si(Short.valueOf(globals.getRol_si()+""));
        obj.setRol_no(Short.valueOf(globals.getRol_no()+""));
        obj.setEsquemaTienda(objectArrayToListTipoEncuestado(globals.getEsquemaTienda()));
        obj.setUnes(objectArrayToListUne(globals.getUnes()));
        obj.setTitle(textItemToListSurveyTextItem(globals.getTitle()));
        obj.setEditableTitle(globals.isEditableTitleGlobal());
        
        survey.setType(globals.getType());
        survey.setRequestFull(globals.getRequest());
        
        if (globals.getRequest() != null) {
            Long surveyThemeId = globals.getRequest().getSurveyThemeId();
            SurveyTheme surveyTheme =  new SurveyTheme(surveyThemeId != null ? surveyThemeId : 1L);
            if ( globals.getRequest().getId() != null && globals.getRequest().getId() > 0) {
                survey.setRequest(new RequestRef(globals.getRequest().getId()));
            }
            survey.setSurveyTheme(surveyTheme);
        } else if (survey.getSurveyTheme() == null){
            survey.setSurveyTheme(new SurveyTheme(1L));
        }
        survey.configSet(config);

        parsedGlobals.setId(globals.getGlobal_id());
        parsedGlobals.setOrder(1);

        List<SurveyField> fields = new ArrayList<>();
        
        int cantidadPreguntas = 0;
        boolean isFillRequestAvailable = false;
        boolean isSignRejectApproval = false;
        field section = null;
        for (field item : items) {
            if(SurveyField.TYPE_SECCION.equals(item.getType()) || SurveyField.TYPE_SIGNATURE.equals(item.getType())) {
                section = item;
            }
            parseField(item,section,fields, null);
            if(
                !SurveyField.TYPE_SECCION.equals(item.getType())
                && !SurveyField.TYPE_SIGNATURE.equals(item.getType())
                && !SurveyField.TYPE_INSTRUCCIONES.equals(item.getType())
            ) {
                cantidadPreguntas++; 
            }
            if(!isSignRejectApproval) {
                isSignRejectApproval = Boolean.TRUE.equals(item.getSignRejectApproval());
            }
            if(!isFillRequestAvailable) {
                isFillRequestAvailable = "requestor".equalsIgnoreCase(item.getFillEntity());
            }
        }
        survey.setSignRejectApproval(isSignRejectApproval);
        if(isFillRequestAvailable) {
            survey.setIsFillRequestAvailable(1);
        } else {
            survey.setIsFillRequestAvailable(0);
        }
        survey.setId(globals.getDb_id());
        survey.setEstatus(globals.getEstatus());
        survey.setFields(fields);
        survey.setCode(globals.getCode());
        obj.setQuestions(cantidadPreguntas);
        parsedGlobals.setObj(obj);
        survey.setGlobals(parsedGlobals);
        getLogger().debug("------------------------------------------");
        getLogger().debug("Survey ID : {}",globals.getDb_id());
        getLogger().debug("isFillRequestAvailable: {}",isFillRequestAvailable);
        getLogger().debug("------------------------------------------");
        return survey;
    }

    private void parseField(
            final field item,
            final field section,
            final List<SurveyField> surveyFields,
            final String parent
    ) {
        EvaluationType clause;
        SurveyFieldObject obj = new SurveyFieldObject();
        List<SurveyHeader> headerTitles;
        List<SurveyMatrixOptions> matrixOptions;
        boolean hasTema = item.getTema()!=0 && item.getTema()!=-1; 
        if(SurveyField.TYPE_SECCION.equals(item.getType()) && hasTema) {
            clause = new EvaluationType(Long.valueOf(item.getTema()+""));
        } else if(hasTema) {
            clause = new EvaluationType(Long.valueOf(item.getTema()+""));
        } else {
            clause = null;
        }
            
        headerTitles = listListStringArrayToListSurveyHeader(item.getHeaderTitles());
            //opciones de la matriz de combos
        matrixOptions = listListStringArrayToListSurveyMatrixOptions(item.getMatrixOptions());
        obj.setId(item.getDb_id());
        obj.setCodify_answers(Short.valueOf(item.getCodify_answers()+""));

        obj.setFillEntity(item.getFillEntity());
        obj.setParamUrlFiller(item.getParamUrlFiller());
        obj.setStage(item.getStage());
        obj.setHelpIcon(item.getHelpIcon());
        obj.setHelpUrl(item.getHelpUrl());
        obj.setAddFindingsEnabled(item.getAddFindingsEnabled());
        obj.setConditionalQuestion(item.getConditionalQuestion());
        obj.setSummationQuestion(item.getSummationQuestion());
        obj.setSelectedWeightedField(item.getSelectedWeightedField() != null ? item.getSelectedWeightedField(): "f");
        obj.setConditionalExpirationQuestion(item.getConditionalExpirationQuestion());
        obj.setAddActivitiesEnabled(item.getAddActivitiesEnabled());
        obj.setAutoSelectSingleAnswer(item.getAutoSelectSingleAnswer());
        
        obj.setActivitiesTemplateEnabled(item.getActivitiesTemplateEnabled());
        obj.setActivitiesTemplateId(item.getActivitiesTemplateId());
        
        obj.setBusinessUnitMainField(Boolean.TRUE.equals(item.getBusinessUnitMainField()));
        obj.setBusinessUnitDepartmentMainField(Boolean.TRUE.equals(item.getBusinessUnitDepartmentMainField()));
        obj.setAreaMainField(Boolean.TRUE.equals(item.getAreaMainField()));
        obj.setAllowDeleteStopwatchRecord(Boolean.TRUE.equals(item.getAllowDeleteStopwatchRecord()));

        obj.setActivitiesTemplateImplementerEntity(item.getActivitiesTemplateImplementerEntity());        
        obj.setActivityImplementerBusinessUnitPositionId(item.getActivityImplementerBusinessUnitPositionId());
        obj.setActivityImplementerBusinessUnitPositionLabel(item.getActivityImplementerBusinessUnitPositionLabel());
        obj.setActivityImplementerOrganizationalUnitPositionId(item.getActivityImplementerOrganizationalUnitPositionId());
        obj.setActivityImplementerOrganizationalUnitPositionLabel(item.getActivityImplementerOrganizationalUnitPositionLabel());
        obj.setActivityImplementerUserId(item.getActivityImplementerUserId());
        obj.setActivityImplementerUserLabel(item.getActivityImplementerUserLabel());
        
        obj.setActivitiesTemplateVerifierEntity(item.getActivitiesTemplateVerifierEntity());
        obj.setActivityVerifierBusinessUnitPositionId(item.getActivityVerifierBusinessUnitPositionId());
        obj.setActivityVerifierBusinessUnitPositionLabel(item.getActivityVerifierBusinessUnitPositionLabel());
        obj.setActivityVerifierOrganizationalUnitPositionId(item.getActivityVerifierOrganizationalUnitPositionId());
        obj.setActivityVerifierOrganizationalUnitPositionLabel(item.getActivityVerifierOrganizationalUnitPositionLabel());
        obj.setActivityVerifierUserId(item.getActivityVerifierUserId());
        obj.setActivityVerifierUserLabel(item.getActivityVerifierUserLabel());
            
        obj.setBusinessUnitPosition(item.getBusinessUnitPosition());
        obj.setOrganizationalUnitPosition(item.getOrganizationalUnitPosition());
        obj.setUser(item.getUser());
        
        obj.setBusinessUnitPositionId(item.getBusinessUnitPositionId());
        obj.setOrganizationalUnitPositionId(item.getOrganizationalUnitPositionId());
        obj.setUserId(item.getUserId());
        
        obj.setColumns(headerTitles.size());
        obj.setHasHeaders(Short.valueOf(item.getHasHeaders()+""));
        obj.setHasTwoHeaders(Short.valueOf(item.getHasTwoHeaders()+""));
        obj.setHasRowPillar(Short.valueOf(item.getHasRowPillar()+""));
        if (!headerTitles.isEmpty()) {
            headerTitles.sort(new HeaderComparator());
        }
        obj.setHeaderTitles(headerTitles);
        obj.setMatrixOptions(matrixOptions);
        obj.setImage(Short.valueOf(item.getImage()+""));
        obj.setLayout(item.getLayout());
        obj.setOrder(item.getOrder());
        obj.setOtherOption(Short.valueOf(item.getOtherOption()+""));
        obj.setOtherOptionText(itemToListSurveyTextItem(item.getOtherOptionText()));
        obj.setRequired(item.getRequired());
        obj.setSize(item.getSize());
        obj.setSubTitle(textItemToListSurveyTextItem(item.getSubTitle()));
        obj.setTitle(textItemToListSurveyTextItem(item.getTitle()));
        obj.setHiddenMatrixItems(stringArrayToListSurveyTextItem(item.getHiddenItems()));
        obj.setValidation(Short.valueOf(item.getValidation()+""));
        obj.setAnswerType(item.getAnswerType());
        obj.setWeightingType(item.getWeightingType());
        obj.setRespondentType(
                item.getRespondentType()==0||item.getRespondentType()==-1?
                null:new TypeSurveyed(Long.valueOf(item.getRespondentType()+""))
                    );
        obj.setPilarOxxo(
                item.getPilarOxxo()==0||item.getPilarOxxo()==-1?
                null:new QuestionTheme(Long.valueOf(item.getPilarOxxo()+""))
                    );
        obj.setTema(clause);
        obj.setAreaPlaza( 
                item.getAreaPlaza()==0||item.getAreaPlaza()==-1?
                null:new AreaDepartment(Long.valueOf(item.getAreaPlaza()+""))
                    );
        obj.setField_id(item.getId());

        obj.setItems(itemArrayToListSurveyItem(item.getItems()));
        obj.setMatrixAreasPlaza(objectListToAreaPlazaArray(item.getMatrixAreasPlaza(),obj)); 
        if (obj.getMatrixAreasPlaza().size()>headerTitles.size()) {
            obj.setColumns(obj.getMatrixAreasPlaza().size());
        }
        if (item.getDaysToExpire() == null) {
            if (section == null || section.getDaysToExpire() == null) {
                obj.setDaysToExpire(0);
            } else {
                obj.setDaysToExpire(section.getDaysToExpire());
            }
        } else {
            obj.setDaysToExpire(item.getDaysToExpire());
        }
        if (item.getDaysToNotifyBeforeExpiration() == null) {
            if (section == null || section.getDaysToNotifyBeforeExpiration() == null) {
                obj.setDaysToNotifyBeforeExpiration(0);
            } else {
                obj.setDaysToNotifyBeforeExpiration(section.getDaysToNotifyBeforeExpiration());
            }
        } else {
            obj.setDaysToNotifyBeforeExpiration(item.getDaysToNotifyBeforeExpiration());
        }
        if (item.getIncludeInMail() == null) {
            if (section == null || section.getIncludeInMail() == null) {
                obj.setIncludeInMail(0);
            } else {
                obj.setIncludeInMail(section.getIncludeInMail());
            }
        } else {
            obj.setIncludeInMail(item.getIncludeInMail());
        }
        obj.setLocation(item.getLocation());
        obj.setParentId(parent);
        obj.setImageSizePreview(item.getImageSizePreview());
        obj.setDefaultDateValue(item.getDefaultDateValue());
        obj.setIncludeTime(item.getIncludeTime());
        obj.setRestrictPastDates(item.getRestrictPastDates());
        obj.setMaxPastHours(item.getMaxPastHours());
        obj.setDefaultValue(removeDefaultValue(item.getDefaultValue()));
        obj.setFormArea(item.getFormArea());
        obj.setCatalogType(item.getCatalogType());
        obj.setCatalogSubType(item.getCatalogSubType());
        obj.setInternalCatalogId(item.getInternalCatalogId());
        obj.setExternalCatalogId(item.getExternalCatalogId());
        obj.setCanCancel(item.getCanCancel());
        obj.setSignRejectApproval(item.getSignRejectApproval());
        obj.setAllowDeleteStopwatchRecord(item.getAllowDeleteStopwatchRecord());
        obj.setRequestAdjust(item.getRequestAdjust());
        obj.setPivotTableOnMobile(item.getPivotTableOnMobile());
        obj.setConfigureShowSummationField(item.getConfigureShowSummationField());
        obj.setAttendProgressStateId(item.getAttendProgressStateId());
        obj.setFixedQrUrl(item.getFixedQrUrl());
        obj.setQrUrlType(item.getQrUrlType());
        obj.setSignatureType(item.getSignatureType());
        obj.setDaysToWait(item.getDaysToWait());
        obj.setCurrentHeaderId(item.getCurrentHeaderId());
        obj.setCurrentItemId(item.getCurrentItemId());
        obj.setDeletable(item.getDeletable());
        obj.setHasUpDroppable(item.getHasUpDroppable());
        obj.setHasDownDroppable(item.getHasDownDroppable());
        obj.setFillEntityEditable(item.getFillEntityEditable());
        obj.setHidden(item.getHidden());
        obj.setAllowNegativeValues(Boolean.TRUE.equals(item.getAllowNegativeValues()));
        obj.setShowFillOutDate(item.getShowFillOutDate());
        obj.setAllowMobileUploadingFrom(item.getAllowMobileUploadingFrom());
        SurveyField field = new SurveyField();
        field.setId(item.getDb_id_type());
        field.setType(item.getType());
        if (item.getEditableSections() != null) {
            obj.setSurveyFieldObjectEditables(new ArrayList<>(item.getEditableSections().size()));
            for (final String editableSection : item.getEditableSections()) {
                obj.getSurveyFieldObjectEditables().add(new SurveyFieldEditable(-1L, -1L, editableSection));
            }
        }
        addSurveyConditionals(item, obj);
        if (item.getPartialProgressStatuses() != null) {
            obj.setPartialProgressStatuses(new LinkedHashSet<>());
            for (final Long partialProgressStatusId : item.getPartialProgressStatuses()) {
                obj.getPartialProgressStatuses().add(new SurveyProgressStatePartial(-1L, partialProgressStatusId));
            }
        }
        if (item.getCancelProgressStatuses() != null) {
            obj.setCancelProgressStatuses(new LinkedHashSet<>());
            for (final Long cancelProgressStatusId : item.getCancelProgressStatuses()) {
                obj.getCancelProgressStatuses().add(new SurveyProgressStateCancel(-1L, cancelProgressStatusId));
            }
        }
        if (item.getRejectProgressStatuses() != null) {
            obj.setRejectProgressStatuses(new LinkedHashSet<>());
            for (final Long rejectProgressStatusId : item.getRejectProgressStatuses()) {
                obj.getRejectProgressStatuses().add(new SurveyProgressStateReject(-1L, rejectProgressStatusId));
            }
        }
        field.setObj(obj);
        surveyFields.add(field);
        if(parent == null && item.getItemsStash() != null){
            for(field f : item.getItemsStash()){
                parseField(f,section,surveyFields,item.getId());
            }
        }
    }
    
    private void addSurveyConditionals(
            final field item,
            final SurveyFieldObject obj
    ) {
        int cSize = item.getConditionals() != null ? item.getConditionals().size() : 0;
        int csSize = item.getSummationFields() != null ? item.getSummationFields().size() : 0;
        int cwSize = item.getWeightedFields() != null ? item.getWeightedFields().size() : 0;
        int ceSize = item.getConditionalsExpiration() != null ? item.getConditionalsExpiration().size() : 0;
        if ((cSize + csSize + ceSize + cwSize) > 0) {
            obj.setConditionals(new TreeSet<>());
            if (cSize > 0) {
                addConditionals(item.getConditionals(), obj, SurveyConditionalType.CONDITIONAL);
            }
            if (csSize > 0) {
                addConditionals(item.getSummationFields(), obj, SurveyConditionalType.SUMMATION);
            }
            if (cwSize > 0) {
                addConditionals(item.getWeightedFields(), obj, SurveyConditionalType.WEIGHTS);
            }
            if (ceSize > 0) {
                addConditionals(item.getConditionalsExpiration(), obj, SurveyConditionalType.EXPIRATION);
            }
        }
    }

    private void addConditionals(
            final List<SurveyConditionalSaveDTO> conditionals,
            final SurveyFieldObject obj,
            final SurveyConditionalType type
    ) {
        long conditionalsSize = conditionals.size();
        for (long i = 0L; i < conditionalsSize; i++) {
            SurveyConditionalSaveDTO conditionalDto = conditionals.get((int) i);
            final SurveyConditional conditional = new SurveyConditional();
            conditional.setId(-1L);
            conditional.setConditionalOrder(i);
            conditional.setConditionalType(conditionalDto.getConditionalType());
            conditional.setConditionalAnswerSelector(conditionalDto.getId());
            conditional.setConditionalQuestionCode(conditionalDto.getConditionalQuestionCode());
            conditional.setConditionalAnswerCode(conditionalDto.getConditionalAnswerCode());
            conditional.setHierarchyRow(conditionalDto.getHierarchyRow());
            conditional.setHierarchyLevel(conditionalDto.getHierarchyLevel());
            conditional.setHierarchyColumn(conditionalDto.getHierarchyColumn());
            conditional.setHierarchyValue(conditionalDto.getHierarchyValue());
            conditional.setType(type.getValue());
            conditional.setDaysToExpire(conditionalDto.getDaysToExpire());
            conditional.setDaysToNotifyBeforeExpiration(conditionalDto.getDaysToNotifyBeforeExpiration());
            conditional.setMaxLim(conditionalDto.getMaxLim());
            conditional.setMinLim(conditionalDto.getMinLim());
            conditional.setEqLim(conditionalDto.getEqLim());
            obj.getConditionals().add(conditional);
        }
    }
    
    private Set<SurveyFieldAreaPlaza> objectListToAreaPlazaArray(Set<matrixAreaPlaza> lista, SurveyFieldObject fieldObject) {
        Set<SurveyFieldAreaPlaza> list = new HashSet<>();
        if(lista == null){
            return list;
        }
        SurveyFieldAreaPlaza headerTemp;
        matrixAreaPlaza textItemTemp;
        Iterator<matrixAreaPlaza> iTextItem = lista.iterator();
        while(iTextItem.hasNext()) {
            textItemTemp = iTextItem.next();
            headerTemp = new SurveyFieldAreaPlaza(textItemTemp.getOrder(),textItemTemp.getId(), fieldObject.getId());
            headerTemp.setAreaPlaza(new AreaDepartment(textItemTemp.getId()));
            list.add(headerTemp);
        }
        return list;
    }
    private List<TypeSurveyed> objectArrayToListTipoEncuestado(object[] lista) {
        List<TypeSurveyed> list = new ArrayList<>();
        if(lista == null) {
            return list;
        }
        for (object lista1 : lista) {
            list.add(new TypeSurveyed(lista1.getId()));
        }
        return list;
    }
    private List<BusinessUnitRef> objectArrayToListUne(object[] lista) {
        List<BusinessUnitRef> list = new ArrayList<>();
        if(lista == null) {
            return list;
        }
        for (object lista1 : lista) {
            list.add(new BusinessUnitRef(lista1.getId()));
        }
        return list;
    }
    private List<SurveyItem> itemArrayToListSurveyItem(item[] items) {
        List<SurveyItem> list = new ArrayList<>();
        if(items == null) {
            return list;
        }
        SurveyItem itemTemp;
        for (item item : items) {
            itemTemp = new SurveyItem();
            itemTemp.setId(-1L);
            itemTemp.setCode(item.getCode());
            itemTemp.setChecked(Short.valueOf(item.getChecked() + ""));
            itemTemp.setItemText(textItemToListSurveyTextItem(item.getItemText()));
            itemTemp.setAreaPlaza(item.getAreaPlaza() == null ? null : new AreaDepartment(item.getAreaPlaza()));
            itemTemp.setPilar(item.getPilar() == null ? null : new QuestionTheme(item.getPilar()));
            itemTemp.setPonderacion(item.getItemText().getWeight());
            itemTemp.setReportActivity(item.getReportActivity());
            itemTemp.setDefaultValue(item.getDefaultValue());
            itemTemp.setSummation(item.getSummation());
            itemTemp.setAnswerType(item.getAnswerType());
            itemTemp.setAllowNegativeValues(Boolean.TRUE.equals(item.getAllowNegativeValues()));
            list.add(itemTemp);
        }
        return list;
    }
    private List<SurveyTextItem> itemToListSurveyTextItem(item other) {
        if(other == null) {
            return new ArrayList<>();
        }
        return textItemToListSurveyTextItem(other.getItemText(), other);
    }
    private List<SurveyHeader> listListStringArrayToListSurveyHeader(List<textItemRelated> listlist) {
        List<SurveyHeader> list = new ArrayList<>();
        if(listlist == null){
            return list;
        }
        SurveyHeader headerTemp;
        textItemRelated textItemTemp;
        Iterator<textItemRelated> iTextItem = listlist.iterator();
        while(iTextItem.hasNext()) {
            headerTemp = new SurveyHeader();
            textItemTemp = iTextItem.next();
            headerTemp.setId(-1L);
            headerTemp.setCode(textItemTemp.getCode());
            headerTemp.setTitle(textItemRelatedToListSurveyTextItem(textItemTemp));
            //headerTemp.setPilar(textItemTemp.getPilar()==null?null:new QuestionTheme(textItemTemp.getPilar()));
            headerTemp.setPonderacion(textItemTemp.getWeight());
            headerTemp.setOrder(textItemTemp.getOrder());
            list.add(headerTemp);
        }
        return list;
    }
    private List<SurveyMatrixOptions> listListStringArrayToListSurveyMatrixOptions(List<textItem> listlist) {
        List<SurveyMatrixOptions> list = new ArrayList<>();
        if(listlist == null){
            return list;
        }
        SurveyMatrixOptions headerTemp;
        textItem textItemTemp;
        Iterator<textItem> iTextItem = listlist.iterator();
        while(iTextItem.hasNext()) {
            headerTemp = new SurveyMatrixOptions();
            textItemTemp = iTextItem.next();
            headerTemp.setId(-1L);
            headerTemp.setTitle(textItemToListSurveyTextItem(textItemTemp));
            headerTemp.setPonderacion(textItemTemp.getWeight());
            list.add(headerTemp);
        }
        return list;
    }
    
    private List<SurveyTextItem> integerArrayToListSurveyTextItem(int[] array) {
        List<SurveyTextItem> list = new ArrayList<>();
        if(array == null){
            return list;
        }
        SurveyTextItem temp;
        for(int item : array) {
            temp = new SurveyTextItem();
            temp.setId(-1L);
            temp.setTitle(""+item);
            list.add(temp);
        }
        return list;
    }

    private List<SurveyTextItem> stringArrayToListSurveyTextItem(String[] array) {
        List<SurveyTextItem> list = new ArrayList<>();
        if(array == null){
            return list;
        }
        SurveyTextItem temp;
        for (String array1 : array) {
            temp = new SurveyTextItem();
            temp.setId(-1L);
            temp.setTitle(array1);
            list.add(temp);
        }
        return list;
    }
    private List<SurveyTextItem> textItemToListSurveyTextItem(textItem ti, item other) {
        List<SurveyTextItem> list = new ArrayList<>();
        if(ti == null){
            return list;
        }
        try {
            SurveyTextItem temp;
            for(int i = 0 ; i < ti.getTitle().length ; i++) {
                temp = new SurveyTextItem();
                temp.setId(ti.getDb_id()[i] == null ? -1L : ti.getDb_id()[i]);
                temp.setTitle(removeDefaultValue(ti.getTitle()[i]));
                if (other != null) {
                    temp.setAnswerType(other.getAnswerType());
                    temp.setDefaultValue(other.getDefaultValue());
                }
                list.add(temp);
            }
        } catch(Exception e) {
            getLogger().error("Stack trace: ",e);
        }
        return list;
    }
    private List<SurveyTextItem> textItemToListSurveyTextItem(textItem ti) {
        return textItemToListSurveyTextItem(ti, null);
    }
    private List<SurveyTextItem> textItemRelatedToListSurveyTextItem(textItemRelated ti) {
        List<SurveyTextItem> list = new ArrayList<>();
        if(ti == null){
            return list;
        }
        try {
        SurveyTextItem temp;
        for(int i = 0 ; i < ti.getTitle().length ; i++) {
            temp = new SurveyTextItem();
            temp.setId(ti.getDb_id()[i] == null ? -1L : ti.getDb_id()[i]);
            temp.setTitle(removeDefaultValue(ti.getTitle()[i]));
            list.add(temp);
        }
        } catch(Exception e) {
            getLogger().error("Stack trace: ",e);
        }
        return list;
    }

    private String removeDefaultValue(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        switch (value.trim()) {
            case "Clic para editar":
                value = "";
                break;
            case "Click to edit":
                value = "";
                break;
            case "Clic%20para%20editar":
                value = "";
                break;
            case "%3Cp%3EClic%20para%20editar%3C%2Fp%3E%0A":
                value = "";
                break;
        }
            
        return value;
    }

    /**
     * @return the globals
     */
    public global getGlobals() {
        return globals;
    }

    /**
     * @param globals the globals to set
     */
    public void setGlobals(global globals) {
        this.globals = globals;
    }

    /**
     * @return the items
     */
    public field[] getItems() {
        return items;
    }

    /**
     * @param items the items to set
     */
    public void setItems(field[] items) {
        this.items = items;
    }

    public Map getConfig() {
        return config;
}

    public void setConfig(Map config) {
        this.config = config;
    }

    
}
