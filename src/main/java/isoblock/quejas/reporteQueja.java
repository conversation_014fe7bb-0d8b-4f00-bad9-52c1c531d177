package isoblock.quejas;

import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.Vector;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PiePlot3D;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

/** 
 *
 * <AUTHOR> <PERSON>
 */
public class reporteQueja extends queja{
    isoblock.common.Utilities util = new isoblock.common.Utilities();
    private String tipoGrafica = "1";
    private String rutagrafica = "";
    private String strGraficaX = "";
    private String tabla = "";

    private String intQuejaId = "0";
    private String intUbicacionId = "0";
    public String[] vchUbicaciones = new String[]{};

    private String intFromUbicacionId = "0";
    private String intEstado = "0";
    public String[] vchAreas = new String[]{};
    private String intAreaId = "0";

    private String intUsuarioId = "0";
    private String intGraficarPor = "1";


//REPORTADA
    private String dteFechaInicio1 = "";
    private String dteFechaTermino1 = "";
//EVALUADA
    private String dteFechaInicio2 = "";
    private String dteFechaTermino2 = "";
//ASIGNADA
    private String dteFechaInicio3 = "";
    private String dteFechaTermino3 = "";
//ATENDIDA EN PROCESO
    private String dteFechaInicio4 = "";
    private String dteFechaTermino4 = "";
//POR ACEPTAR
    private String dteFechaInicio5 = "";
    private String dteFechaTermino5 = "";
//CERRADA
    private String dteFechaInicio6 = "";
    private String dteFechaTermino6 = "";
//ESTADOS
    private String dteFechaInicioModificacion = "";
    private String dteFechaTerminoModificacion = "";

    private String imagePath = "../../files/imagenesReportes/GRAFICA_QUE_ESTADOS.jpg";
    private int imageWidth = 600;
    private int imageHeight = 350;
    DefaultCategoryDataset categoryDataset = new DefaultCategoryDataset();
    DefaultPieDataset pieDataset = new DefaultPieDataset();

    public static String GRAFICA_QUE_ESTADOS = "1";
    public static String GRAFICA_QUE_DETALLE = "2";

    /** Creates a new instance of Report */
    public reporteQueja() {
        if (!UPLOAD_FOLDER_IMG_REPORTES_EXT.equals("")) {
            imagePath = "http://" + SITE_HOST + "/files/imagenesReportes/GRAFICA_QUE_ESTADOS.jpg";
        }
        //loadSettings();
        connect();
    }

    public void finalize() throws Throwable {
        close();
        super.finalize();
    }

    public String comboTiposGrafica(){
        String codigo = "";

        //TODO: Poner en ingles
        codigo += "<option value='"+GRAFICA_QUE_ESTADOS+"'";
        if(tipoGrafica.equals(GRAFICA_QUE_ESTADOS))
            codigo +="selected='selected'" ;
        codigo +=">"+tags.getString("isoblock.quejas.reporteQueja.comboTiposGrafica.Quejasenproceso")+"</option>";

        codigo += "<option value='"+GRAFICA_QUE_DETALLE+"'";
        if(tipoGrafica.equals(GRAFICA_QUE_DETALLE))
            codigo +="selected='selected'" ;
        codigo +=">"+tags.getString("isoblock.quejas.reporteQueja.comboTiposGrafica.Detalledequejas")+"</option>";
        return codigo;
    }

    public String comboGraficarPor(String estado){
        String temp = "";
        String [] sel = getSelection(Integer.parseInt(estado));
        temp += "<option value=1" + sel[1]+">"+tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Estado")+"</option>\n";
        //temp += "<option value=2" + sel[2]+">Departamento desde el que se reporta la queja</option>\n";
        temp += "<option value=6" + sel[6]+">"+tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.QuejasporDepartamento")+"</option>\n";
        //temp += "<option value=3" + sel[3]+">Proceso</option>\n";
        temp += "<option value=4" + sel[4]+">"+tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Usuario")+"</option>\n";
        //temp += "<option value=5" + sel[5]+">Fecha Modificacion</option>\n";
        temp += "<option value=7" + sel[7]+">"+tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Fuente")+"</option>\n";
        temp += "<option value=8" + sel[8]+">"+tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Clasificacion")+"</option>\n";
        return temp;
    }

    public String comboEstados(){
        String codigo = "";
        String[] seleccion = super.seleccion(intEstado);
        codigo += "<option value='"+QUEJA_REPORTADA_POR_ASIGNAR+"' "+seleccion[1]+">"+super.getStringEstadoControlSimple(QUEJA_REPORTADA_POR_ASIGNAR)+"</option>";
        codigo += "<option value='"+QUEJA_REPORTADA_ASIGNADA+"' "+seleccion[2]+">"+super.getStringEstadoControlSimple(QUEJA_REPORTADA_ASIGNADA)+"</option>";
        codigo += "<option value='"+QUEJA_ATENDIDA_EN_IMPLEMENTACION+"' "+seleccion[3]+">"+super.getStringEstadoControlSimple(QUEJA_ATENDIDA_EN_IMPLEMENTACION)+"</option>";
        //codigo += "<option value='"+QUEJA_ATENDIDA_IMPLEMENTADA+"'  "+seleccion[4]+">"+super.getStringEstadoControlSimple(QUEJA_ATENDIDA_IMPLEMENTADA)+"</option>";
        codigo += "<option value='"+QUEJA_IMPLEMENTADA_POR_EVALUAR+"'  "+seleccion[5]+">"+super.getStringEstadoControlSimple(QUEJA_IMPLEMENTADA_POR_EVALUAR)+"</option>";
        codigo += "<option value='"+QUEJA_NO_PROCEDE+"'  "+seleccion[6]+">"+super.getStringEstadoControlSimple(QUEJA_NO_PROCEDE)+"</option>";
        codigo += "<option value='"+QUEJA_EVALUADA+"'  "+seleccion[7]+">"+super.getStringEstadoControlSimple(QUEJA_EVALUADA)+"</option>";
        return codigo;
    }

    public JFreeChart crearGrafica() {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        System.out.println("isoblock.quejas.queja.crearGrafica()");
        String titulo = tags.getString("isoblock.quejas.ReporteQuejas.Quejas");
        String valoresDe = tags.getString("isoblock.quejas.ReporteQuejas.Quejas");
        
        int tipo = Integer.parseInt(tipoGrafica);
        boolean imposibleGraficar = false;
        //super.setIntQuejaId(intQuejaId);
        //super.setAllValues();

        String sql = "";
        tabla = "";
        tabla +="<table>\n";
        System.out.println("tipo: "+tipo);
        SessionViewer sesion = new SessionViewer();
        String filterByBusinessUnit = sesion.isAdmin() ? "" : ""
            + " AND EXISTS ("
                + " SELECT "
                +   " 1 "
                +   " FROM business_unit_department bud "
                + " WHERE intubicacionid = bud.business_unit_department_id "
                + " AND bud.business_unit_id IN ("  
                    + " SELECT bud.business_unit_id "
                    + " FROM business_unit_department bud "
                    + " JOIN puesto p ON p.business_unit_id = bud.business_unit_id "
                    + " JOIN usuario_puesto usr ON p.puesto_id = usr.puesto_id"
                    + " WHERE usr.usuario_id = " + sesion.getLoggedUserId().toString()+ ""
                + ") "
            + " )";
        final NumberFormat formatter = new DecimalFormat("#0.00");   
        switch (tipo){
            case 1: //GRAFICA_QUE_ESTADOS
                System.out.println("--- GRAFICA ESTADOS ---");
                strGraficaX = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejasenproceso");
                sql = "SELECT COUNT(intquejaid) AS cantidad, intestado_control FROM tblqueja quejas "
                        + " WHERE 0=0 "
                        + filterByBusinessUnit 
                        + " GROUP BY intestado_control";
                int total = 0;
                Double max = Double.parseDouble(findSimple("SELECT SUM(cantidad) FROM ("+sql+") AS cantidades").equals("") ? "1" :
                                           findSimple("SELECT SUM(cantidad) FROM ("+sql+") AS cantidades"));
                Vector<String> edoQuejas = new Vector<String>();
                edoQuejas.addAll(Arrays.asList(new String[] {"1","2","3","5","6","10"}));
                System.out.println(sql);
                find(sql);
                tabla += "<tr align='center'>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Estado")+"</b></td>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas")+"</b></td>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Porcentaje")+"</b></td>\n";
                tabla += "</tr>\n";
                while (next()) {
                    if (edoQuejas.contains(fieldStringS("intestado_control"))) {
                        edoQuejas.remove(fieldStringS("intestado_control"));
                    }
                    pieDataset.setValue(super.getStringEstadoControlSimple(fieldStringS("intestado_control")), fieldIntS("cantidad"));
                    total += fieldIntS("cantidad");
                    tabla += "<tr align='center'>\n";
                    tabla += "    <td>" + super.getStringEstadoControlSimple(fieldStringS("intestado_control")) + ":</td>\n";
                    tabla += "    <td>" + fieldIntS("cantidad") + "</td>\n";
                    tabla += "    <td>" + formatter.format(fieldIntS("cantidad") * 100.0 / max) + "%</td>\n";
                    tabla += "</tr>\n";
                }
                for (int i = 0; i < edoQuejas.size(); i++) {
                    tabla += "<tr align='center'>\n";
                    tabla += "    <td>" + super.getStringEstadoControlSimple(edoQuejas.elementAt(i)) + ":</td>\n";
                    tabla += "    <td> - </td>\n";
                    tabla += "    <td> - </td>\n";
                    tabla += "</tr>\n";
                }
                tabla += "<tr align='center'>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas")+"</b></td>\n";
                tabla += "    <td><b>" + total + "</b></td>\n";
                tabla += "    <td><b>" + formatter.format(total*100.0/max) + "%</b></td>\n";
                tabla += "</tr>\n";
                break;
            case 2: //GRAFICA_QUE_DETALLE
                System.out.println("--- GRAFICA DETALLES ---");
                sql = "SELECT intautorid AS from_usuario,intubicacionid AS to_ubicacion,* FROM tblqueja WHERE 0=0 ";
                sql = "SELECT * FROM ("+sql+") AS quejas WHERE 0=0 ";
                sql += filterByBusinessUnit;
                if(!intUsuarioId.equals("0")) {
                    sql += " AND from_usuario = "+intUsuarioId;
                }
                if(vchUbicaciones.length != 0 ) {
                    if(!vchUbicaciones[0].equals("0")  || (vchUbicaciones[0].equals("0") && vchUbicaciones.length >1)) {
                        sql += " AND ( to_ubicacion = " + vchUbicaciones[0];
                        for (int i = 1; i < vchUbicaciones.length; i++) {
                            sql += " OR to_ubicacion = " + vchUbicaciones[i];
                        }
                        sql += ") ";
                    }
                }
                if(!intEstado.equals("0")) {
                    sql += " AND intestado_control = "+intEstado;
                }



//REPORTADA     
                if(!dteFechaInicio1.equals("")) {
                    sql += "AND tspfechahoracreacion >= " + dao.SQL_convertDate("datetime", dteFechaInicio1, "120");
                }
                if(!dteFechaTermino1.equals("")) {
                    sql += " AND tspfechahoracreacion <= "+dao.SQL_convertDate("datetime", dteFechaTermino1, "120");
                }
//EVALUADA
                if(!dteFechaInicio2.equals("")) {
                    sql += " AND tspfechahoraevaluada >= "+dao.SQL_convertDate("datetime", dteFechaInicio2, "120");
                }
                if(!dteFechaTermino2.equals("")) {
                    sql += " AND tspfechahoraevaluada <= "+dao.SQL_convertDate("datetime", dteFechaTermino2, "120");
                }
//ASIGNADA
                if(!dteFechaInicio3.equals("")) {
                    sql += " AND tspfechahoraasignada >= "+dao.SQL_convertDate("datetime", dteFechaInicio3, "120");
                }
                if(!dteFechaTermino3.equals("")) {
                    sql += " AND tspfechahoraasignada <= "+dao.SQL_convertDate("datetime", dteFechaTermino3, "120");
                }
//ATENDIDA EN PROCESO (RESPONDIDA)
                if(!dteFechaInicio4.equals("")) {
                    sql += " AND tspfechahoraatendida >= "+dao.SQL_convertDate("datetime", dteFechaInicio4, "120");
                }
                if(!dteFechaTermino4.equals("")) {
                    sql += " AND tspfechahoraatendida <= "+dao.SQL_convertDate("datetime", dteFechaTermino4, "120");
                }
//ACEPTAR RESULTADOS
                if(!dteFechaInicio5.equals("")) {
                    sql += " AND tspfechahoraaceptacion >= "+dao.SQL_convertDate("datetime", dteFechaInicio5, "120");
                }
                if(!dteFechaTermino5.equals("")) {
                    sql += " AND tspfechahoraaceptacion <= "+dao.SQL_convertDate("datetime", dteFechaTermino5, "120");
                }
//CERRADA NO PROCEDE
                if(!dteFechaInicio6.equals("")) {
                    sql += " AND tspfechahoranoprocede >= "+dao.SQL_convertDate("datetime", dteFechaInicio6, "120");
                }
                if(!dteFechaTermino6.equals("")) {
                    sql += " AND tspfechahoranoprocede <= "+dao.SQL_convertDate("datetime", dteFechaTermino6, "120");
                }
//ULTIMA FECHA DE MODIFICACION
                if(!dteFechaInicioModificacion.equals("")) {
                    sql += " AND tspfechamodificacion >= "+dao.SQL_convertDate("datetime", dteFechaInicioModificacion, "120");
                }
                if(!dteFechaTerminoModificacion.equals("")) {
                    sql += " AND tspfechamodificacion <= "+dao.SQL_convertDate("datetime", dteFechaTerminoModificacion, "120");
                }
                //sql+="AND (intestado_control=10 OR intestado_control=6) ";

                String selects=" ";

                boolean reporteFinal = false;
                if(intEstado.equals("10") || intEstado.equals("6")) {
                    reporteFinal=true;
                    selects = ",AVG(tspfechahoraevaluada - tspfechahoracreacion ) AS promediodias,"
                            + "SUM(tspfechahoraevaluada - tspfechahoracreacion) AS numerodias,"
                            + "get_results" +
                            "( " +
                            "'SELECT DISTINCT b.vchnombrecompleto FROM tblusuario AS b INNER JOIN tblqueja AS a ON "
                            + "a.intworkerid = b.intusuarioid";
                }
                if (intGraficarPor.equals("4")) {
                    if(reporteFinal) {
                        selects += " AND a.intautorid='||from_usuario , 'vchnombrecompleto' " +
                            ") AS workers,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS avgnoprocede,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS sumnoprocede ";
                    }
                    sql = "SELECT from_usuario AS campo,COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY from_usuario";
                    //sql = "SELECT from_usuario,COUNT(*) AS cuenta FROM ("+sql+") AS final INNER JOIN tblqueja ON intautorid = from_usuario GROUP BY from_usuario";
                    titulo = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasporusuario");
                    valoresDe = tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Usuario");
                /*
                } else if (intGraficarPor.equals("3")) {
                    sql = "SELECT from_area AS campo, COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY from_area";
                    titulo = "Quejas levantadas por proceso";
                    valoresDe = "Proceso";
                } else if (intGraficarPor.equals("2")) {
                    sql = "SELECT from_ubicacion AS campo, COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY from_ubicacion";
                    titulo = "Quejas levantadas por departamento";
                    valoresDe = "Departamento";
                //*/
                } else if (intGraficarPor.equals("1")) {
                    if(reporteFinal) {
                        selects += " AND a.intestado_control='||intestado_control , 'vchnombrecompleto' " +
                            ") AS workers,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS avgnoprocede,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS sumnoprocede ";
                    }
                    sql = "SELECT intestado_control AS campo,COUNT(*) AS cuenta"+" FROM (" + sql + ") AS casiFinal GROUP BY intestado_control";
                    titulo = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasaporestados");
                    valoresDe = tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Estado");
                /*
                } else if (intGraficarPor.equals("5")) {
                    sql = "SELECT to_timestamp(tspfechamodificacion, 'YYYY:MM:DD') AS campo,COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY to_timestamp(tspfechamodificacion, 'YYYY:MM:DD'),to_ubicacion";
                    titulo = "Quejas levantadas a por fecha de modificación";
                    valoresDe = "Fecha";
                //*/
                } else if (intGraficarPor.equals("6")) {
                    if(reporteFinal) {
                        selects += " AND a.intubicacionid='||to_ubicacion, 'vchnombrecompleto' " +
                            ") AS workers,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS avgnoprocede,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS sumnoprocede ";
                    }
                    sql = "SELECT to_ubicacion AS campo,COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY to_ubicacion ";
                    titulo = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasadepartamentos");
                    valoresDe = tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Departamento");
                
                } else if (intGraficarPor.equals("8")) {
                    if(reporteFinal) {
                        selects += " AND a.intclasificacion='||intclasificacion, 'vchnombrecompleto' " +
                            ") AS workers,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS avgnoprocede,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS sumnoprocede ";
                    }
                    sql = "SELECT intclasificacion AS campo,COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY intclasificacion ";
                    titulo = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejasporclasificacion");
                    valoresDe = tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Clasificacion");
                }
                else {
                    if(reporteFinal) {
                        selects += " AND a.intfuenteid='||intfuenteid, 'vchnombrecompleto' " +
                            ") AS workers,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS avgnoprocede,"
                            + "avg(tspfechahoranoprocede-tspfechahoracreacion) AS sumnoprocede ";
                    }
                    sql = "SELECT intfuenteid AS campo,COUNT(*) AS cuenta"+selects+" FROM (" + sql + ") AS casiFinal GROUP BY intfuenteid ";
                    titulo = tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasafuentes");
                    valoresDe = tags.getString("isoblock.quejas.reporteQueja.comboGraficarPor.Fuente");
                }

                int totalCuenta = Integer.parseInt(findSimple("SELECT SUM(cuenta) FROM ("+sql+") AS cantidades").equals("") ? "1" : findSimple("SELECT SUM(cuenta) FROM ("+sql+") AS cantidades"));

                tabla += "<tr align='center'>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Campo")+"</b></td>\n";
                if (!intGraficarPor.equals("1") && reporteFinal) {
                    tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Responsable(s)")+"</b></td>\n";
                }
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas")+"</b></td>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Porcentaje")+"</b></td>\n";
                if (!intGraficarPor.equals("1") && reporteFinal) {
                    tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.#Dias")+"</b></td>\n";
                    tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.DiasPromediodeatencion")+"</b></td>\n";
                }
                tabla += "</tr>\n";
                total = 0;
                sql = "SELECT * FROM ("+sql+") AS final";
                System.out.println("SQL QUERY - " + sql);
                find(sql);
                while(next()){
                    int valor = fieldIntS("cuenta");
                    String nombre = "";
                    total += valor;

                    String dias="";
                    String workers="";
                    String diasProm="";
                    if (!intGraficarPor.equals("1") && reporteFinal) {
                        workers = ' ' + fieldStringS("workers");
                        if (!fieldStringS("avgnoprocede").equals("")) {
                            diasProm = fieldStringS("avgnoprocede");
                            dias = fieldStringS("sumnoprocede");
                        } else {
                            diasProm = fieldStringS("promediodias");
                            dias = fieldStringS("numerodias");
                        }
                        if (dias.contains("days")) {
                            dias = dias.replace("days", "día(s)");
                            dias = dias.substring(0, dias.indexOf("día(s)") + 6);
                        } else {
                            dias = "0 días";
                        }

                        if (diasProm.contains("days")) {
                            diasProm = diasProm.replace("days", "día(s)");
                            diasProm = diasProm.substring(0, diasProm.indexOf("día(s)") + 6);
                        } else {
                            diasProm = "0 días";
                        }
                        workers = workers.replace(",", "<br />");
                    }

                    System.out.println("cuenta: "+fieldIntS("campo"));
                    if(intGraficarPor.equals("4")) {
                        nombre = this.getUserName(fieldStringS("campo"));
                    } else if(intGraficarPor.equals("3")) {
                        nombre = this.getAreaName(fieldStringS("campo"));
                    } else if(intGraficarPor.equals("2")) {
                        nombre = this.getUbicacionName(fieldStringS("campo"));
                    } else if(intGraficarPor.equals("1")) {
                        System.out.println("--- "+fieldStringS("campo"));
                        nombre = super.getStringEstadoControlSimple(fieldStringS("campo"));
                    } else if(intGraficarPor.equals("5")) {
                        nombre = this.getFechaFormateada(fieldStringS("campo"));
                    } else if (intGraficarPor.equals("6")) {
                        nombre = this.getUbicacionName(fieldStringS("campo"));
                    } else if (intGraficarPor.equals("8")) {
                        nombre = getClassificationName(fieldStringS("campo"));
                    } else {
                        nombre = new isoblock.configuracion.catalogo().getCatalogoName(CATALOGO_QUEJAS,fieldStringS("campo"));
                    }
                    /*intestado=10 OR intestado=6 cambiar validaciones de fechas*/
                    categoryDataset.addValue(valor, nombre, nombre);
                    tabla += "<tr align='center'>\n";
                    tabla += "    <td>"+nombre+"</td>\n";
if(!intGraficarPor.equals("1") && reporteFinal) {
                    tabla += "    <td>"+workers+"</td>\n";
}
                    tabla += "    <td>"+valor+"</td>\n";
                    tabla += "    <td>"+formatter.format(valor*100.0/totalCuenta)+"%</td>\n";
if(!intGraficarPor.equals("1") && reporteFinal) {
                    tabla += "    <td>"+dias+"</td>\n";
                    tabla += "    <td>"+diasProm+"</td>\n";
}
                    tabla += "</tr>\n";
                }
                tabla += "<tr align='center'>\n";
                tabla += "    <td><b>"+tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas")+"</b></td>\n";
 if(!intGraficarPor.equals("1") && reporteFinal) {
               tabla += "    <td><b></b></td>\n";
 }
                tabla += "    <td><b>" + total + "</b></td>\n";
                tabla += "    <td><b>"+(total*100/totalCuenta)+"%</b></td>\n";
if(!intGraficarPor.equals("1")&& reporteFinal) {
                tabla += "    <td><b></b></td>\n";
}
                tabla += "</tr>\n";
                break;
            case 3: //GRAFICA_ENC_RESULTADOS
                break;
            default:
                System.out.println("Not plausible");
                break;
        }

        // Declaración de variables y gráfica predeterminada
        JFreeChart chart = org.jfree.chart.ChartFactory.createPieChart3D(tags.getString("isoblock.configuracion.user.ReportedeQuejas"),pieDataset,true,true,true);

        // crear la gráfica dependiendo en el tipo de gráfica
        if(tipoGrafica.equals(GRAFICA_QUE_ESTADOS)){
            chart = org.jfree.chart.ChartFactory.createPieChart3D(tags.getString("isoblock.configuracion.user.ReportedeQuejas"),pieDataset,true,true,true);
            PiePlot3D plot3D = (PiePlot3D) chart.getPlot();
            plot3D.setForegroundAlpha(0.65f);
            if(!imposibleGraficar)
                plot3D.setNoDataMessage(tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Noexisteinformacionparagraficar"));
            else
                plot3D.setNoDataMessage(tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio"));
            plot3D.setInteriorGap(0.2);
        }else{
            chart = org.jfree.chart.ChartFactory.createBarChart(titulo,valoresDe,tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas"),categoryDataset,PlotOrientation.VERTICAL,true,true,true);
            CategoryPlot plot = chart.getCategoryPlot();
            if(!imposibleGraficar)
                plot.setNoDataMessage(tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Noexisteinformacionparagraficar"));
            else
                plot.setNoDataMessage(tags.getString("isoblock.quejas.reporteQueja.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio"));
        }

        tabla +="</table>\n";

        String subtitulo = strGraficaX;
        org.jfree.chart.title.TextTitle subtitle1 = new org.jfree.chart.title.TextTitle(subtitulo);
        chart.addSubtitle(subtitle1);

        return chart;

    }
    
    public String getClassificationName(String intclasificacion) {
        String className = findSimple("SELECT vchtexto FROM tblcatalogo WHERE intcatalogoid='12' AND inttipoid = "+intclasificacion);
        return className.isEmpty() ? " - " : className;
    }
    
    public String getReportType() {
        return this.tipoGrafica;
    }

    public void setReportType(String ReportType) {
        this.tipoGrafica = ReportType;
    }

    public String getImagePath() {
        return this.imagePath;
    }

    public void setImagePath(String ImagePath) {
        this.imagePath = ImagePath;
    }

    public int getImageWidth() {
        return this.imageWidth;
    }

    public void setImageWidth(int ImageWidth) {
        this.imageWidth = ImageWidth;
    }

    public int getImageHeight() {
        return this.imageHeight;
    }

    public void setImageHeight(int ImageHeight) {
        this.imageHeight = ImageHeight;
    }

    @Override
    public String getIntQuejaId() {
        return this.intQuejaId;
    }

    @Override
    public void setIntQuejaId(String IntQuejaId) {
        this.intQuejaId = IntQuejaId;
    }

    public String getIntUbicacionId() {
        return this.intUbicacionId;
    }

    public void setIntUbicacionId(String IntUbicacionId) {
        this.intUbicacionId = IntUbicacionId;
    }

    public String getIntFromUbicacionId() {
        return this.intFromUbicacionId;
    }
    public void setIntFromUbicacionId(String IntFromUbicacionId) {
        this.intFromUbicacionId = IntFromUbicacionId;
    }

    public String getIntGraficarPor() {
        return this.intGraficarPor;
    }
    public void setIntGraficarPor(String IntGraficarPor) {
        this.intGraficarPor = IntGraficarPor;
    }

    public String getIntAreaId() {
        return this.intAreaId;
    }
    public void setIntAreaId(String IntAreaId) {
        this.intAreaId = IntAreaId;
    }

    public String getIntEstado() {
        return this.intEstado;
    }
    public void setIntEstado(String IntEstado) {
        this.intEstado = IntEstado;
    }

    public String getIntUsuarioId() {
        return this.intUsuarioId;
    }
    public void setIntUsuarioId(String IntUsuarioId) {
        this.intUsuarioId = IntUsuarioId;
    }

    //Tipo de gráfica
    public String getTipoGrafica(){
        return tipoGrafica;
    }

    public void setTipoGrafica(String tipoGrafica){
        this.tipoGrafica = tipoGrafica;
    }
    public String getTabla(){
        return tabla;
    }

    public void setTabla(String tabla){
        this.tabla = tabla;
    }

    @Override
    public void setDteFechaInicio1(String DteFechaInicio1){ this.dteFechaInicio1=DteFechaInicio1; }
    @Override
    public String getDteFechaInicio1(){ return this.dteFechaInicio1; }

    @Override
    public void setDteFechaTermino1(String DteFechaTermino1){ this.dteFechaTermino1=DteFechaTermino1; }
    @Override
    public String getDteFechaTermino1(){ return this.dteFechaTermino1; }

    public void setDteFechaInicio2(String DteFechaInicio2){ this.dteFechaInicio2=DteFechaInicio2; }
    public String getDteFechaInicio2(){ return this.dteFechaInicio2; }
    public void setDteFechaTermino2(String DteFechaTermino2){ this.dteFechaTermino2=DteFechaTermino2; }
    public String getDteFechaTermino2(){ return this.dteFechaTermino2; }

    public void setDteFechaInicio3(String DteFechaInicio3){ this.dteFechaInicio3=DteFechaInicio3; }
    public String getDteFechaInicio3(){ return this.dteFechaInicio3; }
    public void setDteFechaTermino3(String DteFechaTermino3){ this.dteFechaTermino3=DteFechaTermino3; }
    public String getDteFechaTermino3(){ return this.dteFechaTermino3; }

    public void setDteFechaInicio4(String DteFechaInicio4){ this.dteFechaInicio4=DteFechaInicio4; }
    public String getDteFechaInicio4(){ return this.dteFechaInicio4; }
    public void setDteFechaTermino4(String DteFechaTermino4){ this.dteFechaTermino4=DteFechaTermino4; }
    public String getDteFechaTermino4(){ return this.dteFechaTermino4; }

    public void setDteFechaInicio5(String DteFechaInicio5){ this.dteFechaInicio5=DteFechaInicio5; }
    public String getDteFechaInicio5(){ return this.dteFechaInicio5; }
    public void setDteFechaTermino5(String DteFechaTermino5){ this.dteFechaTermino5=DteFechaTermino5; }
    public String getDteFechaTermino5(){ return this.dteFechaTermino5; }

    public void setDteFechaInicio6(String DteFechaInicio6){ this.dteFechaInicio6=DteFechaInicio6; }
    public String getDteFechaInicio6(){ return this.dteFechaInicio6; }
    public void setDteFechaTermino6(String DteFechaTermino6){ this.dteFechaTermino6=DteFechaTermino6; }
    public String getDteFechaTermino6(){ return this.dteFechaTermino6; }

    public void setDteFechaInicioModificacion(String DteFechaInicioModificacion){ this.dteFechaInicioModificacion=DteFechaInicioModificacion; }
    public String getDteFechaInicioModificacion(){ return this.dteFechaInicioModificacion; }
    public void setDteFechaTerminoModificacion(String DteFechaTerminoModificacion){ this.dteFechaTerminoModificacion=DteFechaTerminoModificacion; }
    public String getDteFechaTerminoModificacion(){ return this.dteFechaTerminoModificacion; }


    public String getVchAreas(int index) {
        return this.vchAreas[index];
    }
    public String[] getVchAreas() {
        return this.vchAreas;
    }
    public void setVchAreas(int index, String vchAreas) {
        this.vchAreas[index] = vchAreas;
    }
    public void setVchAreas(String[] vchAreas) {
        this.vchAreas = vchAreas;
    }

    public String getVchUbicaciones(int index) {
        return this.vchUbicaciones[index];
    }
    public String[] getVchUbicaciones() {
        return this.vchUbicaciones;
    }
    public void setVchUbicaciones(int index, String vchUbicaciones) {
        this.vchUbicaciones[index] = vchUbicaciones;
    }
    public void setVchUbicaciones(String[] vchUbicaciones) {
        this.vchUbicaciones = vchUbicaciones;
    }

}
