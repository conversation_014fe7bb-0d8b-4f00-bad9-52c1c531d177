/*
 * Properties.java
 **
 * Created on June 20, 2003, 9:45 AM!
 */

package isoblock.common;
// JAXP DOM imports

import DPMS.Mapping.ActionSources;
import DPMS.Mapping.Settings;
import Framework.Action.SessionViewer;
import Framework.Config.Mail;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import bnext.reference.UserRef;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Objects;
import java.util.ResourceBundle;
import jakarta.servlet.http.HttpSession;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.w3c.dom.Document;
import qms.framework.util.AboutApp;
import qms.framework.util.LocaleUtil;
import qms.framework.util.SettingsUtil;

/**
 *
 * 
 * @deprecated Utlizar Utilites.getSettings() para cosas de configuración.
 * <AUTHOR> @version
 */
@Deprecated
public class Properties {
    
    private Logger logger;
    
    protected final Logger getLogger() {
        if(logger == null) {
            logger = Loggable.getLogger(this.getClass());
        }
        return logger;
    }
    
     
    protected static boolean xmlLoaded = false;
    public static int LICENSES = 0; //cualquier valor menor a 0 cuenta como ilimitada
    public static String SYSTEM_ID = "Quality Management System"; //
    public static String LOGO = "../../images/logo/logoDPMS.png"; // 
    public static String LOGO_FOOTER_L = "../../images/footer/footer_left.png"; //
    public static String LOGO_FOOTER_R = "../../images/footer/footer_right.png"; //
    
    //variables de validacion
    public static String ALECTORES_OK = ""; //
    public static String VISOR_PDF_OK = ""; //

    Document document;

    public Locale localidad;
    public ResourceBundle tagsParam;
    public ResourceBundle tags2Param;
    public ResourceBundle newTagsParam;

    public ResourceBundle formatPropParam;

    //Tags con control de errores
    public Letreros tags;
    public Letreros tags2;
    public Letreros newTags;
    public Letreros formatProp;

    //Caracteristicas del idioma

    public static String IDIOMA;
    public static String PAIS;
    public static String STRING_SYSTEM_ID;

    //Caracteristicas del Administrador
    public static String ADMINISTRATOR_MAIL;

    //DNS del servidor, usuario y clave de acceso de la base de datos
    public static String DATABASE_USER;
    //public static final String SERVER="isoblockdb";
    public static String DATABASE_PASSWORD;
    //Variable boleana que sirve para determinar si el password de la base de datos es NULO
    public static String bolNulo;
    //Localizaciï¿½n de la base de datos y su nombre
    public static String DATABASE_SERVER;
    //Nombre de la base de datos
    public static String DATABASE_NAME;
    //Puerto de la base de datos
    public static String DATABASE_PORT;
    //URL del server de la base de datos
    public static String DATABASE_URL;
    //MENSAJE DE BIENVENIDA
    public static String WELCOME_MESSAGE;
    //COLOR DEL SISTEMA
    public static String COLOR_SISTEMA;
    //Perfil predeterminado de primer ingreso
    public static String POSITION;
    //DOC_VERSION
    public static String DOC_VERSION = "1";
    //Ruta del postgres
    public static String POSTGRES_PATH;
    //Ruta del 7-zip
    public static String ZIP7_PATH;
    public static String ARRAY_SEPARATOR = "<,>";
    /**
     * Te dice si el DPMS esta o no configurado para forzar lecturas(descargas) de documentos
     */
    public static boolean ASIGNAR_LECTORES = false;
    public String ENTITY_QUANTITY = "10";

    public static String TO_PDF;
    public static boolean CONSERVA_ORIGINAL_TRAS_CONVERSION;
    public static String MS_WORD_PATH;
    public static String USE_MS_CONVERSION;
    public static String TO_PDF_WORD = "";
    public static String TO_PDF_EXCEL = "";
    public static String TO_PDF_PP = "";
    //variables para guardado externo
    public static String UPLOAD_FOLDER_DOCUMENTOS_EXT;
    public static String UPLOAD_FOLDER_SOLICITUDES_EXT;
    public static String UPLOAD_FOLDER_IMG_REPORTES_EXT;
    public static String UPLOAD_FOLDER_LISTAS_EXT;
    public static String UPLOAD_FOLDER_RESPALDOS_EXT;
    public static String SITE_FOLDER_NAME;
    public static String SITE_HOST;
    public static String FILES_CONTEXT;
    public static String DIR_PDFS;
    public static String PDF_VIEWER;

    //Configuracion de mail de difusion (Mail enviado al terminar una secuencia)
    public static String DIFUSION_MAIL_AUTOR;
    public static String DIFUSION_MAIL_AUTORIZANTES;
    public static String DIFUSION_MAIL_AUTORIZANTE_PLANTA;
    public static String DIFUSION_MAIL_PROCESO_CARPETA;
    public static String DIFUSION_MAIL_USUARIO_CARPETA;
    public static String DIFUSION_MAIL_MANAGER_DOCUMENTOS;

    //Localizaciï¿½n de las carpetas
    public static String DOCUMENTS_PATH;

    /** Localización de las carpetas de documentos de solicitudes*/
    public static String SOLICITUDES_PATH;

    /** Localizaciï¿½n de las carpetas de documentos de listas para alta masiva de usuarios*/
    public static String LISTAS_PATH;

    /** Localizaciï¿½n de las carpetas de documentos de imagenes de reportes*/
    public static String IMG_REPORTES_PATH;

    /** Localización donde se guardan los respaldos de la base de datos*/
    public static String RESPALDOS_PATH;

    /** Localización donde se guarda el respaldo de documentos*/
    public static String RESPALDO_EXTERNO_PATH;

    //Localizaciï¿½n de las carpetas
    public static String SITE_URL;

    //llave que utiliza el algoritmo RC4 para encriptar y desencriptar
    public static String RC4_KEY;

    //es el tiempo de vida en dias de las ligas del correo
    public static String MAIL_LIFETIME;

    // Variable que indica si se envian correos o no
    public static String ENVIO_CORREOS;
    public static boolean ENVIAR_CORREOS;

    public static String ASUNTO_CORREOS;

    //Variable que indica el nï¿½mero de registros que se desplegaran en
    //los resultados de las bï¿½squedas
    public static String REG_DOC;

    //Variable que indica cual dia se realizara el 1er
    //respaldo de la base de datos automaticamente
    public static String FECHA_RESPALDO;
    //Indica cada cuando se realizara un respaldo de la base de datos
    public static String PERIODO_RESPALDO;
    //Dia en el que se genera el respaldo de la base de datos automaticamente
    public static String DIA_RESPALDO;

    // Titulo del repositorio en el que se van a crear respaldos de la base datos y documentos
    public static final String REPOSITORIO_RESPALDOS="Respaldos de Base de Datos";
    
    //Programa para Conversión a PDF
    
    //LibreOffice
    public static final String LIBREOFFICE = "libreoffice";
    
    //Microsof Office
    public static final String MICRSOFT_OFFICE = "ms_office";

    // Nodo Padre de los proyectos
    public static final String NODO_CANCEL_RESTORE = "637";

    // Nodo Padre de los proyectos
    public static final String NODO_PROYECTOS = "8";

    // Nodo Padre de las acciones
    public static final String NODO_ACCIONES = "9";

    //Nodo Padre de las Documentos electronicos
     public static final String NODO_DOC_ELECTRONICO = "11";

    //Nodo Padre de las Quejas
     public static final String NODO_QUEJAS = "12";
    //Nodo Padre de las Quejas
     public static final String NODO_RAIZ = "0";

     //TODO: FALTA AGREGARLO (CATALOGOS) EN TODAS LAS PARTES DONDE SE UTILIZA

     //ESTADOS CATALOGOS
     public static final String CATALOGO_ACTIVO = "4";
     public static final String CATALOGO_INACTIVO = "6";
     //ESTADOS CATALOGOS
     public static final String VALIDATION_ON_ACTIONS = "5";
     //TIPOS DE CATALOGOS
     public static final String CATALOGO_CUESTIONARIO_CALIFICACIONES = "1";
     public static final String CATALOGO_ACCIONES = "4"; //ID DE CATALOGO FUENTE DE ACCIONES
     public static final String CATALOGO_QUEJAS = "5";
     public static final String CATALOGO_TIPO_DOCUMENTO = "7";
     public static final String CATALOGO_ESTADOS_AUDITORIAS = "8";
     public static final String CATALOGO_ESTADO_USUARIOS = "9";
     public static final String ESTADOS_CATALOGO_AUDITORIAS = "11"; //ID DE CATALOGO DE ESTADO DE AUDITORIAS ACTIVA E INACTIVA
     public static final String CATALOGO_TEXTO_QUEJA = "12";
     public static final String CATALOGO_LUGAR_ALMACENAMIENTO = "13";
     public static final String CATALOGO_CLASIFICACION_INFORMACION = "14";
     public static final String CATALOGO_DISPOSICION = "15";
     //CATALOGO ESTADO DE USUARIOS
     public static final String USUARIO_INACTIVO = "" + DPMS.Mapping.User.INACTIVE_STATUS;
     public static final String USUARIO_ACTIVO = "" + DPMS.Mapping.User.ACTIVE_STATUS;
     public static final String USUARIO_PORREGISTRAR = "" + DPMS.Mapping.User.TO_REGISTER_STATUS;

     //ESTADOS DE SECCIONES
     public static final String CATALOGO_SECTION="10";
     public static final String ACTIVE_SECTION="1";
     public static final String INACTIVE_SECTION="0";

     //ESTADO DE USUARIOS INVITADOS
     public static final String USER_ACTIVE="1";

     //ESTATUS DE JSP'S
     public static final String JSP_EDITAR = "edit";
     public static final String JSP_ALTA = "juliet";
     //public static final String JSP_EDITAR = "edit";

     //ENTIDADES
     public static final String ENTIDAD_UNIDAD_DE_NEGOCIO = "1";
     public static final String ENTIDAD_DEPARTAMENTO = "2";
     public static final String ENTIDAD_PROCESO = "3";
     public static final String ENTIDAD_SUBPROCESO = "3";
     public static final String ENTIDAD_AREA = "4";
     public static final String ENTIDAD_USUARIO = "5";

     //llave, tag ,liga(link) DE LOS MENUS
     public static final String INDEX_MENU_KEY = "0";
     public static final String INDEX_MENU_TAG = "1";
     public static final String INDEX_MENU_LINK = "2";

     public static final String[] DOCUMENTOS = {"1","menu.folderTreeLeftFrame.Documentos",""};
     public static final String[] DOCUMENTOS_PROYECTOS = {"2","menu.folderTreeLeftFrame.Proyectos",""};
     public static final String[] DOCUMENTOS_ACCIONES = {"3","menu.folderTreeLeftFrame.Acciones",""};
     public static final String[] DOCUMENTOS_SOLICITUDES = {"5","menu.folderTreeLeftFrame.Solicitudes","view/v.request.view"};
     public static final String[] DOCUMENTOS_LISTA_DE_SOLICITUDES = {"6","menu.folderTreeLeftFrame.Listadesolicitudes","view/v.request.list.view"};
     public static final String[] DOCUMENTOS_LISTA_MAESTRA = {"7","menu.folderTreeLeftFrame.ListaMaestra","view/v.document.master.list.view"};
     public static final String[] DOCUMENTOS_LISTA_MAESTRA_DE_REGISTROS = {"8","menu.folderTreeLeftFrame.ListaMaestraRegistros","view/v.document.master.records.list.view"};
     public static final String[] DOCUMENTOS_LISTA_DE_RELACIONES = {"9","menu.folderTreeLeftFrame.ListaRelaciones","view/v.related.document.list.view"};
     public static final String[] DOCUMENTOS_PAPELERA = {"0","menu.folderTreeLeftFrame.Papelera","view/v.document.papelera.view"};

     public static final String[] QUEJAS_ALTAS = {"1","menu.folderTreeLeftFrame.Alta","view/v.complaint.view"};
     public static final String[] QUEJAS_CONTROL = {"2","menu.folderTreeLeftFrame.ConfiguracionQuejasControl","view/v.complaint.list.view"};
     public static final String[] QUEJAS_MISQUEJAS = {"3","menu.folderTreeLeftFrame.ConfiguracionMisQuejas","view/v.my.complaint.view"};
     public static final String[] QUEJAS_CATALOGOS = {"4","menu.folderTreeLeftFrame.Catalogo",""};


     //STATUS DE REUNIONES RECURRENTES
     public static final String RECURRENCIA_ACTIVA = "1";
     public static final String RECURRENCIA_INACTIVA = "2";

    //******Estos parï¿½metros no deberï¿½n modificarse sin el conocimiento que lo requiere.*******
    //Estatus tomados de la base de dtos.

    //STATUS ACCION DE MEJORA CONTINUA
    public static final String AMC_REPORTADA = "1";      //usuario
    public static final String AMC_ASIGNADA = "2";       //responsable de acciones
    public static final String AMC_DEFINIDA = "3";       //responsable de la accion de mejora continua
    public static final String AMC_IMPLEMENTADA = "4";   //responsable de la accion de mejora continua
    public static final String AMC_VERIFICADA = "5";     //responsable de evaluacion
    public static final String AMC_CERRADA = "6";        //responsable de la accion

    //STATUS ACCION PREVENTIVA
    public static final String APP_REPORTADA = "1";      //usuario
    public static final String APP_ASIGNADA = "2";       //responsable de acciones
    public static final String APP_DEFINIDA = "3";       //responsable de la accion preventiva
    public static final String APP_IMPLEMENTADA = "4";   //responsable de la accion preventiva
    public static final String APP_VERIFICADA = "5";     //auditor responsable de verificar
    public static final String APP_CERRADA = "6";        //responsable de la accion

    //STATUS DE ACCIONES A TOMAR
    public static final String AAT_CREADA = "1";
    public static final String AAT_EN_PROCESO = "2";
    public static final String AAT_IMPLEMENTADA = "3";
    public static final String AAT_VERIFICADA = "4";

    //STATUS DE ACCIONES CORRECTIVAS
    public static final String AGG_REPORTADA = "1";      //auditor
    public static final String AGG_ASIGNADA = "2";       //responsable de acciones
    public static final String AGG_ANALIZADAYENIMPLEMENTACION = "3";   //ANALIZADA Y EN IMPLEMENTACION   responsable de la accion correctiva //ANALIZADA
    public static final String AGG_IMPLEMENTADAYVERIFICADA = "4";     //auditor responsable de verificar //EJECUTADA
    public static final String AGG_APROBADA = "5";       //responsable de la accion preventiva
    public static final String AGG_CERRADA = "6";
    public static final String AGG_PROCEDE = "7";
    public static final String AGG_NOPROCEDE = "8";
    public static final String AGG_CANCELADA = "9";

    //STATUS DE ACCIONES DE HALLAZGOS
    public static final String AGG_OVERDUE = "3";
    public static final String AGG_ATTENDED = "2";
    public static final String AGG_EXPIRED = "1";
    public static final String AGG_ONTIME = "0";
    /**
     * Estatus de acciones que no aplican directo a la columna intestado, estos estados no existe tal cual
     * cada uno es el conjunto de el estado AGG_CERRADA mas ciertas condiciones en las que se cerro la accion
     */
    public static final String AGG_CERRADA_EFECTIVA = "12";
    public static final String AGG_CERRADA_NOEFECTIVA = "13";
    public static final String AGG_CERRADA_CANCELADA = "14";

    public static final String AGG_ATT_POR_REALIZAR = "10";
    public static final String AGG_ATT_POR_VERIFICAR = "11";


    //STATUS DE DOCUMENTOS
    public static final String DOC_PROYECTO = "-1";
    public static final String DOC_EN_AUTORIZACION = "1"; //ES ESTADO DE REVISION
    public static final String DOC_ACTIVO= "2";
    public static final String DOC_EN_EDICION= "3";
    public static final String DOC_CANCELADO= "4";
    public static final String DOC_DESCONTINUADO="5";
    public static final String DOC_NOCONTROLADOELIMINADO1 = "-4";
    public static final String DOC_NOCONTROLADOELIMINADO2 = "-99";
            //TIPOS DE PENDIENTES DE DOCUMENTOS
    public static final String DOC_SIN_LECTORES="0";
    public static final String DOC_CON_LECTORES="1";

    //LEIDO Y SIN LEER
    public static final String DOC_LEER="0"; //Documento no leido y el usuario tiene el pendiente de documentos por leer
    public static final String DOC_LEIDO="1"; //Documento leido cuando el usuario no tenia el pendiente de documentos
                                              //por leer pero de todas maneras, ha leido el documento
    public static final String DOC_LEIDO_FORZADO="2"; //Documento leido cuando el usuario tenia el pendiente de documentos por leer

    //STATUS DE INDICADORES
    public static final String INDICADOR_INACTIVO = "0";
    public static final String INDICADOR_ACTIVO = "1";

    public static final String INDICADOR_CREADO = "1";
    public static final String INDICADOR_CALIFICADO = "2";
    public static final String INDICADOR_REVISADO = "3";
    public static final String INDICADOR_CERRADO = "4";

    //STATUS LEVANTAMIENTOS INDICADORES
    public static final String INDICADOR_LEV_PROGRAMADA  = "1"; //naranja
    public static final String INDICADOR_LEV_POR_CALIFICAR = "2"; //verde
    public static final String INDICADOR_LEV_CALIFICADA  = "3"; //azul
    public static final String INDICADOR_LEV_REVISADA = "4"; //gris

    //STATUS REVISIONES INDICADORES
    public static final String INDICADOR_REV_PROGRAMADA  = "1"; //naranja
    public static final String INDICADOR_REV_EN_ESPERA = "2"; //amarillo
    public static final String INDICADOR_REV_POR_REVISAR  = "3"; //verde
    public static final String INDICADOR_REV_REVISADA = "4"; //gris

    //STATUS DE ACTIVIDADES
    public static final String ACT_CREADA = "1";
    public static final String ACT_EN_PROCESO= "2";
    public static final String ACT_CONCLUIDA= "3";
    public static final String ACT_VERIFICADA= "4";

    //STATUS DE METAS
    public static final String MET_CREADA = "1";
    public static final String MET_EN_PROCESO= "2";
    public static final String MET_CONCLUIDA= "3";

    //STATUS DE CUESTIONARIOS
    public static final String CUE_BORRADOR = "1";
    public static final String CUE_ACTIVO = "2";
    public static final String CUE_CANCELADO = "3";

    //STATUS DE PREGUNTAS
    public static final String PRE_ACTIVA = "1";
    public static final String PRE_CANCELADA = "2";

    //STATUS DE AUTORIZACION
    public static final String AUT_PROGRAMADA = "1";
    public static final String AUT_EN_PROCESO = "2";
    public static final String AUT_AUTORIZADA = "3";
    public static final String AUT_NO_AUTORIZADA = "4";

    //STATUS DE SECUENCIA DE AUTORIZACIONES
    public static final String SECAUT_ABIERTA = "1";
    public static final String SECAUT_CERRADA = "2";

    //STATUS DE COMENTARIOS
    public static final String COM_NUEVO = "1";
    public static final String COM_LEIDO = "2";
    public static final String COM_CONTESTADO = "3";

    //STATUS DE QUEJAS CONTROL (LEADER Y WORKER)
    public static final String QUEJA_REPORTADA_POR_ASIGNAR = "1"; //reportada por asignar responsable
    public static final String QUEJA_REPORTADA_ASIGNADA = "2"; //asignada
    //public static final String QUEJA_REPORTADA_ASIGNADA_POR_ATENDER = "3";
    public static final String QUEJA_ATENDIDA_EN_IMPLEMENTACION = "3"; //atendida (en proceso)

    public static final String QUEJA_ATENDIDA_IMPLEMENTADA = "4"; //------------------no se us

    public static final String QUEJA_IMPLEMENTADA_POR_EVALUAR = "5"; //Por aceptar resultados
    public static final String QUEJA_NO_PROCEDE = "6"; //cerrada no procede
    public static final String QUEJA_EVALUADA = "10"; //Atendida (finalizada)

    //STATUS QUEJAS ALUMNO
    public static final String QUEJA_REPORTADA = "1";
    public static final String QUEJA_EN_PROCESO = "2";
    public static final String QUEJA_ATENDIDA_POR_EVALUAR = "3";
    public static final String QUEJA_FINALIZADA = "10";


    //TIPOS DE COMENTARIOS
    public static final String COM_TIPO_HALLAZGO = "1";
    public static final String COM_TIPO_COMENTARIO = "2";
    public static final String COM_TIPO_MINUTA = "3";

    public static final String SPAN_TIPO_DIA = "1";
    public static final String SPAN_TIPO_MES = "2";
    public static final String SPAN_TIPO_ANIO = "3";
    /**
     * AUDITORIA PLANEADA
     * Se utiliza para las anticipaciones de pendientes
     * mientras la auditoria se encuentre en este estatus no puede ser usada por nadie ni
     * es visible en las listas de "Mis Auditorias", solo puede editarse por
     * - El usuario que la subio.
     * - Es visible en Control para todos los encargados de auditorias
     *
     * Luis Limas
     * 17/12/2009
     * */
    //STATUS DE AUDITORIAS
    public static final String AUD_PLANEADA = "1";  //PLANEADA
    public static final String AUD_PROGRAMADA = "2"; //PROGRAMADA
    /**
     * AUDITORIA CONFIRMADA
     */
    public static final String AUD_AUTORIZADA = "3"; //CONFIRMADA
    /**
     * AUDITORIA REALIZADA
     */
    public static final String AUD_AUTORIZADA_CON_AVISOS = "4"; //REALIZADA
    /**
     * AUDITORIA ACEPTADA
     */
    public static final String AUD_REALIZADA = "5"; //ACEPTADA
    public static final String AUD_CERRADA = "6"; //CERRADA
    public static final String AUD_CANCELADA = "7"; //CANCELADA
    public static final String AUD_ELIMINADA = "8"; //TODO: MEJORA A FUTURO, AGREGAR UNA PAPELERA AL MODULO Y PONER TODO LO QUE SE BORRA AHI

	//Estados de borrado y activo en el DPMS
    public static final String DELETED = "1";//MEJORA EN LA ADMINISTRACION DE ARCHIVOS SE AGREGO LA COLUMNA DE INTDELETED
    public static final String ACTIVE ="0";

    /***
     * EN CASO DE QUE SE REQUIERA MIGRAR UNA VERSION VIEJA A ESTA, SE DEBEN CAMBIAR ESTOS VALORES EN LA BASE DE DATOSPOR LOS QUE HAY EN ESTA VERSION
    public static final String AUD_PROGRAMADA = "1"; //no se usaba
    public static final String AUD_AUTORIZADA = "2";
    public static final String AUD_AUTORIZADA_CON_AVISOS = "3";
    public static final String AUD_REALIZADA = "4";
    public static final String AUD_CERRADA = "5";
     */

    //STATUS DE PROYECTOS
    public static final String PRO_CREADO = "1";
    public static final String PRO_LIBERADO = "2";
    public static final String PRO_CONCLUIDO = "3";
    public static final String PRO_CERRADO = "4";

    //STATUS DE REUNIONES
    public static final String REU_PROGRAMADA = "1";
    public static final String REU_PROGRAMADA_CON_AVISOS = "2";
    public static final String REU_REALIZADA = "3";
    public static final String REU_CERRADA = "4";

    //PERMISOS ENCUESTAS
    public static final String ENC_PERMISO_RESPONDER = "1";
    public static final String ENC_PERMISO_EDITAR = "2";

    //STATUS ENCUESTAS
    public static final String ENC_PROGRAMADA = "3";
    public static final String ENC_ACTIVA = "4";
    public static final String ENC_CERRADA = "5";
    public static final String ENC_INACTIVA = "6";
    public static final String ENC_POR_CERRAR = "7";
    public static final String ENC_CANCELADA = "11";

    //STATUS MIS ENCUESTAS
    public static final String ENC_POR_RESPONDER = "4";
    public static final String ENC_RESPONDIDA = "5";

    //TIPO EVALUACION
    public static final String ES_ENCUESTA = "1";
    public static final String ES_FORMATO_ELECTRONICO = "2";

    //TIPOS DE SOLICITUDES
    public static final String SOL_DOCUMENTO_NUEVO = "1";
    public static final String SOL_MODIFICACION = "2";
    public static final String SOL_REAPROBACION = "3";
    public static final String SOL_CANCELACION = "4";

    //RESULTADOS DE LAS ACCIONES PREVENTIVAS, CORRECTIVAS, MEJORA CONTINUA
    public static final String CUMPLE = "1";
    public static final String NO_CUMPLE = "2";
    public static final String EFECTIVA = "1";
    public static final String NO_EFECTIVA = "2";
    public static final String CERRADA = "1";
    public static final String ABIERTA = "2";

    //RESULTADOS DE Procede
    public static final String PROCEDE = "1";
    public static final String NO_PROCEDE = "0";

    //RESULTADOS DE VERIFICACION
    public static final String AAT_CUMPLE = "1";
    public static final String AAT_NO_CUMPLE = "2";
    //RESULTADOS DE ANALISIS
    public static final String AAT_EFECTIVA = "1";
    public static final String AAT_NO_EFECTIVA = "2";

    //TIPOS DE RESPUESTAS
    public static final String RES_SI = "1";
    public static final String RES_NO = "2";
    public static final String RES_NA = "3";

    //TIPOS DE CAMBIOS
    public static final String TIPO_CAMBIO_FECHA_INICIO = "0";
    public static final String TIPO_CAMBIO_FECHA_FIN = "1";
    public static final String TIPO_CAMBIO_VERIFICADOR = "2";
    public static final String TIPO_CAMBIO_FECHA_VERIFICADOR = "3";

    //TIPO DE AVANCES
    public static final String AVANCE_AUTOR = "0";
    public static final String AVANCE_RESPONSABLE = "1";
    public static final String AVANCE_VERIFICADOR = "2";

    //STATUS DE AUTORIZACION DE CAMBIO EN ACTIVIDAD
    public static final String CAMBIO_PENDIENTE = "0";
    public static final String CAMBIO_AUTORIZADO = "1";
    public static final String CAMBIO_NO_AUTORIZADO = "2";

    //STATUS DE AUTORIZACION DE PETICION DE PRESUPUESTO
    public static final String PETICION_PENDIENTE = "0";
    public static final String PETICION_AUTORIZADO = "1";
    public static final String PETICION_NO_AUTORIZADO = "2";


    //STATUS GENERALES

    // USER TYPES
    public static final String ADMINISTRADOR_MAESTRO="1";
    public static final String REPOSITORIO_NO_ACCESO = "0";
    public static final String REPOSITORIO_LECTOR = "1";
    public static final String REPOSITORIO_EDITOR = "2";
    public static final String REPOSITORIO_MANAGER = "3";
    public static final String ACCIONES_NO_ACCESO = "0";
    public static final String ACCIONES_LECTOR = "1";
    public static final String ACCIONES_EDITOR = "2";
    public static final String ACCIONES_MANAGER = "3";
    public static final String REUNIONES_MANAGER = "3";
    public static final String AUDITORIAS_NO_ACCESO = "0";
    public static final String AUDITORIAS_LECTOR = "1";
    public static final String AUDITORIAS_EDITOR = "2";
    public static final String AUDITORIAS_MANAGER = "3";
    public static final String DOCUMENTOS_NO_ACCESO = "0";

    public static final String DOCUMENTOS_LECTOR = "1";
    public static final String DOCUMENTOS_EDITOR = "2";
    public static final String DOCUMENTOS_MANAGER = "3";

    public static final String VALIDATIONS_NO_ACCESS = "0";
    public static final String VALIDATIONS_LECTOR = "1";
    public static final String VALIDATIONS_EDITOR = "2";
    public static final String VALIDATIONS_MANAGER = "3";

    public static final String ENCUESTAS_NO_ACCESO = "0";
    public static final String ENCUESTAS_LECTOR = "1";
    public static final String ENCUESTAS_EDITOR = "2";
    public static final String ENCUESTAS_MANAGER = "3";
    public static final String INDICADORES_NO_ACCESO = "0";
    public static final String INDICADORES_LECTOR = "1";
    public static final String INDICADORES_EDITOR = "2";
    public static final String INDICADORES_MANAGER = "3";
    public static final String PROYECTOS_NO_ACCESO = "0";
    public static final String PROYECTOS_LECTOR = "1";
    public static final String PROYECTOS_CONTABILIDAD = "2";
    public static final String PROYECTOS_EDITOR = "3";
    public static final String PROYECTOS_MANAGER = "4";
    public static final String QUEJAS_NO_ACCESO = "0";
    public static final String QUEJAS_ALUMNO = "1";
    public static final String QUEJAS_LECTOR = "2";
    public static final String QUEJAS_WORKER = "3";
    public static final String QUEJAS_LEADER = "4";

    //IDS DE MODULOS... POR LO PRONTO SOLO SE UTILIZA PARA RELACIONAR FUENTES DE ACCIONES A ALGUN MODULO
    /**
     * Por lo pronto solo se utilizan para relacionar FUENTES DE ACCIONES
     * a algun modulo, solo se utilizan los que tienen flechitas
     * **/
    public static final int SIN_MODULO = 10;
    private String intModuloId = "";
    public static final int MODULO_AUDITORIAS = 1;              //<---
    //public static final int MODULO_ACCIONES = 3;
    //public static final int MODULO_DOCUMENTOS = 4;
    //public static final int MODULO_ENCUESTAS = 5;
    public static final int MODULO_QUEJAS = 6;                  //<---
    public static final int MODULO_INDICADORES = 7;             //<---
    //public static final int MODULO_PROYECTOS = 8;
    public static final int MODULO_AUDITORIAS_PREGUNTAS = 9;
    public static final int MODULO_VALIDACIONES = 10;    //<---
    public static final int MODULO_FORMULARIOS = ActionSources.MODULE_FORMS;

    //COLORES DE CUBOS
    public static final String COLOR_ROJO = "1";
    public static final String COLOR_AMARILLO = "2";
    public static final String COLOR_NARANJA = "3";
    public static final String COLOR_VERDE = "4";
    public static final String COLOR_AZUL = "5";
    public static final String COLOR_GRIS = "6"; //INACTIVO
    public static final String COLOR_MORADO = "7";
    public static final String COLOR_LIMA = "8";
    public static final String COLOR_FOREST = "9"; //ELIMINADA
    public static final String COLOR_MARRON = "10"; //CERRADA
    public static final String COLOR_NEGRO = "11"; //CANCELADA
    public static final String COLOR_AZULM = "12";

    //ESTATUS DE SOLICITUDES
    public static final String SOL_SOLICITADA = "3"; //3
    public static final String SOL_EN_PROCESO = "6"; //6
    public static final String SOL_ATENDIDA = "2"; //2
    public static final String SOL_RECHAZADA = "5"; //5
    public static final String SOL_CERRADA = "7"; //7
    public static final String SOL_RETORNADA = "4";//4

    //LAS SIGUIENTES CONDICIONES SON DE 1 / 0
    public static final String REPORTES_NO_ACCESO= "0";
    public static final String REPORTES_CONFIGURACION = "0";
    public static final String REPORTES_AUDITORIAS = "0";
    public static final String REPORTES_CUESTIONARIOS = "0";
    public static final String REPORTES_REUNIONES = "0";
    public static final String REPORTES_ACCIONES = "0";
    public static final String REPORTES_DOCUMENTOS = "0";
    public static final String REPORTES_PROYECTOS = "0";
    public static final String REPORTES_GERENCIAL = "0";

    /** Holds value of property orderBy. */
    public String orderBy="";

    /** Holds value of property ascDesc. */
    public String ascDesc="";

    /** Holds value of property orderByCat2. */
    public String orderByCat2="";

    /** Holds value of property ascDesc. */
    public String ascDescCat2="";

    /** Holds value of property ascDesc. */
    public static final String INDEX_URL = "/index2.jsp?cURL=aux";
    private String currentURL = SITE_URL + INDEX_URL;

    // Tipos de correo
    public static final String CORREO_AUDITORIA = "0";
    public static final String CORREO_SECUENCIA = "2";
    public static final String CORREO_AVISO = "3";
    public static final String CORREO_GENERAL = "4";
    public static final String CORREO_INDICADORES = "5";
    public static final String CORREO_SECUENCIA_NO_AUTORIZADA = "6";
    public static final String CORREO_ENCUESTA = "7";


    public static Mail MAIL_DPMS;

    // --- Propiedades de la conexion al servidor de SMTP
    @Deprecated
    public static String SMTP_HOST;
    @Deprecated
    public static String SMTP_UID;
    @Deprecated
    public static String SMTP_PWD;
    @Deprecated
    public static String SMTP_PORT;
    
    // ---

    // Propiedades de formato
    public String SOL_FECHA;
    public String SOL_REVISION;
    public String SOL_CLAVE;
    public String AUD_FECHA;
    public String AUD_REVISION;
    public String AUD_CLAVE;
    public String PREG_FECHA;
    public String PREG_REVISION;
    public String PREG_CLAVE;

    public String AMC_FECHA;
    public String AMC_REVISION;
    public String AMC_CLAVE;

    public String APP_FECHA;
    public String APP_REVISION;
    public String APP_CLAVE;

    public String ACC_FECHA;
    public String ACC_REVISION;
    public String ACC_CLAVE;
    public static String DATABASE_DRIVER;
    private boolean admin = false;
    private ProfileServices[] servicios;

    public static HashMap<Long, UserRef> usersCofig = new HashMap<>();

    public static String PROCEDEN_ACCIONES_DEFAULT;

    public Properties() {
        //Todas las variables cargadas pos loadCommonData ahora son estaticas por lo que no tiene sentido leerlas una segunda vez
        //En teoria esto debe de aumentar la efficiencia de creado de TODAS las clases del DPMS en mas de un 200%
        getLogger().trace("isoblock.common.Properties.Properties() ... Properties.xmlLoaded : " + Properties.xmlLoaded);
        if (!Properties.xmlLoaded) {
          this.loadCommonData();
        }
        SessionViewer sv = null;
        try {
            sv = new SessionViewer(false);
        } catch (Exception e) {
            getLogger().trace("Session is closed");
            getLogger().error("There was an error ", e);
        }
        if (sv != null) {
            this.loadPersonalData(sv.getLoggedUserId()); // 0 es para leer datos Defaults nunca debe existir un usuario con el id 0
        } else {
            this.loadPersonalData(0L); // 0 es para leer datos Defaults nunca debe existir un usuario con el id 0
        }
      }

    public Properties(int id) {
        if (!Properties.xmlLoaded) {
            this.loadCommonData();// Siempre es defualt esta parte
        }
        this.loadPersonalData(Long.valueOf(id));
    }
    public Properties(Long id) {
        if (!Properties.xmlLoaded) {
            this.loadCommonData();// Siempre es defualt esta parte
        }
        this.loadPersonalData(id);
    }

    public static boolean isXmlLoaded() {
        return xmlLoaded;
    }
    

  private void loadCommonData() {
        try {
            resetCommonData();
            ascDesc = "";
            orderBy = "";
            ascDescCat2 = "";
            orderByCat2 = "";
        } catch (Exception e) {
            xmlLoaded = false;
            getLogger().error("Stack trace: ", e);
    }
        xmlLoaded = true;
    }

    public static boolean resetCommonData() {
        try {
                Settings settings = Framework.Config.Utilities.getSettings();
                ENVIAR_CORREOS = Framework.Config.Utilities.isMailSendingOn();
                      ENVIO_CORREOS = ENVIAR_CORREOS
                                      ? "SI"
                                      : "NO";
                ADMINISTRATOR_MAIL = settings.getAdminMail();

                FECHA_RESPALDO = settings.getBackupDate().toString();

                PERIODO_RESPALDO = settings.getBackupPeriod().toString();

                try {
                  SimpleDateFormat df3 = new SimpleDateFormat("D"); // se pide dia del año 0-364
                  DIA_RESPALDO = df3.format(settings.getBackupDate());
                } catch (Exception e) {
                          Loggable.getLogger(Properties.class).warn("No pudo generar la fecha del respaldo: "
                                  + e);
                          Loggable.getLogger(Properties.class).error("Stack trace: ", e);
                }
                DATABASE_NAME = "DB_NAME";
                DATABASE_URL = "DB_URL";
                DATABASE_PORT = "DB_PORT";

                WELCOME_MESSAGE = settings.getWelcomeMessage();
                COLOR_SISTEMA = settings.getSystemColor();
                POSITION = settings.getDefaultPosition().toString();
                DOC_VERSION = settings.getDocumentInitialVersion();
                ZIP7_PATH = settings.getZipPath();
                POSTGRES_PATH = settings.getDatabasePath();

                RESPALDO_EXTERNO_PATH = settings.getBackupExternalPath();
                SMTP_HOST = settings.getSmtpHost();
                      SMTP_UID = settings.getSmtpAuthentication().equals(1)
                                 ? settings.getSmtpUid()
                                 : settings.getAdminMail();
                SMTP_PWD = settings.getSmtpPsw();
                SMTP_PORT = settings.getSmtpPort().toString();
                STRING_SYSTEM_ID = settings.getSystemId();
                SITE_URL = SettingsUtil.getAppUrl();
                RC4_KEY = settings.getRcKey();
                MAIL_LIFETIME = settings.getMailLifeTime().toString();
                ASUNTO_CORREOS = settings.getMailSubject();
                PAIS = settings.getLocale();
                IDIOMA = settings.getLang();
                UPLOAD_FOLDER_DOCUMENTOS_EXT = settings.getDocumentsFolderExt();
                UPLOAD_FOLDER_SOLICITUDES_EXT = settings.getRequestFolderExt();
                UPLOAD_FOLDER_IMG_REPORTES_EXT = settings.getFolderImgReportExt();
                UPLOAD_FOLDER_LISTAS_EXT = settings.getFolderListExt();
                UPLOAD_FOLDER_RESPALDOS_EXT = settings.getBackupFolderExt();
                SITE_FOLDER_NAME = settings.getFolderName();
                SITE_HOST = settings.getHost();
                FILES_CONTEXT = settings.getFilesContext();
                SYSTEM_ID = settings.getSystemId();
                ASIGNAR_LECTORES = settings.getReaders() == 1;
                TO_PDF = "1";
                CONSERVA_ORIGINAL_TRAS_CONVERSION = settings.getSavePostPdf() == 1;
                MS_WORD_PATH = settings.getMsWordPath();
                      USE_MS_CONVERSION = settings.getMsConversion() == 1
                                          ? "SI"
                                          : "NO";
                TO_PDF_WORD = settings.getToPdfWord();
                TO_PDF_EXCEL = settings.getToPdfXl();
                TO_PDF_PP = settings.getToPdfPp();
                      DIR_PDFS = settings.getDirPdfs() == 1
                                 ? "SI"
                                 : "";
                PDF_VIEWER = "1";
                PROCEDEN_ACCIONES_DEFAULT = "NO";
                REG_DOC = settings.getPageCountDefault().toString();
                REG_DOC = REG_DOC == null || REG_DOC.trim().isEmpty()
                          ? "15"
                          : REG_DOC;
                UPLOAD_FOLDER_DOCUMENTOS_EXT = (UPLOAD_FOLDER_DOCUMENTOS_EXT == null
                                                ? ""
                                                : UPLOAD_FOLDER_DOCUMENTOS_EXT);
                UPLOAD_FOLDER_LISTAS_EXT = (UPLOAD_FOLDER_LISTAS_EXT == null
                                            ? ""
                                            : UPLOAD_FOLDER_LISTAS_EXT);
                UPLOAD_FOLDER_SOLICITUDES_EXT = (UPLOAD_FOLDER_SOLICITUDES_EXT
                        == null
                                                 ? ""
                                                 : UPLOAD_FOLDER_SOLICITUDES_EXT);
                UPLOAD_FOLDER_RESPALDOS_EXT = (UPLOAD_FOLDER_RESPALDOS_EXT == null
                                               ? ""
                                               : UPLOAD_FOLDER_RESPALDOS_EXT);
                UPLOAD_FOLDER_IMG_REPORTES_EXT = (UPLOAD_FOLDER_IMG_REPORTES_EXT
                        == null
                                                  ? ""
                                                  : UPLOAD_FOLDER_IMG_REPORTES_EXT);

                DOCUMENTS_PATH = (UPLOAD_FOLDER_DOCUMENTOS_EXT.isEmpty()
                                  ? DOCUMENTS_PATH
                                  : UPLOAD_FOLDER_DOCUMENTOS_EXT);
                LISTAS_PATH = (UPLOAD_FOLDER_LISTAS_EXT.isEmpty()
                               ? LISTAS_PATH
                               : UPLOAD_FOLDER_LISTAS_EXT);
                SOLICITUDES_PATH = (UPLOAD_FOLDER_SOLICITUDES_EXT.isEmpty()
                                    ? SOLICITUDES_PATH
                                    : UPLOAD_FOLDER_SOLICITUDES_EXT);
                RESPALDOS_PATH = (UPLOAD_FOLDER_RESPALDOS_EXT.isEmpty()
                                  ? RESPALDOS_PATH
                                  : UPLOAD_FOLDER_RESPALDOS_EXT);
                IMG_REPORTES_PATH = (UPLOAD_FOLDER_IMG_REPORTES_EXT.isEmpty()
                                     ? IMG_REPORTES_PATH
                                     : UPLOAD_FOLDER_IMG_REPORTES_EXT);

                ALECTORES_OK = ASIGNAR_LECTORES
                               ? ""
                               : "OFF";

          //logos
          if (SYSTEM_ID.equalsIgnoreCase("UR")) {
            LOGO = "../../images/header/logoUR.gif";
          } else if (SYSTEM_ID.equalsIgnoreCase("COFLEX")) {
            LOGO = "../../images/header/logoCOFLEX.gif";
          } else if (SYSTEM_ID.equalsIgnoreCase("FORMAS INTELIGENTES")) {
            LOGO = "../../images/header/logoFI.gif";
            LOGO_FOOTER_L = "../../images/footer/logoFI-F1.png";
          } else if (SYSTEM_ID.equalsIgnoreCase("LARMEX")) {
            LOGO = "../../images/header/logoLARMEX.gif";
          }
          MAIL_DPMS = new Mail(STRING_SYSTEM_ID, ADMINISTRATOR_MAIL);
        } catch (Exception e) {
            Loggable.getLogger(Properties.class).error("Error al reiniciar los valores estaticos legacy. ", e);
            return false;
        }
        return true;
    }

    public static void reloadXML(){
      xmlLoaded = false;
    }

    private void loadPersonalData(Long id) {
        /**
         * Elementos que son personalisados *
         */

      // tiene que HACER VALIDO lo del usuario, sino usar lo del SISTEMA (IDIOMA, PAIS).
        String lang, locale;
        lang = Framework.Config.Utilities.getSettings().getLang();
        locale = Framework.Config.Utilities.getSettings().getLocale();
        if(lang == null) {
            lang = "es";
        }
        if(locale == null) {
            locale = "MX";
        }
        Locale.setDefault(new java.util.Locale("es", "MX"));
        if (!usersCofig.containsKey(id)) {
            UserRef user;
            IUntypedDAO DAO = Framework.Config.Utilities.getUntypedDAO();
            if (id == 0L) {
                user = new UserRef(id, lang, locale);
            } else {
                user = DAO.HQLT_findById(UserRef.class, id);
                if (user.getLang() == null || user.getLang().isEmpty() || !LicenseUtil.filterSupportedLanguages().contains(user.getLang() + "-" + user.getLocale())) {
                    user.setLang(lang);
                    user.setLocale(locale);
                }
            }
            usersCofig.put(id, user);
        }

        try {
            try {
                localidad = new java.util.Locale(usersCofig.get(id).getLang(), usersCofig.get(id).getLocale());
            } catch (Exception e) {
                getLogger().warn("\r\n/-- System language setted to user {} ---", id);
                try {
                    localidad = new java.util.Locale(lang, locale);
                } catch (Exception ex) {
                    getLogger().warn("\r\n/-- Default language 'es-MX' setted to user {} ---", id);
                    localidad = new java.util.Locale("es", "MX");
                }               
            }  
            admin = Objects.equals(1, usersCofig.get(id).getRoot()); 
            tagsParam = LocaleUtil.getI18n("isoblock.common.languageBeans", localidad);
            
            while(!localidad.getLanguage().equals(tagsParam.getLocale().getLanguage())
                    || !localidad.getCountry().equals(tagsParam.getLocale().getCountry())) {
                getLogger().warn("\r\n/-- Invalid language setted to user {} {} ---", id, tagsParam.getLocale());
                localidad = new java.util.Locale(lang, locale);
                tagsParam = LocaleUtil.getI18n("isoblock.common.languageBeans", localidad);
                lang = "es";
                locale = "MX";
            }
            
            tags2Param = LocaleUtil.getI18n("isoblock.common.language", localidad);
            newTagsParam = LocaleUtil.getI18n("Framework.Config.Lang.common", localidad);

            formatPropParam = LocaleUtil.getI18n("isoblock.common.propiedadesFormato", localidad);

            /**
             * Tags con control de errores
             */

            tags = new Letreros(tagsParam);
            tags2 = new Letreros(tags2Param);
            newTags = new Letreros(newTagsParam);
            formatProp = new Letreros(formatPropParam);

            SOL_FECHA = formatProp.getString("solicitudFecha");
            SOL_REVISION = formatProp.getString("solicitudRevision");
            SOL_CLAVE = formatProp.getString("solicitudClave");

            AUD_FECHA = formatProp.getString("auditoriaFecha");
            AUD_REVISION = formatProp.getString("auditoriaRevision");
            AUD_CLAVE = formatProp.getString("auditoriaClave");

            PREG_FECHA = formatProp.getString("preguntaFecha");
            PREG_REVISION = formatProp.getString("preguntaRevision");
            PREG_CLAVE = formatProp.getString("preguntaClave");

            AMC_FECHA = formatProp.getString("accionMCFecha");
            AMC_REVISION = formatProp.getString("accionMCRevision");
            AMC_CLAVE = formatProp.getString("accionMCClave");

            APP_FECHA = formatProp.getString("accionPFecha");
            APP_REVISION = formatProp.getString("accionPRevision");
            APP_CLAVE = formatProp.getString("accionPClave");

            ACC_FECHA = formatProp.getString("accionCFecha");
            ACC_REVISION = formatProp.getString("accionCRevision");
            ACC_CLAVE = formatProp.getString("accionCClave");
        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
        }
    }


    /** Getter for property orderBy.
     * @return Value of property orderBy.
     */
    public String getOrderBy() {
        return this.orderBy;
    }

    /** Setter for property orderBy.
     * @param orderBy New value of property orderBy.
     */
    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    /** Getter for property orderByCat2.
     * @return Value of property orderByCat2.
     */
    public String getOrderByCat2() {
        return this.orderByCat2;
    }

    /** Setter for property orderByCat2.
     * @param orderByCat2 New value of property orderByCat2.
     */
    public void setOrderByCat2(String orderByCat2) {
        this.orderByCat2 = orderByCat2;
    }


    /** Getter for property ascDescCat2.
     * @return Value of property ascDescCat2.
     */
    public String getAscDescCat2() {
        return this.ascDescCat2;
    }

    /** Setter for property ascDesc2.
     * @param ascDescCat2 New value of property ascDesc2.
     */
    public void setAscDescCat2(String ascDescCat2) {
        this.ascDescCat2 = ascDescCat2;
    }

    /** Getter for property ascDesc.
     * @return Value of property ascDesc.
     */
    public String getAscDesc() {
        return this.ascDesc;
    }

    /** Setter for property ascDesc.
     * @param ascDesc New value of property ascDesc.
     */
    public void setAscDesc(String ascDesc) {
        this.ascDesc = ascDesc;
    }

    /** Getter for property currentURL.
     * @return Value of property currentURL.
     */
    public String getCurrentURL() {
        return this.currentURL;
    }

    /**
     * Debe tener la forma:
     *       [SITE_URL][/][pagina.jsp][?][parametro][=][valor]
     * Ejemplo:
     *       SITE_URL + "/index2.jsp?cURL=aux"
     *
     * Setter for property currentURL.
     *
     * @param currentURL New value of property currentURL.
     */
    public void setCurrentURL(String currentURL) {
        //si aqui llego un nulo como parametro del JSP revisar el try catch en titulotabla.jsi (include)
        this.currentURL = currentURL;
    }

    public String printAscDesc(String campo){
        String res = "<img border=0 src=\"../images/control/asc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDesc.asc")+"\""
                + " onclick=\"order('"+campo+"','asc')\">"
                + "<img border=0 src=\"../images/control/desc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDesc.desc")+"\""
                + " onclick=\"order('"+campo+"','desc')\">";
        return res;
    }
    public String printAscDesc2(String campo){
        String res = "<img border=0 src=\"../images/control/asc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDesc.asc")+"\""
                + " onclick=\"order2('"+campo+"','asc')\">"
                + "<img border=0 src=\"../images/control/desc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDesc.desc")+"\""
                + " onclick=\"order2('"+campo+"','desc')\">";
        return res;
    }

    public String printAscDescNumero(String campo){
        String res = "<img border=0 src=\"../images/control/asc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDescNumero.asc")+"\""
                + " onclick=\"orderNum('"+campo+"','asc')\">"
                + "<img border=0 src=\"../images/control/desc.gif\" title=\""+tags.getString("isoblock.common.Properties.printAscDescNumero.desc")+"\""
                + " onclick=\"orderNum('"+campo+"','desc')\">";

        return res;
    }

    public void replaceIDCurrentURL(String id, String valor) {
        getLogger().trace("isoblock.common.Properties.replaceIDCurrentURL("+id+","+valor+")");
        String temp = currentURL;
        temp = temp.substring(!temp.contains(id) ? temp.length() : temp.indexOf(id));
        temp = temp.substring(0,!temp.contains("&") ? temp.length() : temp.indexOf("&"));
        if(temp.length() != 0) {
            currentURL = currentURL.replace(temp, id + "=" + valor);
        }
    }
    public String replaceParamValue(String params, String id, String valor) {
       getLogger().trace("isoblock.common.Properties.replaceParamValue("+params+","+id+","+valor+")");
        String temp = params;
        temp = temp.substring(!temp.contains(id) ? temp.length() : temp.indexOf(id));
        temp = temp.substring(0,!temp.contains("&") ? temp.length() : temp.indexOf("&"));
        if(temp.length() != 0) {
            params = params.replace(temp, id + "=" + valor);
        }
        return params;
    }
    public void addParamcURL(String param, String valor) {
        getLogger().trace("isoblock.common.Properties.addParamcURL("+param+","+valor+")");
        if(param.length()!=0 && valor.length()!=0) {
            currentURL += "&"+param +"="+valor;
        } else {
            getLogger().error("addParamcURL(), tiene parametros vacios. ");
        }

    }
    public String getCurrentURLParam(String param, String valor) {

        return currentURL + "&"+param +"="+valor;
    }

    /**
     * @return the REG_DOC
     */
    public String getREG_DOC() {
        return REG_DOC;
    }

    /**
     * @param REG_DOC the REG_DOC to set
     */
    public void setREG_DOC(String REG_DOC) {
        Properties.REG_DOC = REG_DOC;
    }

    public String getPDF_VIEWER() {
        return PDF_VIEWER;
    }

    public void setPDF_VIEWER(String PDF_VIEWER) {
        Properties.PDF_VIEWER = PDF_VIEWER;
    }

    /**
     * @return the intModuloId
     */
    public String getIntModuloId() {
        return intModuloId;
    }

    /**
     * @param intModuloId the intModuloId to set
     */
    public void setIntModuloId(String intModuloId) {
        this.intModuloId = intModuloId;
    }

  public boolean isAdmin() {
    return admin;
  }

  public void setAdmin(boolean admin) {
    this.admin = admin;
  }

  public ProfileServices[] getServicios() {
    return servicios;
  }

  public void setServicios(ProfileServices[] servicios) {
    this.servicios = servicios;
  }
  public static String ApplicationData() {
      return "Bnext QMS - " 
              + SYSTEM_ID 
              + " - v" + AboutApp.getBuildVersion()
              + " - Rev. " + AboutApp.getRevisionDate()
              + " L" + Framework.Config.Utilities.getLicenses()
              + " " 
              + (ALECTORES_OK.isEmpty() ? "" : " - PL:" + ALECTORES_OK)
              ;
  }

    public static String getSystemColor() {
        return Framework.Config.Utilities.getSettings().getSystemColor();
    }

    public static String getWelcomeMessage() {
        return Framework.Config.Utilities.getSettings().getWelcomeMessage();
    }
    
    public static Letreros getTags(HttpSession session) {
        Object intusuarioid = session.getAttribute("intusuarioid");
        ResourceBundle tagsParam;
        if (intusuarioid == null) {
            tagsParam = LocaleUtil.getSystemI18n("isoblock.common.language");
        } else {
            Long loggedUserId = Long.parseLong(intusuarioid.toString());
            tagsParam = LocaleUtil.getI18n("isoblock.common.language", new Properties(loggedUserId).localidad);
        }
        return new Letreros(tagsParam);
    }
}
