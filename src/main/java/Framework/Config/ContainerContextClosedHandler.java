package Framework.Config;

import ape.mail.core.SmtpTransport;
import jakarta.annotation.Nonnull;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.Locale;
import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import mx.bnext.core.util.Loggable;
import org.apache.logging.log4j.LogManager;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ContextCleanupListener;
import qms.framework.daemon.BnextDaemonUtil;
import qms.framework.util.DatabaseQueryCacheHandler;
import qms.framework.util.DatabaseSourcePool;
import qms.framework.util.SimpleLogger;
import qms.tress.dao.ExternalTRESSViewHandler;

/**
 * Created on : Jun 24, 2014, 11:52:28 AM
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Service
public class ContainerContextClosedHandler extends ContextCleanupListener {
    private static final Logger LO4J_LOGGER = Loggable.getLogger(ContainerContextClosedHandler.class);
    private static final SimpleLogger SIMPLE_LOGGER = SimpleLogger.getLogger(ContainerContextClosedHandler.class);


    @Override
    public void contextInitialized(@Nonnull ServletContextEvent servletContextEvent) {
        LO4J_LOGGER.warn("Bnext QMS context initialized");
        Locale.setDefault(new java.util.Locale("es", "MX"));
    }

    @Override
    public void contextDestroyed(@Nonnull ServletContextEvent servletContextEvent) {
        try {
            SIMPLE_LOGGER.info("Called daemons shutdown!");
            BnextDaemonUtil.contextDestroyed();
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down Daemons", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called SmtpTransport shutdown!");
            SmtpTransport.clean();
            SIMPLE_LOGGER.info("Ends SmtpTransport shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down SmtpTransport", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called DataBaseQueryCache shutdown!");
            DatabaseQueryCacheHandler.shutdown();
            SIMPLE_LOGGER.info("Ends DataBaseQueryCache shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down DataBaseQueryCache", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called DataSource shutdown!");
            DatabaseSourcePool.shutdown();
            SIMPLE_LOGGER.info("Ends DataSource shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down Datasource", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called applicationContext shutdown!");
//            Utilities.shutdownApplicationContext();
            SIMPLE_LOGGER.info("Ends applicationContext shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down applicationContext", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called TRESS shutdown!");
            ExternalTRESSViewHandler.closeDataSource();
            SIMPLE_LOGGER.info("Ends TRESS shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down TRESS", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called log4j shutdown!");
            LogManager.shutdown();
            SIMPLE_LOGGER.info("Ends log4j shutdown!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Failed shutting down log4j", ex);
        }
        try {
            SIMPLE_LOGGER.info("Called JDBC shutdown!");
            Enumeration<Driver> drivers = DriverManager.getDrivers();
            while (drivers.hasMoreElements()) {
                Driver driver = drivers.nextElement();
                try {
                    DriverManager.deregisterDriver(driver);
                    SIMPLE_LOGGER.info("Unregistering jdbc driver: " + driver.toString());
                } catch (SQLException ex) {
                    SIMPLE_LOGGER.error("Error unregistering driver " + driver.toString(), ex);
                }
            }
            SIMPLE_LOGGER.info("Called immolate threads!");
            ThreadLocalImmolater i = new ThreadLocalImmolater();
            SIMPLE_LOGGER.info("Ends  immolate threads!");
        } catch (Exception ex) {
            SIMPLE_LOGGER.error("Immolating threads", ex);
        }
        SIMPLE_LOGGER.warn("Bnext QMS context destroyed");
    }

}