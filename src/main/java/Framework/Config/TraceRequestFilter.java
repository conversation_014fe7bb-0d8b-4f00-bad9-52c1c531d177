package Framework.Config;

import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TraceRequestFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger("TRACE_REQUEST." + TraceRequestFilter.class.getCanonicalName());

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpSession session = request.getSession(false);
        if (LOGGER.isTraceEnabled() && session != null && request.isRequestedSessionIdValid()) {
            Object userId = session.getAttribute("intusuarioid");
            if (userId != null) {
                String strUserId = userId.toString();
                if (Utilities.isInteger(strUserId)
                        && Utilities.isValidTraceRequestUserId(Long.valueOf(strUserId))
                        && Utilities.isValidTraceRequestUrl(request.getRequestURI())) {
                    Framework.Config.Utilities.traceRequest(request, LOGGER, true);
                }
            }
        }
        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig fc) throws ServletException {
        // empty
    }

    @Override
    public void destroy() {
        // empty
    }

}
