package Framework.Config;

import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import bnext.login.Login;
import bnext.util.PwaHelper;
import com.google.common.collect.ImmutableSet;
import com.google.gson.Gson;
import java.io.IOException;
import java.util.Set;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.ExceptionUtils;

/**
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
//@Service
public class SecurityFilter implements Filter {

    private static final Logger LOGGER = Loggable.getLogger(SecurityFilter.class);
    public static final Set<String> SECURE_PATHS = ImmutableSet.of(
            "/index.jsp",
            "/qms/index.html",
            "/index2.jsp",
            "/images/favicon.ico",
            "/scripts/framework/dojo/dojo.js",
            "/scripts/framework/jquery.js",
            "/scripts/framework/jquery.validate.min.js"
    );

    private final PwaHelper helper = new PwaHelper();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        //Does nothing
    }

    @Override
    public void doFilter(ServletRequest sreq, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) sreq;
        String servletPath = request.getServletPath().replaceFirst("\\?.+", "");
        ActiveDirectoryUtil.AUTOLOGON_STATUS logonStatus = ActiveDirectoryUtil.autologon(sreq);
        final HttpServletResponse response = (HttpServletResponse) res;
        switch (logonStatus) {
            case LOGIN_SUCCESS:
                /**
                 * Esto es un parche!! 
                 * Por alguna razón la función "chain.doFilter..." no funciona.
                 */
                String qry = request.getQueryString();
                if (qry != null) {
                    response.sendRedirect(request.getContextPath() + request.getServletPath() + "?" + qry);
                } else {
                    response.sendRedirect(request.getContextPath() + request.getServletPath());
                }
                break;
            case LOGGED_ALREADY:
                if (SecurityUtils.isException(servletPath) || SECURE_PATHS.contains(servletPath) || !servletPath.contains(".")) {
                    customHeadersResponse(res);
                }
                if (ActiveDirectoryUtil.isSsoActive() && request.getServletPath().contains("/qms/")) {
                    String account = SecurityUtils.getLoggedUserAccount();
                    HttpSession session = request.getSession(false);
                    if (account == null && session != null) {
                        account = (String) session.getAttribute("accountName");
                    }
                    Login.updateDomainData(account);
                }
                try {
                    if ("https".equals(request.getScheme())) {
                        response.addHeader("Accept-CH", "Sec-CH-UA-Platform-Version");
                    }
                    chain.doFilter(sreq, res);
                } catch (org.springframework.web.util.NestedServletException e) {
                    if (LOGGER.isTraceEnabled()) {
                        Framework.Config.Utilities.traceRequest(request, true);
                    }
                    final String message = ExceptionUtils.getRootCauseMessage(e);
                    RuntimeException err = new RuntimeException(message, e);
                    if (e.getCause() != null) {
                        if (MaxUploadSizeExceededException.class.isInstance(e.getCause())) {
                            response.setStatus(HttpServletResponse.SC_CONFLICT);
                            response.setContentType("application/json;charset=UTF-8");
                            response.setCharacterEncoding("UTF-8");
                            final String details = new Gson().toJson(com.google.common.collect.ImmutableMap.of("errorMessage", "maximum-file-size-exceeded"));
                            response.getWriter().write(details);
                            return;
                        } else if (DataIntegrityViolationException.class.isInstance(e.getCause())) {
                            DataIntegrityViolationException dataException = (DataIntegrityViolationException) e.getCause();
                            if (dataException.getMostSpecificCause() != null && dataException.getMostSpecificCause().getStackTrace() != null) {
                                throw new RuntimeException(dataException.getMostSpecificCause());
                            } else if (dataException.getRootCause() != null && dataException.getRootCause().getStackTrace() != null) {
                                throw new RuntimeException(dataException.getRootCause());
                            } else if (dataException.getCause() != null && dataException.getCause().getStackTrace() != null) {
                                throw new RuntimeException(dataException.getCause());
                            } else if (e.getCause().getStackTrace() != null) {
                                err.setStackTrace(e.getCause().getStackTrace());
                            }
                            LOGGER.error(message, err);
                        } else if (AccessDeniedException.class.isInstance(e.getCause())) {
                            String account = SecurityUtils.getLoggedUserAccount();
                            String qryerror = request.getQueryString();
                            LOGGER.error(
                                    "Access denied to user {} for URL {}.",
                                    new Object[]{
                                            account,
                                            request.getRequestURI() + (qryerror != null ? "?" + qryerror : "")
                                    }
                            );
                        } else if (e.getCause().getStackTrace() != null) {
                            err.setStackTrace(e.getCause().getStackTrace());
                            LOGGER.error(message, err);
                        } else {
                            LOGGER.error(message, err);
                        }
                    } else {
                        LOGGER.error(message, err);
                    }
                    throw err;
                }
                break;
            default:
                // LOGIN_DENIED
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, ""
                        + "Su usuario se encuentra fuera del dominio de la aplicación, "
                        + "su dominio debería ser '" + ActiveDirectoryUtil.getActiveDirectoryDomain() + "'."
                );
                break;
        }
    }

    private void customHeadersResponse(ServletResponse res) {
        HttpServletResponse response = (HttpServletResponse) res;
        response.addHeader("X-Frame-Options", "SAMEORIGIN");
        response.addHeader("X-XSS-Protection", "1;mode=block");
        response.addHeader("X-Content-Type-Options", "nosniff");
    }

    @Override
    public void destroy() {
        // empty
    }

}
