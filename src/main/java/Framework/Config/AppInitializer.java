package Framework.Config;

import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.daemon.BnextDaemonUtil;
import qms.framework.util.ConcurrentUtils;
import qms.framework.util.DatabaseUtil;

import javax.servlet.ServletConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class AppInitializer extends HttpServlet {

    private static final Logger LOGGER = Loggable.getLogger(AppInitializer.class);

    private static final long serialVersionUID = -1L;
    private static boolean VALID_RUN = true;
    private static boolean INITIALIZED = false;

    @Override
    public void init(final ServletConfig config) throws ServletException {
        final ServletContext servletContext = config.getServletContext();
        final String servletContextName = servletContext.getServletContextName();
        if (!VALID_RUN) {
            LOGGER.error("Init app {} has been run many times.", servletContextName);
            return;
        }
        LOGGER.debug(
                "Init app {} in path {}. Running on server {}.",
                servletContextName,
                servletContext.getContextPath(),
                servletContext.getServerInfo()
        );
        VALID_RUN = false;
        final List<CompletableFuture<InitializerTask>> inits = init(servletContext, false);
        final CompletableFuture<?> allReady = ConcurrentUtils.allOf(inits);
        final String systemId = Utilities.getSystemId();
        if (allReady == null || allReady.isDone()) {
            LOGGER.error("{}: Init app {} has already finished running.", systemId, servletContextName);
        } else {
            allReady.whenCompleteAsync((msg, ex) -> {
                INITIALIZED = true;
                if (ex != null) {
                    LOGGER.error("{}: Init app {} has finished running with error.", systemId, servletContextName, ex);
                } else {
                    LOGGER.error("{}: Init app {} has finished running.", systemId, servletContextName);
                }
            });
        }
    }

    public static Boolean isInitialized() {
        return INITIALIZED;
    }

    public static List<CompletableFuture<InitializerTask>> init(final ServletContext servletContext, final Boolean skipAfterReadyTasks)
            throws ServletException {
        /*
          Inicialización básica, no agregar tareas costosas que detengan el arranque de la aplicación
         */
        Utilities.quickCacheInitialize(servletContext);
        DatabaseUtil.setupSecondLevelcache();
        BnextDaemonUtil.contextInitialized();
        return BnextDaemonUtil.getRunningInstance(Initializer.class).asyncInitTasks(servletContext, skipAfterReadyTasks);
    }

}
