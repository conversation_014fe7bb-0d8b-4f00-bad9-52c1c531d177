package Framework.DAO;

import DPMS.DAO.HibernateDAO_GenericDAO;
import DPMS.DAOInterface.IHGenericDAO;
import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.Persistable;
import Framework.Action.SessionViewer;
import Framework.Config.BaseDomainObject;
import Framework.Config.DomainObject;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import bnext.exception.MakePersistentException;
import com.google.gson.Gson;
import isoblock.common.Properties;
import isoblock.common.beanGeneric;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import mx.bnext.access.ProfileServices;
import mx.bnext.cipher.RC4;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.apache.struts2.interceptor.parameter.StrutsParameter;
import org.apache.struts2.json.annotations.SMDMethod;
import org.springframework.dao.DataIntegrityViolationException;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.framework.util.LocalizedFieldDTO;
import qms.framework.util.LocalizedUtil;
import qms.framework.util.SessionFilterHandler;
import qms.framework.util.SettingsUtil;
import qms.util.CustomLinkedSelect;
import qms.util.EntityCommon;
import qms.util.LinkedGridConfig;
import qms.util.ReflectionUtil;
import qms.util.Translate;
import qms.util.interfaces.ILinkedGrid;

/**
 *
 * <AUTHOR> Limas - Utilizado para obtener registros de objetos que hereden de DomainObject (o preferentemente de
 * StandarEntity), teoricamente hacer que los demas CRUD hereden de esta clase se puede considerar una buena practica -
 * Tambien es buena practica implementar los correspondientes servicios desde que se hace el mapeo en struts.xml, es
 * decir enviarlos en el parametro "activeServices" (Se movió "activeServices" y "serviciosActivos" a SessionViewer.java
 * para poder utilizarlos en los Action's)
 * @param <T>
 */
public class CRUD_Generic<T> extends SessionViewer implements ILinkedGrid {

    /* Lo tres parametros se envian desde struts.xml y son fijos */
    /* 
     * REQUERIDO: 
     *      Ej. DPMS.Mapping.Usuario 
     */
    protected String className = "Framework.Config.DomainObject";
    protected Class<T> parameterizedType = null;
    private String daoClassName = null;
    private String cacheable = null;
    public ResourceBundle tags; 
    private static final String DEFAULT_JSON_VALUE = "{}";
    
    private void stackTraceHandle(Exception e) {
        getLogger().error("Se genero un error en la clase: " + this.getClass().getCanonicalName(), e); 
    }
        
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        throw new Exception(""
            + "Please OVERRIDE getLinkedGridConfig() METHOD and ADD support for LinkedGrid '" + linkedGridId + "' at '" + this.getClassName() + "' CRUD!"
        ); 
    }
    
    @SMDMethod
    public <X> GridInfo<X> getGroundRows(SortedPagedFilter filter, String linkedGridId, Long groundId) throws Exception {
        return this.getGroundRows(filter, getLinkedGridConfig(linkedGridId, groundId));
    }

    @SMDMethod 
    public <X> GridInfo<X> getDialogRows(SortedPagedFilter filter, String linkedGridId, Long groundId) throws Exception {
        return this.getDialogRows(filter, getLinkedGridConfig(linkedGridId, groundId));
    }
    
    @SMDMethod 
    public String getSavedWindowFilter(String windowPath, String gridId) {
        getLogger().trace("@loading saved filters in windowPath: {}", windowPath);
        final SessionFilterHandler sessionHandler = new SessionFilterHandler();
        final Map<String, Object> userSearch = sessionHandler.loadSavedWindowFilters(windowPath);
        if (userSearch == null) {
            return DEFAULT_JSON_VALUE;
        }
        final Object gridFilter = userSearch.get(gridId);  
        if (gridFilter != null) {  
            try {
                final Gson tool = new Gson(); 
                if (gridFilter instanceof SortedPagedFilter) {
                    return tool.toJson(gridFilter, SortedPagedFilter.class);
                } else {
                    return tool.toJson(gridFilter);
                }
            } catch(final Exception e) {
                getLogger().error("@loading saved filters failed in windowPath: {}", windowPath, e);
                return DEFAULT_JSON_VALUE;
            }
        }
        return DEFAULT_JSON_VALUE;
    }
    
    protected <X> GridInfo<X> getGroundRows(SortedPagedFilter filter, LinkedGridConfig config) {
        try {
            if(config.isInvalidCall()) {
                getLogger().warn("1. Invalid getGroundRows call! groundId seems to be -1, linkedEntityClass: {}", config.getLinkedEntityClass());
                return Utilities.EMPTY_GRID_INFO;
            } else if(config.isGroundInactive()) {
                return Utilities.EMPTY_GRID_INFO;
            }
            Class clazz = config.getLinkedEntityClass();            
            final IUntypedDAO dao = Utilities.getUntypedDAO();
            final CustomLinkedSelect customSelect = config.getCustomSelect();
            if (customSelect == null) {
                filter.getCriteria().put("<condition>", config.getGroundCondition());
                return dao.getRows(clazz, filter);
            } else {
                if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty() && customSelect.getOrderBy() != null && !customSelect.getOrderBy().isEmpty()) {
                    filter.getField().setOrderBy(customSelect.getOrderBy());
                    filter.setDirection((byte) 1);
                }
                final String customHql = Translate.parseToHQLQuery(customSelect, clazz, config.getGroundCondition());
                return dao.HQL_getRows(customHql, filter);
            }
        } catch (Exception ex) {
            getLogger().error("2. Invalid getGroundRows call! linkedEntityClass: {}", config.getLinkedEntityClass(), ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }
    
    protected <X> GridInfo<X> getDialogRows(SortedPagedFilter filter, LinkedGridConfig config) {
        try {
            Class clazz = config.getLinkedEntityClass();
            this.className = clazz.getCanonicalName();
            IGenericDAO dao = config.getDialogDao();
            if (config.isActiveStatusCondition()) {
                if(!LinkedGridConfig.NO_CONDITION.equals(config.getActiveStatusCondition())) {
                    filter.getCriteria().put("<linked-dialog-grid>", config.getActiveStatusCondition());
                }
            } else { 
                filter.getCriteria().put("<linked-dialog-grid>", ""
                        + "(c.deleted is null OR c.deleted = 0)"
                        + " AND c.status = " + config.getActiveStatus());
            }
            final CustomLinkedSelect customSelect = config.getCustomSelect();
            if (customSelect == null) {
                return this.getRows(filter, dao);
            } else {
                dao.setValidEntitiesFilter(filter, getLoggedUserId().toString(), getServiciosActivos(), isAdmin());
                Object condition = filter.getCriteria().remove("<linked-dialog-grid>");
                if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty() && customSelect.getOrderBy() != null && !customSelect.getOrderBy().isEmpty()) {
                    filter.getField().setOrderBy(customSelect.getOrderBy());
                    filter.setDirection((byte) 1);
                }
                final String customHql = Translate.parseToHQLQuery(customSelect, clazz, condition != null ? condition.toString() : null);
                return dao.HQL_getRows(customHql, filter);
            }
        } catch (Exception ex) {
            getLogger().error("Invalid getDialogRows call", ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }
    
    public CRUD_Generic() {
        this.parameterizedType = (Class<T>) ReflectionUtil.getParametrizedType(getClass());
        if (this.parameterizedType != null) {
            this.className = this.parameterizedType.getName();
        }
        if (this.getSession().get("lang") != null 
            && this.getSession().get("locale") != null 
            && this.getClass().getAnnotation(Language.class)!=null){
            Locale locale = new Locale(this.getSession().get("lang").toString(),this.getSession().get("locale").toString());
            tags = LocaleUtil.getI18n(this.getClass(), locale);
        }
    }

    public String smd() {
        return SUCCESS;
    }

    @SMDMethod
    public int pdfViewrStatus() {
        return Properties.PDF_VIEWER.equals("1") ? 1 : 0;
    }

    /**
     * Es un Ping... Utilizado para probar funciones (dando clic en el logo de BnextDPMS)... Nunca subirlo con algo que
     * haga algo!
     *
     * @since : 2.3.2.39 
     * <AUTHOR> Luis Limas
     */
    @SMDMethod
    public void ping() {
        getLogger(Loggable.LOGGER.APE.PING).info(">> ping: " + getLoggedUserId() + " - " + getLoggedUserName() + " - " + new Date()); 
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(getClass());
        qms.framework.util.MeasureTime.stop(tStart, "Elapsed time loading bean");
    }
    
    public String redirectToBnextDPMS(String user, String name, String mail){
        String location = SettingsUtil.getAppUrl();
        String parameters = "";
        RC4 rc4 = new RC4();
            user = rc4.GetEncryptedString(user, isoblock.common.Properties.RC4_KEY);	
            name = rc4.GetEncryptedString(name, isoblock.common.Properties.RC4_KEY);	
            mail = rc4.GetEncryptedString(mail, isoblock.common.Properties.RC4_KEY);
        if (!"".equals(user)) {
            parameters = "?u=" + user;
            parameters += !"".equals(name) ? "&n=" + name : "";
            parameters += !"".equals(mail) ? "&c=" + mail : "";
        }
        return location + parameters +"&enc=9CE1";
    }
    
    public BaseDomainObject newInstance(String className) throws Exception {
        Object bean = null;
        if (className != null) {
            Class cls = Class.forName(className);
            Constructor ct = cls.getConstructor();
            bean = ct.newInstance();
        } else {
            getLogger().error("Error  instance, no hay clase : {}", className);
        }
        return (BaseDomainObject) bean;
    }

    public ICRUD_GenericDAO newDAOInstance(String daoClassName) throws Exception {
        Object bean = null;

        try {
            if (daoClassName != null) {
                Class cls = Class.forName(daoClassName);
                if(IGenericDAO.class.isAssignableFrom(cls)) {
                    bean = getBean(cls.getSimpleName());
                } else {
                    Constructor ct = cls.getConstructor();
                    bean = ct.newInstance();
                }
            } else {
                getLogger().error("Error  instance, no hay clase");
            }
        } catch (ClassNotFoundException | IllegalAccessException | IllegalArgumentException 
                | InstantiationException | NoSuchMethodException | SecurityException 
                | InvocationTargetException ex) {
            stackTraceHandle(ex);
            bean = getUntypedDAO();
        }
        return (ICRUD_GenericDAO) bean;
    }

    @SMDMethod
    public Object load(Long id) {
        try {
            IUntypedDAO dao = Utilities.getUntypedDAO();
            Class clase = Class.forName(getClassName());
            return dao.HQLT_findById(clase, id);
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return null;
    }

    /**
     * Bug: no filtra por UNE
     *
     * @param backup
     * @return
     */
    protected boolean hasSaveAccess(Persistable backup) {
        if (this.isAdmin()) {
            return true;
        }
        if (getServiciosActivos().length >= 1) {
            for (ProfileServices object : Arrays.asList(getServiciosActivos())) {
                if (getLoggedUserServices().contains(object)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Bug: no filtra por UNE
     *
     * @return
     */
    protected boolean hasDeleteAccess() {
        if (this.isAdmin()) {
            return true;
        }
        if (getServiciosActivos().length >= 2) {
            for (ProfileServices object : Arrays.asList(getServiciosActivos())) {
                if (getLoggedUserServices().contains(object)) {
                    return true;
                }
            }
        }
        getLogger().error(">> Framework.DAO.CRUD_Generic.hasDeleteAccess: --> false! <-- ");
        return false;
    }

    /**
     * Actualiza objetos...
     * @param id
     * @param key
     * @param value
     * @return 
     */
    @SMDMethod
    public GenericSaveHandle updateSimple(Long id, String key, String value) {
        Map<String, Object> up = new HashMap<>();
        up.put(key, value);
        return update(id, up);
    }

    @SMDMethod
    public GenericSaveHandle update(Long id, Map<String, Object> up) {
        getLogger().debug(">> Actualizando {}", className);
        DomainObject backup = null;
        GenericSaveHandle gsh = new GenericSaveHandle();
        try {
        IUntypedDAO dao;
            backup = (DomainObject) newInstance(this.getClassName());
            if (backup == null) {
                gsh.setErrorMessage("El objeto que se va a guardar no esta correctamente configurado (revisar struts.xml)");
                return gsh;
            }
            if (daoClassName != null) {
                getLogger().debug("Creando instancia de DAO explicito '{}'",daoClassName);
                dao = newDAOInstance(daoClassName);
            } else {
                getLogger().debug("Creando instancia de DAO no explicito ");
                dao = Utilities.getUntypedDAO();
            }
            Utilities.populateInstance(backup, up);
            if (id.equals(-1L)) {
                if (!hasSaveAccess(backup)) {
                    gsh.setErrorMessage("Usted no tiene permiso para guardar");
                    gsh.setStatus("NOT-PERMISION-SAVE");
                    getLogger().error(">> Sin permiso para actualizar! ");
                    return gsh;
                }
                backup = dao.makePersistent(backup);
            } else {
                backup.setId(id);
                if (!hasSaveAccess(backup)) {
                    gsh.setErrorMessage("Usted no tiene permiso para guardar");
                    gsh.setStatus("NOT-PERMISION-SAVE");
                    getLogger().error(">> Sin permiso para actualizar! ");
                    return gsh;
                }
                backup.update(up);
                LocalizedUtil.updateLanguages(backup);
            }
            if(up.containsKey("code") && "".equals(up.get("code"))){
                Field f = backup.getClass().getField("PREFIX");
                f.setAccessible(true);
                String prefix = f.get(backup).toString();
                up.put("code", prefix + backup.getId().toString() );
                backup.update(up);
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: {}", Utilities.getSerializedObj(backup));
            stackTraceHandle(e);
            gsh.errorMsgHandle(e.getCause().getCause().getMessage() + " ");
        } catch (Exception e) {
            stackTraceHandle(e); 
            gsh.errorMsgHandle(e.getCause().getMessage() + " ");
        }
        return gsh;
    }
    
    @SMDMethod
    public List<ITextHasValue> getActives() throws ClassNotFoundException,Exception{
        ICRUD_GenericDAO dao;
        if(daoClassName == null){
            throw new ClassNotFoundException("agregar DaoClassName al mapeo para utilizar esta funcion");
        } else {
            dao =  newDAOInstance( daoClassName );
        }
        return dao.getActives();
    }

    /**
     * Metodo para guardar objetos
     * 
     * @param obj objeto que se va a guardar
     * @return GenericSaveHandle con los datos de la operación
     * @throws Exception 
     */
    @SMDMethod
    public GenericSaveHandle saveGeneric (Map obj) throws Exception {
        getLogger().trace(">> Guardando {}", className);
        Persistable backup;
        IUntypedDAO dao;
        GenericSaveHandle gsh = new GenericSaveHandle();
        backup = (Persistable) newInstance(this.getClassName());
        if (backup == null) {
            gsh.setErrorMessage("El objeto que se va a guardar no esta correctamente configurado (revisar struts.xml)");
            return gsh;
        }
        if (daoClassName != null) {
            getLogger().trace("Creando instancia de DAO explicita {}", daoClassName);
            dao = newDAOInstance(daoClassName);
        } else {
            getLogger().trace("Creando instancia de DAO generica {}", daoClassName);
            dao = Utilities.getUntypedDAO();
        }
        if (dao == null) {
            dao = Utilities.getUntypedDAO();
        }
        backup = Utilities.populateInstance(backup, obj);
        boolean nuevo = backup.getId().equals(-1L);
        if (!hasSaveAccess(backup)) {
            gsh.setErrorMessage("Usted no tiene permiso para guardar");
            return gsh;
        }
        if(nuevo && backup instanceof IAuditableEntity entity) {
            entity.setStatus(EntityCommon.getActiveStatus(this.getClassName()).getValue());
        }
        
        beforeSave(backup, nuevo, dao);
        try {
            backup = dao.makePersistent(backup, getLoggedUserId());
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: {}", Utilities.getSerializedObj(backup));
            stackTraceHandle(e);
            backup = null;
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                gsh.setErrorMessage(MakePersistentException.EXIST_RECORD);
            } else {
                gsh.errorMsgHandle(e.getCause().getCause().getMessage() + " ");
            }
        } catch (Exception e) {
            stackTraceHandle(e); 
            backup = null;
            gsh.errorMsgHandle(e.getCause().getMessage() + " ");
        }
        if (backup != null) {
            afterSave(backup, nuevo, dao);
        }
        if (backup != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(nuevo ? SessionViewer.ADD_SUCCESS : SessionViewer.EDIT_SUCCESS);
        } else {
            gsh.setOperationEstatus(0);
            
        }
        return gsh;
    }

    /**
     * Este metodo se ejecuta antes de guardar el entity y la transaccion esta abiera, NO se tiene acceso a la
     * transaccion desde este metodo
     *
     * @param backup
     * @param isNewObject
     */
    protected void beforeSave(Persistable backup, boolean isNewObject) {
        //override this method to modify/add stuff before the actual DomainObject its saved
    }

    /**
     * Este metodo se ejecuta cuando el objeto ya está guardado y la transaccion esta cerrada
     *
     * @param backup
     * @param isNewObject
     * @param dao
     */
    protected void afterSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        //override this method to modify/add stuff after the actual DomainObject its saved
    }

    /**
     * Este metodo se ejecuta antes de guardar el entity y la transaccion esta abiera, se tiene acceso a la misma debido
     * a que recibe como parametro el DAO utilizado
     *
     * @param backup
     * @param isNewObject
     * @param dao
     */
    protected void beforeSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        //override this method to modify/add stuff before the actual DomainObject its saved
        beforeSave(backup, isNewObject);
    }

    @SMDMethod
    public List load() {
        try {
            Class clase = newInstance(this.getClassName()).getClass();
            final IUntypedDAO dao = Utilities.getUntypedDAO();
            return dao.HQL_findAll(clase);
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return null;
    }

    @SMDMethod
    public Integer delete(Long id) {
        String HQL;
        Integer i = 0;
        if (!hasDeleteAccess()) {
            getLogger().error("Sin permiso para enviar a la papelera");
            return 0;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        HQL = "UPDATE " + getClassName() + " SET deleted = 1 WHERE id = " + id;
        try {
            i = dao.HQL_updateByQuery(HQL);
            getLogger().debug("{} HQL delete : {}",i,HQL);
        } catch (Exception ex) {
            stackTraceHandle(ex);
            getLogger().error("\nEl HQL del error: {\n{\n{}\n}\n",HQL);
        }
        return i == 0 ? 0 : 1;
    }

    /**
     * Regresa el Gridinfo en base al filtro
     *
     * @param filter el filtro que va a realizar
     * @param filtered marca que dice si va a filtrar o no
     * @return Gridinfo con los resultados
     */
    @SMDMethod
    public <X> GridInfo<X> getRows(SortedPagedFilter filter, Boolean filtered) {
        return getRows(filter, false, null, 0, filtered);
    }
        
    protected <X> GridInfo<X> getRows(
            final SortedPagedFilter filter, 
            Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds, 
            final Boolean filtered
    ) {
        try {
            ICRUD_GenericDAO dao = null;
            if (daoClassName != null) {
                dao = newDAOInstance(daoClassName);
            } 
            if (this.cacheable != null && Objects.equals(this.cacheable, "true")) {
                cacheable = true;
            }
            if (daoClassName == null || dao == null) {
                final BaseDomainObject backup = newInstance(this.getClassName());
                if (backup == null) {
                    getLogger().warn("El objeto que se va a guardar no esta correctamente configurado (revisar struts.xml)");
                    return Utilities.EMPTY_GRID_INFO;
                }                   
                final IUntypedDAO untypedDao = Utilities.getUntypedDAO();
                return getRows((Class<X>) backup.getClass(), cacheable, null, queryTimeoutSeconds, filter, untypedDao);
            } else {
                return getRows(filter, cacheable, null, queryTimeoutSeconds, dao, filtered);
            }
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }

    /**
     * Regresa el Gridinfo en base al filtro y al dao especificado Asume que va a realizar un filtro en particular
     *
     * @param filter el filtro que va a realizar
     * @param dao en que dao va a hacer el filtro
     * @return Gridinfo con los resultados
     */
    @SMDMethod
    public <X> GridInfo <X> getRows(SortedPagedFilter filter, IUntypedDAO dao) {
        return getRows(filter, false, null, 0, dao, true);
    }

    /**
     * Regresa el Gridinfo en base al filtro especificado Asume que va a realizar un filtro en particular
     *
     * @param filter el filtro que va a realizar
     * @return un GridInfo en base al filtro
     */
    @SMDMethod
    public <X> GridInfo<X> getRows(SortedPagedFilter filter) {
        return getRows(filter, false, null, 0, true);
    }

    /**
     * Regresa el Gridinfo en base al filtro y al dao especificado
     *
     * @param filter el filtro que va a realizar
     * @param dao en que dao va a hacer el filtro
     * @param filtered marca que dice si va a filtrar o no
     * @return Gridinfo con los resultados
     */
    @SMDMethod
    public <X> GridInfo<X> getRows(SortedPagedFilter filter, IUntypedDAO dao, Boolean filtered) {
        return getRows(filter, false, null, 0, dao, filtered);
    }
        
    protected <X> GridInfo<X> getRows(
            final SortedPagedFilter filter, 
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds, 
            final IUntypedDAO dao, 
            final boolean filtered
    ) {
        getLogger().trace("Framework.DAO.getRows ... {} ... {}",filter, daoClassName);
        try {
            Class<X> clase = (Class<X>) newInstance(this.getClassName()).getClass();
            String userId = getLoggedUserId() + "";
            if (filtered) {
                dao.setValidEntitiesFilter(filter, userId, getServiciosActivos(), isAdmin());
                getLogger().debug(DEFAULT_JSON_VALUE, filter.getCriteria().get("<condition>"));
            } else {
                filter.getCriteria().put("<filtered-entity>", " c.deleted = 0"); // <-- ToDo: Tal vez afecte a las papeleras
            }
            return getRows(clase, cacheable, cacheRegion, queryTimeoutSeconds, filter, dao);
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }
    
    protected <TYPE> GridInfo<TYPE> getRows(
            final Class<TYPE> TYPE,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds,
            final SortedPagedFilter filter,
            final IUntypedDAO dao
    ) {
        try {
            if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
                filter.getField().setOrderBy("id");
                filter.setDirection((byte) 2);
            }
            return dao.getRows(TYPE, cacheable, cacheRegion, queryTimeoutSeconds, filter);
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }
    
    //TODO: PROBAR 09/05/2014
    @SMDMethod
    public List getValuesForCombo() {
        try {
            String userId = getLoggedUserId() + "";
            boolean admin = isAdmin();
            SortedPagedFilter filter = new SortedPagedFilter();
            List list;
            if (daoClassName != null) {
                final ICRUD_GenericDAO dao = newDAOInstance(daoClassName);
                list = beanGeneric.callMethodGetList(dao, "getFilteredEntity", admin, filter, userId, getServiciosActivos());
            } else {
                final Class clase = newInstance(this.getClassName()).getClass();
                final IUntypedDAO dao = getUntypedDAO();
                final Boolean cacheable = this.cacheable != null && Objects.equals(this.cacheable, "true");
                list = dao.HQL_getListResultsByQuery(clase, filter, cacheable, null, 0);
                
            }
            getLogger().debug(list.toString());
            return list;
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return null;
    }

    @SMDMethod
    public Long getRowCount(SortedPagedFilter filter) {
        try {
            ICRUD_GenericDAO dao;
            if (daoClassName != null) {
                dao = newDAOInstance(daoClassName);
            } else {
                DomainObject backup = (DomainObject) newInstance(this.getClassName());
                if (backup == null) {
                    getLogger().warn("El objeto que se va a guardar no esta correctamente configurado (revisar struts.xml)");
                    dao = (IHGenericDAO) getBean(HibernateDAO_GenericDAO.class.getSimpleName());
                } else {
                    final IUntypedDAO untypedDao = Utilities.getUntypedDAO();
                    return getRowCount(backup.getClass(), filter, untypedDao);
                }
            }
            return getRowCount(filter, dao);
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return 0L;
    }

    @SMDMethod
    public Long getRowCount(SortedPagedFilter filter, ICRUD_GenericDAO dao) {
        getLogger().debug("Framework.DAO.getRowCount ... {} ... {}", filter, daoClassName);
        Class clase = null;
        try {
            clase = newInstance(this.getClassName()).getClass();
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        String userId = getLoggedUserId() + "";
        dao.setValidEntitiesFilter(filter, userId,  getServiciosActivos(), isAdmin());
        return getRowCount(clase, filter, dao);
    }
 
    private <TYPE> Long getRowCount(Class<TYPE> TYPE, SortedPagedFilter filter, IUntypedDAO dao) {
        try {
            final Long count = dao.HQL_countByPagedFilter(TYPE, filter, false);
            return count;
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
        return 0L;
    }
    
    public String getClassName() {
        return className;
    }

    @StrutsParameter
    public void setClassName(String className) {
        this.className = className;
    }
    public void setClass(Class clase) {
        this.className = clase.getName();
    }

    public String getDaoClassName() {
        return daoClassName;
    }

    @StrutsParameter
    public void setDaoClassName(String daoClassName) {
        this.daoClassName = daoClassName;
    }

    public String getCacheable() {
        return cacheable;
    }

    @StrutsParameter
    public void setCacheable(String cacheable) {
        this.cacheable = cacheable;
    }

    @SMDMethod
    public GenericSaveHandle toggleStatus(Long id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        getLogger().debug("Framework.DAO.CRUD_Generic.toggleStatus ... {} ... {}",this.getClassName(), id);
        try {
            IUntypedDAO dao = Utilities.getUntypedDAO();
            Class clase = newInstance(this.getClassName()).getClass();
            Object o = dao.HQLT_findById(clase, id);
            if(!(o instanceof StandardEntity)) {
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("No tiene permitido cambiar este estatus");
                return gsh;
            }
            StandardEntity<?> domObject = (StandardEntity<?>) o;
            domObject.setStatus(StandardEntity.ACTIVE_STATUS.equals(domObject.getStatus())
                    ? StandardEntity.INACTIVE_STATUS
                    : (StandardEntity.INACTIVE_STATUS.equals(domObject.getStatus())
                    ? StandardEntity.ACTIVE_STATUS
                    : StandardEntity.DISCONTINUED_STATUS)//<--- para validacion de estatus invalido
                    );
            if (domObject.getStatus().equals(StandardEntity.DISCONTINUED_STATUS)) {
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("No tiene permitido cambiar este estatus");
            }
            domObject = dao.makePersistent(domObject);
            if (domObject == null) {
                gsh.setOperationEstatus(0);
            } else {
                gsh.setOperationEstatus(1);
                gsh.setSavedId(domObject.getId() + "");
                final String message = getTag("genericSaveSuccess", CRUD_Generic.class).replace(":code", domObject.getCode());
                gsh.setSuccessMessage(message);
            }
            return gsh;


        } catch (Exception ex) {
            getLogger().error(null, ex);
        }
        return null;
    }
    
    @SMDMethod
    public List<ITextHasValue> getSupportedLanguages() {
        List<ITextHasValue> supportedLanguages = new ArrayList<>(LocalizedUtil.getSupportedLanguages());
        final String locale = getLocale().toString();
        if (Utilities.areEntitiesLocalized()) {
            ITextHasValue userLocale = LocalizedUtil.getLocaleByKey(locale);
            if (userLocale != null) { 
                supportedLanguages.remove(userLocale);
            }
        }
        return supportedLanguages;
    }
    
    @SMDMethod
    public Set<LocalizedFieldDTO> loadLocalizedFields() {
        if (!Utilities.areEntitiesLocalized()) {
            return HashSet.newHashSet(0);
        }
        return LocalizedUtil.loadLocalizedFields(className);
    }
    
    @SMDMethod
    public String loadUserLocale() {
        if (!Utilities.areEntitiesLocalized()) {
            return "";
        }
        return LocalizedUtil.getLocaleByKey(getLocale().toString()).getText();
    }
    
    @SMDMethod
    public Map<String, String> loadLocalized(Long id) {
        if (!Utilities.areEntitiesLocalized()) {
            return HashMap.newHashMap(0);
        }
        IUntypedDAO dao = getUntypedDAO();
        return LocalizedUtil.loadLocalizedEntity(getClassName(), id, dao);
    }
    
    @SMDMethod
    public Map<String, String> loadLocalizedComposite(Map<String, Long> ids) {
        if (!Utilities.areEntitiesLocalized()) {
            return HashMap.newHashMap(0);
        }
        IUntypedDAO dao = getUntypedDAO();
        return LocalizedUtil.loadLocalizedEntity(getClassName(), ids, dao);
    }
    
    @SMDMethod
    public GenericSaveHandle saveLocalized(Map<String, Object> params, Map<String, List<Map<String, String>>> data) {
        if (!Utilities.areEntitiesLocalized()) {
            GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage("LOCALIZE_ENTITIES_DISABLED");
            return gsh;
        }
        IUntypedDAO dao = getUntypedDAO();
        return LocalizedUtil.saveLocalized(params, data, getClassName(), dao);
    }

    @SMDMethod
    public Boolean hasValidSession() {
        return getLoggedUserId() != null && !getLoggedUserId().equals(0L);
    }

    public Class getParameterizedType() {
        return parameterizedType;
    }
    
    
}