package Framework.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.Persistable;
import Framework.Config.ITextHasValue;
import Framework.Config.TextCodeValue;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.ColumnDTO;
import ape.pending.entities.PendingRecord;
import ape.pending.util.RecordRowsType;
import bnext.exception.MakePersistentException;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import javax.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.EntityManager;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.hibernate.Session;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.framework.core.IPersistenceManager;
import qms.framework.util.CacheRegion;
import qms.util.QMSException;
import qms.util.interfaces.IGridFilter;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.IPagedQuery;
import qms.util.interfaces.IPersistableDescription;
import qms.util.interfaces.SessionView;

/**
 * <AUTHOR> Carlos Limas @ Block Networks S.A. de C.V. © 2014
 */
@SuppressWarnings("rawtypes")
public interface IUntypedDAO extends SessionView, IMakePersistentDAO {

    /**
     * @see GenericDAOImpl#makePersistent(java.lang.Object)
     * @deprecated Utilizar makePersistent con parámetro loggedUserId
     */
    <TYPE> TYPE makePersistent(TYPE entity);

    <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId, Boolean generateLogging);

    <TYPE> TYPE saveOrUpdate(final TYPE entity, Long loggedUserId) throws MakePersistentException, BatchUpdateException;

    <TYPE> TYPE saveOrUpdate(final TYPE entity, Long loggedUserId, boolean generateLogging) throws MakePersistentException, BatchUpdateException;

    /**
     * @see GenericDAOImpl#getId(java.lang.Object)
     */
    <TYPE, ID> ID getId(TYPE instancia);

    <TYPE> void makeTransient(TYPE entity);

    /**
     * Los entities sufren modificaciones conforme el paso del tiempo,
     * potencialmente/eventualmente volviendose muy pesados.
     *
     * @deprecated Utilizar "HQL_findByQuery" para cargar solo las columnas requeridas.
     */
    <TYPE> List<TYPE> HQLT_findByQueryFilter(Class<TYPE> TYPE, String filtros);

    <TYPE> List<TYPE> HQLT_findByQueryFilter_NEntityFilter(Class<TYPE> TYPE, String filtro, String[] filtros);

    void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin);

    /*HQL methods*/
    <TYPE> TYPE HQLT_findById(Class<TYPE> TYPE, Long id);

    <TYPE> TYPE HQLT_findById(
            Class<TYPE> TYPE,
            Long id,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL);

    @Nullable
    <TYPE> TYPE HQLT_findSimple(
            Class<TYPE> type,
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nullable
    <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL, String paramName, Object value);

    /**
     * @deprecated Utilizar methods que reciben `params`
     */
    <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL);

    <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL, Map<String, Object> params);

    <TYPE> List<TYPE> HQLT_findByQuery(
            Class<TYPE> type,
            String HQL,
            Map<String, Object> params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL, String paramName, Object value);

    @Deprecated
    <TYPE> List<TYPE> HQLT_findByPagedFilter(Class<TYPE> TYPE, IGridFilter filter);

    @Deprecated
    <TYPE> GridInfo<TYPE> getRows(Class<TYPE> TYPE, IGridFilter filter);

    @Deprecated
    <TYPE> GridInfo<TYPE> getRows(
            Class<TYPE> TYPE,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            IGridFilter filter
    );

    List<ITextHasValue> getActives(Class<? extends IPersistableDescription> cls);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    Integer HQL_updateByQuery(String hql);

    /**
     * Este metodo guarda historico de modificación de bitacora.
     * <p>
     * Modifica el listado de atributos del `entity` enviados en `properties`, solo modifica 1 registro a
     * la vez, el que coincida con el ID de `recordId`.
     */
    @Nonnull
    Integer HQL_updateByQuery(
            Class<? extends Persistable> entity,
            Map<String, Object> params,
            Long loggedUserId,
            Long recordId,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            String observation
    );

    /**
     * Este metodo guarda historico de modificación de bitacora.
     * <p>
     * Modifica el listado de atributos del `entity` enviados en `properties`, modifica varios registros a
     * la vez, el que coincida con el ID de `recordId`, debido a que se envía un entity
     * compuesto, el valor de `customPropId` debe ser el nombre del atributo que coinciden con `recordId`.
     * <p>
     * Ejemplo donde `registryTime.getId()` es el id que corresponde a `id.timesheetId`:
     * HQL_updateByQuery(TimesheetActivity.class, props, loggedUser.getId(), registryTime.getId(), "id.timesheetId");
     */
    @Nonnull
    Integer HQL_updateByQuery(
            Class<?> entity,
            Map<String, Object> properties,
            ILoggedUser loggedUser,
            Long recordId,
            String customPropId,
            String alias,
            String extraWhere,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nonnull
    Integer HQL_updateByQuery(String hql, Map m);

    @Nonnull
    Integer HQL_updateByQuery(
            String hql,
            Map m,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * con un solo parametro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like :code "
     * ,"code","0001"
     * );
     *
     * @param hql       el query HQL
     * @param paramName parametro
     * @param value     valor del parametro
     * @return renglones
     * <AUTHOR>
     * @since 2.3.2.126
     */
    @Nonnull
    Integer HQL_updateByQuery(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nullable
    Object HQL_findSimpleObject(String hql);

    @Nullable
    Object HQL_findSimpleObject(String hql, Map params);

    @Nullable
    Object HQL_findSimpleObject(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nullable
    Object HQL_findSimpleObject(String hql, String paramName, Object value);

    @Nullable
    Object HQL_findSimpleObject(
            String hql,
            String paramName,
            Object value,
            Boolean cacheable,
            CacheRegion cacheRegion
    );

    @Nullable
    Object HQL_findSingleObject(String hql, Map params);

    @Nullable
    Object HQL_findSingleObject(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion
    );

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve un CERO (0L)
     *
     * @deprecated Utilizar métodos que reciben `params`
     */
    @Nonnull
    Long HQL_findSimpleLong(String hql);

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve un CERO (0L)
     */
    @Nonnull
    Long HQL_findSimpleLong(String hql, Map params);

    @Nonnull
    Long HQL_findSimpleLong(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve un CERO (0L)
     */
    @Nonnull
    Long HQL_findSimpleLong(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    Date HQL_findSimpleDate(String hql);

    @Nonnull
    Date HQL_findSimpleDate(String hql, Map params);

    @Nonnull
    Date HQL_findSimpleDate(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    Boolean HQL_findSimpleBoolean(String hql);

    @Nonnull
    Boolean HQL_findSimpleBoolean(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nonnull
    Boolean HQL_findSimpleBoolean(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    Integer HQL_findSimpleInteger(String hql);

    @Nonnull
    Integer HQL_findSimpleInteger(String hql, Map params);

    @Nonnull
    Integer HQL_findSimpleInteger(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nonnull
    Integer HQL_findSimpleInteger(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    String HQL_findSimpleString(String hql);

    @Nonnull
    String HQL_findSimpleString(String hql, Map params);

    @Nonnull
    String HQL_findSimpleString(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nonnull
    String HQL_findSimpleString(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    String HQL_findSingleString(String hql);

    String HQL_findSingleString(String hql, Map params);

    String HQL_findSingleString(String hql, String paramName, Object value);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    <K,V> Map<K,V> HQL_findSimpleMap(String hql);

    @Nonnull
    <K,V> Map<K,V> HQL_findSimpleMap(String hql, Map params);

    @Nonnull
    <K,V> Map<K,V> HQL_findSimpleMap(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    @Nonnull
    <K,V> Map<K,V> HQL_findSimpleMap(String hql, String paramName, Object value);

    List HQL_findByQueryLimit(String HQL, Integer limit);

    List HQL_findByQueryLimit(String HQL, Integer limit, Integer queryTimeoutSeconds);

    /**
     * @deprecated Utilizar metodos que reciben `params`
     */
    <T> List<T> HQL_findByQuery(String hql);


    <T> List<T> HQL_findByQuery(String hql, Map params);

    <T> List<T> HQL_findByQuery(String hql, String paramName, Object value);

    @Nonnull
    List HQL_findByQuery(
            String hql,
            Map<String, Object> params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List HQL_findByQueryPaged(String hql, IPagedQuery page);

    List HQL_findByQueryPaged(
            String hql,
            Map<String, Object> params,
            IPagedQuery page,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List HQL_findByQueryPaged(String hql, String paramName, Object value, IPagedQuery page);

    //Parametros fijos
    List HQL_findByQueryIdx(String hql, Map<String, Object> params);

    List HQL_findByQueryIdx(String hql, String paramName, Object value);

    @Nonnull
    Integer HQL_updateByQueryIdx(String hql, Map m);

    @Nonnull
    Integer HQL_updateByQueryIdx(String hql, String paramName, Object value);

    @Nonnull
    Integer HQL_findSimpleIntegerIdx(String hql, Map params);

    @Nonnull
    Long HQL_findSimpleLongIdx(String hql, Map params);

    @Nullable
    Object HQL_findSimpleObjectIdx(String hql, Map params);

    /**
     * Recibe un HQL sin la condición WHERE incluida, solo acepta SELECT c FROM x JOIN
     * <p>
     * Los filtros generados por "IGridFilter" se agregan al final del HQL
     *
     * @param HQL: Es un HQL completo, SELECT c FROM class WHERE 1=1
     */
    <T> GridInfo<T> HQL_getRowsByQuery(StringBuilder HQL, IGridFilter filter);

    /**
     * {@code @deprecated:} Utilizar el que recibe `StringBuilder`
     */
    <T> GridInfo<T> HQL_getRowsByQuery(String HQL, IGridFilter filter);

    /**
     * {@code @deprecated:} Utilizar el que recibe `StringBuilder`
     */
    <T> GridInfo<T> HQL_getRowsByQuery(
            String HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Recibe un HQL sin la condición WHERE incluida, solo acepta SELECT c FROM x JOIN
     * <p>
     * Los filtros generados por "IGridFilter" se agregan al final del HQL
     *
     * @param HQL: Es un HQL completo, SELECT c FROM class WHERE 1=1
     */
    <T> GridInfo<T> HQL_getRowsByQuery(StringBuilder HQL, IGridFilter filter, boolean like);

    /**
     * {@code @deprecated:} Utilizar el que recibe `StringBuilder`
     */
    <T> GridInfo<T> HQL_getRowsByQuery(String HQL, IGridFilter filter, boolean like);

    /**
     * Recibe un HQL con la condición WHERE incluida,
     * los filtros generados por "IGridFilter" se injectan justo despues del WHERE
     * <p>
     * Known issue: Las columnas no pueden contener la palabra "from"
     *
     * @param HQL: Es un HQL completo, SELECT c FROM class WHERE 1=1
     */
    <T> GridInfo<T> HQL_getRows(
            StringBuilder HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * {@code @deprecated:} Utilizar el que recibe `StringBuilder`
     */
    <T> GridInfo<T> HQL_getRows(String HQL, IGridFilter filter);

    /**
     * {@code @deprecated:} Utilizar el que recibe `StringBuilder`
     */
    <T> GridInfo<T> HQL_getRows(
            String HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve los resultados reestructurados como los requiere el componente <app-grid-tree>
     * <p>
     * Recibe un HQL con la condición WHERE incluida,
     * los filtros generados por "IGridFilter" se injectan justo despues del WHERE
     * <p>
     * Known issue: Las columnas no pueden contener la palabra "from"
     *
     * @param HQL                          : Es un HQL completo, SELECT c.x FROM class WHERE 1=1 GROUP BY c.x
     * @param rowIdFieldName               : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName         : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName        : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate     : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction  : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     * @param peekProcessParentRowConsumer : (Opcional) Es una función que no regresa nada, utilizarla para "parchar" errores en los rows (Ej. row.put("childCount", 0);
     *                                     <p>
     *                                     Las 3 funciones se ejecutan en este orden:
     *                                     1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                     2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                     3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo<Map<String, Object>> HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Sobrecarga de "HQL_getTreeRows" sin el consumer "peekPreprocessRowConsumer"
     *
     * @param HQL                         : Es un HQL completo, SELECT c.x FROM class WHERE 1=1 GROUP BY c.x
     * @param rowIdFieldName              : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName        : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName       : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate    : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     *                                    <p>
     *                                    Las 3 funciones se ejecutan en este orden:
     *                                    1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                    2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                    3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo<Map<String, Object>> HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve los resultados reestructurados como los requiere el componente <app-grid-tree>
     * <p>
     * Recibe un HQL con la condición WHERE incluida,
     * los filtros generados por "IGridFilter" se injectan justo despues del WHERE
     * <p>
     * Known issue: Las columnas no pueden contener la palabra "from"
     *
     * @param HQL                          : Es un HQL completo, SELECT c.x FROM class WHERE 1=1 GROUP BY c.x
     * @param rowIdFieldName               : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName         : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName        : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate     : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction  : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     * @param peekProcessParentRowConsumer : (Opcional) Es una función que no regresa nada, utilizarla para "parchar" errores en los rows (Ej. row.put("childCount", 0);
     *                                     <p>
     *                                     Las 3 funciones se ejecutan en este orden:
     *                                     1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                     2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                     3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo<Map<String, Object>> HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Sobrecarga de "HQL_getTreeRows" sin el consumer "peekPreprocessRowConsumer"
     *
     * @param HQL                         : Es un HQL completo, SELECT c.x FROM class WHERE 1=1 GROUP BY c.x
     * @param rowIdFieldName              : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName        : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName       : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate    : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     *                                    <p>
     *                                    Las 3 funciones se ejecutan en este orden:
     *                                    1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                    2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                    3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo<Map<String, Object>> HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Recibe un HQL con la condición WHERE incluida,
     * los filtros generados por "IGridFilter" se injectan justo despues del WHERE
     * <p>
     * Cuando se utiliza GROUP BY el WHERE es obligatorio
     *
     * @param HQL: Es un HQL completo, SELECT c FROM class WHERE 1=1
     */
    GridInfo HQL_getRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    GridInfo HQL_getRows(String HQL, IGridFilter filter, String entityAlias);

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    GridInfo HQL_getRowsByQuery(String query, IGridFilter filter, String alias);

    GridInfo HQL_getRowsByQuery(StringBuilder query, IGridFilter filter, String alias);

    /**
     * Recibe un HQL con la condición WHERE incluida,
     * los filtros generados por "IGridFilter" se injectan justo despues del WHERE
     *
     * @param HQL: Es un HQL completo, SELECT c FROM class WHERE 1=1
     */
    GridInfo HQL_getRows(String HQL, IGridFilter filter, boolean like);

    List HQL_getListResultsByQuery(String query, IGridFilter filter);

    List HQL_getListResultsByQuery(
            String query,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /*Select HQL*/
    List HQL_selectQuery(String query, Object... params);

    List<Map<String, Object>> HQL_selectMapQuery(
            String HQL,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            Object... params
    );

    @Nonnull
    Integer HQL_updateQuery(String hql, Object... params);

    Long HQL_uniqueLong(String HQL, Object... params);

    Object HQL_uniqueObject(String hql, Object[] params);

    @Nonnull
    Integer HQL_updateByQuery(String hql, Object[] params);

    List HQL_findByQuery(String HQL, Integer queryTimeoutSeconds);

    List HQL_findByQuery(String HQL, Object[] params);

    List HQL_findByQuery(
            String HQL,
            Object[] params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List HQL_findByQueryLimit(String hql, Map params, Integer maxResults);

    List HQL_findByQueryLimit(
            String hql,
            Map params,
            Integer maxResults,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /*Select SQL*/
    List<Object[]> SQL_findQuery(String SQL);

    <X> X SQL_findUniqueResult(String SQL, Class<X> cls);

    /*Context DAOs*/
    <TYPE> TYPE getBean(Class<TYPE> interfaceClass);

    Object getBean(String beanName);

    ICodeSequenceDAO getCodeSequence();

    EntityManager getEntityManager();

    @Override
    Session getSession();

    /*Native SQL methods*/
    Integer SQL_updateByQuery(
            final String SQL,
            final Map params,
            final List<String> synchronizeTables
    );

    Integer SQL_updateByQuery(
            String SQL,
            Map m,
            Integer queryTimeoutSeconds,
            List<String> synchronizeTables
    );

    Integer SQL_externalUpdateByQuery(
            String SQL,
            Map<String, Object> params,
            Connection connection,
            String sessionStatement,
            Integer queryTimeoutSeconds
    );

    /**
     * Utilizado para obtener solo un Long en especifico por un query SQL
     * <p>
     * Ejemplo :
     * dao.SQL_findSimpleLong(
     * "SELECT c.id FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> jnicolas
     * @since : 2.8.0.1
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Nonnull
    @Deprecated
    Long SQL_findSimpleLong(String SQL);

    /**
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Nonnull
    @Deprecated
    Long SQL_findSimpleLong(String SQL, String key, Object value);

    /**
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Nonnull
    @Deprecated
    Long SQL_findSimpleLong(String SQL, Map<String, Object> params);

    @Nonnull
    Long SQL_findSimpleLong(String SQL, Integer queryTimeoutSeconds);

    @Nonnull
    Long SQL_findSimpleLong(String SQL, String key, Object value, Integer queryTimeoutSeconds);

    @Nonnull
    Long SQL_findSimpleLong(
            String SQL,
            Map<String, Object> params,
            Integer queryTimeoutSeconds
    );

    /**
     * @param SQL Query
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Nonnull
    @Deprecated
    Integer SQL_findSimpleInteger(String SQL);

    /**
     * @param SQL Query
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Nonnull
    @Deprecated
    Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params);

    /**
     * @param SQL Query
     * @deprecated Use method with queryTimeoutSeconds
     */
    @Deprecated
    Integer SQL_findSimpleInteger(String SQL, String key, Object value);

    @Nonnull
    Integer SQL_findSimpleInteger(String SQL, Integer queryTimeoutSeconds);

    @Nonnull
    Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params, Integer queryTimeoutSeconds);

    @Nonnull
    Integer SQL_findSimpleInteger(String SQL, String key, Object value, Integer queryTimeoutSeconds);

    @Nonnull
    Integer SQL_findSimpleInteger(String sqlSelect, String key, String value, Connection connection, String sessionStatement, Integer queryTimeoutSeconds);

    @Nonnull
    Integer SQL_findSimpleInteger(String sqlSelect, Map<String, Object> params, Connection connection, String sessionStatement, Integer queryTimeoutSeconds);

    @Nonnull
    Long SQL_findSimpleLong(String sqlSelect, String key, String value, Connection connection, String sessionStatement, Integer queryTimeoutSeconds);

    @Nonnull
    Long SQL_findSimpleLong(String sqlSelect, Map<String, Object> params, Connection connection, String sessionStatement, Integer queryTimeoutSeconds);

    @Nonnull
    Object[] SQL_findSimpleQuery(String SQL, Map params);

    Map SQL_findSimpleMap(String SQL);

    Map SQL_findSimpleMap(String SQL, String key, String value);

    Map SQL_findSimpleMap(String SQL, Map params);

    List<Map<String, Object>> SQL_findByQuery(String SQL, Map<String, Object> params, Connection connection, String sessionStatement);

    @Deprecated
    @Nonnull
    String SQL_findSimpleString(String SQL);

    @Deprecated
    @Nonnull
    String SQL_findSimpleString(String SQL, Map params);

    @Nonnull
    String SQL_findSimpleString(String SQL, Integer queryTimeoutSeconds);

    @Nonnull
    String SQL_findSimpleString(String SQL, Map params, Integer queryTimeoutSeconds);

    List SQL_findByQuery(String SQL);

    List SQL_findByQuery(String SQL, Map m);

    List SQL_findByQuery(String SQL, String key, Object value);


    boolean deleteEntity(Object entity);

    void refresh(Object entity);

    void enableFilter(String filterName);

    void disableFilter(String filterName);

    List<ITextHasValue> getStrutsComboList();

    List<ITextHasValue> getStrutsComboList(Boolean withStatus);

    List<ITextHasValue> getStrutsComboList(Long id);

    List<ITextHasValue> getStrutsComboList(String filtros);

    List<ITextHasValue> getStrutsComboList(String filtros, Boolean withStatus);

    List<ITextHasValue> getStrutsComboList(String description, String filtros);

    List<ITextHasValue> getStrutsComboList(String filtros, Long id);

    List<ITextHasValue> getStrutsComboList(String mappedDescription, String filtros, Long id, Boolean withStatus);

    List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription, String filtros);

    List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription);

    List<ITextHasValue> getStrutsComboList(
            Class clazz,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription, String filtros, Long id, Boolean withStatus);

    <TEXT extends ITextHasValue> List<TEXT> getStrutsComboList(
            Class<TEXT> typeList,
            Class typedClazz,
            String mappedColumns,
            String filtros,
            List<Long> ids,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List<TextCodeValue> getStrutsTextCodeComboList(
            Class clazz,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    List<TextCodeValue> getStrutsTextCodeComboList(
            Class clazz,
            String filtros,
            List<Long> ids,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    GenericSaveHandle successGSH();

    GenericSaveHandle successGSH(boolean nuevo);

    GenericSaveHandle successGSH(boolean nuevo, Object id);

    Boolean SQL_createView(String nameView, String sql) throws QMSException;

    String SQL_convertDate(String tipoDatoDestino, String fecha, String formato);

    void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Long loggedUserId
    );

    void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Integer stage,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds,
            final Long loggedUserId
    );

    void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Long loggedUserId
    );

    void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Integer stage,
            final Long loggedUserId
    );

    Boolean SQL_execute(String sql) throws QMSException;

    Boolean SQL_execute(String sql, Integer timeoutSecounds) throws QMSException;
    
    Boolean SQL_execute(String sql, Connection connection) throws QMSException; 

    GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, IPendingOperation... pendings);

    GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, IPendingOperation... pendings);

    GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, IPendingOperation... pendings);

    GridInfo getRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings);

    GridInfo getRecordRowsWithNewTx(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings);

    GridInfo getLightRows(
            IGridFilter filter,
            Class entityClass,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    GridInfo getLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    <TYPE> GridInfo<TYPE> getRowsByProjection(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            Class<TYPE> projectionClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve los resultados reestructurados como los requiere el componente <app-grid-tree>
     *
     * @param filter                       : Son los filtros enviados desde el front. Adicional, se utiliza para el botón "show more" con el atributo "GridShowMore"
     * @param entityClass                  : Es el entity de tipo "BaseAPE" que tiene un HQL precalculado desde EntityModelCache
     * @param rowIdFieldName               : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName         : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName        : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate     : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction  : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     * @param peekProcessParentRowConsumer : (Opcional) Es una función que no regresa nada, utilizarla para "parchar" errores en los rows (Ej. row.put("childCount", 0);
     *                                     <p>
     *                                     Las 3 funciones se ejecutan en este orden:
     *                                     1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                     2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                     3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Sobrecarga de "getTreeLightRows" sin el consumer "peekPreprocessRowConsumer"
     */
    GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve los resultados reestructurados como los requiere el componente <app-grid-tree>
     *
     * @param filter                       : Son los filtros enviados desde el front. Adicional, se utiliza para el botón "show more" con el atributo "GridShowMore"
     * @param entityClass                  : Es el entity de tipo "BaseAPE" que tiene un HQL precalculado desde EntityModelCache
     * @param rowIdFieldName               : Es el nombre del campo con el ID de tipo "Long" (Ej. "entity_id")
     * @param rowParentIdFieldName         : Es el nombre del campo con el ID de tipo "Long" donde se guarda la relación con el padre (Ej. "entity_recurrenceId")
     * @param rowChildListFieldName        : Es el nombre del campo donde se guardarán todos los hijos de un padre (Ej. "childs")
     * @param filterParentIdsPredicate     : Es una función que debe regresar "true" siempre que encuentre un "row" de tipo "parent" (Ej. "entity.commitmentTask == CommitmentTask.PROGRAM.getValue()")
     * @param childCountParentRowFunction  : Es una función que debe devolver la cantidad de "childs" real que hay dentro de las filas padre, decide si se mostrará o no el botón "Show more" (Ej. row.get("childCount"))
     * @param peekProcessParentRowConsumer : (Opcional) Es una función que no regresa nada, utilizarla para "parchar" errores en los rows (Ej. row.put("childCount", 0);
     * @param fields                       : Nombres de las columnas de la consulta
     * @param join                         : Joins de las tablas de la consulta
     *                                     <p>
     *                                     Las 3 funciones se ejecutan en este orden:
     *                                     1) filterParentIdsPredicate         : Una sola vez para obtener los renglones padre
     *                                     2) peekPreprocessRowConsumer        : Una vez por cada renglón padre para parchar cualquier cosa antes de contar en "childCountFunction"
     *                                     3) childCountFunction               : Una vez por cada renglón padre para colocar los hijos y el botón "show more"
     */
    GridInfo getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );


    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     */
    IUntypedDAO getAspectJAutoProxy();

    List<Map<String, Object>> SQL_findMap(String SQL);

    List<Map<String, Object>> SQL_findMap(String SQL, Map params);

    List<Map<String, Object>> SQL_findMap(String SQL, String key, String value);

    GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String sqlSelect,
            final IGridFilter filter
    );

    GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String sqlWith,
            final String sqlSelect,
            final IGridFilter filter
    );

    Long SQL_findSingleLong(String query, Map<String, Object> params);

    String SQL_findSingleString(String query, Map<String, Object> params);

    Object SQL_findSingleObject(String SQL, Map<String, Object> params);

    String getTag(String key);

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     *
     * @return NULL | new Long()
     */
    Long HQL_findLong(String hql);

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     *
     * @return NULL | new Long()
     */
    Long HQL_findLong(String hql, Map params);

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     *
     * @return NULL | new Long()
     */
    Long HQL_findLong(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve el valor tipo "String", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     *
     * @return NULL | String
     */
    String HQL_findString(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    /**
     * Devuelve el valor tipo "Long", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     *
     * @return NULL | new Long()
     */
    Long HQL_findLong(String hql, String paramName, Object value);

    /**
     * Devuelve el valor tipo "Date", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     */
    Date HQL_findDate(String hql);

    /**
     * Devuelve el valor tipo "Date", en caso de no contar con
     * información o ser un dato invalido devuelve NULL
     */
    Date HQL_findDate(String hql, Map params);

    <TYPE> List<TYPE> HQL_findAll(Class<TYPE> TYPE);

    <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter);

    <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter, boolean like);

    <TYPE> Long HQL_countByPagedFilter(
            Class<TYPE> TYPE,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    <TYPE> List<TYPE> HQL_getListResultsByQuery(
            Class<TYPE> TYPE,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    );

    <TYPE extends Persistable> TYPE makeCustomPersistent(
            TYPE entity,
            Boolean useCodePrefix,
            ILoggedUser loggedUser,
            IPersistenceManager... managers) throws QMSException;

    <TYPE extends Persistable> TYPE saveSingleLinkedItem(
            Class<? extends ILinkedCompositeEntity> compositeEntity,
            TYPE singleLinkedItem,
            Long parentId,
            Integer stage,
            Long loggedUserId
    );

    <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(
            Class<TYPE> cls,
            Long id,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            ILoggedUser loggedUser
    );

    <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(
            Class<TYPE> cls, Long id,
            Integer ACTIVE,
            Integer INACTIVE,
            ILoggedUser loggedUser
    );

    String getRowsSQL(Class entityClass);

    String hqlToSqlString(String HQL);
}
