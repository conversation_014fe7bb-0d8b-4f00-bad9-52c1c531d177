package Framework.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.AccessHistory;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Log;
import DPMS.Mapping.Persistable;
import DPMS.Mapping.Profile;
import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.StandardEntity;
import Framework.Config.TextCodeValue;
import Framework.Config.TextHasValue;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingHelper;
import ape.pending.dto.ColumnDTO;
import ape.pending.entities.PendingCount;
import ape.pending.entities.PendingRecord;
import ape.pending.util.AttenderHQLBuilder;
import ape.pending.util.RecordRowsType;
import ape.pending.util.SqlQueryParser;
import bnext.aspect.IExcludeLogging;
import bnext.exception.ExplicitRollback;
import bnext.exception.MakePersistentException;
import bnext.licensing.LicenseUtil;
import bnext.reference.IAuditable;
import com.google.common.collect.ImmutableMap;
import isoblock.common.beanGeneric;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.servlet.ServletContext;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import jakarta.annotation.Nullable;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.IStatusEnum;
import mx.bnext.core.util.StringLongPair;
import org.hibernate.HibernateException;
import org.hibernate.LockOptions;
import org.hibernate.NonUniqueResultException;
import org.hibernate.QueryException;
import org.hibernate.Session;
import org.hibernate.engine.spi.EntityKey;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.DataException;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.hibernate.query.SemanticException;
import org.hibernate.stat.SessionStatistics;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.activity.util.ActivityTimezoneFields;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicFieldHandler;
import qms.custom.core.DynamicTableHelper;
import qms.custom.core.EntityDynamicFields;
import qms.custom.core.PersistableDynamicEntity;
import qms.custom.dto.DynamicGridConfigSQL;
import qms.custom.dto.IDynamicGridConfigHQL;
import qms.custom.dto.ValidationDTO;
import qms.framework.core.GenericBean;
import qms.framework.core.IPersistenceManager;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.entity.BulkLoad;
import qms.framework.entity.BulkLoadRow;
import qms.framework.entity.BulkUser;
import qms.framework.entity.BulkUserRecord;
import qms.framework.initialload.CreationType;
import qms.framework.initialload.ICreationTypeAware;
import qms.framework.util.CacheRegion;
import qms.framework.util.DAOErrorHandler;
import qms.framework.util.DaoCacheUtils;
import qms.framework.util.DaoParserHandler;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.ISqlDAO;
import qms.framework.util.LocaleUtil;
import qms.framework.util.SessionFilterHandler;
import qms.framework.util.SqlDAOImpl;
import qms.springconfig.QMSProperties;
import qms.util.DAOException;
import qms.util.EntityCommon;
import qms.util.HQLHandler;
import qms.util.LinkedCompositeUtil;
import qms.util.QMSException;
import qms.util.QueryHandler;
import qms.util.ReflectionUtil;
import qms.util.SQLHandler;
import qms.util.StatisticsService;
import qms.util.TimezoneActivityColumns;
import qms.util.Translate;
import qms.util.interfaces.IGridFilter;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.IPagedQuery;
import qms.util.interfaces.IPersistableDescription;


/**
 * @param <T>
 * @param <ID>
 * <AUTHOR> Limas
 */
@Service
@SuppressWarnings({"rawtypes", "SpringTransactionalMethodCallsInspection", "unchecked", "DeprecatedIsStillUsed", "LoggingSimilarMessage", "SqlSourceToSinkFlow"})
public class GenericDAOImpl<T, ID extends Serializable> extends GenericBean implements IGenericDAO<T, ID> {

    public static boolean IS_SQL_SERVER = true;

    private static final Logger HIBERNATE_SQL_LOGGER = LoggerFactory.getLogger("org.hibernate.SQL.HIBERNATE_SQL_LOGGER");

    @PersistenceContext
    private EntityManager entityManager;
    private Class<T> persistentClass;
    private final SQLHandler sqlHandler = new SQLHandler();
    private final ISqlDAO sqlDao = new SqlDAOImpl();
    private final DAOErrorHandler errorHandler = new DAOErrorHandler();
    private final SessionFilterHandler sessionHandler = new SessionFilterHandler();
    private final DaoParserHandler parserHandler = new DaoParserHandler();
    private DynamicFieldHandler handler;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ICodeSequenceDAO getCodeSequence() {
        return getBean(ICodeSequenceDAO.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    public void setEntityManager(EntityManager sessionFactory) {
        this.entityManager = sessionFactory;
    }

    public GenericDAOImpl(ServletContext servletContext, QMSProperties qmsProperties) {
        super.setServletContext(servletContext); //<--- session is allways the same
        IS_SQL_SERVER = qmsProperties.getSqlServer();
    }


    /**
     * Este metodo debe ser @Overriden para cada entity (Hibernate_DAO) con los filtros correspondientes
     * los cuales deben ser colocados con filter.getCriteria().put("<filtered-entity>", "condicion");
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        final Integer corpUserQuery = HQL_findSimpleInteger(""
                + " SELECT MAX(c.intBUsuarioCorporativo)"
                + " FROM " + Profile.class.getCanonicalName() + " c"
                + " WHERE EXISTS ("
                + " SELECT u.id"
                + " FROM " + User.class.getCanonicalName() + " as u"
                + " JOIN u.puestos p "
                + " JOIN p.perfil as perfil"
                + " WHERE c.id = perfil.id"
                + " AND u.id = :id"
                + ")", "id", Long.valueOf(userId));
        setValidEntitiesFilter(filter, userId, servicio, isAdmin, corpUserQuery == 1);
    }

    protected void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin, boolean isCorpUser) {
        // Empty implementation, meant to be overriden
    }

    /**
     * Utilizar metodos query y finds
     *
     * @return session
     * @see Framework.DAO.GenericDAOImpl#HQL_findByQuery(java.lang.String, java.util.Map)
     * @deprecated
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Session getSession() {
        final Session session = this.entityManager.unwrap(Session.class);
        return session;
    }

    /**
     * Basic constructor
     */
    public GenericDAOImpl() {
        this.persistentClass = (Class<T>) ReflectionUtil.getParametrizedType(getClass());
    }


    /**
     * Utilizar directamente la etiqueta `getTag(String key)`
     *
     * @deprecated
     */
    private ResourceBundle getTags() {
        ResourceBundle tags = null;
        Language lang = getClass().getAnnotation(Language.class);
        String bundle;
        if (lang != null) {
            bundle = lang.module()[0];
            tags = LocaleUtil.getSystemI18n(bundle);
        } else {
            getLogger().error("Language {} IS NOT CONFIGURED", getClass());
        }
        return tags;
    }

    @Override
    public String getTag(String key) {
        ResourceBundle tags = getTags();
        return LocaleUtil.getTag(key, tags);
    }

    @SuppressWarnings("unused")
    public GenericDAOImpl(Session s) {
        try {
            this.persistentClass = (Class<T>) ((ParameterizedType) getClass()
                    .getGenericSuperclass()).getActualTypeArguments()[0];
        } catch (ClassCastException e) {
            getLogger().error("There was an error ", e);
        } catch (Exception e) {
            errorHandler.stackTraceHandle(e);
        }
    }

    public GenericDAOImpl(Class t) {
        this.persistentClass = t;
    }

    @Override
    public Class<T> getPersistentClass() {
        return persistentClass;
    }

    /**
     * @param persistentClass Entity class
     * @deprecated No modificar persistentClass debido a que los bean son singleton y afectaría a otros request.
     */
    @Override
    @Deprecated
    public void setPersistentClass(final Class<T> persistentClass) {
        this.persistentClass = persistentClass;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean deleteEntity(Object entity) {
        try {
            if (entity instanceof Persistable) {
                Persistable e = (Persistable) entity;
                if (this.entityManager.contains(e)) {
                    entityManager.remove(e);
                } else {
                    Object o = entityManager.getReference(e.getClass(), e.getId());
                    entityManager.remove(o);
                }
            } else {
                getSession().remove(entity);
            }
            return true;
        } catch (Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorMsgHandle(message + " - " + e.getCause(), e);
        }
        return false;
    }

    protected void errorMsgHandle(String error, Throwable cause) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.errorMsgHandle(error);
        throw new DAOException(gsh, cause);
    }

    /**
     * Utilizado para obtener solo un Object en especifico por un query HQL
     * filtrando por un parametro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQL_findSimpleObject(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * “code”,
     * "0001"
     * );
     *
     * @param hql       : query HQL
     * @param paramName : el nombre de un paramtro contenido en el HQL
     * @param value     : el valor del parametro
     * @return Object
     * <AUTHOR>
     * @since *********
     */
    @Nullable
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObject(String hql, String paramName, Object value) {
        return HQL_findSimpleObject(hql, paramName, value, false, null);
    }

    @Nullable
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObject(
            String hql,
            String paramName,
            Object value,
            Boolean cacheable,
            CacheRegion cacheRegion
    ) {
        try {
            HashMap<String, Object> params = new HashMap<>();
            params.put(paramName, value);
            return this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, 0);
        } finally {
            detachEntity();
        }
    }

    /**
     * Utilizado para obtener solo un Object en especifico por un query HQL
     * filtrando por varios parametros
     * <p>
     * Ejemplo :
     * HashMap<String,Object> mapa = new HashMap<String,Object>();
     * mapa.put("code","0001");
     * new GenericHibernateDAO<Node,Long>(){}.HQL_findSimpleObject(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * map
     * );
     *
     * @param hql    query HQL (Hibernate Query Language)
     * @param params grupo de parametros
     * <AUTHOR>
     * @since *********
     */
    @Override
    @Nullable
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObject(String hql, Map params) {
        return HQL_findSimpleObject(hql, params, false, null, 0);
    }

    @Nullable
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObject(
            final String hql,
            final Map params,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        Object r = null;
        Query q;
        try {
            getLogger().trace("{} {} ", hql, params);
            q = getSession().createQuery(hql);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            q.setMaxResults(2);
            try {
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                r = q.uniqueResult();
            } catch (NonUniqueResultException e) {
                getLogger().error("El query '{}' esta regresando mas de un resultado en un findSimple", hql);
                if (LicenseUtil.isDevelopment()) {
                    throw new ExplicitRollback("[ONLY_DEV] El query esta regresando mas de un resultado en un findSimple");
                }
                List<Object> l;
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                l = q.list();
                if (l != null && !l.isEmpty()) {
                    r = l.get(0);
                }
            }
        } catch (HibernateException e) {
            errorHandler.stackTraceHandle(hql, params, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSingleObject(String hql, Map params) {
        return HQL_findSingleObject(hql, params, false, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSingleObject(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion
    ) {
        Object r = null;
        Query q;
        try {
            getLogger().trace("{} {} ", hql, params);
            q = getSession().createQuery(hql);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            q.setMaxResults(1);
            try {
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                r = q.uniqueResult();
            } catch (NonUniqueResultException e) {
                getLogger().error("El query '{}' esta regresando mas de un resultado en un findSimple", hql);
                List<Object> l;
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                l = q.list();
                if (l != null && !l.isEmpty()) {
                    r = l.get(0);
                }
            }
        } catch (HibernateException e) {
            errorHandler.stackTraceHandle(hql, params, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    @Nullable
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObjectIdx(String hql, Map params) {
        Object r = null;
        Query q;
        try {
            getLogger().trace("{} {} ", hql, params);
            q = createQueryIdx(hql, params);
            q.setMaxResults(2);
            try {
                r = q.uniqueResult();
            } catch (NonUniqueResultException e) {
                getLogger().error("El query '{}' esta regresando mas de un resultado en un findSimple", hql);
                if (LicenseUtil.isDevelopment()) {
                    throw new ExplicitRollback("[ONLY_DEV] El query esta regresando mas de un resultado en un findSimple");
                }
                List<Object> l = q.list();
                if (l != null && !l.isEmpty()) {
                    r = l.get(0);
                }
            }
        } catch (HibernateException e) {
            errorHandler.stackTraceHandle(hql, params, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findSimpleLongIdx(String hql, Map params) {
        long _long = 0L;
        Object container;
        try {
            container = this.HQL_findSimpleObjectIdx(hql, params);
            if (container != null) {
                _long = Long.valueOf(String.valueOf(container));
            }
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return _long;
    }

    @Override
    @Nullable
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_findSimpleObject(String hql) {
        return this.HQL_findSimpleObject(hql, Utilities.EMPTY_MAP, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findSimpleLong(String hql) {
        return this.HQL_findSimpleLong(hql, null);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findSimpleLong(String hql, Map params) {
        return HQL_findSimpleLong(hql, params, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findSimpleLong(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        long _long = 0L;
        Object container;
        try {
            container = this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
            if (container != null) {
                _long = Long.valueOf(String.valueOf(container));
            }
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return _long;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findLong(String hql) {
        return this.HQL_findLong(hql, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findLong(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findLong(hql, params);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findLong(String hql, Map params) {
        return HQL_findLong(hql, params, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findLong(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Object container;
        try {
            container = HQL_findSimpleObject(hql, params, cacheable, null, queryTimeoutSeconds);
            if (container != null) {
                return Long.valueOf(String.valueOf(container));
            }
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return null;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findString(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Object container;
        try {
            container = HQL_findSimpleObject(hql, params, cacheable, null, queryTimeoutSeconds);
            if (container != null) {
                return String.valueOf(container);
            }
        } finally {
            detachEntity();
        }
        return "";
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date HQL_findDate(String hql) {
        return this.HQL_findDate(hql, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date HQL_findDate(String hql, Map params) {
        try {
            return (Date) this.HQL_findSimpleObject(hql, params);
        } catch (Exception e) {
            getLogger().error("ERROR: {}", hql, e);
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return null;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map HQL_findSimpleMap(String hql) {
        return HQL_findSimpleMap(hql, null);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map HQL_findSimpleMap(String hql, String paramName, Object value) {
        Map m = new HashMap();
        m.put(paramName, value);
        return HQL_findSimpleMap(hql, m);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map HQL_findSimpleMap(String hql, Map params) {
        return HQL_findSimpleMap(hql, params, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map HQL_findSimpleMap(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Map _map = null;
        try {
            _map = (Map) this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
        } catch (Exception e) {
            //@ToDo Implementar GenericException
            getLogger().error("ERROR: {}", hql, e);
        } finally {
            detachEntity();
        }
        if (_map == null) {
            _map = Utilities.EMPTY_MAP;
        }
        return _map;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_findSimpleLong(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findSimpleLong(hql, params);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date HQL_findSimpleDate(String hql) {
        return this.HQL_findSimpleDate(hql, null);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date HQL_findSimpleDate(String hql, Map params) {
        Date _date = new Date();
        try {
            _date = (Date) (this.HQL_findSimpleObject(hql, params));
        } catch (Exception e) {
            //@ToDo Implementar GenericException
            getLogger().error("ERROR: {}", hql, e);
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return _date;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date HQL_findSimpleDate(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findSimpleDate(hql, params);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_findSimpleInteger(String hql) {
        return this.HQL_findSimpleInteger(hql, null);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean HQL_findSimpleBoolean(String hql) {
        return this.HQL_findSimpleBoolean(hql, null, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean HQL_findSimpleBoolean(String hql, String paramName, Object value) {
        return this.HQL_findSimpleBoolean(hql, ImmutableMap.of(paramName, value), false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean HQL_findSimpleBoolean(
            String hql, Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        try {
            Object i = this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, 0);
            if (i == null) {
                getLogger().trace("HQL {} didn't found any record, map: {} ", hql, params);
            }
            if (i instanceof Boolean) {
                return Boolean.TRUE.equals(i);
            }
            if (LicenseUtil.isDevelopment()) {
                throw new ExplicitRollback("[ONLY_DEV] Invalid BOOLEAN");
            }
            return false;
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        }
        return false;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_findSimpleInteger(String hql, Map params) {
        return HQL_findSimpleInteger(hql, params, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_findSimpleInteger(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        int _integer = 0;
        try {
            Object i = this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
            if (i == null) {
                getLogger().trace("HQL {} didn't found any record, map: {} ", hql, params);
                return _integer;
            }
            _integer = Integer.valueOf(String.valueOf(i));
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        }
        return _integer;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_findSimpleIntegerIdx(String hql, Map params) {
        int _integer = 0;
        try {
            Object i = this.HQL_findSimpleObjectIdx(hql, params);
            if (i == null) {
                getLogger().trace("HQL {} didn't found any record, map: {} ", hql, params);
                return _integer;
            }
            _integer = Integer.valueOf(String.valueOf(i));
        } catch (NumberFormatException e) {
            errorHandler.stackTraceHandle(hql, e);
        }
        return _integer;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_findSimpleInteger(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findSimpleInteger(hql, params);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSimpleString(String hql) {
        return this.HQL_findSimpleString(hql, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSingleString(String hql) {
        return this.HQL_findSingleString(hql, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSingleString(String hql, Map params) {
        String _string = "";
        try {
            Object o = this.HQL_findSingleObject(hql, params);
            if (o != null) {
                _string = String.valueOf(o);
            }
        } catch (Exception e) {
            //@ToDo Implementar GenericException
            getLogger().error("ERROR: {}", hql, e);
        }
        return _string;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSimpleString(String hql, Map params) {
        return HQL_findSimpleString(hql, params, false, null, 0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSimpleString(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        String _string = "";
        try {
            Object o = this.HQL_findSimpleObject(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
            if (o != null) {
                _string = String.valueOf(o);
            }
        } catch (Exception e) {
            //@ToDo Implementar GenericException
            getLogger().error("ERROR: {}", hql, e);
        }
        return _string;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSimpleString(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findSimpleString(hql, params);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String HQL_findSingleString(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findSingleString(hql, params);
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Node c");
     *
     * @param HQL el query HQL, puede utilizarse para cualquier tipo de dato
     * @return renglones
     * <AUTHOR>
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(String HQL) {
        return this.HQL_findByQuery(HQL, new Object[]{});
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo con un mapa de filtros
     * <p>
     * Ejemplo :
     * HashMap<String,Object> map = new HashMap<String,Object>();
     * map.put("code","001");
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQLT_findByQuery(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * map
     * );
     *
     * @param HQL    : El query HQL, puede utilizarse para cualquier tipo de dato
     * @param params : Map con todos los paramtros incluidos
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(String HQL, Map params) {
        return HQL_findByQuery(HQL, params, false, null, null);
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(
            final String HQL,
            final Map<String, Object> params,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        List resultado = Utilities.EMPTY_LIST;
        try {
            final Query query = getSession().createQuery(HQL);
            DaoCacheUtils.configureCache(query, cacheable, cacheRegion);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                query.setTimeout(queryTimeoutSeconds);
            }
            if (params == null) {
                resultado = query.list();
            } else {
                resultado = query.setProperties(params).list();
            }
            if (resultado == null) {
                resultado = Utilities.EMPTY_LIST;
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, e);
        }
        return resultado;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @SuppressWarnings({"ThrowableInstanceNotThrown", "ThrowableInstanceNeverThrown"})
    public <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL) {
        Object result = HQL_findSimpleObject(HQL);
        if (result != null && type.isInstance(result)) {
            return (TYPE) result;
        } else if (result != null) {
            getLogger().error("Wrong entity type, expected '{}', found: '{}' from query: {}", new Object[]{
                    type.getCanonicalName(), result.getClass().getCanonicalName(), HQL,
                    new RuntimeException("Wrong entity type, expected '" + type.getCanonicalName() + "', found: '" + result.getClass().getCanonicalName() + "'.")
            });
        }
        return null;
    }

    @Override
    @SuppressWarnings({"ThrowableInstanceNotThrown", "ThrowableInstanceNeverThrown"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE HQLT_findSimple(
            Class<TYPE> type,
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Object result = HQL_findSimpleObject(HQL, params, cacheable, cacheRegion, queryTimeoutSeconds);
        if (result != null && type.isInstance(result)) {
            return (TYPE) result;
        } else if (result != null) {
            getLogger().error("Wrong entity type, expected '{}', found: '{}' from query: {}", new Object[]{
                    type.getCanonicalName(), result.getClass().getCanonicalName(), HQL,
                    new RuntimeException("Wrong entity type, expected '" + type.getCanonicalName() + "', found: '" + result.getClass().getCanonicalName() + "'.")
            });
        }
        return null;
    }

    @Nullable
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL, String paramName, Object value) {
        Object result = HQL_findSimpleObject(HQL, paramName, value);
        if (type.isInstance(result)) {
            return (TYPE) result;
        } else if (result != null) {
            getLogger().error("Wrong entity type, expected '{}', found: '{}' from query: {}", new Object[]{
                    type.getCanonicalName(), result.getClass().getCanonicalName(), HQL,
                    new RuntimeException("Wrong entity type, expected '" + type.getCanonicalName() + "', found: '" + result.getClass().getCanonicalName() + "'.")
            });
        }
        return null;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL) {
        return HQLT_findByQuery(type, HQL, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQuery(
            final Class<TYPE> type, String HQL,
            final String paramName,
            final Object value
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQLT_findByQuery(type, HQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQuery(
            final Class<TYPE> type,
            final String HQL,
            final Map<String, Object> params
    ) {
        return HQLT_findByQuery(type, HQL, params, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQuery(
            final Class<TYPE> type,
            final String HQL,
            final Map<String, Object> params,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        try {
            final Query<TYPE> q = getSession().createQuery(HQL);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            final List<TYPE> result;
            if (params == null) {
                result = q.list();
            } else {
                result = q.setProperties(params).list();
            }
            return result;
        } catch (final Exception e) {
            errorHandler.stackTraceHandle(HQL, e);
            return new ArrayList<>(0);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryIdx(String hql, String paramName, Object value) {
        Map<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findByQueryIdx(hql, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryIdx(String hql, Map<String, Object> params) {
        List resultado = Utilities.EMPTY_LIST;
        try {
            resultado = createQueryIdx(hql, params).list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, e);
        }
        return resultado;
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQueryIdx(String hql, String paramName, Object value) {
        Map<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_updateByQueryIdx(hql, params);
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQueryIdx(String hql, Map m) {
        int r = 0;
        try {
            if (BnextStatementInspector.isInvalidEqualNullWhere(hql)) {
                /**
                 * Revisar req #50693 y actividad ACT-10293-IMP-001
                 *
                 * Si llegaste aquí probablemente se esten modificando valores
                 * incorrectos en el UPDATE, validar la concatenación del HQL y
                 * que no lleguen nulos por error.
                 * */
                getLogger().error("LIKELY A BUG! Suspicious NULL VALUE, use 'IS NULL' instead, or try another value, [" + hql + "]");
                if (LicenseUtil.isDevelopment()) {
                    throw new QueryException("Invalid HQL syntax! `IS NULL` must be explict, ` = null` is invalid.", hql);
                }
            }
            Query q = createQueryIdx(hql, m);
            r = q.executeUpdate();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, m, e);
        }
        return r;
    }

    private Query createQueryIdx(String hql, Map<String, Object> params) {
        StringBuilder hqlBuilder = new StringBuilder(hql);
        Object paramValue;
        Set<String> dateKeys = new HashSet<>();
        int n;
        for (Map.Entry<String, Object> param : params.entrySet()) {
            String paramName = param.getKey();
            paramValue = param.getValue();
            if (hqlBuilder.indexOf(":" + paramName) == -1) {
                if (getLogger().isWarnEnabled()) {
                    getLogger().warn("Useless parameter '{}' has been set to '{}' with value '{}', it is recomended to you for removal", paramName, hql, paramValue);
                }
            } else if (paramValue instanceof Date) {
                dateKeys.add(paramName);
            } else if (paramValue instanceof String) {
                while (hqlBuilder.indexOf(":" + paramName) != -1) {
                    n = hqlBuilder.indexOf(":" + paramName);
                    hqlBuilder.replace(n, n + paramName.length() + 1, "'" + HQLHandler.sanatizeStringParameter((String) paramValue) + "'");
                }
            } else {
                while (hqlBuilder.indexOf(":" + paramName) != -1) {
                    n = hqlBuilder.indexOf(":" + paramName);
                    hqlBuilder.replace(n, n + paramName.length() + 1, paramValue.toString());
                }
            }
        }
        Query q = getSession().createQuery(hqlBuilder.toString());
        final boolean cacheable = false;
        DaoCacheUtils.configureCache(q, cacheable, null);
        for (String paramName : dateKeys) {
            handleObjectProperty(q, paramName, params.get(paramName));
        }
        return q;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(String HQL, Object[] params) {
        return HQL_findByQuery(HQL, params, false, null, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(
            String HQL,
            Object[] params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_findByQuery(HQL, params, cacheable, cacheRegion, 0, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(String HQL, Integer queryTimeoutSeconds) {
        return HQL_findByQuery(HQL, null, false, null, 0, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryLimit(String HQL, Integer limit) {
        return HQL_findByQueryLimit(HQL, limit, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryLimit(String HQL, Integer limit, Integer queryTimeoutSeconds) {
        return HQL_findByQuery(HQL, Utilities.EMPTY_ARRAY, false, null, limit, queryTimeoutSeconds);
    }

    private List HQL_findByQuery(
            String HQL,
            Object[] params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer maxResults,
            Integer queryTimeoutSeconds
    ) {
        List resultado = Utilities.EMPTY_LIST;
        try {
            Query q = getSession().createQuery(HQL);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            if (maxResults != null && maxResults > 0) {
                q.setMaxResults(maxResults);
            }
            if (params != null && params.length != 0) {
                handleQueryProperties(params, q);
            }
            resultado = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, e);
        }
        return resultado;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryLimit(String hql, Map params, Integer maxResults) {
        return HQL_findByQueryLimit(hql, params, maxResults, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryLimit(
            String hql,
            Map params,
            Integer maxResults,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        List resultado = Utilities.EMPTY_LIST;
        try {
            Query q = getSession().createQuery(hql);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (maxResults != null && maxResults > 0) {
                q.setMaxResults(maxResults);
            }
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            if (params != null && !params.isEmpty()) {
                handleQueryProperties(params, q);
            }
            resultado = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return resultado;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryPaged(String hql, IPagedQuery page) {
        return HQL_findByQueryPaged(hql, null, page, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryPaged(
            String hql,
            Map<String, Object> params,
            IPagedQuery page,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        List result = Utilities.EMPTY_LIST;
        try {
            final Query query = getSession().createQuery(hql);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                query.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(query, cacheable, cacheRegion);
            final int pageSize = IPagedQuery.getAllowedPageSize(page);
            if (pageSize > 0) {
                query.setFirstResult(page.getPage() * pageSize);
                query.setMaxResults(pageSize);
            }
            if (params == null) {
                result = query.list();
            } else {
                result = query.setProperties(params).list();
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, e);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQueryPaged(String hql, String paramName, Object value, IPagedQuery page) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put(paramName, value);
        return HQL_findByQueryPaged(hql, params, page, false, null, 0);
    }


    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateQuery(String hql, Object... params) {
        int r = 0;
        try {
            if (BnextStatementInspector.isInvalidEqualNullWhere(hql)) {
                /**
                 * Revisar req #50693 y actividad ACT-10293-IMP-001
                 *
                 * Si llegaste aquí probablemente se esten modificando valores
                 * incorrectos en el UPDATE, validar la concatenación del HQL y
                 * que no lleguen nulos por error.
                 * */
                getLogger().error("LIKELY A BUG! Suspicious NULL VALUE, use 'IS NULL' instead, or try another value, [" + hql + "]");
                if (LicenseUtil.isDevelopment()) {
                    throw new QueryException("Invalid HQL syntax! `IS NULL` must be explict, ` = null` is invalid.", hql);
                }
            }
            Query q = getSession().createQuery(hql);
            final boolean cacheable = false;
            DaoCacheUtils.configureCache(q, cacheable, null);
            handleQueryProperties(params, q);
            r = q.executeUpdate();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_selectQuery(String HQL, Object... params) {
        return HQL_findByQuery(HQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> HQL_selectMapQuery(
            String HQL,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            Object... params
    ) {
        List resultado = Utilities.EMPTY_LIST;
        try {
            final Query q = getSession().createQuery(HQL);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (params != null && params.length != 0) {
                handleQueryProperties(params, q);
            }
            resultado = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, e);
        } finally {
            detachEntity();
        }
        return resultado;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQL_uniqueLong(String HQL, Object... params) {
        long _long = 0L;
        Object container;
        try {
            container = this.HQL_uniqueObject(HQL, params);
            if (container != null) {
                _long = Long.parseLong(String.valueOf(container));
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, params, e);
        }
        return _long;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object HQL_uniqueObject(String HQL, Object[] params) {
        Object r = null;
        Query q;
        try {
            getLogger().trace("{} {} ", HQL, params);
            q = getSession().createQuery(HQL);
            final boolean cacheable = false;
            DaoCacheUtils.configureCache(q, cacheable, null);
            try {
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                r = q.uniqueResult();
            } catch (NonUniqueResultException e) {
                List<Object> l;
                if (params != null) {
                    handleQueryProperties(params, q);
                }
                l = q.list();
                if (l != null && !l.isEmpty()) {
                    r = l.get(0);
                    getLogger().warn("Unique object WARNING the executed HQL is returning {} results instead of one: \r\n {}{}", l.size(), HQL, e.getLocalizedMessage());
                }
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, params, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo con un solo filtro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * "code",
     * "0001"
     * );
     *
     * @param HQL       el query HQL, puede utilizarse para cualquier tipo de dato
     * @param paramName el nombre parametro
     * @param value     el valor del parametro a sustituir
     * @return renglones
     * <AUTHOR>
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_findByQuery(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_findByQuery(HQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean SQL_execute(String sql) throws QMSException {
        try {
            return getSession().doReturningWork(work -> {
                final Connection conn = work.unwrap(Connection.class);
                try (PreparedStatement statement = conn.prepareStatement(sql)) {
                    return statement.execute();
                }
            });
        } catch (Exception e) {
            errorHandler.stackTraceHandle("Failed to execute sql: \r\n" + sql, e);
            return false;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean SQL_execute(String sql, Integer timeoutSecounds) throws QMSException {
        try {
            return getSession().doReturningWork(work -> {
                final Connection conn = work.unwrap(Connection.class);
                try (PreparedStatement statement = conn.prepareStatement(sql)) {
                    if (timeoutSecounds > 0) {
                        statement.setQueryTimeout(timeoutSecounds);
                    }
                    return statement.execute();
                }
            });
        } catch (Exception e) {
            errorHandler.stackTraceHandle("Failed to execute sql: \r\n" + sql, e);
            return false;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean SQL_execute(String sql, Connection conn) throws QMSException {
        if (conn == null) {
            getLogger().error("Can not execute SQL {}, connection is null", sql);
            return false;
        }
        try {
            try (PreparedStatement statement = conn.prepareStatement(sql)) {
                return statement.execute();
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle("Failed to execute sql: \r\n" + sql, e);
            return false;
        }
    }

    /**
     * Utilizado para obtener solo un String en especifico por un query SQL
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.SQL_findSimpleString(
     * "SELECT c.code FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> Llimas
     * @since : *********
     */
    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_findSimpleString(String SQL) {
        return SQL_findSimpleString(SQL, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_findSimpleString(String SQL, Map params) {
        return SQL_findSimpleString(SQL, params, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_findSimpleString(String SQL, Integer queryTimeoutSeconds) {
        final Map<String, Object> params = new HashMap<>(0);
        return SQL_findSimpleString(SQL, params, getSession(), queryTimeoutSeconds);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_findSimpleString(String SQL, Map params, Integer queryTimeoutSeconds) {
        return SQL_findSimpleString(SQL, params, getSession(), queryTimeoutSeconds);
    }

    /**
     * Utilizado para obtener solo un Long en especifico por un query SQL
     * <p>
     * Ejemplo :
     * dao.SQL_findSimpleLong(
     * "SELECT c.id FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> jnicolas
     * @since : 2.8.0.1
     */
    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL) {
        return SQL_findSimpleLong(SQL, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL, String key, Object value) {
        return SQL_findSimpleLong(SQL, key, value, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL, Map<String, Object> params) {
        return SQL_findSimpleLong(SQL, params, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL, Integer queryTimeoutSeconds) {
        String result = SQL_findSimpleString(SQL, queryTimeoutSeconds);
        if (!result.isEmpty()) {
            return Long.valueOf(result);
        }
        return 0L;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL, String key, Object value, Integer queryTimeoutSeconds) {
        final Map<String, Object> params = new HashMap<>(0);
        params.put(key, value);
        return SQL_findSimpleLong(SQL, params, queryTimeoutSeconds);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(String SQL, Map<String, Object> params, Integer queryTimeoutSeconds) {
        final String result = SQL_findSimpleString(SQL, params, queryTimeoutSeconds);
        if (!result.isEmpty()) {
            return Long.valueOf(result);
        }
        return 0L;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL) {
        return SQL_findSimpleInteger(SQL, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL, Integer queryTimeoutSeconds) {
        String result = SQL_findSimpleString(SQL, queryTimeoutSeconds);
        if (!result.isEmpty()) {
            return Integer.valueOf(result);
        }
        return 0;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL, String key, Object value) {
        return SQL_findSimpleInteger(SQL, key, value, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL, String key, Object value, Integer queryTimeoutSeconds) {
        final Map<String, Object> params = new HashMap<>(0);
        params.put(key, value);
        return SQL_findSimpleInteger(SQL, params, queryTimeoutSeconds);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params) {
        return SQL_findSimpleInteger(SQL, params, -1);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params, Integer queryTimeoutSeconds) {
        String result = SQL_findSimpleString(SQL, params, queryTimeoutSeconds);
        if (!result.isEmpty()) {
            return Integer.valueOf(result);
        }
        return 0;
    }

    @Nonnull
    private String SQL_findSimpleString(String SQL, Map params, Session session, Integer queryTimeoutSeconds) {
        String r = "";
        try {
            Query q = session.createNativeQuery(SQL);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            if (params != null) {
                q.setProperties(params);
            }
            Object result = q.uniqueResult();
            if (result != null) {
                r = result.toString();
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
            return r;
        }
        return r;
    }

    /**
     * Utilizado para listado de renglones por un query SQL
     * <p>
     * Ejemplo :
     * SQL_findByQuery(
     * "SELECT c.code FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> Llimas
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List SQL_findByQuery(String SQL) {
        return SQL_findByQuery(SQL, null);
    }

    /**
     * Utilizado para listado de renglones por un query SQL
     * <p>
     * Ejemplo :
     * Map m = new HashMap<>();
     * m.put("code", "SomeCode");
     * SQL_findByQuery(
     * "SELECT c.code FROM tblnodo c WHERE c.code = :code", m
     * );
     *
     * @param m   : Parametros dentro del Query
     * @param SQL : query SQL
     * <AUTHOR> Llimas
     * @since : 2.10.0.9
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Object[]> SQL_findByQuery(String SQL, Map m) {
        List r = Utilities.EMPTY_LIST;
        try {
            /**
             * "T" y "ID" valdrá [null] siempre que venga de un Framework.DAO.GenericHibernateDAO
             */
            NativeQuery q = getSession().createNativeQuery(SQL);
            if (m != null) {
                q.setProperties(m);
            }
            r = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, m, e);
        }
        return r;
    }

    /**
     * Utilizado para listado de renglones por un query SQL
     * Ejemplo :
     * SQL_findByQuery(
     * "SELECT c.code FROM tblnodo c WHERE c.code = :code", "code", "SomeCode"
     * );
     *
     * @param key   : Llave del parametro
     * @param value : Valor del parametro
     * @param SQL   : query SQL
     * <AUTHOR> Llimas
     * @since : 2.10.0.9
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Object[]> SQL_findByQuery(String SQL, String key, Object value) {
        Map m = new HashMap<>();
        m.put(key, value);
        return SQL_findByQuery(SQL, m);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Object[]> SQL_findQuery(String SQL) {
        List<Object[]> r = Utilities.EMPTY_LIST;
        try {
            Query q = getSession().createNativeQuery(SQL);
            r = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
        }
        return r;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object[] SQL_findSimpleQuery(String SQL, Map params) {
        Object[] r = Utilities.EMPTY_ARRAY;
        try {
            Query q = getSession().createNativeQuery(SQL);
            if (params != null) {
                q.setProperties(params);
            }
            List l = q.list();
            if (!l.isEmpty()) {
                if (l.get(0).getClass().isArray()) {
                    r = (Object[]) l.get(0);
                } else {
                    r = new Object[]{l.get(0)};
                }
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
        }
        return r;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <X> X SQL_findUniqueResult(String SQL, Class<X> cls) {
        X result = null;
        try {
            Query q = getSession().createNativeQuery(SQL);
            try {
                result = (X) q.uniqueResult();
            } catch (NonUniqueResultException e) {
                List<Object> l = q.list();
                if (l != null && !l.isEmpty()) {
                    result = (X) l.get(0);
                }
            }

            //Parche para castear de BigInteger a Long. 
            if (result != null && !cls.isInstance(result)) {
                if (result instanceof java.math.BigInteger) {
                    if (cls.isAssignableFrom(Long.class)) {
                        return cls.cast(((java.math.BigInteger) result).longValue());
                    }
                }
            }

        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
            return null;
        }
        return result;
    }


    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like 'A%'"
     * );
     *
     * @param hql el query HQL
     * @return renglones
     * <AUTHOR>
     * @since *********
     * @deprecated Utilizar metodos que reciben `params`
     */
    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(String hql) {
        return this.HQL_updateByQuery(hql, new Object[]{});
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(
            Class<? extends Persistable> entity,
            Map<String, Object> params,
            Long loggedUserId,
            Long recordId,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            String observation
    ) {
        if (params.isEmpty()) {
            return 0;
        }
        if (params.containsKey("id")) {
            getLogger().error("The parameter 'id' is not allowed in the update query, it is reserved for the recordId, updating class {}", entity.getCanonicalName());
        }
        final StringBuilder logSb = new StringBuilder();
        final StringBuilder hqlSet = new StringBuilder();
        hqlSet
                .append(" UPDATE ").append(entity.getCanonicalName()).append(" entity ")
                .append(" SET ");
        try {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() == null) {
                    hqlSet.append(" entity.").append(entry.getKey()).append(" = null,");
                    logSb.append(" entity.").append(entry.getKey()).append(" = null,");
                } else {
                    hqlSet.append(" entity.").append(entry.getKey()).append(" = :").append(entry.getKey()).append(",");
                    logSb.append(" entity.").append(entry.getKey()).append(" = :").append(entry.getKey()).append(",");
                }
            }
            hqlSet.setLength(hqlSet.length() - 1);
            hqlSet
                    .append(" WHERE entity.id = :recordId");
            params.put("recordId", recordId);
            return HQL_updateByQuery(hqlSet.toString(), params, cacheable, cacheRegion, queryTimeoutSeconds);
        } finally {
            generateLogs(entity.getSimpleName(), loggedUserId, logSb.toString(), "update", " WHERE entity.id = " + recordId, recordId, observation);
        }
    }


    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(
            Class<?> entity,
            Map<String, Object> props,
            ILoggedUser loggedUser,
            Long recordId,
            String customPropId,
            String alias,
            String extraWhere,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Integer response;
        StringBuilder hqlSet = new StringBuilder("SET ");
        String _aliasProp = " " + alias + ".";
        String hqlWhere = " WHERE " + _aliasProp + customPropId + " = :recordId";
        boolean timezoneColumnsAvailable = entity.isAnnotationPresent(TimezoneActivityColumns.class);
        Map<String, Object> paramsTimezoneColumns = new HashMap();
        try {
            for (Map.Entry<String, Object> entry : props.entrySet()) {
                if (entry.getValue() == null) {
                    hqlSet.append(_aliasProp).append(entry.getKey()).append(" = null,");
                } else {
                    hqlSet.append(_aliasProp).append(entry.getKey()).append(" = :").append(entry.getKey()).append(",");
                }
                if (timezoneColumnsAvailable) {
                    handleEntityColumns(entity, hqlSet, _aliasProp, loggedUser, paramsTimezoneColumns, entry);
                }
            }
            props.putAll(paramsTimezoneColumns);
            hqlSet = new StringBuilder(hqlSet.substring(0, hqlSet.length() - 1));
            String hql = "UPDATE " + entity.getCanonicalName() + " " + alias + " " + hqlSet + hqlWhere + " " + extraWhere;
            props.put("recordId", recordId);
            response = HQL_updateByQuery(hql, props, cacheable, cacheRegion, queryTimeoutSeconds);
        } finally {
            generateLogs(
                    entity.getSimpleName(),
                    loggedUser.getId(),
                    hqlSet.toString(),
                    "update",
                    hqlWhere.replaceAll(":recordId", "" + recordId),
                    recordId,
                    null
            );
        }
        return response;

    }

    private void handleEntityColumns(
            Class<?> entity,
            StringBuilder hqlSet,
            String _aliasProp,
            ILoggedUser loggedUser,
            Map<String, Object> paramsTimezoneColumns,
            Map.Entry<String, Object> entry
    ) {
        List<String> columnTimezoneName = null;
        if (entity.isAnnotationPresent(TimezoneActivityColumns.class)) {
            columnTimezoneName = ActivityTimezoneFields.getAvailableFields().stream()
                    .map((field) -> ActivityTimezoneFields.getTimezoneNameByField(entry.getKey()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        }
        if (columnTimezoneName != null && !columnTimezoneName.isEmpty()) {
            String columnName = columnTimezoneName.get(0);
            hqlSet.append(_aliasProp).append(columnName).append(" =:").append(columnName).append(",");
            paramsTimezoneColumns.put(columnName, loggedUser.getTimezone());
        }
    }

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * con varios parametros
     * <p>
     * Ejemplo :
     * HashMap<String, Object> map = new HashMap<String, Object>();
     * map.put("code","001");
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like :code "
     * ,map
     * );
     *
     * @param hql : El query HQL
     * @param m   : parametros
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(String hql, Map m) {
        return HQL_updateByQuery(hql, m, false, null, 0);
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        int r = 0;
        try {
            getEntityManager().flush();
            if (BnextStatementInspector.isInvalidEqualNullWhere(hql)) {
                /**
                 * Revisar req #50693 y actividad ACT-10293-IMP-001
                 *
                 * Si llegaste aquí probablemente se esten modificando valores 
                 * incorrectos en el UPDATE, validar la concatenación del HQL y 
                 * que no lleguen nulos por error.
                 * */
                getLogger().error("LIKELY A BUG! Suspicious NULL VALUE, use 'IS NULL' instead, or try another value, [{}]", hql);
                if (LicenseUtil.isDevelopment()) {
                    throw new QueryException("Invalid HQL syntax! `IS NULL` must be explict, ` = null` is invalid.", hql);
                }
            }
            Query q = getSession().createQuery(hql);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (params != null) {
                q.setProperties(params);
            }
            r = q.executeUpdate();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, params, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_updateByQuery(
            final String SQL,
            final Map params,
            final List<String> synchronizeTables
    ) {
        return SQL_updateByQuery(SQL, params, 0, synchronizeTables);
    }

    /**
     * Actualizacion de datos por query nativo, UPDATE, DELETE, INSERT
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .SQL_updateByQuery(
     * "UPDATE users u SET password = 'x' WHERE code like 'A%'"
     * );
     * <p>
     * {@code @synchronizeTables} Tablas a sincronizan en caché de hibernate, si no incluye el parameter o está vacío se limpia todo el caché
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_updateByQuery(
            final String SQL,
            final Map params,
            final Integer queryTimeoutSeconds,
            final List<String> synchronizeTables
    ) {
        int r = 0;
        try {
            HIBERNATE_SQL_LOGGER.debug(SQL);
            NativeQuery q = getSession().createNativeQuery(SQL);
            if (params != null) {
                q.setProperties(params);
            }
            if (synchronizeTables != null && !synchronizeTables.isEmpty()) {
                synchronizeTables.forEach(q::addSynchronizedQuerySpace);
            } else if (LicenseUtil.isDevelopment()) {
                throw new QueryException(
                        "Invalid SQL_updateByQuery PARAMETERS! `synchronizeTables` must be used,"
                                + " otherwise the hibernate cache is cleared.",
                        SQL
                );
            }
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            r = q.executeUpdate();
        } catch (Exception e) {
            String error = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(error + " --> [" + SQL + "]", params, e);
        }
        return r;
    }

    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(String hql, Object[] params) {
        int r = 0;
        try {
            if (BnextStatementInspector.isInvalidEqualNullWhere(hql)) {
                /**
                 * Revisar req #50693 y actividad ACT-10293-IMP-001
                 *
                 * Si llegaste aquí probablemente se esten modificando valores 
                 * incorrectos en el UPDATE, validar la concatenación del HQL y 
                 * que no lleguen nulos por error.
                 * */
                getLogger().error("LIKELY A BUG! Suspicious NULL VALUE, use 'IS NULL' instead, or try another value, [" + hql + "]");
                if (LicenseUtil.isDevelopment()) {
                    throw new QueryException("Invalid HQL syntax! `IS NULL` must be explict, ` = null` is invalid.", hql);
                }
            }
            Query q = getSession().createQuery(hql);
            final boolean cacheable = false;
            DaoCacheUtils.configureCache(q, cacheable, null);
            handleQueryProperties(params, q);
            r = q.executeUpdate();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hql, e);
        } finally {
            detachEntity();
        }
        return r;
    }

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * con un solo parametro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like :code "
     * ,"code","0001"
     * );
     *
     * @param hql       el query HQL
     * @param paramName parametro
     * @param value     valor del parametro
     * @return renglones
     * <AUTHOR>
     * @since *********
     */
    @Nonnull
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer HQL_updateByQuery(String hql, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_updateByQuery(hql, params);
    }

    private void handleQueryProperties(Map params, Query q) {
        for (Object p : params.entrySet()) {
            String string = ((Map.Entry) p).getKey().toString();
            Object object = ((Map.Entry) p).getValue();
            handleObjectProperty(q, string, object);
        }
    }

    private void handleObjectProperty(Query query, String propertyName, Object propertyValue) {
        if (propertyValue instanceof IStatusEnum) {
            query.setParameter(propertyName, ((IStatusEnum) propertyValue).getValue(), Integer.class);
        } else if (propertyValue instanceof Persistable) {
            query.setParameter(propertyName, ((Persistable) propertyValue).getId(), Long.class);
        } else if (propertyValue instanceof Date) {
            query.setParameter(propertyName, (Date) propertyValue, Date.class);
        } else if (propertyValue instanceof BigDecimal) {
            query.setParameter(propertyName, (BigDecimal) propertyValue, BigDecimal.class);
        } else if (propertyValue instanceof Double) {
            query.setParameter(propertyName, (Double) propertyValue, Double.class);
        } else if (propertyValue instanceof List) {
            query.setParameterList(propertyName, (List) propertyValue);
        } else if (propertyValue instanceof Object[]) {
            query.setParameterList(propertyName, (Object[]) propertyValue);
        } else {
            query.setParameter(propertyName, propertyValue);
        }
    }

    private final Pattern wherePattern = Pattern.compile("(^.+?\\swhere\\s.*)(\\?.*)", Pattern.CASE_INSENSITIVE);
    private final Pattern updatePattern = Pattern.compile("^.*update\\s.+?\\sset\\s.*\\?.*", Pattern.CASE_INSENSITIVE);
    private final Pattern whereParamXPattern = Pattern.compile("^.+?\\swhere\\s.*param[0-9].*", Pattern.CASE_INSENSITIVE);
    private final Pattern updateParamXPattern = Pattern.compile("^.*update\\s.+?\\sset\\s.*param[0-9].*", Pattern.CASE_INSENSITIVE);

    private void handleQueryProperties(Object[] params, Query q) {
        if (params == null || q == null) {
            return;
        }
        String queryStr = q.getQueryString();
        Object object;
        if (
                wherePattern.matcher(queryStr).matches()
                        || updatePattern.matcher(queryStr).matches()
        ) {
            //modelo de parametros por posicion ? (no soporta listas)
            for (int i = 0; i < params.length; i++) {
                object = params[i];
                if (object instanceof IStatusEnum) {
                    q.setParameter(i + 1, ((IStatusEnum) object).getValue(), Integer.class);
                } else if (object instanceof Persistable) {
                    q.setParameter(i + 1, ((Persistable) object).getId(), Long.class);
                } else if (object instanceof Date) {
                    q.setParameter(i + 1, (Date) object, Date.class);
                } else if (object instanceof BigDecimal) {
                    q.setParameter(i + 1, (BigDecimal) object, BigDecimal.class);
                } else if (object instanceof Double) {
                    q.setParameter(i + 1, (Double) object, Double.class);
                } else {
                    q.setParameter(i + 1, object);
                }
            }
        } else if (
                whereParamXPattern.matcher(queryStr).matches()
                        || updateParamXPattern.matcher(queryStr).matches()
        ) {
            //modelo de parametros por posicion :Param1, :Param2, :Param3, etc.
            for (int i = 0; i < params.length; i++) {
                object = params[i];
                if (object instanceof IStatusEnum) {
                    q.setParameter("Param" + i, ((IStatusEnum) object).getValue(), Integer.class);
                } else if (object instanceof Persistable) {
                    q.setParameter("Param" + i, ((Persistable) object).getId(), Long.class);
                } else if (object instanceof Date) {
                    q.setParameter("Param" + i, (Date) object, Date.class);
                } else if (object instanceof BigDecimal) {
                    q.setParameter("Param" + i, (BigDecimal) object, BigDecimal.class);
                } else if (object instanceof Double) {
                    q.setParameter("Param" + i, (Double) object, Double.class);
                } else if (object instanceof Object[]) {
                    q.setParameterList("Param" + i, ((Object[]) object));
                } else {
                    q.setParameter("Param" + i, object);
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList() {
        return getStrutsComboList("description", "0=0", -1L, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(Boolean withStatus) {
        return getStrutsComboList("description", "0=0", -1L, withStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(Long id) {
        return getStrutsComboList("description", "0=0", id, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(String filtros) {
        return getStrutsComboList("description", filtros, -1L, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(String filtros, Boolean withStatus) {
        return getStrutsComboList("description", filtros, -1L, withStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(String description, String filtros) {
        return getStrutsComboList(description, filtros, -1L, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(String filtros, Long id) {
        return getStrutsComboList("description", filtros, id, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(String mappedDescription, String filtros, Long id, Boolean withStatus) {
        return getStrutsComboList(null, mappedDescription, filtros, id, withStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription, String filtros) {
        return getStrutsComboList(clazz, mappedDescription, filtros, -1L, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        List<Long> ids = Collections.singletonList(-1L);
        return getStrutsComboList(clazz, mappedDescription, filtros, ids, true, cacheable, cacheRegion, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        List<Long> ids = Collections.singletonList(-1L);
        return getStrutsComboList(clazz, mappedDescription, filtros, ids, withStatus, cacheable, cacheRegion, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription) {
        return getStrutsComboList(clazz, mappedDescription, "1=1", -1L, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getStrutsComboList(clazz, "description", "1=1", Collections.singletonList(-1L), true, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * Metodo que obtiene una lista de tipo TextHasValue para utilizar en un combo
     *
     * @param clazz             clase de la cual se obtendrán los datos
     * @param mappedDescription valor que aparecerá en el combo
     * @param filtros           filtros para los datos
     * @param id                valor especifico para mostrar
     * @return List<ITextHasValue> con los datos para ser utilizados en un combo
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription, String filtros, Long id, Boolean withStatus) {
        List<Long> ids = new ArrayList<>(1);
        if (id != null) {
            ids.add(id);
        }
        return getStrutsComboList(clazz, mappedDescription, filtros, ids, withStatus, false, null, 0);
    }

    private List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            List<Long> ids,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        final Class actualClazz;
        if (clazz == null) {
            actualClazz = this.getPersistentClass();
        } else {
            actualClazz = clazz;
        }
        try {
            final Method method = actualClazz.getMethod("getId");
            final Class<? extends ITextHasValue> typeList;
            if (Long.class.equals(method.getReturnType())) {
                typeList = TextLongValue.class;
            } else {
                typeList = TextHasValue.class;
            }
            return (List<ITextHasValue>) getStrutsComboList(
                    typeList,
                    actualClazz,
                    mappedDescription,
                    filtros,
                    ids,
                    withStatus,
                    cacheable,
                    cacheRegion,
                    queryTimeoutSeconds
            );
        } catch (Exception ex) {
            errorHandler.stackTraceHandle(mappedDescription, ex);
            return Utilities.EMPTY_LIST;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TEXT extends ITextHasValue> List<TEXT> getStrutsComboList(
            final Class<TEXT> typeList,
            final Class typedClazz,
            final String mappedColumns,
            final String filtros,
            final List<Long> ids,
            final Boolean withStatus,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        final StringBuilder query = new StringBuilder();
        try {
            final Class clazz;
            if (typedClazz == null) {
                clazz = this.getPersistentClass();
            } else {
                clazz = typedClazz;
            }
            final String builder = typeList.getCanonicalName();
            final String clase = clazz.getName();
            final String deletedField = getDeletedField(clazz);
            query.append(""
                            + " SELECT new ").append(builder).append("(")
                    .append(mappedColumns).append(",id"
                            + " )"
                            + " FROM ").append(clase).append(" c ");
            final StringBuilder condition = new StringBuilder();
            if (!filtros.trim().isEmpty() && !deletedField.isEmpty()) {
                condition.append(" ( ").append(filtros).append(" ) AND c.").append(deletedField);
            } else if (filtros.trim().isEmpty() && !deletedField.isEmpty()) {
                condition.append(" c.").append(deletedField);
            } else if (!filtros.trim().isEmpty() && deletedField.isEmpty()) {
                condition.append(" ( ").append(filtros).append(" ) ");
            }
            if (withStatus) {
                final String statusField = getStatusField(clazz);
                if (!statusField.isEmpty()) {
                    if (condition.length() == 0) {
                        condition.append(statusField);
                    } else {
                        condition.append(" AND ").append(statusField);
                    }
                }
            }
            if (ids == null || ids.isEmpty()) {
                if (condition.length() > 0) {
                    query.append(" WHERE ( ").append(condition.toString()).append(" ) ");
                }
            } else {
                final String idsBuilder;
                idsBuilder = ids.stream().map(Object::toString).filter(f -> !f.equals("-1")).collect(Collectors.joining(","));
                if (!idsBuilder.isEmpty()) {
                    if (condition.length() == 0) {
                        query.append(" WHERE id IN (").append(idsBuilder).append(" ) ");
                    } else {
                        query.append(" WHERE ( ").append(condition).append(" )  OR id IN (").append(idsBuilder).append(" ) ");
                    }
                } else if (condition.length() > 0) {
                    query.append(" WHERE ( ").append(condition).append(" ) ");
                }
            }
            query.append(" ORDER BY ").append(mappedColumns).append(", id");
            final Query q = getSession().createQuery(query.toString());
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            final List<TEXT> result = q.list();
            return result;
        } catch (Exception e) {
            errorHandler.stackTraceHandle(query.toString(), e);
        }
        final List<TEXT> failedResult = Utilities.EMPTY_LIST;
        return failedResult;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextCodeValue> getStrutsTextCodeComboList(
            final Class clazz,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        return getStrutsTextCodeComboList(clazz, "0=0", Utilities.EMPTY_LIST, true, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextCodeValue> getStrutsTextCodeComboList(
            final Class clazz,
            final String filtros,
            final List<Long> ids,
            final Boolean withStatus,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        return getStrutsComboList(
                TextCodeValue.class,
                clazz,
                "c.code, c.description",
                filtros,
                ids,
                withStatus,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    private String getDeletedField(Class cls) {
        String result = "";
        try {

            String notDeleted = "0";
            Field[] fieldlist = cls.getDeclaredFields();
            int contador = 0;
            boolean encontrado = false;
            Field fld = null;
            while (contador < fieldlist.length) {
                fld = fieldlist[contador];
                if (fld.getName().equals("deleted")) {
                    encontrado = true;
                    break;
                }
                contador++;
            }
            if (encontrado) {
                result = fld.getName() + "=" + notDeleted;
            }

        } catch (Exception e) {
            errorHandler.stackTraceHandle(result, e);
        }
        return result;/**/

    }

    /**
     * Cantidad de datos del Entity especificado en la declaracion del DAO,
     * utilizado en gridComponent.js
     *
     * @param filter utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return cantidad de renglondes
     * <AUTHOR>
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQLT_countByPagedFilter(IGridFilter filter) {
        return this.HQLT_countByPagedFilter(filter, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long HQLT_countByPagedFilter(IGridFilter filter, boolean like) {
        return HQL_countByPagedFilter(persistentClass, filter, like);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter) {
        return HQL_countByPagedFilter(TYPE, filter, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter, boolean like) {
        return HQL_countByPagedFilter(TYPE, filter, like, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> Long HQL_countByPagedFilter(
            Class<TYPE> TYPE,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Long resultado = null;
        String query = null;
        try {
            // filter is saved into HttpSession
            sessionHandler.saveWindowFilter(filter);
            query = HQLHandler.makeCountHQL(TYPE, filter, like);
            Query q = getSession().createQuery(query);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            parserHandler.bindParamsCriteria(q, filter, like);
            String r = "";
            getLogger(LOGGER.GridComponent_PagedFilterHQL).trace("\r\n{}\r\n/{}\r\n", query, filter.getCriteria());
            try {
                r = q.uniqueResult() + "";
            } catch (Exception ex) {
                getLogger().error("Error al correr el query", ex);
            }
            if (!"null".equals(r) && !r.isEmpty()) {
                resultado = Long.valueOf(r);
            } else if ("null".equals(r)) {
                getLogger().error("HQL_countByPagedFilter, failure at query to count entity '{}'", TYPE);
                resultado = 0L;
            } else {
                resultado = 0L;
            }
        } catch (HibernateException | NumberFormatException | QMSException e) {
            errorHandler.stackTraceHandle(query, e);
            resultado = 0L;
        } catch (Exception ex) {
            errorHandler.stackTraceHandle(query, ex);
        } finally {
            detachEntity();
        }
        getLogger().trace("resultado: {}", resultado);
        return resultado;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(String query, IGridFilter filter) {
        return HQL_getRowsByQuery(query, filter, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(
            String HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(HQL, filter, false, false, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(StringBuilder query, IGridFilter filter) {
        return HQL_getRowsByQuery(query.toString(), filter, false);
    }

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(String HQL, IGridFilter filter, String alias) {
        return HQL_getRows(HQL, filter, false, false, alias, false, null, 0);
    }

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(StringBuilder HQL, IGridFilter filter, String alias) {
        return HQL_getRows(HQL.toString(), filter, false, false, alias, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(
            StringBuilder query,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(query.toString(), filter, false, true, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(String query, IGridFilter filter) {
        return HQL_getRows(query, filter, false, true, false, null, 0);
    }

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(
            String query,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(query, filter, false, true, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(
            StringBuilder query,
            IGridFilter filter,
            String entityAlias,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(query.toString(), filter, false, true, entityAlias, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(String query, IGridFilter filter, String entityAlias) {
        return HQL_getRows(query, filter, false, true, entityAlias, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getTreeRows(
            StringBuilder query,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getTreeRows(
                query.toString(),
                filter,
                null,
                "c",
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                peekProcessParentRowConsumer,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getTreeRows(
            StringBuilder query,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getTreeRows(
                query.toString(),
                filter,
                null,
                "c",
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                null,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getTreeRows(
            StringBuilder query,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getTreeRows(
                query.toString(),
                filter,
                null,
                entityAlias,
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                peekProcessParentRowConsumer,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getTreeRows(
            StringBuilder query,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getTreeRows(
                query.toString(),
                filter,
                null,
                entityAlias,
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                null,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, IPendingOperation... pendings) {
        return getActiveRecordRows(user, filter, entityClass, "entity", pendings);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, IPendingOperation... pendings) {
        return getActiveRecordRows(user, filter, entityClass, "entity", pendings);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, IPendingOperation... pendings) {
        return getActiveRecordRows(user, filter, getPersistentClass(), "c", pendings);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings) {
        return getRecordRows(user, filter, entityClass, "entity", type, pendings);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class, MakePersistentException.class, ExplicitRollback.class, QueryException.class})
    public GridInfo getRecordRowsWithNewTx(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings) {

        return getRecordRows(user, filter, entityClass, "entity", type, pendings);
    }

    private GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, String entityClassAlias, IPendingOperation... pendings) {
        return getRecordRows(user, filter, entityClass, entityClassAlias, RecordRowsType.CROSS_APE_ENTITY, pendings);
    }

    private GridInfo getRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, String entityClassAlias, RecordRowsType type, IPendingOperation... pendings) {
        String HQL = PendingHelper.getRecordRowsHQL(user, entityClass, type, pendings);
        return HQL_getRows(HQL, filter, false, true, entityClassAlias, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getLightRows(
            IGridFilter filter,
            Class entityClass,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        String HQL = PendingHelper.getRowsHQL(entityClass);
        return HQL_getRows(HQL, filter, false, true, "entity", cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> GridInfo<TYPE> getRowsByProjection(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            Class<TYPE> projectionClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        String constructor = "map";
        if (projectionClass != null) {
            constructor = projectionClass.getCanonicalName();
        }
        final AttenderHQLBuilder builder = new AttenderHQLBuilder(fields.size() * 90)
                .append(SqlQueryParser.SELECT).append("new ").append(constructor).append("(entity.id AS entity_id");
        fields.forEach(field -> {
            if (field.getTableAlias() == null) {
                builder.append("entity", field);
            } else {
                builder.append(field.getTableAlias(), field);
            }
        });
        builder.append(")").append(SqlQueryParser.FROM);
        String hql = builder.getBuildedQuery(
                PendingHelper.getRowsHQL(entityClass)
                        .replaceFirst(
                                "(" + SqlQueryParser.FROM + ".+? entity ).+? entity[0-9]+(\\s*,\\s*qms)",
                                "$1$2"
                        ) // <-- Se remueven JOINS genericos
        );
        if (join != null) {
            hql = hql.replaceFirst(SqlQueryParser.WHERE, join + SqlQueryParser.WHERE);
        }
        return HQL_getRows(hql, filter, false, true, entityClass, "entity", null, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getRowsByProjection(filter, entityClass, null, fields, join, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName, //ID_FIELD
            String rowParentIdFieldName, //RECURRENCE_ID_FIELD
            String rowChildListFieldName, //CHILDS_KEY
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getTreeLightRows(
                filter,
                entityClass,
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                null,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName, //RECURRENCE_ID_FIELD
            String rowChildListFieldName, //CHILDS_KEY
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        final String HQL = PendingHelper.getRowsHQL(entityClass);
        return HQL_getTreeRows(
                HQL,
                filter,
                entityClass,
                "entity",
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                peekProcessParentRowConsumer,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }


    private final static Pattern TREE_LIGHT_ROWS_ENTITY_CLEANER_REGEX = Pattern.compile("(from [.\\w\\s]+? entity)[.\\w\\s]+ entity[0-9]+");

    /**
     * @see IUntypedDAO#getTreeLightRows(IGridFilter, Class, String, String, String, Predicate, Function, Consumer, Boolean, CacheRegion, Integer)
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        final AttenderHQLBuilder builder = new AttenderHQLBuilder(200).append(SqlQueryParser.SELECT + "new map(entity.id AS entity_id");
        fields.forEach(field -> {
            if (field.getTableAlias() == null) {
                builder.append("entity", field);
            } else {
                builder.append(field.getTableAlias(), field);
            }
        });
        builder.append(")").append(SqlQueryParser.FROM);
        String from = TREE_LIGHT_ROWS_ENTITY_CLEANER_REGEX.matcher(PendingHelper.getRowsHQL(entityClass)).replaceFirst(" $1 ");
        String hql = builder.getBuildedQuery(from);
        if (join != null) {
            hql = hql.replaceFirst(SqlQueryParser.WHERE, " " + join + SqlQueryParser.WHERE);
        }
        return HQL_getTreeRows(
                hql,
                filter,
                entityClass,
                "entity",
                rowIdFieldName,
                rowParentIdFieldName,
                rowChildListFieldName,
                filterParentIdsPredicate,
                childCountParentRowFunction,
                peekProcessParentRowConsumer,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRows(String query, IGridFilter filter, boolean like) {
        return HQL_getRows(query, filter, like, true, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(StringBuilder query, IGridFilter filter, boolean like) {
        return HQL_getRows(query.toString(), filter, like, false, false, null, 0);
    }

    /**
     * @deprecated Utilizar el que recibe `StringBuilder`
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo HQL_getRowsByQuery(String query, IGridFilter filter, boolean like) {
        return HQL_getRows(query, filter, like, false, false, null, 0);
    }

    private GridInfo HQL_getRows(
            String HQL,
            IGridFilter filter,
            boolean like,
            boolean injectWhere,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(HQL, filter, like, injectWhere, "c", cacheable, cacheRegion, queryTimeoutSeconds);
    }

    private void setTreeRowsHierarchy(
            Set<Long> parentIds,
            List<Map<String, Object>> result,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer
    ) {
        if (result == null || result.isEmpty()) {
            return;
        }
        final String PARENT_ID_KEY = "invalidRowChildParentId";                 // <--- Transient, no viene de la BD
        final String INVALID_ROW_CHILD_KEY = "invalidRowChild";                 // <--- Transient, no viene de la BD, debe coincidir con el de "grid.interfaces.ts"
        final String EMPTY_CHILDS_KEY = "EMPTY_CHILDS";                         // <--- Transient, no viene de la BD, debe coincidir con el de "grid.enums.ts"
        final String SHOW_MORE_CHILDS_KEY = "SHOW_MORE_CHILDS";                 // <--- Transient, no viene de la BD, debe coincidir con el de "grid.enums.ts"
        parentIds.addAll(
                result.stream().filter(filterParentIdsPredicate).map(row -> Long.valueOf(row.get(rowIdFieldName).toString())).collect(Collectors.toList())
        );
        if (!parentIds.isEmpty()) {
            // Se respaldan "childs"
            final List<Map<String, Object>> childs =
                    result.stream()
                            .filter(row -> row.get(rowParentIdFieldName) != null && parentIds.contains(Long.valueOf(row.get(rowParentIdFieldName).toString())))
                            .collect(Collectors.toList());
            // Se inicializan valores de renglones padre
            result.stream().filter(row ->
                    row.get(rowIdFieldName) != null && parentIds.contains((Long.valueOf(row.get(rowIdFieldName).toString())))
            ).forEach(row -> {
                if (peekProcessParentRowConsumer != null) {
                    peekProcessParentRowConsumer.accept(row);
                }
                row.computeIfAbsent(rowChildListFieldName, k -> new ArrayList<>());
            });
            // Se eliminan "childs" del arreglo original que no son tambien padre
            result.removeIf(row -> row.get(rowParentIdFieldName) != null && parentIds.contains((Long.valueOf(row.get(rowParentIdFieldName).toString()))));
            // Se agregan "childs" a su padre
            result.stream().filter(row ->
                    row.get(rowIdFieldName) != null && parentIds.contains((Long.valueOf(row.get(rowIdFieldName).toString())))
            ).forEach(row -> {
                final List<Map<String, Object>> childsRef = (List<Map<String, Object>>) row.get(rowChildListFieldName);
                Integer childCount = childCountParentRowFunction.apply(row);
                // Se agregan hijos correspondientes
                childsRef.addAll(childs.stream()
                        .filter(child -> Objects.equals(Long.valueOf(row.get(rowIdFieldName).toString()), Long.valueOf(child.get(rowParentIdFieldName).toString())))
                        .collect(Collectors.toList()));
                // Se agrega un renglón adicional diciendo "+ Mostrar más" o "No hay registros" (según sea el caso)
                if (childsRef.isEmpty() && childCount == 0) {
                    childsRef.add(ImmutableMap.of(INVALID_ROW_CHILD_KEY, EMPTY_CHILDS_KEY)); // <-- Mensaje "No hay registros"
                } else if (childCount < result.size() && childsRef.size() < childCount) {
                    childsRef.add(ImmutableMap.of(
                            INVALID_ROW_CHILD_KEY, SHOW_MORE_CHILDS_KEY,                         // <-- Botón "Mostrar más"
                            PARENT_ID_KEY, row.get(rowIdFieldName)                               // <-- parentId
                    ));
                }
            });
            Set<Long> childParenIds = childs.stream()
                    .filter(child -> child.get(rowIdFieldName) != null
                            && parentIds.contains(Long.valueOf(child.get(rowIdFieldName).toString())) &&
                            ((List<Map<String, Object>>) child.get(rowChildListFieldName)).isEmpty())
                    .map(child -> Long.valueOf(child.get(rowIdFieldName).toString()))
                    .collect(Collectors.toSet());
            setTreeRowsHierarchy(
                    childParenIds,
                    childs,
                    rowIdFieldName,
                    rowParentIdFieldName,
                    rowChildListFieldName,
                    filterParentIdsPredicate,
                    childCountParentRowFunction,
                    peekProcessParentRowConsumer
            );
        }
    }

    private GridInfo<Map<String, Object>> HQL_getTreeRows(
            String HQL,
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        final GridInfo<Map<String, Object>> result = HQL_getRows(
                HQL,
                filter,
                false,
                true,
                entityClass,
                entityAlias,
                rowParentIdFieldName,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
        final Set<Long> parentIds = new HashSet<>();
        if (rowIdFieldName != null) {
            setTreeRowsHierarchy(
                    parentIds,
                    result.getData(),
                    rowIdFieldName,
                    rowParentIdFieldName,
                    rowChildListFieldName,
                    filterParentIdsPredicate,
                    childCountParentRowFunction,
                    peekProcessParentRowConsumer
            );
        }
        return result;
    }

    private GridInfo HQL_getRows(
            String HQL,
            IGridFilter filter,
            boolean like,
            boolean injectWhere,
            String entityAlias,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return HQL_getRows(HQL, filter, like, injectWhere, null, entityAlias, null, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    private <TYPE extends BaseAPE> GridInfo HQL_getRows(
            String HQL,
            IGridFilter filter,
            boolean like,
            boolean injectWhere,
            Class<TYPE> entityClass,
            String entityAlias,
            String parentField,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        final GridInfo result = new GridInfo();
        sessionHandler.saveWindowFilter(filter); // <--- filter is saved into HttpSession
        result.setGridId(filter.getGridId());
        filter.parseCriteriaMap(HQL);
        if (filter.isAsumeAlias()
                && filter.getField().getOrderBy() != null
                && !filter.getField().getOrderBy().trim().isEmpty()
                && !filter.getField().getOrderBy().contains(entityAlias + ".")) {
            filter.getField().setOrderBy(entityAlias + "."
                    + filter.getField().getOrderBy());
            filter.getField().setDirection(2);
        }
        QueryHandler queryHandler = new QueryHandler(entityAlias, HQL);
        String sqlExecuted = null;
        String hqlFinal = null;
        try {
            if (injectWhere) {
                HQL_getRowsInjectWhere(queryHandler, filter, like, entityAlias, parentField);
            } else {
                queryHandler.getHqlResultCount()
                        .append(QueryHandler.appendNotEmpty(parserHandler.parseFilterWhere(filter, like, entityAlias, parentField), " WHERE "));
                queryHandler.getHqlResult().append(" WHERE ").append(parserHandler.getQueryStringNonBinded(queryHandler, filter, parentField, like));
            }
            getLogger(LOGGER.GridComponent_PagedFilterHQL).trace("\r\n{}\r\n/{}\r\n", queryHandler.getHqlResultCount(), filter.getCriteria());
            getLogger(LOGGER.GridComponent_PagedFilterHQL).trace("\r\n{}\r\n/{}\r\n", queryHandler.getHqlResult(), filter.getCriteria());
            result.setCount(HQL_getRows_Count(queryHandler, filter, like, cacheable, cacheRegion, queryTimeoutSeconds));
            result.setLastSyncDate(Utilities.getNow());

            String injectedHql = queryHandler.getHqlResult().toString();
            if (!filter.isEnableStatistics() || filter.getStatisticsFields() == null
                    || filter.getStatisticsFields().isEmpty()) {
                List rows;
                if (entityClass != null) {
                    // Revisión de campos dinamicos
                    ValidationDTO v = DynamicFieldHandler.isDynamicSearchValid(entityClass);
                    if (v.isValid() && filter.isDynamicSearchEnabled()) {
                        getLogger(LOGGER.DynamicSearch).trace("Se detecta busqueda por campos dinamicos");
                        IDynamicFieldDAO dynamicFieldsDAO = getBean(IDynamicFieldDAO.class);
                        IDynamicGridConfigHQL hqlConfig = dynamicFieldsDAO.getDynamicGridConfig(entityClass, entityAlias, HQL);
                        // Se agregan las expresiones faltantes (Where y Order)
                        Map<String, String> m = parserHandler.getCriteriaBinded(filter, like, parentField, entityAlias);
                        String where = m.get(QueryHandler.CRITERIA_WHERE),
                                order = m.get(QueryHandler.CRITERIA_ORDER);
                        where = where.isEmpty() ? " where 0=0" : " AND " + where;
                        hqlFinal = HQL + where + order;
                        String SQL = hqlToSqlString(hqlFinal);
                        DynamicGridConfigSQL sqlConfig = new DynamicGridConfigSQL(SQL);
                        dynamicFieldsDAO.setDynamicGridConfigSQL(filter, entityClass.getName(), hqlConfig, sqlConfig);
                        result.setParseFromDynamicResults(true);
                        sqlExecuted = sqlConfig.getSqlFinal().toString();
                        Query query = getSession().createNativeQuery(sqlExecuted);
                        NativeQuery qs = (NativeQuery) query;
                        qs.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
                        // Se agrega restricción de paginación
                        final int pageSize = IPagedQuery.getAllowedPageSize(filter);
                        if (pageSize > 0) {
                            query.setFirstResult(filter.getPage() * pageSize);
                            query.setMaxResults(pageSize);
                        }
                        rows = query.list();
                    } else {
                        rows = inner_HQL_getRows(injectedHql, filter, like, cacheable, cacheRegion, queryTimeoutSeconds);
                    }
                } else {
                    rows = inner_HQL_getRows(injectedHql, filter, like, cacheable, cacheRegion, queryTimeoutSeconds);
                }
                if (rows == null) {
                    result.setData(Collections.EMPTY_LIST);
                } else if (result.getCount() == 0 && rows.isEmpty()) {
                    result.setData(Collections.EMPTY_LIST);
                } else if (result.getCount() > 0) {
                    result.setData(rows);
                } else {
                    result.setCount(Long.valueOf(rows.size()));
                    result.setData(rows);
                }
            } else {
                getLogger().trace("Framework.DAO.getRows - statistics mode {}... {}", getPersistentClass(), filter.getCriteria());
                try {
                    Map<String, List<StringLongPair>> statistics
                            = StatisticsService.getHQLStatistics(getSession(), injectedHql, filter, false, cacheable, queryTimeoutSeconds);
                    result.setStatisticsResults(statistics);
                } catch (QMSException ex) {
                    errorHandler.stackTraceHandle(injectedHql, ex);
                }
            }
        } catch (Exception e) {
            String error = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(error + " --> [" + hqlFinal + "][" + sqlExecuted + "]", e);
        } finally {
            detachEntity();
        }
        return result;
    }

    private List inner_HQL_getRows(
            String HQL,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        try {
            Query q = getSession().createQuery(HQL);
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            parserHandler.bindParamsCriteria(q, filter, like);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            final int pageSize = IPagedQuery.getAllowedPageSize(filter);
            if (pageSize > 0) {
                q.setFirstResult(filter.getPage() * pageSize);
                q.setMaxResults(pageSize);
            }
            return q.list();
        } catch (Exception e) {
            String error = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(error + " --> [" + HQL + "]", e);
            return null;
        } finally {
            detachEntity();
        }
    }

    private Long HQL_getRows_Count(
            QueryHandler queryHandler,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        Query q;
        long c = 5L;
        try {
            if (queryHandler.getSqlResultCount() == null || queryHandler.getSqlResultCount().length() == 0) {
                q = getSession().createQuery(queryHandler.getHqlResultCount().toString());
                DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
                parserHandler.bindParamsCriteria(q, filter, like);
                if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                    q.setTimeout(queryTimeoutSeconds);
                }
                final Object result = q.uniqueResult();
                if (result == null) {
                    return 0L;
                } else if (result instanceof Long) {
                    c = (Long) result;
                } else {
                    c = ((Number) result).longValue();
                }
            } else {
                c = SQL_findSimpleLong(queryHandler.getSqlResultCount().toString(), queryTimeoutSeconds);
            }
        } catch (Exception e) {
            String error = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(error + " --> [" + queryHandler.getHqlResultCount() + "]", e);
        } finally {
            detachEntity();
        }
        return c;
    }

    private final static Pattern ROWS_INJECT_WHERE_MATCHER = Pattern.compile("\\sWHERE\\s.+", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);

    private void HQL_getRowsInjectWhere(QueryHandler hqlHandler,
                                        IGridFilter filter, boolean like, String entityAlias, String parentField) {
        String where;
        try {
            if (ROWS_INJECT_WHERE_MATCHER.matcher(hqlHandler.getHqlResult().toString()).find()) {
                Map<String, String> nonBindedCriteria =
                        parserHandler.getCriteriaNonBinded(hqlHandler, filter, like, entityAlias, parentField);
                if (hqlHandler.isGroupByExpression()) {
                    String HQL =
                            hqlHandler.getInjectedHQL(parserHandler.getCriteriaBinded(filter, like, parentField));
                    String SQL =
                            hqlToSqlString(HQL);
                    if (SQL == null) {
                        throw new ExplicitRollback("SQL is null for: " + HQL);
                    }
                    hqlHandler.setSqlResultCount(HQLHandler.makeCountHQL(SQL));
                } else {
                    hqlHandler.setHqlResultCount(
                            hqlHandler.getInjectedHQLCount(parserHandler.parseFilterWhere(filter, like, entityAlias, parentField))
                    );
                }
                hqlHandler.setHqlResult(hqlHandler.getInjectedHQL(nonBindedCriteria));
                hqlHandler.getHqlResult().append(nonBindedCriteria.get(QueryHandler.CRITERIA_ORDER));
            } else {
                where = parserHandler.parseFilterWhere(filter, like, entityAlias, parentField);
                hqlHandler.getHqlResultCount().append(where.isEmpty()
                        ? ""
                        : " WHERE " + where);
                hqlHandler.getHqlResult().append(" WHERE ").append(parserHandler.getQueryStringNonBinded(hqlHandler, filter, like));
                getLogger().trace("InjectWhere method has been called but it wasn't necessary!, final HQL '{}'", hqlHandler.getHqlResult());
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(hqlHandler.getHqlResult().toString(), e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_getListResultsByQuery(String query, IGridFilter filter) {
        return HQL_getListResultsByQuery(query, filter, false, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQL_getListResultsByQuery(
            Class<TYPE> TYPE,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        sessionHandler.saveWindowFilter(filter); // <--- filter is saved into HttpSession
        final String query = " SELECT c FROM " + TYPE.getName() + " as c "
                + " WHERE " + parserHandler.getQueryStringNonBinded(null, filter, false);
        return HQL_getListResultsByQuery(query, filter, false, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List HQL_getListResultsByQuery(
            String query,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        List resultado = Utilities.EMPTY_LIST;
        StringBuilder builder = new StringBuilder();
        try {
            sessionHandler.saveWindowFilter(filter); // <--- filter is saved into HttpSession
            filter.parseCriteriaMap(query);
            if (filter.getField().getOrderBy() != null && !filter.getField().getOrderBy().trim().isEmpty() && !filter.getField().getOrderBy().contains("c.")) {
                filter.getField().setOrderBy("c." + filter.getField().getOrderBy());
            }
            if (query.toLowerCase().contains(" where ")) {
                String entityAlias = "entity";
                QueryHandler hqlHandler = new QueryHandler(entityAlias, query);
                Map<String, String> nonBindedCriteria =
                        parserHandler.getCriteriaNonBinded(hqlHandler, filter, like, entityAlias, null);
                hqlHandler.setHqlResult(hqlHandler.getInjectedHQL(nonBindedCriteria));
                builder.append(hqlHandler.getHqlResult());
            } else {
                builder.append(query).append(" WHERE ").append(parserHandler.getQueryStringNonBinded(null, filter, like));
            }
            Query q;
            q = getSession().createQuery(builder.toString());
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            parserHandler.bindParamsCriteria(q, filter, like);
            final int pageSize = IPagedQuery.getAllowedPageSize(filter);
            if (pageSize > 0) {
                q.setFirstResult(filter.getPage() * pageSize);
                q.setMaxResults(pageSize);
            }

            resultado = q.list();
        } catch (Exception ex) {
            errorHandler.stackTraceHandle(builder.toString(), ex);
        } finally {
            detachEntity();
        }

        return resultado;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void refresh(Object entity) {
        this.getSession().refresh(entity);
    }

    /**
     * @deprecated Utilizar makePersistent con parámetro loggedUserId
     */
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity) {
        Long loggedUserId = new SessionViewer(false).getLoggedUserId();
        return makePersistent(entity, loggedUserId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId) {
        return makePersistent(entity, loggedUserId, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId, Boolean generateLogging) {
        try {
            return saveOrUpdate(entity, loggedUserId, generateLogging);
        } catch (MakePersistentException ex) {
            if (ex.getErr() instanceof ConstraintViolationException) {
                throw (ConstraintViolationException) ex.getErr();
            } else if (ex.getErr() instanceof DataException) {
                throw (DataException) ex.getErr();
            } else if (ex.getErr() instanceof HibernateException) {
                throw (HibernateException) ex.getErr();
            } else if (ex.getErr() instanceof QMSException) {
                throw new ExplicitRollback(ex.getErr());
            } else if (ex.getErr() != null) {
                throw new ExplicitRollback(ex.getErr());
            } else {
                throw new ExplicitRollback("Unknown makePersistent failure", ex);
            }
        } catch (BatchUpdateException ex) {
            errorHandler.stackTraceHandle(ex);
        }
        return null;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE saveOrUpdate(final TYPE entity, Long loggedUserId) throws MakePersistentException, BatchUpdateException {
        return saveOrUpdate(entity, loggedUserId, true);
    }

    @SuppressWarnings("deprecation")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE saveOrUpdate(final TYPE entity, Long loggedUserId, boolean generateLogging) throws MakePersistentException, BatchUpdateException {
        TYPE workEntity = null;
        try {
            ID id;
            boolean newEntity = false;
            getLogger(LOGGER.MakePersistentTraking).trace("\r\n[MakePersistentTraking] makePersistent() -> init\r\n[MakePersistentTraking] {}", entity);
            try {
                id = getId(entity);
                if (id == null) {
                    getLogger().warn("ID must be not null when saving {}", entity.toString());
                    return null;
                }
                newEntity = isNewEntityByType(entity);
            } catch (Exception e) {
                getLogger().error("Regular exception with: {}", Utilities.getSerializedObj(entity));
                errorHandler.stackTraceHandle(e);
            }
            if (entity instanceof IAuditable && !Objects.equals(loggedUserId, 0L)) {
                if (newEntity) {
                    ((IAuditable) entity).setCreatedBy(loggedUserId);
                }
                ((IAuditable) entity).setLastModifiedBy(loggedUserId);
            }
            if (entity instanceof PersistableDynamicEntity) {
                PersistableDynamicEntity e = (PersistableDynamicEntity) entity;
                DynamicFieldHandler h = this.getHandler(e);
                if (newEntity) {
                    try {
                        e.setDynamicTableNameId(
                                h.persist(-1L, e.getDynamicFieldData(), e.getDynamicTableName()).getId()
                        );
                    } catch (QMSException ex) {
                        throw new MakePersistentException("No se logro guardar los campos dinámicos", ex);
                    }
                } else {
                    try {
                        h.persist(e.getDynamicTableNameId(), e.getDynamicFieldData(), e.getDynamicTableName());
                    } catch (QMSException ex) {
                        throw new MakePersistentException("No se logro guardar los campos dinámicos", ex);
                    }
                }
            }
            if (newEntity && entity instanceof ICreationTypeAware) {
                ((ICreationTypeAware) entity).setCreationType(
                        CreationType.getCurrentCreationTypeValue(this)
                );
            }
            if (newEntity) {
                if (entity instanceof StandardEntity) {
                    try {
                        EntityCommon.setNextCode((StandardEntity) entity);
                    } catch (QMSException ex) {
                        throw new MakePersistentException("No se logro establecer el codigo autogenerado", ex);
                    }
                }
            }
            try {
                workEntity = entity;
                if (getSession().contains(entity) || isEntityInSession(entity)) {
                    workEntity = getSession().merge(entity);
                }
                getSession().saveOrUpdate(workEntity);
            } catch (ConstraintViolationException e) {
                getLogger().error("ConstraintViolationException with: " + Utilities.getSerializedObj(workEntity));
                workEntity = null;
                final String message = ExceptionUtils.getRootCauseMessage(e);
                getLogger().error("Error 1 : " + message
                        + " - " + e.getConstraintName()
                        + " - " + e.getErrorCode()
                        + " - " + e.getSQLException().getSQLState() // <--- #23505 -- Representa llaves UK's violadas
                        + " - " + e.getSQLException().getErrorCode()
                        + " (Si el error que apareció en pantalla no se entiende cambiarlo [Ej: #23505])");
                if (e.getSQLException().getNextException() != null) {
                    getLogger().error(" - " + e.getSQLException().getNextException().getSQLState()
                            + " - " + e.getSQLException().getNextException().getErrorCode());
                }
                errorHandler.stackTraceHandle(message
                        + " - " + e.getConstraintName()
                        + " - " + e.getSQLException().getSQLState()
                        + " ", e);
            } catch (DataIntegrityViolationException e) {
                getLogger().error("ConstraintViolationException with: " + Utilities.getSerializedObj(workEntity));
                workEntity = null;
                final String message = ExceptionUtils.getRootCauseMessage(e);
                getLogger().error("Error 1 : " + message
                        + " - " + e.getCause()
                        + " - " + e.getMostSpecificCause()
                        + " - " + e.getRootCause()
                        + " (Si el error que apareció en pantalla no se entiende cambiarlo [Ej: #23505])");
                errorHandler.stackTraceHandle(message
                        + " - " + (e.getCause() != null ? e.getCause().getCause() : "")
                        + " - " + (e.getMostSpecificCause() != null ? e.getMostSpecificCause().getCause() : "")
                        + " - " + (e.getRootCause() != null ? e.getRootCause().getCause() : "")
                        + " ", e);
            } catch (DataException e) {
                getLogger().error("DataException with: " + Utilities.getSerializedObj(workEntity));
                final String message = ExceptionUtils.getRootCauseMessage(e);
                workEntity = null;
                getLogger().error("Error 2 : " + message
                        + " - " + e.getErrorCode()
                        + " - " + e.getSQLException().getSQLState() // <--- #22001 -- valor demasiado largo
                        + " - " + e.getSQLException().getErrorCode()
                        + " (Si el error que apareció en pantalla no se entiende cambiarlo [Ej: #22001])");
                if (e.getSQLException().getNextException() != null) {
                    getLogger().error(" - " + e.getSQLException().getNextException().getSQLState()
                            + " - " + e.getSQLException().getNextException().getErrorCode());
                }
                errorHandler.stackTraceHandle(message
                        + " - " + e.getSQLException().getSQLState()
                        + " ", e);
            } catch (HibernateException e) {
                getLogger().error("HibernateException with: " + Utilities.getSerializedObj(workEntity));
                workEntity = null;
                final String message = ExceptionUtils.getRootCauseMessage(e);
                getLogger().error("Error 3 : " + message);
                errorHandler.stackTraceHandle(message + "", e);
            } catch (Exception ee) {
                if (ee instanceof BatchUpdateException) {
                    BatchUpdateException e = (BatchUpdateException) ee;
                    getLogger().error("BatchUpdateException with: " + Utilities.getSerializedObj(workEntity));
                    workEntity = null;
                    final String message = ExceptionUtils.getRootCauseMessage(e);
                    getLogger().error("Error 1 : " + message
                            + " - " + e.getErrorCode()
                            + " - " + e.getSQLState()
                            + " - UpdateCounts: " + Arrays.toString(e.getUpdateCounts())
                    );
                    errorHandler.stackTraceHandle(message
                            + " - " + e.getErrorCode()
                            + " - " + e.getSQLState()
                            + " ", e);
                } else {
                    final String message = ExceptionUtils.getRootCauseMessage(ee);
                    getLogger().error("\r\n/Unhandled MakePersistentException!! " + ee.getLocalizedMessage() + " - " + message);
                    throw ee;
                }
            } finally {
                if (workEntity != null) {
                    if (!(workEntity instanceof Log
                            || workEntity instanceof AccessHistory
                            || workEntity instanceof ILinkedCompositeEntity
                            || workEntity instanceof PendingCount)) {
                        getLogger(LOGGER.MakePersistentTraking).trace("\r\n[MakePersistentTraking] makePersistent() -> finally\r\n[MakePersistentTraking] {}", workEntity);
                    }
                }
                if (entity != null && entity instanceof EntityDynamicFields) {
                    if (entity instanceof PersistableDynamicEntity) {
                        this.getHandler((PersistableDynamicEntity) entity).getHelper()
                                .updateDynReference((PersistableDynamicEntity) entity);
                    } else if (entity instanceof EntityDynamicFields) {
                        EntityDynamicFields ref = (EntityDynamicFields) entity;
                        if (ref.getDynamicFieldInsertDTO() != null) {
                            new DynamicTableHelper(this).updateDynReference(ref.getDynamicFieldInsertDTO(), ref);
                        } else {
                            getLogger().debug(
                                    "El entity '{}' llegó sin su valor de DynamicFieldInsertDTO para el ID = {}",
                                    new Object[]{entity.getClass().getCanonicalName(), ref.getId()}
                            );
                        }
                    }
                }
            }
        } finally {
            if (entity instanceof Persistable && generateLogging) {
                generateLogs(entity, loggedUserId, "", "persistent", "", 0L, null);
            }
        }
        return workEntity;
    }

    /**
     * Metodo para verificar si un objeto se encuentra en sesión para determinar si se hará un getSession().update ó getSession().merge
     *
     * @param entity Entidad a buscar
     * @return Boolean {true} si lo encontró ó {false} si no se encuentra en sesión
     */
    private Boolean isEntityInSession(Object entity) {
        ID id = getId(entity);
        String className = entity.getClass().getName();
        final SessionStatistics statistics = getSession().getStatistics();
        Set<?> keys = statistics.getEntityKeys();
        for (Object key : keys) {
            final EntityKey entityKey = (EntityKey) key;
            if (entityKey.getIdentifier().equals(id) && entityKey.getEntityName().equals(className)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE, TYPE_ID> TYPE_ID getId(TYPE instancia) {
        try {
            if (instancia instanceof Persistable) {
                return (TYPE_ID) ((Persistable) instancia).getId();
            } else if (instancia instanceof ILinkedCompositeEntity) {
                return (TYPE_ID) ((ILinkedCompositeEntity) instancia).getId();
            }
            Object r = beanGeneric.callMethodLong(instancia, "getId");
            if (r == null) {
                @SuppressWarnings("WrapperTypeMayBePrimitive") final Long id = -1L;
                return (TYPE_ID) id;
            }
            return (TYPE_ID) r;
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            @SuppressWarnings("WrapperTypeMayBePrimitive") final Long id = -1L;
            return (TYPE_ID) id;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDescription(T instancia) {
        try {
            return beanGeneric.callMethodString(instancia, "getDescription");
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            return "NA";
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> void makeTransient(TYPE entity) {
        try {
            getSession().remove(entity);
        } catch (HibernateException e) {
            errorHandler.stackTraceHandle(Utilities.getSerializedObj(entity), e);
        } catch (Exception e) {
            errorHandler.stackTraceHandle(Utilities.getSerializedObj(entity), e);
        }
    }

    private String getStatusField(Class<?> cls) {
        String result = "";
        try {
            String activeStatus = "1";
            Field fieldlist[] = cls.getDeclaredFields();
            boolean encontrado = false;
            String name = null;
            for (Field fld : fieldlist) {
                if (fld.getName().equals("ACTIVE_STATUS")) {
                    activeStatus = fld.get(null) + " ";
                }
                if (fld.getName().equals("status")
                        || fld.getName().equals("estatus")
                        || fld.getName().equals("intestado")
                        || fld.getName().equals("intstatususuario")) {
                    encontrado = true;
                    name = fld.getName();
                }
            }
            if (encontrado) {
                result = name + " = " + activeStatus;
            }
        } catch (SecurityException e) {
            errorHandler.stackTraceHandle(result, e);
        } catch (IllegalArgumentException e) {
            errorHandler.stackTraceHandle(result, e);
        } catch (IllegalAccessException e) {
            errorHandler.stackTraceHandle(result, e);
        }
        return result;/**/
    }

    /**
     * activa un filtro en una transaccion
     *
     * @param filterName nombre del filtro de hibernate
     * <AUTHOR> Garza Verastegui
     * @since ********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void enableFilter(String filterName) {
        this.getSession().enableFilter(filterName);
    }

    /**
     * desctiva un filtro en una transaccion
     *
     * @param filterName nombre del filtro de hibernate
     * <AUTHOR> Garza Verastegui
     * @since ********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void disableFilter(String filterName) {
        this.getSession().disableFilter(filterName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives() {
        return getActives((Class<? extends IPersistableDescription>) getPersistentClass());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Class<? extends IPersistableDescription> cls) {
        String activeFilter = "c." + this.getStatusField(cls);
        String deletedFilter = "c." + this.getDeletedField(cls);
        String filter = activeFilter + " AND " + deletedFilter;
        return (List<ITextHasValue>) this.HQLT_findByQuery(" SELECT new " + TextLongValue.class.getCanonicalName() + "("
                + "c.description,c.id"
                + " )"
                + " FROM " + cls.getCanonicalName() + " c"
                + " WHERE " + filter
                + " ORDER BY c.description"
        );
    }

    /**
     * @deprecated utilizar 'setValidEntitiesFilter'
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> getFilteredEntity(boolean isAdmin, IGridFilter filter, String intusuarioid, ProfileServices[] servicio, String order) {
        List<T> r = Utilities.EMPTY_LIST;
        try {
            boolean admin = isAdmin;
            String query;
            query = ""
                    + " SELECT c FROM " + getPersistentClass().getName() + " as c "
                    + " WHERE " + parserHandler.getQueryStringNonBinded(null, filter, false);

            this.getSession();
            Query q;
            try {
                q = getSession().createQuery(query);
                final boolean cacheable = false;
                DaoCacheUtils.configureCache(q, cacheable, null);
                parserHandler.bindParamsCriteria(q, filter, admin);
                final int pageSize = IPagedQuery.getAllowedPageSize(filter);
                if (pageSize > 0) {
                    q.setFirstResult(filter.getPage() * pageSize);
                    q.setMaxResults(pageSize);
                }
                r = q.list();
            } catch (HibernateException e) {
                errorHandler.stackTraceHandle(query, e);
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(e);
        }
        getLogger().debug("Sale filtering... se obtuvieron {} valores filtrados.", r.size());
        return r;
    }//*/

    /**
     * Busquedas de datos del Entity especificado en la declaracion del DAO,
     * utilizado en gridComponent.js
     *
     * @param filter utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return listado de entities
     * <AUTHOR>
     * @since *********
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findByPagedFilter(IGridFilter filter) {
        return HQLT_findByPagedFilter(filter, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByPagedFilter(Class<TYPE> cls, IGridFilter filter) {
        GridInfo<TYPE> temp = new GridInfo<>();
        setGridInfoData(cls, temp, filter, false, false, null, 0);
        return temp.getData();
    }

    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findByPagedFilter(IGridFilter filter, boolean like) {
        GridInfo<T> temp = new GridInfo<>();
        setGridInfoData(persistentClass, temp, filter, like, false, null, 0);
        return temp.getData();
    }

    private <TYPE> void setGridInfoData(
            Class<TYPE> TYPE,
            GridInfo<TYPE> gridInfo,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        String clase = TYPE.getName();
        List<TYPE> resultado = null;
        String HQL = "";
        String join = "", field = filter.getField().getOrderBy();

        try {
            // filter is saved into HttpSession
            sessionHandler.saveWindowFilter(filter);
            //si tiene mas de un punto
            if (field != null && field.contains(".")) {
                join = field.substring(0, field.lastIndexOf('.'));
                filter.getField().setOrderBy(field.replace(join, "x"));
                join = " LEFT JOIN c." + join + " x ";
            } else if (field != null && !field.isEmpty()) {
                filter.getField().setOrderBy("c." + filter.getField().getOrderBy());
            }
            Query q;
            ValidationDTO v = DynamicFieldHandler.isDynamicSearchValid(clase);
            if (v.isValid() && filter.isDynamicSearchEnabled()) {
                getLogger(LOGGER.DynamicSearch).trace("Se detecta busqueda por campos dinamicos");
                IDynamicFieldDAO dynamicFieldsDAO = getBean(IDynamicFieldDAO.class);
                IDynamicGridConfigHQL hqlConfig = dynamicFieldsDAO.getDynamicGridConfig(TYPE);
                Map<String, String> m = parserHandler.getCriteriaBinded(filter, like);
                String selectFrom = hqlConfig.getHqlSelect(),
                        where = m.get(QueryHandler.CRITERIA_WHERE),
                        order = m.get(QueryHandler.CRITERIA_ORDER);
                where = where.isEmpty() ? " where 0=0" : " where 0=0 AND (" + where + ") ";
                HQL = ""
                        + selectFrom
                        + join
                        + where
                        + order;
                if (getLogger(GenericDAOImpl.LOGGER.DynamicSearch).isTraceEnabled()) {
                    getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                            .trace("[{}] SELECT/BINDED HQL: {}", new Object[]{TYPE.getCanonicalName(), HQL});
                }
                String SQL = hqlToSqlString(HQL);
                if (SQL == null) {
                    throw new ExplicitRollback("Error al convertir HQL a SQL " + HQL);
                }
                if (getLogger(GenericDAOImpl.LOGGER.DynamicSearch).isTraceEnabled()) {
                    getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                            .trace("[{}] SELECT/BINDED SQL: {}", new Object[]{TYPE.getCanonicalName(), SQL});
                    getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                            .trace("[{}] Obteniendo configuración SQL para agregar filtros.", new Object[]{TYPE.getCanonicalName()});
                }
                DynamicGridConfigSQL sqlConfig = new DynamicGridConfigSQL(SQL);
                dynamicFieldsDAO.setDynamicGridConfigSQL(filter, clase, hqlConfig, sqlConfig);
                gridInfo.setParseFromDynamicResults(true);
                q = getSession().createNativeQuery(sqlConfig.getSqlFinal().toString());
                if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                    q.setTimeout(queryTimeoutSeconds);
                }
                NativeQuery qs = (NativeQuery) q;
                qs.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
                if (getLogger(LOGGER.DynamicSearch).isTraceEnabled()) {
                    getLogger(LOGGER.DynamicSearch).trace("\r\nSQL: "
                            + sqlConfig.getSqlFinal()
                            + "\r\n/{}\r\n", filter.getCriteria());
                }
                //TODO: La columnas en un SUBQUERY deben ser unicas. Error en Solicitudes de documentos: The column 'code' was specified multiple times for 'n'.
                String sqlCount = "SELECT count(1) " + sqlHandler.removeSelect(q.getQueryString().substring(0, q.getQueryString().lastIndexOf("order by")));
                Long count = SQL_findSimpleLong(sqlCount, Utilities.EMPTY_MAP, queryTimeoutSeconds);
                gridInfo.setCount(count);
                gridInfo.setLastSyncDate(Utilities.getNow());
            } else {
                HQL = "SELECT c FROM " + clase + " c " + join + " WHERE c.id = c.id AND " + parserHandler.getQueryStringNonBinded(null, filter, like);
                q = getSession().createQuery(HQL);
                if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                    q.setTimeout(queryTimeoutSeconds);
                }
                DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
                parserHandler.bindParamsCriteria(q, filter, like);
                getLogger(LOGGER.GridComponent_PagedFilterHQL).trace("\r\nHQL: " + HQL + "\r\n/{}\r\n", filter.getCriteria());
                if (getLogger(LOGGER.GridComponent_PagedFilterSQL).isTraceEnabled()) {
                    getLogger(LOGGER.GridComponent_PagedFilterSQL).trace("\r\nSQL: "
                            + hqlToSqlString(HQL)
                            + "\r\n/{}\r\n", filter.getCriteria());
                }
            }
            final int pageSize = IPagedQuery.getAllowedPageSize(filter);
            if (pageSize > 0) {
                q.setFirstResult(filter.getPage() * pageSize);
                q.setMaxResults(pageSize);
            }
            try {
                resultado = q.list();
            } catch (HibernateException e) {
                if ("collection is not associated with any session".equals(e.getMessage())) {
                    HQL = "SELECT c FROM " + clase + " c " + join + " WHERE c.id = c.id AND " + parserHandler.getQueryStringNonBinded(null, filter, like);
                    q = getSession().createQuery(HQL);
                    if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                        q.setTimeout(queryTimeoutSeconds);
                    }
                    DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
                    parserHandler.bindParamsCriteria(q, filter, like);
                    resultado = q.list();
                } else {
                    errorHandler.stackTraceHandle(HQL, e);
                }
            }
        } catch (Exception e) {
            errorHandler.stackTraceHandle(HQL, e);
        } finally {
            detachEntity();
        }
        if (resultado == null) {
            getLogger().error("\r\n//Error: validar las siguientes posibilidades, "
                    + "[Columnas sin declarar (StandarEntity)], "
                    + "[Clases sin mapear (hibernate.cfg.xml)] ");
            resultado = Utilities.EMPTY_LIST;
        }
        gridInfo.setData(resultado);
    }

    /**
     * Utilizado para obtener un listado de Entities que cumplen con un filtro "String"
     * especifico, el Entity tiene como alias "c"
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQueryFilter("c.cuenta = 'admin'");
     *
     * @param clazz   Class del entity a buscar
     * @param filtros {String}: es el filtro que va despues del WHERE en un query HQL
     * @return listado de entities
     * <AUTHOR>
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQueryFilter(Class<TYPE> clazz, String filtros) {
        return this.HQLT_findByQueryFilter_NEntityFilter(clazz, filtros, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findByQueryFilter(String filtros) {
        return this.HQLT_findByQueryFilter_NEntityFilter(this.getPersistentClass(), filtros, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findByQueryFilter_NEntityFilter(String filtro, String[] filtros) {
        return this.HQLT_findByQueryFilter_NEntityFilter(this.getPersistentClass(), filtro, filtros);
    }

    /**
     * Utilizado para obtener un listado de Entities que cumplen con un filtro "String"
     * especifico, el Entity tiene como alias "c"
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findByQueryFilter(
     * "c.code like '%0001'",
     * new String[]{"not_children"}
     * );
     *
     * @param <TYPE>  : Tipo
     * @param TYPE    : Class del entity a buscar
     * @param filtro  {String}    : es el filtro que va despues del WHERE en un query HQL
     * @param filtros {String[]} : es el filtro que va despues del WHERE en un query HQL
     * @return : listado de entities
     * <AUTHOR> Llimas
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQLT_findByQueryFilter_NEntityFilter(Class<TYPE> TYPE, String filtro, String[] filtros) {
        List<TYPE> resultado = Utilities.EMPTY_LIST;

        String query = null;
        try {
            if (filtros != null) {
                for (String f : filtros) {
                    getSession().enableFilter(f);
                }
            }
            query = "FROM " + TYPE.getCanonicalName() + " c " + (filtro == null ? "" : " WHERE " + filtro);
            final Query q = getSession().createQuery(query);
            final boolean cacheable = false;
            DaoCacheUtils.configureCache(q, cacheable, null);
            resultado = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(query, e);
        } finally {
            if (filtros != null) {
                for (String f : filtros) {
                    getSession().disableFilter(f);
                }
            }
            detachEntity();
        }
        return resultado;
    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algun CRUD (CRUD_Generic.java)
     *
     * @param filter : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<T> getRows(IGridFilter filter) {
        return getRows(filter, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<T> getRows(
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        GridInfo<T> result = new GridInfo<>();
        if (!filter.isEnableStatistics() || filter.getStatisticsFields() == null
                || filter.getStatisticsFields().isEmpty()) {
            getLogger().trace("Framework.DAO.getRows {}... {}", getPersistentClass(), filter.getCriteria());

            //Primero se obtiene el número de resultados
            // :: filter is saved into HttpSession inside HQLT_countByPagedFilter ::
            Long count = HQL_countByPagedFilter(persistentClass, filter, false, cacheable, cacheRegion, queryTimeoutSeconds);
            result.setCount(count);
            result.setLastSyncDate(Utilities.getNow());

            if (null != count && count > 0) {
                setGridInfoData(persistentClass, result, filter, false, cacheable, cacheRegion, queryTimeoutSeconds);
            } else {
                result.setData(Utilities.EMPTY_LIST);
            }
            result.setGridId(filter.getGridId());
        } else {
            getLogger().trace("Framework.DAO.getRows - statistics mode {}... {}", getPersistentClass(), filter.getCriteria());
            try {
                Map<String, List<StringLongPair>> statistics
                        = StatisticsService.getSimpleStatistics(getSession(), persistentClass, filter, false, cacheable, queryTimeoutSeconds);
                result.setStatisticsResults(statistics);
            } catch (Exception ex) {
                errorHandler.stackTraceHandle(ex);
            }
        }
        return result;
    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algun CRUD (CRUD_Generic.java)
     *
     * @param filter : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * <AUTHOR> Luis Carlos Limas
     * @since : 2.3.2.167
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> GridInfo<TYPE> getRows(Class<TYPE> TYPE, IGridFilter filter) {
        return getRows(TYPE, false, null, 0, filter);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> GridInfo<TYPE> getRows(
            Class<TYPE> TYPE,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            IGridFilter filter
    ) {
        getLogger().trace("Framework.DAO.getRows {}... {}", getPersistentClass(), filter.getCriteria());
        Long count;
        GridInfo<TYPE> result = new GridInfo<>();
        if (!filter.isEnableStatistics() || filter.getStatisticsFields() == null || filter.getStatisticsFields().isEmpty()) {
            //Primero se obtiene el número de resultados
            count = HQLT_countByPagedFilter(TYPE, filter, cacheable, cacheRegion, queryTimeoutSeconds);
            result.setCount(count);
            result.setLastSyncDate(Utilities.getNow());

            if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
                filter.getField().setOrderBy("id");
                filter.setDirection((byte) 2);
            }

            if (count > 0) {
                setGridInfoData(TYPE, result, filter, false, cacheable, cacheRegion, queryTimeoutSeconds);
            } else {
                result.setData(Utilities.EMPTY_LIST);
            }
            result.setGridId(filter.getGridId());
        } else {
            getLogger().trace("Framework.DAO.getRows - statistics mode {}... {}", getPersistentClass(), filter.getCriteria());
            try {
                Map<String, List<StringLongPair>> statistics = StatisticsService.getSimpleStatistics(
                        getSession(),
                        TYPE,
                        filter,
                        false,
                        cacheable,
                        queryTimeoutSeconds
                );
                result.setStatisticsResults(statistics);
            } catch (Exception ex) {
                errorHandler.stackTraceHandle(ex);
            }
        }
        return result;
    }

    private <TYPE> Long HQLT_countByPagedFilter(
            Class<TYPE> TYPE,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        long resultado;
        String query = null;
        try {
            // filter is saved into HttpSession
            sessionHandler.saveWindowFilter(filter);
            query = HQLHandler.makeCountHQL(TYPE, filter, false);
            getLogger(LOGGER.GridComponent_PagedFilterHQL).trace("\r\n{}\r\n/{}\r\n", query, filter.getCriteria());
            Query q = getSession().createQuery(query);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            DaoCacheUtils.configureCache(q, cacheable, cacheRegion);
            parserHandler.bindParamsCriteria(q, filter, false);
            String r = "";
            try {
                r = q.uniqueResult() + "";
            } catch (Exception ex) {
                getLogger().error("Error al correr el query", ex);
            }
            if (!"null".equals(r)) {
                resultado = Long.parseLong(r);
            } else {
                resultado = 0L;
            }
        } catch (Exception ex) {
            errorHandler.stackTraceHandle(query, ex);
            resultado = 0L;
        } finally {
            detachEntity();
        }
        getLogger().trace("resultado: {}", resultado);
        return resultado;
    }

    /**
     * Utilizado para obtener un Entity por su ID en especifico
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findById(
     * 1L
     * );
     *
     * @param id : Id del entity, generalmente un Long
     * @return entity Typed con los datos obtenidos en base al id del entity
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public T HQLT_findById(ID id) {
        return this.HQLT_findById(id, false);
    }

    /**
     * Utilizado para obtener un Entity por su ID en especifico
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findById(
     * Request.class,
     * 1L
     * );
     *
     * @param id : Id del entity, generalmente un Long
     * @return entity Typed con los datos obtenidos en base al id del entity
     * <AUTHOR> Luis Limas
     * @since : 2.3.2.158
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE HQLT_findById(Class<TYPE> TYPE, Long id) {
        return HQLT_findById(TYPE, id, false, null, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE HQLT_findById(
            Class<TYPE> TYPE,
            Long id,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        if (id == null) {
            return null;
        }
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", id);
        final String hql = "SELECT c FROM " + TYPE.getName() + " c WHERE c.id = :id";
        return (TYPE) this.HQL_findSimpleObject(
                hql,
                params,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds
        );
    }

    /**
     * Utilizado para obtener un Entity por su ID en especifico
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findById(
     * 1L,
     * false
     * );
     *
     * @param id   : Id del entity, generalmente un Long
     * @param lock : bandera para trabar la tabla
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public T HQLT_findById(ID id, boolean lock) {
        try {
            T entity;
            if (id instanceof Long) {
                entity = (T) HQL_findSimpleObject("FROM " + this.getPersistentClass().getCanonicalName() + " c WHERE c.id = :id", "id", id);
            } else {
                entity = getSession().get(getPersistentClass(), id, lock ? LockOptions.UPGRADE : LockOptions.NONE);
            }
            return entity;
        } finally {
            detachEntity();
        }
    }

    protected GenericSaveHandle failGSH() {
        GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setOperationEstatus(0);
        getLogger(LOGGER.MakePersistentTraking).trace("\r\n[MakePersistentTraking] failGSH()\r\n[MakePersistentTraking] {}", gsh);
        throw new DAOException(gsh);
    }

    /**
     * Utilizado para obtener un listado de Entities
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findAll();
     *
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findAll() {
        return HQLT_findByQueryFilter(null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> List<TYPE> HQL_findAll(Class<TYPE> TYPE) {
        return this.HQLT_findByQueryFilter_NEntityFilter(TYPE, null, null);
    }

    /**
     * Busquedas de datos del Entity especificado en la declaracion del DAO,
     * utilizado para busquedas por query directo
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Users c");
     *
     * @param query : El query, debe regressar entities tipo "T"
     * @return : listado de entities
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<T> HQLT_findByQuery(String query) {
        List<T> resultado = Utilities.EMPTY_LIST;
        try {
            final Query q = getSession().createQuery(query);
            final boolean cacheable = false;
            DaoCacheUtils.configureCache(q, cacheable, null);
            resultado = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(query, e);
        } finally {
            detachEntity();
        }
        return resultado;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(T entity) {
        throw new ExplicitRollback("Not supported yet. this method is meant to be overridden");
    }

    @Override
    public GenericSaveHandle successGSH(boolean nuevo, Object id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (id != null) {
            gsh.setSavedId(id.toString());
        }
        gsh.setOperationEstatus(1);
        gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        getLogger(LOGGER.MakePersistentTraking).trace("\r\n[MakePersistentTraking] successGSH()\r\n[MakePersistentTraking] {}", gsh);
        return gsh;
    }

    @Override
    public GenericSaveHandle successGSH(boolean nuevo) {
        return this.successGSH(nuevo, null);
    }

    @Override
    public GenericSaveHandle successGSH() {
        return this.successGSH(true);
    }

    @SuppressWarnings("ConstantValue")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String generateCode(String prefix, Long id) {
        Long proxyId = id;
        String codeNum;
        String code;
        Map<String, Object> params = new HashMap<>(1);
        Object retValue;
        String query = "SELECT id FROM " + this.getPersistentClass().getName() + " c WHERE c.code = :code";
        do {
            codeNum = Framework.Config.Utilities.formatConsecutivo(proxyId++);
            code = prefix + "-" + codeNum;
            params.put("code", code);
            retValue = this.HQL_findSimpleObject(query, params);
        } while (retValue != null);
        return code;
    }

    /**
     * @return the IS_SQL_SERVER
     */
    @SuppressWarnings("unused")
    public static boolean isIS_SQL_SERVER() {
        return IS_SQL_SERVER;
    }

    /**
     * @param aIS_SQL_SERVER the IS_SQL_SERVER to set
     */
    public static void setIS_SQL_SERVER(boolean aIS_SQL_SERVER) {
        IS_SQL_SERVER = aIS_SQL_SERVER;
    }

    /**
     * Metodo necesario para los querys de los reportes en quejas
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean SQL_createView(String nameView, String sql) throws QMSException {
        //vista necesaria para usar el reporte jasper
        String view;
        if (IS_SQL_SERVER) {
            view = "CREATE OR ALTER VIEW " + nameView + " AS " + sql;
        } else {
            view = "CREATE OR REPLACE VIEW " + nameView + " AS " + sql;
        }
        return SQL_execute(view);
    }

    /**
     * Metodo para convertir en diferentes formatos fechas
     * Ejemplo convert(varchar(10), '11/20/2015', 120)
     * Para consultar los formatos ver: <a href="https://msdn.microsoft.com/es-es/library/ms187928(v=sql.120).aspx">...</a>
     *
     * @param tipoDatoDestino tipo de dato al que se convertira
     * @param fecha           fecha en string
     * @param formato         el formato de fecha al que se desea convertir
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_convertDate(String tipoDatoDestino, String fecha, String formato) {
        return "convert(" + tipoDatoDestino + ", '" + fecha + "', " + formato + ")";
    }

    @Override
    public boolean isNewEntity(final Persistable ent) {
        return isNewEntityByType(ent);
    }

    private <TYPE> boolean isNewEntityByType(final TYPE ent) {
        if (ent instanceof ILinkedCompositeEntity) {
            return ((ILinkedCompositeEntity) ent).isInsert();
        }
        final ID id = getId(ent);
        return isNewEntity(id);
    }

    @Override
    public boolean isNewEntity(ID id) {
        if (!(id instanceof Long)) {
            return false;
        }
        return -1L == (Long) id;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Long loggedUserId
    ) {
        saveLinkedItem(compositeEntity, groundId, dialogId, null, loggedUserId);
    }

    /**
     * Método para guardar los elementos de un LinkedGrid
     *
     * @param compositeEntity CompositeEntity configurada que implementa ILinkedCompositeEntity
     * @param groundId        Id del entity base
     * @param dialogId        Id del entity relacionado
     * @param stage           Etapa en que se agrega
     * @param loggedUserId    Logged User Id
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Integer stage,
            final Long loggedUserId
    ) {
        final ILinkedCompositeEntity linked = LinkedCompositeUtil.newInstance(
                compositeEntity,
                groundId,
                dialogId,
                stage,
                loggedUserId
        );
        makePersistent(linked, loggedUserId);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public <TYPE extends Persistable> TYPE saveSingleLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final TYPE singleLinkedItem,
            final Long parentId,
            final Integer stage,
            final Long loggedUserId
    ) {
        final TYPE result = makePersistent(singleLinkedItem, loggedUserId);
        final ILinkedCompositeEntity linked = LinkedCompositeUtil.newInstance(
                compositeEntity,
                parentId,
                result.getId(),
                stage,
                loggedUserId
        );
        makePersistent(linked, loggedUserId);
        return result;

    }

    protected void detachEntity() {
        final Session session = getSession();
        session.flush();
        session.clear();
    }

    protected boolean isSql2012OrGreater() {
        return getSession().doReturningWork(work -> {
            final Connection conn = work.unwrap(Connection.class);
            return sqlHandler.isSql2012OrGreater(conn);
        });
    }

    private void generateLogs(final Object entity, final Long loggedUserId, String newValue, String type, String condition, Long recordId, String observation) {
        if (entity != null
                && !(
                entity instanceof Log
                        || entity instanceof IExcludeLogging
                        || entity instanceof AccessHistory
                        || entity instanceof CodeSequence
                        || entity instanceof BulkLoadRow
                        || entity instanceof BulkUserRecord
                        || entity instanceof BulkUser
                        || entity instanceof BulkLoad
                        || entity instanceof PendingRecord
                        || entity instanceof PendingCount
        )
        ) {
            Log log = new Log();

            log.setId(-1L);

            if ("update".equals(type)) {
                log.setRecordId(recordId);
                //esto arroja una excepcion aproposito! (solo cuando ya ocurrio una en el saveOrUpdate)
                log.setClassName(entity.toString());
            } else {
                Long updatedId = getId(entity);
                log.setRecordId(updatedId);
                //esto arroja una excepcion aproposito! (solo cuando ya ocurrio una en el saveOrUpdate)
                log.setClassName(entity.getClass().getSimpleName());
            }
            log.setNewValue(newValue);
            log.setType(type);
            log.setCondition(condition);
            log.setObservation(observation);

            final Long userId;
            if (loggedUserId == null || loggedUserId.equals(-1L)) {
                final SessionViewer sv = new SessionViewer(false);
                if (sv.getLoggedUserId() != 0) {
                    userId = sv.getLoggedUserId();
                    log.setUserId(userId);
                } else {
                    userId = 1L;
                    log.setUserId(userId);
                }
            } else {
                userId = loggedUserId;
                log.setUserId(userId);
            }
            makePersistent(log, userId);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Long loggedUserId
    ) {
        saveLinkedItems(compositeEntity, groundId, dialogIds, deleteLinkedItems, null, false, null, 0, loggedUserId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Integer stage,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds,
            final Long loggedUserId
    ) {
        final boolean emptyIds = dialogIds.isEmpty();
        final String canonicalName = compositeEntity.getCanonicalName();
        final String groundIdField = LinkedCompositeUtil.getGroundIdField(canonicalName);
        final String dialogIdField = LinkedCompositeUtil.getDialogIdField(canonicalName);
        if (deleteLinkedItems) {
            final StringBuilder hql = new StringBuilder(200);
            hql.append(SqlQueryParser.FROM).append(compositeEntity.getCanonicalName()).append(" c"
                    + " WHERE c.id.").append(groundIdField).append(" = :groundId");
            boolean delete;
            if (emptyIds) {
                delete = HQL_findSimpleInteger("SELECT count(*) " + hql,
                        ImmutableMap.of("groundId", groundId),
                        true,
                        cacheRegion,
                        0
                ) > 0;
                if (delete) {
                    HQL_updateByQuery("DELETE " + hql, "groundId", groundId);
                }
            } else {
                hql.append(" AND c.id.").append(dialogIdField).append(" NOT IN (:dialogIds)");
                final Map<String, Object> params = new HashMap<>(2);
                params.put("groundId", groundId);
                params.put("dialogIds", dialogIds);
                delete = HQL_findSimpleInteger("SELECT count(*) " + hql,
                        params,
                        true,
                        cacheRegion,
                        0
                ) > 0;
                if (delete) {
                    HQL_updateByQuery("DELETE " + hql, params, cacheable, cacheRegion, 0);
                }
            }
        }
        if (!emptyIds) {
            String hql = " SELECT c.id." + dialogIdField + " FROM " + compositeEntity.getCanonicalName() + " c"
                    + " WHERE c.id. " +
                    groundIdField + " = :groundId";
            final List currentDialogIds = HQL_findByQuery(
                    hql,
                    ImmutableMap.of("groundId", groundId),
                    true,
                    cacheRegion,
                    0
            );
            dialogIds.removeAll(currentDialogIds);
            dialogIds.stream()
                    .map((dialogId) -> LinkedCompositeUtil.newInstance(compositeEntity, groundId, dialogId, stage, loggedUserId))
                    .forEachOrdered((linked) -> makePersistent(linked, loggedUserId)
                    );
        }
    }

    protected void throwExplicitRollback(String message, Object... print) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        getLogger(LOGGER.ExplicitRollback).info(message, print);
        gsh.setErrorMessage(message);
        gsh.setOperationEstatus(0);
        gsh.getJsonEntityData().put("error", "rollback");
        gsh.getJsonEntityData().put("errorString", message);
        throw new DAOException(gsh);
    }

    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     */
    @Override
    public IUntypedDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IUntypedDAO.class);
    }

    /**
     * Executes a SQL statement and return a list of maps with the
     * result
     *
     * @param SQL SQL statement to execute, you can include a parameter defined with key and its value
     * @return List of maps with the result
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> SQL_findMap(String SQL, String key, String value) {
        Map params = new HashMap();
        params.put(key, value);
        return SQL_findMap(SQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> SQL_findMap(String SQL) {
        return SQL_findMap(SQL, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map SQL_findSimpleMap(String SQL) {
        Map params = new HashMap();
        return SQL_findSimpleMap(SQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map SQL_findSimpleMap(String SQL, String key, String value) {
        Map params = new HashMap();
        params.put(key, value);
        return SQL_findSimpleMap(SQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map SQL_findSimpleMap(String SQL, Map params) {
        Map result = Utilities.EMPTY_MAP;
        try {
            Query q = getSession().createNativeQuery(SQL);
            q.setMaxResults(2);
            q.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
            handleQueryProperties(params, q);
            List<Map<String, Object>> l = q.list();
            if (!l.isEmpty()) {
                result = l.get(0);
            }

        } catch (NonUniqueResultException e) {
            getLogger().error("El query '{}' esta regresando mas de un resultado en un findSimple", SQL);
            if (LicenseUtil.isDevelopment()) {
                throw new ExplicitRollback("[ONLY_DEV] El query esta regresando mas de un resultado en un findSimple");
            }
            errorHandler.stackTraceHandle(SQL, e);
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
        }
        return result;
    }

    /**
     * Executes a SQL statement and return a list of maps with the
     * result
     *
     * @param SQL    SQL statement to execute, you can include named parameters but
     *               they must be also included in <code>params</code>
     * @param params Named parameters
     * @return List of maps with the result
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> SQL_findMap(String SQL, Map params) {
        List<Map<String, Object>> results = null;
        try {
            NativeQuery q = getSession().createNativeQuery(SQL);
            q.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
            if (params != null) {
                handleQueryProperties(params, q);
            }
            results = q.list();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
        }
        return results;

    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     *
     * @param sqlSelect Consulta sql la cual puede tener solo el estamento del WHERE,
     *                  este método genera automaticamente la parte del SELECT utilizando
     *                  las columnas guardadas en IGridFilter
     * @param filter    : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * @since : 2.13.0.9
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String sqlSelect,
            final IGridFilter filter
    ) {
        return SQL_getRowsByQuery(null, sqlSelect, filter);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String withSelect,
            final String sqlSelect,
            final IGridFilter filter
    ) {
        return getSession().doReturningWork(work -> {
            final Connection conn = work.unwrap(Connection.class);
            return sqlDao.SQL_getRowsByQuery(withSelect, sqlSelect, filter, conn, null);
        });
    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     * <p>
     * /**
     * Executes a SQL statement and return a single long result
     *
     * @param query  SQL statement to execute
     * @param params Named parameters
     * @return Single long result
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSingleLong(String query, Map<String, Object> params) {
        final Object result = SQL_findSingleObject(query, params);
        if (result == null) {
            return null;
        }
        return ((Number) result).longValue();
    }

    /**
     * Executes a SQL statement and return a single string result
     *
     * @param query  SQL statement to execute
     * @param params Named parameters
     * @return Single string result
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String SQL_findSingleString(String query, Map<String, Object> params) {
        return (String) SQL_findSingleObject(query, params);
    }

    /**
     * Executes a SQL statement and return a single object
     *
     * @param SQL    SQL statement to execute, you can include named parameters but
     *               they must be also included in <code>params</code>
     * @param params Named parameters
     * @return Object
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object SQL_findSingleObject(String SQL, Map<String, Object> params) {
        Object result = null;
        try {
            Query q = getSession().createNativeQuery(SQL);
            handleQueryProperties(params, q);
            result = q.uniqueResult();
        } catch (Exception e) {
            errorHandler.stackTraceHandle(SQL, e);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE extends Persistable> TYPE makeCustomPersistent(
            final TYPE entity,
            final Boolean useCodePrefix,
            final ILoggedUser loggedUser,
            final IPersistenceManager... managers
    ) throws QMSException {
        for (final IPersistenceManager manager : managers) {
            if (manager == null) {
                continue;
            }
            manager.persistBefore(this, entity, useCodePrefix, loggedUser);
        }
        final TYPE saved = makePersistent(entity, loggedUser.getId());
        if (saved == null) {
            throw new DAOException("Could not persist custom entity", null);
        }
        for (final IPersistenceManager manager : managers) {
            if (manager == null) {
                continue;
            }
            manager.persistAfter(this, saved, loggedUser);
        }
        return saved;
    }

    public DynamicFieldHandler getHandler(PersistableDynamicEntity entity) {
        if (handler == null) {
            Class temp;
            if (entity.getType() == null) {
                temp = ReflectionUtil.getFirstInterfaceTypeParameter(entity.getClass(), PersistableDynamicEntity.class);
            } else {
                temp = entity.getType().getClass();
            }
            this.handler = new DynamicFieldHandler(
                    temp /*dynamicEntity*/, entity.getClass() /*custom*/, Utilities.getBean(IDynamicFieldDAO.class)
            );
        }
        return handler;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> toggleStatus(Long id, ILoggedUser loggedUser) {
        return GenericDAOImplUtil.toggleStatus(this, this.persistentClass, id, false, null, 0, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> toggleStatus(
            final Long id,
            final Integer ACTIVE,
            final Integer INACTIVE,
            final ILoggedUser loggedUser
    ) {
        return GenericDAOImplUtil.toggleStatus(this, this.persistentClass, id, ACTIVE, INACTIVE, false, null, 0, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(
            final Class<TYPE> cls,
            final Long id,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds,
            final ILoggedUser loggedUser
    ) {
        return GenericDAOImplUtil.toggleStatus(this, cls, id, cacheable, cacheRegion, 0, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(
            final Class<TYPE> cls, Long id,
            final Integer ACTIVE,
            final Integer INACTIVE,
            final ILoggedUser loggedUser
    ) {
        return GenericDAOImplUtil.toggleStatus(this, cls, id, ACTIVE, INACTIVE, false, null, 0, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getRowsSQL(Class entityClass) {
        return PendingHelper.getRowsSQL(getEntityManager(), entityClass);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String hqlToSqlString(String HQL) {
        return Translate.hqlToSqlString(getEntityManager(), HQL);
    }


    /**
     * Actualizacion de datos por query nativo, UPDATE, DELETE, INSERT
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .SQL_updateByQuery(
     * "UPDATE users u SET password = 'x' WHERE code like 'A%'"
     * );
     *
     * @param SQL              : El query SQL
     * @param sessionStatement SET statements that change the current session handling of specific information
     * @return : renglones
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_externalUpdateByQuery(
            final String SQL,
            final Map<String, Object> params,
            Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        if (connection == null) {
            return getSession().doReturningWork(work -> {
                final Connection conn = work.unwrap(Connection.class);
                return SQL_externalUpdateByQueryWithConnection(SQL, params, conn, sessionStatement, queryTimeoutSeconds);
            });
        } else {
            return SQL_externalUpdateByQueryWithConnection(SQL, params, connection, sessionStatement, queryTimeoutSeconds);
        }
    }

    private Integer SQL_externalUpdateByQueryWithConnection(
            final String SQL,
            final Map<String, Object> params,
            Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        if (connection == null) {
            return getSession().doReturningWork(work -> {
                final Connection conn = work.unwrap(Connection.class);
                return SQL_externalUpdateByQuery(SQL, params, conn, sessionStatement, queryTimeoutSeconds);
            });
        }
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(getClass());
        try {
            final NamedParameterJdbcTemplate jdbcTemplate = sqlDao.getJdbcTemplate(connection);
            if (sessionStatement != null && !sessionStatement.trim().isEmpty()) {
                jdbcTemplate.getJdbcTemplate().execute(sessionStatement);
            }
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                jdbcTemplate.getJdbcTemplate().setQueryTimeout(queryTimeoutSeconds);
            }
            return jdbcTemplate.update(SQL, params);
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } catch (final HibernateException e) {
            errorHandler.stackTraceHandle(SQL, e);
        } catch (final Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } finally {
            qms.framework.util.MeasureTime.stop(tStart, "Elapsed time in SQL_updateByQuery [" + SQL + "]");
        }
        return 0;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> SQL_findByQuery(
            final String SQL,
            final Map<String, Object> params,
            final Connection connection,
            final String sessionStatement
    ) {
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(getClass());
        try {
            final NamedParameterJdbcTemplate jdbcTemplate = sqlDao.getJdbcTemplate(connection);
            if (sessionStatement != null && !sessionStatement.trim().isEmpty()) {
                jdbcTemplate.getJdbcTemplate().execute(sessionStatement);
            }
            return jdbcTemplate.queryForList(SQL, params);
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } catch (final HibernateException e) {
            errorHandler.stackTraceHandle(SQL, e);
        } catch (final Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } finally {
            qms.framework.util.MeasureTime.stop(tStart, "Elapsed time in SQL_findByQuery [" + SQL + "]");
        }
        return new ArrayList<>(0);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(
            final String SQL,
            final String key,
            final String value,
            final Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put(key, value);
        return SQL_findSimpleInteger(SQL, params, connection, sessionStatement, queryTimeoutSeconds);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer SQL_findSimpleInteger(
            final String SQL,
            final Map<String, Object> params,
            final Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        if (connection == null) {
            return SQL_findSimpleInteger(SQL, queryTimeoutSeconds);
        }
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(getClass());
        try {
            final NamedParameterJdbcTemplate jdbcTemplate = sqlDao.getJdbcTemplate(connection);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                jdbcTemplate.getJdbcTemplate().setQueryTimeout(queryTimeoutSeconds);
            }
            Integer result = jdbcTemplate.queryForObject(SQL, params, Integer.class);
            if (result == null) {
                return 0;
            }
            return result;
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } catch (final HibernateException e) {
            errorHandler.stackTraceHandle(SQL, e);
        } catch (final Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } finally {
            qms.framework.util.MeasureTime.stop(tStart, "Elapsed time in SQL_findSimpleInteger [" + SQL + "]");
        }
        return 0;
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(
            final String SQL,
            final String key,
            final String value,
            final Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put(key, value);
        return SQL_findSimpleLong(SQL, params, connection, sessionStatement, queryTimeoutSeconds);
    }

    @Override
    @Nonnull
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long SQL_findSimpleLong(
            final String SQL,
            final Map<String, Object> params,
            final Connection connection,
            final String sessionStatement,
            final Integer queryTimeoutSeconds
    ) {
        final ElapsedDataDTO tStart = qms.framework.util.MeasureTime.start(getClass());
        try {
            final NamedParameterJdbcTemplate jdbcTemplate = sqlDao.getJdbcTemplate(connection);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                jdbcTemplate.getJdbcTemplate().setQueryTimeout(queryTimeoutSeconds);
            }
            final Long result = jdbcTemplate.queryForObject(SQL, params, Long.class);
            if (result == null) {
                return 0L;
            }
            return result;
        } catch (final SemanticException e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } catch (final HibernateException e) {
            errorHandler.stackTraceHandle(SQL, e);
        } catch (final Exception e) {
            final String message = ExceptionUtils.getRootCauseMessage(e);
            errorHandler.stackTraceHandle(message + "\r\n" + SQL, e);
        } finally {
            qms.framework.util.MeasureTime.stop(tStart, "Elapsed time in SQL_findSimpleLong [" + SQL + "]");
        }
        return 0L;
    }


}