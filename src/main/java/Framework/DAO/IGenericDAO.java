package Framework.DAO;

import DPMS.Mapping.Persistable;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.util.interfaces.IGridFilter;

/**
 * An interface shared by all business data access objects.
 * <p>
 * All CRUD (create, read, update, delete) basic data access operations are
 * isolated in this interface and shared accross all DAO implementations.
 * The current design is for a state-management oriented persistence layer
 * (for example, there is no UDPATE statement function) that provides
 * automatic transactional dirty checking of business objects in persistent
 * state.
 *
 * <AUTHOR>
 * @param <T>
 * @param <ID>
 */
public interface IGenericDAO<T, ID extends Serializable> extends ICRUD_GenericDAO<T>{
    
    /*Typed HQL methods*/
    T HQLT_findById(ID id);
    T HQLT_findById(ID id, boolean lock);
    @Deprecated
    List<T> HQLT_findByPagedFilter(IGridFilter filter);
    @Deprecated
    List<T> HQLT_findByPagedFilter(IGridFilter filter, boolean like);
    List<T> HQLT_findByQuery(String query);
    List<T> HQLT_findByQueryFilter(String filtros);
    List<T> HQLT_findByQueryFilter_NEntityFilter(String filtro, String[] filtros);
    List<T> getFilteredEntity(boolean isAdmin, IGridFilter filter, String intusuarioid, ProfileServices[] servicio, String order);
    /*Context DAOs*/
    IUntypedDAO getUntypedDAO();
    
    boolean isNewEntity(Persistable ent);
    boolean isNewEntity(ID id);
    
    
    ResponseEntity<Map<String, Object>> toggleStatus(Long id, ILoggedUser loggedUser);
    ResponseEntity<Map<String, Object>> toggleStatus(Long id, final Integer ACTIVE, Integer INACTIVE, ILoggedUser loggedUser);
    
}