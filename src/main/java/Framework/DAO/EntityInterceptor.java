package Framework.DAO;

import DPMS.Mapping.DocumentModification;
import DPMS.Mapping.Log;
import DPMS.Mapping.Persistable;
import DPMS.Mapping.UserSettings;
import Framework.Config.BaseDomainObject;
import Framework.Config.IdentityUuidObject;
import Framework.Config.Utilities;
import ape.pending.entities.PendingCount;
import ape.pending.entities.PendingRecord;
import bnext.reference.IAuditable;
import java.io.Serializable;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import org.hibernate.CallbackException;
import org.hibernate.Interceptor;
import org.hibernate.type.Type;
import qms.framework.core.EntityModelCache;
import qms.framework.core.EntityModelCache;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.LocalizedUtil;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.IPersistableManualId;

public class EntityInterceptor implements Interceptor, Serializable {

    private static final long serialVersionUID = -1L;

    public static final Pattern COLLATION_PATTERN = Pattern.compile(" LIKE ('%.+?%')| LIKE (\\?)", Pattern.CASE_INSENSITIVE);
    public static final Pattern COLLATION_PATTERN_FIX = Pattern.compile("like  collate");
    public static final String COLLATION_LATIN_CASE_AND_ACCENT_INSENSITIVE = " Latin1_General_100_CI_AI ";
    public static final String COLLATION_LATIN_CS_AI = " Latin1_General_100_CS_AI ";
    public static final String COLLATION_REPL = " like $1$2 collate " + COLLATION_LATIN_CASE_AND_ACCENT_INSENSITIVE;
    public static final String COLLATION_REPL_FIX = "like ? collate";
    private final Set<BaseDomainObject<?>> inserts = ConcurrentHashMap.newKeySet();
    private final Set<BaseDomainObject<?>> updates = ConcurrentHashMap.newKeySet();

    @Override
    public boolean onPersist(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types) throws CallbackException {
        if (!Utilities.areEntitiesLocalized()) {
            return true;
        }
        if (EntityModelCache.isBaseDomainObject(entity.getClass())) {
            if (EntityModelCache.isLinkedCompositeEntity(entity.getClass())) {
                if (((ILinkedCompositeEntity) entity).isInsert()) {
                    inserts.add((BaseDomainObject) entity);
                } else {
                    updates.add((BaseDomainObject) entity);
                }
            } else {
                inserts.add((BaseDomainObject) entity);
            }
        }
        return true;
    }

    @Override
    public boolean onLoad(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types) throws CallbackException {
        if (!Utilities.areEntitiesLocalized()) {
            return true;
        }
        if (entity instanceof BaseDomainObject) {
            updates.add((BaseDomainObject) entity);
        }
        return true;
    }

    @Override
    public Boolean isTransient(Object n) {
        Boolean result = false;
        boolean isNew = true;
        if (n instanceof DocumentModification //<--- siempre debe de SOLO insertar (coment by Richard)
                || n instanceof UserSettings //<--- unknown reason to be here
                || n instanceof Log //<--- unknown reason to be here
                || n instanceof IPersistableManualId
                || n instanceof PendingCount
                || n instanceof PendingRecord
                ) {
            return true;
        }
        if (n instanceof ILinkedCompositeEntity) { //<--- las llaves compuestas siempre deben ser regidas por esta propiedad(por default se inserta
            result = ((ILinkedCompositeEntity) n).isInsert();
        } else if (n instanceof IdentityUuidObject) {
            final IdentityUuidObject uuidEntity = (IdentityUuidObject)n;
            result = uuidEntity.identifuerValue() == null;
        } else if (n instanceof Persistable) {
            Persistable entity = (Persistable) n;
            Long l = entity.getId();
            if ((l == null) || (l == -1L)) {
                entity.setId(null);
                result = Boolean.TRUE;
            } else {
                isNew = false;
            }
        }
        if (n instanceof IAuditable) {
            IAuditable auditable = ((IAuditable) n);
            if (isNew) {
                auditable.setCreatedDate(null);
            }
            auditable.setLastModifiedDate(null);
            if (auditable.getCreatedBy() == null) {
                auditable.setCreatedBy(SecurityUtils.getLoggedUserId());
            }
            if (auditable.getLastModifiedBy() == null) {
                auditable.setLastModifiedBy(SecurityUtils.getLoggedUserId());
            }
        }
        return result;
    }

    @Override
    public void postFlush(Iterator iterator) {
        if (!Utilities.areEntitiesLocalized()) {
            return;
        }
        try {
            inserts.stream().map(BaseDomainObject.class::cast).forEach(LocalizedUtil::insertLanguages);
            updates.stream().map(BaseDomainObject.class::cast).forEach(LocalizedUtil::updateLanguages);
        } finally {
            inserts.clear();
            updates.clear();
        }
    }
}
