div.fileUpload div.uploadFile .Button.disabled,
div.fileUpload div.uploadFile .UnhoveredButton.disabled,
div.disabled-uploader div.uploadFile .noFilesAdded,
.previewEmpty .noFilesAdded,
.conditional-field-hidden,
.displayNone,
.hidden {
  display: none !important;
}
body.is-solo-true {
  overflow-x: hidden;
}
body {
  background-color: #e5e5e5 !important;
}
.hidden {
  height: 0 !important;
  clear: none !important;
  float: left !important;
  content: "";
}

.header {
  border-bottom: 0.195rem black solid;
}
.surveyPage > .header.header_count_0 {
  border-bottom: 0;
}
#header table td,
#header table {
  /* Se eliminan bordes dentro de la tabla del encabezado */
  border-collapse: collapse;
  padding: 1rem;
}
#header .row > h4 {
  margin: 0;
}

#header table img {
  /* Limitar el tamaño del logo */
  max-height: 6rem;
  padding: 3px;
  display: flex;
  width: auto !important;
}
#header br + br {
  /* <PERSON><PERSON>tar enters repetidos en el header */
  display: none;
}
#header table tr td:first-child {
  /* Agregado con la intención de hacer pequeña la celda del logo */
  width: 15%;
}
#header table tr td:first-child + td > * {
  /* Agregado con la intención de hacer pequeña la celda del logo */
  display: inline-block;
}
h3,
.row {
  cursor: context-menu;
}

.grid-floating-active .required:after,
.grid-floating-active .tableField .required:after,
.grid-floating-active .fieldMainTd .required:after {
  content: "" !important;
}

/* Dialogos */
.dijitDialog ul.content_area > li {
  max-width: calc(100% - 1rem);
}
.bnext .dijitDialogPaneContent {
  min-height: 100%;
}
.dijitDialog hr {
  display: none;
}
.dijitDialog .gridExpandable.grid-component-container table {
  width: 100%;
}

/* large-font*/
@media print, screen and (min-width: 64.1em) {
  h2 {
    font-size: 1.6rem;
  }
  h3 {
    font-size: 1.3rem;
    margin: 0px;
  }
  h3.subheader,
  h4 {
    font-size: 1rem;
  }
  #buttonContainerSpan a img {
    height: 20pt;
    width: 20pt;
    min-height: 20pt;
    min-width: 20pt;
    margin-right: 0.313rem;
  }
  #buttonContainerSpan h4 {
    margin: 0px;
    font-size: .9rem;
  }
  .surveyPage table tbody th,
  .surveyPage table tbody td {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .freeText table {
    margin-bottom: 0px;
  }
  p {
    line-height: 1;
  }
}
/* Banners */
#messages_layout {
  margin-left: auto;
  margin-right: auto;
  padding-right: 1rem;
  padding-left: 1rem;
  background: #fff;
  align-items: center;
  position: sticky;
  flex-wrap: wrap;
  justify-content: flex-end;
  z-index: 5;
  border-bottom: 1px solid #aaa;
}
#messages_layout div {
  position: relative;
  max-width: 75rem;
}
#messages_layout span i {
  font-size: 0.75rem;
}
#messages_layout div div {
  display: inline-block;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-weight: 400;
  font-size: 0.875rem;
  font-style: normal;
  letter-spacing: 0.015625rem;
  text-transform: none;
  line-height: 1.25rem;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 2rem;
}
#messages_layout div .material-icons {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding-bottom: 0.25rem;
}
#messages_layout div .button {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
#messages_layout div .button + button {
  right: 7rem;
}
#messages_layout #message_oldVersion_container {
  max-width: 100% !important;
}

#messages_layout #message_oldVersion {
  text-indent: 0px;
  margin-left: 2.5rem;
  max-width: calc(100% - 10rem) !important;
}

body.is-solo-true > #messages_layout div,
body.disabled_body > #messages_layout div {
  max-width: 77rem;
  margin: 0 auto;
}
body.disabled_body > #messages_layout div.message_preview {
  max-width: 60rem;
}
#messages_layout div i + div {
  margin-left: 2rem !important;
}

div.surveyPage > h2 {
  padding: 1rem;
  border-bottom: 1px solid #000000;
  margin: 0;
  background-color: aliceblue;
}

/* Free Text */
.header .freeText,
.freeText .fieldQuestionText {
  display: block;
}

/* Cuadro de texto */
#fields textarea {
  float: none;
  max-width: 100%;
  box-sizing: border-box !important;
}
.textField .answer > div {
  position: relative;
}
#almostEverything textarea.autoresize.contenteditable {
  height: 0px !important;
}
#almostEverything {
  padding-left: 0px;
  padding-right: 0px;
}
#buttonContainerSpan {
  padding-right: 1rem;
}
.textFieldArray .contenteditable {
  border: 0;
  border-style: none !important;
  border-width: 0 !important;
}
.textFieldArray > .questionContainer > .mainAnswer .fieldMainTable tbody tr td h4 {
  margin: 0.5rem !important;
}
.contenteditable {
  text-indent: 0.2rem;
  border-width: 1px !important;
  border-style: solid !important;
  background-color: #fff !important;
  padding-bottom: 1px;
  padding-top: 1px;
  padding-left: 1px;
  min-height: 1.375rem !important;
  border: #b3b3b3;
  word-break: break-word;
  overflow: hidden;
  box-sizing: border-box;
}
.grid-frame[disabled],
.contenteditable.disabled,
.contenteditable[disabled] {
  background-color: #f5f5f5 !important;
  color: #494949;
}
.visibleTextArea {
  display: block;
}
.contenteditable.visibleTextArea {
  white-space: pre-wrap;
  text-indent: 0;
  padding: 0.75rem;
  line-height: 1.35rem;
  height: auto !important;
  min-height: 3.125rem !important;
}
.grid-floating-active.grid-container[data-dbox-name="partial-progress-status-container"] {
  padding-top: 0;
  padding-bottom: 0;
}

.container-horizontal .item-horizontal,
.container-vertical .item-vertical {
  flex-flow: row;
  vertical-align: middle;
  align-items: center;
  border-bottom: 1px solid;
}

.questionContainer.exclusiveSelect-horizontal .container-horizontal .item-horizontal {
  flex: 1 0 21%;
  margin: 5px;
}
.questionContainer.exclusiveSelect-horizontal .container-horizontal .item-horizontal div:first-child {
  width: 15%;
}
.questionContainer.exclusiveSelect-horizontal .container-horizontal .item-horizontal div:last-child {
  width: 70%;
}

#almostEverything input[type="radio"],
#almostEverything input[type="checkbox"],
input[type="checkbox"].material-icons,
input[type="radio"].material-icons {
  visibility: hidden;
  font-size: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  text-align: center;
  -moz-appearance: initial !important;
}
.grid-options.grid-options-panel .display-column-chooser input[type="checkbox"],
.grid-options.grid-options-panel .display-column-chooser input[type="checkbox"] {
  display: inline-block;
}

input[type="checkbox"],
input[type="radio"] {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  box-sizing: content-box;
  display: flex;
  justify-content: center;
  align-items: center;
}
input[type="checkbox"].material-icons:before,
input[type="radio"].material-icons:before {
  position: relative;
  visibility: visible;
  vertical-align: middle;
  padding-top: 0.313rem;
  padding-bottom: 0.313rem;
}
input[type="checkbox"].material-icons:before {
  content: "check_box_outline_blank";
}
input[type="checkbox"]:checked.material-icons:before {
  content: "check_box";
}
input[type="radio"].material-icons:before {
  content: "radio_button_unchecked";
}
input[type="radio"]:checked.material-icons:before {
  content: "radio_button_checked";
}
.input-box-label {
  display: flex;
  max-width: 100%;
  align-items: center;
}
.input-box-label.label-style,
.input-box-label label {
  width: 100%;
  height: 100%;
  text-align: justify;
  vertical-align: middle;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
  font-weight: normal;
}

.fieldArray .selectOption {
  width: inherit; /* Corrección a combos empalmados */
}

/* Vertical */
#fields .vertical.row {
  border-bottom: 1px solid;
  padding: 12px 12px 12px 12px;
}
#fields .container-vertical .item-vertical h4 {
  margin: 0;
}
#fields .qr-image-fragment {
  display: flex;
  align-items: center;
  justify-content: center;
}
.geolocation div.vertical,
.handwrittenSignature div.vertical,
.stopwatchSelector div.vertical,
.catalogSelect div.vertical,
.exclusiveSelectYesNo div.vertical,
.multiSelect div.vertical,
.exclusiveSelect div.vertical {
  width: 100%;
  gap: 0.5rem;
}
.geolocation div.vertical,
.stopwatchSelector div.vertical {
  border-bottom-width: 0;
}
.exclusiveSelect .item-horizontal.horizontal.row.grid-x > * {
  width: auto;
}
.item-vertical.vertical .input-box-label label {
  margin-left: 1rem;
}
.fieldMainTd .item-vertical.vertical .input-box-label label {
  margin-left: 0rem;
}
.fieldMainTable .fieldMainTh h4 {
  min-width: 12.5rem;
}

/* Horizontal */

.item-horizontal.horizontal h4.input-box-label {
  padding-right: 1rem;
}

/* Cuadro de texto en preguntas con opciones */
.answer .boxText {
  padding-top: 0.25rem;
}
.answer .boxText .boxTextLabel {
  padding-left: 0.5rem;
}
.answer .boxText .boxTextAnswer {
  margin-bottom: 0px;
  margin-top: 0.5rem;
}
.answer .yesNoText .yesNoTextAnswer {
  margin-bottom: -0.5rem;
}
.answer .yesNoText .yesNoTextLabel {
  padding-top: 0.25rem;
  padding-left: 0.5rem;
  margin-top: 0.5rem;
}
.handwrittenSignature .cell.grid-container,
.stopwatchSelector .cell.grid-container,
.exclusiveSelect .cell.grid-container,
.multiSelect .cell.grid-container,
.catalogSelect .cell.grid-container,
.exclusiveSelectYesNo .cell.grid-container {
  padding: 0px;
}
/* Ligas en campo texto */
.help-icon {
  cursor: pointer;
  position: absolute;
  right: 1.188rem;
  transform: translate(0, -50%);
  top: 50%;
}
#fields .help-icon .material-icons {
  font-size: 1.25rem;
}

/* Tablas */
.fieldMainTable {
  width: 100%;
  border: 1px solid #c9c9c9;
  border-radius: 0.2rem;
  border-spacing: 0;
}
.exclusiveSelectArray td.fieldMainTd div,
.multiSelectArray td.fieldMainTd div {
  justify-content: center;
}
/* Fechas */
.answer .dateSelectorLabel {
  font-size: 1.125rem;
  padding-bottom: 0.5rem;
}
.dateSelector .include-time .answer .row.grid-x {
  gap: 1rem;
}
.dateSelector .include-time .answer .row.grid-x .large-5 {
  width: calc(50% - 0.5rem);
}
/* Menu */ /* Catalogo */
.answer .catalogSelect {
  font-size: 1rem;
  width: inherit;
  height: 3.125rem;
}
.answer .selectOption {
  font-size: 1rem;
  width: inherit;
  border-radius: .25rem;
  border: 1px solid;
  border-color: #b3b3b3;
  height: 3.125rem;
  text-indent: 0.5rem;
}

/* Textos autogenerados*/
span.auto-text {
  margin: 0px 0.5rem;
  display: inline-block;
  border-bottom: 2px solid #666;
  color: #666;
  font-weight: normal;
}
div.fieldArray th.fieldMainTh.noop,
div.exclusiveSelectArray th.fieldMainTh.noop,
div.tableField th.fieldMainTh.noop,
div.multiSelectArray th.fieldMainTh.noop {
  display: none;
}

.fieldMainTable .fieldMainTh {
  width: fit-content;
}
.fieldMainTable .fieldMainTh.matrix_item,
.fieldMainTable .fieldMainTd.matrix_item {
  border-right: 1px solid #c9c9c9;
  position: sticky;
  left: 0;
  background: white;
  z-index: 1;
  box-sizing: border-box;
  min-width: 15.25rem;
}
.fieldMainTable .fieldMainTh.matrix_item:after,
.fieldMainTable .fieldMainTd.matrix_item:after {
  content: "";
  position: absolute;
  right: -6px;
  top: 0;
  bottom: -1px;
  width: 5px;
  border-left: 1px solid #c9c9c9;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0) 100%);
}
.fieldMainTable .fieldMainTh.matrix_item:before,
.fieldMainTable .fieldMainTd.matrix_item:before {
  content: "";
  position: absolute;
  left: -6px;
  top: 0;
  bottom: -1px;
  width: 5px;
  border-right: 1px solid #c9c9c9;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.08) 100%);
}
.fieldMainTable .fieldMainTh.matrix_item > h4,
.fieldMainTable .fieldMainTd.matrix_item > h4 {
  text-align: center;
  min-width: 200px;
}
.fieldMainTable .fieldMainTh:not(.matrix_item),
.fieldMainTable .fieldMainTd:not(.matrix_item) {
  border-bottom: 1px solid #c9c9c9;
  min-width: 200px;
}
.fieldMainTable .fieldMainTd:not(.matrix_item):has(.error) {
  border: 1px solid #dc3545;
}
.fieldMainTable .fieldMainTh,
.fieldMainTable .fieldMainTd {
  word-wrap: break-word;
  word-break: break-word;
}
table.fieldMainTable th {
  vertical-align: bottom;
}

body.disabled_body input[type="text"].dijitReset.dijitArrowButtonInner,
.form .outter thead tr {
  background-color: white;
}
.answers table td,
.answers table th,
div.answer.mainAnswer td {
  border-right: 0px solid #f8f8f8;
  vertical-align: middle;
  padding: 1px;
  page-break-inside: avoid;
  page-break-after: avoid;
  page-break-before: avoid;
}
.matrix_item {
  padding-left: 0.313rem;
  vertical-align: middle;
}
/* Errores */
.form input.error,
.form textarea.error,
.form input.parcialError,
.form textarea.parcialError,
.form select.error,
.form canvas.error,
.form .contenteditable.error {
  border-color: #dc3545 !important;
  box-sizing: border-box;
}
.UnhoveredButton.error {
  border: #dc3545 solid 1px !important;
}
.multiSelect div.redBorderLeft,
.multiSelect div.redBorderUp,
.multiSelect div.redBorderRight,
.catalogSelect div.redBorderLeft,
.catalogSelect div.redBorderUp,
.catalogSelect div.redBorderRight,
.handwrittenSignature div.redBorderLeft,
.stopwatchSelector div.redBorderLeft,
.stopwatchSelector div.redBorderUp,
.stopwatchSelector div.redBorderRight,
.exclusiveSelect div.redBorderLeft,
.exclusiveSelect div.redBorderUp,
.exclusiveSelect div.redBorderRight {
  border: #dc3545 solid 1px !important;
}
.form div.redBorderRight,
.form ul.redBorderRight,
.form td.redBorderRight {
  border-right: #dc3545 solid 1px !important;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.form div.redBorderBottom,
.form ul.redBorderBottom,
.form td.redBorderBottom {
  border-bottom: #dc3545 solid 1px !important;
}
.form div.redBorderUp,
.form ul.redBorderUp,
.form td.redBorderUp {
  border-top: #dc3545 solid 1px !important;
}
.form div.redBorderLeft,
.form ul.redBorderLeft,
.form td.redBorderLeft {
  border-left: #dc3545 solid 1px !important;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.form ul.redBorderLeft,
.form ul.redBorderUp,
.form ul.redBorderBottom,
.form ul.redBorderRight,
.form div.redBorderLeft,
.form div.redBorderUp,
.form div.redBorderBottom,
.form div.redBorderRight {
  padding: 0.188rem;
}

/* Auditorias, Hallazgos y Actividades */
.survey .auditScoreTitle,
.survey .auditIncidentTitle,
.survey .scoreContainer .select-component {
  display: none;
}
.horizontal-small .actionBox.scoreContainer {
  flex: 1;
  flex-basis: 50%;
  width: auto;
}
.actionBox.scoreContainer {
  width: 18%;
  float: right;
  padding-left: 2%;
  transition: all 140ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
  line-height: 3.75rem;
  vertical-align: middle;
  background: transparent;
}

.score-container-inside {
  width: 80%;
}

.actionBox.scoreContainer {
  container-type: inline-size;
  container-name: buttons;
}

@container buttons (max-width: 150px) {
  span.material-icons {
    display: none;
  }
}

.actionBox.scoreContainer .raised-button {
  box-shadow: 0 1px 0.313rem 0 rgb(0 0 0 / 26%), 0 0.125rem 0.125rem 0 rgb(0 0 0 / 12%), 0 0.188rem 1px -0.125rem rgb(0 0 0 / 8%);
}
.actionBox.scoreContainer .grid-x {
  display: block;
  padding: 0;
}
.actionBox.scoreContainer .shrink {
  line-height: normal;
}
.audit .actionBox.scoreContainer .button-component,
.survey .actionBox.scoreContainer .button-component {
  margin-top: 0rem;
  padding-top: 0.75rem;
}
.audit .actionBox.scoreContainer .button-component span.material-icons + input[type="button"],
.survey .actionBox.scoreContainer .button-component span.material-icons + input[type="button"] {
  width: 100% !important;
  text-indent: 0rem;
}
.audit .actionBox.scoreContainer .button-component span.material-icons,
.survey .actionBox.scoreContainer .button-component span.material-icons {
  top: 1.35rem;
  left: 1.75rem;
  color: black !important;
}
span.material-icons {
  font-size: 1rem;
  text-indent: 0;
}
/* Auditorias */

.audit .auditScoreTitle,
.audit .auditIncidentTitle {
  width: 8.75rem;
  height: 2.5rem;
  margin: 0.3rem auto;
  background-color: white;
  font-size: 1.25rem;
  text-align: center;
  position: relative;
}

/* Archivos */
div.fileUpload div.fileSection {
  text-align: center;
}
div.fileUpload div.fileSection button {
  box-shadow: none;
  border: 1px solid #ddd;
  margin-bottom: 0.5rem;
}
div.fileUpload div.fileSection .fileDom {
  margin: auto;
  display: block !important;
}
div.fileUpload div.fileSection .Button.fileDom {
  display: inline !important;
  margin-top: 1rem;
  margin-right: 1rem;
}
div.fileUpload div.fileSection .Button.fileDom.displayNone {
  display: none !important;
}
div.fileUpload div.uploadFile {
  text-align: left;
}

/* Secciones y firmas  */
body .fillOutField.first-readable-field {
  width: calc(100% - 1.5rem);
  position: relative;
  z-index: 0;
}
.form.grid-container {
  margin: 2.5rem 2rem;
}
#fields .seccionContainer,
#fields .lastCommentContainer {
  position: relative;
}
.seccion .questionContainer {
  flex-grow: 1;
}
#fields .seccionContainer.authorize,
#fields .seccionContainer.reject,
#fields .seccionContainer.to-sign {
  border: 0.125rem dashed #808080;
  border-radius: 1.875rem;
  padding: 1rem;
  margin: 0.5rem 0.5rem 0 0.5rem;
}
#fields .seccionContainer.authorize > .seccion,
#fields .seccionContainer.reject > .seccion,
#fields .seccionContainer.to-sign > .seccion {
  display: inline-flex;
  flex-flow: column-reverse;
}
#fields .seccionContainer.to-review-adjustment {
  border-width: 0.75rem;
  border-style: solid;
  border-color: #fffcc5;
  border-radius: 1.25rem;
  box-shadow: 2px 2px 1px 0px #a5a5a5 inset, 0 0px 4px 0 #a5a5a5 inset, 0 1px 0.313rem 0 rgb(0 0 0 / 26%), 0 0.125rem 0.125rem 0 rgb(0 0 0 / 12%), 0 0.188rem 1px
    -0.125rem rgb(0 0 0 / 8%);
  padding: 1rem;
}
#fields .seccionContainer.to-sign .contenteditable.disabled,
#fields .seccionContainer.to-sign .contenteditable[disabled] {
  background-color: white !important;
  color: black;
}
.authorize,
.authorize + .redeableButtonsContainer div.containerComment,
.authorize + div.btnHistory {
  border-color: #008000 !important;
}
.reject,
.reject + .redeableButtonsContainer div.containerComment,
.reject + div.btnHistory {
  border-color: #dc3545 !important;
}
.readableField.button {
  display: flex;
  height: 2rem;
  cursor: pointer;
  top: 0.313rem;
  margin-right: 0.5rem;
  text-transform: none;
  box-shadow: 0 1px 0.313rem 0 rgb(0 0 0 / 26%), 0 0.125rem 0.125rem 0 rgb(0 0 0 / 12%), 0 0.188rem 1px -0.125rem rgb(0 0 0 / 8%);
}
.readableField.button .material-icons.igx-icon {
  margin-right: 0.5rem;
  pointer-events: none;
}
.readableField.to-authorize.button,
.authorize .authorizedLabel {
  color: rgba(40, 167, 69, 1) !important;
  background-color: #fff;
  border: solid 0px rgba(40, 167, 69, 1);
}
.readableField.to-reject.button,
.reject .rejectedLabel {
  color: rgba(220, 53, 69, 1) !important;
  background-color: #fff;
  border: solid 0px rgba(220, 53, 69, 1);
}
.readableField.to-authorize.button,
.authorize .authorizedLabel.button,
.readableField.to-reject.button,
.reject .rejectedLabel.button {
  border-width: 1px;
  display: flex;
}
.readableField.to-authorize.disabled.button,
.readableField.to-reject.disabled.button {
  color: gray !important;
}
.readableField.to-authorize.active.button .material-icons:before,
.readableField.to-reject.active.button .material-icons:before {
  content: "radio_button_checked";
}
.readableField.to-authorize.button .material-icons:before,
.readableField.to-reject.button .material-icons:before {
  content: "radio_button_unchecked";
}
.seccion .rejectedLabel,
.seccion .authorizedLabel {
  display: none;
  background-color: transparent;
  border: none;
  pointer-events: none;
  min-width: 12.25rem;
  align-items: flex-start;
  justify-content: end;
  margin: -2rem 0 1rem 0;
  align-self: center;
}
.seccion .rejectedLabel > span,
.seccion .authorizedLabel > span {
  display: flex;
  background: white;
  border-radius: 1rem;
  text-transform: uppercase;
  font-weight: 600;
  width: 100%;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid;
  align-items: center;
  padding: 0.15rem 0.75rem;
}
.seccion .rejectedLabel > span {
  background: rgba(220, 53, 69, 1) !important;
  color: white;
}
.seccion .authorizedLabel > span {
  background: rgba(40, 167, 69, 1) !important;
  color: white;
}
.seccion .authorizedLabel > span,
.seccion .rejectedLabel > span {
  border-radius: 1rem 1rem 4rem 4rem;
}
.reject .rejectedLabel,
.authorize .authorizedLabel {
  display: flex;
}
.redeableButtonsContainer {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  padding-bottom: 1rem;
}
.containerComment {
  background-color: #efefef;
  border-bottom: 0.125rem solid #808080;
  border-left: 0.125rem solid #808080;
  border-radius: 0px 0px 0.938rem 0.938rem;
  border-right: 0.125rem solid #808080;
  border-top: 0.125rem solid #808080;
  cursor: pointer;
  display: inline-block;
  max-width: 90%;
  min-width: 35%;
  overflow-y: hidden;
  overflow-x: hidden;
  resize: none;
  margin-top: -0.15rem;
  padding: 0.5rem 0.5rem 0.15rem 0.5rem;
  flex-grow: 1;
  margin-right: 20%;
  z-index: 1;
  box-shadow: 0 1px 0.313rem 0 rgb(0 0 0 / 26%), 0 0.125rem 0.125rem 0 rgb(0 0 0 / 12%), 0 0.188rem 1px -0.125rem rgb(0 0 0 / 8%);
}
div.lastCommentContainer {
  background-color: #ededed !important;
  cursor: pointer;
  display: flex;
  font-weight: bold;
  height: 2.5rem;
  top: 0px;
  width: calc(100% - 0.5rem);
  padding-top: 0;
  resize: none;
  text-align: center;
  z-index: 1;
  border-radius: 0.35rem;
  border: 1px solid;
  margin-bottom: 2.5rem;
}
div.lastCommentContainer h3 {
  text-align: left;
  margin: 0;
  width: 100%;
  background-color: #5699c5;
  padding: 0.5rem 0.35rem;
  color: white;
  max-width: calc(100% - 3.438rem);
}
textarea.commentable-field,
div.lastCommentField {
  background-color: transparent;
  border-width: 1px;
  display: inline-block;
  height: 2.188rem !important;
  overflow-x: hidden;
  overflow-y: auto;
  resize: none;
  width: 100%;
  border-radius: 0px;
  -webkit-box-shadow: inset 1px 1px 1px 1px rgb(163 163 163);
  -moz-box-shadow: inset 1px 1px 1px 1px rgba(163, 163, 163, 1);
  box-shadow: inset 1px 1px 0px 0px #d3d3d3;
}
div.lastCommentField {
  opacity: 0.9;
  border-width: 0px;
  box-shadow: inset 0px 0px 0px 0px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: normal;
  width: 100%;
}
div.btnHistory {
  background-color: #ededed;
  color: #777;
  font-size: 0.75rem;
  font-weight: bold;
  font-style: normal;
  line-height: 3.4rem;
  width: 3.438rem;
  text-decoration: none;
  text-align: center;
  z-index: 1;
  cursor: pointer;
  height: 100%;
  margin: 0 0.5rem;
}
div.btnHistory img {
  height: 1.5rem;
  margin-top: 0.05rem;
}
.signatureName {
  margin: 0 auto;
}
.signature.outter,
.signature .questionContainer,
.signature .questionContainer div.mainAnswer {
  text-align: center;
}
.signature .main_text .itemTitle {
  display: flex;
  padding: 0.313rem;
  margin: 0.125rem;
  text-align: center;
  font-weight: bold;
  text-decoration: none;
  position: relative;
  justify-content: center;
  align-items: center;
}

.unavailable-field .date-widget {
  pointer-events: none;
}

/* Large */
@media screen and (max-width: 63.9375em) {
  .tableField .contenteditable,
  .boxText .contenteditable,
  .yesNoText .contenteditable,
  .textField .contenteditable {
    min-height: 100pt;
    text-align: center;
  }
  div.containerComment {
    border-style: solid;
    border-radius: 0.938rem;
    border-width: 0.4rem;
    width: calc(100% - 1rem);
    margin-left: 0px;
    max-width: 53.75rem;
    height: auto;
    position: relative;
  }
  div#bloqueBoton .button {
    width: calc(100% - 2rem) !important;
    margin-right: 0px;
    margin-left: 0px !important;
    float: initial;
  }
  #actionsBtn,
  #printBtn {
    display: none;
  }
  div.dijitDialogPaneContent .gridExpandable.grid-component-container {
    margin-top: 0;
    padding-bottom: 0;
  }
  div.lastCommentField {
    text-align: justify;
    word-break: break-all;
    white-space: normal;
    line-height: 2.188rem;
  }
  div.lastCommentContainer,
  .readableField,
  body div.fillOutField.readable-field,
  body div.fillOutField.last-readable-field,
  body div.fillOutField.first-readable-field {
    border-color: gray;
    width: calc(100% - 2rem);
  }
  div.btnHistory {
    height: 2.813rem;
  }
  .readableField.button .material-icons.igx-icon {
    margin-right: 0rem;
  }
  div.btnHistory img {
    margin-top: 0.25rem;
  }
  div.lastCommentContainer {
    width: calc(100%);
    border-right: none;
    position: relative;
    height: auto;
    padding-right: 0px;
    max-width: 100%;
  }
  .dijitDialog,
  div.dijitDialog {
    max-width: 100% !important;
    max-height: 100% !important;
    top: 10% !important;
    position: fixed !important;
    left: 0 !important;
    display: inline-block;
    font-size: 4rem;
    background-color: #0000 !important;
    border: 0px !important;
    box-shadow: 0px 0px 0px 0px !important;
  }
  .audit .actionBox.scoreContainer .button-component span.material-icons,
  .survey .actionBox.scoreContainer .button-component span.material-icons {
    left: 0.5rem;
  }
}
/* Medium */
@media screen and (max-width: 48em) {
  body div.fillOutField.readable-field {
    padding-right: 0.625rem;
    padding-left: 0.625rem;
  }
  .surveyPage .freeText table {
    max-width: 100% !important;
    width: 100% !important;
  }
  .actionBox.scoreContainer {
    width: 100% !important;
    float: none;
    margin-left: 0rem !important;
    margin-top: 1rem;
    order: 3;
  }
  .audit.questionContainer {
    display: flex;
    flex-direction: column;
  }
  #fields textarea,
  .contenteditable,
  .visibleTextArea {
    min-height: 3.125rem !important;
  }
  #messages_layout div .button {
    right: initial;
  }
  div.btnHistory {
    width: 2.813rem;
    top: -1rem;
    right: -0.2rem;
    border-left-style: dashed;
    border-top-left-radius: 0.938rem;
    border-bottom-left-radius: 0.938rem;
  }
  div.lastCommentField {
    height: auto !important;
  }
  .readableField.button {
    min-width: 6.5rem;
  }
  .readableField.button .material-icons.igx-icon {
    margin-right: 0rem;
  }
}
/* Tablet */
@media screen and (max-width: 39.9375em) {
  #header table tr td:first-child + td > h3 {
    font-size: 1.5rem;
    text-align: center;
    width: 100%;
    margin: 1rem 0;
  }
  #header table tr td:first-child + td > h3 br {
    display: none;
  }
  #header .freeText td > h3 {
    display: inline-block;
    margin: 0;
  }
  #fields .section-name-description span.seccion.fieldQuestionText {
    font-size: 1.5rem;
    text-align: center;
  }
  .textFieldArray .fieldMainTable .fieldMainTh.matrix_item > h4,
  .textFieldArray .fieldMainTable .fieldMainTd.matrix_item > h4,
  .textFieldArray .fieldMainTable .fieldMainTh.matrix_item,
  .textFieldArray .fieldMainTable .fieldMainTd.matrix_item {
    min-width: 6rem;
    max-width: 6rem;
  }
  .textFieldArray > .questionContainer > .mainAnswer .fieldMainTable tbody tr td h4 {
    margin: 0 !important;
  }
  .textFieldArray .fieldMainTable .fieldMainTh.matrix_item > h4,
  .textFieldArray .fieldMainTable .fieldMainTd.matrix_item > h4 {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  .searchDialog:not(.displayNone) {
    width: 100%;
  }
  .bnext .dijitDialog.fixedTop .dijitDialogPaneContent .grid-filled-container {
    box-shadow: none;
  }
  #fields {
    padding-top: 0.625rem;
  }
  .answer.mainAnswer {
    margin-left: 0rem !important;
    margin-bottom: 1rem;
  }
  .seccion .questionContainer,
  .signature .cell,
  .subheader {
    margin-top: 0rem !important;
    margin-bottom: 0rem;
  }
  .seccion .answer.mainAnswer {
    margin-bottom: 0.8rem;
  }
  .surveyPage .seccionContainer:first-child {
    margin-top: 1.625rem;
  }
  .surveyPage .seccionContainer {
    padding: 0;
  }
  .surveyPage .form.grid-container {
    padding: 2.5rem 1.187rem !important;
  }
  .seccion .rejectedLabel,
  .seccion .authorizedLabel {
    font-size: 0px !important;
    right: -1.563rem !important;
    top: 0px;
    margin: 0;
    min-width: 3rem;
    margin-top: -1rem;
  }
  .seccion .rejectedLabel > .material-icons,
  .seccion .authorizedLabel .material-icons {
    margin-right: -0.5rem;
  }
  .readableField,
  body div.fillOutField.readable-field,
  body div.fillOutField.last-readable-field,
  body div.fillOutField.first-readable-field {
    width: calc(100% - 1rem);
  }
  /* Dialogos */
  .bnext .dijitDialog .dijitDialogTitleBar[data-dojo-attach-point="titleBar"] {
    top: 0px !important;
    display: flex !important;
    position: relative !important;
  }
  .bnext .dijitDialogPaneContent,
  .bnext div.dijitDialogPaneContent {
    top: 0px !important;
    display: block !important;
    position: relative !important;
  }
  .bnext .dijitDialog .dijitDialogTitleBar[data-dojo-attach-point="titleBar"] {
    height: auto !important;
    text-align: center !important;
    width: 100% !important;
    padding-top: 0.5rem;
    padding-bottom: 0;
  }
  .bnext .dijitDialog {
    top: 0.5% !important;
    height: auto !important;
    overflow-y: auto;
  }
  .bnext #dBoxDialog,
  .bnext .dijitDialog {
    left: 0.2rem !important;
  }
  .bnext :not(.addUserDialog) div.dijitDialogPaneContent {
    height: inherit !important;
    width: 100% !important;
  }
  .dijitDialog ul.content_area,
  .dijitDialog .grid-component-container tr span {
    font-size: 1rem !important;
    line-height: 1.5 !important;
  }
  .bnext #dBoxDialog .dijitDialogPaneContent pre {
    padding: 1rem !important;
    font-size: 1rem !important;
    line-height: normal !important;
  }
  .dijitDialog .dijitDialogPaneContent div.fRight {
    padding-bottom: 0.5rem !important;
    width: calc(100% - 1rem);
    text-align: center !important;
  }
  .bnext #dBoxDialog .dijitDialogPaneContent .fRight .Button {
    width: 14rem !important;
    height: 2rem !important;
    font-size: 1rem !important;
    position: initial;
  }
  @media (hover: none) and (pointer: coarse) {
    #almostEverything {
      padding-bottom: 3.75rem;
    }
  }
  /* Comentarios */
  div.containerComment h3 {
    margin: 0.8rem 0 0.5rem 0;
  }
  div.lastCommentContainer {
    min-height: 2.25rem;
  }
  div.lastCommentField {
    font-size: 1.125rem;
    line-height: 1.313rem;
    word-break: normal;
    width: 96%;
  }
  div.btnHistory {
    height: 2.25rem;
    width: 2.25rem;
    right: -0.5rem;
    top: -1rem;
  }
  div.btnHistory img {
    height: 2.25rem;
    margin-top: 0px;
  }
  div.lastCommentContainer,
  div.containerComment,
  div.btnHistory,
  .readableField,
  body div.fillOutField.readable-field,
  body div.fillOutField.last-readable-field,
  body div.fillOutField.first-readable-field {
    border-width: 0.2rem;
    min-height: 1.5rem;
  }
  .readableField.button {
    min-width: 5.5rem;
    width: 49% !important;
    float: none;
    margin-right: 0;
    min-height: 2.25rem;
  }
  .multiSelectArray [columnsSize="large"] input[type="checkbox"]:before,
  .exclusiveSelectArray [columnsSize="large"] input[type="radio"]:before {
    font-size: 3rem;
  }
  body.isCHROME td.fieldMainTd div,
  .fieldMainTable .fieldMainTh h4 {
    min-width: auto !important;
    margin: auto !important;
  }
  .fieldMainTable .fieldMainTh h4 {
    transform: rotate(180deg);
    writing-mode: vertical-lr;
    text-indent: 1rem;
  }
  .fieldArray .fieldMainTable th.fieldMainTh h4 {
    text-indent: 0rem;
  }
  .fieldArray td.fieldMainTd div .input-box-label label {
    width: 100%;
    margin-left: 0;
    text-align: center;
  }
  .fieldArray .exclusiveSelectYesNo .input-box-label,
  .fieldArray .multiSelect .input-box-label,
  .fieldArray .multiSelect .input-box-label .label-style,
  .fieldArray .exclusiveSelect .input-box-label {
    display: block !important;
    margin: auto !important;
    padding: 0;
  }
  body.disabled_body > #messages_layout #message_preview {
    max-width: 66%;
  }
  .redeableButtonsContainer {
    margin: -2.5rem 0;
    display: flex;
    flex-direction: column;
  }
  .readableActionButtonsContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
  }
  .signature .itemTitle.fieldQuestionText {
    flex-direction: column;
    gap: 1rem;
  }
  .form.grid-container {
    margin: 0;
  }
  #messages_layout > div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  #messages_layout div .button {
    position: relative;
    transform: none;
  }
}
/* Small */
@media screen and (max-width: 26.5625em) {
  .fieldMainTable .contenteditable {
    text-indent: 0rem;
    line-height: inherit;
    min-height: 3.125rem !important;
  }
  .subtitle_question > div {
    padding-left: 0rem;
  }
  .input-box-label {
    padding-left: 0.5rem;
  }
  .readableField.button {
    width: 49% !important;
    float: none;
    margin-right: 0;
    min-height: 2.25rem;
  }
  .redeableButtonsContainer {
    margin-bottom: 0.5rem;
  }
  body div.fillOutField.readable-field {
    padding-left: 0.25rem !important;
    width: calc(100% - 0.5rem) !important;
  }
  .answer .dateSelectorLabel,
  .answer .selectOption {
    font-size: 1rem;
  }
  .fieldArray .container-horizontal,
  .fieldArray .container-vertical {
    max-width: 100%;
  }
  .fieldArray .container-horizontal .item-horizontal,
  .fieldArray .container-vertical .item-vertical {
    width: calc(100% - 0.2rem) !important;
  }
  .surveyPage .freeText td img {
    width: 100% !important;
    height: auto !important;
    margin: auto !important;
  }
  .surveyPage .freeText td,
  .surveyPage .freeText td img:not([data-field-key]) {
    display: block;
  }
  body.disabled_body > #messages_layout #message_preview {
    max-width: 100%;
  }
  body.is-solo-true > #messages_layout div,
  body.disabled_body > #messages_layout div {
    display: inline-block;
  }
  div.lastCommentContainer,
  div.containerComment,
  div.btnHistory,
  .readableField,
  body div.fillOutField.readable-field,
  body div.fillOutField.last-readable-field,
  body div.fillOutField.first-readable-field {
    border-width: 0.1rem;
  }
  .redeableButtonsContainer {
    display: block;
    padding: 0 1rem;
  }
  .readableField.button .material-icons.igx-icon {
    margin-right: 0.5rem;
  }
  .redeableButtonsContainer {
    margin: -2.5rem 0;
    display: flex;
    flex-direction: column;
  }
  .readableActionButtonsContainer {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    flex-flow: row;
    margin-bottom: 3.5rem;
  }
  .signature .itemTitle.fieldQuestionText {
    flex-direction: column;
    gap: 1rem;
  }
  .form.grid-container {
    margin: 0;
  }
  #messages_layout > div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  #messages_layout div .button {
    position: relative;
    transform: none;
  }
  .bnext #dBoxDialog .dijitDialogPaneContent .fRight .Button {
    width: 100% !important;
  }
}

/* ----------- medium ----------- */
/* MaxPro */
@media screen and (max-width: 40em) {
  #header table tr td:first-child {
    /* Agregado con la intención de hacer pequeña la celda del logo, para movil evtia que se descuadre */
    width: auto !important;
  }
  #header table img {
    /* Agregado con la intención de hacer pequeña la celda del logo, para movil evita ques e descuadre */
    width: auto !important;
  }
  .surveyPage .freeText td img {
    width: 100% !important;
    height: auto !important;
    margin: auto !important;
  }
  .surveyPage .freeText td,
  .surveyPage .freeText td img:not([data-field-key]) {
    display: block !important;
  }

  .form.grid-container {
    margin: 0;
  }
}
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  select,
  textarea,
  input {
    font-size: 1rem;
  }
}
@media screen and (-webkit-min-device-pixel-ratio: 3) {
  input[type="checkbox"].material-icons:before,
  input[type="radio"].material-icons:before {
    right: 0rem;
  }
}
/* Portrait and Landscape */
@media only screen and (min-device-width: 23.438rem) and (max-device-width: 57.875rem) and (-webkit-min-device-pixel-ratio: 3) {
  input[type="checkbox"].material-icons:before,
  input[type="radio"].material-icons:before {
  }
  .bnext #dBoxDialog .dijitDialogPaneContent .fRight .Button {
    margin-right: 0 !important;
  }
  .dijitDialog .dijitDialogPaneContent div.fRight,
  .bnext .dijitDialog .dijitDialogTitleBar[data-dojo-attach-point="titleBar"] {
    text-align: center !important;
  }
  .fieldMainTable .fieldMainTh h4 {
    text-orientation: sideways-right;
    min-height: 5rem;
  }
}

/* Print */
@media print {
  body.printCss input[type="checkbox"].material-icons:before,
  body.printCss input[type="radio"].material-icons:before {
    top: 0rem;
  }
  body.printCss .questionContainer {
    page-break-inside: avoid;
  }
  body.printCss div.answer.mainAnswer tr,
  body.printCss div.answer.mainAnswer td.fieldMainTd {
    page-break-inside: avoid;
    page-break-after: auto;
    page-break-before: auto;
  }
  .textarea-component.date-widget {
    border: none;
  }
  .buttonsDiv,
  .mainAnswer div.dijitArrowButtonContainer,
  .mainAnswer div.dijitValidationContainer {
    display: none;
  }
  .mainAnswer input.dijitInputInner {
    border-color: initial !important;
  }
  span.auto-text {
    border-radius: initial !important;
    border: initial !important;
    box-shadow: inherit !important;
  }
  body {
    background: initial;
    padding: 0px;
    margin: 0px;
  }
  #sectionToDefineUser {
    display: none;
  }
  .pageBreak {
    page-break-after: always;
  }
  #fields {
    margin-top: 0.625rem;
    margin-bottom: 0.625rem;
    margin-left: 0px;
    margin-right: 0px;
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 0.313rem;
    padding-bottom: 0.313rem;
  }
  .main-grid-container,
  body #almostEverything {
    margin: 0px !important;
    padding: 0px !important;
  }
  img {
    margin-top: 1px !important;
  }
  pre {
    border: 0px !important;
  }
  .hideToPrint {
    visibility: hidden;
    display: none !important;
  }
  .uploadFile,
  .downloadFileButton,
  .viewFileButton {
    display: none;
  }
  .titleToPrint {
    font-weight: bold;
    font-family: cursive;
    border-bottom: 0.25rem solid #aa0000;
  }
  .displayNone.showOnPrint,
  .showToPrint {
    display: inherit !important;
  }
  body.printCss {
    width: 100%;
    border: 0;
    background: white !important;
  }
  body.printCss .printCss-block {
    display: block !important;
  }
  body.printCss table.grid-component-container,
  body.printCss table.grid-component-container td,
  body.printCss table.grid-component-container th,
  body.printCss table.grid-component-container tr {
    border: 1px solid black !important;
    background: white !important;
  }
  body.printCss select,
  body.printCss input {
    border: 0 !important;
    font-weight: bold;
  }

  body.printCss #mainDiv {
    background: white !important;
    border: 0 !important;
  }

  body.printCss .printCss-border {
    border: 1px solid #b3b3b3 !important;
    border-radius: 0.25rem;
    width: auto !important;
    height: auto !important;
    min-height: 1.125rem !important;
    line-height: 1.15rem;
    padding: 0.95rem 0.70rem 0.95rem 0.70rem !important;
    white-space: pre-wrap;
  }

  body.printCss .textFieldArray .printCss-border {
    border: 0 !important;
  }

  body.printCss select {
    width: 100% !important;
    margin-top: -0.625rem;
  }

  body.printCss table.grid-component-container th {
    display: none !important;
  }
  body.printCss table.grid-component-container .csstype_13 {
    display: none !important;
  }
  body.printCss table.grid-component-container .csstype_7 {
    display: none !important;
  }
  body.printCss .content_title {
    display: none !important;
  }
  body.printCss .eyesClassIdentifier {
    display: none !important;
  }
  body.printCss input[type="file"] {
    display: none !important;
  }
  body.printCss .printCss-displayNone {
    display: none !important;
  }
  body.printCss #footerComponent {
    display: none !important;
  }
  body.printCss .cke,
  body.printCss #mainTitle,
  body.printCss .actionButtons,
  body.printCss select::-ms-expand {
    display: none !important;
  }
  body.printCss select {
    text-indent: 1px;
    text-overflow: "";
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none; /* If you want to remove the border as well */
    background: none;
  }
  body.printCss .content_area {
    background: white !important;
    border: 0 !important;
  }

  body.printCss li > table {
    width: 100%;
  }

  body.printCss textarea:not(.commentable-field) {
    display: none;
  }
  body.printCss .fileUploaderLabel,
  body.printCss .dijitEditor {
    display: none;
  }
  body.printCss .dojoEditorTd,
  body.printCss div.textField {
    page-break-inside: avoid;
  }
  body.printCss .scoreContainer {
    min-height: 1rem;
    box-shadow: none;
  }
  body.printCss .textarea-component.date-widget input {
    box-shadow: 0px 0.125rem 0px -1px rgba(0, 0, 0, 0.54) !important;
  }
  body.printCss .deleteFileButton,
  body.printCss .viewFileButton,
  body.printCss .downloadFileButton,
  body.printCss .button-audit {
    display: none;
  }
  body.printCss h4.textarea.displayNone.showOnPrint {
    display: inherit !important;
  }
  body.printCss .seccionContainer.authorize,
  body.printCss .seccionContainer.reject,
  body.printCss .seccionContainer.to-sign {
    break-inside: avoid;
  }
  body.printCss .reject .rejectedLabel.button,
  body.printCss .authorize .authorizedLabel.button,
  body.printCss .redeableButtonsContainer .readableField.button {
    display: flex !important;
    visibility: visible;
  }
  body.printCss .actionBox.scoreContainer,
  body.printCss .actionBox.scoreContainer .cell.shrink,
  body.printCss .grid-container.grid-floating-active .actionBox.scoreContainer .select-component select,
  body.printCss .grid-container.grid-floating-active .actionBox.scoreContainer .select-component::before {
    display: none !important;
  }
  body.printCss .grid-container.grid-floating-active .actionBox.scoreContainer .select-component .printCss-block {
    margin-left: 0rem !important;
    margin-top: 1rem !important;
    line-height: 1rem;
  }
  h2 {
    font-size: 1.6rem;
  }
  h3 {
    font-size: 1.3rem;
    margin: 0px;
  }
  h3.subheader,
  h4 {
    font-size: 1rem;
  }
  #almostEverything input[type="checkbox"],
  #almostEverything input[type="radio"],
  #buttonContainerSpan a img {
    height: 20pt;
    width: 20pt;
    margin-right: 0.313rem;
    min-height: 20pt;
    min-width: 20pt;
  }
  #buttonContainerSpan h4 {
    margin: 0px;
    font-size: .9rem;
  }
  .surveyPage table tbody th,
  .surveyPage table tbody td {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .freeText table {
    margin-bottom: 0px;
  }
  p {
    line-height: 1;
  }
  div.Waiting {
    background-image: url("data:image/svg+xml;base64,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");
  }
  .surveyPage td {
    height: 2rem !important;
  }
  #buttonContainerSpan a {
    margin: 0.313rem;
  }
  .textFieldArray .contenteditable,
  .tableField .contenteditable,
  .boxText .contenteditable,
  .yesNoText .contenteditable,
  .textField .contenteditable {
    min-height: 20pt;
  }
  #buttonContainerSpan a img,
  #almostEverything input[type="checkbox"],
  #almostEverything input[type="radio"] {
    width: 30pt;
    height: 30pt;
  }
}

img[data-field-key] {
  display: none;
}
.grid-export-config-dialog ul.searchFields,
.grid-export-config-dialog ul.searchFields li.searchField {
  float: none;
  padding: 0;
  margin-top: 0;
  min-height: 5rem;
}

.grid-export-config-dialog .searchFields .searchField label.search-input-with-value {
  position: initial;
}

.grid-export-config-dialog .dijitDialogPaneContent .grid-x.grid-padding-x .buttons-align-right {
  display: flex;
  justify-content: center;
}

.text-ellipsis-table {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: break-spaces;
  text-align: center;
}
.qrImage {
  padding: 10px;
  border: 2px solid #000;
  display: inline-block;
  background: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 0, 0, 0.1) inset;
  border-radius: 5px;
}
input.editable-title {
  border: none;
  color: black;
  font-family: "TypoPRO Titillium Text", sans-serif;
  border-bottom: 0.15rem solid;
  padding: 0.25rem;
  margin-top: 0.83rem;
  margin-left: 1rem;
  width: calc(100% - 1.85rem);
  font-size: 1.6rem;
}
.surveyPage .form > .seccionContainer:not(:first-child) {
  margin-top: 2rem;
}
