require([
  'loader',
  'i18n-util',
  'dojo/dom',
  'dojo/dom-class',
  'dojo/Deferred',
  'dojo/_base/lang',
  'dojo/json',
  'dojo/parser',
  'bnext/canDeactivate',
  'bnext/_base/object',
  'bnext/administrator/surveys/SurveyCaptureSave',
  'bnext/administrator/surveys/SurveyCaptureBusy',
  'bnext/administrator/surveys/SurveyCaptureGeolocation',
  'bnext/_base/url-util',
  'dojo/domReady!'
], (
  loader,
  I18nUtil,
  dom,
  domClass,
  Deferred,
  lang,
  dojoJson,
  parser,
  deactivate,
  objectUtil,
  SurveyCaptureSave,
  SurveyCaptureBusy,
  SurveyCaptureGeolocation,
  urlUtil
) => {
  function doEverything(i18n) {
    const w = window;
    let timeRunning = 0;
    const archived = dom.byId('archived');
    const outstandingArchived = dom.byId('outstandingArchived');
    const archivedEnabled = archived && archived.value === 'true' && outstandingArchived && outstandingArchived.value === 'true';
    const $GET = urlUtil.getUrlVars();
    w.seeActions = () => {
      require(['bnext/administrator/surveys/SurveyCaptureFindings'], (SurveyCaptureFindings) => {
        SurveyCaptureFindings.openActionsDialog();
        loader.hideLoader();
      });
    };
    function initActualActionsDialog() {
      if (dom.byId('actualActionsDialog')) {
        require(['bnext/administrator/surveys/SurveyCaptureFindings'], (SurveyCaptureFindings) => {
          SurveyCaptureFindings.initActualActionsDialog();
        });
      }
    }
    function initActualActivitiesDialog() {
      if (dom.byId('actualActivitiesDialog')) {
        require(['bnext/administrator/surveys/SurveyCaptureActivities'], (SurveyCaptureActivities) => {
          SurveyCaptureActivities.initActualActivitiesDialog();
        });
      }
    }
    parser.parse().then(() => {
      initActualActionsDialog();
      initActualActivitiesDialog();
    });
    w.seeActivities = () => {
      loader.showLoader().then(() => {
        require(['bnext/administrator/surveys/SurveyCaptureActivities'], (SurveyCaptureActivities) => {
          SurveyCaptureActivities.seeActivities();
          loader.hideLoader();
        });
      });
    };
    w.acceptResults = function acceptResults() {
      loader.showLoader().then(() => {
        require(['bnext/administrator/surveys/SurveyCaptureAudits'], (SurveyCaptureAudits) => {
          SurveyCaptureAudits.acceptResults();
        });
      });
    };

    w.showActionsSeedDialog = (fieldId) => {
      loader.showLoader().then(() => {
        require(['bnext/administrator/surveys/SurveyCaptureFindings'], (SurveyCaptureFindings) => {
          SurveyCaptureFindings.showActionsSeedDialog(fieldId);
        });
      });
    };
    w.hideActualActionsDialog = () => {
      if (dom.byId('actualActionsDialog')) {
        require(['bnext/administrator/surveys/SurveyCaptureFindings'], (SurveyCaptureFindings) => {
          SurveyCaptureFindings.hideActualActionsDialog();
        });
      }
    };
    w.hideActualActivitiesDialog = () => {
      if (dom.byId('actualActivitiesDialog')) {
        require(['bnext/administrator/surveys/SurveyCaptureActivities'], (SurveyCaptureActivities) => {
          SurveyCaptureActivities.hideActualActivitiesDialog();
        });
      }
    };
    w.showNewActivityDialog = showNewActivityDialog;
    w.changeAuditScore = changeAuditScore;
    w.onClickRadioInput = onClickRadioInput;
    w.onClickCheckboxInput = onClickCheckboxInput;
    w.onClickArrayRadioInput = onClickArrayRadioInput;
    w.onClickArrayCheckboxInput = onClickArrayCheckboxInput;
    w.showClauseDialog = showClauseDialog;
    w.onSelectChange = (select, fieldId) => {
      require([
        'core',
        'bnext/administrator/surveys/ConditionalHandler',
        'bnext/administrator/surveys/ConditionalExpirationFieldsValidator',
        'bnext/administrator/surveys/SelectedWeightedFieldsValidator',
        'bnext/administrator/surveys/FormWeightingHandler'
      ], (core, ConditionalHandler, ConditionalExpirationFieldsValidator, SelectedWeightedFieldsValidator, FormWeightingHandler) => {
        (async () => {
          try {
            await ConditionalHandler.onFieldValueChange(select, fieldId, 'single');
            ConditionalExpirationFieldsValidator.onFieldOptionValueChange(select, fieldId, 'single');
            await FormWeightingHandler.onWeightingValueChange(fieldId);
            SelectedWeightedFieldsValidator.executeSelectedWeightedFieldsEvent(fieldId, select.value);
          } catch (e) {
            core.error(e);
          }
        })();
      });
    };
    function changeAuditScore(domAuditScore, fieldId) {
      require(['bnext/administrator/surveys/SurveyCaptureAudits'], (SurveyCaptureAudits) => {
        SurveyCaptureAudits.changeAuditScore(domAuditScore, fieldId);
      });
    }
    function showNewActivityDialog(fieldId) {
      loader.showLoader().then(() => {
        require(['bnext/administrator/surveys/SurveyCaptureActivities'], (SurveyCaptureActivities) => {
          SurveyCaptureActivities.showNewActivityDialog(fieldId);
        });
      });
    }
    function showClauseDialog(question, clause) {
      require(['bnext/administrator/surveys/SurveyCaptureActivities'], (SurveyCaptureActivities) => {
        SurveyCaptureActivities.showClauseDialog(question, clause);
      });
    }

    function validateSurvey(lastIndexToValidate) {
      const def = new Deferred();
      require(['bnext/administrator/surveys/SurveyCaptureValidation', 'bnext/administrator/surveys/SurveyCaptureUtils'], (
        SurveyCaptureValidation,
        SurveyCaptureUtils
      ) => {
        SurveyCaptureValidation.validateSurvey(lastIndexToValidate).then((result) => {
          def.resolve(result);
        }, SurveyCaptureUtils.failureRequest);
      });
      return def.promise;
    }
    function onClickInput(input, fieldId, typeSelection) {
      require([
        'core',
        'bnext/administrator/surveys/SurveyCaptureDomUtils',
        'bnext/administrator/surveys/SurveyCaptureActivities',
        'bnext/administrator/surveys/ConditionalHandler',
        'bnext/administrator/surveys/ConditionalExpirationFieldsValidator',
        'bnext/administrator/surveys/SelectedWeightedFieldsValidator',
        'bnext/administrator/surveys/FormWeightingHandler'
      ], (
        core,
        SurveyCaptureDomUtils,
        SurveyCaptureActivities,
        ConditionalHandler,
        ConditionalExpirationFieldsValidator,
        SelectedWeightedFieldsValidator,
        FormWeightingHandler
      ) => {
        (async () => {
          try {
            SurveyCaptureActivities.onClickActivityInput(input, fieldId);
            await ConditionalHandler.onFieldValueChange(input, fieldId, typeSelection);
            ConditionalExpirationFieldsValidator.onFieldOptionValueChange(input, fieldId, typeSelection);
            SurveyCaptureDomUtils.toogleOtherOption(input);
            await FormWeightingHandler.onWeightingValueChange(fieldId);
            SelectedWeightedFieldsValidator.executeSelectedWeightedFieldsEvent(fieldId, input.value);
          } catch (e) {
            core.error(e);
          }
        })();
      });
    }
    function onClickArrayInput(input) {
      require(['bnext/administrator/surveys/FormWeightingHandler'], (FormWeightingHandler) => {
        (async () => {
          try {
            const fieldId = input.getAttribute('data-field-id');
            const headerId = input.getAttribute('data-header-id');
            await FormWeightingHandler.onWeightingValueChange(fieldId, headerId);
          } catch (e) {
            core.error(e);
          }
        })();
      });
    }
    function onClickRadioInput(input, fieldId) {
      onClickInput(input, fieldId, 'single');
    }
    function onClickCheckboxInput(input, fieldId) {
      onClickInput(input, fieldId, 'multiple');
    }
    function onClickArrayRadioInput(input) {
      onClickArrayInput(input);
    }
    function onClickArrayCheckboxInput(input) {
      onClickArrayInput(input);
    }
    const task = $GET.task;
    const requestMode = $GET.requestMode;
    let initialData = null;
    if ((task && task.toLowerCase() !== 'preview') || (requestMode && requestMode.toLowerCase() === 'requestor')) {
      window.addEventListener('onSurveyCaptureLoaded', () => {
        initialData = getCurrentData();
        function beforeUnloadEvent() {
          return changeData(initialData, getCurrentData());
        }
        function canDeactivate() {
          return new Promise((resolve) => {
            const currentData = getCurrentData();
            if (initialData === null || typeof initialData === 'undefined' || currentData === null || typeof currentData === 'undefined') {
              console.error('No se pudo obtener los datos para verificar cambios... Saliendo de la ventana sin dialogo.');
              resolve(true);
            }
            if (changeData(initialData, currentData)) {
              resolve(false);
            } else if (dojoJson.stringify(initialData) === dojoJson.stringify(currentData)) {
              resolve(true);
            } else {
              console.log('Algo, salio mal al evaluar la salida segura... Saliendo de la ventana sin dialogo.');
              resolve(true);
            }
          });
        }
        function successDeactivateCallback() {
          return new Promise((success) => {
            deactivate.reset();
            success();
          });
        }
        deactivate.setup(beforeUnloadEvent, canDeactivate, successDeactivateCallback);
      });
      function changeData(initialData, currentData) {
        return !objectUtil.equals(initialData, currentData);
      }
      function getCurrentData() {
        return SurveyCaptureSave.collectData(4)[0].preguntasRespondidas.map((m) => ({
          respuesta: m.respuesta
        }));
      }
    }
    function formRequestAction(
      formRequestId,
      actionType
      /*
       * 'authorizeFormReopenRequest'
       * | 'rejectFormReopenRequest'
       * | 'adjustmentAuthorizerOk'
       * | 'adjustmentAuthorizerReject'
       * | 'adjustmentVerifyOk'
       * | 'adjustmentVerifyReject'
       * | 'approveFormCancelRequest'
       * */
    ) {
      const def = new Deferred();
      require(['core'], (core) => {
        const inputMinLimit = 10;
        core
          .inputDialog(
            i18n[`${actionType}Message`].replace('{inputMinLimit}', inputMinLimit),
            i18n.formRequestInputMinLimit.replace('{inputMinLimit}', inputMinLimit),
            i18n.formRequestMandatoryAndLimit.replace('{inputMinLimit}', inputMinLimit),
            [i18n.accept, i18n.cancel],
            i18n[`${actionType}Title`],
            {
              inputLimitText: i18n.formRequestEmptyPlaceholder,
              inputMaxLimit: 255,
              inputMinLimit: inputMinLimit
            }
          )
          .then((dialog) => {
            dialog.event('clickBtn1', (data) => {
              core.showLoader().then(() => {
                const commentValue = data.detail.inputValue || '';
                require(['bnext/callMethod', 'bnext/administrator/surveys/SurveyCaptureMessages', 'bnext/angularNavigator'], (
                  callMethod,
                  SurveyCaptureMessages,
                  angularNavigator
                ) => {
                  callMethod({
                    url: 'Request.Survey.Capture.action',
                    method: actionType,
                    params: [
                      {
                        value: formRequestId,
                        text: commentValue // <-- Razón de autorización de reapertura
                      }
                    ]
                  }).then(
                    (r) => {
                      const saveHandle = r.body;
                      switch (saveHandle.logicResponse) {
                        case 2: // VERIFY_DEPARTMENT_MANAGER
                          core.dialog(i18n.successReopenVerifyDepartmentManager, i18n.pendings, core.i18n.accept_label).then(() => {
                            loader.showLoader().then(() => {
                              angularNavigator.navigate('pendings');
                            });
                          }, def.resolve);
                          break;
                        case 3: // VERIFY_APPROVED
                          core.dialog(i18n.successApprovedFormRequestVerifyApproved, i18n.pendings, core.i18n.accept_label).then(() => {
                            loader.showLoader().then(() => {
                              angularNavigator.navigate('pendings');
                            });
                          }, def.resolve);
                          break;
                        case 4: // VERIFY_REPEATED_DATA
                          core.dialog(i18n.successReopenVerifyRepeatedData, i18n.pendings, core.i18n.accept_label).then(() => {
                            loader.showLoader().then(() => {
                              angularNavigator.navigate('pendings');
                            });
                          }, def.resolve);
                          break;
                        case 5: // VERIFY_FINAL
                        case 6: // AUTHORIZED
                        case 7: // REJECTED
                          if (actionType === 'approveFormCancelRequest' || actionType === 'authorizeFormReopenRequest') {
                            core.dialog(i18n[actionType], i18n.pendings, core.i18n.accept_label).then(
                              () => {
                                loader.showLoader().then(() => {
                                  angularNavigator.navigate('pendings');
                                });
                              },
                              () => {
                                window.parent.location.reload();
                                def.resolve();
                              }
                            );
                          }
                          break;
                      }
                      SurveyCaptureMessages.hideBanner('message_reopen_container');
                      SurveyCaptureMessages.hideBanner('message_cancel_container');
                      SurveyCaptureMessages.hideBanner('message_adjustment_authorization_container');
                      core.hideLoader();
                    },
                    (e) => {
                      const message = e.message || e.name || String(e) || 'No fue posible enviar la solicitud';
                      core.error(message);
                      core.hideLoader();
                      def.reject({ error: message });
                    }
                  );
                });
              });
            });
            dialog.event('clickBtn2', () => {
              dialog.hide();
              def.reject({ error: 'cancel' });
            });
          });
      });
      return def.promise;
    }
    w.hideActionsSeedDialog = () => {
      loader.showLoader().then(() => {
        require(['bnext/administrator/surveys/SurveyCaptureFindings'], (SurveyCaptureFindings) => {
          SurveyCaptureFindings.hideActionsSeedDialog();
        });
      });
    };
    w.end = function end(msg) {
      dom.byId('id').value = `O${dom.byId('id').value}`;
      if (msg === '<parcial>') {
      } else if (msg === '<terminar>') {
        const win = window;
        if (win.top.window.vueApp) {
          win.top.window.vueApp.hideFrame();
        } else {
          require(['bnext/_base/refererPage'], (refererPage) => {
            refererPage.back(i18n.saveSuccess, i18n.accept);
          });
        }
      }
    };
    w.collectData = (estatus) => {
      const def = new Deferred();
      const data = SurveyCaptureSave.collectData(estatus);
      def.resolve(data);
      return def.promise;
    };
    w.guardadoParcial = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      if (dom.byId('justLoad').value === 'true') {
        return;
      }
      SurveyCaptureSave.guardadoParcial();
      initialData = getCurrentData();
    };
    w.cancelFillForm = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      if (dom.byId('justLoad').value === 'true' && dom.byId('cancelAny').value === 'false') {
        return;
      }
      const activityId = dom.byId('activityId') ? dom.byId('activityId').value : null;
      if (activityId) {
        require(['core', 'bnext/angularNavigator', 'bnext/_base/url-util', 'bnext/angularActivity'], (core, angularNavigator, urlUtil, angularActivity) => {
          angularActivity.bannerAttendActivity({ activityId: activityId, cancelActivity: true }, 'formularie').then(
            (result) => {
              if (result) {
                core.dialog(i18n.messageExit).then(() => {
                  angularNavigator.navigate('pendings');
                });
              }
            },
            () => {}
          );
        });
      } else {
        require(['bnext/administrator/solicitudes/cancelFillForm'], (cancelFillFormAction) => {
          const requestId = dom.byId('requestId').value;
          const outstandingSurveyId = dom.byId('outstandingid').value;
          const cancelationCurrentUserName = dom.byId('cancelationCurrentUserName').value;
          const cancelationCurrentUserId = dom.byId('cancelationCurrentUserId').value;
          cancelFillFormAction(requestId, outstandingSurveyId, true, cancelationCurrentUserName, cancelationCurrentUserId)
            .then(() => {
              require(['core', 'bnext/angularNavigator'], (core, angularNavigator) => {
                angularNavigator.navigate('pendings');
              });
            })
            .catch(() => {});
        });
      }
    };
    w.guardadoParcialAutomatico = () => {
      SurveyCaptureGeolocation.findGeolocation().then(
        (position) => {
          if (position?.coords) {
            const currentLatitude = dom.byId('current_latitude');
            const currentLongitude = dom.byId('current_longitude');
            if (currentLatitude && currentLongitude) {
              currentLatitude.value = position.coords.latitude;
              currentLongitude.value = position.coords.longitude;
            }
          }
          return this.guardadoParcialAutomaticoHandler();
        },
        (error) => {
          return this.guardadoParcialAutomaticoHandler();
        }
      );
    };
    w.guardadoParcialAutomaticoHandler = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        console.warn('El formulario esta realizando cálculos de los campos. ' + 'El guardado de respuestas automático está desactivado temporalmente.');
        return;
      }
      if (dom.byId('surveyStatus') && +dom.byId('surveyStatus').value === 9) {
        return;
      }
      if (dom.byId('justLoad').value === 'true') {
        console.warn('El guardado de respuestas automático está desactivado en el modo de consulta.');
        return;
      }
      if (w.misDatos.busy) {
        console.warn(i18n.alreadySaving);
        return;
      }
      if (dom.byId('signFormBtn') && !domClass.contains(dom.byId('signFormBtn'), 'displayNone')) {
        console.warn('El guardado de respuestas automático está desactivado al firmar.');
        return;
      }
      timeRunning = 0;
      if (w.misDatos) {
        loader.showLoader(i18n.autoSavingLoader).then(() => {
          console.log('autoguardando...');
          return w.collectData(3).then(
            (data) => {
              w.misDatos.setData(data);
              if (w.misDatos.busy) {
                return;
              }
              w.misDatos.busy = true;
              w.misDatos.saveData('none'); //'Guardado automatico de respuestas...');
            },
            (err) => {
              require(['bnext/administrator/surveys/SurveyCaptureUtils'], (SurveyCaptureUtils) => {
                SurveyCaptureUtils.failureRequest(err);
              });
            }
          );
        });
      } else {
        fracasoMsg(i18n.errorInitialize);
      }
    };
    w.autoSaveMiliseconds = () => {
      try {
        let t = +dom.byId('autoSaveTime').value;
        t = t * 60000;
        return t;
      } catch (e) {
        return 600000; //<--- 10 minutos
      }
    };
    w.ultimateSave = (signatureAsSaveBehavior) => {
      return loader.showLoader(i18n.savingLoader).then(() => {
        return SurveyCaptureGeolocation.findGeolocation().then(
          (position) => {
            if (position?.coords) {
              const currentLatitude = dom.byId('current_latitude');
              const currentLongitude = dom.byId('current_longitude');
              if (currentLatitude && currentLongitude) {
                currentLatitude.value = position.coords.latitude;
                currentLongitude.value = position.coords.longitude;
              }
            }
            return this.ultimateSaveHandler(signatureAsSaveBehavior);
          },
          (error) => {
            return this.ultimateSaveHandler(signatureAsSaveBehavior);
          }
        );
      });
    };
    w.ultimateSaveHandler = (signatureAsSaveBehavior) => {
      if (archivedEnabled) {
        loader.hideLoader();
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        loader.hideLoader();
        SurveyCaptureBusy.showBusyError();
        return;
      }
      if (window.deactivate) {
        window.deactivate.reset();
      }
      if (w.misDatos.busy) {
        loader.hideLoader();
        console.warn(i18n.alreadySaving);
        require(['core'], (core) => {
          core.warn(i18n.alreadySaving);
          return core.resolvedPromise();
        });
      }
      const def = new Deferred();
      require(['bnext/administrator/surveys/SurveyCaptureAutoFields'], (SurveyCaptureAutoFields) => {
        SurveyCaptureAutoFields.closeAutoFields().then(
          () => {
            ultimateSaveAction(signatureAsSaveBehavior).then(
              (result) => {
                def.resolve(result);
              },
              (e) => {
                loader.hideLoader();
                def.reject(e);
              }
            );
          },
          (e) => {
            loader.hideLoader();
            def.reject(e);
          }
        );
      });
      return def.promise;
    };
    function ultimateSaveAction(signatureAsSaveBehavior) {
      return loader.showLoader(i18n.savingLoader).then(() =>
        w.collectData(5).then(
          (data) => {
            if (signatureAsSaveBehavior) {
              data[0].signatureAsSaveBehavior = true;
            }
            w.misDatos.setData(data);
            if (w.misDatos.busy) {
              loader.hideLoader();
              return;
            }
            w.misDatos.busy = true;
            return w.misDatos.saveData();
          },
          (err) => {
            require(['bnext/administrator/surveys/SurveyCaptureUtils'], (SurveyCaptureUtils) => {
              loader.hideLoader();
              SurveyCaptureUtils.failureRequest(err);
            });
          }
        )
      );
    }
    w.ultimateInvalidSurvey = () => {
      require(['core', 'dojo/query'], (core, query) => {
        loader.hideLoader();
        if (query('.parcialError', 'form').length > 0) {
          core.error(i18n.answerAllParcial);
        } else if (query('.required-field-error', 'form').length > 0) {
          core.error(i18n.answerAll);
        } else if (query('.error', 'form').length === query('.UnhoveredButton.error', 'form').length) {
          core.error(i18n.answerFiles);
        } else {
          core.error(i18n.answerAll);
        }
      });
    };
    const messages = {
      pool: `${i18n.pool + i18n.complementPool}<br/>${i18n.confirm}`,
      audit: `${i18n.audit + i18n.complementAudit}<br/>${i18n.confirm}`,
      request: i18n.request + i18n.confirm
    };
    const messagesConfirm = {
      pool: i18n.savingMessages,
      audit: i18n.savingMessages,
      request: i18n.saving
    };
    w.getCurrentAuthorizationPoolIndex = () => {
      let currentAutorizationPoolIndex = false;
      if (dom.byId('currentAutorizationPoolIndex') && dom.byId('surveyType').value === 'request') {
        currentAutorizationPoolIndex = +dom.byId('currentAutorizationPoolIndex').value || 1;
      }
      return currentAutorizationPoolIndex;
    };
    w.updateGeolocation = () => {
      const currentAutorizationPoolIndex = w.getCurrentAuthorizationPoolIndex();
      const queryStr = `.fillOutField[data-fill-authorization-pool-index=${currentAutorizationPoolIndex}].geolocation`;
      require(['dojo/query'], (query) => {
        const inputs = query(queryStr, 'form');
        for (const input of inputs) {
          const id = input.id;
          const labelLatitude = dom.byId(`${id}_latitude`);
          const labelLongitude = dom.byId(`${id}_longitude`);
          const availableField = domClass.contains(input, 'available-field');
          if (availableField) {
            // Verify if field available, so the position is updated
            const latitude = dom.byId('current_latitude').value;
            const longitude = dom.byId('current_longitude').value;
            labelLatitude.innerHTML = latitude;
            labelLongitude.innerHTML = longitude;
          }
        }
      });
    };
    w.guardadoTotal = () => {
      SurveyCaptureGeolocation.findGeolocation().then(
        (position) => {
          if (position?.coords) {
            const currentLatitude = dom.byId('current_latitude');
            const currentLongitude = dom.byId('current_longitude');
            if (currentLatitude && currentLongitude) {
              currentLatitude.value = position.coords.latitude;
              currentLongitude.value = position.coords.longitude;
            }
            w.updateGeolocation();
          }
          return this.guardadoTotalHandler();
        },
        (error) => {
          console.error('Error al obtener la geolocalización', error);
          return this.guardadoTotalHandler();
        }
      );
    };
    w.guardadoTotalHandler = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      if (dom.byId('justLoad').value === 'true') {
        return;
      }
      const currentAutorizationPoolIndex = w.getCurrentAuthorizationPoolIndex();
      validateSurvey(currentAutorizationPoolIndex || 'all').then(
        (result) => {
          if (!result) {
            w.ultimateInvalidSurvey();
            return;
          }
          const d = dom.byId('editedOutstandingidTitleId')?.value.trim();
          if (typeof d !== 'undefined' && !d) {
            require(['core'], (core) => {
              core.error(i18n.noTitleFound);
            });
            return;
          }
          require(['bnext/administrator/surveys/SurveyCaptureLoad'], (SurveyCaptureLoad) => {
            if (SurveyCaptureLoad.needsToAssignOwner(currentAutorizationPoolIndex)) {
              SurveyCaptureLoad.initializeUserSelector(currentAutorizationPoolIndex).then(() => {
                SurveyCaptureLoad.nextUserSelector.onUserSelected = (userSelected) => {
                  require(['core'], (core) => {
                    if (!core.isNull(userSelected)) {
                      const userName = userSelected.userDescription;
                      const fieldType = SurveyCaptureLoad.getFieldType(currentAutorizationPoolIndex);
                      let message;
                      if (fieldType === 'signature') {
                        message = i18n.signature_confirmNextUser.replace(':userName', userName);
                      } else {
                        message = i18n.seccion_confirmNextUser.replace(':userName', userName);
                      }
                      core.dialog(message, core.i18n.Validate.yes, core.i18n.Validate.no).then(
                        () => {
                          SurveyCaptureLoad.nextUserSelector.hide();
                          showLoader(messagesConfirm[dom.byId('surveyType').value] || i18n.saving);
                          dom.byId('justLoad').value = 'true';
                          w.ultimateSave();
                        },
                        () => {}
                      );
                    }
                  });
                };
                SurveyCaptureLoad.nextUserSelector.show();
              });
              return;
            }
            notice({
              message: messages[dom.byId('surveyType').value] || i18n.confirmSaving,
              btn1Action: () => {
                showLoader(messagesConfirm[dom.byId('surveyType').value] || i18n.saving);
                dom.byId('justLoad').value = 'true';
                w.ultimateSave();
              }
            });
          });
        },
        (e) => {
          dom.byId('justLoad').value = 'false';
          require(['bnext/administrator/surveys/SurveyCaptureUtils'], (SurveyCaptureUtils) => {
            SurveyCaptureUtils.failureRequest(e);
          });
        }
      );
    };
    w.saveHandleCallbackCapture = (resultado) => {
      if (!resultado) {
        loader.hideLoader();
        return;
      }
      if (resultado.jsonEntityData?.signatureAsSaveBehavior) {
        //do nothing
        return;
      }
      console.log(`LOCAL: saveHandleCallback ... resultado.operationEstatus : ${resultado.operationEstatus}`);
      // biome-ignore lint/security/noGlobalEval: TODO: Fix this
      const obj = eval(resultado.savedObjName);
      if (+resultado.operationEstatus === 0) {
        try {
          const json = resultado.extraJson;
          const originalFailureFn = obj.getHookUpdateFailure();
          let failureFn = originalFailureFn;
          if (json !== '0') {
            if (failureFn) {
              failureFn = lang.replace(originalFailureFn, {
                extraJson: json
              });
            }

            if (obj) {
              obj.setHookUpdateFailure(failureFn);
            }
          }
          if (obj) {
            obj.runHookUpdateFailure();
            obj.setHookUpdateFailure(originalFailureFn);
          }
        } catch (e) {
          console.log(e);
        }
      } else if (+resultado.operationEstatus === 1) {
        console.log(`saveHandle OK! ${resultado.successMessage} - savedId : ${resultado.savedId}`);
        if (dom.byId('id') && resultado.savedId && !resultado.file) {
          dom.byId('id').value = resultado.savedId;
        }
        console.log(`obj:${obj}`);
        try {
          const json = resultado.extraJson;
          if (json !== '0') {
            console.log('go json...');
            let successFn = obj.getHookUpdateSuccess();
            if (successFn) {
              successFn = lang.replace(successFn, {
                extraJson: json
              });
            }
            const data = dojoJson.parse(json);
            if (dom.byId('surveyStatus') && data.estatus) {
              dom.byId('surveyStatus').value = +data.estatus;
            }
            if (obj) {
              obj.setHookUpdateSuccess(successFn);
            }
          }
          if (obj) {
            obj.runHookUpdateSuccess();
          }
        } catch (e) {
          console.info(e);
        }
      } else {
        fracasoMsg(i18n.errorOperation);
        if (obj) {
          obj.runHookUpdateFailure();
        }
      }
      //si es un archivo
      if (resultado.file) {
        throw new Error('Legacy code, fileGet jsp is gone');
      }
      loader.hideLoader();
    };

    w.errorHandle = (json) => {
      console.log(' >> errorHandle ... ');
      let data = null;
      try {
        data = dojoJson.parse(json);
      } catch (e) {}
      if (!data) {
        return;
      }
      switch (data.estatus) {
        case 5:
          w.end(i18n.warningClosed);
          break;
        case 6:
          w.end(i18n.warningCanceled);
          break;
        case 7:
          w.end(i18n.warningSurveyInterrupted);
          break;
        case -1:
          w.end(i18n.warningServer);
          break;
      }
    };
    w.extraJsonHandle = (json) => {
      let data;
      try {
        data = dojoJson.parse(json);
      } catch (e) {
        console.error(` >> extraJsonHandle ... ${json}`);
        require(['core'], (core) => {
          core.dialog(core.i18n.Validate.fail.replace('%error%', json), i18n.accept);
        });
        return;
      }
      if (data.link) {
        require(['core', 'bnext/angularNavigator'], (core, angularNavigator) => {
          core.dialog(i18n.sameResponsible, i18n.continue, i18n.pendings).then(
            () => {
              if (w.keepAliveConf) {
                w.keepAliveConf.stoppedThread = true;
              }
              loader.showLoader().then(() => {
                angularNavigator.navigateLegacy(data.link);
              });
            },
            () => {
              if (w.keepAliveConf) {
                w.keepAliveConf.stoppedThread = true;
              }
              loader.showLoader().then(() => {
                angularNavigator.navigate('pendings');
              });
            }
          );
        });
      } else if (data.signatureAsSaveBehavior) {
        loader.showLoader(i18n.savingSuccessfully);
      } else {
        if (data && +data.estatus === 4) {
          require(['bnext/angularNotice'], (angularNotice) => {
            angularNotice.notice(i18n.saveSuccess);
          });
        } else if (data && +data.estatus === 5) {
          if (w.keepAliveConf) {
            w.keepAliveConf.stoppedThread = true;
          }
          require(['bnext/_base/refererPage'], (refererPage) => {
            refererPage.back(i18n.saveSuccess, i18n.accept);
          });
        } else {
          console.log(data);
          if (!timeRunning) {
            startAutoSaveTask();
          } else {
            stopAutoSaveTask();
            startAutoSaveTask();
          }
          loader.hideLoader();
        }
      }
    };
    w.startAutoSaveTask = startAutoSaveTask;
    w.stopAutoSaveTask = stopAutoSaveTask;
    function startAutoSaveTask() {
      timeRunning = setTimeout(w.guardadoParcialAutomatico, w.autoSaveMiliseconds());
    }
    function stopAutoSaveTask() {
      require(['bnext/survey/_util/dateSelectorUtils'], (dateSelectorUtils) => {
        dateSelectorUtils.stopDateMinValidations();
      });
      require(['bnext/survey/_util/StopwatchUtils'], (StopwatchUtils) => {
        StopwatchUtils.stopRefresh();
      });
      clearTimeout(timeRunning);
    }
    w.getMultiSelectArrayAnswer = (respuestas, columna, fila, attName) => {
      console.log(`Buscando fila:${fila} columna:${columna}`);
      let respuesta = null;
      if (!lang.isArray(respuestas)) {
        return respuesta;
      }
      let found = false;
      let j = 0;
      while (!found && j < respuestas.length) {
        if (lang.getObject(attName, false, respuestas[j]).id === columna && respuestas[j].opcion.id === fila) {
          found = true;
          respuesta = respuestas[j];
        } else {
          j++;
        }
      }
      console.log(`Entrado : ${found}`);
      return respuesta;
    };
    w.startRequestBlocking = () => {
      require(['bnext/administrator/solicitudes/requestBlocker'], (requestBlocker) => {
        const requestId = requestBlocker.getRequestId();
        if (requestId !== null) {
          requestBlocker.pingToSetBusy({ id: requestId }, true);
        }
      });
    };
    function requestFormReopen() {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      const reopenRequestDisabled = dom.byId('reopenRequestDisabled') && dom.byId('reopenRequestDisabled').value === 'true';
      const reopenCurrentIsInactive = dom.byId('reopenCurrentUserStatus') && dom.byId('reopenCurrentUserStatus').value !== '1';
      const reopenCurrentUserName = dom.byId('reopenCurrentUserName').value;
      const adjustmentRequestorUserName = dom.byId('adjustmentRequestorUserName').value;
      const outstandingSurveysId = dom.byId('outstandingSurveysId').value;
      if (reopenCurrentIsInactive) {
        w.callMethod({
          url: 'Request.Survey.Capture.action',
          method: 'requestFormAuthorizers',
          params: [outstandingSurveysId]
        }).then((result) => {
          console.log('>> ', result);
          require(['core'], (core) => {
            core
              .comboDialog(
                `${i18n.reopenRequestNotAvailable
                  .replace('{reopenCurrentUserName}', reopenCurrentUserName)
                  .replace('{adjustmentRequestorUserName}', adjustmentRequestorUserName)}`,
                result.body || [{ text: 'Admin', value: 1 }],
                i18n.reopenRequestNotAvailableLabel,
                i18n.reopenRequestNotAvailableHint,
                [i18n.accept, i18n.cancel],
                i18n.reopenRequestNotAvailableTitle
              )
              .then((dialog) => {
                dialog.event('clickBtn1', (r) => {
                  core.showLoader().then(() => {
                    w.callMethod({
                      url: 'Request.Survey.Capture.action',
                      method: 'refreshFormReopenAuthorizer',
                      params: [outstandingSurveysId, r.detail.selectValue]
                    }).then((result) => {
                      switch (result.statusCodeValue) {
                        case 200: // OK
                          dom.byId('reopenCurrentUserName').value = r.detail.selectText;
                          dom.byId('reopenCurrentUserStatus').value = '1';
                          dialog.hide().then(() => {
                            setTimeout(() => {
                              w.requestFormReopen();
                            });
                          });
                          break;
                        default:
                          core.error(i18n.reopenRequestNotAvailableError);
                          break;
                      }
                      core.hideLoader();
                    });
                  });
                });
                dialog.event('clickBtn2', () => {
                  dialog.hide();
                });
              });
          });
        });
        return;
      }
      if (reopenRequestDisabled) {
        require(['core'], (core) => {
          core.dialog(`${i18n.reopenRequestAlreadyCreatedReload.replace('{reopenCurrentUserName}', reopenCurrentUserName)}`);
        });
        return;
      }
      require(['core'], (core) => {
        const inputMinLimit = 10;
        core
          .inputDialog(
            i18n.reopenRequestMessage.replace('{reopenCurrentUserName}', dom.byId('reopenCurrentUserName').value),
            i18n.formRequestInputMinLimit.replace('{inputMinLimit}', inputMinLimit),
            i18n.formRequestMandatoryAndLimit.replace('{inputMinLimit}', inputMinLimit),
            [i18n.accept, i18n.cancel],
            i18n.reopenRequestTitle,
            {
              inputLimitText: i18n.formRequestEmptyPlaceholder,
              inputMaxLimit: 255,
              inputMinLimit: inputMinLimit
            }
          )
          .then((dialog) => {
            dialog.event('clickBtn1', (data) => {
              core.showLoader().then(() => {
                const commentValue = data.detail.inputValue || '';
                w.callMethod({
                  url: 'Request.Survey.Capture.action',
                  method: 'requestFormReopen',
                  params: [
                    {
                      outstandingSurveysId: dom.byId('outstandingSurveysId').value,
                      reason: commentValue // <-- Razón de la solicitud
                    }
                  ]
                }).then(
                  (r) => {
                    switch (r.statusCodeValue) {
                      case 200: // OK
                        core.dialog(i18n.reopenRequestSuccess);
                        break;
                      case 429: // TOO_MANY_REQUESTS
                        require(['bnext/gridComponentUtil'], (gcUtil) => {
                          core.dialog(
                            i18n.reopenRequestAlreadyCreated
                              .replace('{createdByName}', r.body.createdByName)
                              .replace('{createdDate}', gcUtil.parseDateStrToPattern(r.body.createdDate, 'dd/MM/yyyy'))
                          );
                        });
                        break;
                    }
                    dom.byId('reopenRequestDisabled').value = 'true';
                    core.hideLoader();
                  },
                  (e) => {
                    core.error(e.message || e.name || String(e) || 'No fue posible enviar la solicitud');
                    core.hideLoader();
                  }
                );
              });
            });
            dialog.event('clickBtn2', () => {
              dialog.hide();
            });
          });
      });
    }
    w.requestFormReopen = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      requestFormReopen();
    };
    w.reopenAuthorizeAction = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeReopenFormRequestId').value, 'authorizeFormReopenRequest');
    };
    w.rejectAuthorizeAction = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeReopenFormRequestId').value, 'rejectFormReopenRequest');
    };
    w.adjustmentAuthorizerOk = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeAdjustmentFormRequestId').value, 'adjustmentAuthorizerOk');
    };
    w.adjustmentAuthorizerReject = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeAdjustmentFormRequestId').value, 'adjustmentAuthorizerReject');
    };
    w.adjustmentVerifyOk = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeAdjustmentFormRequestId').value, 'adjustmentVerifyOk');
    };
    w.adjustmentVerifyReject = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeAdjustmentFormRequestId').value, 'adjustmentVerifyReject');
    };
    w.rejectCancelAction = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeCancelationFormRequestId').value, 'rejectFormCancelRequest');
    };
    w.approveCancelAction = () => {
      if (archivedEnabled) {
        return;
      }
      if (SurveyCaptureBusy.hasBusyFields()) {
        SurveyCaptureBusy.showBusyError();
        return;
      }
      formRequestAction(dom.byId('activeCancelationFormRequestId').value, 'approveFormCancelRequest').then((result) => {
        require(['dojo/query'], (query) => {
          query('.change-user-buttons-section, #cancelFillFormBtn').addClass('displayNone'); //test
        });
      });
    };
  }
  I18nUtil.setLang('bnext/administrator/surveys/nls/surveycapture.savehandle').then(doEverything);
});
