/* 
    Document   : surveys
    Created on : May 28, 2012, 7:20:40 PM
    Author     : <PERSON>
    Description:
        Purpose of the stylesheet follows.
*/
.classic-yellow {
    background-color : #F9F7BA;
}
.css_push {
    text-align: center;
    min-width: 6.25rem;
}
.css_push input {
    cursor: pointer;
}
.css_signature {
  width: auto!important;
  display: inline-block; 
  padding: 0.313rem;
  border: solid 1px #DDD;
  margin: 0.125rem;
  background: #EEE;
  text-align: center;
  cursor: pointer;
  -moz-border-radius: 0.188rem;
  -webkit-border-radius: 0.188rem;
  border-radius: 0.188rem;
  font-weight: bold;
  border: 1px solid black;
  font-weight: bold;
  text-decoration: none;
  position: relative;
  cursor: pointer;
}
.weighted-fields-to-select,
.summation-question,
.conditional-question {
    outline-color: lightgray;
    outline-style: auto;
    outline-width: 0.125rem;
}
.css_signature .uiObjectItems {
    display : none;
}
.css_tableField .arrayTable td.first_td_tr {
  display: none;
}
.tableFieldCell {
}
#tabContainer {
    position: static;
}
.tabContainerParent {
    z-index: 2;
    position: fixed;
}
.dijitTabInner.dijitTabContent.dijitTab {
    border: 0px;
    line-height: 3rem;
    padding: 0px;
    background: red;
    margin: 0px;
    vertical-align: top;
}
.dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter {
    left: 0px;
    top: 0px;
    position: absolute;
    z-index: 12;
}
.bnext .dijitTab .tabLabel {
    min-height: 0.75rem;
    display: inline-block;
    font-family: 'TypoPRO Titillium Text', sans-serif;
    font-size: 0.875rem;
    letter-spacing: 0.046875rem;
    text-transform: uppercase;
    font-weight: 600;
    padding: 0px 1rem;
    background-color: #fff;
    position: static!important;
}
.dijitTabInner.dijitTabContent.dijitTab,
.bnext .dijitTabContainerTop-tabs,
.bnext .dijitTabContainerBottom-tabs,
div#tabContainer_tablist,
.dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter,
.bnext .dijitTab .tabLabel {
    height: 3.188rem!important;
}
.dijitTabPaneWrapper.dijitTabContainerTop-container.dijitAlignCenter {
    top: 3.438rem!important;
}
.bnext .dijitTab.dijitTabChecked  .tabLabel {
    height: 3.063rem!important;
    display: block;
}
.dijitTabContainerTop-dijitContentPane {
    border-radius: 0px 0px 0.938rem 0.938rem;
    position: static!important;
    height: 100%!important;
    overflow: hidden;
    background-color: transparent!important;
}
.dijitTabPaneWrapper.dijitTabContainerTop-container.dijitAlignCenter {
    left: 0px;
    height: auto!important;
    border-left: none;
    border-right: none;
    background-color: transparent;
    border: 0;
}
.enabled-hierarchy-container-class,
.catalog-external-hiearchy-missing-query-configuration {
    float: none;
}
.catalog-external-hiearchy-missing-query-configuration {
    margin-bottom: 0px;   
}
.enabled-hierarchy-label {
    padding: 1rem;
}
.dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter,
div#tabContainer_tablist,
.ghostMarginAtTop,
.dijitTabContainerTopChildWrapper .dijitContentPane.dijitTabContainerTop-child,
.dijitTabPaneWrapper.dijitTabContainerTop-container.dijitAlignCenter,
.dijitTabContainerTop-dijitContentPane {
    width: calc(75vw - 2rem) !important;
}

.dijitTabPaneWrapper.dijitTabContainerTop-container.dijitAlignCenter:focus,
.dijitTabContainerTop-dijitContentPane:focus,
.bnext .dijitTab .tabLabel:focus,
.dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter:focus,
div#tabContainer_tablist:focus,
div#tabContainer_tablist_menuBtn .dijitTabStripIcon:focus,
.dijitTabInner.dijitTabContent.dijitTab:focus {
    /*background-color: transparent;*/
    border-color: transparent;
    outline: none;
}
.bnext .dijitTabContainerTop-tabs,
.bnext .dijitTabContainerBottom-tabs {
    padding: 0px;
    border: 0px;
}
.bnext .tabStrip-disabled .tabStripButton {
    display: none;
}
div#tabContainer_tablist_menuBtn .dijitTabStripIcon {
    border: none;
    width: 3rem;
    height: 3rem;
    line-height: 3rem;
    text-align: center;
    background: #ccc;
    position: absolute;
    right: 0;
}
div#tabContainer_tablist_menuBtn {
    background: none;
    border: none;
    display: block;
    right: 0px;
    margin-left: -2.375rem!important;;
    width: 5.25rem!important;;
    z-index: 13;
    padding: 0px;
    background-image: linear-gradient(to right, #0000, #fff, #fff);
}
.ghostMarginAtTop {
    border-bottom: 1px solid #ccc;
    position: fixed;
    height: 9.688rem;
    background: #fff;
    z-index: 1;
    top: 2rem;
}
.marginAtTop {
    margin-top: 9.688rem;
}
body.request-MODULE .ghostMarginAtTop {
    height: 11.563rem;
}
body.request-MODULE .marginAtTop {
    margin-top: 11.563rem;
}
.pageBreak {
    text-align: center;
    font-size: 0.938rem;
    font-weight: 700;
    overflow: hidden;
    color: crimson;
    width: 100%;
    font-variant: small-caps;
    border-top: 0.125rem solid lightgray;
    padding-top: 0.313rem;
}
input[type="file"]{
    border: 0.625rem solid white;
    background-color: white;
    width: auto!important;
    border-radius: 0.938rem;
}
#areaTrabajo {
    padding: 0.625rem;
}
.dijitDialog div.gridInfo{
    width: 100%;
}
.dijitDialog div.border_b_light_gris{
    border: 0px;
}
#sidebar span.dijitDialogCloseIcon {
    display:block;
}
.content_area li {
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}
.content_area li.separator-form {
	padding-top: 0px;
	padding-right: 0.938rem;
	padding-bottom: 0.625rem;
	padding-left: 0.938rem;
    position: relative;
    z-index: 3;
}
.content_area li.actionButtons {
    margin-left: 0.438rem;
}
.content_area label.envelope {
    float: none;
}
.dijitDialogUnderlay {
    background: #00000061;
    opacity: 0.7;
}
textarea {
    height: auto!important;
}

.min550 {
    min-width: 34.375rem;
}
td, th {
    border-right: 0px solid #F8F8F8;
    vertical-align: middle;
    padding: 2px;
}body {
    /*margin-left: 0.625rem;*/
    margin-right: 0.313rem;
}
.css_freeText div.cke_textarea_inline {
    min-height: 1.875rem;
    height: initial!important; 
}
#fieldArea input, #fieldArea textarea, #fieldArea select, #fieldArea label{
    float:none;
    display:inline;
}
#fieldArea input.html-text-line-display, #fieldArea textarea.html-text-line-display, #fieldArea select.html-text-line-display, #fieldArea label.html-text-line-display {
    width: 100%;
    border: 0;
    background: transparent;
}
#fieldArea .array-item-answer-type {
    width: 100%;
    height: 30.5px;
    min-height: 30.5px;
    max-width: 18rem;
}
.selectable > .main_form_obj_div {
    padding: 0.313rem 0.938rem;
    width: 100%;
}
.html-text-line-display {
    display: inline-block;
    vertical-align: middle;
    cursor: text;
    min-height: 1.563rem;
    width: 100%;
}
.html-text-line-display img + br {
    display: none;
}
.html-text-line-display p {
    display: inline-block;
}

input[type=checkbox],input[type=radio]{
    width:1.25rem!important;
    border:none!important;
    cursor: pointer;
}
.reset{
    float:none;
    display:inline;            
}
a {
    color: black;
}
.w15 {
    width:0.938rem;
}
.w25{
    width:1.563rem;
}
.ponderada {

}
.border_b_light_gris {
    border-bottom: solid 1px #333;
}
.middleImg {
    height: 0.625rem;
    margin: 1px;
}

.qrImage {
    padding: 10px;
    border: 2px solid #000;
    display: inline-block;
    background: #ffffff;
    box-shadow: 0 0 10px rgba(0,0,0,0.5), 0 0 20px rgba(0,0,0,0.1) inset;
    border-radius: 5px;
}

.left{float:left;}
.right{float:right;}
.center{margin:0 auto;}
.block{display:block;}
.inline{display:inline;}
.hide{display:none;}
.bold{ font-weight:bold;}

.txt-left{text-align:left;}
.txt-right{text-align:right;}
.txt-center{text-align:center;}
.text-justify{text-align:justify;}

.envelope{float: left;width: 100%;}

.col1{width:3.75rem;}
.col2{width:8.75rem;}
.col3{width:13.75rem;}
.col4{width:18.75rem;}
.col5{width:23.75rem;}
.col6{width:28.75rem;}
.col7{width:33.75rem;}
.col8{width:38.75rem;}
.col9{width:43.75rem;}
.col10{width:48.75rem;}
.col11{width:53.75rem;}
.col12{width:58.75rem;}

.grid1{width:5rem;}
.grid2{width:10rem;}
.grid3{width:15rem;}
.grid4{width:20rem;}
.grid5{width:25rem;}
.grid6{width:30rem;}
.grid7{width:35rem;}
.grid8{width:40rem;}
.grid9{width:45rem;}
.grid10{width:50rem;}
.grid11{width:55rem;}
.grid11dot5{width:57.25rem;}
.grid12{width:60rem;}

.col1, .col2, .col3,
.col4, .col5, .col6,
.col7, .col8, .col9,
.col10, .col11, .col12{
    margin:0.313rem; 
    padding:0.313rem;
}

.col1, .col2, .col3,
.col4, .col5, .col6,
.col7, .col8, .col9,
.col10, .col11, .col12{
    _margin:0.25rem; 
    _padding:0.313rem;
}


/***Paddings**/
.p5{padding:0.313rem;}
.p10{padding:0.625rem;}
.p15{padding:0.938rem;}
.p20{padding:1.25rem;}

.ph5{ padding-left:0.313rem; padding-right:0.313rem;}
.ph10{ padding-left:0.625rem; padding-right:0.625rem;}
.ph15{ padding-left:0.938rem; padding-right:0.938rem;}
.ph20{ padding-left:1.25rem; padding-right:1.25rem;}

.pv5{ padding:0.313rem 0;}
.pv10{ padding:0.625rem 0;}
.pv15{ padding:0.938rem 0;}
.pv20{ padding:1.25rem 0;}

.pr5{	padding-right:0.313rem;}
.pr10{	padding-right:0.625rem;}
.pr15{	padding-right:0.938rem;}
.pr20{	padding-right:1.25rem;}

.pb5{	padding-bottom:0.313rem;}
.pb10{	padding-bottom:0.625rem;}
.pb15{	padding-bottom:0.938rem;}
.pb20{	padding-bottom:1.25rem;}

/**Margin**/

.m5{margin:0.313rem;}
.m10{margin:0.625rem;}
.m15{margin:0.938rem;}
.m20{margin:1.25rem;}

.mtop5{ margin-top:0.313rem;}
.mtop10{ margin-top:0.625rem;}
.mtop11{ margin-top:0.688rem;}
.mtop15{ margin-top:0.938rem;}
.mtop20{ margin-top:1.25rem;}
.mtop30{ margin-top:1.875rem;}

.mh5{ margin:0 0.313rem;}
.mh10{margin:0 0.625rem;}
.mh15{margin:0 0.938rem;}
.mh20{margin:0 1.25rem;}

.mv5{ margin:0.313rem 0;}
.mv10{ margin:0.625rem 0;}
.mv15{ margin:0.938rem 0;}
.mv20{ margin:1.25rem 0;}

/**********Menus**************/
ul.menu-vert, ul.menu-vert li ul,ul.menu-hor, ul.menu-hor li ul{margin:0; padding:0; border: 0 none;}
ul.menu-vert li, ul.menu-hor li, ul.menu-vert li ul li, ul.menu-hor li ul li{list-style: none;}
ul.menu-vert li a, ul.menu-vert li ul li a{display: block;text-decoration: none;}
ul.menu-hor li, ul.menu-hor li ul li{float: left; /*For Gecko*/display: inline;}
ul.menu-hor li a, ul.menu-hor li ul li a{float: none !important; /*For Opera*/float: left; /*For IE*/display: block;}
ul.menu-hor ul:after /*From IE 7 lack of compliance*/{clear:both;display:block; content:".";  height:0;visibility:hidden;}

/* Surveys */

.field-icons-container {
    outline: 1px solid gray;
    display: inline-flex;
    width: auto;
    background-color: greenyellow;
    color: green;
    height: 0.95rem;
}
.field-icons-container .is-not-main {
    background-color: white;
    outline: 1px solid gray;
    color: gray;
}
.field-icons-container > * {
    font-size: 0.95rem;
    line-height: 0.95rem;
    display: block;
    width: 0.95rem;
    height: 0.95rem;
}
.requiredProperties,
.done-icon,
.help-url-test a {
    height: 2.25rem;
    width: 2.25rem;
    position: absolute;
    text-align: center;
    left: -2.563rem;
    font-size: 1.5rem;
    background: #ff6347;
    border-radius: 2.25rem;
    line-height: 2.25rem;
    top: 0.875rem;
    color: #fff;
}
.subContainer .requiredProperties,
.subContainer .done-icon,
.subContainer .help-url-test a {
    left: -2.188rem;
    top: 0.125rem;
}
.done-icon {
    background: #6db16d;
}
.help-url-test a {
    color: #fff;
    top: 4.063rem;
    background: #000;
}
.dpms_button {
    padding: 0.313rem;
    border: solid 1px #DDD;
    margin: 0.125rem;
    background: #EEE;
    text-align: center;
    cursor: pointer;
    -moz-border-radius: 0.188rem;
    -webkit-border-radius: 0.188rem;
    border-radius: 0.188rem;
    font-weight: bold;
    border: 1px solid black;
    font-weight:bold; 
    text-decoration:none; 
    position: relative; 
    cursor: pointer;
}
.hiddenStuff {
    opacity: 0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
}
.showedStuff {
    opacity: 1;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=1)";
    filter:alpha(opacity=1);
    -moz-opacity:1;
    -khtml-opacity: 1;
}
#textWhiteMinus {
    color: white;
    text-align: center;
    vertical-align: middle;
    font-size: medium;
    cursor: pointer;
}
#textWhitePlus {
    color: white;
    text-align: center;
    vertical-align: middle;
    font-size: medium;
    cursor: pointer;
}
#plusQuestions {
    z-index: 948;
    top: 3.188rem;
    position: fixed;
    margin-left: -0.375rem;
    margin-top: -0.313rem;
}
.cellMatrix, .cellMatrixOn {
    text-align: center;
    padding: 1px;
    height: 1.563rem;
    vertical-align: middle !important; 
}
.MatrixOnText {
    font-size: 1rem;
    margin-bottom: -0.625rem;
}
.MatrixOnText + .checkbox_matrix,
.MatrixOnText + .radio_matrix {
    display: none !important;
}
.cellMatrixOn:hover .MatrixOnText {
    opacity: 0;
}
.cellMatrix td {
    padding: 1px;
}
.cellMatrix:hover {
    background: gainsboro url('../../images/survey/onoff.png')  no-repeat center center;
}
.cellMatrix input {
    margin: 0;
    vertical-align: middle;
}
.cellMatrix input:hover {
    opacity:0!important;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
    cursor: pointer;
}
.cellMatrix table:hover {
    background-color: red;
    opacity:0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
    cursor: pointer;
}
.cellMatrixOn table:hover {
    background-color: red;
    opacity:0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
    cursor: pointer;
}
.cellMatrixOn:hover {
    background: gainsboro url('../../images/survey/onoff_on.png')  no-repeat center center;
}
.cellMatrixOn input {
    margin: 0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
}
.cellMatrixOn input:hover {
    opacity:0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter:alpha(opacity=0);
    -moz-opacity:0;
    -khtml-opacity: 0;
    cursor: pointer;
}
.cellMatrixOn {
    text-align: center;
}
a img{border-width:0}
a:visited{color:blue}
q:before,q:after{content:""}
#content{max-width:60rem;min-width:48.5rem;width:99%;text-align:left;margin:auto}
#banner{color:gray;width:70%;float:left}
#account-selection{float:right;color:#777;margin:1.25rem 0 0.125rem}
#navMenu{border-bottom:1px solid #DCDCDC;font-size:1.25rem;font-weight:400;padding-top:0.938rem}
#navMenu ul{margin:0 0 0 0px;padding:0}
#navMenu ul li{list-style:none;display:inline;margin:0 0 0 0.313rem}
#navMenu ul li a{background:#DCDCDC;border:1px solid #DCDCDC;border-bottom:1px solid #DCDCDC;text-decoration:none;color:#666;padding:1px 0.688rem}
#navMenu ul li a:hover{background-color:#fafafa;border-bottom:1px solid #fafafa;color:#dc7272}
body#surveysPage a#navSurveys,body#accountPage a#navAccount,body#reportsPage a#navReports,body#helpPage a#navHelp,body#templatesPage a#navThemes,body#partnersPage
a#navPartners,body#membersPage
#navMembers{background-color:#fff;border-bottom:1px solid #fff;color:#DC5151;font-weight:700}
#unauth-menu{width:100%;border-bottom:1px solid #dcdcdc}
#main{width:100%;border:1px solid #DCDCDC;border-top:none;background-color:#FFF;text-align:center;padding-bottom:1.563rem;margin:auto}
#mainHeader{font-size:0.688rem;font-weight:700;padding:0.625rem}
#mainHeader img{vertical-align:middle;padding-right:0.188rem}
#mainHeader a{text-decoration:none;margin-left:0.188rem;padding:0.188rem}
#whatsNew{color:#ef844a}#whatsNew:hover{background-color:#efdcd1}
#bugReport:hover{background-color:#daf1c2}#feedback{color:#5DA6D8}
#feedback:hover{background-color:#dbeafd}
#footer{margin-top:0.313rem;font-size:0.688rem;letter-spacing:0.1px;padding:0 0.625rem 0.625rem}
#footer a{padding:0 0.625rem 0 0}#footer li{display:inline;list-style:none;margin:0;padding:0}
#footer span{color:green;float:right}#footer form{float:right}
#copyright{margin-top:1em;color:#777}
.unsupported-browser{background-color:#F0F9FF;color:rgb(13, 137, 196);font-size:0.875rem;text-align:center;font-weight:700;width:80%;border:1px solid #dcdcdc;margin:1.25rem auto auto;padding:0.313rem}
#overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#F5F5F5;opacity:0.8;z-index:10001}
#overlay2{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#252525;opacity:0.8}
.overlayVisible{position:relative;z-index:10001}
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden}
.questionForm{color:#888}
.blockTitle{width:95%;margin:auto}
.blockTitle
h2.title{font-size:1.125rem;font-weight:700;border-bottom:1px solid #dcdcdc;color:rgb(13, 137, 196);text-align:left;margin:0 auto;padding:0}
.blockTitle div.vcard{color:#666;margin-bottom:0.625rem}.qf-logo{font-size:0.938rem;font-weight:700;color:#8cc919;font-family:verdana}
.qf-logo span{color:#7f7f7f}div.selSurvey{width:100%;background-color:#E0FFCF;border:1px solid #dcdcdc;text-align:left;margin:0.313rem auto}
div.selSurvey h3{font-size:1.125rem;margin:0 0 0 0.313rem;padding:0.313rem 0 0}div.selSurvey h3 span{width:80%;margin-right:1.25rem}
div.selSurvey h3 img{vertical-align:middle;padding-right:0.625rem}div.selSurvey h3 small{color:#74983b;margin-left:0.313rem}
div.selSurvey p{margin:0.313rem 0 0.313rem 0.313rem}div.selSurvey p.actions a{text-decoration:none;font-weight:700;font-size:0.75rem;color:gray;margin-right:0.625rem;display:inline;padding:0.188rem 0.625rem 0.188rem 0.188rem}div.selSurvey p.actions a:hover{color:red;background-color:rgb(13, 137, 196)}div.selSurvey.disabled{opacity:0.6}#accountPlans{border-collapse:collapse;width:100%;margin:auto}#accountPlans td{border:1px solid #dcdcdc;font-size:0.75rem;color:#444;text-align:center}#accountPlans tr.planHeader td{border:0;font-size:0.875rem;padding:0.313rem}#accountPlans thead td.features{width:30%;color:#666;background-color:#f5f5f5;font-size:1rem;font-weight:700;padding:0.125rem}#accountPlans thead td.plan{width:15%;font-weight:700;color:#DC5151;font-size:1rem;background-color:#f5f5f5;padding:0.188rem 0.188rem 0.313rem}#accountPlans thead td.selected{background-color:#ddd}#accountPlans tr.alItem{background-color:#EEE}#accountPlans tbody td.features{text-align:left;color:#000;background-color:#DAF1C2;font-weight:700;padding:0.313rem}#accountPlans tbody td.features small{display:block;color:#666;font-weight:400}#accountPlans td.selected{background-color:#ddd;font-weight:700;border:1px solid #FFF}.infoMsg{width:100%;font-size:1.25rem;font-weight:700;padding-top:3.125rem;padding-bottom:3.125rem}table.redgrid thead{background-color:#DC5151;color:#fff;font-size:0.75rem;font-weight:700;border:1px solid #D7D7D7}table.redgrid tbody td{border:1px solid #D7D7D7;font-size:0.688rem;color:#000}table.greygrid thead{background-color:#DBEAFD;color:#000;font-size:0.75rem;font-weight:700;border:1px solid #D7D7D7}table.greygrid tbody td{border:1px solid #D7D7D7;font-size:0.688rem;color:#000;background-color:#FFF}div.tip{background-color:#FFFFE0;border:1px solid #dcdcdc;text-align:left;font-size:0.75rem;padding:0.25rem}div.tip .title{color:#444;font-weight:700;border-bottom:1px solid #444;font-size:0.875rem;margin:0 0 0.625rem;padding:0.125rem}div.tip .title img{padding-right:0.25rem}div.tip p.question{margin-bottom:0;font-weight:700}div.tip p.answer{background-color:#f5f5f5;margin-top:0;border:1px solid #dcdcdc;padding:0.125rem}div.tip .text{font-size:0.688rem}p.tip{background-color:#FFFFE0;border:1px solid #dcdcdc;font-size:0.688rem;margin:0.625rem;padding:0.313rem}div.faq{margin-left:auto;margin-right:auto;text-align:left;border:1px solid #dcdcdc;background-color:#FFF;padding:0}div.faq h3{background-color:#f5f5f5;color:#000;font-size:1.125rem;margin:0;padding:0.375rem}div.faq .question{color:rgb(13, 137, 196);font-size:0.875rem;font-weight:700;margin:0;padding:0.5rem 0 0 0.375rem}div.faq .answer{color:#666;font-size:0.75rem;margin-top:0;padding:0.125rem 0.375rem}.smallInfo{width:70%;text-align:center;font-size:0.688rem;font-weight:700;color:gray;background-color:rgb(13, 137, 196);margin:0.313rem auto}div.option{text-align:left;width:95%;margin:1.563rem auto auto}div.option h3{font-size:1.125rem;margin:0;padding:0}div.optionBody{background-color:#f5f5f5;border:1px solid #dcdcdc;padding:0.313rem}div.optionBody p{font-weight:700;font-size:1rem;margin:0;padding:0.313rem}div.optionBody p.info{font-size:0.75rem;color:#666;font-weight:400;padding:0}div.optionBody p small{font-weight:400;color:green;margin-left:0.25rem}div.optionBody p small.disabled{background-color:#FFFFE0;margin-left:0.938rem;color:rgb(13, 137, 196)}div.optionBody p span.link{margin-left:0.625rem;color:#00F;text-decoration:underline;font-size:0.75rem}div.optionBody p.details{font-size:0.75rem;font-weight:400;margin:0.625rem 0;padding:0 0.625rem}div.optionBody p.details input{margin-left:0.313rem}div.optionBody p.disabled{font-weight:400;color:rgb(13, 137, 196);background-color:#FFFFE0;font-size:0.75rem}div#upgrade-required div{width:90%;margin:0.313rem auto}div#upgrade-required p strong{background-color:#dbeafd;color:#000}div#upgrade-required p.exit{margin-top:0.313rem}#whatsNewWindow{text-align:left;background-color:#FFF;border:1px solid #dcdcdc}#whatsNewWindow div.title{background-color:#dbeafd;font-weight:700;font-size:1rem;margin:0.25rem;padding:0.313rem}#whatsNewWindow .rss{float:right;font-size:0.688rem;font-weight:400}#whatsNewWindow .rss img{vertical-align:middle;margin-left:0.188rem}#whatsNewWindow .rss a{text-decoration:none}#whatsNewWindow .entries{height:12.5rem;overflow:auto;margin:0.625rem;padding:0.188rem}#whatsNewWindow .separator{width:100%;border-bottom:1px solid #ccc;padding:0.313rem 0}#whatsNewWindow .entry{color:#333;margin-left:0.313rem;font-size:0.688rem}#whatsNewWindow h3.title{color:#333;text-align:left;margin-left:0.313rem;font-size:0.813rem}#whatsNewWindow span.date{float:right;background-color:#dcdcdc;font-size:0.625rem}#whatsNewWindow p{padding:0.625rem}.alignRight{text-align:right}.alignLeft{text-align:left}
.alignCenter * {
    margin-left:auto;
    margin-right:auto
}.visible{display:block;visibility:visible}.invisible{visibility:hidden}
.error{color:#666;font-weight:400;font-size:0.875rem;background-color:#FFFFE0;border:1px solid #dcdcdc;margin:0.313rem auto;padding:0.313rem}
.error .title{background-color:#DC5151;color:#FFF;font-weight:700;margin:0.125rem auto;padding:0.125rem}
.clickable{cursor:pointer}.resize{cursor:w-resize}.unclickable{cursor:default}span.tooltip{color:blue;text-decoration:underline;cursor:pointer}.helpTooltip{background-color:#666;color:#FFF;width:25rem;border:1px solid #ccc;text-align:left;opacity:0.8;margin:0.313rem;padding:0.625rem}

.button img{margin-right:0.25rem;vertical-align:middle}.buttons{clear:both}.buttons

.button{
    background-color:#fff;
    border: 1px solid;
    filter:progid:DXImageTransform.Microsoft.Gradient(
        GradientType=0,StartColorStr='#00000000',EndColorStr='#cccccccc');
    cursor:pointer;
    border-top:1px solid #f5f5f5;border-left:1px solid #f5f5f5;
}
a.button{
    text-decoration: none;
    max-width: 100%;
    display: flex;
    align-items: center;
}
button.button{padding:0.188rem 0.313rem 0.25rem 0.188rem}
.abutton{font-size:0.75rem;font-weight:700;text-decoration:none;border:1px solid #aaa;padding:0.313rem}
.abutton span{padding-left:0.313rem}
.grey{color:#666;border-top:1px solid #f5f5f5;border-left:1px solid #f5f5f5}
.grey:hover{background-color:#DAF1C2;color:green}
.green{background-color:#DAF1C2;color:green;border-top:1px solid #f5f5f5;border-left:1px solid #f5f5f5}
.green:hover{background-color:#A6F15B}
.red{background-color:#F1D4D4;color:red;border-top:1px solid #f5f5f5;border-left:1px solid #f5f5f5}.red:hover{background-color:#F1C5C5;color:red}.white{border:1px solid #ccc;background-color:#FFF;color:#000;border-top:1px solid #f5f5f5;border-left:1px solid #f5f5f5}select.inset{border:1px inset #dcdcdc;background-color:#FFF;font-size:0.75rem;font-family:arial,helvetica,verdana;padding:0.188rem}
.lightbox{
    width:100%;
    height:100%;
    margin:0;
    padding:0;           
    background:#fff url('../../images/survey/loading.gif')  no-repeat center center;
    position:fixed;
    z-index:900;
}
.lightbox h1{font-size:1.375rem;margin:0;padding:0.313rem}
.lightboxWait{
    /*z-index: 10010; background-color:#FFF;font-size:0.75rem;padding:1.25rem*/
    background: #EDF0F3;
    -moz-border-radius: 0.5rem;
    -webkit-border-radius: 0.5rem;        
    border: 1px solid #003;
    padding: 0.625rem;
    -webkit-box-shadow: 0 0.313rem 0.625rem #adadad;
    margin-top: 33%;
}
.greenLB{background-color:#FFF;border:1px solid #dcdcdc}.greenLB h1{background-color:#009000;color:#FFF}.redLB{border:1px solid #dcdcdc;background-color:#fff}.redLB h1{background-color:rgb(13, 137, 196);color:#FFF}.veryLargeLB{width:43.75rem;margin:6.25rem 0 0 -21.875rem}.largeLB{width:37.5rem;margin:6.25rem 0 0 -18.75rem}.mediumLB{width:25rem;margin:6.25rem 0 0 -12.5rem}.smallLB{width:18.75rem;margin:6.25rem 0 0 -9.375rem}#forms{margin:0.625rem auto 0}textarea{border:1px solid;font-family:helvetica,arial}
textarea.disabled{background-color:inherit;color:whitesmoke}
input.textfield.disabled{background-color:inherit;color:whitesmoke}

div.menu{
    color: #333;
    background-color: #e0e0e0;
    height: 2rem;
    width: auto!important;
    font-size: 0.75rem;
    text-align: left;
    border-radius: 2rem;
    line-height: 2rem;
    margin: 0.15rem 0.15rem;
    padding: 0rem 1rem 0rem 0.25rem;
    float: none;
    position: static;
    display: inline-flex;
}
div.menu.dragged-dom {
    -webkit-box-shadow: 0 0.438rem 0.5rem -0.25rem rgba(0,0,0,.26), 0 0.75rem 1.063rem 0.125rem rgba(0,0,0,.12), 0 0.313rem 1.375rem 0.25rem rgba(0,0,0,.08);
    box-shadow: 0 0.438rem 0.5rem -0.25rem rgba(0,0,0,.26), 0 0.75rem 1.063rem 0.125rem rgba(0,0,0,.12), 0 0.313rem 1.375rem 0.25rem rgba(0,0,0,.08);
    opacity: 1!important;
}
div.menu {
    cursor: move; /* fallback if grab cursor is unsupported */
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
}
div.menu:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}
div.menu:hover{
    color: #000;
    background-color: #D8D8D8;
}
div.menu_tool{
    height: 1.438rem;
    width:1.563rem;
    font-size:0.688rem;font-weight:700;
    color:#FFF;
    text-align:left;
    border-left:1px solid #f5f5f5;
    border-top:1px solid #f5f5f5;
    border:1px solid #dcdcdc;
    margin:0.25rem 0.125rem 0.125rem;
    padding-right: 0.125rem;
    padding-top: 0.188rem;
    padding-left: 0.25rem;
    border-radius:0.188rem;
    background: url(../../images/survey/menu_s.png) no-repeat;
}
div.menu_tool:hover {
    background: rgb(13, 137, 196)
}
div.menu img{
    margin-right: 0.5rem;
    height: 1rem;
    margin-left: 0.25rem;
    margin-top: 0.5rem;
}
div.tabContainerParent .dijitVisible {
    height: auto;
    position: fixed;
    width: 73.125rem;
    min-height: 5rem;
}
div.tabContainerParent div.dijitTabContainerTop-dijitContentPane,
.unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
#cols {
    padding-right: 0.313rem;
}
fieldset {   
    -moz-border-radius:0.313rem;  
    border-radius: 0.313rem;  
    -webkit-border-radius: 0.313rem;
}
#menu_separator{float:left;width:100%;margin:0.313rem 0}
#special_fields{color:#999;font-weight:700;font-size:0.75rem;border-bottom:1px solid #BBB;margin:0 0.125rem 0.125rem;padding:0 0.25rem 0.25rem}
#verticalp{min-width:0.938rem;min-height:10.438rem;float:left;height: 10.438rem}
#verticalText {
    writing-mode:tb-rl;
    -webkit-transform:rotate(90deg);
    -moz-transform:rotate(90deg);
    -o-transform:rotate(90deg);
    white-space:nowrap;
    display:block;
    bottom:0;
    width:1.25rem;
    height:1.25rem;
    color:#FFF;
    font-size:0.875rem;
    font-weight:600;
    text-align:left
}
#propertiesArea .disabled-property-label {
    background-color: #dcdcdc;
    margin: 0 0 0.5rem 0;
}
#propertiesArea input[type="radio"],
#propertiesArea input[type="checkbox"]
{
    margin-top: 0.25rem;
    float: left;
}
#propertiesArea .GridProperty.rightLinkedGrid .message_info.grid-component-container.info.gridInfo,
#propertiesArea .fillEntity-parent-class {
    padding: 0;
}
#propertiesArea .days-to-expire-properties {
    border: 1px dashed gray;
    margin: 1rem 0;
}
#propertiesArea .days-to-expire-properties > hr {
    visibility: visible;
    margin-top: 0px;
    margin-right: 1rem;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
}
#propertiesArea .days-to-expire-properties > hr {
    visibility: visible;
    margin-top: 0px;
    margin-right: 1rem;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
}
#propertiesArea .days-to-expire-properties > .propertyWrapper {
    border: 0;
}
#propertiesArea .days-to-expire-properties > hr:first-child {
    display: none;
}
#propertiesArea input[type='text'] {
    float: none!important;
    width: 100%;
    line-height: 3.125rem;
    height: 3.125rem;
    border: 1px solid #808080;
    border-radius: 0.25rem;
    text-indent: 1rem;
}

#propertiesArea input, #propertiesArea  textarea, #propertiesArea  select {
    width: 13.938rem;
    display: block;
    height: 1.375rem;
    border: 1px solid #444;
}
#propertiesArea  textarea, #propertiesArea  select {
    float: none!important;
}
div#propertiesArea .propertyWrapper span {
    margin: 0px 0px 0px 0px;
    width: 100%;
    display: flex;
    max-width: 100%;
}
div#propertiesArea .propertyWrapper span select{
    margin: 0.625rem 0px;
}
div#propertiesArea .propertyWrapper.helpIcon-container-class span + span select,
div#propertiesArea .propertyWrapper.helpIcon-container-class span + span > span {
    display: inline-block;
    line-height: 2rem;
    vertical-align: middle;
}
div#propertiesArea .propertyWrapper legend {
    padding: 0.313rem 0px;
    font-style: italic;
}
#sidebar .dijitDialogPaneContent,
#sidebar.dijitDialog ul.content_area > li,
#sidebar.dijitDialog .gridInfo.info {
    width: 100%!important;
    height: auto!important;
    max-height: 100%;
    min-width: 16.75rem;
    padding-bottom: 4rem;
    overflow-x: auto;
    overflow-y: hidden;
}
#sidebar.dijitDialog ul.content_area > li {
    max-width: 100%;
    text-align: center;
}
#sidebar #propertiesArea label{
    display: inline-block;
    float: none;
    clear: none;
}
#propertiesArea label{
    clear: initial;
    padding: 0.313rem 0.313rem;
    width: auto;
    max-width: calc(100% - 2.5rem);
    text-align: left;
}
.startArea, #formArea {
    width:100%;
    text-align:center;
    outline: 0.125rem dashed #a9a9a9;
}
.startArea span, #formArea span {
    display: block;
    color: #676767;
    padding: 1rem;
}
#fieldArea{
    padding: 0.938rem;
    width: 100%;
}
#stage {
    width:100%;
}
#preguntas{
    position: fixed;
    float: left;
    z-index: 948;
    width: 100%;
    /*height: 1.875rem;*/
    margin-left: -0.188rem;
    padding-right: 0.313rem;
}
#formHeader
.formTitle{
    text-align: left;
    font-size: 1.5rem;
    font-weight: 400;
    font-family: 'TypoPRO Titillium Text', sans-serif;
    overflow: hidden;
    margin: 2rem 0px;
    border-style: none;
    line-height: 1.5;
    border-bottom: solid 1px #aaa;
    width: 100%!important;
}
.formTitle:hover{border:1px solid rgb(13, 137, 196)}
#processCombo{font-size:0.875rem;font-weight:bolder;background-color:#F0F9FF;text-align:left;color:red;border-style:none}
div.languageBar{text-align:left;border-top:1px solid #dcdcdc;padding-left:0.313rem;font-weight:700;font-size:0.688rem;margin:0.625rem 0 0.125rem}
div.languageBar img{margin-right:0.25rem}.languageBar a{padding:0.125rem}
.unSelectedLang{opacity:0.2;cursor:pointer;border:0.125rem solid #F0F9FF}
.unSelectedLang:hover{opacity:0.8}
.selectedLang{border:0.125rem solid rgb(13, 137, 196)}
div.pages{background-color:#F2FC40D15;text-align:left;font-size:0.688rem;font-weight:700;border-top:1px solid #dcdcdc;padding:0.313rem}
div.pages span.page{margin-left:0.125rem;border:1px solid #dcdcdc;padding:0.188rem}
div.pages span.page:hover{background-color:green;cursor:pointer;color:#FFF}
div.pages span.selected{background-color:#DC5151;color:#FFF}
div.pages span.paginator{background-color:#FFFFE0;color:#000;border:1px solid #dcdcdc;cursor:pointer;padding:0.188rem}
div.pages span.left{margin-left:0.5rem}
div.pages span.disabled{cursor:default;opacity:0.3}
.dragformArea {
    background-color: rgb(145, 198, 255);
}
.dragheaderArea, .dragfooterArea {
    background-color: darkseagreen;
}
.dragging {
    color:#FFF/*#0f3b64*/;
}
div.selectable .dragging {
    background-color: skyblue;
    border-radius: 0.125rem;
    border: dotted 1px lightgreen;
}
.dropdiv{
    line-height: 2.25rem;
    height: 2.25rem;
    margin: auto; 
    padding: 0px;
    text-align:center;
    font-size:0.75rem;
    font-weight:700;
    text-transform: capitalize;
}
.dropdiv img {width:0.875rem;padding: 0px;}
.dropp {
    background-color:rgb(13, 137, 196);
    color:#FFF
}
.droppFHArea {
    background-color: seagreen;
    color:#FFF
}
.candrop{cursor:move}
.isSelected{
    background-color:#FFFFE0;
}
.selectable{
    text-align:left;
    cursor:pointer;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    padding: 0; 
    position: relative;
    margin-left: 2.25rem;
}
.selectable:hover > .main_form_obj_div { 
    background-color: #FFFFE0;
}
/*
 * Styles de preguntas
 */
#fieldArea {
    min-height: 31.25rem; 
    max-height: calc(100vh - 15rem);
    padding-bottom: 5rem;
    overflow: auto;
}
#fieldArea
.textfield{
    width: 100%;
    background-color:#fff;
    min-height: 3.75rem;
    border-left: none;
    border-right: none;
    border-top: none;
    text-align: left;
    border-radius: 0;
    text-indent: 0.25rem;
    box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.54);
}
#fieldArea .arrayItem .textfield {
    cursor: default;
}
.supersuperseccion{font-size:1.563rem;font-weight:600;overflow:hidden;color: #cc9900;border-style:none;border-width:0;margin-left: 0.313rem}
.superseccion{font-size:1.375rem;font-weight:900;overflow:hidden;color:#191970;border-style:none;border-width:0;margin-left: 2.5rem}
.itemSubTitle{font-size:0.75rem;overflow:hidden;color:#666;border-width:0}
.itemSubTitle82{font-size:0.75rem;overflow:hidden;color:#666;border-width:0;margin-left: 1rem}
.itemText{
    font-size:0.75rem;
    overflow:hidden;
    font-family:Arial;
    border-width:0;
    min-width: 3.75rem;
    /*width: 80%!important;*/
    display: inline;
    position: relative;
    bottom: 0.438rem;
    margin-left:0.563rem;
}
.clickable img{
    cursor:pointer;
    z-index: 1;
}
.uiObjectItems{margin-top:0.625rem;margin-left: 3.75rem}
.uiObjectItems p{margin:0.313rem 0 0;padding:0;}
.htmlListItem {
    border: 0;
    background-color: white;
    /*max-width: 12.5rem!important;*/
    min-width: 12.5rem!important;
    width: 100%!important;
}
.htmlListArrayItem {
    width: 100%;
    margin: 0px;
    height: 100%;
}
.uiObjectItemsList {
    background-color: white;
    border: 0px solid gainsboro;
    max-width: 15.625rem;
    margin-top:0.625rem;
    margin-left: 3.75rem
}
div[data-field-type="fieldArray"] .arrayTable .uiObjectItemsList {
    margin-top: 0;
    margin-left: 0;
}
div[data-field-type="dateSelector"] .include-time-t {
    gap: 0.5rem;
}
div[data-field-type="dateSelector"] .include-time-t .large-5 {
    width: calc(50% - 0.5rem);
}
.uiObjectItemsList p {
    border: 0;
    background-color: white;
    max-width: 12.5rem;
    padding-left: 0.625rem;
}

.actions {
    background-color: #FFFFE0;
    position: absolute;
    height: 1.75rem;
    margin: 0;
    padding: 0.125rem;
    top: -1.75rem!important;
    left: 0px!important;
}
.subContainer .actions {
    position: inherit;
    top: auto!important;
    left: auto!important;
    background-color: transparent;
    padding-top: 0.125rem;
    padding-left: 0.75rem;
    padding-bottom: 0px;
}
.subContainer .actions .clickable {
    display: none !important;
}
.subContainer .isSelected .actions .clickable {
    display: inline !important;
}
.subContainer div {
    margin-left: 0px;
    max-width: 100%;
}
.subContainer .uiObjectItems p {
    display: block !important;
}
.subContainer .uiObjectItems .itemText {
    margin-left: 0px;
    max-width: 100%;
    padding: 0;
}
.subContainer .uiObjectItemsList .htmlListItem  {
    width: calc(100% - 0.25rem) !important;
    padding-left: 0.25rem;
    padding-right: 0px;
}
.subContainer .main_form_obj_div {
    padding: 0px !important;
    margin: auto;
    width: calc(100% - 1rem) !important;
}
.subContainer .uiObjectItemsList p {
    padding-left: 0px;
}
.subContainer input[type=checkbox].material-icons,
.subContainer input[type=radio].material-icons {
    margin-left: 0px;
}
.subContainer .selectable {
    padding-top: 0.25rem;
}
.subContainer .itemActions {
    height: auto;
    display: flex;
    position: inherit;
    margin-left: 1px;
}
.subContainer .itemActions img {
    display: inline;
    margin-right: 0.313rem;
    margin-top: 0.25rem;
    margin-bottom: 0px;
}
.subContainer .arrayQuestionTitle {
    text-align: left;
}
.actions img{vertical-align:top}
.fieldNumber{font-size:1.375rem;font-weight:700;color:#000;vertical-align:top}
.itemActions{position:absolute;height:3.188rem;width:1.875rem;
             text-align:center}
.itemActions img{margin-top:0.375rem}
.arrayTable{
    border-collapse:collapse;
    width:100%;
    /*max-width: 850.125rem;*/
    background-color: white;
}
.arrayTable select {

}
.arrayQuestionTitle {
    font-size:0.75rem;
    text-align:center;
    overflow:hidden;
    /*width:30%;*/
    border-width:0;
    min-width: 6.25rem;
    background-color: transparent;
    padding-left: 0px!important;
}
.arrayColHeader{
    text-align:center;
    font-size:0.75rem;
    overflow:hidden;
    border-width:0;
    min-width: 1.25rem;
    min-height: 1.375rem;
}
.arrayColHeader input{
    width: 100% !important;
    background-color: transparent;
}
.propertyWrapper select {
    width: 10rem;
}
#add-lang h1{background-color:#E0FFCF;font-size:1.125rem;margin:0;padding:0.313rem}#add-lang div{width:70%;background-color:#f5f5f5;border:1px solid #dcdcdc;margin:0 auto}#add-lang p{margin:0;padding:0.313rem 0}#add-lang p.item{text-align:left;cursor:pointer;font-weight:700;color:#666;margin:0.313rem;padding:0.313rem}
#add-lang p.unSelectable{cursor:default;opacity:0.4;text-decoration:line-through}img,div.selSurvey p.actions a img{vertical-align:middle}q,.italicText{font-style:italic}
#__auto_check,.hidden{display:none}
#header,.fullWidth{width:100%}body#indexPage div
#main,body#signupPage div#main,body#loginPage div#main
{
    border:1px solid #dcdcdc;
    vertical-align: middle;
}
.arrayTable td
{
    border:1px solid #DDD;
    vertical-align: top;
}
#bugReport,.questionForm span,a.green:visited{color:green}#footer ul,button{margin:0;padding:0}#footer a:hover,#accountPlans .planHeader strong{color:rgb(13, 137, 196)}div.vcard .role,.boldText{font-weight:700}div.selSurvey p.actions a span,div.option h3 span{margin-left:0.25rem}#accountPlans tr.planHeader,.highlightedText,div.pages span.disabled:hover,#add-lang p.item:hover{background-color:#FFFFE0}table.redgrid,table.greygrid{border-collapse:collapse;margin-bottom:0.313rem;width:100%;text-align:left;border-width:0}table.redgrid td,table.greygrid td{padding:0.313rem}table.redgrid tr.item,table.greygrid tr.item{background-color:#F5F5F5}table.redgrid tr.alItem,table.greygrid tr.alItem{background-color:#FFF}table.redgrid tbody tr:hover,table.greygrid tbody tr:hover{background-color:#E0FFCF}table.redgrid tfoot td,table.greygrid tfoot td{font-size:0.688rem;color:#000}table.redgrid td.img img,table.greygrid td.img img{margin-right:0.125rem}span.oneline-tip,.error .message,a.grey:visited{color:#666}span.oneline-tip img,.optionBody p img,
#toolbox-title img,
#toolbox-reusetitle img,
#propertiesTitle img,
.propertyWrapper img,.uiObjectItems span{margin:0.313rem}.optionBody p a img,a.abutton img{margin-right:0}div
#upgrade-required,
#add-lang{background-color:#FFF;text-align:center;border:1px solid #dcdcdc;padding:0.625rem}div
#upgrade-required h1,div#upload-image h1,div
#upload-audio-window h1{background-color:#DC5151;color:#FFF;font-size:1.125rem;margin:0;padding:0.313rem}div
#upgrade-required p,div
#upload-image p,div#upload-audio-window p{color:#666;margin:0;padding:0.313rem}div#upload-image,div#upload-audio-window{background-color:#FFF;text-align:center;border:1px solid #dcdcdc;font-family:arial;padding:0.625rem}div#upload-image form,div#upload-audio-window form{background-color:#FFFFE0;border:1px solid #dcdcdc;padding:1.25rem 0}.alignCenter,.field-image{text-align:center}.large,a.large{font-size:1.125rem;padding:0.438rem 0.625rem}a.red:visited,.languageBar a:hover{color:red}.white:hover,
#add-lang p.unSelectable:hover{background-color:#f5f5f5}
input[type="text"].inset,
input[type="password"].inset,
textarea.inset {
    border:1px inset #dcdcdc;background-color:#F5F5F5;font-size:0.75rem;font-family:arial,helvetica,verdana;padding:0.188rem
}
input[type="text"].whiteinset,
input[type="password"].whiteinset,
textarea.whiteinset{
    border:1px inset #dcdcdc;
    background-color:#FFF;
    font-size:0.75rem;
    font-family:arial, helvetica, verdana;
    padding:0.188rem
}
div.pages span.paginator:hover { 
    background-color:rgb(13, 137, 196)
}
#mySelect {width:5.313rem;}
#mySelectRight {width:5.313rem;}
#ocultar {position:fixed;left:0.625rem;top:0.625rem;width:0.938rem;border:1px solid transparent;background-color:transparent;font-size:0.875rem;font-weight:700;text-align:left;color:#FFF;padding:0.125rem;}
#left1{width:40%;min-width:10.625rem;height:100%;float:left;height:100%;position:relative;left:1px}
#right1{width:60%;min-width:10.625rem;height:100%;float:left;}
#all1{width:100%;min-width:21.875rem;height:100%;overflow:hidden;}
#over1{width:100%;top:0.625rem;position:fixed}
.checkbox,.radio,.radio_matrix,.checkbox_matrix{
    height:1.25rem!important;
    width:1.25rem!important;
    border:none!important;
}
.propertyWrapper {
    border-width: 1px;
    border-style: dashed;
    padding-left: 0.5rem;
    padding-right: 0.5rem;    
    padding-bottom: 0.5rem;
    padding-right: 0.5rem;
    border-radius: 0px;
    border-color: #808080;
}
.propertyWrapper > div {width: 100%!important;display: inline-block;max-width: 100%;}
.propertyWrapper > div > label{width:auto!important;display: inline-block}
div#propertiesArea .propertyWrapper.rejectProgressStatuses .multi-select-widget-container,
div#propertiesArea .propertyWrapper.cancelProgressStatuses .multi-select-widget-container,
div#propertiesArea .propertyWrapper.partialProgressStatuses .multi-select-widget-container {
    display: flex;
    margin: 0.625rem 0px;
}
div#propertiesArea .propertyWrapper.rejectProgressStatuses .multi-select-widget-container input[type="checkbox"],
div#propertiesArea .propertyWrapper.cancelProgressStatuses .multi-select-widget-container input[type="checkbox"],
div#propertiesArea .propertyWrapper.partialProgressStatuses .multi-select-widget-container input[type="checkbox"] {
    margin-top: 0;
    float: none;
}
#sidebar {
    position: fixed!important;
    height: calc(100vh - 2rem)!important;
    top: 0.9375rem!important;
    left: auto!important;
    right: 1rem;
    width: calc(25vw - 1rem)!important;
    border-radius: 0;
    box-shadow: 0 1px 0.313rem 0 rgba(0, 0, 0, 0.26), 0 0.125rem 0.125rem 0 rgba(0, 0, 0, 0.12), 0 0.188rem 1px -0.125rem rgba(0, 0, 0, 0.08);
}
#sidebar .dijitDialogTitleBar[data-dojo-attach-point="titleBar"] {
    width: 100%!important;
}
#sidebar.dijitDialog .dijitDialogPaneContent .gridExpandable.grid-component-container,
#sidebar.dijitDialog .gridExpandable.grid-component-container table, 
#sidebar.dijitDialog .gridExpandable.grid-component-container,
#sidebar.dijitDialog .gridInfo {
    min-height: 3.188rem!important;
    max-height: available!important;
}
.subContainer .uiObjectItems{margin-top: auto;margin-left: auto;}
.subContainer .itemSubTitle,
.subContainer .fieldNumber,
.subContainer .fixedSpace,
.css_fieldArray .arrayTable  .subContainer  .done-icon,
.subContainer .itemTitle,
.subContainer br {
    display:none;
}
.fieldProperties{
  vertical-align: baseline!important;
  padding-right: 0.375rem;
  padding-top: 0.188rem;
}
.propertyDisplay{
    min-height: 1.375rem;
    margin-left: 0px;
    margin-top: 0px;
    padding-right: 0px!important;
    padding-left: 0px!important;
    max-width: 100%!important;
    flex-wrap: wrap;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    line-height: 3rem;
}
.propertyWrapper.catalogType-container-class span {
    padding: 0px 0px 0.313rem 0px;
}
div#propertiesArea .propertyWrapper .grid-component-container tr.grid-row span {
    word-break: break-all;
}
div#propertiesArea .propertyWrapper .grid-component-container thead th.css_delete {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
div.propertyDisplay:empty:not(:focus):before{
    content:attr(data-text)
}
#conditionalFieldProperty thead,
#summationProperty thead,
#conditionalExpirationFieldProperty thead,
#gridProperty thead {
    display: contents;
}
#fieldArea .catalog-external-hiearchy-container {
    display: flex;
    background-color: white;
    width: fit-content;
    flex-direction: row;
    flex-wrap: wrap;
}
#fieldArea  .catalog-external-hiearchy-field-container {
    display: block;
    width: fit-content;
    height: fit-content;
    overflow: hidden;
    margin: 0.125rem 0.688rem;
    box-sizing: border-box;
    background-color: white;
    padding-top: 0.5rem;
    padding-right: 1rem;
    padding-left: 1rem;
    padding-bottom: 1rem;
}
#fieldArea .catalog-external-hierarchy-field-label {
    width: fit-content;
    text-align: left;
    position: relative;
    margin-top: -1.563rem !important;
    margin-right: 1px !important;
    margin-bottom: 0.375rem !important;
    margin-left: 0.688rem !important;
    text-indent: 0px;
    padding-top: 0.625rem !important;
    padding-right: 0.313rem !important;
    padding-bottom: 0px !important;
    padding-left: 0.313rem !important;
    pointer-events: none;
    overflow: hidden;
    transition: all .25s cubic-bezier(.4,0,.2,1);
    letter-spacing: 0.009375rem;
    text-transform: none;
    top: 2.563rem;
    height: auto;
    box-shadow: none;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: calc(100% - 1.5rem);
    transform: translateY(-1.5rem);
    font-size: 0.875rem;
    font-weight: 700;
    color: #000;
    display: block;
    background-color: white;
}
#fieldArea .catalog-external-hiearchy-field-input {
    height: 2.5rem;
    font-size: 1rem;
    line-height: 1.75rem;
    font-weight: 400;
    padding: 0;
    border-left: 1px solid;
    border-right: 1px solid;
    border-top: 1px solid;
    border-radius: 0.25rem;
    background: none;
    padding-right: 0.938rem;
    padding-left: 0.938rem;
    padding-top: 0.688rem;
    padding-bottom: 0.813rem;
    margin: 1px;
    outline: none;
    border-color: rgba(0,0,0,.24);
    box-sizing: border-box;
    border: 1px solid #b3b3b3;
}
#fieldArea .catalog-external-hiearchy-field-expand {
    transform: translateX(-1.25rem);
    width: 8px;
    height: 8px;
}

#summationProperty .gridExpandable.grid-component-container,
#gridProperty .gridExpandable.grid-component-container {
    width: 100% !important;
    display: contents;
}
#conditionalFieldProperty .gridExpandable.grid-component-container,
#conditionalExpirationFieldProperty .gridExpandable.grid-component-container {
    padding: 0rem;
    max-width: calc(100% - 3rem);
    min-width: unset;
}
#conditionalFieldProperty .gridExpandable.grid-component-container input, 
#conditionalFieldProperty .gridExpandable.grid-component-container textarea,
#conditionalExpirationFieldProperty .gridExpandable.grid-component-container input, 
#conditionalExpirationFieldProperty .gridExpandable.grid-component-container textarea {
    width: 8rem;
    display: block;
    height: 1.375rem;
    border: 1px solid #a9a9a9;
}
.actions img.reportActivity,
.actions img.summation {
    margin-top: 0.188rem;
}

.actions img.reportActivity.SelectableAnswer,
.actions img.summation.SelectableAnswer {
    margin-top: 1px;
    border-color: lightgray;
    border-style: solid;
    border-width: 0.125rem;
    border-radius: 0.375rem;
    padding: 0.125rem;
}
div.formArea p.SelectableAnswer input,
div.formArea tr.SelectableAnswer {
    background: lightgray;
	cursor: pointer;
   -moz-user-select: none;
   -khtml-user-select: none;
   -webkit-user-select: none;

   /*
     Introduced in IE 10.
     See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
   */
   -ms-user-select: none;
   user-select: none;
}

div.formArea tr.SelectableAnswer > td.arrayItem select,
div.formArea tr.SelectableAnswer > td.arrayItem div,
div.formArea tr.SelectableAnswer > td.first_td_tr div {
    background: lightgray;
    cursor: pointer;
   -moz-user-select: none;
   -khtml-user-select: none;
   -webkit-user-select: none;
    pointer-events:none;
   /*
     Introduced in IE 10.
     See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
   */
   -ms-user-select: none;
   user-select: none;
}

div.formArea p.SelectableAnswer input:hover {
    background: lightgreen;
}

div.formArea p.ReportActivity:after {
    content: url('../../scripts/framework/bnext/images/document_add.png');
    vertical-align: middle;
    display: inline-block;
    margin-left: 0.313rem;
}

div.formArea tr.Summation:after {
    content: url('../../scripts/framework/bnext/images/sum-sign.png');
    vertical-align: middle;
    display: inline-block;
    margin-left: 0.313rem;
    background: none;
}

div.formArea div.css_seccion .sectionId {
    height: 1.1em;
    line-height: 1.1em;
    color: #fff;
    background: #2980b9;
    text-align: center;
    box-shadow: 0px 1px 0.25rem 0px rgba(0,0,0,0.3);
    max-width: 12.5rem;
    border-top-left-radius: 1.063rem;
    border-top-right-radius: 1.063rem;
}
#search_gridProperty,
#gridSearchableDataWindow{
    z-index: 901!important;
    padding-left: 0.938rem;
    padding-right: 0.938rem;
}
.cke_editable_inline h1 {
    color: black;
}
.cke_editable_inline em{
    font-style: italic;
}
.cke_editable_inline ul{
    list-style-type: disc;
}
.cke_editable_inline ol{
    list-style-type: decimal;
}

.cke_editable blockquote {
  background: #f9f9f9;
  border-left: 0.625rem solid #ccc;
  margin: 1.5em 0.625rem;
  padding: 0.5em 0.625rem;
  quotes: "\201C""\201D""\2018""\2019";
}
.cke_editable blockquote:before {
  color: #ccc;
  content: open-quote;
  font-size: 4em;
  line-height: 0.1em;
  margin-right: 0.25em;
  vertical-align: -0.4em;
}
.cke_editable blockquote p {
  display: inline;
}

.cke_dialog_ui_vbox_child a.cke_dialog_ui_button:first-child {
    display: none !important;
}
.cke_editor_field1_uiObject_freeText_dialog .cke_dialog_page_contents:first-child input[type="checkbox"].cke_dialog_ui_checkbox_input {
    visibility: hidden;
    opacitiy: 1;
}
.cke_editor_field1_uiObject_freeText_dialog .cke_dialog_page_contents:first-child input[type="checkbox"].cke_dialog_ui_checkbox_input:before {
    content: "radio_button_unchecked";
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "liga";
    top: -0.625rem; 
    position: relative;
    visibility:visible;
    vertical-align: middle;
    padding: 0.188rem;
    margin-left: -0.313rem;
}
.cke_editor_field1_uiObject_freeText_dialog .cke_dialog_page_contents:first-child input[type="checkbox"]:checked.cke_dialog_ui_checkbox_input:before {
    content: "radio_button_checked";
}

html {
    background: #fff;
}
#fieldArea input.itemText, #fieldArea textarea.itemText, #fieldArea select.itemText {
    line-height: 1.938rem; 
    min-height: 1.938rem;
}
.bnext .dijitDialog.fixedTop.fullScreen {
    max-height: calc(100% - 0.75rem)!important;
}
.bnext .dijitDialog.fixedTop.fullWidthScreen {
    min-height: 100vh!important;
}
.arrayTable tr:nth-child(odd) {
    background-color: rgba(0, 100, 207, 0.03);
}
.arrayTable tr
{
    border:1px solid #DDD;
}
/* Chrome */
textarea::-webkit-scrollbar-thumb{
    background: #0375be;
    border-radius: 0;
}
/* Firefox */
textarea {
    scrollbar-color: #0375be transparent;
}
.empty-properties {
    padding: 1.875rem;
    background: #dcdcdc;
    color: #343434;
}
:focus {
    outline: none!important;
}
.displayInlineBlock {
    display: inline-block!important;
}
.displayNone {
    display: none!important;
}
form.envelope.container-form.grid-floating-action-buttons {
    min-height: calc(100vh - 2rem);
    position: relative;
}
.fixed-button-bar {
    position: absolute;
    bottom: 0;
    height: auto;
    width: 100%;
}
html {
    overflow: hidden;
}

.cke.cke_float {
    max-width: 64vw;
}

html .grid-container {
    max-width: 75vw !important;
}
@media screen and (max-width: 95rem) {

}

@media screen and (max-width: 80rem) {
    .cke.cke_float { 
        max-width: calc(100% - 28rem);
    }
    
    .dijitDialog ul.content_area > li {
        padding-left: 0.9rem;
        min-width: calc(100% - 0.9rem) !important;
    }
    
    .dijitDialog ul.content_area > li.actionButtons{
        padding-left: 0.3rem;
    }
    
    .dijitDialog .GridProperty div.content_area > div .Button.addToGrid {
        margin-left: -0.50rem !important;
    }
    
    .dijitDialog .GridProperty div.content_area > div > label {
        max-width: calc(100% - 1px) !important;
        margin-left: -0.5rem;
    }
    
    .dijitDialog .GridProperty div.content_area > div .info.gridInfo {
        padding: 0.5rem;
        min-width: calc(100% - 1px) !important;
        text-align: center;
    }
    
}

@media screen and (max-width: 67.5rem) {
    body.request-MODULE .ghostMarginAtTop {
        height: 11.25rem;
    }
    #sidebar .dijitDialogPaneContent,
    #sidebar.dijitDialog ul.content_area > li,
    #sidebar.dijitDialog .gridInfo.info {
        width: auto!important;
        min-width: 21.875rem;
    }
    .dijitTabContainerTop-dijitContentPane {
        max-height: 8.125rem;
        overflow-y: auto;
    }
    .dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter,
    div#tabContainer_tablist,
    .ghostMarginAtTop,
    .dijitTabPaneWrapper.dijitTabContainerTop-container.dijitAlignCenter {
        width: calc(100vw - 2rem)!important;
    }
    #sidebar {
        width: 22.5rem!important;
    }
    html .grid-container {
        max-width: 100% !important;
    }
    .cke.cke_float {
        max-width: calc(100% - 8rem);
    }
    .dijitTabContainerTopChildWrapper .dijitContentPane.dijitTabContainerTop-child {
        width: calc(100vw - 1.85rem) !important;
    }
}

@media screen and (max-width: 47.813rem) {
    .dijitTabContainerTopChildWrapper .dijitContentPane.dijitTabContainerTop-child {
        width: calc(100vw - 1.65rem) !important;
    }
}

@media screen and (max-width: 40rem) {
    body.request-MODULE .ghostMarginAtTop {
        height: 10.625rem;
    }
    .dijitTabContainerTop-dijitContentPane {
        width: calc(100vw - 1rem)!important;
    }
}

@media screen and (max-height: 47.813rem) {
    #fieldArea {
        min-height: 18.75rem; 
    }
}

.html-text-field-array-placeholder:before {
    content: attr(data-text);
}

#globalPropertiesArea fieldset div span label{
    width: unset;
}
#globalSidebar .dijitDialogPaneContent  {
    min-width: unset;
}
#globalPropertiesArea fieldset {
    margin-left: 1rem;
    margin-right: 1rem;
}
.weightedFieldsProperty