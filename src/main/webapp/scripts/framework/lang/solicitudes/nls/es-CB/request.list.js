define({
    root: {
        language_strings: [
            {element: '#window_title', text: 'Lista de solicitudes'}
        ],
        colNames: {
            status: 'Estado',
            code: 'Clave de solicitud',
            version: 'Revisión',
            type: 'Tipo de solicitud',
            date: 'Fecha de solicitud',
            verify: 'Fecha de verificación',
            author: 'Solicitante',
            doc_code: '<PERSON><PERSON><PERSON> doc.',
            doc_title: '<PERSON><PERSON><PERSON><PERSON> doc.',
            nodo: 'Carpet<PERSON>',
            reason: 'Razón de la solicitud',
            file: 'Archivo subido',
            edit: 'Ver',
            unlock: 'Desbloquear',
            pendingTab: 'Pendientes',
            attendedTab: 'Atendidas',
            ALLTab: '-- Todas --'
        },
        statusNames: {
            selection: '--SELECCIONE--',
            resquested: 'Solicitada',
            rejected: 'Rechazada',
            returned: 'Retornada',
            in_process: 'En proceso',
            finished: 'Terminada'
        },
        commonNames: {
            titleVerify: 'Verificación del documento'
        },
        Validate:{
            unlock_message:"Se liberará la solicitud.<br><br>¿Desea continuar?",
            unlock_success:"La solicitud se liberó correctamente",
            yes: "Sí",
            no: "No",
            _new : 'Nuevo',
            _modification : 'Modificación',
            _re_approval : 'Reaprobación',
            _cancellation : 'Cancelación',
            _promotion : 'Promoción',
            _new_form : 'Nuevo formulario',
            _modification_form : 'Modificación de formulario',
            _re_approval_form : 'Reaprobación de formulario',
            _cancellation_form : 'Cancelación de formulario',
            _fill_form : 'Llenado de formulario'
        },
        columnBusinessUnit: '{Facility}',
        columnDepartment: '{Department}'
    },
    en:true,
    "es-AA":true
});