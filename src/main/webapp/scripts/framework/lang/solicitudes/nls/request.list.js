define(['bnext/i18n!bnext/administrator/document/nls/document-module'], (documentModule) => ({
  root: {
    language_strings: [{ element: '#window_title', text: 'Lista de solicitudes' }],
    colNamesVersion: documentModule.version,
    colNamesAuthor: documentModule.versionAuthor,
    colNames: {
      status: 'Estado',
      code: 'Clave de solicitud',
      version: 'Edición',
      type: 'Tipo de solicitud',
      date: 'Fecha de solicitud',
      verify: 'Fecha de verificación',
      author: 'Solicitante',
      doc_code: 'Clave doc.',
      doc_title: 'Título doc.',
      nodo: 'Carpeta',
      reason: 'Raz<PERSON> de la solicitud',
      file: 'Archivo subido',
      edit: 'Ver',
      unlock: 'Desbloquear',
      pendingTab: 'Pendientes',
      attendedTab: 'Atendidas',
      ALLTab: '-- Todas --'
    },
    colNamesRejectionDate: '<PERSON><PERSON> de rechazo',
    statusNames: {
      selection: '--SELECCIONE--',
      resquested: 'Solicitada',
      rejected: 'Recha<PERSON><PERSON>',
      returned: 'Retornada',
      in_process: 'En proceso',
      finished: 'Terminada'
    },
    statusNameStandBy: 'Borrador',
    commonNames: {
      titleVerify: 'Detalle de la solicitud'
    },
    Validate: {
      unlock: 'Desbloqueada',
      lock: 'Bloqueada',
      unlock_message: 'Se desbloqueará la solicitud. ¿Desea continuar?',
      unlock_success: 'La solicitud se desbloqueó correctamente.',
      yes: 'Sí',
      no: 'No',
      _new: 'Nuevo',
      _modification: 'Modificación',
      _re_approval: 'Reaprobación',
      _cancellation: 'Cancelación',
      _promotion: 'Promoción',
      _new_form: 'Nuevo formulario',
      _modification_form: 'Modificación de formulario',
      _re_approval_form: 'Reaprobación de formulario',
      _cancellation_form: 'Cancelación de formulario',
      _fill_form: 'Llenado de formulario'
    },
    columnBusinessUnit: '{Facility}',
    columnDepartment: '{Department}',
    columnAuthorizersNames: 'Autorizador(es)',
    colNameDocumentControlledType: 'Clasificación',
    documentControlledTypeControlled: 'Documento controlado.',
    documentControlledTypeUncontrolled: 'Documento no controlado'
  },
  en: true,
  'es-FRISA': true,
  'es-AA': true,
  'es-CB': true,
  'es-CC': true,
  'es-MI': true,
  'es-CIESA': true
}));
