define([
  'loader',
  'dojo/dom',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dojo/_base/lang',
  'dojo/_base/array',
  'dojo/Evented',
  'dijit/_WidgetsInTemplateMixin',
  'dojo/on',
  'dojo/dom-attr',
  'dojo/dom-class',
  'dojo/query',
  'bnext/_base/debounce-queue',
  'bnext/timework/TimeworkAction',
  'bnext/timework/TimeworkMode',
  'bnext/misc',
  'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.events',
  'dojo/Deferred',
  'bnext/callMethod',
  'bnext/survey/_util/StopwatchUtils',
  'bnext/BnextJsonRest'
], (
  loader,
  dom,
  declare,
  _WB,
  lang,
  array,
  Evented,
  wt,
  on,
  domAttr,
  domClass,
  query,
  DebounceQueue,
  TimeworkAction,
  TimeworkMode,
  misc,
  i18n,
  Deferred,
  callMethod,
  StopwatchUtils,
  BnextJsonRest
) => {
  /**
   * Represents a geolocation coordinates.
   *
   * @typedef {Object} GeolocationCoordinates
   * @property {number|null} id - The unique identifier of the geolocation coordinates entity.
   * @property {number|null} latitude - GPS latitude.
   * @property {number|null} longitude - GPS longitude.
   */

  /**
   * Represents a timework entity.
   *
   * @typedef {Object} Timework
   * @property {number} id - The unique identifier of the timework entity.
   * @property {Date|null} checkIn - The check-in time.
   * @property {Date|null} checkOut - The check-out time.
   * @property {string|null} checkInTz - The check-in time.
   * @property {string|null} checkOutTz - The check-out time.
   * @property {Date|null} localCheckIn - The local check-in time.
   * @property {Date|null} localCheckOut - The local check-out time.
   * @property {string|null} localCheckInTz - The local check-in time.
   * @property {string|null} localCheckOutTz - The local check-out time.
   * @property {GeolocationCoordinates} checkInLocation - The geolocation coordinates of the check-in location.
   * @property {GeolocationCoordinates} checkOutLocation - The geolocation coordinates of the check-out location.
   */

  /**
   * Represents a timework diff
   * @typedef {Object} TimeworkDiff
   * @property {number} totalSeconds
   * @property {string} diffDescription - The check-in time.
   */

  /**
   * Represents a timework entity.
   *
   * @typedef {Object} TimeworkEntity
   * @property {number|null} id - The unique identifier of the timework entity.
   * @property {Date|null} checkIn - The check-in time.
   * @property {Date|null} checkOut - The check-out time.
   * @property {string|null} checkInTz - The check-in time.
   * @property {string|null} checkOutTz - The check-out time.
   * @property {Date|null} localCheckIn - The local check-in time.
   * @property {Date|null} localCheckOut - The local check-out time.
   * @property {string|null} localCheckInTz - The local check-in time.
   * @property {string|null} localCheckOutTz - The local check-out time.
   * @property {number|null} checkInLatitude - The check-in latitude.
   * @property {number|null} checkInLongitude - The check-in longitude.
   * @property {number|null} checkOutLatitude - The check-out latitude.
   * @property {number|null} checkOutLongitude - The check-out longitude.
   */

  /**
   * Represents an answer of a OutstandingQuestion.
   *
   * @typedef {Object} OutstandingAnswer
   * @property {string|null} id - The identifier of the answer.
   * @property {number|null} deleted - The identifier of the answer.
   * @property {string|null} [descripcion] - The description of the answer.
   * @property {string|null} [descripcionPlainText] - The plain text description of the answer.
   * @property {string|null} [rangeStart] - The range start date of the answer.
   * @property {string|null} [rangeEnd] - The range end date of the answer.
   * @property {number|null} [rangeSeconds] - The range seconds of the answer.
   * @property {number|null} [timeworkId] - The timeworkId of the answer.
   * @property {Timework} [timework] - The timework entity of the answer.
   *
   */

  /**
   * Represents the result of a timework check method.
   *
   * @typedef {Object} TimeworkCheckMethodResult
   * @property {TimeworkEntity[]} items - The list of timework entities.
   */

  /**
   * Represents the result of a timework check method.
   *
   * @typedef {Object} AngularTimeworkResult
   * @property {TimeworkEntity[]} items - The list of timework entities.
   */

  /**
   * Represents the timesheet record data.
   *
   * @typedef {Object} TimesheetRecordData
   * @property {string|null} rangeDescription - The formatted time difference (HH:mm).
   * @property {number|null} [rangeSeconds] - The range seconds of the answer.
   * @property {string|null} localCheckInDate - The formatted local check-in date.
   * @property {string|null} localCheckOutDate - The formatted local check-out date.
   * @property {string|null} latitudeCheckIn - The latitude at check-in.
   * @property {string|null} longitudeCheckIn - The longitude at check-in.
   * @property {string|null} latitudeCheckOut - The latitude at check-out.
   * @property {string|null} longitudeCheckOut - The longitude at check-out.
   * @property {boolean} isRunning - Indicates whether the widget is running.
   */

  /**
   * Represents the timesheet record data.
   *
   * @typedef {Object} DoCheckExternalData
   * @property {number|null} documentId - Document ID.
   * @property {string|null} documentMasterId - Document Master ID (UUID)
   * @property {string|null} documentDescription - Document description.
   * @property {number|null} surveyId - Survey ID.
   * @property {number|null} surveyFieldId - Survey field ID.
   * @property {number|null} outsandingSurveyId - Outstanding survey ID.
   * @property {string|null} outstandingSurveyCode - Outstanding survey code.
   */

  /**
   * Represents the mapped value from the answers.
   *
   * @typedef {Object} MappedValue
   * @property {Object.<string, any>} [key] - The key-value pairs representing the mapped value.
   */

  const tool = new misc();

  const DATE_WITH_TIME_PATTERN = 'dd/MM/yyyy HH:mm';

  const CURRENT_TIMEZONE = Intl.DateTimeFormat().resolvedOptions().timeZone;

  /**
   * StopwatchSelectorWidget is a widget that handles the stopwatch functionality for timework actions.
   * It allows users to check in, check out, and delete timework records.
   *
   * @class
   * @mixes {dijit/_WidgetsInTemplateMixin}
   * @mixes {dojo/Evented}
   */
  const StopwatchSelectorWidget = {
    /**
     * The unique identifier of the widget.
     * @type {string|null}
     */
    id: null,

    /**
     * Indicates whether the widget is required.
     * @type {boolean}
     */
    required: true,

    /**
     * Indicates whether the widget is busy.
     */
    busy: false,

    /**
     * Indicates whether the widget is in debug mode.
     * @type {boolean}
     */
    debug: false,

    /**
     * Indicates whether the widget is disabled.
     * @type {boolean}
     */
    disabled: false,

    /**
     * Indicates whether the widget has a delete action.
     * @type {boolean}
     */
    hasDelete: false,

    /**
     * The container element for the widget fields.
     * @type {HTMLElement|null}
     */
    fieldContainer: null,

    /**
     * The ID of the field.
     * @type {string|null}
     */
    fieldId: null,

    /**
     * The label for the input field.
     * @type {HTMLElement|null}
     */
    inputLabel: null,

    /**
     * The button element for performing the check action.
     * @type {HTMLElement|null}
     */
    doCheckButton: null,

    /**
     * The input element for the local check-in date.
     * @type {HTMLElement|null}
     */
    inputLocalCheckInDate: null,

    /**
     * The input element for the local check-out date.
     * @type {HTMLElement|null}
     */
    inputLocalCheckOutDate: null,

    /**
     * The input element for the latitude at check-in.
     * @type {HTMLElement|null}
     */
    inputLatitudeCheckIn: null,

    /**
     * The input element for the longitude at check-in.
     * @type {HTMLElement|null}
     */
    inputLongitudeCheckIn: null,

    /**
     * The input element for the latitude at check-out.
     * @type {HTMLElement|null}
     */
    inputLatitudeCheckOut: null,

    /**
     * The external data for the widget.
     * @type {DoCheckExternalData|null}
     */
    externalData: null,

    /**
     * The input element for the longitude at check-out.
     * @type {HTMLElement|null}
     */
    inputLongitudeCheckOut: null,

    /**
     * Indicates whether the widget is busy.
     * @type {boolean}
     */
    _busy: false,

    /**
     * Indicates the server time delta with local time
     * @type {number}
     */
    delayMiliseconds: 0,

    /**
     * Indicates whether the server time has been verified
     * @type {boolean}
     */
    _serverTimeReady: false,

    /**
     * The value of the widget, representing the timework entities.
     * @type {TimeworkEntity[]|null}
     */
    _value: null,

    /**
     * The answers of the widget, representing the timework entities.
     * @type {OutstandingAnswer[]|null}
     */
    __answers: null,

    /**
     * The timework entity associated with the widget.
     * @type {TimeworkEntity|null}
     */
    _entity: null,

    /**
     * Constructor for the StopwatchSelectorWidget.
     */
    constructor: function () {
      // biome-ignore lint/style/noArguments: TODO: Fix this
      this.inherited(arguments);
      this._value = [];
      this._answers = [];
    },

    /**
     * Called after the widget is created.
     */
    postCreate: function postCreate() {
      this.doCheckButton = query('.do-check-action', this.fieldContainer)[0];
      this.inputLocalCheckInDate = query('.local-check-in-date', this.fieldContainer)[0];
      this.inputLocalCheckOutDate = query('.local-check-out-date', this.fieldContainer)[0];
      this.inputLatitudeCheckIn = query('.latitude-check-in', this.fieldContainer)[0];
      this.inputLongitudeCheckIn = query('.longitude-check-in', this.fieldContainer)[0];
      this.inputLatitudeCheckOut = query('.latitude-check-out', this.fieldContainer)[0];
      this.inputLongitudeCheckOut = query('.longitude-check-out', this.fieldContainer)[0];
      if (!this.doCheckButton) {
        console.error('doCheckButton not found');
      } else if (this.disabled) {
        this.disableDeleteButton();
      } else {
        this.setupEvent();
      }
      this.checkServerTime();
    },
    /**
     * Sets up the event listeners for the widget.
     */
    setupEvent: function () {
      on.once(this.doCheckButton, 'click', lang.hitch(this, this.onStopwatchClick));
    },
    onStopwatchClick: function () {
      if (this.busy || !this._serverTimeReady) {
        if (!this._serverTimeReady) {
          console.warn('Server time not ready, please try again');
          this.checkServerTime();
        }
        this.setupEvent();
        return;
      }
      this.busy = true;
      try {
        this.handleStopwatchClick().then(
          () => {
            this.busy = false;
            this.setupEvent();
          },
          (e) => {
            console.error('Error on stopwatch click', e);
            this.busy = false;
            this.setupEvent();
          }
        );
      } catch (e) {
        console.error('Error on stopwatch click', e);
        this.busy = false;
        this.setupEvent();
      }
    },
    /**
     * Handles the click event for the stopwatch button.
     */
    handleStopwatchClick: async function () {
      try {
        const isDoCheckAllowed = await this.isDoCheckAllowed();
        if (!isDoCheckAllowed) {
          return false;
        }
        await loader.showLoader();
        await this.doCheckAction();
        return true;
      } catch (e) {
        console.error('Failed to do check action:', e);
        loader.hideLoader();
        return false;
      }
    },
    /**
     * Checks if the widget check action is allowed.
     * @returns {Promise<boolean>} A promise that resolves to true if the check action is allowed, false otherwise.
     */
    isDoCheckAllowed: async function () {
      const busy = domAttr.get(this.doCheckButton, 'data-busy');
      if (busy === 'true') {
        console.warn('Stopwatch is busy');
        loader.hideLoader();
        return false;
      }
      /**
       * @type {TimeworkActionEnum}
       */
      const method = domAttr.get(this.doCheckButton, 'data-method');
      if (!method || method === '' || method === TimeworkAction.READONLY) {
        console.error('method is not defined');
        loader.hideLoader();
        return false;
      }
      if (method === TimeworkAction.DELETE) {
        if (!this.hasDelete) {
          console.error('Delete action is not allowed');
          loader.hideLoader();
          return false;
        }
        const resultDelete = await this.requestConfirmDelete();
        if (resultDelete) {
          this.renderCheckIn();
        }
        return false;
      }
      return true;
    },
    /**
     * Performs the check action for the stopwatch.
     *
     * @function doCheckAction
     * @returns {Promise<boolean>} A promise that resolves to true when the check action is completed.
     */
    doCheckAction: async function () {
      const isDoCheckAllowed = await this.isDoCheckAllowed();
      if (!isDoCheckAllowed) {
        return false;
      }
      domAttr.set(this.doCheckButton, 'data-busy', 'true');
      this.addAnimations();
      await this.sendDoCheck();
      return true;
    },
    sendDoCheck: async function () {
      const def = new Deferred();
      try {
        // noinspection JSCheckFunctionSignatures
        require(['bnext/angularTimework'], lang.hitch(this, async function (angularTimework) {
          try {
            /**
             * @type {TimeworkAction}
             */
            const method = domAttr.get(this.doCheckButton, 'data-method');
            /**
             * @type {DoCheckActionConfig}
             */
            const config = {
              items: this._value,
              timeworkAction: method,
              timeworkMode: TimeworkMode.CONCURRENT_TIME
            };
            /**
             * @type {DoCheckExternalData}
             */
            const externalData = this.externalData;
            /**
             * @type {AngularTimeworkResult}
             */
            const checkResult = await angularTimework.doCheckAction(config, externalData);
            this.onSuccessDoCheckAction(checkResult, method);
            def.resolve(true);
          } catch (e) {
            /**
             * @type {TimeworkAction}
             */
            const method = domAttr.get(this.doCheckButton, 'data-method');
            console.error(`Failed executing doCheck ${method}`, e);
            this.onFailedDoCheckAction();
            def.reject(false);
          }
        }));
      } catch (e) {
        console.error('Error calling doCheckAction', e);
        def.reject(false);
      }
      return def.promise;
    },
    /**
     * Confirms the deletion of the timework record.
     */
    requestConfirmDelete: function () {
      return new Promise((resolve, reject) => {
        try {
          // noinspection JSCheckFunctionSignatures
          require(['core'], lang.hitch(this, async function (core) {
            try {
              await core.confirm(i18n.deleteStopwatchConfirmMessage);
              const result = await this.executeDelete();
              if (!result) {
                core.error(i18n.deleteStopwatchErrorMessage);
                reject(false);
              } else {
                resolve(true);
              }
            } catch (e) {
              resolve(false);
            }
          }));
        } catch (e) {
          console.error('Error confirming delete action', e);
          reject(e);
        }
      });
    },
    /**
     * Deletes the timework record.
     */
    executeDelete: async function () {
      if (!this.hasDelete) {
        return false;
      }
      try {
        await loader.showLoader();
        const result = await callMethod({
          url: 'Request.Survey.Capture.action',
          method: 'deleteTimework',
          params: [this.externalData]
        });
        loader.hideLoader();
        return result?.operationEstatus === 1;
      } catch (e) {
        loader.hideLoader();
        console.error('Error executing delete action', e);
      }
    },
    /**
     * Handles the success of the check action.
     * @param {AngularTimeworkResult} result - The result of the check action.
     * @param {string} method - The method of the check action.
     */
    onSuccessDoCheckAction: function (result, method) {
      if (!result) {
        this.onFailedDoCheckAction();
        return;
      }
      this._value = result.items;
      this._answers = this.generateAnswers();
      if (method === TimeworkAction.CHECK_IN) {
        this.renderCheckOut();
      } else if (method === TimeworkAction.CHECK_OUT) {
        this.renderDelete();
        this.refreshRender();
        StopwatchUtils.timerStopRefreshWidget(this);
      } else if (method === TimeworkAction.DELETE) {
        this.renderCheckIn();
      }
      this.releaseDoCheckAction();
    },
    /**
     * Handles the failed check action.
     */
    onFailedDoCheckAction: function () {
      /**
       * @type {TimeworkAction}
       */
      const method = domAttr.get(this.doCheckButton, 'data-method');
      if (method === TimeworkAction.CHECK_IN) {
        this.renderCheckIn();
      } else if (method === TimeworkAction.CHECK_OUT) {
        this.renderCheckOut();
      }
      this.releaseDoCheckAction();
    },
    /**
     * Releases the check action, resetting the button state.
     */
    releaseDoCheckAction: function () {
      domAttr.set(this.doCheckButton, 'data-busy', 'false');
      loader.hideLoader();
    },
    /**
     * Renders the widget state after a check-in action.
     */
    renderCheckOut: function () {
      this.doCheckButton.innerHTML = 'stop_circle';
      this.doCheckButton.title = i18n.checkOutStopwatchButtonTitle;
      domAttr.set(this.doCheckButton, 'data-method', TimeworkAction.CHECK_OUT);
      this.addAnimations();
      StopwatchUtils.addRefreshWidget(this);
      this.refreshRender();
      StopwatchUtils.timerStartPeriodicRefresh();
    },

    /**
     * Disables the delete button.
     */
    disableDeleteButton: function () {
      this.doCheckButton.innerHTML = 'info';
      this.doCheckButton.title = i18n.disabledDeleteStopwatchButtonTitle;
      domAttr.set(this.doCheckButton, 'data-method', TimeworkAction.READONLY);
      domAttr.set(this.doCheckButton, 'disabled', 'disabled');
      domClass.remove(this.doCheckButton, 'do-check-action');
      domClass.remove(this.doCheckButton, 'ripple');
    },
    /**
     * Renders the widget state after a check-out action.
     */
    renderDelete: function () {
      if (this.hasDelete) {
        this.doCheckButton.innerHTML = 'delete';
        this.doCheckButton.title = i18n.deleteStopwatchButtonTitle;
        domAttr.set(this.doCheckButton, 'data-method', TimeworkAction.DELETE);
        this.clearAnimations();
      } else {
        this.disableDeleteButton();
      }
    },

    /**
     * Renders the widget state after a delete action.
     */
    renderCheckIn: function () {
      this.doCheckButton.innerHTML = 'play_arrow';
      this.doCheckButton.title = i18n.checkInStopwatchButtonTitle;
      domAttr.set(this.doCheckButton, 'data-method', TimeworkAction.CHECK_IN);
      this.clearAnimations();
      this._value = [];
      this._answers = [];
      this.renderDefaultValue();
      StopwatchUtils.timerStopRefreshWidget(this);
    },
    /**
     * Adds animations to the widget.
     */
    addAnimations: function () {
      if (!this.doCheckButton) {
        console.error('doCheckButton not found');
        return;
      }
      domClass.add(this.doCheckButton, 'stopwatch-widget--running');
    },
    /**
     * Clears animations from the widget.
     */
    clearAnimations: function () {
      if (!this.doCheckButton) {
        console.error('doCheckButton not found');
        return;
      }
      domClass.remove(this.doCheckButton, 'stopwatch-widget--running');
    },
    paddingTime: (time) => {
      if (!time) {
        return '00';
      }
      return time < 10 ? `0${time}` : `${time}`;
    },
    /**
     * Computes the time difference between the check-in and check-out times.
     * @param localCheckOut
     * @param localCheckIn
     * @returns {TimeworkDiff}
     */
    computeDiffTime(localCheckOut, localCheckIn) {
      const diff = localCheckOut - localCheckIn;
      const diffHours = Math.floor(diff / 3_600_000);
      const diffMinutes = Math.floor((diff % 3_600_000) / 60_000);
      const diffSeconds = Math.floor((diff % 60_000) / 1_000);
      const totalSeconds = Math.floor(diff / 1_000.0);
      /**
       * @type {string}
       */
      const diffDescription = `${this.paddingTime(diffHours)}:${this.paddingTime(diffMinutes)}:${this.paddingTime(diffSeconds)}`;
      return { totalSeconds: totalSeconds, diffDescription: diffDescription };
    },
    /**
     * Gets the time difference between the check-in and check-out times.
     * @returns {TimeworkDiff|null}
     */
    getTimesheetRecordDiff: function () {
      const valueLength = this._value?.length;
      if (!valueLength) {
        return null;
      }
      const firstItem = this._value[valueLength - 1];
      const lastItem = this._value[0];
      /**
       * @type {Date}
       */
      let localCheckOut;
      if (lastItem.localCheckOut) {
        localCheckOut = lastItem.localCheckOut;
      } else {
        localCheckOut = this.getNowInTz(lastItem.localCheckInTz);
      }
      return this.computeDiffTime(localCheckOut, firstItem.localCheckIn);
    },
    checkServerTime: function () {
      BnextJsonRest({ url: '../rest/timework-widget/now', params: [] }).then(
        (time) => {
          try {
            const now = this.convertDateInTz(time.timezone, new Date());
            const serverTime = tool.parseDateFromJson(time.now);
            const delayMs = serverTime.getTime() - now.getTime();
            if (typeof delayMs === 'number') {
              this.delayMiliseconds = delayMs;
              this._serverTimeReady = true;
            } else {
              this.delayMiliseconds = null;
            }
            if (this.requiresTimeAdjustment()) {
              const nowString = now.toLocaleString();
              const serverString = serverTime.toLocaleString();
              console.warn(`Local clock running is not the same than the server. Local: ${nowString} Server: ${serverString}`);
            }
          } catch (e) {
            console.error('Failed executing check server time', new Date(), e);
          }
        },
        (e) => {
          console.error('Failed executing check server time', new Date(), e);
        }
      );
    },
    requiresTimeAdjustment: function () {
      const time = this.delayMiliseconds;
      if (time == null || typeof time === 'undefined') {
        return false;
      }
      return Math.abs(time) > 100;
    },
    getCurrentTime: function () {
      const time = new Date(Date.now());
      if (this.requiresTimeAdjustment()) {
        time.setTime(time.getTime() + this.delayMiliseconds);
      }
      return time;
    },
    /**
     * Gets the current date in the provided timezone.
     * @param {string} tz - The timezone.
     * @param {Date} target - The current date.
     * @returns {Date}
     */
    convertDateInTz: (tz, target) => {
      if (CURRENT_TIMEZONE === tz) {
        return target;
      }
      if (tz === null || tz === '' || typeof tz === 'undefined') {
        console.error('Missing timezone to compute target in target timezone', tz);
        return target;
      }
      const formatNow = target.toLocaleString('en-US', { timeZone: tz });
      return new Date(formatNow);
    },
    /**
     * Gets the current date in the provided timezone.
     * @param {string} tz - The timezone.
     * @returns {Date}
     */
    getNowInTz: function (tz) {
      return this.convertDateInTz(tz, new Date());
    },
    /**
     * Gets the timesheet record data.
     * If there are no timework entities, returns default values.
     * Otherwise, calculates the time difference between check-in and check-out,
     * and formats the data for display.
     *
     * @returns {TimesheetRecordData} The timesheet record data.
     */
    getTimesheetRecordData: function () {
      const diff = this.getTimesheetRecordDiff();
      if (!diff) {
        return {
          rangeDescription: '00:00:00',
          rangeSeconds: 0,
          localCheckInDate: '-',
          localCheckOutDate: '-',
          latitudeCheckIn: '-',
          longitudeCheckIn: '-',
          latitudeCheckOut: '-',
          longitudeCheckOut: '-',
          isRunning: false
        };
      }
      const firstItem = this._value[this._value.length - 1];
      const lastItem = this._value[0];
      return {
        rangeDescription: diff.diffDescription,
        rangeSeconds: diff.totalSeconds,
        localCheckInDate: tool.formatDateTime(firstItem.localCheckIn, DATE_WITH_TIME_PATTERN) || '-',
        latitudeCheckIn: firstItem.checkInLatitude || '-',
        longitudeCheckIn: firstItem.checkInLongitude || '-',
        localCheckOutDate: tool.formatDateTime(lastItem.localCheckOut, DATE_WITH_TIME_PATTERN) || '-',
        latitudeCheckOut: lastItem.checkOutLatitude || '-',
        longitudeCheckOut: lastItem.checkOutLongitude || '-',
        isRunning: firstItem.localCheckIn && !lastItem.localCheckOut
      };
    },
    /**
     * Refreshes the clock widget.
     */
    refreshClock: function () {
      let clockDescription;
      const diff = this.getTimesheetRecordDiff();
      if (diff) {
        clockDescription = diff.diffDescription;
      } else {
        clockDescription = '00:00:00';
      }
      if (clockDescription !== this.inputLabel.value) {
        this.inputLabel.value = clockDescription;
      }
    },
    renderDefaultValue() {
      this.inputLabel.value = '';
      this.inputLocalCheckInDate.innerHTML = '-';
      this.inputLocalCheckOutDate.innerHTML = '-';
      this.inputLatitudeCheckIn.innerHTML = '-';
      this.inputLongitudeCheckIn.innerHTML = '-';
      this.inputLatitudeCheckOut.innerHTML = '-';
      this.inputLongitudeCheckOut.innerHTML = '-';
    },
    /**
     * Refreshes the widget state.
     */
    refreshRender: function () {
      const data = this.getTimesheetRecordData();
      this.inputLabel.value = data.rangeDescription;
      this.inputLocalCheckInDate.innerHTML = data.localCheckInDate;
      this.inputLocalCheckOutDate.innerHTML = data.localCheckOutDate;
      this.inputLatitudeCheckIn.innerHTML = data.latitudeCheckIn;
      this.inputLongitudeCheckIn.innerHTML = data.longitudeCheckIn;
      this.inputLatitudeCheckOut.innerHTML = data.latitudeCheckOut;
      this.inputLongitudeCheckOut.innerHTML = data.longitudeCheckOut;
    },
    /**
     * Formats a JSON date.
     * @param date
     * @returns {string|null}
     */
    formatJsonDate: (date) => {
      if (!date) {
        return null;
      }
      return tool.formatJsonDate(date);
    },
    /**
     * Generates answers for the catalog data.
     * @returns {OutstandingAnswer[]} The generated answers.
     */
    generateAnswers: function () {
      /**
       * @type {TimeworkEntity[]}
       */
      const value = this._value;
      if (!value || !value.length) {
        return [];
      }
      const data = this.getTimesheetRecordData();
      /**
       * @type {OutstandingAnswer[]}
       */
      const answers = [];
      for (const record of value) {
        /**
         * @type {OutstandingAnswer}
         */
        const answer = {
          id: null,
          deleted: 0,
          descripcion: data.rangeDescription,
          descripcionPlainText: data.rangeDescription,
          rangeStart: this.formatJsonDate(record.localCheckIn),
          rangeEnd: this.formatJsonDate(record.localCheckOut),
          rangeSeconds: data.rangeSeconds,
          timeworkId: record.id
        };
        answers.push(answer);
      }
      return answers;
    },
    /**
     * Maps the value from the provided answers to the corresponding fields.
     *
     * @param {OutstandingAnswer[]} answers - The array of answers.
     * @returns {TimeworkEntity[]} The mapped value.
     */
    mapValueFromAnswers: (answers) => {
      /**
       *
       * @type {TimeworkEntity[]}
       */
      const value = [];
      if (!answers?.length) {
        return value;
      }
      for (const answer of answers) {
        /**
         * @type {OutstandingAnswer}
         */
        const answerValue = answer;
        /**
         * @type {Timework}
         */
        const timework = answerValue.timework;
        if (timework) {
          /**
           * @type {GeolocationCoordinates}
           */
          let checkInLocation;
          if (timework.checkInLocation) {
            checkInLocation = timework.checkInLocation;
          } else {
            checkInLocation = {
              id: null,
              latitude: null,
              longitude: null
            };
          }
          /**
           * @type {GeolocationCoordinates}
           */
          let checkOutLocation;
          if (timework.checkOutLocation) {
            checkOutLocation = timework.checkOutLocation;
          } else {
            checkOutLocation = {
              id: null,
              latitude: null,
              longitude: null
            };
          }
          const checkIn = tool.parseDateFromJson(timework.checkIn);
          const checkOut = tool.parseDateFromJson(timework.checkOut);
          const localCheckIn = tool.parseDateFromJson(timework.localCheckIn);
          const localCheckOut = tool.parseDateFromJson(timework.localCheckOut);
          /**
           * @type {TimeworkEntity}
           */
          const record = {
            id: answerValue.timeworkId,
            checkIn: checkIn,
            checkOut: checkOut,
            checkInTz: timework.checkInTz,
            checkOutTz: timework.checkOutTz,
            localCheckIn: localCheckIn,
            localCheckOut: localCheckOut,
            localCheckInTz: timework.localCheckInTz,
            localCheckOutTz: timework.localCheckOutTz,
            checkInLatitude: checkInLocation.latitude,
            checkInLongitude: checkInLocation.longitude,
            checkOutLatitude: checkOutLocation.latitude,
            checkOutLongitude: checkOutLocation.longitude
          };
          value.push(record);
        } else {
          const rangeStart = tool.parseDateFromJson(answerValue.rangeStart);
          const rangeEnd = tool.parseDateFromJson(answerValue.rangeEnd);
          /**
           * @type {TimeworkEntity}
           */
          const record = {
            id: answerValue.timeworkId,
            checkIn: rangeStart,
            checkOut: rangeEnd,
            checkInTz: null,
            checkOutTz: null,
            localCheckIn: rangeStart,
            localCheckOut: rangeEnd,
            localCheckInTz: null,
            localCheckOutTz: null,
            checkInLatitude: null,
            checkInLongitude: null,
            checkOutLatitude: null,
            checkOutLongitude: null
          };
          value.push(record);
        }
      }
      return value;
    },
    /**
     * Refreshes the widget
     */
    updateValue: function () {
      const data = this.getTimesheetRecordData();
      if (data.localCheckOutDate && data.localCheckOutDate !== '-') {
        this.renderDelete();
      } else if (data.rangeSeconds === 0) {
        this.renderCheckIn();
      } else {
        this.renderCheckOut();
      }
      this.refreshRender();
    },
    /**
     * Checks if the stopwatch widget is running.
     *
     * @function isRunning
     * @returns {boolean} True if the stopwatch widget is running, false otherwise.
     */
    isRunning: function () {
      const data = this.getTimesheetRecordData();
      return data.isRunning;
    },
    /**
     * Closes the stopwatch widget.
     *
     * @function autoCheckOut
     * @returns {Promise<boolean>} A promise that resolves to true when the widget is successfully closed.
     */
    autoCheckOut: async function () {
      try {
        const running = this.isRunning();
        if (!running) {
          return true;
        }
        await this.doCheckAction();
        return true;
      } catch (e) {
        console.error('Error auto closing stopwatch widget', e);
        return false;
      }
    },
    /**
     * Sets the value of the widget.
     * @param {TimeworkEntity[]} value - The value to set.
     */
    _setValueAttr: function (value) {
      this._value = value;
    },

    /**
     * Gets the value of the widget.
     * @returns {TimeworkEntity[]} The value of the widget.
     */
    _getValueAttr: function () {
      return this._value;
    },
    async loadTimeworkData(answers) {
      const timeworkIds = answers.map((answer) => answer.timeworkId);
      const timeworks = await this.loadTimeworks(timeworkIds);
      if (!(timeworks && timeworks.length > 0)) {
        return;
      }
      for (const answer of answers) {
        const timework = timeworks.find((tw) => tw.id === answer.timeworkId);
        if (timework) {
          answer.timework = timework;
        }
      }
      this._value = this.mapValueFromAnswers(answers);
    },
    _setAnswersAttr: function (answers) {
      // biome-ignore lint/complexity/noUselessThisAlias: Biome bug, self is required
      const self = this;
      this._answers = answers;
      if (!(answers && answers.length > 0)) {
        self.updateValue();
        return;
      }
      this._value = this.mapValueFromAnswers(answers);
      try {
        this.loadTimeworkData(answers).then(
          () => {
            self.updateValue();
          },
          (e) => {
            console.error('Error loading timework data', e);
            self.updateValue();
          }
        );
      } catch (e) {
        console.error('Error loading timework data', e);
        self.updateValue();
      }
    },
    _getAnswersAttr: function () {
      return this._answers;
    },
    loadTimeworks(timeworkIds) {
      return callMethod({
        url: 'Request.Survey.Capture.action',
        method: 'getTimeworks',
        params: [timeworkIds]
      });
    }
  };
  return declare([_WB, wt, Evented], StopwatchSelectorWidget);
});
