define([
  'dojo/dom-construct',
  'dojo/query',
  'dojo/_base/array',
  'dojo/dom',
  'dojo/dom-class',
  'bnext/misc',
  'dojo/dom-attr',
  'bnext/survey/_util/dateSelectorUtils',
  'bnext/administrator/surveys/CurrencyConversionFieldsValidator',
  'dojo/date/locale',
  'bnext/_base/NumberUtils',
  'bnext/survey/_util/answerFunctions',
  'dijit/registry',
  'bnext/administrator/surveys/SurveyCaptureDomUtils'
], (
  domConstruct,
  query,
  array,
  dom,
  domClass,
  misc,
  domAttr,
  dateSelectorUtils,
  CurrencyConversionFieldsValidator,
  locale,
  NumberUtils,
  answerFunctions,
  registry,
  SurveyCaptureDomUtils
) => {
  const tool = new misc();

  function hasHeaderValue(value, node) {
    return query(`input[name=headerValue][value="${value}"]`, node).length !== 0;
  }

  function getCeDiv(container) {
    return query('.contenteditable', container)[0];
  }

  function getAllCeDiv(container, attributes) {
    let selector = '.contenteditable';
    if (attributes !== null && typeof attributes !== 'undefined' && attributes !== '') {
      selector += attributes;
    }
    return query(selector, container);
  }

  function exclusiveSelectArrayForEach(radio, column, id) {
    if (hasHeaderValue(column, radio.parentNode)) {
      const disabled = radio.disabled;
      radio.disabled = false;
      radio.click();
      answerFunctions.getDbId(radio.parentNode.parentNode).value = id;
      radio.disabled = disabled;
    }
  }

  function multiSelectArrayForEach(check, column, id) {
    if (hasHeaderValue(column, check.parentNode)) {
      const disabled = check.disabled;
      check.disabled = false;
      check.click();
      answerFunctions.getDbId(check.parentNode.parentNode).value = id;
      check.disabled = disabled;
    }
  }

  function tableFieldForEach(input, column, id, descripcion) {
    try {
      if (!hasHeaderValue(column, input.parentNode)) {
        return;
      }
      const cell = getCeDiv(input.parentNode);
      cell.innerHTML = descripcion;
      const textareaEditor = dom.byId(`${cell.id}_readonlyText`);
      if (textareaEditor) {
        textareaEditor.innerHTML = descripcion;
      }
      answerFunctions.getDbId(input.parentNode).value = id;
    } catch (e) {
      console.error(`Error setting table field value for column ${column} and id ${id}`, e);
    }
  }

  function updateCurrencyField(regularFieldDom, respuesta) {
    SurveyCaptureDomUtils.updateCurrencyDisplayField(regularFieldDom);
    const currencyFieldDom = dom.byId(`${regularFieldDom.id}_currencyConversion`);
    if (!currencyFieldDom) {
      return;
    }
    let currencyRespuesta = respuesta;
    if (!currencyRespuesta) {
      currencyRespuesta = {
        exchangeRateId: null,
        exchangeRateSource: null,
        exchangeConversionRate: null,
        exchangeRateDate: null
      };
    }
    CurrencyConversionFieldsValidator.updateCurrencyFieldValue(currencyFieldDom, regularFieldDom, currencyRespuesta);
    SurveyCaptureDomUtils.updateCurrencyDisplayField(currencyFieldDom);
  }
  function answerAssignFillOutDate(input, respuesta, container) {
    try {
      let fillOutDateContainer;
      const hasFillOutData = domAttr.get(input, 'data-show-fill-out-date') === '1';
      if (hasFillOutData) {
        const fillOutContainerResults = query('.field-show-fill-out-date', container);
        fillOutDateContainer = fillOutContainerResults?.length > 0 ? fillOutContainerResults[0] : null;
      } else {
        fillOutDateContainer = null;
      }
      if (!fillOutDateContainer) {
        return;
      }
      let fechaCapturaDateFormatted = null;
      if (respuesta.descripcion !== null && typeof respuesta.descripcion !== 'undefined' && respuesta.descripcion !== '') {
        if (respuesta.fechaCaptura) {
          const fechaCapturaDate = tool.parseDateFromJson(respuesta.fechaCaptura);
          if (fechaCapturaDate !== null && typeof fechaCapturaDate !== 'undefined') {
            fechaCapturaDateFormatted = locale.format(fechaCapturaDate, {
              selector: 'date',
              datePattern: 'dd/MM/yyyy HH:mm:ss'
            });
            assignFillOutDateDom(fechaCapturaDateFormatted, fillOutDateContainer);
          }
        } else {
          assignFillOutDateDom(fechaCapturaDateFormatted, fillOutDateContainer);
        }
      } else {
        assignFillOutDateDom(fechaCapturaDateFormatted, fillOutDateContainer);
      }
    } catch (e) {
      console.error(`Failed to assign fill out date to text field${fillOutId}`, e);
    }
  }
  function assignFillOutDateDom(dateString, fillOutDateContainer) {
    if (dateString === null || typeof dateString === 'undefined') {
      fillOutDateContainer.innerHTML = '';
      domClass.add(fillOutDateContainer, 'displayNone');
    } else {
      fillOutDateContainer.innerHTML = dateString;
      domClass.remove(fillOutDateContainer, 'displayNone');
    }
  }

  const loadFunctions = {
    debug: false,
    textField: (data, container, id, isTextFieldArray) => {
      const inputs = getAllCeDiv(container, '[data-is-regular-field=true][data-has-plain-value=true]');
      const dbId = answerFunctions.getAllDbId(container);
      let total = 0;
      let hasSummation = false;
      let hasSummationValid = false;
      if (data.respuesta.length) {
        for (let i = 0, l = inputs.length; i < l; i++) {
          const input = inputs[i];
          let respuesta = data.respuesta[i];
          let inputContainer = null;
          if (isTextFieldArray) {
            const optionId = +domAttr.get(input, 'data-id');
            respuesta = data.respuesta.filter((r) => r.opcionId === optionId);
            if (respuesta?.length > 0) {
              respuesta = respuesta[0];
            } else {
              respuesta = false;
            }
            inputContainer = input.parentNode;
          } else {
            inputContainer = container;
          }
          if (!respuesta) {
            if (loadFunctions.debug) {
              console?.warn('---- textField sin respuesta');
              console?.warn(data);
              console?.warn(container);
            }
            continue;
          }
          if (respuesta.descripcion !== null && typeof respuesta.descripcion !== 'undefined') {
            input.value = respuesta.descripcion;
            const textareaEditor = dom.byId(`${input.id}_readonlyText`);
            if (textareaEditor) {
              textareaEditor.innerHTML = input.value;
            }
            updateCurrencyField(input, respuesta);
          } else {
            input.value = null;
            updateCurrencyField(input, null);
          }
          const hasSummationField = domClass.contains(input, 'sumfield');
          if (hasSummationField) {
            let numValue;
            if (input.value !== null && typeof input.value !== 'undefined' && input.value !== '') {
              numValue = Number(input.value);
            } else {
              numValue = Number.NaN;
            }
            if (!Number.isNaN(numValue)) {
              total += numValue;
              hasSummationValid = true;
            }
            hasSummation = true;
          }
          dbId[i].value = respuesta.id;
          SurveyCaptureDomUtils.autoresize(input, 50);
          answerAssignFillOutDate(input, respuesta, inputContainer);
        }
      }
      if (hasSummation) {
        const summationField = inputs[inputs.length - 1];
        if (hasSummationValid) {
          summationField.value = NumberUtils.redondeoTwoDecimal(total);
        } else {
          summationField.value = null;
        }
        SurveyCaptureDomUtils.autoresize(summationField, 50);
        SurveyCaptureDomUtils.buildTextRichTextFields(summationField);
      }
    },
    textFieldArray: function (data, container, id) {
      this.textField(data, container, id, true);
    },
    formWeightingResult: function (data, container, id) {
      this.textField(data, container, id, false);
    },
    exclusiveSelect: (data, container, id) => {
      const respuesta = data.respuesta[0];
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- exclusiveSelect sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
        return;
      }
      if (respuesta.opcion) {
        const radio = query(`input[value="${respuesta.opcion.id}"]`, container)[0];
        const disabled = radio.disabled;
        radio.disabled = false;
        radio.click();
        radio.disabled = disabled;
      } else {
        const radios = query('input', container);
        for (const radio of radios) {
          if (!radio.disabled) {
            radio.checked = false;
            SurveyCaptureDomUtils.toogleOtherOption(radio);
          }
        }
      }
      answerFunctions.getDbId(container.parentNode).value = respuesta.id;
      const element = getCeDiv(container);
      if (element) {
        if (element instanceof HTMLTextAreaElement || element instanceof HTMLInputElement || element instanceof HTMLSelectElement) {
          element.value = respuesta.descripcion || '';
        } else {
          element.innerHTML = respuesta.descripcion || '';
        }
        let fieldContainer;
        if (data.parentFieldId) {
          fieldContainer = dom.byId(`field_${data.parentFieldId}`);
        } else {
          fieldContainer = dom.byId(id);
        }
        SurveyCaptureDomUtils.buildTextRichTextFields(element, fieldContainer);
      }
    },
    exclusiveSelectYesNo: function (data, container, id) {
      this.exclusiveSelect(data, container, id);
      if (!data.respuesta[0] && loadFunctions.debug) {
        console?.warn('---- exclusiveSelectYesNo sin respuesta');
        console?.warn(data);
        console?.warn(container);
      }
    },
    exclusiveSelectArray: (data, container, id) => {
      if (!data.respuesta?.length) {
        return;
      }
      for (const respuesta of data.respuesta) {
        if (respuesta.opcion && respuesta.columna) {
          const value = respuesta.opcion.id;
          const column = respuesta.columna.id;
          for (const radio of query(`input[type=radio][value="${value}"]`, container)) {
            exclusiveSelectArrayForEach(radio, column, respuesta.id);
          }
        } else {
          const radios = query('input[type=radio]', container);
          for (const radio of radios) {
            if (!radio.disabled) {
              radio.checked = false;
            }
          }
        }
      }
    },
    multiSelectArray: (data, container, id) => {
      if (!data.respuesta?.length) {
        return;
      }
      for (const respuesta of data.respuesta) {
        if (respuesta.opcion && respuesta.columna) {
          const value = respuesta.opcion.id;
          const column = respuesta.columna.id;
          for (const check of query(`input[type=checkbox][value="${value}"]`, container)) {
            multiSelectArrayForEach(check, column, respuesta.id);
          }
        } else {
          const checks = query('input[type=checkbox]', container);
          for (const check of checks) {
            if (!check.disabled) {
              check.checked = false;
            }
          }
        }
      }
    },
    menuSelect: (data, container, id) => {
      const select = query('select', container)[0];
      const respuesta = data.respuesta[0];
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- menuSelect sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
        return;
      }
      if (respuesta.opcion) {
        select.value = respuesta.opcion.id;
      } else {
        const options = query('option', select);
        select.value = options[0].value;
      }
      const dbId = answerFunctions.getDbId(container);
      dbId.value = respuesta.id;
    },
    dateSelector: (data, containerAns, id) => {
      const dateInput = query('.date-widget input[type=text]', containerAns)[0];
      let respuesta = data.respuesta[0];
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- dateSelector sin respuesta');
          console?.warn(data);
          console?.warn(containerAns);
        }
        return;
      }
      const field = dom.byId(`field_${data.field.id}`);
      const time = respuesta.descripcion;
      const date = tool.parseDateTime(time);
      let dateString = null;
      if (date !== null && typeof date !== 'undefined') {
        dateString = locale.format(date, {
          selector: 'date',
          datePattern: 'dd/MM/yyyy'
        });
      }
      domClass.remove(field, 'defaultValue');
      dateInput.value = dateString;
      answerFunctions.getDbId(containerAns).value = respuesta.id;
      const dateData = dateSelectorUtils.getDateData(dateInput);
      if (dateData.includeTime) {
        respuesta = data.respuesta[0];
        if (respuesta?.descripcion) {
          const dateWithTime = locale.parse(respuesta.descripcion, {
            selector: 'date',
            datePattern: 'dd/MM/yyyy HH:mm:ss'
          });
          respuesta.descripcion = tool.formatDateTime(dateWithTime, "yyyy-MM-dd'T'HH:mm:ss");
        }
        loadFunctions.timeSelector(data, containerAns, id);
      }
    },
    timeSelector: (data, containerAns, id) => {
      let timeInput = query('.time-widget input[type=text]', containerAns)[0];
      if (!timeInput) {
        timeInput = query('.time-widget input[type=time]', containerAns)[0];
      }
      const respuesta = data.respuesta[0];
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- timeSelector sin respuesta');
          console?.warn(data);
          console?.warn(containerAns);
        }
        return;
      }
      const field = dom.byId(`field_${data.field.id}`);
      const time = tool.parseTimeFromJson(respuesta.descripcion);
      let timeString = null;
      if (time !== null && typeof time !== 'undefined') {
        timeString = locale.format(time, {
          selector: 'date',
          datePattern: 'HH:mm'
        });
      }
      domClass.remove(field, 'defaultValue');
      timeInput.value = timeString;
      answerFunctions.getDbId(containerAns).value = respuesta.id;
    },
    /**
     * Set the stopwatch selector widget data
     *
     * @param {Object} data - The data to load into the widget.
     * @param {HTMLElement} containerAns - The container element for the widget.
     * @param {string} id - The unique identifier of the widget.
     */
    stopwatchSelector: (data, containerAns, id) => {
      if (data.respuesta.length === 0) {
        if (loadFunctions.debug) {
          console?.warn('---- stopwatchSelector sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
        return;
      }
      /**
       * @type {StopwatchSelectorWidget}
       */
      const widget = registry.byId(`${id}_stopwatchSelectorWidget`);
      if (widget) {
        widget.set('answers', data.respuesta);
      } else {
        console.error('StopwatchSelectorWidget not found', id);
      }
    },
    fileUpload: (data, container) => {
      const respuesta = data.respuesta[0];
      if (!respuesta) {
        require(['bnext/administrator/surveys/SurveyCaptureFields'], (SurveyCaptureFields) => {
          SurveyCaptureFields.missingFile(container.parentNode.parentNode);
        });
        return;
      }
      for (const r of data.respuesta) {
        if (!r) {
          if (loadFunctions.debug) {
            console?.warn('---- fileUpload sin respuesta');
            console?.warn(data);
            console?.warn(container);
          }
          continue;
        }
        r.jsonEntityData = {};
        r.jsonEntityData.description = r.descripcion;
        r.jsonEntityData.id = `${r.fileId || '-1'}`;
        r.jsonEntityData.contentType = r.fileContentType;
        r.jsonEntityData.extension = r.fileExtension;
        r.id = `${r.fileId || '-1'}`;
      }
      answerFunctions.getDbId(container).value = respuesta.persistentId || respuesta.id;
      const descripcion = respuesta.descripcion;
      if (descripcion && descripcion !== '') {
        require(['bnext/administrator/surveys/SurveyCaptureFields'], (SurveyCaptureFields) => {
          SurveyCaptureFields.onFileChanged(container.parentNode.parentNode, data.respuesta);
        });
      } else {
        require(['bnext/administrator/surveys/SurveyCaptureFields'], (SurveyCaptureFields) => {
          SurveyCaptureFields.missingFile(container.parentNode.parentNode);
          SurveyCaptureFields.clearFile(container.parentNode.parentNode);
        });
      }
    },
    multiSelect: (data, container, id) => {
      let notFoundAnswer = false;
      if (data.respuesta?.length) {
        for (const respuesta of data.respuesta) {
          if (respuesta.opcion) {
            const check = query(`input[value="${respuesta.opcion.id}"]`, container)[0];
            const disabled = check.disabled;
            check.disabled = false;
            check.click();
            check.disabled = disabled;
            answerFunctions.getDbId(container).value = respuesta.id;
          } else {
            notFoundAnswer = true;
          }
        }
      }
      if (!data.respuesta[0]) {
        notFoundAnswer = true;
      }
      const otherContainer = getCeDiv(container);
      let fieldContainer;
      if (data.parentFieldId) {
        fieldContainer = dom.byId(`field_${data.parentFieldId}`);
      } else {
        fieldContainer = dom.byId(id);
      }
      if (notFoundAnswer) {
        if (loadFunctions.debug) {
          console?.warn('---- multiSelect sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
        const checks = query('input', container);
        for (const check of checks) {
          if (!check.disabled) {
            check.checked = false;
          }
        }
        answerFunctions.getDbId(container).value = null;
        if (otherContainer) {
          otherContainer.innerHTML = '';
          SurveyCaptureDomUtils.buildTextRichTextFields(otherContainer, fieldContainer);
        }
      } else {
        if (otherContainer) {
          otherContainer.innerHTML = data.respuesta[0].descripcion || '';
          SurveyCaptureDomUtils.buildTextRichTextFields(otherContainer, fieldContainer);
        }
      }
    },
    tableField: (data, container, id) => {
      if (!data.respuesta?.length) {
        if (loadFunctions.debug) {
          console?.warn('---- tableField sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
      }
      try {
        for (const respuesta of data.respuesta) {
          if (respuesta.opcion && respuesta.columna) {
            const value = respuesta.opcion.id;
            const column = respuesta.columna.id;
            for (const input of query(`input[value="${value}"]`, container)) {
              tableFieldForEach(input, column, respuesta.id, respuesta.descripcion);
            }
          } else {
            for (const item of query('input[id^=dbId_]', container)) {
              item.value = null;
            }
            for (const item of query('.contenteditable', container)) {
              item.innerHTML = null;
            }
          }
        }
      } catch (e) {
        console.error('Error setting table field value', e);
      }
    },
    catalogSelect: (data, container, id) => {
      if (data.respuesta.length === 0) {
        if (loadFunctions.debug) {
          console?.warn('---- catalogSelect sin respuesta');
          console?.warn(data);
          console?.warn(container);
        }
        return;
      }
      const answerField = dom.byId(id);
      const subtype = domAttr.get(answerField, 'data-catalog-subtype');
      switch (subtype) {
        case 'catalog': {
          const select = query('select', container)[0];
          catalogSelectSingleLoad(data, container, select);
          break;
        }
        case 'catalog-multiple':
          catalogSelectMultipleLoad(data, container, id);
          break;
        case 'catalog-hierarchy':
          catalogSelectCatalogHierarchyLoad(data, container, id);
          break;
      }
    },
    signature: (data, container, id) => {},
    handwrittenSignature: (data, containerAns, id) => {
      const input = query('input', containerAns)[0];
      const canvas = query('canvas', containerAns)[0];
      const clearBtn = dom.byId(`${canvas.id}_clearBtn`);
      const sigImage = dom.byId(`${canvas.id}_sigImage`);
      const contentDom = dom.byId(`${canvas.id}_content`);
      const respuesta = data.respuesta[0];
      const requestModePreview = dom.byId('requestMode').value === 'PREVIEW';
      const mainField = dom.byId(id);
      const unavailable = domClass.contains(mainField, 'unavailable-field');
      if (requestModePreview) {
        domClass.add(clearBtn, 'displayNone');
        const signBtn = dom.byId(`${canvas.id}_signBtn`);
        domClass.add(signBtn, 'displayNone');
      } else if (!unavailable) {
        domClass.add(sigImage, 'displayNone');
      } else {
        domClass.add(canvas, 'displayNone');
        domClass.add(clearBtn, 'displayNone');
        domClass.remove(sigImage, 'displayNone');
      }
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- handwrittenSignature sin respuesta');
          console?.warn(data);
          console?.warn(containerAns);
        }
        if (requestModePreview) {
          domClass.add(contentDom, 'accepted-canvas');
        }
        return;
      }
      if (respuesta.descripcion !== null && typeof respuesta.descripcion !== 'undefined') {
        input.value = respuesta.descripcion || input.value;
        const dataUrl = respuesta.imageBase64;
        if (dataUrl) {
          sigImage.setAttribute('src', dataUrl);
          // URI TO CANVAS
          const img = new window.Image();
          img.addEventListener('load', () => {
            canvas.getContext('2d').drawImage(img, 0, 0);
          });
          img.addEventListener('error', () => {
            sigImage.setAttribute('src', '');
          });
          img.setAttribute('src', dataUrl);
          domClass.add(contentDom, 'accepted-canvas');
          domClass.add(canvas, 'modified');
          if (!unavailable && !requestModePreview) {
            // Si tiene permiso de editar, habilita botones
            domClass.remove(contentDom, 'disabled-field');
            domClass.remove(contentDom, 'fully-disabled');
          }
        }
      }
    },
    geolocation: (data, containerAns, id) => {
      const respuesta = data.respuesta[0];
      if (!respuesta) {
        if (loadFunctions.debug) {
          console?.warn('---- geolocation sin respuesta');
          console?.warn(data);
          console?.warn(containerAns);
        }
        return;
      }
      const coords = respuesta.descripcion.replace('[GPS]:', '').split(',');
      const labelLatitude = dom.byId(`${id}_latitude`);
      const labelLongitude = dom.byId(`${id}_longitude`);
      if (labelLatitude && labelLongitude) {
        labelLatitude.innerHTML = coords[0];
        labelLongitude.innerHTML = coords[1];
      }
    }
  };

  function catalogSelectSingleLoad(data, container, select) {
    if (!data.respuesta?.length) {
      if (loadFunctions.debug) {
        console?.warn('---- catalogSelectSingle sin respuesta');
        console?.warn(data);
        console?.warn(container);
        return;
      }
    }
    for (const answer of data.respuesta) {
      catalogSelectLoad(answer, select);
    }
    if (!data.respuesta.length) {
      return;
    }
    const respuesta = data.respuesta[0];
    let selectedValue = respuesta.catalogOptionValue;
    if (typeof selectedValue === 'undefined' || selectedValue === null) {
      selectedValue = '';
    }
    select.value = selectedValue;
    if (select.value !== selectedValue) {
      const options = query('option', select);
      if (options && options.length > 0) {
        select.value = options[0].value;
      } else {
        console.error('No options available at `select`:', data, select, container, new Error().stack);
      }
    }
    select.dispatchEvent(new Event('bnextChange'));
    const dbId = answerFunctions.getDbId(container);
    dbId.value = respuesta.id;
  }

  function createCatalogMultipleOption(fieldIdOwner, respuesta, answerIndex) {
    if (!fieldIdOwner) {
      console.error(`Missing fieldIdOwner! answerIndex:${answerIndex}`);
      console.error(respuesta);
      return;
    }
    const fieldId = domAttr.get(fieldIdOwner, 'field-id');
    const options = query('input[type="checkbox"]', fieldIdOwner);
    let classes;
    if (options && options.length > 0) {
      classes = domAttr.get(options[0], 'classes');
    } else {
      classes = domAttr.get(fieldIdOwner, 'classes');
    }
    const container = domConstruct.create(
      'div',
      {
        class: 'vertical row grid-x'
      },
      fieldIdOwner
    );
    const inputContainer = domConstruct.create(
      'div',
      {
        class: 'cell small-1'
      },
      container
    );
    const labelContainer = domConstruct.create(
      'div',
      {
        class: 'cell small-11',
        innerHTML: '<h4></h4>'
      },
      container
    ).firstChild;
    domConstruct.create(
      'input',
      {
        type: 'checkbox',
        id: `row_${fieldId}_${answerIndex}`,
        name: `row_${fieldId}_${answerIndex}`,
        value: respuesta.catalogOptionValue,
        class: classes,
        checked: 'checked'
      },
      inputContainer
    );
    domConstruct.create(
      'label',
      {
        for: `row_${fieldId}_${answerIndex}`,
        innerHTML: respuesta.catalogOptionLabel
      },
      labelContainer
    );
  }

  function catalogSelectCatalogHierarchyLoad(data, container, id) {
    const widget = registry.byId(`${id}_hierarchy`);
    if (widget) {
      widget.set('answers', data.respuesta);
      widget.updateValue();
    } else {
      if (!window.catalogDataAnswers) {
        window.catalogDataAnswers = {};
      }
      window.catalogDataAnswers[id] = data.respuesta;
    }
  }

  function catalogSelectMultipleLoad(data, container) {
    let numberInputs = query('input[type=checkbox]', container).length;
    const fieldIdContainer = query('[field-id]', container)[0];
    const autoSelectSingleAnswer = domAttr.get(fieldIdContainer, 'data-autoSelectSingleAnswer') === 'true';
    let singleAnswer = numberInputs === 1;
    for (const input of query('input', container)) {
      domClass.remove(input.parentNode, 'displayNone');
      if (autoSelectSingleAnswer && singleAnswer) {
        if (!input.checked) {
          input.click();
        }
      } else {
        domAttr.remove(input, 'disabled');
        if (input.checked) {
          input.click();
        }
      }
    }
    const answerIndex = numberInputs + 1;
    if (data.respuesta?.length) {
      for (const respuesta of data.respuesta) {
        const check = query(`input[type=checkbox][value="${respuesta.catalogOptionValue}"]`, container)[0];
        if (check) {
          const inputLabel = query(`label[for="${check.id}"]`, container)[0];
          inputLabel.innerHTML = respuesta.catalogOptionLabel;
          const disabled = check.disabled;
          check.disabled = false;
          if (!check.checked) {
            check.click();
          }
          check.disabled = disabled;
        } else {
          createCatalogMultipleOption(fieldIdContainer, respuesta, answerIndex);
          numberInputs++;
        }
      }
    }
    if (autoSelectSingleAnswer) {
      singleAnswer = query('input[type=checkbox]', container).length === 1;
      if (singleAnswer) {
        for (const input of query('input[type=checkbox]', container)) {
          domClass.add(input.parentNode, 'displayNone');
          if (!input.checked) {
            input.click();
          }
        }
      }
    }
    if (!data.respuesta[0]) {
      if (loadFunctions.debug) {
        console?.warn('---- catalogSelectMultiple sin respuesta');
        console?.warn(data);
        console?.warn(container);
      }
    }
  }

  function getSelectOptionsByValue(select, selectedValue) {
    const allOptions = query('option', select);
    const options = [];
    for (const option of allOptions) {
      const value = option.value;
      if (value === null || typeof value === 'undefined' || value === '') {
        continue;
      }
      const key = value?.trim();
      if (key !== selectedValue) {
        continue;
      }
      options.push(option);
    }
    return options;
  }

  function catalogSelectLoad(respuesta, select) {
    let selectedValue = respuesta.catalogOptionValue;
    let selectedLabel = respuesta.catalogOptionLabel;
    if (typeof selectedValue === 'undefined') {
      selectedValue = null;
    }
    if (selectedLabel === null || typeof selectedLabel === 'undefined') {
      selectedLabel = '';
    }
    if (selectedValue !== null) {
      selectedValue = selectedValue.trim();
    }
    selectedLabel = selectedLabel.trim();
    const options = getSelectOptionsByValue(select, selectedValue);
    if (options.length > 0) {
      for (const option of options) {
        option.innerHTML = selectedLabel;
      }
    } else if (selectedValue !== null) {
      domConstruct.create(
        'option',
        {
          value: selectedValue,
          innerHTML: selectedLabel
        },
        select
      );
    }
    const autoSelectSingleAnswer = domAttr.get(select, 'data-autoSelectSingleAnswer') === 'true';
    if (!autoSelectSingleAnswer || select.options.length === 2) {
      domClass.remove(select, 'readonly');
    }
  }
  return loadFunctions;
});
