define([
    'dojo/dom',
    'dojo/dom-construct',
    'bnext/survey/lang/languages',
    'bnext/survey/formObject/ArrayFormObject',
    'bnext/survey/_code/SurveyTypeRuleDefinition',
    'bnext/survey/formObject/HtmlTextFieldArray',
    'bnext/survey/_util/jsFailureExposure!',
    'bnext/survey/ext/eventExtend!'
],
        function(dom, domConstruct, lang, arrayFormObject, sTypeRules, htmlTextFieldArray) {
            
            /**
             * Listado de textos abiertos (Renglones)
             */
            var textFieldArray = arrayFormObject.extend({
                constructor: function() {
                    this.base();
                    this.columns = 1;
                    this.type = "textFieldArray";
                    this.size = "large";
                    this.layout = "vertical";
                    this.requiredProperties = sTypeRules.fieldTextFieldArrayRequiredProperties;
                },
                setSchema: function(obj) {
                    this.base(obj);
                    this.type = "textFieldArray";
                    this.size = (obj && obj.size) ? obj.size : "large";
                    this.columns = 1;
                    this.applySize(this.size);
                },
                render: function (parentId) {
                    this.base(parentId);
                    this.applySize(this.size);
                },
                getSchema: function() {
                    var item = this.base();
                    if (this.size != undefined)
                        item.size = this.size;
                    return item;
                },
                buildNewItem: function() {
                    return(new htmlTextFieldArray(this.columns, this.size));
                },
                showProperties: function() {
                    this.base();
                    if (this.formArea !== null) {
                        this.renderProperty(
                            lang.current("size"), 
                            "size", {
                                small: lang.current("small"),
                                large: lang.current("large")
                            }, 
                            'vertical'
                        );
                    }
                },
                setProp: function(prop, value) {
                    this[prop] = value;
                    if (prop === 'size')
                        this.applySize(value);
                    this.refresh();
                }
            });
            
            return textFieldArray;
        });