define([
    'bnext/module/GridField',
    'dojo/dom',
    'dojo/dom-class',
    'dojo/query',
    'bnext/survey/_code/page',
    'bnext/survey/formObject/PropertiesDiv',
    'bnext/survey/formObject/ValidAlert',
    'bnext/survey/_util/jsFailureExposure!',
    'dojo/domReady!'
],
function (
    GridField, dom, domClass, query,
    page, PropertiesDiv, isValidAlert
) {
    /**
     * SummationProperty.js
     */
    var SummationProperty = new GridField({
        id: 'summationProperty',
        tag: 'SummationProperty',
        wrapperId: '_summation_propertyWrapper',
        mode: 'multiple',
        floatingGridSize: dom.byId('floatingGridSize').value,
        _getValueDataAttr: function() {
            if (!this.baseGrid) {
                return [];
            }
            var data = this.baseGrid.getBean().data || [];
            return data;
        },
        _isValidSummationData: function(valueData) {
            var valid = valueData?.length > 0;
            return valid;
        },
        _validateSummationData: function(valueData, changes) {
            if (changes) {
                const targetNode  = query('#' + this.fieldId + this.wrapperId + ' #' + this.id);
                if (!changes.newSelection || changes.newSelection.length === 0) {
                    targetNode.forEach(function (e) {
                        domClass.add(e,'classic-yellow');
                    });
                } else {
                    targetNode.forEach(function (e) {
                        domClass.remove(e,'classic-yellow');
                    });
                }
            }
            isValidAlert({'classic-yellow': true, context: this.fieldId + "_uiObject"});
        },
        onOpenAddClick: function() {
            page.form.destroyCurrentAnswerSelector();
        },
        validateSummationData: function(changes) {
            var value = this.get('valueData');
            this._validateSummationData(value, changes);
        },
        updateSummationData: function(items, selected) {
            return this.update(items, selected);
        },
        syncSummationData: function(items, selected) {
            return this.syncData(items, selected);
        },
        onChange: function(changes) {
            page.form.destroyCurrentAnswerSelector();
            const fieldDom = dom.byId(this.fieldId);
            if (!fieldDom) {
                return;
            }
            if (!this.baseGrid) {
                return;
            }
            if (changes.added && changes.added.length > 0) {
                page.form.addRowsConditionalFields(this.fieldId, changes.added);
            }
            if (changes.removed && changes.removed.length > 0) {
                page.form.removeConditionalFields(this.fieldId, changes.removed);
            }
            this.validateSummationData(changes);
        }
    }, 'summationProperty');
    return SummationProperty;
});