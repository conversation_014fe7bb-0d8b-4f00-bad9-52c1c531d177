define([
  'bnext/survey/formObject/_formObject',
  'bnext/survey/_code/page',
  'bnext/survey/_base/$SV',
  'dojo/dom-attr',
  'dojo/dom-class',
  'dojo/_base/array',
  'dojo/dom',
  'dojo/query',
  'dojo/_base/lang',
  'dijit/registry',
  'dojo/dom-construct',
  'bnext/survey/formObject/ValidAlert',
  'bnext/survey/formObject/_SearchableFieldGrid',
  'bnext/survey/formObject/GridProperty',
  'bnext/survey/_code/SurveyTypeRuleDefinition',
  'bnext/survey/lang/languages',
  'bnext/survey/formObject/ConditionalExpirationFieldProperty',
  'bnext/survey/formObject/ConditionalFieldProperty',
  'bnext/survey/_util/jsFailureExposure!'
], (
  formObject,
  page,
  $SV,
  domAttr,
  domClass,
  array,
  dom,
  query,
  dojoLang,
  registry,
  domConstruct,
  isValidAlert,
  _SearchableFieldGrid,
  GridProperty,
  sTypeRules,
  i18n,
  ConditionalExpirationFieldProperty,
  ConditionalFieldProperty
) => {
  const seccion = formObject.extend({
    constructor: function () {
      this.base();
      this.type = 'seccion';
      this.subTitle = null;
      this.title.cssClass = 'seccion';
      this.requiredProperties = sTypeRules.fieldSeccionRequiredProperties;
      this.validSectionProperties = ['nobody', 'requestor', 'boss', 'business-unit-position', 'organizational-unit-position', 'user', 'user-to-be-defined'];
      this.editableSections = [];
      this.defineFieldProperty('canCancel', 'f');
      this.defineFieldProperty('signatureType', null);
      this.defineFieldProperty('daysToWait', null);
      this.defineFieldProperty('daysToExpire', null);
      this.defineFieldProperty('daysToNotifyBeforeExpiration', null);
      this.defineFieldProperty('includeInMail', null);
      this.defineFieldProperty('includeInSubject', null);
      this.defineFieldProperty('requestAdjust', false);
      this.fillEntityEditable = 1;
      this.daysToExpireLabel = '0';
    },
    getSchema: function () {
      const item = this.base();
      if (typeof this.validation !== 'undefined') {
        item.size = this.size;
      }
      if (typeof item.includeInMail === 'undefined') {
        item.includeInMail = 1;
      }
      item.fillEntity = this.fillEntity || null;
      item.attendProgressStateId = this.attendProgressStateId && this.attendProgressStateId !== -1 ? this.attendProgressStateId : null;
      item.partialProgressStatuses = this.partialProgressStatuses || [];
      item.cancelProgressStatuses = this.cancelProgressStatuses || [];
      item.stage = this.stage || null;
      item.rejectProgressStatuses = this.rejectProgressStatuses || [];
      item.fillEntityEditable = this.fillEntityEditable;
      //Textos
      item.businessUnitPosition = this['business-unit-position'] || null;
      item.organizationalUnitPosition = this['organizational-unit-position'] || null;
      item.user = this.user || null;
      //IDs
      item.businessUnitPositionId = this['business-unit-position-entity-value'] || null;
      item.organizationalUnitPositionId = this['organizational-unit-position-entity-value'] || null;
      item.userId = this['user-entity-value'] || null;
      const validation = $SV(`${this.id}_seccion`).value;
      if (validation && validation[0] === '_') {
        item.validation = validation;
      } else if (typeof this.validation !== 'undefined') {
        item.validation = this.validation;
      }
      item.editableSections = this.getEditableSections();
      return item;
    },
    initialize: function (fieldConfig) {
      this.base(fieldConfig);
      this.fillEntityEditable = fieldConfig.fillEntityEditable;
      if (fieldConfig.fillEntity !== null) {
        this.fillEntity = fieldConfig.fillEntity;
      }
      if (fieldConfig.attendProgressStateId !== null) {
        this.attendProgressStateId = fieldConfig.attendProgressStateId;
      }
      if (fieldConfig.partialProgressStatuses !== null) {
        this.partialProgressStatuses = fieldConfig.partialProgressStatuses;
      }
      if (fieldConfig.cancelProgressStatuses !== null) {
        this.cancelProgressStatuses = fieldConfig.cancelProgressStatuses;
      }
      if (fieldConfig.stage !== null) {
        this.stage = fieldConfig.stage;
      }
      if (fieldConfig.rejectProgressStatuses !== null) {
        this.rejectProgressStatuses = fieldConfig.rejectProgressStatuses;
      }
    },
    getEditableSections: function () {
      const sections = this.getConfigurableSections();
      const gridValue = GridProperty.syncData(sections, this.editableSections);
      this.editableSections = gridValue?.value ?? [];
      return this.editableSections;
    },
    setSchema: function (obj) {
      this.base(obj);
      this.size = obj?.size ? obj.size : 'large';
      this.validation = obj?.validation ? obj.validation : 0;
      this.validation = 0;
      this.fillEntity = obj.fillEntity || null;
      this.attendProgressStateId = obj.attendProgressStateId || null;
      const partialProgressStatuses = (obj.partialProgressStatuses || []).filter((item) => item?.id).map((item) => item.id.formProgressStateId);
      this.partialProgressStatuses = partialProgressStatuses;
      const cancelProgressStatuses = (obj.cancelProgressStatuses || []).filter((item) => item?.id).map((item) => item.id.formProgressStateId);
      this.cancelProgressStatuses = cancelProgressStatuses;
      this.stage = obj.stage || null;
      const rejectProgressStatuses = (obj.rejectProgressStatuses || []).filter((item) => item?.id).map((item) => item.id.formProgressStateId);
      this.rejectProgressStatuses = rejectProgressStatuses;
      this.fillEntityEditable = obj.fillEntityEditable;
      //Textos
      this['business-unit-position'] = obj.businessUnitPosition || null;
      this['organizational-unit-position'] = obj.organizationalUnitPosition || null;
      this.user = obj.user || null;
      //IDs
      this['business-unit-position-entity-value'] = obj.businessUnitPositionId || null;
      this['organizational-unit-position-entity-value'] = obj.organizationalUnitPositionId || null;
      this['user-entity-value'] = obj.userId || null;
      if (this.getDaysToExpire() > 0) {
        this.daysToExpireLabel = '1'; // <-- El valor de `daysToExpireLabel` no se guardá en BDD, se considera que es "Si" siempre que la expiración sea > 0
      }
      if (this.getIncludeInMail() > 0) {
        this.includeInMail = '1';
      }
      this.editableSections = this.loadEditableSections(obj);
    },
    loadEditableSections: (obj) => {
      const sections = [];
      const val = obj.surveyFieldObjectEditables;
      if (typeof val === 'undefined' || val === null) {
        return [];
      }
      array.forEach(obj.surveyFieldObjectEditables, (section) => {
        sections.push(section.fieldId);
      });
      return sections;
    },
    render: function (parentId) {
      this.base(parentId);
      const parent = $SV(this.id);
      const sectionNumberDom = $div({
        id: `${parent.id}_sectionId`,
        class: 'fieldNumber sectionId'
      });
      parent.insertBefore(sectionNumberDom, parent.firstChild);
      const container = $SV(`${this.id}_items`);
      const field = $input({
        id: `${this.id}_seccion`,
        type: 'hidden',
        size: '50',
        class: 'seccion',
        value: 'Sección',
        style: 'width:474px'
      });
      container.appendChild(field);
      this.select(this.isSelected);
    },
    getConfigurableSections: (seccionId) => {
      const sections = [];
      const formItems = page.form.formItems;
      array.some(formItems, (field) => {
        if (field.id === seccionId) {
          return true;
        }
        if (field.type === 'seccion') {
          const id = field.id;
          const order = dom.byId(`${id}_uiObject_sectionId`).textContent;
          sections.push({
            id: id,
            code: order,
            description: field.uiObject.getTitleText()
          });
        }
        return false;
      });
      return sections;
    },
    showProperties: sTypeRules.fieldSeccionProperties,
    onChangeCanCancel: function (evt, property, value) {
      const cancelStatusesId = this.getCheckeckMultiSelectWidgetId('cancelProgressStatuses');
      const cancelStatusesContainer = dom.byId(`propertyWrapper${cancelStatusesId}`);
      if (!cancelStatusesContainer) {
        return;
      }
      const cancelStatusesWidget = registry.byId(cancelStatusesId);
      const showCancelStatuses = value === 't';
      cancelStatusesWidget.required = showCancelStatuses;
      if (showCancelStatuses) {
        domClass.remove(cancelStatusesContainer, 'displayNone');
      } else {
        cancelStatusesWidget.set('value', []);
        domClass.add(cancelStatusesContainer, 'displayNone');
      }
      this.onChangeCheckedMultiSelectProperty(cancelStatusesWidget, cancelStatusesWidget.get('value'));
    },
    showEditableSections: function (sectionsDom) {
      let sections = sectionsDom;
      const parent = $SV('propertiesArea');
      this.renderProperty(
        i18n.current('canFillerCancel'),
        'canCancel',
        {
          t: i18n.current('yes'),
          f: i18n.current('no')
        },
        null,
        dojoLang.hitch(this, this.onChangeCanCancel)
      );
      sections = this.getConfigurableSections(this.parentId);
      GridProperty.setConfig({ configuredFieldName: 'editableSections', fieldId: this.parentId });
      GridProperty.placeAt(parent);
      GridProperty.update(sections, this.editableSections);
      this.showDaysToWaitProperties(sections); // -- Días para recordatorio de atraso -- //
    },
    showCanRequestAdjust: function () {
      const parent = $SV('propertiesArea');
      this.renderProperty(
        i18n.current('requestAdjust'),
        'requestAdjust',
        {
          true: i18n.current('yes'),
          false: i18n.current('no')
        },
        null,
        dojoLang.hitch(this, (evt, property, value) => {})
      );
    },
    daysToExpireTitle: () => i18n.current('daysToExpireSectionTitle'),
    includeInMailTitle: () => i18n.current('includeInMailTitle'),
    /**
     * Se manda llamar desde `fieldSeccionProperties`
     * @type
     */
    showDaysToExpireProperties: function (sectionsDom) {
      let sections = sectionsDom;
      sections = sections || this.getConfigurableSections(this.parentId);
      if (sections && sections.length < 1) {
        return;
      }
      // -- Días para expirar la sección/firma -- //
      const domContainer = domConstruct.create('div', { class: 'days-to-expire-properties' }, $SV('propertiesArea'));
      const toggleQuerySelector = '.daysToExpire-visible-toggle';
      this.daysToExpire = this.daysToExpire || '0';
      this.daysToNotifyBeforeExpiration = this.daysToNotifyBeforeExpiration || '0';
      this.renderSelectProperty(this.daysToExpireTitle(), 'daysToExpireLabel', {
        data: [
          { value: '1', label: i18n.current('yes') },
          { value: '0', label: i18n.current('no') }
        ],
        domContainer: domContainer,
        skipDefaultOption: true,
        required: true,
        onAfterChange: function (e) {
          if (e.target.value === '1') {
            // Se selecciiona "Si"
            for (const dom1 of query(toggleQuerySelector, dom.byId('properties'))) {
              domClass.remove(dom1, 'displayNone');
            }
          } else {
            for (const dom1 of query(toggleQuerySelector, dom.byId('properties'))) {
              domClass.add(dom1, 'displayNone');
            }
            this.daysToExpire = '0';
            this.daysToNotifyBeforeExpiration = '0';
            const selectConditionalExpiration = query('select[data-property-name=conditionalExpirationQuestion]', dom.byId('properties'))[0];
            selectConditionalExpiration.value = '0';
            this.onChangeConditionalQuestion(null, ConditionalExpirationFieldProperty.conditional.question, selectConditionalExpiration.value);
          }
        }
      });
      this.renderTextProperty(i18n.current('daysToExpire'), 'daysToExpire', false, {
        domContainer: domContainer,
        type: 'number',
        className: 'daysToExpire',
        min: '0'
      });
      this.renderTextProperty(i18n.current('daysToNotifyBeforeExpiration'), 'daysToNotifyBeforeExpiration', false, {
        domContainer: domContainer,
        type: 'number',
        className: 'daysToExpire',
        min: '0'
      });
      this.renderSelectProperty(i18n.current('conditionalExpirationQuestion'), 'conditionalExpirationQuestion', {
        data: [
          { value: '1', label: i18n.current('yes') },
          { value: '0', label: i18n.current('no') }
        ],
        domContainer: domContainer,
        skipDefaultOption: true,
        required: true,
        onAfterChange: this.onChangeConditionalQuestion,
        className: 'daysToExpire-visible-toggle'
      });
      this.renderConditionalField({
        label: 'conditionalExpirationAnswerSelector',
        fieldName: 'conditionalExpirationAnswerSelector',
        conditionalQuestion: 'conditionalExpirationQuestion',
        conditionalProperty: ConditionalExpirationFieldProperty
      });
      if (
        // Si ya se capturó un valor de `daysToExpire`
        this.getDaysToExpire() > 0 ||
        // O si ya se capturó `Si` en `daysToExpireLabel`
        this.getDaysToExpireLabel() > 0
      ) {
        for (const domNode of query(toggleQuerySelector, dom.byId('properties'))) {
          domClass.remove(domNode, 'displayNone');
        }
      } else {
        for (const domNode of query(toggleQuerySelector, dom.byId('properties'))) {
          domClass.add(domNode, 'displayNone');
        }
      }
    },
    showMailProperties: function (sectionsDom) {
      let sections = sectionsDom;
      sections = sections || this.getConfigurableSections(this.parentId);
      if (sections && sections.length < 1) {
        this.setDefaultIncludeInMail('1');
      }
      const domContainer = domConstruct.create('div', { class: 'include-in-mail-properties' }, $SV('propertiesArea'));
      this.setDefaultIncludeInMail('0');
      this.renderSelectProperty(this.includeInMailTitle(), 'includeInMail', {
        data: [
          { value: '1', label: i18n.current('yes') },
          { value: '0', label: i18n.current('no') }
        ],
        domContainer: domContainer,
        skipDefaultOption: true,
        required: true,
        onAfterChange: function (e) {
          if (e.target.value === '1') {
            this.setIncludeInMail(e.target.value);
          } else {
            this.setIncludeInMail(0);
          }
        }
      });
    },
    showConditionalFieldForSection: function (sectionsDom) {
      let sections = sectionsDom;
      sections = sections || this.getConfigurableSections(this.parentId);
      if (sections && sections.length < 1) {
        return;
      }
      this.renderProperty(
        i18n.current('conditionalQuestion'),
        'conditionalQuestion',
        {
          t: i18n.current('yes'),
          f: i18n.current('no')
        },
        null,
        this.onChangeConditionalQuestion
      );
      this.renderConditionalField({
        label: 'conditionalAnswerSelector',
        fieldName: 'conditionalAnswerSelector',
        conditionalQuestion: 'conditionalQuestion',
        conditionalProperty: ConditionalFieldProperty
      });
    },
    setDefaultIncludeInMail: function (defaultValue) {
      if (this.includeInMail !== null && typeof this.includeInMail !== 'undefined') {
        return;
      }
      this.setIncludeInMail(defaultValue);
    },
    setIncludeInMail: function (includeInMail) {
      if (this.isInteger(includeInMail)) {
        this.setProp('includeInMail', `${includeInMail}`);
      } else {
        this.setProp('includeInMail', '0');
      }
    },
    getIncludeInMail: function () {
      if (this.includeInMail) {
        return +this.includeInMail || 0;
      }
      return 0;
    },
    setDaysToExpireLabel: function (daysToExpireLabel) {
      if (this.isInteger(daysToExpireLabel)) {
        this.daysToExpireLabel = +daysToExpireLabel || 0;
      }
      this.daysToExpireLabel = 0;
    },
    getDaysToExpireLabel: function () {
      if (this.daysToExpireLabel) {
        return +this.daysToExpireLabel || 0;
      }
      return 0;
    },
    setDaysToExpire: function (daysToExpire) {
      if (this.isInteger(daysToExpire)) {
        this.daysToExpire = +daysToExpire || 0;
      }
      this.daysToExpire = 0;
    },
    getDaysToExpire: function () {
      if (this.daysToExpire) {
        return +this.daysToExpire || 0;
      }
      return 0;
    },
    isInteger: (value) => {
      if (value === null || typeof value === 'undefined') {
        return false;
      }
      if (typeof value === 'number') {
        return true;
      }
      if (value && typeof value === 'object') {
        return false;
      }
      if (typeof value === 'boolean') {
        return false;
      }
      if (Number.isInteger(value)) {
        return true;
      }
      if (Number.isInteger(+value)) {
        return true;
      }
      if (Array.isArray(value)) {
        return false;
      }
      const result =
        value
          .toString()
          .trim()
          .match(/^[0-9]+$/) !== null;
      return result;
    },
    showDaysToWaitProperties: function (sectionsDom) {
      let sections = sectionsDom;
      // -- Días para recordatorio de atraso -- //
      sections = sections || this.getConfigurableSections(this.parentId);
      if (sections && sections.length > 0) {
        if (!this.signatureType) {
          this.signatureType = 'SAME_DAY';
        }
        if (!this.daysToWait) {
          this.daysToWait = 0;
        }
        this.renderSelectProperty(i18n.current('signatureType'), 'signatureType', {
          data: [
            { value: 'SAME_DAY', label: i18n.current('sameDay') },
            { value: 'DAYS_AFTER', label: i18n.current('laterDay') }
          ],
          skipDefaultOption: true,
          required: true,
          onAfterChange: function (e) {
            for (const dom1 of query('.daysToWait-container-class', dom.byId('properties'))) {
              if (e.target.value === 'DAYS_AFTER') {
                domClass.remove(dom1, 'displayNone');
              } else if (!dom1.hasClassName('displayNone')) {
                domClass.add(dom1, 'displayNone');
              }
            }
            isValidAlert({ 'classic-yellow': true, context: this.id });
          }
        });
        this.renderTextProperty(i18n.current('laterDayInput'), 'daysToWait', true, {
          type: 'number',
          className: 'daysToWait',
          min: '0'
        });
        if (this.signatureType !== 'DAYS_AFTER') {
          domClass.add(query('.daysToWait-container-class', dom.byId('properties'))[0], 'displayNone');
        }
      }
    },
    hideProperties: function () {
      this.base();
      _SearchableFieldGrid.placedAtDialog?.hide();
      GridProperty.hide();
    }
  });
  return seccion;
});
