define([
  'bnext/survey/formObject/field/Seccion',
  'bnext/survey/_code/page',
  'bnext/survey/_util/DefaultValue',
  'bnext/survey/lang/languages',
  'bnext/survey/formObject/GridProperty',
  'bnext/survey/_base/$SV',
  'dojo/dom-attr',
  'dojo/_base/array',
  'dojo/dom',
  'dojo/query',
  'dojo/_base/lang',
  'core',
  'bnext/survey/_util/jsFailureExposure!'
], (seccion, page, defaultValue, i18n, GridProperty, $SV, domAttr, array, dom, query, dojoLang, core) => {
  const signature = seccion.extend({
    constructor: function () {
      this.base();
      this.title.defaultText = defaultValue.signatureTitle(this.id);
      this.type = 'signature';
      this.validSectionProperties = ['business-unit-position', 'organizational-unit-position', 'user', 'requestor', 'boss', 'user-to-be-defined'];
      this.editableSections = [];
      this.defineFieldProperty('signRejectApproval', false);
    },
    getSchema: function () {
      const item = this.base();
      item.type = 'signature';
      item.editableSections = this.getEditableSections();
      return item;
    },
    getEditableSections: function () {
      const sections = this.getConfigurableSections();
      const gridValue = GridProperty.syncData(sections, this.editableSections);
      this.editableSections = gridValue.value;
      return this.editableSections;
    },
    setSchema: function (obj) {
      this.base(obj);
      this.editableSections = this.loadEditableSections(obj);
    },
    render: function (parentId) {
      this.base(parentId);
    },
    loadEditableSections: (obj) => {
      const sections = [];
      if (core.isNull(obj.surveyFieldObjectEditables)) {
        return [];
      }
      array.forEach(obj.surveyFieldObjectEditables, (section) => {
        sections.push(section.fieldId);
      });
      return sections;
    },
    getConfigurableSections: (signatureId) => {
      const sections = [];
      const formItems = page.form.formItems;
      array.some(formItems, (field) => {
        if (field.id === signatureId) {
          return true;
        }
        if (field.type === 'seccion') {
          const id = field.id;
          const order = dom.byId(`${id}_uiObject_sectionId`).textContent;
          sections.push({
            id: id,
            code: order,
            description: field.uiObject.getTitleText()
          });
        }
        return false;
      });
      return sections;
    },
    showEditableSections: function () {
      const parent = $SV('propertiesArea');
      const sections = this.getConfigurableSections(this.parentId);
      this.showDaysToWaitProperties(sections);
      GridProperty.setConfig({ configuredFieldName: 'editableSections', fieldId: this.parentId });
      GridProperty.placeAt(parent);
      GridProperty.update(sections, this.editableSections);
    },
    showCanRequestAdjust: () => {},
    daysToExpireTitle: () => i18n.current('daysToExpireSignatureTitle'),
    onChangeSignRejectApproval: (evt, property, value) => {},
    showProperties: function () {
      this.base();
      this.renderProperty(
        i18n.current('signRejectApproval'),
        'signRejectApproval',
        {
          true: i18n.current('yes'),
          false: i18n.current('no')
        },
        null,
        dojoLang.hitch(this, this.onChangeSignRejectApproval)
      );
      this.renderProperty(
        i18n.current('canCancel'),
        'canCancel',
        {
          t: i18n.current('yes'),
          f: i18n.current('no')
        },
        null,
        dojoLang.hitch(this, this.onChangeCanCancel)
      );
    },
    hideProperties: function () {
      this.base();
      GridProperty.hide();
    }
  });
  return signature;
});
