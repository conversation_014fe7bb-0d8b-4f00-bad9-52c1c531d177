define([
    'bnext/survey/formObject/HtmlArray',
    'dojo/dom-construct',
    'bnext/survey/formObject/SurveyFieldAnswerType',
    'dojo/query',
    'bnext/survey/lang/languages',
    'dojo/dom-class',
    'dojo/dom',
    'dojo/on',
    'bnext/survey/_util/jsFailureExposure!'
],
function(htmlArray, domConstruct, SurveyFieldAnswerType, query, lang, domClass, dom, on) {
    var htmlTextFieldArray = htmlArray.extend({
        constructor: function(columns_, size_) {
            this.base(columns_);
            this.size = size_;
        },
        renderItem: function(i) {
            var col = $td({
                "class": "arrayItem alignCenter"
            });
            var _self = this;
            var numericRegExp = new RegExp('[0-9]');
            var keysAllowedInNumeric = ['Backspace', 'Enter', 'NumpadEnter', 'Delete', 'ArrowLeft','ArrowRight','ArrowUp', 'ArrowDown'];
            var id = _self.id + "_col" + i.toString();
            var div = domConstruct.create('div', {
                'id': id,
                'class': 'textfield html-text-line-display html-text-field-array-placeholder',
                'contentEditable': true,
                'style': 'width:100%; height: 30.5px; min-height: 30.5px; min-width: 6.79rem;',
                'data-text': lang.current("untitledHelpText"),
                onkeyup: function(e) {
                    e.srcElement.classList.remove('html-text-field-array-placeholder');
                },
                onkeypress: function (e) {
                    if (keysAllowedInNumeric.includes(e.key)) {
                        return;
                    }
                    var typeSelected = dom.byId(_self.id + '_col_type');
                    if (+typeSelected.value === 2 && !numericRegExp.test(e.key)) {
                        e.preventDefault();
                    }
                }
            }, col);
            if (_self.defaultValue && _self.defaultValue !== "") {
                div.classList.remove("html-text-field-array-placeholder");
                div.innerHTML = this.defaultValue;
            }
            return(col);
        },
        renderAdditionalItem: function(container, mainFieldType){
            var elements = [];
            var col_type = $td({
                "class": "arrayItem alignCenter"
            });
            var id_type = this.id + "_col_type"; // Si se cambia el nombre también actualizar en Summation.js:setSummation y HtmlTextFieldArray:getSchema
            var select = domConstruct.create('select', {
                    'id': id_type,
                    'class': 'array-item-answer-type',
                    innerHTML: 
                    '<option value="' + SurveyFieldAnswerType.ALPHA_NUMERIC + '">' + lang.current("alphanumeric") + '</option>' +
                    '<option value="' + SurveyFieldAnswerType.NUMERIC + '">' + lang.current("numerical") + ' </option>' +
                    '<option value="' + SurveyFieldAnswerType.DECIMAL + '">' + lang.current("decimal") + ' </option>' +
                    '<option value="' + SurveyFieldAnswerType.MAIL + '">' + lang.current("mail") + ' </option>' +
                    '<option value="' + SurveyFieldAnswerType.CURRENCY_USD_TO_MXN + '">' + lang.current("currencyConversionUsdToMxn") + ' </option>' +
                    '<option value="' + SurveyFieldAnswerType.CURRENCY + '">' + lang.current("currency") + ' </option>'
            }, col_type);
            if (this.answerType) {
                select.value = this.answerType;
            }
            elements.push(col_type);
            container.appendChild(col_type);
            if (mainFieldType === 'textFieldArray') {
                on(select, 'change', function(evt) {
                    const value = +evt.target.value;
                    selectAllowNegativeValues.disabled = !(value === SurveyFieldAnswerType.NUMERIC || value === SurveyFieldAnswerType.DECIMAL || value === SurveyFieldAnswerType.CURRENCY);
                    selectAllowNegativeValues.value = selectAllowNegativeValues.disabled && selectAllowNegativeValues.value === '1' ? '0' : selectAllowNegativeValues.value;
                });
                var col_allow_negative_values = $td({
                    "class": "arrayItem alignCenter"
                });
                var id_allow_negative_values = this.getAllowNegativeValuesId(this.id);
                var selectAllowNegativeValues = domConstruct.create('select', {
                    'id': id_allow_negative_values,
                    'class': 'array-item-answer-type',
                    innerHTML:
                      '<option value="0">' + lang.current("denyNegativeValues") + '</option>' +
                      '<option value="1">' + lang.current("allowNegativeValues") + ' </option>'
                }, col_allow_negative_values);
                if (!!this.allowNegativeValues) {
                    selectAllowNegativeValues.value = '1';
                }
                if (this.answerType) {
                    selectAllowNegativeValues.disabled = !(this.answerType === SurveyFieldAnswerType.NUMERIC || this.answerType === SurveyFieldAnswerType.DECIMAL || this.answerType === SurveyFieldAnswerType.CURRENCY);
                }
                elements.push(col_allow_negative_values);
                container.appendChild(col_allow_negative_values);
            }
            if (this.summation) {
                domClass.add(container, 'Summation');
            }
            return elements;
        },
        getSchema: function() {
            var item = this.base();
            item.code = this.code;
            var domDefaultValueQuery = `#${this.id} .arrayItem div[contenteditable="true"]`; 
            var domDefaultValue =  query(domDefaultValueQuery)[0];
            if (domDefaultValue) {
                item.defaultValue = domDefaultValue.innerHTML || '';
            }
            var tr = dom.byId(this.id);
            item.summation = domClass.contains(tr, 'Summation') ? 1 : 0;
            var answerTypeColumnId = this.id + "_col_type";
            item.answerType = +dom.byId(answerTypeColumnId).value;
            var allowNegativeValuesColumnDom = this.getAllowNegativeValuesDom(this.id);
            item.allowNegativeValues = allowNegativeValuesColumnDom ? +allowNegativeValuesColumnDom.value === 1 : false;
            return item;
        },
        getAllowNegativeValuesId: function (id) {
            return id + "_col_allow_negative_values";
        },
        getAllowNegativeValuesDom: function (id) {
            const nId = this.getAllowNegativeValuesId(id);
            return dom.byId(nId);
        },
        setSchema: function(obj, weight) {
            this.base(obj, weight);
            if (obj) {
                this.code = obj.code;
                this.defaultValue = obj.defaultValue;
                if (obj.summation && obj.summation === 1){
                    this.summation = 1;
                }
                if (obj.answerType){
                    this.answerType = obj.answerType;
                }
                if (obj.allowNegativeValues){
                    this.allowNegativeValues = obj.allowNegativeValues;
                }
            }
        }
    });
    return htmlTextFieldArray;
});