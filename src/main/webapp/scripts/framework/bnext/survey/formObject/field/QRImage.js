define([
  'bnext/survey/formObject/_formObject',
  'bnext/survey/_util/jsFailureExposure!',
  'QRCodeGenerator',
  'dojo/dom',
  'dojo/dom-construct',
  'dojo/dom-class',
  'dojo/on',
  'dojo/query',
  'bnext/survey/formObject/ValidAlert',
  'bnext/survey/_base/$SV',
  'dojo/dom-attr',
  'bnext/survey/formObject/PropertiesPositionManager',
  'bnext/survey/_code/page',
  'bnext/survey/lang/languages'
], (formObject, jsFailureExposure, QRCodeGenerator, dom, domConstruct, domClass, on, query, isValidAlert, $SV, domAttr, propPositionManager, page, i18n) => {
  const QRImage = formObject.extend({
    constructor: function () {
      this.base();
      this.type = 'QRImage';
      this.qrUrlType = 'fixed';
      this.subTitle = null;
      this.fixedQrUrl = '';
      this.defaultTitle = i18n.current('scanMe');
      this.title.defaultText = this.defaultTitle;
      this.title.wysiwygEnabled = false;
      this.requiredProperties = true;
    },
    setSchema: function (obj) {
      this.base(obj);
      this.qrUrlType = obj.qrUrlType;
      this.fixedQrUrl = obj.fixedQrUrl;
    },
    getSchema: function () {
      const item = this.base();
      item.qrUrlType = this.qrUrlType;
      item.fixedQrUrl = this.fixedQrUrl;
      return item;
    },
    showProperties: function () {
      page.form.resetProperties();
      if (this.formArea !== null) {
        const container = this.formArea;
        const emptyProperties = query('.empty-properties', container)[0];
        if (emptyProperties) {
          domClass.add(emptyProperties, 'displayNone');
        }

        const selectProperty = this.renderSelectProperty(i18n.current('linkQR'), 'qrUrlType', {
          data: [
            { value: 'fixed', label: i18n.current('linkQRFixed') },
            { value: 'dynamic', label: i18n.current('linkQROnFill') }
          ],
          onAfterChange: this.onqrUrlTypeChange.bind(this)
        });

        if (container && typeof container.insertBefore === 'function') {
          container.insertBefore(selectProperty, container.firstChild);
        } else {
          console.error('Elemento contenedor no válido para selectProperty:', container);
        }

        this.textProperty = this.renderTextProperty(i18n.current('addLinkQR'), 'fixedQrUrl', true, {
          placeholder: i18n.current('exampleLinkQR'),
          validationRegExp: /.+/,
          invalidDataToRemoveRegExp: /[^a-zA-Z0-9-._\/:]/,
          onAfterChange: this.onfixedQrUrlChange.bind(this)
        });

        if (this.qrUrlType === 'fixed' && container && typeof container.insertBefore === 'function') {
          container.insertBefore(this.textProperty, container.firstChild);
        } else if (this.qrUrlType !== 'fixed') {
          this.textProperty.style.display = 'none';
        } else {
          console.error('Elemento contenedor no válido para textProperty:', container);
        }

        const textInput = dom.byId(this.getTextPropertyInputId('fixedQrUrl'));
        if (textInput) {
          on(textInput, 'input', this.onfixedQrUrlChange.bind(this));
          on(textInput, 'keyup', this.onfixedQrUrlChange.bind(this));
        } else {
          console.error("El elemento 'fixedQrUrl' no se encontró en el DOM.");
        }
      }
    },
    onqrUrlTypeChange: function (evt) {
      this.qrUrlType = evt.target.value;
      if (this.textProperty) {
        if (this.qrUrlType === 'fixed') {
          this.textProperty.value = '';
          this.textProperty.style.display = '';
        } else {
          this.textProperty.value = '-';
          this.textProperty.style.display = 'none';
        }
      }
      this.updateQRCode();
      isValidAlert({ 'classic-yellow': true, context: this.id });
    },
    onfixedQrUrlChange: function (evt) {
      this.fixedQrUrl = evt.target.value;
      this.updateQRCode();
    },
    updateQRCode: function () {
      const container = document.getElementById(`${this.id}_items`);
      container.innerHTML = '';
      const dynamicUrl = this.qrUrlType === 'fixed' ? this.fixedQrUrl : '';
      const qrElement = QRCodeGenerator.generate(dynamicUrl);
      if (container && typeof container.insertBefore === 'function') {
        container.insertBefore(qrElement, container.firstChild);
      } else {
        console.error('Elemento contenedor no válido para qrElement:', container);
      }
    },
    render: function (parentId) {
      this.base(parentId);
      this.updateQRCode();
    }
  });
  return QRImage;
});
