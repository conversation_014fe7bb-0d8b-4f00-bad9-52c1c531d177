define([
    'core','dojo/_base/declare', './DocumentRequest', 'dijit/form/Button', 'dojo/Deferred',
    'bnext/module/UltraFilteringSelect', 'dojo/dom-construct', 'dojo/_base/lang', 'bnext/module/DocumentCodePreview', 'dojo/dom-class',
    'bnext/extensionIcons', 'bnext/gridComponentUtil', 'bnext/misc', 'dojo/query','dojo/on', 'bnext/callMethod', 'dojo/store/Memory', 'dojo/date/locale', 'dijit/form/CheckBox', 'bnext/i18n!./nls/DocumentVerification', 'dojo/dom-style', "dijit/focus",
    'bnext/angularNavigator',
    'xstyle/css!./styles/DocumentRequestVerify.css',
    "dojo/domReady!"
],
function(
    core, declare, DocumentRequest, Button, Deferred, UltraFilteringSelect, domConstruct, lang, DocumentCodePreview, domClass,
    extensionIcons, gcUtil, BnextMisc, query, on,
    callMethod, Mem, locale, CheckBox, i18n, domStyle , focusUtil,
    angularNavigator
) {
            var bnextMisc = new BnextMisc();
            var flujoBuffer = {
                plantas: {},
                corp: {},
                department: {}
            },
            datePattern = 'yyyy-MM-dd';
            function parseDate(str) {
                return locale.parse(str.substr(0, str.indexOf('T')), {
                    datePattern: datePattern,
                    formatLength: "short",
                    selector: 'date'
                });
            }
            function formatDate(dte) {
                return locale.format(dte, {
                    selector: 'date'
                });
            }
            function formatDateString(str) {
                return formatDate(parseDate(str));
            }
            function getFlujos(sol) {
                var corp = sol.organizationalUnit, planta = sol.businessUnit, department = sol.department, id;
                if (department) {
                    id = department.id;
                    if (flujoBuffer.department[id]) {
                        return flujoBuffer.department[id];
                    } else {
                        return flujoBuffer.plantas[id] = callMethod({
                            url: 'Template.action',
                            params: [id],
                            method: 'getTemplatesByDepartment'
                        });
                    }
                }
                if (planta) {
                    id = planta.id;
                    if (flujoBuffer.plantas[id]) {
                        return flujoBuffer.plantas[id];
                    } else {
                        return flujoBuffer.plantas[id] = callMethod({
                            url: 'Template.action',
                            params: [id],
                            method: 'getTemplatesByPlanta'
                        });
                    }
                }
                if (corp) {
                    id = corp.id;
                    if (flujoBuffer.corp[id]) {
                        return flujoBuffer.corp[id];
                    } else {
                        return flujoBuffer.corp[id] = callMethod({
                            url: 'Template.action',
                            params: [id],
                            method: 'getTemplatesByCorp'
                        });
                    }
                }
                return null;
            }
            var DocumentRequestVerify = {
                uploadCheck: null,
                documentCodePreview: null,
                returnBtn: null,
                rejectBtn: null,
                flujoSelect: null,
                originalFileId: null,
                originalDepartment: null,
                replaceDocDiv: null,
                changeDepartmentDiv: null,
                externalFileSelect: core.getExternalFileIntegration(),
                changeDepartmentCheck: null,
                lan: i18n,
                constructor: function(arg) {
                    if (!arg.sol) {
                        throw {
                            error: i18n.missingRequest,
                            clase: 'DocumentRequestVerify'
                        };
                    }
                    arg.sol = lang.clone(arg.sol);
                    this.lan.submit = i18n.verify;
                },
                isAcceptBusy: function () {
                  if (this.documentRequestVerify?.sol) {
                    return this.documentRequestVerify.sol.busyAccept;
                  }
                  if (this.sol) {
                    return this.sol.busyAccept;
                  }
                  return false;
                },
                updateAcceptBusy: function (busy) {
                  if (this.documentRequestVerify?.sol) {
                    this.documentRequestVerify.sol.busyAccept = busy;
                  } else if (this.sol) {
                    this.sol.busyAccept = busy;
                  } else {
                   console.error('No request to update busy status');
                  }
                },
                _loadFillFormsGrid: function() {
                    var self = this;
                    if (!this.fillFormsGrid) {
                        return;
                    }
                    this.fillFormsGrid.refreshData().then(function() {
                       if (self.fillFormsGrid.getBean().data.length > 0) {
                           core.dialog(i18n.cancelFillRequestsWarning, i18n.ok);
                       }
                    });  
                },
                postCreate: function() {
                    //hace todo el setup de DocumentRequest
                    domClass.add(this.domNode, 'DocumentRequestVerify');
                    this.documentRequestVerify = true;
                    this.originalDepartment = this.sol.department;
                    this.type = this.sol.type;
                    this.status = this.sol.status;
                    var controlled = 
                            this.sol 
                            && this.sol.documentType 
                            && this.sol.documentType.documentControlledType === 'controlled';
                    this.isControlled = controlled;
                    this.inherited(arguments);
                    if (this.isAFormRequest 
                            && (this.type === DocumentRequest.create || this.type === DocumentRequest.update)) {
                        this.formTemplateModify.set('style', {
                            display: 'inline-block'
                        });
                    } 
                    this.department.set('required', true);
                    this.originalFileId = this.sol.fileId;
                    this.file = {id: this.sol.fileId};
                    this._createMissingDom();
                    this._setup();
                    var _self = this;
                    if (this.type === DocumentRequest.create && this.sol && this.sol.generateCode === 1) {
                        DocumentCodePreview.getCodePreview(this.sol).then(function(documentCodePreview) {
                            _self.documentCodePreview = bnextMisc.escapeRegex(documentCodePreview);
                            var currentDocumentCode = _self.code.get('value');
                            if(!currentDocumentCode.match(new RegExp('^' + _self.documentCodePreview.replace(/#+/g, '\\d+') + '$'))) {
                                core.info(
                                    core.specifiedMessage(i18n.verifyConfirmNewCode)
                                        .set('currentDocumentCode', currentDocumentCode)
                                        .set('documentCodePreview', documentCodePreview)
                                        .get(),
                                    core.i18n.yes,
                                    core.i18n.no
                                ).then(function() {
                                    _self.showLoader(i18n.reservingCodeSequence);
                                    DocumentCodePreview.getCode(_self.sol).then(function(documentCode) {
                                        _self.code.set('value', documentCode);
                                        _self._loadFillFormsGrid();
                                        _self.hideLoader();
                                    }, _self.hideLoader);
                                }, _self.hideLoader);
                            } else {
                                _self._loadFillFormsGrid();
                            }
                        });
                    } else {
                        _self._loadFillFormsGrid();
                    }
                    domClass.add(this.unknownCodeDiv, 'displayNone');
                },
                onDownloadFileAction: function() {
                    angularNavigator.navigateLegacy('v-document-viewer.view?requestId='+ this.sol.id);
                },
                onSourceFileAction: function() {
                    this.fDownloaderAnchor.href = 'Download.view?fileId=' + this.originalFileId + "&originalForce=1";
                    core.anchorClick(this.fDownloaderAnchor);
                },
                onOriginalFileAction: function() {
                    angularNavigator.navigateLegacy('v-document-viewer.view?requestId='+ this.sol.document.requestId);
                },
                _createMissingDom: function() {
                    var self = this;
                    if (this.isControlled) {
                        var flow = domConstruct.create('div', {
                            class: 'cell small-12',
                            innerHTML: ''
                                + ' <div class="widget-component select-component">'
                                    + '<select class="select"></select>' 
                                + ' </div>'
                        }, this.reazonDiv, 'after');
                        //crea select para los flujos de autorizacion
                        this.flujoSelect = new UltraFilteringSelect({
                            onChange: lang.hitch(this, this.onFlujoChange),
                            lblValue: i18n.flow,
                            searchAttr: 'description'
                        }, query('select.select', flow)[0]);
                        getFlujos(this.sol).then(lang.hitch(this, function(f) {
                            var store = new Mem({
                                data: f
                            });
                            this.flujoSelect.set('store', store);
                            if (this.sol.flujoId) {
                                this.flujoSelect.set('value', this.sol.flujoId);
                            }
                        }));
                    }
                    //crea markup para descarga y subido de nuevos documentos
                    var fUploaderDiv = this.fUploader.get('domNode');
                    fUploaderDiv.style.display = 'none';
                    var originalFileId = this.originalFileId, replaceDepartmentCheckDiv;
                    if ( this.sol.type === DocumentRequest.create 
                            || this.sol.type === DocumentRequest.update) {
                        if (!this.isAFormRequest) {
                            this.replaceDocDiv = domConstruct.create('div', {
                                'class': 'inner-check cell small-12 medium-6 textarea-component',
                                'innerHTML': ''
                                    + '<input type="text" readonly/>'
                                    + '<div class="inner-check-container">'
                                        + '<input class="material-icons" type="checkbox">'
                                        + '<label>' + i18n.replaceDocument + '</label>'
                                    + '</div>'
                            }, this.fileButtons, 'before');
                            var _self = this;
                            var toogleVisibilityDoms = [
                                this.downloadFileLi, this.sourceFileLi, this.fileButtons
                            ];
                            this.fileLabel = query('input[type="text"]', this.replaceDocDiv)[0];
                            this.uploadCheck = query('input[type="checkbox"]', this.replaceDocDiv)[0];
                            on(this.uploadCheck, 'change', function() {
                                var isReplaceChecked = self.uploadCheck.checked;
                                if (isReplaceChecked) {
                                    _self.file = null;
                                    fUploaderDiv.style.display = '';
                                    setDisplayToArray(toogleVisibilityDoms, 'none');
                                    if(query('.dijitTooltip', this.replaceDocDiv)[0]){
                                        query('.dijitTooltip', this.replaceDocDiv)[0].style.display = '';
                                    }
                                } else {
                                    fUploaderDiv.style.display = 'none';
                                    setDisplayToArray(toogleVisibilityDoms, '');
                                    _self.file = {
                                        id: this.originalFileId
                                    };
                                    if(query('.dijitTooltip', this.replaceDocDiv)[0]){
                                        query('.dijitTooltip', this.replaceDocDiv)[0].style.display = 'none';
                                    }
                                }
                            });
                            function setDisplayToArray(array, display) {
                                for (var i = 0, max = array.length; i < max; i++) {
                                    domStyle.set(array[i], 'display', display);
                                }
                            }
                        }
                        this.department.set({disabled: true});
                        this.changeDepartmentDiv = replaceDepartmentCheckDiv = domConstruct.create('div', {
                            class: 'contentChangeDepartment',
                            innerHTML: '<div></div>' +
                                '<label>' + i18n.changeDepartment + '</label>'
                        }, this.department.get('domNode').parentNode, 'after');
                        this.changeDepartmentCheck = new CheckBox({
                            onChange: function(x) {
                                // si self.originalDepartment es null y se ha desactivado el check entonces modificar el combo                        
                                if (self.originalDepartment==null && !x){
                                    self.department.set({value: ''}); 
                                };
                                self.onChangeStatusFlujoSelect();
                                self.department.set({disabled: !x});
                                domStyle.set(self.rejectBtn.domNode, 'display', x ? 'none' : '');  
                                domStyle.set(self.accept.domNode, 'display', x ? 'none' : '');
                                domStyle.set(self.cancel.domNode, 'display', x ? ($(self.cancel.domNode).addClass('raised-button')) : ($(self.cancel.domNode).removeClass('raised-button')));
                            }
                        }).placeAt(replaceDepartmentCheckDiv.childNodes[0]);
                    }
                    if (originalFileId) {
                        this.fDownloaderAnchor = domConstruct.create('a', {
                            href: "javascript: void(0);",
                            style: {
                                        display: 'none'
                                    },
                            target: window.top.pdfViewer ? 'visorPdf' : ''
                        }, fUploaderDiv, 'before');
                        var fileInfo = extensionIcons.getExtensionInfo(this.sol.fileContent, this.sol);
                        this.downloadFileSrc.src = fileInfo.iconSrc;
                        if (this.fileLabel) {
                            this.fileLabel.value = fileInfo.title;
                        }
                        domClass.remove(this.downloadFileLi, 'displayNone');
                        this.sourceFileSrc.src = require.toUrl('bnext/module/images/download.png');
                        domClass.remove(this.sourceFileLi, 'displayNone');
                        if (this.sol.type === DocumentRequest.update || this.type === DocumentRequest.update) {
                            var originalFileInfo = extensionIcons.getExtensionInfo(this.sol.document.fileContent, this.sol.document);
                            this.originalFileSrc.src = originalFileInfo.iconSrc;
                            domClass.remove(this.originalFileLi, 'displayNone');
                        }
                    }
                    if (this.isAFormRequest 
                            && (this.type === DocumentRequest.update || this.type === DocumentRequest.cancel)) {
                        var fillRequestDiv = domConstruct.create('div', {
                            'class': 'table-component cell small-12 rightGridContainer rightLinkedGridWithTitles rightGridContainer',
                            innerHTML: ''
                                    + '<label for="fillRequests">' + i18n.fillRequests + '</label>'
                                    + '<div class="fill-requests-container"><table></table></div>'
                        }, this.formTemplateDiv, 'after');
                        
                        var columns = [];
                        gcUtil.column(columns)
                            .push('documentCode', i18n.colNameDocumentCode, gcUtil.cType().Text(null, false), null, null, null, null, false)
                            .push('authorDescription', i18n.colNameAuthor, gcUtil.cType().Text(null, false), null, null, null, null, false)
                            .push('creationDate', i18n.colNameCreationDate, gcUtil.cType().Date(false), null, null, null, null, false)
                        ;
                        var documentId = this.sol.document.id;
                        if (core.isNull(documentId)) {
                            var error = 'Invalid document id "' + documentId + '"provided for Form Request Modification.';
                            console.error(error);
                            fillRequestDiv.childNodes[1].firstChild.innerHTML = error;
                            core.error(error);
                        } else {
                            this.fillFormsGrid = new gridComponent({
                                id: this.id + '_fillFormsGrid',
                                container: fillRequestDiv.childNodes[1].firstChild,
                                columns: columns,
                                size: 0,
                                freezeHeader: false,
                                serviceStore: 'Fill.Out.History.action?currentEntityId=' + documentId, 
                                methodName:"getInProcessFillForms",
                                searchContainer: null,
                                noExcel: true,
                                noEyes: true,
                                noPagination: true,
                                noRegMessage: i18n.noFillsFormsAvailable
                            });
                        }
                    }
                    this.rejectBtn = new Button({
                        onClick: lang.hitch(this, this._onReject),
                        label: i18n.reject,
                        'class': 'outlined-button'
                    });
                    this.rejectBtn.placeAt(this.accept, 'after');
                },
                _onSubmit: function() {
                    this.isSubmiting = true;
                    this.showLoader(core.i18n.Validate.saving);
                    this._onDocTypeChange(this.docType.get('value'));
                    if (this.isControlled) {
                        if (this.type >= DocumentRequest.update) {
                            this.flujoSelect.set('required', true);
                        } else {
                            if (this.department.get('value') === '') {
                                core.dialog(i18n.missingDepartment, i18n.accept);
                                this.isSubmiting = false;
                                this.hideLoader();
                                return;
                            }
                            if ((!this.originalDepartment && this.department.get('value')) || (+this.originalDepartment.id !== +this.department.get('value'))) {
                                this.flujoSelect.set('required', false);
                            } else {
                                this.flujoSelect.set('required', true);
                            }
                        }
                    }
                    this.inherited(arguments);
                    this.isSubmiting = false;
                },
                destroyRecursive: function() {
                    try {
                        if (this.fillFormsGrid) {
                            this.fillFormsGrid.destroyRecursive();
                        }
                    } catch(e) {
                        console.error("Can not destroy DocumentRequestVerify component");
                        console.error(e);
                    }
                    this.inherited(arguments);
                },
                _setSolValues: function() {
                    this.inherited(arguments);
                    if (this.uploadCheck && !this.uploadCheck.checked) {
                        this.sol.fileId = this.originalFileId;
                        this.file = {
                            id: this.originalFileId
                        };
                    }
                    if (this.isControlled && this.flujoSelect.get('value')) {
                        this.sol.flujoId =  this.flujoSelect.get('value') || undefined;
                    }
                    this._simplifySolValues();
                },
                _simplifySolValues: function() {
                    var nodo = {
                        id: this.sol.nodo.id,
                        code: this.sol.nodo.code
                    }, UNE = this.sol.businessUnit ? {
                        id: this.sol.businessUnit.id
                    } : null, author = {
                        id: this.sol.author.id
                    };
                    this.sol.nodo = nodo;
                    this.sol.businessUnit = UNE;
                    this.sol.author = author;
                },
                _setup: function() {
                    var date = formatDateString(this.sol.creationDate);
                    this.name.set('value', this.sol.description);
                    if (this.slimReportName) {
                        this.slimReportName.set('value', this.sol.slimReportName);
                    }
                    this.code.set('value', this.sol.documentCode);
                    this.deferreds.then(lang.hitch(this, function() {
                        this.hideLoader();
                        core.hideLoader();
                        this.docType.set('value', this.sol.documentType.id);
                        this.currentUser.set('value', this.sol.author.description);
                        if (this.code.get('value') === '') {
                            this.unknownCode.set('checked', true);
                        }
                    }));
                    this.version.set('required', true);
                    this.version.set('value', this.sol.version);
                    this.date.set('value', date);
                    this.setReasonReadOnly(this.sol.reazon);
                    this.selectFolder.innerHTML = this.selectFolder.title = this.sol.nodo.path;
                    this.node = this.sol.nodo;
                    if (this.type >= DocumentRequest.update) {
                        domClass.add(this.nodeDiv, 'displayNone');
                        this.docType.set('disabled', true);
                        this.fldDrop.set('disabled', true);
                        this.name.set('disabled', true);
                        this.code.set('disabled', true);
                        this.version.set('disabled', true);
                        this.department.set({disabled: true, required: false});
                        this.unknownCodeDiv.style.display = 'none';
                    }
                    
                    if (this.storagePlace.value > 0 || this.sol.storagePlaceId > 0) {
                        this.storagePlace.style.display = '';
                    }
                    this.storageSpan.set({disabled: true, required: false});
                    this.storagePlace.set({required: false});  // sirve para que el campo lugar de almacenamiento no sea requerido
                    return this.sol;
                },
                _onReturn: function() {
                    var sol = this._setSolValues();
                    this.onReturn && this.onReturn(sol);
                },
                onReturn: function() {
                },
                onFlujoChange: function() {
                },
                _onReject: function() {
                    this._setSolValues();
                    this.onReject && this.onReject(this.sol.id);
                },
                onReject: function() {
                },
                _onDepartmentChange: function() {
                    if (this.isControlled) {
                        this.flujoSelect.set('required', false);
                    }
                    this.inherited(arguments);
                },
                _onFormTemplatePreviewClick: function() {
                    angularNavigator.navigateLegacy('v-document-viewer.view?requestId='+ this.sol.id);
                },
                _onUnknownCodeChange: function() {
                    this.inherited(arguments);
                    domClass.toggle(this.code.domNode, 'displayNone');
                    domClass.toggle(this.codeMsgNode, 'displayNone');
                    if(domClass.contains(this.code.domNode, 'displayNone')) {
                        this._setSolValues();
                        if (this.sol && this.sol.generateCode === 1) {
                            DocumentCodePreview.fillDiv(this.sol, this.codeMsgNode);
                        }
                    }
                },
                _onDocTypeChange: function() {
                    this.inherited(arguments);
                    if ((this.docType.get('store').get(this.docType.get('value'))) || this.doc.tipo) {
                        this.ensureGeneratedCode();
                        if ( this.sol.type === DocumentRequest.create || this.sol.type === DocumentRequest.update){
                            this.onChangeStatusFlujoSelect();
                        }
                    }
                },
                onChangeStatusFlujoSelect: function() {
                    if( this.department.get('displayedValue') === "") {
                        this.department.set('required', true);
                        this.flujoSelect.set('readOnly', true);
                        var self = this;
                        setTimeout(function(){  
                            /**  
                             * El focus se hace con TimeOut para brincar 
                             * los eventos 'onChanges', en este punto this.changeDepartmentCheck.get('checked'); 
                             * puede valer true o false.
                             */
                            focusUtil.focus(self.department.get('focusNode'));
                        }, 10);
                        if( !this.changeDepartmentCheck.get('checked') ) {
                            this.changeDepartmentCheck.set('checked', true);
                            core.dialog(i18n.missingDepartment, i18n.accept);
                        } 
                    } else if(this.changeDepartmentCheck.get('checked')) {
                          core.dialog(i18n.changeOrExistDepartment, i18n.accept);
                          this.flujoSelect.set('readOnly', true);
                    } else {
                          this.flujoSelect.set('readOnly', false);
                    }   
                },
                ensureGeneratedCode: function() {
                    if(this.isSubmiting) {
                        return core.simpleSuccessDeferred;
                    }
                    var _self = this, def = new Deferred();
                    if(this.type === DocumentRequest.create) {
                        this._setSolValues();
                        if (this.sol && this.sol.generateCode === 1) {
                            DocumentCodePreview.getCodePreview(this.sol).then(function(documentCodePreview) {
                                _self.documentCodePreview = bnextMisc.escapeRegex(documentCodePreview);
                                var currentDocumentCode = _self.code.get('value');
                                if(!currentDocumentCode.match(new RegExp('^' + _self.documentCodePreview.replace(/#+/g, '\\d+') + '$'))) {
                                    core.info(
                                        core.specifiedMessage(i18n.verifyConfirmNewCode)
                                            .set('currentDocumentCode', currentDocumentCode)
                                            .set('documentCodePreview', documentCodePreview)
                                            .get(),
                                        core.i18n.yes,
                                        core.i18n.no
                                    ).then(function() {
                                        _self.showLoader(i18n.reservingCodeSequence);
                                        DocumentCodePreview.getCode(_self.sol).then(function(documentCode) {
                                            _self.code.set('value', documentCode);
                                            def.resolve();
                                            _self.hideLoader();
                                        }, _self.hideLoader);
                                    }, def.resolve);
                                } else {
                                    def.resolve();
                                }
                            }, def.resolve);
                        }
                    } else {
                        def.resolve();
                    }
                    return def.promise;
                }
            };
            return DocumentRequestVerify = declare([DocumentRequest], DocumentRequestVerify);
        });