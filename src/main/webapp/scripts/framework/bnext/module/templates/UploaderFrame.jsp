<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<% request.setCharacterEncoding("UTF-8"); %>
<html style="overflow: hidden;">
    <head>
        <script>
            function load() {
                var fileAttacher = parent.document.getElementById("fileAttacher");
                if (!fileAttacher) {
                    return;
                }
                var advanced = fileAttacher.parentElement.classList.contains("advanced");
                if (advanced) {
                    var lnk = document.createElement('link');
                    lnk.type='text/css';
                    lnk.href='../styles/uploader-action.css';
                    lnk.rel='stylesheet';
                    document.getElementsByTagName('head')[0].appendChild(lnk);
                }
            }
            window.isLoaded = true;
            window.addEventListener('load', load);
        </script>
        <link rel="stylesheet" href="../qms/<%=bnext.resources.DynamicCssService.CURRENT_MATERIAL_FONTS_FILE_NAME%>.css?${systemVersion}">
        <style type="text/css">
            .label, label{
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: "TypoPRO Titillium Text", sans-serif, Verdana, Arial, Helvetica;
                font-size: 14px;
                background-image: url(../scripts/framework/bnext/images/clip.gif);
                background-repeat: no-repeat;
                background-size: 24px;
                background-position-x: 11px;
                background-position-y: 8px;
                text-indent: 40px;
            }
            input::-webkit-file-upload-button {
                cursor: pointer;
            }
            .Button, .button, .buttonColor {
                border-radius: 1.75rem;
                color: white;
            }
            .filePickerBackground{
                transform: translateX(50%);
            }
            .material-icons {
                position: absolute;
                top: 5px;
                left: 0.5rem;
            }
            .igx-icon.material-icons {
                color: ${systemColorTextColor}!important;
            }
            #uploaderForm .filePickerBackground.material-icons{
                position: relative;
                left: 0rem;
            }
            @media screen and (max-width: 39.9375em) {
                #uploaderForm .filePickerBackground.material-icons {
                    margin-right: 20px;
                }
            }
            .fileUploaderInput {
                width: 100%;
            }
            .file-upload-dashed-container {
                height: 8.75rem;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column-reverse;
                padding: 0 1rem;
            }

            .file-upload-dashed-container .file-upload-dashed-label {
                text-indent: 0;
                font-size: 1rem;
                color: #c9c9c9;
                text-align: justify;
            }

            .file-upload-dashed-container .file-upload-dashed-icon.material-icons-outlined {
                font-size: 3.75rem;
                color: #c9c9c9;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <form id="uploaderForm" method="POST" enctype="multipart/form-data"></form>
    </body>
</html>
