<div data-dojo-attach-point="domNode">
    <div dojoType="dijit.Dialog" data-dojo-attach-point="gridDialog" id="gridDialog_${id}" class="sign-form-wrapper">
        <div class="SignForm">
            <ul class="content_area">
                <li>
                    <table id="gridDialogTable_${id}" data-dojo-attach-point="gridDialogTable"></table>
                </li>
                <li data-dojo-attach-point="gridMessage" tabindex="0">
                </li>
                <li class="actionButtons">
                    <input class="button right authorizeBtn raised-button" 
                           data-dojo-attach-event="onClick: _onAuthorizeClick"
                           data-dojo-attach-point="authorizeBtn" type="button" />
                    <input class="button right cancelBtn" 
                           data-dojo-attach-event="onClick: _onCancelClick"
                           data-dojo-attach-point="cancelBtn" type="button" />
                </li>
            </ul>
        </div>
    </div>
</div>