define([
  'core',
  'loader',
  'dojo/dom-attr',
  'dijit/registry',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dijit/_WidgetsInTemplateMixin',
  'dojo/text!./templates/DocumentRequest.html',
  'bnext/i18n!./nls/DocumentRequest',
  'dojo/date/locale',
  'bnext/callMethod',
  'bnext/i18n!bnext/administrator/catalog/dictionary_index/nls/dictionary-index',
  'bnext/module/data/RequestType',
  'bnext/angularNavigator',
  'dojo/store/Memory',
  'dijit/Tree',
  'dojo/_base/lang',
  'entity/Document',
  'dojo/promise/all',
  'dojo/when',
  'dojo/string',
  'dojo/dom-class',
  'dojo/dom-style',
  'dojo/on',
  'dojo/query',
  'dojo/dom-construct',
  'dojo/dom',
  'dijit/focus',
  'bnext/i18n!bnext/administrator/document/nls/document-module',
  'bnext/i18n!bnext/administrator/document/nls/request-module',
  'bnext/module/data/RequestStatus',
  'bnext/module/util/folder-utils',
  'bnext/module/UltraFilteringSelect',
  'bnext/module/UltraValidationTextBox',
  'bnext/module/UltraSimpleTextarea',
  'bnext/module/UltraNumberSpinner',
  'dojo/store/Observable',
  //a partir de aqui se hacen includes de los widgets para el template
  'dijit/form/ValidationTextBox',
  'dijit/form/CheckBox',
  'dijit/form/FilteringSelect',
  'dijit/form/NumberSpinner',
  'dijit/form/Button',
  'dijit/form/DropDownButton',
  'dijit/form/Textarea',
  'dijit/TooltipDialog',
  'dijit/form/Select',
  'dijit/form/Form',
  'dojo/_base/sniff',
  'bnext/module/FilePicker',
  'xstyle/css!./styles/DocumentRequest.css',
  'dojo/domReady!',
  'bnext/plugins/cssSniff!'
], (
  core,
  loader,
  domAttr,
  registry,
  declare,
  _WB,
  _TM,
  _WT,
  template,
  i18n,
  dateLocale,
  callMethod,
  dictionaryIndexI18n,
  RequestType,
  angularNavigator,
  Memory,
  Tree,
  dojoLang,
  documentBase,
  all,
  when,
  string,
  domClass,
  domStyle,
  on,
  query,
  domConstruct,
  dom,
  focusUtil,
  documentModule,
  requestModule,
  RequestStatus,
  FolderUtils
) => {
  //Clase solo funciona correctamente cuando se usa struts
  if (!window.dialog) {
    window.dialog = alert;
  }
  const archived = dom.byId('archived');
  const userDef = callMethod({
    url: 'Document.User.action',
    method: 'getCurrentUserIdName',
    params: []
  });
  const nodesDef = FolderUtils.loadNodes(false);
  const $noop = () => {};
  let docTypesDef;
  let storageTypesDef;
  let departmentDef;
  let precedingDepartment;
  let templateSurveyIdDef;
  let restrictDepartmentDef;
  let surveyThemeIdDef;
  let collectStoreRespDef;
  let informationClassificationDef;
  let dispositionDef;
  const tmp = {
    department: null
  };
  const statusList = [
    { name: requestModule.statusName_expired, value: 10 },
    { name: requestModule.statusName_standBy, value: 9 },
    { name: requestModule.statusName_requested, value: 3 },
    { name: requestModule.statusName_rejected, value: 5 },
    { name: requestModule.statusName_returned, value: 4 },
    { name: requestModule.statusName_in_process, value: 6 },
    { name: requestModule.statusName_finished, value: 7 }
  ];
  function shouldLoadRestrictRecordsFieldValues(type, documentRequestVerify, documentRequestView) {
    const loadValues =
      type === DocumentRequest.aprove || type === DocumentRequest.FILL || type === DocumentRequest.cancel || documentRequestVerify || documentRequestView;
    return loadValues;
  }
  function defineDocumentRequestData(docTypeOwner, type, documentRequestVerify, documentRequestView) {
    if (docTypeOwner?.documentType?.documentControlledType) {
      if (docTypeOwner.documentType.dictionaryIndexId) {
        docTypesDef = callMethod({
          url: 'Document.action',
          method: 'getDocumentTypes',
          params: [docTypeOwner.documentType.documentControlledType, docTypeOwner.documentType.dictionaryIndexId]
        });
      } else {
        docTypesDef = callMethod({
          url: 'Document.action',
          method: 'getDocumentTypes',
          params: [docTypeOwner.documentType.documentControlledType]
        });
      }
    } else {
      docTypesDef = callMethod({
        url: 'Document.action',
        method: 'getDocumentTypes',
        params: []
      });
    }
    if (!storageTypesDef || storageTypesDef.isFulfilled()) {
      storageTypesDef = callMethod({
        url: 'Document.action',
        method: 'getStoragePlaces',
        params: []
      });
    }
    if (!departmentDef) {
      departmentDef = (businessUnitDepartment, type) => {
        const businessUnitDepartmentId = businessUnitDepartment ? businessUnitDepartment.id : null;
        return callMethod({
          url: 'OptBusinessUnitDepartment.action',
          method: 'getDepartmentsForRequest',
          params: [businessUnitDepartmentId, type]
        });
      };
    }
    if (!precedingDepartment) {
      precedingDepartment = (requestId) =>
        callMethod({
          url: 'OptBusinessUnitDepartment.action',
          method: 'getLastDepartmentsForRequest',
          params: [requestId]
        });
    }
    if (!templateSurveyIdDef || templateSurveyIdDef.isFulfilled()) {
      templateSurveyIdDef = callMethod({
        url: 'Document.action',
        method: 'getFormTemplateDef',
        params: []
      });
    }
    if (!restrictDepartmentDef || restrictDepartmentDef.isFulfilled()) {
      const loadValues = shouldLoadRestrictRecordsFieldValues(type, documentRequestVerify, documentRequestView);
      if (loadValues) {
        if (docTypeOwner?.surveyId && docTypeOwner.surveyId !== -1) {
          restrictDepartmentDef = callMethod({
            url: 'Request.Survey.action',
            method: 'getRestrictRecordsFieldValues',
            params: [docTypeOwner.surveyId]
          });
        } else {
          console.error('Could not load restrict records field values as not sol or doc defined');
          restrictDepartmentDef = core.resolvedPromise();
        }
      } else {
        restrictDepartmentDef = core.resolvedPromise();
      }
    }
    if (!surveyThemeIdDef || surveyThemeIdDef.isFulfilled()) {
      surveyThemeIdDef = callMethod({
        url: 'Document.action',
        method: 'getSurveyThemesDef',
        params: []
      });
    }
  }
  let DocumentRequest = {
    checkImg: `${require.toUrl('bnext/module/images')}/button_accept.png`,
    rejectImg: `${require.toUrl('bnext/module/images')}/button_cancel.png`,
    _externalCatalogsWithHierarchyData: null,
    _restrictFormData: null,
    templateString: template,
    codeMsgNode: null,
    lan: i18n,
    documentModule: documentModule,
    test: null,
    lanCodeDom: null,
    domNode: null,
    type: 0,
    status: 0,
    request: null,
    folder: null,
    node: null,
    creator: null,
    doc: dojoLang.clone(documentBase),
    sol: null,
    changed: null,
    uploadPath: '',
    fileDisplay: '',
    deferreds: null,
    file: null,
    externalFileSelect: false,
    surveyId: null,
    outstandingSurveysId: null,
    templateSurveyId: null,
    collectingAndStoreResponsible: null,
    collectingAndStoreResponsibleDescription: null,
    dispositionDiv: null,
    disposition: null,
    informationClassificationDiv: null,
    informationClassification: null,
    licenceUserCheck: null,
    restrictRecordsByDepartmentContainer: null,
    restrictRecordsByDepartment: null,
    validateAccessFormDepartmentContainer: null,
    validateAccessFormDepartment: null,
    restrictRecordsFieldContainer: null,
    restrictRecordsField: null,
    lblEmptyRestrictRecordsField: null,
    templateSurveyChanged: false,
    templateSurveyValidationId: 0,
    templateSurveyIdDiv: null,
    surveyThemeId: null,
    surveyThemeValidationId: 1,
    surveyThemeChanged: null,
    surveyThemeIdDiv: null,
    department: null,
    placedAtDialog: null,
    formTemplateDiv: null,
    fldDrop: null,
    fileButtons: null,
    fileLabel: null,
    selectFolder: null,
    fldDialog: null,
    surveyForm: null,
    isAFormRequest: false,
    busy: false,
    completed: false,
    formTemplateModify: null,
    visorPdf: null,
    viewDocument: null,
    dynamicFields: null,
    dialogCache: {},
    fldDialogDiv: null,
    archived: false,
    _getExternalCatalogsWithHierarchyDataAttr: function () {
      return this._externalCatalogsWithHierarchyData;
    },
    _setExternalCatalogsWithHierarchyDataAttr: function (dataParam) {
      let data = dataParam;
      if (data === null || typeof data === 'undefined') {
        data = [];
      }
      const preSort = new Memory({
        data: data,
        idProperty: 'value'
      });
      const sortedList = new Memory({
        data: preSort.query(
          {},
          {
            sort: [
              {
                attribute: 'text',
                descending: false
              }
            ]
          }
        ),
        idProperty: 'value'
      });
      this.restrictRecordsField.set('store', sortedList);
      this.restrictRecordsField.set('searchAttr', 'text');
      if (data.length === 1) {
        this.restrictRecordsField.set('value', `${data[0].value}`);
        this.restrictRecordsField.set('disabled', true);
        domClass.add(this.lblEmptyRestrictRecordsField, 'displayNone');
      }
      if (data.length === 0) {
        domClass.remove(this.lblEmptyRestrictRecordsField, 'displayNone');
      } else {
        domClass.add(this.lblEmptyRestrictRecordsField, 'displayNone');
      }
      if (this.doc && this.doc.restrictRecordsField !== null && typeof this.doc.restrictRecordsField !== 'undefined' && this.doc.restrictRecordsField !== '') {
        this.restrictRecordsField.set('value', this.doc.restrictRecordsField);
      }
      if (
        this.sol &&
        this.restrictRecordsField.get('value') === '' &&
        this.sol.restrictRecordsField !== null &&
        typeof this.sol.restrictRecordsField !== 'undefined' &&
        this.sol.restrictRecordsField !== ''
      ) {
        this.restrictRecordsField.set('value', this.sol.restrictRecordsField);
      }
      if (this.restrictRecordsField.get('value') === null || typeof this.restrictRecordsField.get('value') === 'undefined') {
        console.log('fail!!, cannot set the restrictRecordsField >.<');
        this.restrictRecordsField.set('value', '');
      }
      this._externalCatalogsWithHierarchyData = data;
    },
    _getRestrictFormDataAttr: function () {
      return this._restrictFormData;
    },
    _setRestrictFormDataAttr: function (data) {
      if (!data) {
        if (!this.restrictRecordsByDepartment.get('checked')) {
          return;
        }
        this.validateAccessFormDepartment.set('checked', true);
        this.restrictRecordsField.set('value', '');
        return null;
      }
      this.restrictRecordsByDepartment.set('checked', true);
      this.validateAccessFormDepartment.set('checked', false);
      this.restrictRecordsField.set('value', `${data.restrictRecordsField}`);
      this._restrictFormData = data;
    },
    _renderNodes: function () {
      nodesDef.then(
        dojoLang.hitch(this, function (nodesModel) {
          this.folder = registry.byId('requestTree');
          if (!this.folder) {
            this.folder = new Tree({
              id: 'requestTree',
              persist: true,
              model: nodesModel,
              onClick: dojoLang.hitch(this, function (obj, elem, evt) {
                if (obj.readonly) {
                  FolderUtils.warnNotSelectAccess();
                  return;
                }
                this.selectFolder.innerHTML = `/${(obj.path || obj.code).replace(/\\/g, '/')}`;
                this.selectFolder.title = `/${(obj.path || obj.code).replace(/\\/g, '/')}`;
                this.fldDrop.set('label', i18n.folderSelectedInput);
                if (this.fldDropLabel) {
                  this.fldDropLabel.value = i18n.folderSelectedInput;
                }
                this.node = obj;
                domClass.add(this.fldDialogDiv, 'hidden');
              }),
              onOpen: FolderUtils.openTreeNode,
              showRoot: false
            });
          }
          this.folder.placeAt(this.fldDialog);
          on(
            this.fldDrop,
            'click',
            dojoLang.hitch(this, function () {
              domClass.toggle(this.fldDialogDiv, 'hidden');
            })
          );
        })
      );
    },
    normalizeMissingProperties: function () {
      if (this.doc) {
        if (this.doc?.fileContent?.id && !this.doc?.fileId) {
          this.doc.fileId = this.doc.fileContent.id;
        }
        if (this.doc?.nodePath?.id && !this.doc?.nodoId) {
          this.doc.nodoId = this.doc.nodePath.id;
        }
        if (this.doc?.survey?.id && !this.doc?.surveyId) {
          this.doc.surveyId = this.doc.survey.id;
        }
        if (this.doc?.documentType?.id && !this.doc?.tipo) {
          this.doc.tipo = this.doc.documentType.id;
        }
      }
      if (this.sol) {
        if (this.sol?.fileContent?.id && !this.sol?.fileId) {
          this.sol.fileId = this.sol.fileContent.id;
        }
        if (this.sol?.nodePath?.id && !this.sol?.nodoId) {
          this.sol.nodoId = this.sol.nodePath.id;
        }
        if (this.sol?.survey?.id && !this.sol?.surveyId) {
          this.sol.surveyId = this.sol.survey.id;
        }
        if (this.sol?.documentType?.id && !this.sol?.tipo) {
          this.sol.tipo = this.sol.documentType.id;
        }
      }
    },
    postCreate: function postCreate() {
      this.normalizeMissingProperties();
      let datepattern;
      const lang = dom.byId('SYSTEM_LANGUAGE') !== null ? dom.byId('SYSTEM_LANGUAGE').value : null;
      if (lang !== null && lang === 'en') {
        datepattern = 'MM/dd/yyyy';
      } else {
        datepattern = 'dd/MM/yyyy';
      }
      if (this.unknownCode && this.unknownCodeLbl) {
        domAttr.set(this.unknownCodeLbl, 'for', this.unknownCode.id);
      }
      this.date.set(
        'value',
        dateLocale.format(new Date(), {
          selector: 'date',
          datePattern: datepattern
        })
      );
      this.fUploader.onFileSelected = dojoLang.hitch(this, this.onFileSelected);
      this.fUploader.onDelete = dojoLang.hitch(this, this._onFileDeleted);
      this._setEnablePdfViewer(this.enablePdfViewer);
      this.fUploader.set('uploadPath', this.uploadPath);
      this._renderNodes();
      this.templateSurveyChanged = false;
      this.deferreds = this._loadDocumentData().then(
        dojoLang.hitch(this, function (a, b, c) {
          //Se deshabilitan todos los campos que no son editables
          if (
            (this.type === DocumentRequest.update ||
              this.type === DocumentRequest.editDetails ||
              this.type === DocumentRequest.aprove ||
              this.type === DocumentRequest.cancel ||
              this.type === DocumentRequest.prom ||
              this.type === DocumentRequest.newProm ||
              this.type === DocumentRequest.new) &&
            +(this.doc || { id: '' }).id > 0
          ) {
            this.slimReportName?.set('required', false);
            this.name.set('value', this.doc.description);
            this.code.set('value', this.doc.code);
            this.docType.set('value', (this.doc.documentType || { id: '' }).id);
            if ((this.type === DocumentRequest.update || this.type === DocumentRequest.editDetails) && this.doc.documentType.documentControlledType === 'uncontrolled') {
              this.name.set('disabled', false);
              this.docType.set('disabled', false);
            } else {
              this.name.set('disabled', true);
              this.docType.set('disabled', true);
            }
            this.code.set('disabled', true);
            this.version.set('disabled', true);
            if (this.type === DocumentRequest.aprove || this.type === DocumentRequest.cancel) {
              this.department.set('disabled', true);
              this.disposition?.set('disabled', true);
              this.informationClassification?.set('disabled', true);
              this.collectingAndStoreResponsible?.set('disabled', true);
              this.licenceUserCheck?.set('disabled', true);
              domClass.add(this.nodeDiv, 'displayNone');
            }

            this.unknownCode.set('disabled', true);
            if (this.doc?.fileId) {
              this.file = { id: this.doc.fileId };
            }
            this.version.set('value', this.doc.version);
            if (this.doc) {
              this.doc.storagePlaceId && this.storagePlace.set('value', this.doc.storagePlaceId);
              this.doc.retentionTime && this.storageTime.set('value', this.doc.retentionTime);
              this.doc.retentionText && this.storageSpan.set('value', this.doc.retentionText);
            } else if (this.sol) {
              this.sol.storagePlaceId && this.storagePlace.set('value', this.sol.storagePlaceId);
              this.sol.retentionTime && this.storageTime.set('value', this.sol.retentionTime);
              this.sol.retentionText && this.storageSpan.set('value', this.sol.retentionText);
            }
          }
          let reqType;
          if (this.type === DocumentRequest.create) {
            reqType = i18n.NEW;
          } else if (this.type === DocumentRequest.update) {
            reqType = i18n.MODIFICACTION;
          } else if (this.type === DocumentRequest.editDetails) {
            reqType = i18n.EDIT_DETAILS;
          } else if (this.type === DocumentRequest.aprove) {
            reqType = i18n.APROVE;
          } else if (this.type === DocumentRequest.cancel) {
            reqType = i18n.CANCEL;
          } else if (this.type === DocumentRequest.FILL) {
            reqType = i18n.FILL;
          } else {
            reqType = i18n.PROMOTION;
          }
          // si no es ninguna de las anteriores debe ser de promocion
          if ((typeof this.surveyId.value === 'number' && this.surveyId.value > 0) || (this.sol && typeof this.sol.surveyId === 'number' && this.sol.surveyId > 0)) {
            reqType += ` / ${i18n.formTemplate}`;
            this.isAFormRequest = true;
          }
          this.requestType.set('value', reqType);
          if (!this.status) {
            this.status = 9;
          }
          const statusName = statusList.find((item) => this.status === item.value);
          this.statusDom.set('value', statusName.name);
          if (this.type === DocumentRequest.aprove || this.type === DocumentRequest.cancel || this.type === DocumentRequest.editDetails) {
            this.docType.set('disabled', true);
            domClass.add(this.fUploaderContent, 'displayNone');
          }
          if (this.type === DocumentRequest.aprove || this.type === DocumentRequest.cancel) {
            this.fldDrop.set('disabled', true);
          }
          if (this.isAFormRequest) {
            const externalCatalogsWithHierarchyData = this.get('externalCatalogsWithHierarchyData');
            if (externalCatalogsWithHierarchyData === null || typeof externalCatalogsWithHierarchyData === 'undefined') {
              this.set('externalCatalogsWithHierarchyData', []);
            }
            domClass.add(this.fUploaderContent, 'displayNone');
            domClass.remove(this.restrictRecordsByDepartmentContainer, 'displayNone');
            this.fUploader.set({ required: false });
            if (this.documentRequestVerify || this.documentRequestView) {
              domClass.add(this.fUploaderContent, 'displayNone');
              if (this.templateSurveyIdDiv) {
                this.templateSurveyIdDiv.style.display = 'none';
              }
              this.surveyThemeId.set('disabled', true);
              if (this.type === DocumentRequest.create) {
                this.slimReportNameDiv && domClass.remove(this.slimReportNameDiv, 'displayNone');
              }
              if (archived && archived.value === 'true' && this.archived) {
                loader.hideLoader();
                return;
              }
              this.formViewContainer && domClass.remove(this.formViewContainer, 'displayNone');
            }
            if (this.isAFormRequest && !this.documentRequestView) {
              this.templateSurveyId.set('style', {
                display: ''
              });
              if (this.surveyThemeIdDiv) {
                this.surveyThemeIdDiv.style.display = '';
              }
              this.slimReportName?.set('required', false);
            }
          } else {
            this.slimReportName?.set('required', false);
          }
          if (this.sol?.creationDate) {
            this.date.set(
              'value',
              dateLocale.format(new Date(this.sol.creationDate), {
                selector: 'date',
                datePattern: datepattern
              })
            );
          }
          if (this.type === DocumentRequest.cancel) {
            this.storageSpan.set({ disabled: true, required: false });
            this.storagePlace.set({ disabled: true, required: false });
            this.storageTime.set({ disabled: true, required: false });
          }
          if (!this.isAFormRequest) {
            if (this.templateSurveyIdDiv) {
              this.templateSurveyIdDiv.style.display = 'none';
            }
            if (this.surveyThemeIdDiv) {
              this.surveyThemeIdDiv.style.display = 'none';
            }
          } else if (this.type === DocumentRequest.FILL) {
            this.code.labelDom.innerText = i18n.fillCode;
          }
          if (this.type === DocumentRequest.create) {
            if (this.sol) {
              this.templateSurveyId.set('disabled', true);
            } else {
              this.templateSurveyId.set('disabled', false);
            }
          } else {
            this.storagePlace.set({ required: false }); // no requerir en caso de modificacion
            this.templateSurveyId.set('disabled', true);
            if (this.type === DocumentRequest.aprove || this.type === DocumentRequest.FILL || this.type === DocumentRequest.cancel || this.documentRequestView) {
              this.restrictRecordsByDepartment.set('disabled', true);
              this.validateAccessFormDepartment.set('disabled', true);
              this.restrictRecordsField.set('disabled', true);
            }
          }
          if (this.department.store.data.length === 0) {
            // No se tienen departamentos
            domClass.remove(this.lblEmptyDepartment, 'displayNone');
          }
          loader.hideLoader();
        }),
        () => {
          loader.hideLoader();
        }
      );
      this._currentUser();
      this.dialogCache = {};
      this.placeAt(this.requestDialog.get('containerNode'));
      this.requestDialog.set('draggable', false);
      domClass.add(this.requestDialog.get('containerNode').parentNode, 'fixedTop fancy-scroll');
      if (this.isAFormRequest) {
        domClass.add(this.requestDialog.get('containerNode'), 'documentRequestForm');
      }

      on(window, 'resize', () => {
        this._resize.call(this);
      });
      for (const body of query('body.bnext')) {
        domStyle.set(body, 'overflow', 'hidden');
      }
      domClass.toggle(this.requestDialog.domNode, 'fullWidthScreen', true);
      domClass.add(this.requestDialog.domNode, 'grid-floating-active grid-container');
      this._resize();
      core.dijitChangeIE(this.domNode);
      if (!this.isAFormRequest) {
        this.hideLoader();
      } else {
        if (this.type === DocumentRequest.cancel) {
          this.hideLoader();
        }
      }
      setTimeout(() => {
        focusUtil.focus(this.reason);
      }, 100);
      on(this.form, 'change', () => {
        this.changed = true;
      });
    },
    destroyRecursive: function () {
      try {
        const dijitDom = this.domNode;
        // biome-ignore lint/style/noArguments: TODO: Fix this
        this.inherited(arguments);
        if (dijitDom) {
          domConstruct.destroy(dijitDom);
        }
      } catch (e) {
        console.error('Can not destroy DocumentRequest component');
        console.error(e);
      }
    },
    _resize: function () {
      this.requestDialog.resize();
      this.requestDialog._position();
    },
    _currentUser: function _prepareRequest() {
      userDef.then(
        dojoLang.hitch(this, function (a) {
          this.creator = this.creator || a;
          this.currentUser.set('value', this.creator.description);
        })
      );
    },
    _setEnablePdfViewer: function (value) {
      this.enablePdfViewerDivText.set('value', value === 1 ? documentModule.enabledPdfViewer : documentModule.disabledPdfViewer);
      this.fUploader.set('enablePdfViewer', value);
      this.enablePdfViewer = value;
    },
    _setPdfViewerFitDocumentScreen: function (value) {
      this.fUploader.downloadParameters = `fileId=[id]&documentId=-1&pdfViewerFitDocumentScreen=${value !== 0}`;
    },
    _onNameChange: function _onNameChange(value) {
      if (this.onNameChange !== $noop) {
        when(
          this.onNameChange(value),
          dojoLang.hitch(this, function (repeated) {
            this.nameCheck.src = repeated ? this.rejectImg : this.checkImg;
          })
        );
      }
    },
    onNameChange: $noop,
    _onCodeChange: function _onCodeChange(value) {
      if (this.onCodeChange !== $noop) {
        when(
          this.onCodeChange(value),
          dojoLang.hitch(this, function (repeated) {
            this.codeCheck.src = repeated ? this.rejectImg : this.checkImg;
          })
        );
      }
    },
    onCodeChange: $noop,
    _onUnknownCodeChange: function _onUnknownCodeChange(checked) {
      this.code.set('disabled', checked);
      if (
        (this.type === DocumentRequest.update ||
          this.type === DocumentRequest.editDetails ||
          this.type === DocumentRequest.aprove ||
          this.type === DocumentRequest.cancel ||
          this.type === DocumentRequest.prom) &&
        (this.doc || { id: '' }).id !== -1
      ) {
        //keep code (already setted)
      } else {
        this.code.set('value', checked ? this.lan.unknowncode : '');
      }
      if (this.onUnknownCodeChange) {
        this.onUnknownCodeChange();
      }
    },
    _onQuestionForSelectDepartment: function (e) {
      if (e && e !== '' && this.restrictRecordsByDepartment && this.restrictRecordsByDepartment.get('checked')) {
        require(['bnext/survey/formObject/field/GlobalSurveyUtil'], (GlobalSurveyUtil) => {
          const type = 'businessUnitDepartment';
          const id = `${e}_uiObject`;
          ///Cambia el cuadrito verde correspondiente.
          GlobalSurveyUtil.refreshKeyRender(type, id, e, 1);
        });
      }
    },
    _onDepartmentChange: function () {
      if (this.onDepartmentChange) {
        // biome-ignore lint/style/noArguments: TODO: Fix this
        this.onDepartmentChange.apply(this, arguments);
      }
    },
    _onVersionChange: function (evt) {
      if (this.onVersionChange) {
        this.onVersionChange(evt);
      }
    },
    onVersionChange: null,
    onDepartmentChange: function () {
      const deptId = this.department.get('value');
      if (core.isNull(deptId) || !this.isAFormRequest || +this.templateSurveyId.get('value') > 0) {
        return;
      }
      const dept = this.department.get('store').query({ id: deptId })[0];
      if (!dept || dept.formTemplateId === +this.templateSurveyId.get('value')) {
        return;
      }
      this.templateSurveyId.set('value', dept.formTemplateId);
    },
    onUnknownCodeChange: $noop,
    _onDocTypeChange: function _onDocTypeChange(value) {
      let type = {};
      this._resetDocType();
      if (this.docType?.get('store').get(value)) {
        type = this.docType.get('store').get(value);
        if (type.documentControlledType) {
          this.documentControlledType.set('value', documentModule[type.documentControlledType]);
        } else {
          this.documentControlledType.set('value', i18n.docTypeInactive);
        }
        if (type.dictionaryIndex) {
          this.dictionaryIndex.set('value', dictionaryIndexI18n[type.dictionaryIndex.description] || type.dictionaryIndex.description);
          if (type.dictionaryIndex.id === 1) {
            // Numerico
            if (this.version) {
              this.version.set('regExp', '[0-9]{0,15}');
              this.version.set('pattern', '[0-9]{0,15}');
              this.version.set('invalidMessage', i18n.versionNumericInvalidMessage);
            }
          } else if (type.dictionaryIndex.id === 2) {
            // Alfabetico
            if (this.version) {
              this.version.set('regExp', '[A-Za-z]{1,15}');
              this.version.set('pattern', '[A-Za-z]{1,15}');
              this.version.set('invalidMessage', i18n.versionAlphabeticInvalidMessage);
            }
          } else if (type.dictionaryIndex.id === 3) {
            // Binario
            if (this.version) {
              this.version.set('regExp', '[01]{1,15}');
              this.version.set('pattern', '[01]{1,15}');
              this.version.set('invalidMessage', i18n.versionBinaryInvalidMessage);
            }
          } else {
            if (this.version) {
              this.version.set('regExp', '[a-zA-Z0-9\\-]{1,15}');
              this.version.set('pattern', '[a-zA-Z0-9\\-]{1,15}');
              this.version.set('invalidMessage', i18n.versionInvalidMessage);
            }
          }
        } else {
          this.dictionaryIndex.set('value', i18n.docTypeInactive);
        }
        if (this.type === DocumentRequest.FILL) {
          domClass.add(this.collectingAndStoreResponsibleContent, 'displayNone');
          domClass.add(this.collectingAndStoreRespDesc, 'displayNone');
          this.collectingAndStoreResponsible.set({ required: false });
          this.collectingAndStoreResponsibleDescription.set({ required: false });
        } else if (type.collectingAndStoreResponsible) {
          domClass.remove(this.collectingAndStoreResponsibleContent, 'displayNone');
          if (this.licenceUserCheck.get('checked')) {
            domClass.remove(this.collectingAndStoreResp, 'displayNone');
            this.collectingAndStoreResponsible.set({ required: true });
          } else {
            domClass.remove(this.collectingAndStoreRespDesc, 'displayNone');
            this.collectingAndStoreResponsibleDescription.set({ required: true });
          }
        } else {
          this.collectingAndStoreResponsible.set('value', '');
          this.collectingAndStoreResponsibleDescription.set('value', '');
        }
        if (type.informationClassification && this.type !== DocumentRequest.FILL) {
          domClass.remove(this.informationClassificationDiv, 'displayNone');
          this.informationClassification.set({ required: true });
        } else {
          this.informationClassification.set('value', '');
        }
        if (type.disposition && this.type !== DocumentRequest.FILL) {
          domClass.remove(this.dispositionDiv, 'displayNone');
          this.disposition.set({ required: true });
        } else {
          this.disposition.set('value', '');
        }
        this._setEnablePdfViewer(type.enablePdfViewer);
        this._setPdfViewerFitDocumentScreen(type.pdfViewerFitDocumentScreen);
      }
      if (type.isRetainable && this.type !== DocumentRequest.FILL) {
        domClass.remove(this.storagePlaceContent, 'displayNone');
        domClass.remove(this.storageTimeContent, 'displayNone');
        domClass.remove(this.storageSpanContent, 'displayNone');
        if (this.type !== DocumentRequest.cancel) {
          if (this.type === DocumentRequest.aprove) {
            this.storagePlace.set('disabled', true);
            this.storageTime.set('disabled', true);
            this.storageSpan.set('disabled', true);
          } else {
            this.storagePlace.set('disabled', false);
            this.storageTime.set('disabled', false);
            this.storageSpan.set('disabled', false);
          }
        }
      }
      if (type.documentControlledType === 'uncontrolled') {
        this.department.set({ required: true });
      }
      if (type.readOnlyEnabled === 1) {
        if (this.file !== null && this.file.hasPdf === 0) {
          this.file = null;
        }
      }
      if (this.onDocTypeChange) {
        this.onDocTypeChange();
      }
    },
    _onLicenceUserCheck: function () {
      if (this.type === DocumentRequest.FILL) {
        return;
      }
      if (this.licenceUserCheck.get('checked')) {
        domClass.add(this.collectingAndStoreRespDesc, 'displayNone');
        domClass.remove(this.collectingAndStoreResp, 'displayNone');
        this.collectingAndStoreResponsibleDescription.set({ required: false });
        this.collectingAndStoreResponsible.set({ required: true });
      } else {
        domClass.remove(this.collectingAndStoreRespDesc, 'displayNone');
        domClass.add(this.collectingAndStoreResp, 'displayNone');
        this.collectingAndStoreResponsibleDescription.set({ required: true });
        this.collectingAndStoreResponsible.set({ required: false });
      }
    },
    _resetDocType: function () {
      //Se marca el departamento como "no requerido"
      this.department.set({ required: false });
      this.informationClassification.set({ required: false });
      this.disposition.set({ required: false });
      this.collectingAndStoreResponsibleDescription.set({ required: false });
      this.collectingAndStoreResponsible.set({ required: false });
      //Se oculta lugar de almacenamiento
      domClass.add(this.storagePlaceContent, 'displayNone');
      domClass.add(this.storageTimeContent, 'displayNone');
      domClass.add(this.storageSpanContent, 'displayNone');
      domClass.add(this.collectingAndStoreResponsibleContent, 'displayNone');
      this.storagePlace.set('disabled', true);
      this.storageTime.set('disabled', true);
      this.storageSpan.set('disabled', true);
    },
    onDocTypeChange: $noop,
    onFileSelected: function onFileSelected(file) {
      this.file = file;
      const type = this.docType.get('store').get(this.docType.get('value'));
      if (type !== undefined && type !== null) {
        if (type.readOnlyEnabled && file.hasPdf === 0) {
          core.dialog(i18n.fileInvalidReadOnlyEnabled, core.i18n.yes, core.i18n.no).then(
            dojoLang.hitch(this, function () {
              this.fUploader._onDelete();
              this.file = null;
            })
          );
        }
      }
    },
    _onFileDeleted: function () {
      this.file = null;
    },
    _onFolderChange: function _onFolderChange() {},
    _completeSubmit: function () {
      this.busy = false;
      this.completed = true;
      this._enableAccept();
    },
    _enableAccept: function () {
      this.accept.set('disabled', false);
    },
    _disableAccept: function () {
      this.accept.set('disabled', true);
    },
    setReasonReadOnly: function (value) {
      this.reason.set({ value: value });
      this.reason.refreshNoValid();
      this.reason.set({ disabled: true });
    },
    onFolderChange: $noop,
    _onSubmit: function _onSubmit() {
      if (this.busy || this.completed) {
        console.error('Busy or completed', this.busy, this.completed);
        if (this.completed) {
          this.hideLoader();
        }
        return false;
      }
      this.showLoader(core.i18n.Validate.saving);
      this._disableAccept();
      const valid = this.form.validate();
      if (!valid || !this.file || !this.node || string.trim(this.reason.value) === '' || this.fUploader.get('busy')) {
        if (valid) {
          let message = false;
          if (string.trim(this.reason.value) === '') {
            message = this.lan.reasonrequest;
          }
          if (!this.node) {
            message = this.lan.needfolder;
          }
          if ((!this.file && !this.isAFormRequest) || this.fUploader.get('busy')) {
            this._enableAccept();
            this.hideLoader();
            this.fUploader.showTooltip(this.lan.needfile);
            return false;
          }
          if (message) {
            dialog(message, this.lan.accept);
            this._enableAccept();
            this.hideLoader();
            return false;
          }
        } else {
          this._enableAccept();
          this.hideLoader();
          return false;
        }
      }
      this._setDocValues();
      this._setSolValues();
      if (!this.completed) {
        this.onBeforeSubmit(this, this.sol).then(dojoLang.hitch(this, this.onBeforeSubmitSuccess), dojoLang.hitch(this, this.onBeforeSubmitFailed));
      }
      return true;
    },
    onBeforeSubmitSuccess: function () {
      if (typeof this.onSubmit === 'function') {
        all(
          this.onSubmit(this.sol, this.file, {
            templateSurveyChanged: this.templateSurveyChanged
          })
        ).then(dojoLang.hitch(this, this._completeSubmit), dojoLang.hitch(this, this._completeSubmit));
      } else {
        this.busy = false;
        this._enableAccept();
      }
      this.doc.department = tmp.department;
    },
    onBeforeSubmitFailed: function (result) {
      this.doc.department = tmp.department;
      this.busy = false;
      if (typeof result === 'object' && result.success === true) {
        this._enableAccept();
        this.hideLoader();
        return;
      }
      if (typeof result === 'string') {
        core.error(result);
      } else {
        console.error(result);
        core.error(i18n.savingError);
      }
      this._enableAccept();
      this.hideLoader();
    },
    onBeforeSubmit: () => core.resolvedPromise(),
    onSubmit: function () {
      this.busy = true;
      return core.resolvedPromise();
    },
    _onLoadEditor: () => {
      window.open('../view/v.document.editor.view');
    },
    onLoadEditor: $noop,
    _onCancel: function _onCancel() {
      if (this.onCancel) {
        all(this.onCancel()).then(
          dojoLang.hitch(this, function () {
            if (this.fUploader) {
              this.fUploader.cancel();
            }
          })
        );
      } else if (this.fUploader) {
        this.fUploader.cancel();
      }
    },
    onCancel: core.resolvedPromises(),
    _setTypeAttr: function _setTypeAttr(t) {
      this.type = t;
    },
    _setTitleAttr: function (titleParam) {
      let title = titleParam;
      title = title || i18n.requestTitle || 'i18n.requestTitle';
      this.mainTitle.innerHTML = title;
    },
    show: function () {
      this.requestDialog.show();
      this.requestDialog.titleBar.style['margin-right'] = '15px';
      core.setGlobalSessionExpiredFunction(this, 'hide');
    },
    hide: function () {
      for (const body of query('body.bnext')) {
        domStyle.set(body, 'overflow', '');
      }
      this.fUploader?.hide();
      this.requestDialog?.hide();
    },
    showLoader: function showLoader(msg) {
      core.showLoader(msg || i18n.uppload);
    },
    hideLoader: function hideLoader() {
      core.hideLoader();
    },
    _loadDocumentData: function _loadDocumentData() {
      let a;
      let c;
      let d;
      let e;
      let f;
      let g;
      let h;
      // biome-ignore lint/style/useConst: TODO: Fix this
      let i;
      let j;
      let k;

      defineDocumentRequestData(this.sol || this.doc || this.test, this.type, this.documentRequestVerify, this.documentRequestView);
      a = docTypesDef.then((dta) => {
        this.docType.set('searchAttr', 'description');
        const preSort = new Memory({
          data: dta
        });

        if (this.sol?.documentType) {
          const previous = preSort.query({ id: this.sol.documentType.id });
          if (+previous.total === 0) {
            const type = this.sol.documentType;
            preSort.put(type);
          }
        } else if (this.doc?.documentType) {
          const previous = preSort.query({ id: this.doc.documentType.id });
          if (+previous.total === 0) {
            const type = this.doc.documentType;
            preSort.put(type);
          }
        }

        const sortedList = new Memory({
          data: preSort.query(
            {},
            {
              sort: [
                {
                  attribute: 'description',
                  descending: false
                }
              ]
            }
          )
        });
        this.docTypeStore = sortedList;
        this.docType.set('store', this.docTypeStore);
      });

      c = storageTypesDef.then((a) => {
        this.storagePlace.set('searchAttr', 'code');
        const preSort = new Memory({
          data: a
        });
        const sortedList = new Memory({
          data: preSort.query(
            {},
            {
              sort: [
                {
                  attribute: 'code',
                  descending: false
                }
              ]
            }
          )
        });
        this.storagePlace.set('store', sortedList);
        if (this.sol) {
          this.sol.storagePlaceId && this.storagePlace.set('value', this.sol.storagePlaceId);
          this.sol.retentionTime && this.storageTime.set('value', this.sol.retentionTime);
          this.sol.retentionText && this.storageSpan.set('value', this.sol.retentionText);
        }
      });
      let department;
      if (this.sol) {
        if (this.sol.surveyId) {
          this.surveyId.value = this.sol.surveyId;
        }
        if (this.sol.outstandingSurveysId) {
          this.outstandingSurveysId.value = this.sol.outstandingSurveysId;
        }
        department = this.sol.department;
        this.restrictRecordsByDepartment.set('checked', !!this.sol.restrictRecordsByDepartment);
        this.validateAccessFormDepartment.set('checked', !!this.sol.validateAccessFormDepartment);
        this.collectingAndStoreResponsibleDescription.set('value', this.sol.collectingAndStoreResponsibleDescription);
      } else if (this.doc) {
        if (this.doc.surveyId) {
          this.surveyId.value = this.doc.surveyId;
        }
        if (this.doc.outstandingSurveysId) {
          this.outstandingSurveysId.value = this.doc.outstandingSurveysId;
        }
        department = this.doc.department;
        this.restrictRecordsByDepartment.set('checked', !!this.doc.restrictRecordsByDepartment);
        this.validateAccessFormDepartment.set('checked', !!this.doc.validateAccessFormDepartment);
        this.collectingAndStoreResponsibleDescription.set('value', this.doc.collectingAndStoreResponsibleDescription);
      }
      this.licenceUserCheck.set('checked', this.collectingAndStoreResponsibleDescription.get('value') === '');
      if (this.sol?.id) {
        f = precedingDepartment(this.sol.id).then(
          dojoLang.hitch(this, function (departmentDescription) {
            if (departmentDescription !== '') {
              this.lastDepartment.set('value', departmentDescription);
              domClass.add(this.lblDepartment, 'displayNone');
              domClass.remove(this.lblCurrentDepartment, 'displayNone');
              domClass.remove(this.divLastDepartment, 'displayNone');
            }
          }),
          (err) => {
            if (err.message !== 'nosession') {
              alert(err);
            }
          }
        );
      }

      d = departmentDef(department, this.type).then(
        (x) => {
          const preSort = new Memory({
            data: x,
            idProperty: 'id'
          });
          const sortedList = new Memory({
            data: preSort.query(
              {},
              {
                sort: [
                  {
                    attribute: 'description',
                    descending: false
                  }
                ]
              }
            ),
            idProperty: 'id'
          });
          if (this.doc?.department) {
            sortedList.put(this.doc.department);
            tmp.department = this.doc.department;
          }
          if (this.sol?.department) {
            sortedList.put(this.sol.department);
          }
          this.department.set('store', sortedList);
          this.department.set('searchAttr', 'description');
          if (x && x.length === 1) {
            this.department.set('value', `${x[0].id}`);
            this.department.set('disabled', true);
          }
          this.doc?.department?.id && this.department.set('value', this.doc.department.id);
          this.sol?.department?.id && this.department.get('value') === '' && this.department.set('value', this.sol.department.id);
          if (+this.department.get('value') === 0) {
            console.log('fail!!, cannot set the department >.<');
            this.department.set('value', '');
          }
        },
        (err) => {
          if (err && err.message !== 'nosession') {
            alert(err);
          }
        }
      );
      let nodoId;
      if (this.sol) {
        nodoId = this.sol.nodoId;
      } else if (this.doc) {
        nodoId = this.doc.nodoId;
      }
      if (nodoId) {
        g = callMethod({
          url: 'Repositorio.action',
          params: [nodoId],
          method: 'load'
        }).then((folder) => {
          this.node = folder;
          this.selectFolder.innerHTML = folder.path;
          this.fldDrop.set('label', i18n.folderSelectedInput);
          if (this.fldDropLabel) {
            this.fldDropLabel.value = i18n.folderSelectedInput;
          }
        });
      } else {
        g = core.resolvedPromise();
      }
      i = this._setupCollectStoreRespDef();
      j = this._setupInformationClassificationDef();
      k = this._setupDispositionDef();

      if (this.isAFormRequest) {
        const e = templateSurveyIdDef.then((x) => {
          x.splice(0, 0, {
            id: '0',
            description: i18n.noForm
          });
          const preSort = new Memory({
            data: x,
            idProperty: 'id'
          });
          const sortedList = new Memory({
            data: preSort.query(
              {},
              {
                sort: [
                  {
                    attribute: 'description',
                    descending: false
                  }
                ]
              }
            ),
            idProperty: 'id'
          });
          this.templateSurveyId.set('store', sortedList);
          this.templateSurveyId.set('searchAttr', 'description');
          this.doc?.templateSurveyId && this.templateSurveyId.set('value', this.doc.templateSurveyId);
          this.sol?.templateSurveyId && this.templateSurveyId.get('value') === '' && this.templateSurveyId.set('value', this.sol.templateSurveyId);
          if (+this.templateSurveyId.get('value') === 0) {
            console.log('fail!!, cannot set the templateSurveyId >.<');
            this.templateSurveyId.set('value', '0');
          } else {
            this.templateSurveyValidationId = +this.templateSurveyId.get('value');
          }
        });
        const i = restrictDepartmentDef.then((x) => {
          if (x !== null && typeof x !== 'undefined') {
            this.set('externalCatalogsWithHierarchyData', x);
          }
        });
        const h = surveyThemeIdDef.then((xParam) => {
          let x = xParam;
          /* To do: eliminar filtro al agregar mas temas de formularios. */
          x = x.filter((theme) => theme.id === 1);
          const preSortThemes = new Memory({
            data: x,
            idProperty: 'id'
          });
          const sortedListThemes = new Memory({
            data: preSortThemes.query(
              {},
              {
                sort: [
                  {
                    attribute: 'description',
                    descending: false
                  }
                ]
              }
            ),
            idProperty: 'id'
          });
          this.surveyThemeId.set('store', sortedListThemes);
          this.surveyThemeId.set('searchAttr', 'description');
          this.doc?.surveyThemeId && this.surveyThemeId.set('value', this.doc.surveyThemeId);
          this.sol?.surveyThemeId && this.surveyThemeId.get('value') === '' && this.surveyThemeId.set('value', this.sol.surveyThemeId);
          if (+this.surveyThemeId.get('value') === 0 || this.surveyThemeId.get('value') === '' || this.surveyThemeId.get('value') === null) {
            console.log('Error!, Failed to set valid value to surveyThemeId');
            this.surveyThemeId.set('value', '1');
          } else {
            this.surveyThemeValidationId = +this.surveyThemeId.get('value');
          }
        });
        return all([a, c, d, e, g, h, i, j, k]);
      }
      return all([a, c, d, g, i, j, k]);
    },
    updateAccessFormDepartment: function () {
      if (this.sol) {
        this.restrictRecordsByDepartment.set('checked', !!this.sol.restrictRecordsByDepartment);
        this.validateAccessFormDepartment.set('checked', !!this.sol.validateAccessFormDepartment);
        this.restrictRecordsField.set('value', this.sol.restrictRecordsField);
      }
    },
    _setupDispositionDef: function () {
      let getAll = true;
      if (this.type === DocumentRequest.create) {
        getAll = false;
      }
      if (!dispositionDef || dispositionDef.isFulfilled()) {
        dispositionDef = callMethod({
          url: 'Document.action',
          method: 'getDispositionCatalog',
          params: [getAll]
        });
      }
      return dispositionDef.then((a) => {
        const preSort = new Memory({
          data: a,
          idProperty: 'value'
        });
        const sortedList = new Memory({
          data: preSort.query(
            {},
            {
              sort: [
                {
                  attribute: 'text',
                  descending: false
                }
              ]
            }
          ),
          idProperty: 'value'
        });
        this.disposition.set('store', sortedList);
        this.disposition.set('searchAttr', 'text');
        if (a && a.length === 1) {
          this.disposition.set('value', `${a[0].value}`);
          this.disposition.set('disabled', true);
        }
        this.doc?.disposition && this.disposition.set('value', this.doc.disposition);
        this.sol?.disposition && this.disposition.get('value') === '' && this.disposition.set('value', this.sol.disposition);
      });
    },
    _setupInformationClassificationDef: function () {
      if (!informationClassificationDef || informationClassificationDef.isFulfilled()) {
        informationClassificationDef = callMethod({
          url: 'Document.action',
          method: 'getInformationClassificationCatalog',
          params: []
        });
      }
      return informationClassificationDef.then((a) => {
        const preSort = new Memory({
          data: a,
          idProperty: 'value'
        });
        const sortedList = new Memory({
          data: preSort.query(
            {},
            {
              sort: [
                {
                  attribute: 'text',
                  descending: false
                }
              ]
            }
          ),
          idProperty: 'value'
        });
        this.informationClassification.set('store', sortedList);
        this.informationClassification.set('searchAttr', 'text');
        if (a && a.length === 1) {
          this.informationClassification.set('value', `${a[0].value}`);
          this.informationClassification.set('disabled', true);
        }
        this.doc?.informationClassification && this.informationClassification.set('value', this.doc.informationClassification);
        this.sol?.informationClassification &&
          this.informationClassification.get('value') === '' &&
          this.informationClassification.set('value', this.sol.informationClassification);
      });
    },
    _setupCollectStoreRespDef: function () {
      if (!collectStoreRespDef || collectStoreRespDef.isFulfilled()) {
        collectStoreRespDef = callMethod({
          url: 'Document.action',
          method: 'getUsersAll',
          params: []
        });
      }
      return collectStoreRespDef.then((a) => {
        const preSort = new Memory({
          data: a,
          idProperty: 'value'
        });
        const sortedList = new Memory({
          data: preSort.query(
            {},
            {
              sort: [
                {
                  attribute: 'text',
                  descending: false
                }
              ]
            }
          ),
          idProperty: 'value'
        });
        this.collectingAndStoreResponsible.set('store', sortedList);
        this.collectingAndStoreResponsible.set('searchAttr', 'text');
        if (a && a.length === 1) {
          this.collectingAndStoreResponsible.set('value', `${a[0].value}`);
          this.collectingAndStoreResponsible.set('disabled', true);
        }
        if (typeof this.doc?.collectingAndStoreResponsible === 'object') {
          this.doc?.collectingAndStoreResponsible && this.collectingAndStoreResponsible.set('value', this.doc.collectingAndStoreResponsible.id);
        } else {
          this.doc?.collectingAndStoreResponsible && this.collectingAndStoreResponsible.set('value', this.doc.collectingAndStoreResponsible);
        }
        this.sol?.collectingAndStoreResponsible &&
          this.collectingAndStoreResponsible.get('value') === '' &&
          this.collectingAndStoreResponsible.set('value', this.sol.collectingAndStoreResponsible);
      });
    },
    _setDocValues: function _setDocValues() {
      this.doc = this.doc || {
        id: -1,
        tipo: ''
      };
      let version = this.version.get('value');
      if (!version) {
        version = null;
      }
      const name = this.name.get('value');
      const node = this.node;
      const file = this.file || {};

      this.doc.description = name;
      this.doc.department = this.department.get('value')
        ? {
            id: this.department.get('value'),
            description: this.department.get('displayedValue')
          }
        : null;
      this.doc.templateSurveyId = +this.templateSurveyId.get('value') || null;
      this.doc.surveyThemeId = +this.surveyThemeId.get('value') || 1;
      this.doc.code = this.unknownCode.get('checked') ? '' : this.code.get('value');
      this.doc.nodo = node;
      this.doc.file = file;
      this.doc.author = this.doc.author || this.creator;
      this.doc.nombreArchivoReal = this.doc.NombreArchivoSistema = file.description || 'No file';
      this.doc.version = version;
      this.doc.status = 1;
      this.doc.restrictRecordsByDepartment = this.restrictRecordsByDepartment.get('checked') || false;
      this.doc.validateAccessFormDepartment = this.validateAccessFormDepartment.get('checked') || false;
      this.doc.restrictRecordsField = this.restrictRecordsField.get('value');
      this.doc.originador = this.creator;
      this.doc.enablePdfViewer = this.enablePdfViewer || 0;
      this.doc.esRespaldo = 0;
      this.doc.readers = 0;
      this.doc.documentoElectronico = 0;
      this.doc.tipo = this.docType.get('store').get(this.docType.get('value')) || this.doc.tipo;
      this.doc.documentoElectronicoId = 0;
      this.doc.contentType = file.contentType || 'text/plain';
      if (this.licenceUserCheck.get('checked')) {
        this.doc.collectingAndStoreResponsible = this.collectingAndStoreResponsible.get('value') || null;
        this.doc.collectingAndStoreResponsibleDescription = null;
      } else {
        this.doc.collectingAndStoreResponsible = null;
        this.doc.collectingAndStoreResponsibleDescription = this.collectingAndStoreResponsibleDescription.value;
      }
      this.doc.informationClassification = this.informationClassification.get('value') || null;
      this.doc.disposition = this.disposition.get('value') || null;
      this._setExtraInfo(this.doc);
    },
    _setSolValues: function _setSolValues() {
      this.sol = this.sol || {
        id: -1,
        status: 1
      };
      const file = this.file || {};
      this.sol.department = this.department.get('item') ? this.department.get('item') : null;
      this.sol.templateSurveyId = +this.templateSurveyId.get('value') || null;
      this.sol.surveyThemeId = +this.surveyThemeId.get('value') || 1;
      this.sol.description = this.name.get('value');
      if (this.isAFormRequest && (this.documentRequestVerify || this.documentRequestView)) {
        this.sol.slimReportName = this.slimReportName.get('value');
      } else if (this.isAFormRequest && this.type === DocumentRequest.create) {
        this.sol.slimReportName = this.name.get('value');
      } else if (this.isAFormRequest && (this.type === DocumentRequest.update || this.type === DocumentRequest.editDetails)) {
        this.sol.slimReportName = null;
      }
      this.sol.code = '';
      this.sol.generateCode = this.unknownCode.get('checked') ? 1 : this.sol.generateCode || 0;
      this.sol.storagePlaceId = +this.storagePlace.get('value') || null;
      this.sol.author = {
        id: (this.sol.author || this.creator).id
      };
      this.sol.restrictRecordsByDepartment = this.restrictRecordsByDepartment.get('checked') || false;
      this.sol.validateAccessFormDepartment = this.validateAccessFormDepartment.get('checked') || false;
      this.sol.restrictRecordsField = this.restrictRecordsField.get('value');
      this.sol.tipoSolicitud = this.type || 1;
      this.sol.type = this.type || 1;
      this.sol.documentCode = this.unknownCode.get('checked') ? '' : this.code.get('value');
      this.sol.fileId = file.id || null;
      this.sol.nodo = {
        id: this.node.id,
        code: this.node.code
      };

      this.sol.documentType = this.docType.get('store').get(this.docType.get('value')) || this.doc.tipo;
      this.sol.documentType = {
        id: this.sol.documentType.id,
        isRetainable: this.sol.documentType.isRetainable,
        dictionaryIndexId: this.sol.documentType.dictionaryIndexId,
        readOnlyEnabled: this.sol.documentType.readOnlyEnabled
      };
      this.sol.document = this.doc && this.doc.id !== -1 ? { id: this.doc.id } : null;
      this.sol.reazon = this.reason.value || this.reason.get('value');
      if (this.doc.version) {
        this.sol.version = this.doc.version;
      }
      this.sol.enablePdfViewer = this.enablePdfViewer || 0;
      if (this.licenceUserCheck.get('checked')) {
        this.sol.collectingAndStoreResponsible = this.collectingAndStoreResponsible.get('value') || null;
        this.sol.collectingAndStoreResponsibleDescription = null;
      } else {
        this.sol.collectingAndStoreResponsible = null;
        this.sol.collectingAndStoreResponsibleDescription = this.collectingAndStoreResponsibleDescription.value || null;
      }
      this.sol.informationClassification = this.informationClassification.get('value') || null;
      this.sol.disposition = this.disposition.get('value') || null;
      this._setExtraInfo(this.sol);
    },
    _setExtraInfo: function (x) {
      if ((x.documentType || x.tipo).isRetainable && +this.storagePlace.get('value') > 0) {
        x.storagePlaceId = this.storagePlace.get('value');
        x.retentionText = this.storageSpan.get('value');
        x.retentionTime = this.storageTime.get('value');
      }
      x.surveyId = this.surveyId ? this.surveyId.value || null : null;
      x.outstandingSurveysId = this.outstandingSurveysId ? this.outstandingSurveysId.value || null : null;
    },
    _getContentType: function _getContentType() {
      //función utiliza HTML5 para obtener el content-type si no se puede usar regresa vacio
      let contentType = '';
      if (this.fileInput.files?.[0]) {
        contentType = this.fileInput.files[0].type;
      }
      return contentType;
    },
    _setNodeAttr: function (node) {
      this.node = node;
      this.selectFolder.innerHTML = node.path;
      this.fldDrop.set('title', node.path);
      if (this.fldDropLabel) {
        this.fldDropLabel.value = i18n.folderSelectedInput;
      }
    },
    _onFormTemplateModifyClick: function () {
      if (!this.surveyId.value) {
        core.dialog(i18n.invalidRequest);
        return;
      }
      let msg;
      if (this.changed) {
        msg = i18n.requestChanged;
      } else {
        msg = i18n.formTemplateConfirmation;
      }

      const d = core.eventedDialog(msg, [core.i18n.Validate.yes, core.i18n.Validate.no]);
      d.then((dialog) => {
        dialog.event('clickBtn1', () => {
          this.showLoader();
          if (this.onFormTemplateModifyClick) {
            this.onFormTemplateModifyClick();
          }
          this.surveyForm.target = 'basefrm'; //+_self.surveyId.value;
          if (this.outstandingSurveysId.value) {
            this.surveyForm.id.value = `O${this.outstandingSurveysId.value}`;
          } else {
            this.surveyForm.id.value = this.surveyId.value;
          }
          this.surveyForm.show_hide_buttons.value = 'inline-flex';
          this.surveyForm.sourcePage.value = 'VERIFY';
          this.surveyForm.requestId.value = this.sol?.id || null;
          this.surveyForm.viewMode.value = 'EDIT';
          this.surveyForm.action = '../DPMS/v.request.survey.mode.action';
          this.surveyForm.submit();
          this.fUploader?.cancel();
          dialog.hide();
        });
        dialog.event('clickBtn2', () => {
          dialog.hide();
          this.fUploader?.cancel();
        });
      });
    },
    _onFormContinueClick: function () {
      require(['bnext/administrator/solicitudes/fillRequestHandle'], dojoLang.hitch(this, function (fillRequestHandle) {
        if (this.status === RequestStatus.IN_PROCESS) {
          const isAdminRole = this.authRole === 'ADMIN';
          fillRequestHandle(
            this.sol.fillOutDocument.surveyId,
            this.sol.fillOutDocument.id,
            this.sol.outstandingSurveysId,
            this.sol.id,
            null,
            isAdminRole,
            isAdminRole,
            true,
            this.sol?.fillOutDocument?.description || ''
          );
        } else {
          fillRequestHandle(
            this.sol.fillOutDocument.surveyId,
            this.sol.fillOutDocument.id,
            this.sol.outstandingSurveysId,
            null,
            null,
            null,
            null,
            true,
            this.sol?.fillOutDocument?.description || ''
          );
        }
      }));
    },
    _onFormTemplatePreviewClick: function () {
      if (!this.surveyId.value) {
        core.dialog(i18n.invalidRequest);
        return;
      }
      if (this.onFormTemplatePreviewClick) {
        this.onFormTemplatePreviewClick();
      }
      this.surveyForm.target = `request_survey_preview_${this.surveyId.value}`;
      if (this.outstandingSurveysId.value) {
        this.surveyForm.id.value = `O${this.outstandingSurveysId.value}`;
      } else {
        this.surveyForm.id.value = this.surveyId.value;
      }
      this.surveyForm.requestId.value = this.sol?.id || null;
      this.surveyForm.viewMode.value = null;
      angularNavigator.navigateLegacy(
        `v.request.survey.preview.view?id=${this.surveyForm.id.value}&task=preview${this.surveyForm.requestId.value !== null ? `&requestId=${this.surveyForm.requestId.value}` : ''}`,
        null,
        null,
        this.sol?.fillOutDocument?.description || ''
      );
    },
    onFormTemplateModifyClick: $noop,
    onFormTemplatePreviewClick: $noop,
    _onTemplateChange: function () {
      this.templateSurveyChanged = +this.templateSurveyValidationId !== +this.templateSurveyId.get('value');
      this.templateSurveyValidationId = this.templateSurveyId.get('value');
    },
    _onSurveyThemeChange: function () {
      this.surveyThemeChanged = +this.surveyThemeValidationId !== +this.surveyThemeId.get('value');
      this.surveyThemeValidationId = this.surveyThemeId.get('value');
      const themes = [];
      for (const item of this.surveyThemeId.store.data) {
        themes.push(item.fileName);
      }
      const stageDom = dom.byId('stage');
      if (stageDom) {
        domClass.remove(stageDom, themes);
        domClass.add(stageDom, this.surveyThemeId.item.fileName);
      }
    },
    _onRestrictRecordsByDepartmentChange: function (checked) {
      if (checked) {
        domClass.remove(this.validateAccessFormDepartmentContainer, 'displayNone');
        this._onValidateAccessFormDepartmentChange(this.validateAccessFormDepartment.checked);
      } else {
        domClass.add(this.validateAccessFormDepartmentContainer, 'displayNone');
        const validateAccessFormDepartment = this.validateAccessFormDepartment.get('checked');
        if (validateAccessFormDepartment) {
          this.validateAccessFormDepartment.set('checked', false);
        } else {
          this._onValidateAccessFormDepartmentChange(false);
        }
      }
    },
    _showRestrictRecordsField: function () {
      const restrictRecordsByDepartment = this.restrictRecordsByDepartment.get('checked');
      if (!restrictRecordsByDepartment) {
        return false;
      }
      const validateAccessFormDepartment = this.validateAccessFormDepartment.get('checked');
      return !validateAccessFormDepartment;
    },
    _onValidateAccessFormDepartmentChange: function (checked) {
      const shown = this._showRestrictRecordsField();
      if (shown) {
        domClass.remove(this.restrictRecordsFieldContainer, 'displayNone');
        this.restrictRecordsField.set('required', true);
        const store = this.restrictRecordsField.get('store');
        if (store.data.length === 1) {
          this.restrictRecordsField.set('value', store.data[0].value);
          this.restrictRecordsField.set('disabled', true);
        } else {
          this.restrictRecordsField.set('disabled', false);
        }
      } else {
        domClass.add(this.restrictRecordsFieldContainer, 'displayNone');
        this.restrictRecordsField.set('value', null);
        this.restrictRecordsField.set('required', false);
        this.restrictRecordsField.set('disabled', true);
      }
    },
    getDialogCache: function () {
      return this.dialogCache;
    },
    _getRequestDialogAttr: function () {
      return this.requestDialog;
    },
    _onDownloadFileAction: function () {
      if (this.onDownloadFileAction) {
        this.onDownloadFileAction();
      }
    },
    _onSourceFileAction: function () {
      if (this.onSourceFileAction) {
        this.onSourceFileAction();
      }
    },
    _onOriginalFileAction: function () {
      if (this.onOriginalFileAction) {
        this.onOriginalFileAction();
      }
    }
  };
  DocumentRequest = declare([_WB, _TM, _WT], DocumentRequest);

  //Solicitudes
  DocumentRequest.create = RequestType.NEW;
  DocumentRequest.update = RequestType.MODIFY;
  DocumentRequest.editDetails = RequestType.EDIT_DETAILS;
  DocumentRequest.aprove = RequestType.APROVE;
  DocumentRequest.cancel = RequestType.CANCEL;
  DocumentRequest.FILL = RequestType.FILL;
  DocumentRequest.TIME = RequestType.TIME;

  //Documento Nuevo
  DocumentRequest.new = 8;

  return DocumentRequest;
});
