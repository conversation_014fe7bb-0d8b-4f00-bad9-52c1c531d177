.SignForm .gridExpandable.grid-component-container{
    width: 100%!important;
    padding: 0;
}

.SignForm ul.content_area > li:not(.actionButtons) {
    width: 100%;
    margin-bottom: 0.938rem;
}

.SignForm ul.content_area {
    margin: 0%;
}

.dijitDialog .dijitDialogPaneContent .SignForm .content_area {
    padding: 0 1.25rem 1.25rem 1.25rem;
}

.SignForm .css_code span div {
    text-align: center;
    border: solid 1px black;
    max-width: 1.25rem;
    border-radius: 0.313rem;
    margin: auto;
}

.SignForm .content_area .gridExpandable tr td {
    padding: .5rem;
    border: none !important;
}

.SignForm .content_area .grid-component-container thead th:first-child {
    min-width: 1.875rem;
    text-align: center;
}
.bnext .dijitDialog .SignForm  input[type=button] {
    float: right;
}
.sign-form-wrapper.dijitDialog .actionButtons {
    margin-bottom: 0.938rem;
}
@media screen and (max-width: 63.9375em) {
    .bnext .dijitDialogTitle {
        font-size: 1.5rem!important;
        line-height: 2.5rem;
    }
}

@media screen and (max-width: 26.5625em) {
    .SignForm .gridExpandable tr td, .SignForm .gridExpandable tr th {
        text-align: left;
    }
}