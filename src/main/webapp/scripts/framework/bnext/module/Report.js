define([
  'core',
  'dijit/focus',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dijit/_WidgetsInTemplateMixin',
  'dijit/Tooltip',
  'dojo/text!./templates/Report.html',
  'bnext/i18n!./nls/Report',
  'bnext/callMethod',
  'bnext/gridComponentUtil',
  'bnext/LinkedGridFactory',
  'bnext/module/ReportColumn',
  'bnext/module/report-column-sort',
  'bnext/module/report-column-group',
  'dojo/dom-style',
  'dojo/Deferred',
  'dojo/dom-construct',
  'cm/lib/codemirror',
  'dojo/_base/lang',
  'bnext/module/ReportPreview',
  'dojo/_base/array',
  'dojo/dom-class',
  'dojo/query',
  'dojo/on',
  'bnext/grid-modules',
  'bnext/administrator/reports/report-config',
  'dojo/promise/all',
  //a partir de aqui se hacen includes de los widgets para el template
  'dijit/form/ValidationTextBox',
  'dijit/form/Button',
  'dijit/form/Form',
  'dijit/form/SimpleTextarea',
  'cm/mode/javascript/javascript',
  'cm/mode/css/css',
  'xstyle/css!cm/lib/codemirror.css',
  'xstyle/css!cm/theme/neat.css',
  'dijit/Dialog',
  'xstyle/css!./styles/Report.css',
  'bnext/survey/_util/jsFailureExposure!'
], (
  core,
  focus,
  declare,
  _WB,
  _TM,
  _WT,
  Tooltip,
  template,
  i18n,
  callMethod,
  gcUtil,
  LinkedGridFactory,
  ReportColumn,
  ReportColumnSort,
  ReportColumnGroup,
  domStyle,
  Deferred,
  domConstruct,
  CodeMirror,
  lang,
  ReportPreview,
  array,
  domClass,
  query,
  on,
  GridModules,
  ReportConfig,
  all
) => {
  let Report = {
    _requiredMainParams: ['dialog', 'operation', 'module', 'urlAction'],
    _isValidParams: (requiredParams, args) => {
      let valid = true;
      array.some(requiredParams, (param) => {
        if (!args[param] || args[param] === '') {
          console.log(args);
          console.error(`Failure at Report! missing parameter "${param}"`);
          valid = false;
          return false;
        }
      });
      return valid;
    },
    templateString: template,
    i18n: i18n,
    dialog: null,
    module: null,
    urlAction: null,
    _validQueryConfiguration: function () {
      const data = this._queryGrid.getGroundData();
      if (data.length === 0) {
        this._setErrorMessage(this.queryGridContainer, i18n.queryGrid_ground_lang_noRegMessage);
        return false;
      }
      const valid = this._columns.validate(true);
      return valid;
    },
    _valid: function () {
      const valid = this._validQueryConfiguration();
      if (!valid) {
        return false;
      }
      if (this.details.get('value') === '') {
        focus.focus(this.details);
        return false;
      }
      return this.form.validate();
    },
    _enableSaveButton: function () {
      if (this.save) {
        this.save.set('disabled', false);
      }
    },
    _onSaveSuccess: function (result) {
      if (this._validOperationResult(result)) {
        if (typeof this.onOperationSuccess === 'function') {
          this.onOperationSuccess();
        }
        const msg = i18n[`saveSuccess_${this.operation}`];
        this._hideLoader();
        this._close();
        return core.dialog(msg, i18n.accept);
      }
      return this._onSaveFail(result);
    },
    _onSaveFail: function (result) {
      return this._onOperationFail(result, i18n.saveFail);
    },
    _getErrorMesssage: (result, operationMessage) => {
      let message = i18n[result] || i18n[result.error] || result.errorMessage || result.error || result.message;
      if (typeof message === 'undefined' || message === null) {
        message = result.toString();
      }
      return operationMessage + message;
    },
    _onOperationFail: function (result, operationMessage) {
      this._hideLoader();
      const message = this._getErrorMesssage(result, operationMessage);
      return core.error(message, core.i18n.accept);
    },
    _validJS: function () {
      const def = new Deferred();
      try {
        // biome-ignore lint/security/noGlobalEval: TODO: Fix this
        eval(this.jsEditor.getValue());
        def.resolve(true);
      } catch (e) {
        let message = `\n<h2>${i18n.scriptsSynthaxError}</h2> \n\n<div id="Report-Errors" style="font-size:10px;color:#444;">${e.stack}`;
        if (message.length < 80) {
          message += e.message;
        }
        message += '</div>';
        core.dialog(message).then(() => {
          def.resolve(false);
        });
      }
      return def.promise;
    },
    _onSave: function () {
      this.save.set('disabled', true);
      const self = this;
      if (this._valid()) {
        this._validJS().then(JSresults);
        function JSresults(result) {
          if (result) {
            self._prepareEntityValues();
            self._saveOperation().then(
              lang.hitch(self, function () {
                this._enableSaveButton();
              })
            );
          }
          self._enableSaveButton();
        }
      } else {
        this._enableSaveButton();
      }
    },
    _getTitle: function () {
      const moduleName = GridModules.titleCase(this.module);
      return this.i18n[`title_${this.operation}`].replace('{module}', moduleName) || `NOT DEFINED LANGUAGE FOR OPERATION ${this.operation}`;
    },
    _onCancel: function () {
      Tooltip.hide(this.queryGridContainer);
      core.dialog(core.i18n.shouldCancel, core.i18n.Validate.yes, core.i18n.Validate.no).then(() => {
        this._close();
      });
    },
    _loadDataQuery: function () {
      const columns = this._columns.get('value');
      const query = this._getQuery();
      const loadDef = new Deferred();
      callMethod({
        url: this.urlAction,
        params: [columns, query],
        method: 'testQuery'
      }).then(
        lang.hitch(this, (result) => {
          loadDef.resolve(result);
        }),
        lang.hitch(this, (result) => {
          loadDef.reject(result);
        })
      );
      return loadDef;
    },
    _onTest: function () {
      this._executeTest();
    },
    _getQuery: function () {
      const data = this._queryGrid.getGroundData();
      const queryId = data[0].id;
      if (!queryId) {
        return null;
      }
      return {
        id: queryId
      };
    },
    _executeTest: function () {
      const _self = this;
      if (this._valid()) {
        this._validJS().then(JSresults, (e) => {
          console.error('Failed to check valid js', e);
        });
        function JSresults(result) {
          if (result) {
            _self._prepareEntityValues();
            new ReportPreview({
              reportId: _self.data.id || -1,
              databaseQueryId: _self._getQuery().id,
              columns: _self.data.columns,
              urlCatalogAction: ReportConfig.CATALOG_ACTION[_self.module],
              urlModuleAction: ReportConfig.MODULE_ACTION[_self.module],
              scripts: _self.jsEditor.getValue(),
              styles: _self.cssEditor.getValue()
            });
          }
        }
      }
    },
    _validOperationResult: (result) => result.operationEstatus || +result.operationEstatus === 1 || result.operationEstatus === '1',
    _loadValues: function () {
      this.code.innerHTML = this.data.entity.code;
      this.module = this.data.entity.module;
      this.description.set('value', this.data.entity.description);
      this.localTimeForDates.set('value', this.data.entity.localTimeForDates);
      this.details.set('value', this.data.entity.details);
      this._buildColumnsSource();
      this._buildGrid(this.data.entity.query);
      this.jsEditor.setValue(this.data.entity.scripts);
      this.cssEditor.setValue(this.data.entity.styles);
      this._getQueryColumns().then(
        lang.hitch(this, function (allColumns) {
          const allFormColumns = this._columns.transformRawColumns(allColumns);
          this._columns.set('available', allFormColumns);
          callMethod({
            url: this.urlAction,
            method: 'getColumnsOfReport',
            params: [this.data.entity.id]
          }).then(
            lang.hitch(this, function (reportColumns) {
              this._columns.set('value', reportColumns);
              this._updateSortPrioritiesFromColumns(reportColumns.slice());
              this._updateSortGroupingsFromColumns(reportColumns.slice());
              this._hideLoader();
            }),
            lang.hitch(this, function (error) {
              console.error('Failed to get columns of report', error);
              this._hideLoader();
            })
          );
        }),
        lang.hitch(this, function (err) {
          console.error('Failed lo get query columns', err);
          if (this._hideLoader) {
            this._hideLoader();
          }
        })
      );
    },
    _updateSortPrioritiesFromColumns: function (reportColumns) {
      const values = array.filter(reportColumns, (column) => column.sortPriority !== null && typeof column.sortPriority !== 'undefined');
      this._sortPriorities.set('value', values);
      this._sortPriorities.set('available', reportColumns);
    },
    _updateSortGroupingsFromColumns: function (reportColumns) {
      const values = array.filter(reportColumns, (column) => column.groupingPriority !== null && typeof column.groupingPriority !== 'undefined');
      this._sortGroupings.set('value', values);
      this._sortGroupings.set('available', reportColumns);
    },
    _setErrorMessage: (domElement, message) => {
      if (!domClass.contains(domElement, 'error')) {
        domClass.add(domElement, 'error');
      }
      Tooltip.show(`<div class="Report-Tooltip">${message}</div>`, domElement);
    },
    _prepareEntityValues: function () {
      this.data.entity.description = this.description.get('value');
      this.data.entity.localTimeForDates = this.localTimeForDates.checked;
      this.data.entity.details = this.details.get('value');
      this.data.entity.scripts = this.jsEditor.getValue();
      this.data.entity.styles = this.cssEditor.getValue();
      this.data.entity.query = this._getQuery();
      this.data.entity.module = this.module;
      const columns = this._columns.get('value');
      this._sortPriorities.copyDataTo(columns);
      this._sortGroupings.copyDataTo(columns);
      this.data.columns = columns;
    },
    _saveOperation: function () {
      this._showLoader();
      return callMethod({
        url: this.urlAction,
        params: [this.data.entity, this.data.columns],
        method: this.operation
      }).then(lang.hitch(this, this._onSaveSuccess), lang.hitch(this, this._onSaveFail));
    },
    _sqlChange: (editor, event) => {
      editor.save();
      const sql = editor.getValue();
      const str = '&fillerUserId';
      if (event.text.length && event.text[0] === '&' && sql.lastIndexOf('&') === sql.length - 1) {
        const cursor = editor.getCursor();
        editor.setValue(sql.replace(/&(\s|$)/gm, `${str} `));
        editor.focus();
        cursor.ch += str.length;
        editor.setCursor(cursor);
      }
      editor.save();
    },
    _disableSelection: function () {
      domClass.remove(this.queryButton, 'Button');
      domClass.remove(this.queryButton, 'addToGrid');
      this._queryGrid.getDialog().hide();
    },
    _enableSelection: function () {
      domClass.add(this.queryButton, 'Button');
      domClass.add(this.queryButton, 'addToGrid');
    },
    _validateSelection: function () {
      const def = new Deferred();
      if (this._queryGrid.getGroundData().length > 0) {
        this._disableSelection();
        def.reject();
      } else {
        this._enableSelection();
        def.resolve();
      }
      return def;
    },
    _onLoadQueryColumns: function (columns) {
      const formColumns = this._columns.transformRawColumns(columns);
      this._columns.set('value', formColumns);
      this._updateSortPrioritiesFromColumns(formColumns.slice());
      this._updateSortGroupingsFromColumns(formColumns.slice());
    },
    _getQueryColumns: function () {
      const data = this._queryGrid.getGroundData();
      if (!data || !data.length) {
        return core.resolvedPromise();
      }
      const queryId = data[0].id;
      return callMethod({
        url: this.urlAction,
        method: 'getColumnsOfQuery',
        params: [queryId]
      });
    },
    _loadQueryColumns: function () {
      const def = new Deferred();
      this._getQueryColumns().then(
        lang.hitch(this, function (columns) {
          this._onLoadQueryColumns(columns);
          def.resolve();
        }),
        lang.hitch(this, function (error) {
          console.error('Failed to load query columns', error);
          this._hideLoader();
        })
      );
      return def.promise;
    },
    _onQuerySelected: function () {
      if (!this._queryGrid) {
        return;
      }
      return this._loadQueryColumns().then(
        lang.hitch(this, this._validateSelection),
        lang.hitch(this, function (error) {
          console.error('Failed load columns', error);
          this._hideLoader();
        })
      );
    },
    _onQueryCleared: function () {
      this._columns.set('value', []);
      this._sortPriorities.set('value', []);
      this._sortGroupings.set('value', []);
      this._validateSelection();
    },
    _buildGrid: function (query) {
      const columns = [];
      gcUtil
        .column(columns)
        .push('code', core.i18n.colNameCode, gcUtil.cType().Text())
        .push('description', core.i18n.colNamesDescription, gcUtil.cType().Text())
        .push(
          'source',
          i18n.colNameFieldDatabaseSource,
          gcUtil.cType().TextMap([
            { name: i18n.colNameBnextQMS, value: 'bnext-qms' },
            { name: i18n.colNameConfigurable, value: 'configurable' }
          ])
        );

      const id = +this.data.entity.id;
      this._queryGrid = LinkedGridFactory.create(
        {
          id: this.queryGridContainer.id,
          freezeHeader: false,
          loadGround: false,
          onBeforeAddActivate: lang.hitch(this, this._validateSelection),
          onGroundLoaded: lang.hitch(this, this._onQuerySelected),
          onAfterAdd: lang.hitch(this, this._onQuerySelected),
          onAfterRemove: lang.hitch(this, this._onQueryCleared),
          addButton: this.queryButton
        },
        id,
        this.urlAction,
        i18n,
        columns
      );
      if (query) {
        this._queryGrid.getGroundGrid().pushRow(query);
      }
      this._queryGrid.getGroundGrid().updateDataInfo();
      this._validateSelection();
    },
    _buildColumnsSource: function () {
      this._columns = new ReportColumn({ id: `columns_${this.id}`, reportDialog: this.dialog, required: true }, this.columnsContainer);
      this._columns.on('columnAdded', lang.hitch(this, this._onColumnRowsChanged));
      this._columns.on('columnRemoved', lang.hitch(this, this._onColumnRowsChanged));
      this._sortPriorities = new ReportColumnSort({ id: `priorities_${this.id}`, reportDialog: this.dialog }, this.sortPrioritiesContainer);
      this._sortGroupings = new ReportColumnGroup({ id: `groupings_${this.id}`, reportDialog: this.dialog }, this.sortGroupingsContainer);
    },
    _onColumnRowsChanged: function () {
      const reportColumns = this._columns.get('value');
      this._updateSortPrioritiesFromColumns(reportColumns);
      this._updateSortGroupingsFromColumns(reportColumns);
    },
    _loadComponents: function () {
      this.columns = [];
      this.jsEditor = CodeMirror.fromTextArea(this.scripts, {
        mode: 'text/typescript',
        indentWithTabs: true,
        smartIndent: true,
        lineNumbers: true,
        matchBrackets: true,
        extraKeys: {
          'Ctrl-Space': 'autocomplete'
        },
        theme: 'neat'
      });
      this.cssEditor = CodeMirror.fromTextArea(this.styles, {
        mode: 'text/css',
        indentWithTabs: true,
        smartIndent: true,
        lineNumbers: true,
        matchBrackets: true,
        extraKeys: {
          'Ctrl-Space': 'autocomplete'
        },
        theme: 'neat'
      });
      if (this.operation === 'newReport') {
        this.data = {
          entity: {},
          columns: []
        };
        this.data.entity.id = -1;
        this.data.entity.module = this.module;
        this._buildColumnsSource();
        this._buildGrid();
        this._hideLoader();
      } else if (this.operation === 'editReport') {
        this.data.columns = [];
        this._loadValues();
      }
    },
    _resize: function () {
      this.dialog.resize();
      this.dialog._position();
    },
    _showLoader: function (msg) {
      this.loadMessage.innerHTML = msg || i18n.saving;
      this.loader.style.display = '';
    },
    _hideLoader: function () {
      if (this.loader) {
        this.loader.style.display = 'none';
      }
      return core.hideLoader();
    },
    _postCreate: function () {
      on(window, 'resize', () => {
        this._resize.call(this);
      });
      for (const body of query('body.bnext')) {
        domStyle.set(body, 'overflow', 'hidden');
      }
      this.dialog.set('title', `<b>${this.title || this._getTitle()}</b>`);
      domClass.add(this.dialog.get('containerNode').parentNode, 'fixedTop');
      this.dialog.show();
      this._resize();
    },
    _destroy: function () {
      try {
        this._queryGrid.destroy();
        this._columns._destroy();
        this._sortPriorities._destroy();
        this._sortGroupings._destroy();
        const dijitDom = this.domNode;
        this.destroyRecursive();
        domConstruct.destroy(dijitDom);
      } catch (e) {
        console.error('Can not destroy Report component');
        console.error(e);
      }
    },
    _close: function () {
      for (const body of query('body.bnext')) {
        domStyle.set(body, 'overflow', '');
      }
      this._destroy();
      this.dialog.hide();
    },
    constructor: function (args) {
      this.isValid = this._isValidParams(this._requiredMainParams, args);
      if (!this.isValid) {
        console.error('Invalid Report declaration!');
      }
    },
    postCreate: function () {
      this.placeAt(this.dialog);
      this._postCreate();
      this.dialog.onCancel = lang.hitch(this, this._onCancel);
      this._loadComponents();
    }
  };
  Report = declare([_WB, _TM, _WT], Report);
  return Report;
});
