define({
  lblTo: 'To:',
  lblCC: 'CC:',
  lblSubject: 'Subject:',
  lblAttachmentType: 'Link type:',
  lblAttachTypeViewerLink: 'Viewer',
  lblAttachTypeDownloadLink: 'Download',
  lblLink: 'Link:',
  lblShareMaxDownloads: 'Maximum downloads:',
  lblShareMaxViews: 'Maximum views:',
  lblShareLife: 'Life of link(days):',
  sendEmailBtn: 'Send',
  cancelEmailBtn: 'Cancel',
  copyToClipBoardBtn: 'Copy to clipboard',
  msgEmailIsRequired: 'Capture an email',
  msgEmailIsInvalid: 'Invalid email',
  msgSubjectInvalid: 'Capture a subject',
  msgInvalid: 'Invalid',
  msgSendError: 'Error occurred while sending to document.',
  msgSended: 'Notification was send.',
  msgCopyToClipBoard: 'Link was copied',
  msgUseCtrlCopy: 'Please press Ctrl/Cmd+C to copy',
  msgUseCtrlPaste: 'For browser security reasons, pasting via the button is not allowed. Please use Ctrl+V or right-click > Paste.',
  shareDenied: 'The selected document cannot be shared.',
  msgEmail: 'Message:'
});
