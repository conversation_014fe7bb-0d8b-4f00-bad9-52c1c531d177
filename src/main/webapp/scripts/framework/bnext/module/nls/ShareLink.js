define({
  root: {
    lblTo: 'Para:',
    lblCC: 'CC:',
    lblSubject: 'Asunto:',
    lblAttachmentType: 'Tipo liga:',
    lblAttachTypeViewerLink: 'Visor',
    lblAttachTypeDownloadLink: '<PERSON>car<PERSON>',
    lblLink: 'Liga:',
    lblShareMaxDownloads: 'Número máximo de descargas:',
    lblShareMaxViews: 'Número máximo de vistas:',
    lblShareLife: 'Vida de enlace(días):',
    sendEmailBtn: 'Enviar',
    cancelEmailBtn: 'Cancelar',
    copyToClipBoardBtn: 'Copiar a portapapeles',
    msgEmailIsRequired: 'Capture un correo',
    msgEmailIsInvalid: 'Correo inválido',
    msgSubjectInvalid: 'Capture un asunto',
    msgInvalid: 'Valor invalido',
    msgSendError: 'Ocurrio un error inesperado al enviar el documento.',
    msgSended: 'Se envió la notificación por correo.',
    msgCopyToClipBoard: 'Se copió la liga al portapapeles',
    msgUseCtrlCopy: 'Por favor presione Control+C para copiar',
    msgUseCtrlPaste: 'Por seguridad del navegador, no se puede pegar desde el botón. Usa Ctrl+V o clic derecho > Pegar.',
    shareDenied: 'El documento no está habilitado para compartirse.',
    msgEmail: 'Mensaje:'
  },
  en: true
});
