define([
  'core',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dijit/_WidgetsInTemplateMixin',
  'dijit/Tooltip',
  'dojo/text!./templates/ShareLink.html',
  'bnext/i18n!./nls/ShareLink',
  'dojox/validate/web',
  'dojo/on',
  'dojo/query',
  'dojo/dom-style',
  'dojo/_base/lang',
  'bnext/callMethod',
  'dojo/dom-attr',
  'dojo/store/Memory',
  'dojo/_base/lang',
  'dojo/dom-class',
  'xstyle/css!./styles/ShareLink.css',
  'bnext/survey/_util/jsFailureExposure!',
  'dojo/domReady!',
  'bnext/module/UltraFilteringSelect',
  'dijit/Editor'
], (core, declare, _WB, _TM, _WT, Tooltip, template, i18n, validate, on, query, domStyle, lang, callMethod, domAttr, Memory, base, domClass) => {
  let ShareLink = {
    templateString: template,
    i18n: i18n,
    emailMessage: null,
    afterOpen: null,
    afterClose: null,
    dialog: null,
    validator: null,
    shareDocument: null,
    rulesToValidate: function () {
      $.validator.addMethod('validateEmailTo', (value, element) => {
        const emailsArr = value.split(';');
        for (var i = 0; i < emailsArr.length; i++) {
          const mail = emailsArr[i].replace(/ /g, '');
          if (!validate.isEmailAddress(mail)) {
            return false;
          }
        }
        return true;
      });
      $.validator.addMethod('validateEmailCC', (value, element) => {
        if (value.replace(/ /g, '').length === 0) {
          return true;
        }
        const emailsArr = value.split(';');
        for (var i = 0; i < emailsArr.length; i++) {
          const mail = emailsArr[i].replace(/ /g, '');
          if (!validate.isEmailAddress(mail)) {
            return false;
          }
        }
        return true;
      });
      this.validator = $(this.frmShareDocument).validate({
        rules: {
          emailToText: {
            required: true,
            validateEmailTo: true
          },
          emailCCText: {
            validateEmailCC: true
          },
          emailSubjectText: {
            required: true
          },
          shareMaxDownloads: {
            required: true
          },
          shareMaxViews: {
            required: true
          },
          shareLife: {
            required: true
          }
        },
        messages: {
          emailToText: {
            required: i18n.msgEmailIsRequired,
            validateEmailTo: i18n.msgEmailIsInvalid
          },
          emailCCText: {
            validateEmailCC: i18n.msgEmailIsInvalid
          },
          emailSubjectText: {
            required: i18n.msgInvalid
          },
          shareMaxDownloads: {
            required: i18n.msgInvalid
          },
          shareMaxViews: {
            required: i18n.msgInvalid
          },
          shareLife: {
            required: i18n.msgInvalid
          }
        },
        errorPlacement: (error, element) => {
          error.insertBefore(element);
        }
      });
    },
    send: function () {
      if (!this.shareDocument.valid) {
        core.dialog(i18n.shareDenied);
        return;
      }
      if ($(this.frmShareDocument).valid()) {
        core.showLoader();
        this.shareDocument.emailToText = this.emailToText.value;
        this.shareDocument.emailCCText = this.emailCCText.value;
        this.shareDocument.emailSubjectText = this.emailSubjectText.value;
        this.shareDocument.emailMessage = this.emailMessage.get('value');
        this.shareDocument.emailAttachType = this.emailAttachType.get('value') === '1' ? 'SHARE_DOWNLOAD_LINK' : 'SHARE_VIEWER_LINK';
        this.shareDocument.shareMaxDownloads = this.shareMaxDownloads.value;
        this.shareDocument.shareMaxViews = this.shareMaxViews.value;
        this.shareDocument.shareLife = this.shareLife.value;
        callMethod({
          url: 'SendShareDocument.action',
          method: 'shareDocumentoToEmail',
          params: [this.shareDocument]
        }).then(
          (gsh) => {
            core.hideLoader();
            if (!gsh.validCallback || gsh.jsonEntityData.error) {
              return core.error(gsh);
            }
            if (!gsh.operationEstatus) {
              return core.error(i18n[gsh.msgSendError]);
            }
            core.dialog(i18n.msgSended);
            this.onClose();
          },
          (e) => {
            core.hideLoader();
            core.error(e);
          }
        );
      }
    },
    handleErrorPasteEditor: function () {
      if (this.emailMessage) {
        this.emailMessage.onLoadDeferred.then(() => {
          // Almacenar la referencia original de _clipboardCommand
          const originalClipboardCommand = this.emailMessage._clipboardCommand;

          // Sobrescribir _clipboardCommand
          this.emailMessage._clipboardCommand = function (cmd) {
            if (cmd === 'paste') {
              core.dialog(i18n.msgUseCtrlPaste);
              return;
            }
            // Delegar a la implementación original para otros comandos
            return originalClipboardCommand.call(this, cmd);
          };
        });
      }
    },
    copyLinkToClipboard: function () {
      domAttr.remove(this.inputLink, 'disabled');
      try {
        this.inputLink.value = this.emailAttachType.get('value') === '1' ? this.shareDocument.shareLink : this.shareDocument.viewerLink;
        this.inputLink.select();
        document.execCommand('copy');
        this.inputLink.blur();
        Tooltip.show(i18n.msgCopyToClipBoard, this.copyToClipBoardBtn);
        setTimeout(function () {
          Tooltip.hide(this.copyToClipBoardBtn);
        }, 1100);
      } catch (err) {
        core.dialog(i18n.msgUseCtrlCopy);
      }
      domAttr.set(this.inputLink, 'disabled', '');
    },
    onChangeLinkType: function () {
      this.inputLink.value = this.emailAttachType.get('value') === '1' ? this.shareDocument.shareLink : this.shareDocument.viewerLink;
    },
    onClose: function () {
      Tooltip.hide(this.copyToClipBoardBtn);
      this.hide();
    },
    events: function () {
      on(this.sendEmailBtn, 'click', () => {
        this.send();
      });
      on(this.copyToClipBoardBtn, 'click', () => {
        this.copyLinkToClipboard();
      });
      on(this.emailAttachType, 'change', () => {
        this.onChangeLinkType();
      });
      on(this.cancelEmailBtn, 'click', () => {
        this.onClose();
      });
    },
    setControllers: function () {
      this.emailToText.value = '';
      this.emailCCText.value = '';
      this.emailSubjectText.value = '';
      this.shareMaxDownloads.value = this.shareDocument.shareMaxDownloads;
      this.shareMaxViews.value = this.shareDocument.shareMaxViews;
      this.shareLife.value = this.shareDocument.shareLife;
      this.emailMessage.set('value', '');
      this.validator.resetForm();
      domClass.remove(this.emailToText, 'error');
      domClass.remove(this.emailCCText, 'error');
      domClass.remove(this.emailSubjectText, 'error');
      domClass.remove(this.shareMaxDownloads, 'error');
      domClass.remove(this.shareMaxViews, 'error');
      domClass.remove(this.shareLife, 'error');
      if (this.enablePdfViewer !== 1) {
        this._loadDownloadAttachTypes();
      } else if (!this.hasFile) {
        this.emailAttachType.set('disabled', true);
        this._loadFormAttachTypes();
      } else {
        this._loadDefaultAttachTypes();
      }
      this.inputLink.value = this.emailAttachType.get('value') === '1' ? this.shareDocument.shareLink : this.shareDocument.viewerLink;
    },
    show: function () {
      on(
        window,
        'resize',
        lang.hitch(this, function () {
          this._resize();
        })
      );
      this.dialog.set('draggable', false);
      domClass.add(this.dialog.get('containerNode').parentNode, 'fixedTop fancy-scroll');
      domClass.add(this.dialog.domNode, 'fullWidthScreen shareLinkDialog', true);
      domClass.add(this.dialog.domNode, 'grid-floating-active grid-container');
      query('body.bnext').forEach((body) => {
        domStyle.set(body, 'overflow', 'hidden');
      });
      this.dialog.show();
      this._resize();
      core.setGlobalSessionExpiredFunction(this, 'hide');
      if (typeof this.afterOpen === 'function') {
        this.afterOpen();
      }
    },
    hide: function () {
      query('body.bnext').forEach((body) => {
        domStyle.set(body, 'overflow', '');
      });
      this.dialog?.hide();
      this.dialog?.destroyRecursive();
      this.destroyRecursive();
      if (typeof this.afterClose === 'function') {
        this.afterClose();
      }
    },
    _loadDefaultAttachTypes: function () {
      const attachTypes = new Memory({
        data: [
          {
            id: 2,
            name: i18n.lblAttachTypeViewerLink
          },
          {
            id: 1,
            name: i18n.lblAttachTypeDownloadLink
          }
        ]
      });
      this.emailAttachType.set('searchAttr', 'name');
      this.emailAttachType.set('store', attachTypes);
      this.emailAttachType.set('value', 1);
      this.mainTitle.innerHTML = this.title;
    },
    destroyRecursive: function () {
      this.dialog?.destroyRecursive();
      this.inherited(arguments);
    },
    _resize: function () {
      if (this.dialog.domNode) {
        this.dialog.resize();
        this.dialog._position();
      }
    },
    _loadDownloadAttachTypes: function () {
      const attachTypes = new Memory({
        data: [
          {
            id: 1,
            name: i18n.lblAttachTypeDownloadLink
          }
        ]
      });
      this.emailAttachType.set('searchAttr', 'name');
      this.emailAttachType.set('store', attachTypes);
      this.emailAttachType.set('value', 1);
    },
    _loadFormAttachTypes: function () {
      const attachTypes = new Memory({
        data: [
          {
            id: 2,
            name: i18n.lblAttachTypeViewerLink
          }
        ]
      });
      this.emailAttachType.set('searchAttr', 'name');
      this.emailAttachType.set('store', attachTypes);
      this.emailAttachType.set('value', 2);
    },
    postCreate: function () {
      this._loadDefaultAttachTypes();
      this.rulesToValidate();
      this.events();
      this.setControllers();
      this.placeAt(this.dialog);
      this.show();
      this.handleErrorPasteEditor();
    }
  };
  ShareLink = declare([_WB, _TM, _WT], ShareLink);
  return ShareLink;
});
