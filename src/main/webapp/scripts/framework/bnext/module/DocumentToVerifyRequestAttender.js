define([
  'core',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'bnext/module/DocumentRequestVerify',
  'bnext/callMethod',
  'bnext/FieldListUtil',
  'bnext/i18n!./nls/DocumentToVerifyRequestAttender',
  'dijit/Dialog',
  'bnext/module/data/RequestType',
  'dojo/json',
  'dojo/dom',
  'bnext/DynamicFieldUtil',
  'dojo/aspect',
  'dojo/Deferred',
  'dojo/_base/lang',
  'dojo/dom-class',
  'bnext/angularNavigator',
  'dojo/dom-construct',
  'dijit/form/Button',
  'bnext/misc',
  'bnext/saveHandle',
  'dojo/_base/unload',
  'dojo/domReady!'
], (
  core,
  declare,
  _WB,
  DocumentRequestVerify,
  callMethod,
  FieldListUtil,
  i18n,
  Dialog,
  requestType,
  dojoJson,
  dom,
  DynamicFieldUtil,
  aspect,
  Deferred,
  lang,
  domClass,
  angularNavigator,
  domConstruct,
  Button,
  BnextMisc,
  SaveHandle
) => {
  const misc = new BnextMisc();
  const DocumentToVerifyRequestAttenderBase = {
    attend: function (pendingRecordId, requestId) {
      core.showLoader(i18n.loading);
      //ToDo: Implementar escalación aquí utilizando "pendingRecordId"
      callMethod({
        url: 'RequestService.action',
        method: 'load',
        params: [requestId]
      }).then(
        (request) => {
          this.run(request);
        },
        (e) => {
          core.error(e);
          core.hideLoader();
        }
      );
    },
    run: function (request) {
      const dialg = this.dialg;
      if (this.documentRequestVerify) {
        this.documentRequestVerify.destroyRecursive?.();
        this.documentRequestVerify = null;
      }
      try {
        this.documentRequestVerify = this.getDocumentRequestVerifyInstance(request);
        this.bindDocumentRequestVerifyAspect(this.documentRequestVerify);
      } catch (e) {
        console.log(e, e.stack);
      }
      dialg.show();
      this.documentRequestVerify.mainTitle.innerHTML = i18n.messages_documentVerification;
    },
    dialg: null,
    saveHandler: null,
    updateHandle: null,
    documentRequestVerify: null,
    fieldListUtil: null,
    onSuccess: null,
    grid: {
      reload: () => {
        angularNavigator.navigate('pendings');
      },
      refreshData: () => {
        angularNavigator.navigate('pendings');
        return {
          // biome-ignore lint/suspicious/noThenProperty: Fake deferred
          then: (fs) => {
            fs();
          }
        };
      }
    },
    constructor: function (args) {
      if (args.grid) {
        this.grid = args.grid;
      }
      this.onSuccess = args.onSuccess || core.$noop;
      this.fieldListUtil = new FieldListUtil();
      this.dialg = new Dialog({ autofocus: false });
      this.saveHandler = new SaveHandle({
        savedObjName: 'misDatos',
        serviceStore: 'Request.action',
        hookUpdateSuccess: core.hideLoader,
        hookUpdateFailure: 'void(0)',
        saveCallback: lang.hitch(this, (gsh) => {
          if (this.checkForVerificationTimeout) {
            clearTimeout(this.checkForVerificationTimeout);
          }
          core.hideLoader();
          if (!gsh.validCallback || !gsh.operationEstatus) {
            const errorKey = gsh.successMessage || gsh.errorMessage;
            if (errorKey === 'autorization_in_process') {
              this.updateAcceptBusy(true);
              this.checkForVerification.call(this, this.documentRequestVerify.sol);
            } else {
              this.updateAcceptBusy(false);
            }
            let dialogMsg = i18n[gsh.errorMessage] || i18n[gsh.successMessage];
            dialogMsg = dialogMsg ? dialogMsg : gsh.errorMessage;
            this.documentRequestVerify.completed = false;
            core.dialog(dialogMsg).then(() => {
              this.checkForVerification.call(this, this.documentRequestVerify.sol);
            });
            return;
          }
          this.updateVerificationStartedOnDate(gsh);
          this.documentRequestVerify.completed = false;
          core.dialog(this.getVerificationStartedOnDate(), core.i18n.wait_label, core.i18n.exit_label).then(
            () => {
              if (this.isAcceptBusy()) {
                core.showLoader().then(() => {
                  this.checkForVerification.call(this, this.documentRequestVerify.sol);
                });
              } else {
                core.dialog(i18n.Validate_add_success).then(() => {
                  this.checkForVerification.call(this, this.documentRequestVerify.sol);
                  this.grid.reload();
                  this.onSuccess();
                });
              }
            },
            () => {
              angularNavigator.navigate('pendings');
            }
          );
        }),
        methodName: 'verifyRequestSMD',
        justAccept: true
      });
      this.updateHandle = new SaveHandle({
        savedObjName: 'misDatos',
        serviceStore: 'Request.action',
        hookUpdateSuccess: core.hideLoader,
        hookUpdateFailure: 'void(0)',
        methodName: 'changeDepartmentRequestSMD',
        justAccept: true
      });
    },
    getVerificationStartedOnDate: function () {
      return i18n.verificationInProcess.replace(':verificationStartedOn', this.documentRequestVerify.verificationStartedOn);
    },
    updateVerificationStartedOnDate: function (gsh) {
      if (gsh.jsonEntityData?.autorization_started_on) {
        this.documentRequestVerify.verificationStartedOn = gsh.jsonEntityData.autorization_started_on;
      } else {
        this.documentRequestVerify.verificationStartedOn = misc.formatDateTime(new Date(), 'yyyy-MM-dd HH:mm:ss');
      }
    },
    isAcceptBusy: function () {
      if (this.documentRequestVerify?.sol) {
        return this.documentRequestVerify.sol.busyAccept;
      }
      if (this.sol) {
        return this.sol.busyAccept;
      }
      return false;
    },
    updateAcceptBusy: function (busy) {
      if (this.documentRequestVerify?.sol) {
        this.documentRequestVerify.sol.busyAccept = busy;
      } else if (this.sol) {
        this.sol.busyAccept = busy;
      } else {
        console.error('No request to update busy status');
      }
    },
    checkForVerification: function (request) {
      if (this.checkForVerificationTimeout) {
        clearTimeout(this.checkForVerificationTimeout);
      }
      this.checkForVerificationTimeout = setTimeout(() => {
        this.executeCheckForVerification.call(this, request);
      }, 1000);
    },
    executeCheckForVerification: function (request) {
      if (!request) {
        console.error('No request to check for verification');
        return;
      }
      const token = request.documentCode + request.version;
      callMethod({
        url: 'Request.action',
        method: 'checkVerificationStatus',
        params: [request.id, token]
      }).then(
        (gsh) => {
          if (!gsh.validCallback || !gsh.operationEstatus) {
            this.checkForVerification.call(this, request);
            return;
          }
          this.updateAcceptBusy(false);
          this.updateVerificationStartedOnDate(gsh);
          core.hideLoader();
          if (gsh.successMessage === 'autorization_failed') {
            core.error(i18n.verificationFailed).then(() => {
              this.documentRequestVerify.hide();
              this.grid.reload();
              this.onSuccess();
            });
            return;
          }
          core.dialog(i18n.Validate_add_success).then(() => {
            this.documentRequestVerify.hide();
            this.grid.reload();
            this.onSuccess();
          });
        },
        (e) => {
          core.hideLoader();
          console.error('Failed to check for authorization status of request', e, request);
        }
      );
    },
    documentRequestVerifyAction: function (request, file) {
      this.saveHandler.setData([
        +request.id || null, //requestId
        +request.fileId || null,
        +request.flujoId || null, //flujoId
        request.description || null,
        request.documentCode || null,
        request.version || null,
        +request.department?.id || null,
        +request.documentType?.id || null,
        +request.nodo?.id || null,
        request.restrictRecordsByDepartment || false,
        request.validateAccessFormDepartment || false,
        request.restrictRecordsField || null,
        +request.retentionTime || null,
        +request.retentionText || null,
        +request.storagePlaceId || null,
        request.dynamicFieldInsertDTO || null,
        request.dynamicTableName || null,
        request.slimReportName || null
      ]);
      core.showLoader();
      this.saveHandler.saveData();
    },
    getDocumentRequestVerifyInstance: function (request) {
      const self = this;
      const dialg = this.dialg;
      const documentRequestVerify = new DocumentRequestVerify({
        isAFormRequest: !!request.surveyId,
        surveyId: request.surveyId,
        sol: request,
        onBeforeSubmit: (docComponent, request) => {
          const def = new Deferred();
          if (self.isAcceptBusy()) {
            console.log('editActionAcceptBusy = true!');
            def.reject(self.getVerificationStartedOnDate());
            return def.promise;
          }
          self.updateAcceptBusy(true);
          def.resolve();
          return def.promise;
        },
        onSubmit: function (request, file) {
          this.showLoader(core.i18n.Validate.saving);
          return [
            request.then(
              (sobj) => {
                self.documentRequestVerifyAction(sobj.sol, sobj.file);
              },
              (e) => {
                core.error('No se pudo verificar la solicitud.');
                dialg.hide();
                self.grid.refreshData();
                console.error('Falla al guardar valores dinamicos');
                console.error(e);
                self.hideLoader();
              }
            )
          ];
        },
        onCancel: () => {
          dialg.hide();
          return self.grid.refreshData();
        },
        onReturn: () => {
          dialg.hide();
          self.onSuccess();
          self.grid.refreshData();
        },
        onReject: function (requestId) {
          if (this.isAcceptBusy()) {
            console.log('editActionAcceptBusy = true!');
            core.dialog(this.getVerificationStartedOnDate());
            return;
          }
          this.showLoader(core.i18n.Validate.saving);
          this.set('style', { display: 'none', 'text-align': 'center' });
          self.rejectHandle(requestId, self);
        },
        onDepartmentChange: function () {
          if (self.isAcceptBusy()) {
            console.log('editActionAcceptBusy = true!');
            core.dialog(this.getVerificationStartedOnDate());
            return;
          }
          if (
            (this.originalDepartment && this.originalDepartment.id.toString() !== this.department.get('value')) ||
            (!this.originalDepartment && this.department.get('value'))
          ) {
            if (this.department.get('displayedValue') === '') {
              return;
            }
            core.dialog(this.lan.onChangeDepartment.replace(':department', this.department.get('displayedValue')), core.i18n.Validate.yes, core.i18n.Validate.no).then(
              () => {
                this.showLoader();
                dialg.hide();
                self.updateHandle.setData([+request.id, this.department.get('value')]);
                self.updateHandle.saveData().then(() => {
                  self.grid.reload();
                  self.onSuccess();
                });
              },
              () => {
                this.department.set('value', this.originalDepartment.id);
                this.department.set('displayedValue', this.originalDepartment.description);
              }
            );
          }
        },
        requestDialog: dialg
      });
      return documentRequestVerify;
    },
    bindDocumentRequestVerifyAspect: function () {
      const self = this;
      //se enlaza al cambio de valor del combo de tipo de documento
      aspect.after(
        self.documentRequestVerify,
        '_onDocTypeChange',
        lang.hitch(self.documentRequestVerify, function () {
          const sol = this.sol;
          DynamicFieldUtil.load(
            this.sol.id,
            'DocumentType.Request.Custom.action',
            this.dynamicFields,
            this.docType.get('value'),
            {
              cacheOn: false,
              dijits: true,
              legacy: false
            },
            this.sol.dynamicTableName ? this.sol.dynamicTableName : 'recalculate'
          ).then((r) => {
            if (+sol.type === requestType.APROVE || +sol.type === requestType.CANCEL) {
              const dMap = r?.domMap ? r.domMap : {};
              self.fieldListUtil.handleAsDisabled(dMap);
            }
          });
        })
      );
      //se enlaza guardado de valores de campos dinamicos al guardado normal
      aspect.before(
        this.documentRequestVerify,
        'onSubmit',
        lang.hitch(this.documentRequestVerify, function (sol, file) {
          const def = new Deferred();
          DynamicFieldUtil.save('DocumentType.Request.Custom.action', this.dynamicFields, this.docType.get('value')).then(
            (dynamicFieldInsertDTO) => {
              if (dynamicFieldInsertDTO.tableName) {
                sol.dynamicFieldInsertDTO = dynamicFieldInsertDTO;
                sol.dynamicTableName = dynamicFieldInsertDTO.tableName;
              }
              def.resolve({ sol: sol, file: file });
            },
            () => {
              def.reject();
            }
          );
          return [def.promise];
        })
      );
    },
    rejectClear: function (domHandler) {
      const _self = this.documentRequestVerify;
      const dialg = this.dialg;
      dialg.hide();
      _self.set('style', { display: '', 'text-align': '' });
      for (const doom of domHandler.destroyables) {
        domConstruct.destroy(doom);
      }
      for (const widget of domHandler.destroyableWidgets) {
        widget.destroyRecursive();
      }
      domClass.remove(dialg.domNode, 'reject-request');
      core.dialog(i18n.Validate_decline_success, i18n.messages_accept);
      this.onSuccess();
      this.grid.refreshData().then(() => {
        this.documentRequestVerify.hideLoader();
        _self.hideLoader();
      });
    },
    rejectAcceptAction: function (domHandler, requestId) {
      if (this.isAcceptBusy()) {
        console.log('editActionAcceptBusy = true!');
        core.dialog(this.getVerificationStartedOnDate());
        return;
      }
      if (lang.trim(domHandler.rejectReasonTextArea.value) === '') {
        core.dialog(i18n.messages_specify_reason, i18n.messages_accept);
        return;
      }
      core.dialog(i18n.messages_decline_request, i18n.messages_accept, i18n.messages_cancel).then(() => {
        core.showLoader(i18n.saving).then(() => {
          callMethod({
            url: 'Request.action',
            method: 'rejectVerification',
            params: [requestId, i18n.messages_rejectedFor + domHandler.rejectReasonTextArea.value]
          }).then(
            () => {
              this.rejectClear(domHandler);
            },
            () => {
              this.rejectClear(domHandler);
            }
          );
        });
      });
    },
    rejectCancelAction: function (domHandler) {
      const _self = this.documentRequestVerify;
      const dialg = this.dialg;
      _self.set('style', { display: '', 'text-align': '' });
      for (const doom of domHandler.destroyables) {
        domConstruct.destroy(doom);
      }
      domHandler.acceptBtn.destroyRecursive();
      domHandler.dialogContainerNode.style.textAlign = '';
      domClass.remove(dialg.domNode, 'reject-request');
      _self.hideLoader();
    },
    rejectHandle: (requestId, attender) => {
      const dialg = attender.dialg;
      const dialogContainerNode = dialg.get('containerNode');
      domClass.add(dialg.domNode, 'reject-request');
      dialogContainerNode.style.boxShadow = '0 1px 5px 0 rgba(0, 0, 0, 0.26), 0 2px 2px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.08)';
      const domHandler = {
        destroyables: [],
        destroyableWidgets: []
      };
      const rejectReasonContainer = domConstruct.create(
        'div',
        {
          class: 'grid-floating-action-buttons',
          innerHTML: `
                            <div class="header grid-container"> 
                                <div class="grid-x"> 
                                    <h3 class="cell igx-card-header__title content_title">  ${i18n.messages_documentVerificationReject}  </h3> 
                                </div> 
                            </div> 
                            <div class="floating-action-buttons hideOnPrint"></div> 
                            </div> 
                            <div class="grid-container"> 
                                <div class="grid-x grid-padding-x"> 
                                    <div class="cell small-12">
                                        <div class="textarea-component"> 
                                            <textarea id="rejectReasonTextArea"></textarea> 
                                            <label>  ${i18n.messages_rejectReason}  </label>
                                        </div>
                                    </div>
                                    <div class="cell small-12">&nbsp;</div> 
                                    <div id="reject-buttons-container" class="cell small-12"></div>
                                    <div class="cell small-12">&nbsp;</div> 
                                </div> 
                            </div> 
                            `
        },
        dialogContainerNode
      );
      const acceptBtn = new Button({
        label: i18n.messages_accept,
        class: 'raised-button accept-btn',
        onClick: () => {
          attender.rejectAcceptAction(domHandler, requestId);
        }
      });
      const cancelBtn = new Button({
        label: i18n.messages_cancel,
        class: 'cancel-btn',
        onClick: () => {
          dialogContainerNode.style.boxShadow = 'none';
          attender.rejectCancelAction(domHandler);
        }
      });
      core.autoheight('rejectReasonTextArea');
      acceptBtn.placeAt(dom.byId('reject-buttons-container'));
      cancelBtn.placeAt(dom.byId('reject-buttons-container'));
      domHandler.acceptBtn = acceptBtn;
      domHandler.cancelBtn = cancelBtn;
      domHandler.dialogContainerNode = dialogContainerNode;
      domHandler.rejectReasonTextArea = dom.byId('rejectReasonTextArea');
      domHandler.destroyables.push(rejectReasonContainer);
      domHandler.destroyableWidgets.push(cancelBtn, acceptBtn);
      core.hideLoader();
    }
  };
  const DocumentToVerifyRequestAttender = declare([_WB], DocumentToVerifyRequestAttenderBase);
  return DocumentToVerifyRequestAttender;
});
