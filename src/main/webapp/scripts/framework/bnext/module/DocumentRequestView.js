define([
  'core',
  'dojo/_base/declare',
  'dojo/dom-class',
  'bnext/module/DocumentRequest',
  'dojo/date/locale',
  'bnext/module/data/RequestStatus',
  'bnext/module/data/OutstandingSurveys',
  'bnext/module/DocumentCodePreview',
  'bnext/i18n!bnext/administrator/document/nls/document-module',
  'bnext/extensionIcons',
  'bnext/angularNavigator',
  'bnext/callMethod',
  'dojo/_base/lang',
  'dojo/dom-construct',
  'dijit/form/CheckBox',
  'bnext/i18n!./nls/DocumentRequestView',
  'dijit/form/Button',
  'dojo/dom'
], (
  core,
  declare,
  domClass,
  DocumentRequest,
  locale,
  RequestStatus,
  OutstandingStatus,
  DocumentCodePreview,
  documentModule,
  extensionIcons,
  angularNavigator,
  callMethod,
  lang,
  domConstruct,
  CheckBox,
  i18n,
  Button,
  dom
) => {
  const datePattern = 'yyyy-MM-dd';
  const archived = dom.byId('archived');
  function parseDate(str) {
    if (!str) {
      return '0000-00-00';
    }
    return locale.parse(str.substr(0, str.indexOf('T')), {
      datePattern: datePattern,
      formatLength: 'short',
      selector: 'date'
    });
  }

  function formatDate(dte) {
    return locale.format(dte, {
      selector: 'date'
    });
  }

  function formatDateString(str) {
    return formatDate(parseDate(str));
  }
  let DocumentRequestView = {
    postCreate: function () {
      this.documentRequestView = true;
      //hace todo el setup de DocumentRequest
      this.type = this.sol.type;
      if (this.type === DocumentRequest.FILL) {
        domClass.add(this.collectingAndStoreResponsibleContent, 'displayNone');
        domClass.add(this.collectingAndStoreRespDesc, 'displayNone');
        domClass.add(this.collectingAndStoreResp, 'displayNone');
      } else if (this.sol.documentType.collectingAndStoreResponsible && this.type !== DocumentRequest.FILL) {
        domClass.remove(this.collectingAndStoreResponsibleContent, 'displayNone');
        if (this.sol.collectingAndStoreResponsibleDescription && this.sol.collectingAndStoreResponsibleDescription !== '') {
          domClass.remove(this.collectingAndStoreRespDesc, 'displayNone');
          this.collectingAndStoreResponsibleDescription.set('value', this.sol.collectingAndStoreResponsibleDescription);
        } else {
          domClass.remove(this.collectingAndStoreResp, 'displayNone');
          this._setupCollectStoreRespDef();
        }
      }
      if (this.sol.documentType.informationClassification && this.type !== DocumentRequest.FILL) {
        domClass.remove(this.informationClassificationDiv, 'displayNone');
        this._setupInformationClassificationDef();
      }
      if (this.sol.documentType.disposition && this.type !== DocumentRequest.FILL) {
        domClass.remove(this.dispositionDiv, 'displayNone');
        this._setupDispositionDef();
      }
      this.status = this.sol.status;
      // biome-ignore lint/style/noArguments: TODO: Fix this
      this.inherited(arguments);
      this.originalFileId = this.sol.fileId || null;
      this._createMissingDom();
      this._setup();
      if (this.code.get('value') === '') {
        domClass.add(this.code.domNode, 'displayNone');
        domClass.remove(this.codeMsgNode, 'displayNone');
        if (this.sol && this.sol.generateCode === 1) {
          DocumentCodePreview.fillDiv(this.sol.id, this.codeMsgNode);
        }
      }
      if ((this.sol?.outstandingSurveysId && this.status === RequestStatus.IN_PROCESS) || this.status === RequestStatus.STAND_BY) {
        callMethod({
          url: 'Request.My.Survey.action',
          method: 'getWorkflowAuthRole',
          params: [this.sol.outstandingSurveysId]
        }).then(
          lang.hitch(this, function (authRole) {
            const showContinueFill = authRole && (authRole === 'REQUESTOR' || authRole === 'ASSIGNED' || authRole === 'ADMIN');
            this.authRole = authRole;
            const deleted = +this.sol.deleted;
            if (archived && archived.value === 'true' && this.archived) {
              return;
            }
            if (deleted === 0 && showContinueFill) {
              this.formContinue.set('style', {
                display: 'inline-block'
              });
            }
          })
        );
      }
    },
    onDownloadFileAction: function () {
      const viewerUrl = `v-document-viewer.view?fileId=${this.originalFileId}&documentId=-1&requestId=${this.sol.id}`;
      angularNavigator.showDocumentViewer(viewerUrl);
    },
    onOriginalFileAction: function () {
      const file = this.sol.oldDocument || this.sol.document;
      this.fDownloaderAnchor.href = `v-visor-pdf.view?fileId=${file.fileId}&documentId=-1&requestId=${file.requestId}`;
      core.anchorClick(this.fDownloaderAnchor);
    },
    _createMissingDom: function () {
      const fUploaderDiv = this.fUploader.get('domNode');
      fUploaderDiv.style.display = 'none';
      const originalFileId = this.originalFileId || null;
      if (originalFileId) {
        this.fDownloaderAnchor = domConstruct.create(
          'a',
          {
            href: 'javascript: void(0);',
            style: {
              display: 'none'
            },
            target: window.top.pdfViewer ? 'visorPdf' : ''
          },
          fUploaderDiv,
          'before'
        );
        const fileInfo = extensionIcons.getExtensionInfo(this.sol.fileContent, this.sol);
        this.downloadFileSrc.src = fileInfo.iconSrc;
        if (this.fileLabel) {
          this.fileLabel.value = fileInfo.title;
        }
        domClass.remove(this.downloadFileLi, 'displayNone');
        if (this.sol.type === DocumentRequest.update || this.type === DocumentRequest.editDetails) {
          const doc = this.sol.oldDocument || this.sol.document;
          const originalFileInfo = extensionIcons.getExtensionInfo(doc.fileContent, doc.oldDocument);
          this.originalFileSrc.src = originalFileInfo.iconSrc;
          domClass.remove(this.originalFileLi, 'displayNone');
        }
      }
      if (
        this.sol.documentType.documentControlledType === 'controlled' &&
        (this.sol.status === RequestStatus.REJECTED ||
          this.sol.status === RequestStatus.RETURNED ||
          this.sol.status === RequestStatus.IN_PROCESS ||
          this.sol.status === RequestStatus.FINISHED ||
          this.sol.status === RequestStatus.EXPIRED)
      ) {
        let btnText;
        if (this.isAFormRequest && this.outstandingSurveysId.value !== '') {
          btnText = i18n.flowDetailButton;
        } else {
          btnText = i18n.authorizationDetailButton;
        }
        authorizationDetailButton = new Button({
          innerHTML: `<span class="material-icons">supervised_user_circle</span><input type="button" button="type"  value="${btnText}"/>`,
          class: 'view-form-container button-component outlined-button',
          onClick: () => {
            if (this.sol.flujoId === null && this.sol.status === RequestStatus.REJECTED) {
              const user = lang.getObject('sol.rejectedByUser.description', false, this);
              let message;
              callMethod({
                url: 'Request.My.Survey.action',
                method: 'getStatusOfOutstandingSurveyId',
                params: [this.sol.outstandingSurveysId]
              }).then((outstandingSurveyStatus) => {
                if (outstandingSurveyStatus === OutstandingStatus.CANCELLED && user) {
                  message = i18n.cancelledMessage.toString().replace(':user', user);
                } else if (user) {
                  message = i18n.rejectionMessage.toString().replace(':user', user);
                } else {
                  message = i18n.automaticRejectionMessage;
                }
                core.dialog(message, core.i18n.accept);
              });
              return;
            }
            core.showLoader().then(() => {
              const url = `v.sequence.detail.list.view?requestId=${this.sol.id}&sourcePage=${this.sourcePage}&show_hide_buttons=none`;
              angularNavigator.navigateLegacy(url);
              this.hide?.();
            });
          }
        });
        authorizationDetailButton.placeAt(this.formViewContainer, 'before');
      }
      this.replaceDocDiv = domConstruct.create(
        'div',
        {
          innerHTML: `<label>${i18n.replaceDocument}</label><div></div>`
        },
        fUploaderDiv,
        'before'
      );
      this.uploadCheck = new CheckBox({
        onChange: (x) => {
          fUploaderDiv.style.display = x ? '' : 'none';
        }
      }).placeAt(this.replaceDocDiv.childNodes[1]);
    },
    _setup: function () {
      const date = formatDateString(this.sol.creationDate);
      this.name.set('value', this.sol.description);
      this.code.set('value', this.sol.documentCode);
      this.deferreds.then(
        lang.hitch(this, function () {
          this.docType.set('value', this.sol.documentType.id);
          this.currentUser.set('value', this.sol.author.description);
        })
      );
      this.version.set('value', this.sol.version);
      this.date.set('value', date);
      const tempReason = domConstruct.create('div', {
        innerHTML: this.sol.reazon
      });
      this.setReasonReadOnly(tempReason.textContent || tempReason.innerText || '');
      domClass.add(this.nodeDiv, 'displayNone');
      this.selectFolder.innerHTML = this.sol.nodo.code;
      this.selectFolder.title = this.sol.nodo.code;
      this.node = this.sol.nodo;
      this.docType.set('disabled', true);
      this.name.set('disabled', true);
      this.code.set('disabled', true);
      this.version.set('disabled', true);
      this.collectingAndStoreResponsible.set('disabled', true);
      this.licenceUserCheck.set('disabled', true);
      this.collectingAndStoreResponsibleDescription.set('disabled', true);
      this.informationClassification.set('disabled', true);
      this.disposition.set('disabled', true);
      this.department.set({ disabled: true, required: false });
      this.unknownCodeDiv.style.display = 'none';
      this.fUploader.set('style', {
        display: 'none'
      });
      this.replaceDocDiv.style.display = 'none';
      this.cancel.set('label', i18n.returns);
      this.cancel.set('class', 'raised-button');
      this.accept.set('style', 'display:none');
      if (this.storagePlace.value > 0 || this.sol.storagePlaceId > 0) {
        this.storagePlaceContent.style.display = '';
      }
      this.storageSpan.set({ disabled: true, required: false });
      return this.sol;
    },
    _onDocTypeChange: function (value) {
      const type = this.docType.get('store').get(value);
      if (type) {
        this.documentControlledType.set('value', documentModule[type.documentControlledType]);
        this.dictionaryIndex.set('value', i18n[type.dictionaryIndex.code]);
      } else {
        this.documentControlledType.set('value', i18n.toDefine);
        this.dictionaryIndex.set('value', i18n.toDefine);
      }
    }
  };
  DocumentRequestView = declare([DocumentRequest], DocumentRequestView);
  return DocumentRequestView;
});
