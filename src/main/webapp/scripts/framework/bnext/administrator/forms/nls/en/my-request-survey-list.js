define({
  language_strings: [{ element: '#window_title', text: 'Designed by me' }],
  statusLst: {
    none: '--SELECT--',
    active: 'Active',
    locked: 'Locked',
    inactive: 'Inactive'
  },
  colNameFill: 'Continue',
  colNamesUseAsTemplate: 'Template',
  colNamesUseAsTemplateHelp: 'Can be used as a template',
  colNames: {
    estatus_header: 'Status',
    vchTexto_header: 'Name',
    intQuestions_header: '# Fields',
    dteCreacion_header: 'Creation date',
    grid_preview_header: 'Preview',
    grid_edit_header: 'Edit',
    request: 'Request'
  },
  requestDialogTitle: 'Request',
  requestIconTitle: 'The form has a related document request',
  msg: {
    change_status: 'Do you really want to change the document status?',
    success_change: 'The form has been successfully modified.',
    toggling_status: "The status it's being updated..."
  },
  colNamesRequestDocumentCode: 'Folio',
  colNamesRequestStatus: 'Request status',
  statusNameStandBy: 'Draft',
  statusNameResquested: 'Requested',
  statusNameRejected: 'Rejected',
  statusNameReturned: 'Returned',
  statusNameInProcess: 'In process',
  statusNameFinished: 'Finished',
  statusNameArchived: 'Archived',
  statusNameFinishedForm: 'Completed form',
  clicToChangeTo: 'Click to switch to ',
  requestType: 'Type of request',
  requestVersion: 'Request version',
  formVersion: 'Form version',
  typeOfRequest_template: 'Template',
  typeOfRequest_new: 'New',
  typeOfRequest_modification: 'Modification',
  typeOfRequest_edit_details: 'Edit details',
  confirmDenyTemplate: 'Do you really not want to let this form be used as a template for creating new ones?<br>',
  confirmNewTemplate: 'Do you really want to let this form be used as a template for creating new ones?<br>',
  warningSaveDenyTemplate: 'This form will no longer appear in the list of available templates.',
  warningSaveConfirmTemplate: 'This form now appears in the list of available templates in new forms.',
  msgChangeStatusLocked: 'The form is locked and can not be modified.',
  modifyForm: 'Form edited successfully.',
  noModifyForm: 'Could not modify the form.'
});
