require([
  'core',
  'bnext/gridCubes',
  'bnext/gridIcons',
  'bnext/gridComponent',
  'bnext/gridComponentUtil',
  'dojo/_base/lang',
  'dojo/dom',
  'bnext/angularNavigator',
  'bnext/module/DocumentRequestView',
  'bnext/module/data/RequestType',
  'bnext/module/data/RequestStatus',
  'bnext/i18n!bnext/administrator/document/nls/document-module',
  'bnext/callMethod',
  'dojo/domReady!'
], (core, gridCubes, gridIcons, gridComponent, gcUtil, dojoLang, dom, angularNavigator, DocumentRequestView, RequestType, RequestStatus, documentModule, callMethod) => {
  const doEverything = (lang) => {
    const c_forms = [];
    const cType = gcUtil.cType;
    let docComponent;
    const requests = {};
    const dialog = core.newDialog();
    const statusList = [
      { name: lang.statusLst.active, value: 1, icon: gridCubes.green },
      { name: lang.statusLst.locked, value: 2, icon: gridCubes.black },
      { name: lang.statusLst.inactive, value: 0, icon: gridCubes.gray }
    ];
    const hasRequestVal = (row) => row.requestId;
    const gatherRequest = (requestId) => {
      core.showLoader();
      if (requests[requestId]) {
        requestHandle(requests[requestId]);
        core.hideLoader();
      } else {
        callMethod({
          url: 'Request.action',
          params: [requestId],
          method: 'load'
        }).then(
          (request) => {
            if (request === null) {
              core.dialog(core.i18n.variableMsg('fail', 'error', 'Null request'), core.i18n.accept);
              dialog.hide();
            } else {
              requestHandle(request);
            }
            core.hideLoader();
          },
          (failResult) => {
            core.dialog(core.i18n.variableMsg('fail', 'error', failResult), core.i18n.accept);
            core.hideLoader();
            dialog.hide();
          }
        );
      }
    };
    const requestHandle = (request) => {
      if (docComponent) {
        docComponent.destroyRecursive();
      }
      docComponent = new DocumentRequestView({
        isAFormRequest: !!request.surveyId,
        surveyId: request.surveyId || null,
        enablePdfViewer: request.enablePdfViewer || 0,
        sol: request,
        sourcePage: 'myFormList',
        onCancel: () => {
          docComponent.hide();
        },
        requestDialog: dialog
      });
      docComponent.set('title', `${lang.requestDialogTitle} - ${request.code}`);
      docComponent.show();
    };
    const editRecord = (surveyId, requestId) => {
      core.showLoader();
      if (requestId && requestId !== 'null') {
        window.location = `../DPMS/v.request.survey.mode.view?id=${surveyId}&requestId=${requestId}&viewMode=edit`;
      } else {
        window.location = `../DPMS/v.request.survey.mode.view?id=${surveyId}&viewMode=edit`;
      }
    };
    //estatus de solicitud
    const printStandBy = (row) => +row.requestStatus === RequestStatus.STAND_BY;
    const printResquested = (row) => +row.requestStatus === RequestStatus.REQUESTED;
    const printRejected = (row) => {
      return +row.requestStatus === RequestStatus.REJECTED; //5
    };
    const printReturned = (row) => +row.requestStatus === RequestStatus.RETURNED;
    const printInProcess = (row) => +row.requestStatus === RequestStatus.IN_PROCESS;
    const printFinished = (row) => +row.requestStatus === RequestStatus.FINISHED;
    const printTypeTemplate = (row) => !row.requestId;
    const printTypeNewForm = (row) => +row.requestType === RequestType.NEW;
    const printTypeModify = (row) => +row.requestType === RequestType.MODIFY;
    const printTypeEditDetails = (row) => +row.requestType === RequestType.EDIT_DETAILS;
    const validRequestStatus = (row) => +row.requestStatus === RequestStatus.STAND_BY || !row.requestId;
    const changeIsTemplateUseAvailable = (surveyId, isTemplateUseAvailable) => {
      let _message;
      const isTemplate = isTemplateUseAvailable === 1;
      if (isTemplate) {
        _message = `${lang.confirmDenyTemplate}`;
      } else {
        _message = `${lang.confirmNewTemplate}`;
      }
      core.dialog(_message, core.i18n.Validate.yes, core.i18n.Validate.no).then((evt) => {
        callMethod({
          url: 'Request.Survey.action',
          params: [surveyId, isTemplate],
          method: 'changeIsTemplateUseAvailable'
        }).then(
          (success) => {
            if (success) {
              core.dialog(lang.modifyForm, core.i18n.accept).then(() => {
                grid_forms.refreshData();
              });
            } else {
              core.error(lang.noModifyForm, core.i18n.accept);
            }
            core.hideLoader();
          },
          (failResult) => {
            core.error(core.i18n.variableMsg('fail', 'error', failResult), core.i18n.accept);
            core.hideLoader();
          }
        );
      }, core.$noop);
    };
    const toggleStatus = (id, status) => {
      if (status === 2) {
        // Cuando status vale 2 está Bloqueado
        core.dialog(lang.msgChangeStatusLocked);
      } else {
        core.showLoader(lang.msg.toggling_status);
        core.dialogExecutioner(
          lang.msg.change_status,
          core.i18n.Validate.yes,
          core.i18n.Validate.no,
          'Request.Survey.Status.action',
          'toggleStatus',
          [id],
          lang.msg.success_change,
          '',
          core.i18n.Validate.accept,
          grid_forms
        );
        hideLoader();
      }
    };
    const preview = (id, requestId, documentId, documentMasterId) => {
      const optionalParams = `${documentId ? `&documentId=${documentId}` : ''}${documentMasterId ? `&documentMasterId=${documentMasterId}` : ''}${requestId ? `&requestId=${requestId}` : ''}${!documentId ? '&viewFinder=true' : ''}`;
      angularNavigator.navigateLegacy(`v.request.survey.preview.view?id=${id}&task=preview&origin=my_request_survey_list${optionalParams}`);
    };
    const requestStatusList = [
      {
        text: lang.statusNameStandBy,
        searchName: lang.statusNameStandBy,
        Function: printStandBy,
        value: RequestStatus.STAND_BY
      },
      {
        text: lang.statusNameResquested,
        searchName: lang.statusNameResquested,
        Function: printResquested,
        value: RequestStatus.REQUESTED
      },
      {
        text: lang.statusNameRejected,
        searchName: lang.statusNameRejected,
        Function: printRejected,
        value: RequestStatus.REJECTED
      },
      {
        text: lang.statusNameReturned,
        searchName: lang.statusNameReturned,
        Function: printReturned,
        value: RequestStatus.RETURNED
      },
      {
        text: lang.statusNameInProcess,
        searchName: lang.statusNameInProcess,
        Function: printInProcess,
        value: RequestStatus.IN_PROCESS
      },
      {
        text: lang.statusNameFinished,
        searchName: lang.statusNameFinished,
        Function: printFinished,
        value: RequestStatus.FINISHED
      }
    ];
    const isTemplateUseAvailableList = [
      {
        icon: gridIcons.approve,
        Function: (row) => row.isTemplateUseAvailable,
        name: `${core.i18n.Validate.yes}, ${lang.clicToChangeTo} '${core.i18n.Validate.no}'`,
        searchName: core.i18n.Validate.yes,
        value: '1'
      },
      {
        icon: gridIcons.reject,
        Function: (row) => !row.isTemplateUseAvailable,
        name: `${core.i18n.Validate.no}, ${lang.clicToChangeTo} '${core.i18n.Validate.yes}'`,
        searchName: core.i18n.Validate.no,
        value: '0'
      }
    ];
    const decodeUri = (row) => decodeURIComponent(row.vchTexto);

    gcUtil
      .column(c_forms)
      .push('estatus', lang.colNames.estatus_header, cType().FunctionImage(['id', 'estatus'], toggleStatus, statusList, true), null, '30px')
      .push(
        'isTemplateUseAvailable',
        lang.colNamesUseAsTemplate,
        cType().FunctionImageEval(['id', 'isTemplateUseAvailable'], changeIsTemplateUseAvailable, isTemplateUseAvailableList, true),
        null,
        '80px'
      )
      .set('titleHelp', lang.colNamesUseAsTemplateHelp)
      .push('preview', lang.colNames.grid_preview_header, cType().Function(['id', 'requestId', 'documentMasterId'], preview, gridIcons.question), null, '80px')
      .push(
        'request_view',
        lang.colNames.request,
        cType().FunctionImageEval(['requestId'], gatherRequest, [
          {
            icon: gridIcons.view_comment,
            Function: hasRequestVal,
            name: lang.requestIconTitle
          }
        ]),
        null,
        '80px'
      )
      .push(
        'edit',
        lang.colNames.grid_edit_header,
        cType().FunctionImageEval(['id', 'requestId'], editRecord, [
          {
            icon: gridIcons.edit,
            Function: validRequestStatus,
            name: lang.requestIconTitle
          }
        ]),
        null,
        '50px'
      )
      .push(
        'requestType',
        lang.requestType,
        cType.FunctionTextEval(['id'], core.$noop, [
          { text: lang.typeOfRequest_template, Function: printTypeTemplate },
          { text: lang.typeOfRequest_new, Function: printTypeNewForm },
          { text: lang.typeOfRequest_modification, Function: printTypeModify },
          { text: lang.typeOfRequest_edit_details, Function: printTypeEditDetails }
        ]),
        null,
        '85px'
      )
      .push('requestStatus', lang.colNamesRequestStatus, cType.FunctionTextEval(['id'], core.$noop, requestStatusList, true), null, '85px')
      .push('requestVersion', lang.requestVersion, cType.Text(), null, '70px')
      .push('documentVersion', lang.formVersion, cType.Text(), null, '70px')
      .push('vchTexto', lang.colNames.vchTexto_header, cType.Text(), 1)
      .push('intQuestions', lang.colNames.intQuestions_header, cType.Integer(), 2, '90px')
      .push(
        'restrictRecordsByDepartment',
        documentModule.restrictRecordsByDepartment,
        cType.TextMap([
          { name: core.i18n.yes, value: true },
          { name: core.i18n.no, value: false }
        ]),
        null,
        '150px',
        'none'
      )
      .push(
        'validateAccessFormDepartment',
        documentModule.validateAccessFormDepartment,
        cType.TextMap([
          { name: core.i18n.yes, value: true },
          { name: core.i18n.no, value: false }
        ]),
        null,
        '150px',
        'none'
      )
      .push('dteCreacion', lang.colNames.dteCreacion_header, cType().Date(true), null, '110px');
    const grid_forms = new gridComponent(
      gcUtil.basic({
        size: dom.byId('gridSize').value,
        container: 'grid_forms',
        serviceStore: 'Request.My.Survey.action',
        methodName: 'getMyRequestSurveyRows',
        windowPath: dom.byId('windowPath').value,
        fullColumns: c_forms
      })
    );
    grid_forms.setPageSize(dom.byId('gridSize').value);
  };
  fillFooter();
  core.setLang('bnext/administrator/forms/nls/my-request-survey-list').then(doEverything);
  hideLoader();
});
