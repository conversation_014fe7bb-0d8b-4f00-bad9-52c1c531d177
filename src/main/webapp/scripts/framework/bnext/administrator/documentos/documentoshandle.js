require([
    'bnext/i18n!bnext/administrator/documentos/nls/documentoshandle',
    'bnext/i18n!lang/configuracion/nls/configuration.facility', 'bnext/gridIcons',
    'bnext/DynamicFieldUtil', 'bnext/module/grid-menu', 'bnext/module/grid-menu-util', 'bnext/administrator/document/document-menu-item',
    'dojo/_base/lang', 'dojo/_base/query', 'dojo/aspect', 'core',  'bnext/columnTypes',
    'dojo/dom-class', 'dojo/dom-attr', 'dojo/dom-style',
    'bnext/angularNavigator', 'bnext/angularDocumentOptions',
    'dijit/Dialog', 'bnext/gridComponent', 'dojo/on', 'bnext/callMethod', 'dojo/dom-construct', 'dojo/_base/array',
    'bnext/gridComponentUtil', 'dojo/domReady!', 'dojo/dom', 'dijit/registry', 'bnext/angularNotice', 'dojo/domReady!'],
function(
    i18n, facilityI18n, gridIcons, DynamicFieldUtil, GridMenu, GridMenuUtil, DocumentMenuItem, lang, query, aspect,
    core, columnTypes,
    domClass, domAttr, domStyle, angularNavigator, angularDocumentOptions, Dialog, gridComponent, on, callMethod, domConstruct, array, gcUtil, doc, dom, registry, angularNotice
) {
    doc.body.className += ' bnext';
    var 
        global = window.documentoshandle,
        gridRelatedDocument,
        dialog = new Dialog({title: i18n.documentos_documentoshandle_AsignarDocumentosRelacionados}),
        editRelatedDocuments = dom.byId('editRelatedDocuments').value === 'true' && global.activeDocument,
        editReaders = dom.byId('editReaders').value === 'true' && global.activeDocument,
        editCopyPositions = dom.byId('editCopyPositions').value === 'true',
        editRetention = dom.byId('editRetention').value === 'true'
    ;
    if (global.positionForControlledCopyOn) {
        global.positionForControlledCopyOn = editCopyPositions;
    }
    core.fixDialogTop(dialog);
    var 
        contentArea = domConstruct.create("ul", {
            'class': 'content_area',
            innerHTML: '<li><table id="extraDataGrid"></table></li>'
        }, dialog.get('containerNode')),
        closeBtn = domConstruct.create("input", {
            type: 'button',
            'class': 'Button right finishBtn raised-button',
            value: i18n.documentos_documentoshandle_Cerrar},
            domConstruct.create('li', {
                'class': 'actionButtons',
                style: {
                    paddingBottom: '0px!important'
                }
            }, contentArea)
        ),
        gridPosibleRelatedDocument,
        c = [], cType = gcUtil.cType
    ;
    function previewAction(id) {
        var isDocOrForm = 0; // Documento
        var data = gridRelatedDocument.getBean().data, l = data.length, i = 0;
        for (; i < l; i++) {
            if (data[i].id === id) {
                if(!core.isNull(data[i].fileId)){
                    id = data[i].fileId;
                    break;
                }else{
                    id = data[i].survey.id;
                    isDocOrForm = 1; // Formulario
                    break;
                }
            }
        }
        if(+isDocOrForm===0){
            window.open('../view/v-visor-pdf.view?fileId=' + id, 'pdfview' + id );
        } else {
            window.open('../view/v.request.survey.preview.action?id=' + id );
        }
    }
    function removeAction(documentId) {
        callMethod({
            url: '../DPMS/Document.action',
            method: 'unrelateDocument',
            params: [global.intDocumentoId, documentId]
        }).then(function() {
            gridRelatedDocument.popRowById(documentId);
            gridRelatedDocument.updateDataInfo();
        });
    }
    gcUtil.column(c)
        .push('status', i18n.documentos_documentoshandle_Estatus, null, null, null, 'none')
        .push('view', i18n.documentos_documentoshandle_Ver, cType.Function(['id'], previewAction, gridIcons.preview), null, '30px');
    if (editRelatedDocuments) {gcUtil.column(c)
        .push('remove', i18n.remove, cType.Function(['id'], removeAction, gridIcons.remove), null, '30px');
    }
    gcUtil.column(c)
        .push('code', i18n.documentos_documentoshandle_Clave2, columnTypes.text, 2, '90px')
        .push('description', i18n.documentos_documentoshandle_Descripcion, columnTypes.text, 1, '150px')
        .push('businessUnit.description', facilityI18n.colNamefacility, columnTypes.text, 1, '150px')
    ;
    var gridComponentConfig = {
        id: "gridRelatedDocument",
        container: "datagrid_related_documents",               
        resultsInfo: "#auto",
        paginationInfo: "#auto",
        serviceStore: "../DPMS/Document.action?currentEntityId=" + global.intDocumentoId,
        methodName: "getRelatedDocumentsList",
        noExcel: true,
        noEyes: true,
        noPagination: true,
        columns: c                
    };
    if (global.isInactiveStatus) {
        if (editRelatedDocuments) {
            doc.getElementById('add_related_documents_btn') && on(doc.getElementById('add_related_documents_btn'), 'click', function(evt) {
                gridPosibleRelatedDocument.setPageSize(doc.getElementById('floatingGridSize').value);
                dialog.show();
                doc.body.style.overflow ='hidden';
            });
        } else if (dom.byId('add_related_documents_btn')) {
            domStyle.set(dom.byId('add_related_documents_btn'), 'display', 'none');
        }
        on(closeBtn, 'click', function(evt) {
            if (!editRelatedDocuments) {
                dialog.hide();
                return;
            }
            var dta = gridRelatedDocument.getBean().data, arr = [];
            for (var i in dta) {
                arr.push(dta[i].id);
            }
            callMethod({
                url: '../DPMS/Document.action',
                method: 'relateDocuments',
                params: [global.intDocumentoId, arr]
            });
            dialog.hide();
            doc.body.style.overflow ='auto';
        });
    } else {
        try {
            doc.getElementById('add_related_documents_btn') && (doc.getElementById('add_related_documents_btn').style.display = 'none');
            delete gridComponentConfig.columns.delete_column;
            delete gridComponentConfig.columns.deleteRecordActionDefault;
        } catch (e) {
        }
    }
    if (dom.byId('datagrid_related_documents')) {
        gridRelatedDocument = new gridComponent(gridComponentConfig);
        //Aqui comienza la configuración del Grid de la ventana que aparece al dar clic a Add Related Document
        var c = [], cType = gcUtil.cType;
        gcUtil.column(c)
            .push('status', i18n.documentos_documentoshandle_Estatus, null, null, null, 'none')
            .push('remove', i18n.documentos_documentoshandle_Agregar, cType.Function(['id'],
                    function(id) {
                        gridPosibleRelatedDocument.popRowById(id);
                        gridPosibleRelatedDocument.updateDataInfo();
                    }, gridIcons.append), null, '30px')
            .push('code', i18n.documentos_documentoshandle_Clave2, columnTypes.text, 2, '90px')
            .push('description', i18n.documentos_documentoshandle_Descripcion, columnTypes.text, 1, '150px')   
            .push('businessUnit.description', facilityI18n.colNamefacility, columnTypes.text, null, '150px')
        ;
        gridPosibleRelatedDocument = new gridComponent(gcUtil.basic({
            id: "gridPosibleRelatedDocument",
            container: "extraDataGrid",
            size: dom.byId('floatingGridSize').value,
            searchContainer: "#auto",
            noExcel: true,
            noEyes: true,
            noPagination: true,
            serviceStore: "../DPMS/Document.action?currentEntityId=" + global.intDocumentoId,
            methodName: "getPosibleRelatedDocumentsList",
            fullColumns: c                
        }));
        gridRelatedDocument.setPageSize(doc.getElementById('floatingGridSize').value);
        gridRelatedDocument.linkGrid(gridPosibleRelatedDocument);
    }
    /*Componentes para el listado de puestos asignados para generar copias controladas*/
    var grid_floating_size = dom.byId('floatingGridSize').value,
    grid_positions_to_add, grid_positions_added, gridReadersDialog, gridReadersBase,
    showReadersSection = dom.byId('readers_container');
    function setPositions() {
        core.fixDialogTop('positions_dialog');
        registry.byId('positions_dialog').show();
        registry.byId('positions_dialog').set('title', i18n.documentos_documentoshandle_lectoresdelacopiacontrolada);
        registry.byId('positions_dialog').set('style', {top: '0px'});
        /*Se refresca la información del grid del cuál se toman los datos*/
        grid_positions_to_add.setCurrentPage(0);
        grid_positions_to_add.refreshData();
        doc.body.style.overflow ='hidden';
    }
    function toggle_related_documents() {
        var li = dom.byId('docs');
        if (li.style.display === 'none') {
            dom.byId('docs').style.display = '';
            dom.byId('docsbtn').innerHTML = i18n.documentos_documentoshandle_Ocultar;
        } else {
            dom.byId('docs').style.display = 'none';
            dom.byId('docsbtn').innerHTML = i18n.boton_Mostrar;
        }
    }
    function toggle_readers() {
        if (global.readersOn) {
            var li = dom.byId('readers_container');
            if (li.style.display === 'none') {
                dom.byId('readers_container').style.display = '';
                dom.byId('readersBtn').innerHTML = i18n.documentos_documentoshandle_Ocultar;
                gridReadersBase.setCurrentPage(0);
                gridReadersBase.refreshData();
            } else {
                dom.byId('readers_container').style.display = 'none';
                dom.byId('readersBtn').innerHTML = i18n.boton_Mostrar;
            }
        } 
    }
    function toggle_positions() {
        var li = dom.byId('positions_container');
        if (li.style.display === 'none') {
            li.style.display = '';
            dom.byId('show_positions_btn').innerHTML = i18n.documentos_documentoshandle_Ocultar;
            grid_positions_added.setCurrentPage(0);
            grid_positions_added.refreshData();
        } else {
            li.style.display = 'none';
            dom.byId('show_positions_btn').innerHTML = i18n.boton_Mostrar;
        }
    };

    function push(id) {
        grid_positions_to_add.popRowById(id);
        grid_positions_to_add.updateDataInfo();
        save_position(id);
    }

    function pop(id) {
        grid_positions_added.popRowById(id);
        grid_positions_added.updateDataInfo();
        delete_position(id);
    }

    function save_position(positionId) {
        showLoader();
        var documentId = +global.intDocumentoId;
        console.log("[documentId=" + documentId + ", positionId=" + positionId + "]");
        var args = {
            url: "../DPMS/PhysicalReader.action",
            method: "save",
            params: [documentId, positionId]
        };
        callMethod(args).then(function(value) {
            if (!value) {
                dialog(i18n.documentos_documentoshandle_Haocurridounerroralintentargenerarlacopiacontrolada, i18n.documentosproyectos_listasdocumentos_aceptar);
            }
            hideLoader();
        });
    }

    function delete_position(positionId) {
        showLoader();
        var documentId = +global.intDocumentoId;
        console.log("[documentId=" + documentId + ", positionId=" + positionId + "]");
        var args = {
            url: "../DPMS/PhysicalReader.action",
            method: "delete",
            params: [documentId, positionId]
        };
        callMethod(args).then(function() {
            hideLoader();
        });
    }
    var newReaders = [];
    function addReader(id) {
        gridReadersDialog.popRowById(id);
        gridReadersDialog.updateDataInfo();
        newReaders.push(id);
    }
    
    function delete_reader(userId) {
        if (!editReaders) {
            return;
        }
        var documentId = +global.intDocumentoId;
        console.log("[documentId=" + documentId + ", userId=" + userId + "]");
        var args = {
            url: "../DPMS/ReadersByDocument.action",
            method: "delete",
            params: [documentId, userId]
        };
        callMethod(args).then(core.hideLoader, core.hideLoader);
    }
    function doEverything() {
        core.setLanguageByLang(i18n);
        if (dom.byId('datagrid_readers_to_add')) {
            if(showReadersSection) {
                /*Columnas para lectores del documento por agregar*/
                var columnsReadersDialog = [], cType = gcUtil.cType;
                if (global.readersOn) {
                    if (editReaders) {
                        gcUtil.column(columnsReadersDialog)
                            .push('add', i18n.documentos_documentoshandle_Agregar, cType.Function(['id'], addReader, gridIcons.append), null, '30px');
                    }
                    gcUtil.column(columnsReadersDialog)
                        .push('code', i18n.documentos_documentoshandle_Clave2, columnTypes.text, 2, '90px')
                        .push('description', i18n.documentos_documentoshandle_Descripcion, columnTypes.text, 1, '200px')
                        .push('job_description', i18n.documentos_documentoshandle_Puestos, columnTypes.text, true)
                        .push('department_description', i18n.documentos_documentoshandle_Department, columnTypes.text, 1, '200px')
                        .push('une_description', i18n.documentos_documentoshandle_Plantas, columnTypes.text, 1, '150px')
                        .push('corp_description', i18n.documentos_documentoshandle_UnidadesOrg, columnTypes.text, 1, '150px')
                    ;
                    gridReadersDialog = new gridComponent(gcUtil.basic({
                        id: "grid_readers_to_add",
                        searchContainerIsHidden: false,
                        container: "datagrid_readers_to_add",
                        size: dom.byId('floatingGridSize').value,
                        serviceStore: "../DPMS/ReadersByDocument.action?currentEntityId=" + global.intDocumentoId,
                        methodName: "getRowsReadersSearch",
                        noExcel: true,
                        freezeHeader: true,
                        onLoaded: function(grid) {
                            // El boton de agregar usuarios lecotres requiere que el status de las filas del grid flotante llegue en 0 para su correcta visualización
                            var data = grid.getBean().data;
                            array.forEach(data, function(row){
                                row.status = 0; 
                            });
                        },
                        fullColumns: columnsReadersDialog,
                        noRegMessage: i18n.documento_documentoshandle_NosehanagregadoLectores
                    }));

                    /*Columnas para lectores del documento*/
                    var
                    lst = [
                        {name: i18n.documentos_documentoshandle_Noleido, icon: 'unreaded.png', value: 0},
                        {name: i18n.documentos_documentoshandle_Leidosinpendiente, icon: 'readed.png', value: 1},
                        {name: i18n.documentos_documentoshandle_Leidodesdependientes, icon: 'readed_by_pendant.png', value: 2},
                        {name: i18n.documentos_documentoshandle_Cancelled, icon: 'trash.png', value: -1}
                    ];
                    var status = {
                        type: columnTypes.imageMap,
                        id: 'status',
                        title: i18n.documentos_documentoshandle_Estatus,
                        key: 'icon',
                        name: 'name',
                        value: 'value',
                        list: lst,
                        searchObj: {type: 'combo', col: 1, list: lst}
                    };
                    var user = {
                        type: columnTypes.text,
                        id: 'description',
                        title: i18n.user,
                        searchObj: {type: 'texto', col: 1}
                    };
                    var date = {
                        type: columnTypes.date,
                        id: 'readedDate',
                        title: i18n.documentos_documentoshandle_Fechadelectura
                    };
                    var edit = {
                        type: columnTypes['function'],
                        id: 'edit',
                        icon: gridIcons.remove,
                        title: i18n.documentos_documentoshandle_QuitarLector,
                        parameters: ['id'],
                        action: function(id) {
                            gridReadersBase.popRowById(id);
                            gridReadersBase.updateDataInfo();
                            delete_reader(id.usrId);
                        }
                    };
                    var columnsReadersGround = !editReaders ? [status, user, date] : [status, edit, user, date];
                    gcUtil.column(columnsReadersGround)
                      .push('businessUnitDepartmentName', i18n.documentos_documentoshandle_Department);
                    gridReadersBase = new gridComponent({
                        genericGridIsSortable: false,
                        size: 0,
                        id: "grid_readers_added",
                        container: "datagrid_readers_added",
                        resultsInfo: "#auto",
                        serviceStore: "../DPMS/ReadersByDocument.action?currentEntityId=" + global.intDocumentoId,
                        methodName: "getRowsReadersSaved",
                        noEyes: true,
                        noExcel: true,
                        noPagination: true,
                        noSettings: true,
                        refreshOnPopRow: false,
                        columns: columnsReadersGround
                    });
                    aspect.before(gridReadersBase, 'pushRow', lang.hitch(gridReadersBase, function(row) {
                        if (row.une_description) {
                            row.businessUnitName = row.une_description || '-';
                        }
                        if (row.department_description) {
                            row.businessUnitDepartmentName = row.department_description || '-';
                        }
                    }));
                    gridReadersBase.linkGrid(gridReadersDialog, {
                        base: 'id.usrId',
                        linked: 'id',
                        isComposite: false
                    });

                    var addAllReadersBtn = dom.byId('add_all_readers_btn');
                    var showReadersBtn = dom.byId('readersBtn');
                    var showReadersDialogBtn = dom.byId('add_readers_btn');
                    var closeReadersBtn = dom.byId('close_readers_btn');
                    
                    on(addAllReadersBtn, 'click', add_all_readers);
                    on(showReadersBtn, 'click', toggle_readers);
                    on(closeReadersBtn, 'click', close_readers);
                    
                    if (editReaders && showReadersDialogBtn) {
                        on(showReadersDialogBtn, 'click', show_readers_dialog);
                    } else if (showReadersDialogBtn) {
                        domStyle.set(showReadersDialogBtn, 'display', 'none');
                    }
                    function show_readers_dialog() {
                            core.fixDialogTop('readers_dialog');
                            registry.byId('readers_dialog').set('title', i18n.documentos_documentoshandle_AgregarLectores);
                            registry.byId('readers_dialog').set('style', {top: '50px'});
                            doc.body.style.overflow = 'hidden';
                            registry.byId('readers_dialog').show();
                            newReaders = [];
                        gridReadersDialog.newSearch();
                    }
                    function close_readers() {
                         registry.byId('readers_dialog').hide();

                        doc.body.style.overflow ='auto';
                        if (newReaders.length === 0) {
                            return;
                        }
                        core.showLoader(core.i18n.Validate.saving).then(function() {
                            callMethod({
                                url: '../DPMS/Document.action',
                                method: 'saveReaders',
                                params: [newReaders, global.intDocumentoId]
                            }).then(function(result) {
                                if (+result === -1) {
                                    core.dialog(i18n.label_mailDisabled);
                                } else {
                                    core.dialog(i18n.label_mailEnabled.replace('{}', newReaders.length));
                                }
                                gridReadersBase.setPageSize(0);
                                core.hideLoader();
                            }, core.hideLoader); 
                        });
                    }
                    function add_all_readers() {
                        var gridGround = gridReadersBase;
                        var gridDialog = gridReadersDialog;
                        core.confirm(
                            lang.replace(i18n.add_all_users_confirm, {
                                userCount: gridDialog.getBean().count
                            })
                        ).then(function(){
                            gridGround.waiting();
                            gridDialog.refreshData(true).then(function () {
                                gridDialog.getBean().data.forEach(function(doc) {
                                    newReaders.push(doc.id);
                                });
                                close_readers();
                            });
                        });
                    }
                    dom.byId('readers_label').style.display = '';
                }
            }

            if(global.positionForControlledCopyOn) {
                /* Todos los puestos lectores */
                var columnsPositionsToAdd = [], cType = gcUtil.cType;
                var columnsPositionsAdded = [], cType = gcUtil.cType;
                gcUtil.column(columnsPositionsToAdd)
                    .push('add', i18n.add, cType.Function(['id'], push, gridIcons.append), null, '50px');
                gcUtil.column(columnsPositionsAdded)
                    .push('remove', i18n.remove, cType.Function(['id'], pop, gridIcons.remove), null, '50px');
                gcUtil.column(columnsPositionsToAdd, columnsPositionsAdded)
                    .push('code', i18n.documentos_documentoshandle_Clave2, columnTypes.text, 1, '120px')
                    .push('description', i18n.position, columnTypes.text, 1, false)
                    .push('une.description', facilityI18n.colNamefacility, null, null, null, null, null, false);
                                   
                grid_positions_to_add = new gridComponent({
                    size: grid_floating_size,
                    id: "grid_positions_to_add",
                    container: "datagrid_position_to_add",
                    searchContainer: "#auto",
                    resultsInfo: "#auto",
                    paginationInfo: "#auto",
                    serviceStore: "../DPMS/Documents.Position.action?currentEntityId=" + global.intDocumentoId,
                    methodName: "getRowsPosition",
                    noEyes: true,
                    noPagination: true,
                    noExcel: true,
                    freezeHeader: true,
                    autoStatusSearch: false,
                    columns: columnsPositionsToAdd
                });
                if (window.documentoshandle.positionForControlledCopyOn) {

                    /* Puestos lectores agregados */
                    grid_positions_added = new gridComponent({
                        genericGridIsSortable: false,
                        size: 0,
                        id: "grid_positions_added",
                        container: "datagrid_position_added",
                        resultsInfo: "#auto",
                        serviceStore: "../DPMS/PhysicalReader.action?currentEntityId=" + global.intDocumentoId,
                        methodName: "getRows",
                        noRegMessage: global.grid_positions_added_noRegMessage,
                        noEyes: true,
                        noPagination: true,
                        noExcel: true,
                        noSettings: true,
                        refreshOnPopRow: false,
                        columns: columnsPositionsAdded
                    });
                    grid_positions_added.linkGrid(grid_positions_to_add);
                    grid_positions_added.updateDataInfo();
                    on(dom.byId('add_positions_btn'), 'click', setPositions);
                    on(dom.byId('close_positions_btn'), 'click', function() {
                        grid_positions_added.refreshData();
                        registry.byId('positions_dialog').hide();
                        doc.body.style.overflow ='auto';
                    });
                }
                on(dom.byId('show_positions_btn'), 'click', toggle_positions);
            }
            if (dom.byId('docsbtn')) {
                on(dom.byId('docsbtn'), 'click', toggle_related_documents);
            }
            if (core.getExternalFileIntegration() === true) {
                require(['bnext/third-party-app/google-drive-exporter'], function(googleDriveExporter) {
                    googleDriveExporter.then(function (exporter) {
                        if (exporter && typeof exporter.render === 'function') {
                            var url = '../view/v-download-document.view?id=' + dom.byId('documentId').value;
                            exporter.render(dom.byId('saveToDrive'), url, dom.byId('documentName').value, 'Bnext QMS');
                        }
                    });
                });
            } else if(dom.byId('save-to-drive-li')) {
                domClass.add('save-to-drive-li', 'displayNone');
            }
        }
        
        var configMenuItems = [];
        
        if (dom.byId('hasRetention').value === '1') {
            GridMenuUtil.menu(configMenuItems, DocumentMenuItem)
                    .push(DocumentMenuItem.CHANGE_RETENTION, changeRetention, true);
        }
        
        if (dom.byId('hasDisposition').value === '1') {
            GridMenuUtil.menu(configMenuItems, DocumentMenuItem)
                    .push(DocumentMenuItem.CHANGE_DISPOSITION, changeDisposition, true);
        }
        if (configMenuItems && configMenuItems.length > 0 && editRetention) {
            var configMenu = new GridMenu({
                id: 'configMenu',
                type: DocumentMenuItem,
                leftClickToOpen: true
            });
            domClass.remove(dom.byId('menu'), 'displayNone');
            configMenu.build(dom.byId('moreOptions'), configMenuItems);
        }
        query('input[disabled]').forEach(function(doom) {
            domClass.add(doom, 'information');
        });
        //se carga de valores dinamicos al despuest de settear el objeto Request cargado en SOL
        DynamicFieldUtil.load(global.intDocumentoId, 'DocumentType.Document.Custom.action', dom.byId('dynamicFields'), global.documentTypeId, {
            cacheOn: false,
            dijits: true,
            legacy: false,
            isQuery: true
        }, global.dynamicTableName).then(function(r) {
            for(var key in r.domMap) {
                if(!r.domMap.hasOwnProperty(key)) {
                    continue;
                }
                if(r.domMap[key].widjet) {
                    r.domMap[key].widjet.set('disabled', true);
                } else if(r.domMap[key].domNode) {
                    var doom = r.domMap[key].domNode;
                    domAttr.set(doom, 'disabled', true);
                }
            }
        });
        var PENDING_WHEN_BACK = {
            'v-attender-wrapper.view': true
        };
        var SELF_REFRENCE = {
            'v.documentos.view': true
        };
        function refreshParent() {
            if (window.opener) { //cuando la ventana fue abierta como popup, se busca al que la abrio y se refresca
                if (window.opener.progressWindow) {
                    window.opener.progressWindow.close();
                }
                window.close();
            } else {
                var referrerSource = document.referrer || '/';
                var idx = referrerSource.lastIndexOf('/') + 1;
                if (idx > 0) {
                    var referrer = referrerSource.substring(idx);
                    var idxQuery = referrer.indexOf('?');
                    if (idxQuery === -1) {
                        idxQuery = referrer.length;
                    }
                    var referrerName = referrer.substring(0, idxQuery);
                    if (PENDING_WHEN_BACK[referrerName]) {
                        angularNavigator.navigate('pendings');
                    } else if (SELF_REFRENCE[referrerName]) {
                        angularNavigator.navigate('pendings');
                    } else {
                        angularNavigator.navigateLegacy(referrer);
                    }
                } else {
                    angularNavigator.back();
                }
            }
        }
        function changeRetention() {
            angularDocumentOptions.retention(global.intDocumentoId, global.retentionType, global.retentionTime).then(
                function (result) {
                    if (result.text !== 'fail') {
                        angularNotice.notice(i18n.changeRetetntionSuccess);
                        global.grid.refreshData();
                        var label = query(".labelText", dom.byId('retencion'))[0];
                        label.innerHTML = result.text;
                        global.retentionType = result.retentionType;
                        global.retentionTime = result.retentionTime; 
                    }
                },
                function (error) {
                    core.dialog(error, core.i18n.Validate.accept);
                }
            );
        }
        function changeDisposition() {
            angularDocumentOptions.disposition(global.intDocumentoId, global.disposition).then(
                function (result) {
                    if (result.text !== 'fail') {
                        angularNotice.notice(i18n.changeDispositionSuccess);
                        global.grid.refreshData();
                        var label = query(".labelText", dom.byId('disposition'))[0];
                        label.innerHTML = result.text;
                        global.disposition = result.disposition;
                    }
                },
                function (error) {
                    core.dialog(error, core.i18n.Validate.accept);
                }
            );
        }
        on(dom.byId('Back'), 'click', refreshParent);
        var filePathBtn = dom.byId('filepath');
        on(filePathBtn, 'click', function() {
            core.navigateLegacy("v.document.list.ae.view?nodo=" + domAttr.get(filePathBtn, 'data-node-id') + '&documentId=' + dom.byId('documentId').value);
        });
    }
    window.openDocumentFinder = function(documentId, fileId, options) {
        var viewerUrl = 'v-document-viewer.view?id=' + documentId + '&simpleview=true';
        angularNavigator.showDocumentViewer(viewerUrl);
    };
    doEverything();
});