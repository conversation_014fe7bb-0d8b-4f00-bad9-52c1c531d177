define([
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dojo/Evented',
  'dijit/_WidgetsInTemplateMixin',
  'dojo/text!./templates/HierarchyLevelField.html',
  'bnext/_base/SelectWithSearch',
  'dojo/_base/lang',
  'xstyle/css!./styles/HierarchyLevelField.css'
], (declare, _WB, _TM, Evented, wt, tem, SelectWithSearch, lang) => {
  /**
   *  Se debe agregar en el JSP bnext.core.css
   * @type type
   */
  const HierarchyLevelField = {
    id: null,
    templateString: tem,
    required: true,
    column: null,
    order: null,
    level: null,
    label: null,
    levels: null,
    floatingGridSize: 10,
    searchServiceStore: null,
    searchMethodName: null,
    disabled: false,
    countData: null,
    _searchGridActivated: false,
    _pendingRefreshData: false,
    _value: null,
    _data: null,
    constructor: () => {},
    postCreate: function postCreate() {
      this._render();
    },
    _setValueAttr: function (value) {
      if (this._select) {
        this._select.set('value', value);
      }
      this._value = value;
    },
    _getValueAttr: function () {
      return this._value;
    },
    _setDataAttr: function (data) {
      if (this._select) {
        this._select.set('data', data);
      }
      this._data = data;
    },
    _getDataAttr: function () {
      return this._data;
    },
    _setSearchGridActivatedAttr: function (searchGridActivated) {
      if (this._select) {
        this._select.set('searchGridActivated', searchGridActivated);
      }
      this._searchGridActivated = searchGridActivated;
    },
    _getSearchGridActivatedAttr: function () {
      return this._searchGridActivated;
    },
    _setPendingRefreshDataAttr: function (pendingRefreshData) {
      if (this._select) {
        this._select.set('pendingRefreshData', pendingRefreshData);
      }
      this._pendingRefreshData = pendingRefreshData;
    },
    _getPendingRefreshDataAttr: function () {
      return this._pendingRefreshData;
    },
    _render: function () {
      if (!this._data) {
        return;
      }
      const searchServiceStore = (this.searchServiceStore || '').replace('{level}', `${this.level}`);
      this._select = new SelectWithSearch(
        {
          id: `${this.id}_filteringSelect`,
          label: this.label,
          disabled: this.disabled,
          required: this.required,
          idValueAttr: this.column,
          searchAttr: this.column,
          value: this._value,
          fields: this.levels,
          searchGridActivated: this.get('searchGridActivated'),
          pendingRefreshData: this.get('pendingRefreshData'),
          searchServiceStore: searchServiceStore,
          searchMethodName: this.searchMethodName,
          floatingGridSize: this.floatingGridSize,
          countData: this.countData,
          data: this._data || []
        },
        this.selectNode
      );
      this._select.on(
        'change',
        lang.hitch(this, function (event) {
          this._onSelectChange(event);
        })
      );
      this._select.on(
        'dataRefreshed',
        lang.hitch(this, function (data) {
          this._onDataRefreshed(data);
        })
      );
    },
    _hasValueChanged: function (value) {
      if (this._value && value) {
        return this._value[this.column] !== value[this.column];
      }
      if (this._value) {
        return this._value[this.column] !== null && typeof this._value[this.column] !== 'undefined';
      }
      if (value) {
        return value[this.column] !== null && typeof value[this.column] !== 'undefined';
      }
      return false;
    },
    _onSelectChange: function (event) {
      if (this._hasValueChanged(event.value)) {
        this._value = event.value;
        this.emit('change', {
          value: event.value,
          column: this.column,
          level: this.level,
          order: this.order
        });
      }
    },
    _onDataRefreshed: function (data) {
      this.emit('dataRefreshed', {
        data: data,
        column: this.column,
        level: this.level,
        order: this.order
      });
    },
    destroyRecursive: function () {
      if (this._select) {
        this._select.destroyRecursive();
      }
      // biome-ignore lint/style/noArguments: TODO: Fix this
      this.inherited(arguments);
    }
  };
  return declare([_WB, _TM, wt, Evented], HierarchyLevelField);
});
