define({
  verify: 'Verifying mapped variables ...',
  message: 'Remember this is a preview, therefore it cannot be edited nor see any advances.',
  messageArchived: 'Remember that this is a read-only preview and therefore will not be able to fill.',
  messageControl: 'It will be sent to the screen pending. Do you wish to continue?',
  noResultFound: 'No results found',
  loadingSpecificFields: 'Loading values of ":labelObj"...',
  loadingSpecificFieldsSuccesful: 'The values of ":labelObj" were successfully loaded',
  lastComment: 'Last comment written by :userName',
  genericCameraNotAvailable:
    'Camera not available, please allow access to the camera in the browser / check the settings of your browser and device. A page reload is required before trying again',
  cameraNotAvailableIPhone:
    'Camera not available, please allow access to the camera in the browser / check the privacy settings of your browser from Preferences / Privacy . A page reload is required before trying again',
  add: 'Add',
  delete: 'Delete',
  checkInStopwatchButtonTitle: 'Click to start the stopwatch',
  checkOutStopwatchButtonTitle: 'Click to stop the stopwatch',
  deleteStopwatchButtonTitle: 'Click to delete the record',
  deleteStopwatchConfirmMessage: 'Are you sure you want to delete the Timework record?',
  disabledDeleteStopwatchButtonTitle: 'The form does not allow deleting the record',
  deleteStopwatchErrorMessage: 'The Timework record cannot be deleted. Only the author of the record can delete it.',
  messageExit: 'Do you want to leave?<br>Any unsaved information will be lost.',
  impersonateMessage: 'You are filling this survey in the name of :userName',
  continueFillWithAdminRole: 'You can continue with the flow of this form because you are ADMIN',
  colNameDescription: 'Description',
  noFilesAdded: 'No files selected',
  noFilesAddedEmpty: 'Upload File',
  invalidMinDateWithTimeSelected: 'Date out of the allowed range. The minimum value is {minDate}.',
  scoreTitle: 'Score:',
  incidentTitle: 'Incidence:',
  defaultEmptyColumn: 'Column without information',
  dashedFileUpload: 'Drag and drop a file to upload, or click to select a file.'
});
