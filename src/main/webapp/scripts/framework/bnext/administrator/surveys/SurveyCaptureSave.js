define([
  'loader',
  'dojo/_base/lang',
  'dojo/_base/array',
  'dojo/query',
  'dojo/dom',
  'bnext/survey/_util/saveFunctions',
  'bnext/administrator/surveys/ConditionalFieldsUtils',
  'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.savehandle',
  'bnext/administrator/surveys/SurveyCaptureLoad',
  'bnext/administrator/surveys/OutstandingSurveysLoggingSave',
  'dojo/dom-class',
  'dojo/dom-attr',
  'dojo/Deferred'
], (loader, lang, array, query, dom, saveFunctions, ConditionalFieldsUtils, i18n, SurveyCaptureLoad, OutstandingSurveysLoggingSave, domClass, domAttr, Deferred) => {
  const debug = false;
  const w = window;

  /**
   * De acuerdo al tipo de pregunta, llena un objeto `outstandingQuestion` con la respuesta.
   * @param type
   * @param order
   * @param id
   * @returns {{field: {obj: {field_id}, id: number, type: string}, daysToExpire: (number|number), id: number, daysToNotifyBeforeExpiration: (number|number), order: number}}
   */
  function handleAnswerType(type, order, id) {
    const outter = dom.byId(id);
    const rOrder = query('.currentNo', outter)[0].innerHTML;
    const daysToExpire = dom.byId(id).getAttribute('data-conditional-expiration-daysToExpire');
    const daysToNotifyBeforeExpiration = dom.byId(id).getAttribute('data-conditional-expiration-daysToNotifyBeforeExpiration');
    const outstandingQuestion = {
      id: +dom.byId(`${id}_outstanding`).value,
      field: {
        id: +dom.byId(`${id}_id`).value,
        obj: {
          field_id: dom.byId(`${id}_code`).value
        },
        type: type || 'failure'
      },
      daysToExpire: daysToExpire ? +daysToExpire : 0,
      daysToNotifyBeforeExpiration: daysToNotifyBeforeExpiration ? +daysToNotifyBeforeExpiration : 0,
      order: +rOrder.replace(/[^0-9]+/, '').replace(/[^0-9]+/, '')
    };
    const containerAns = ConditionalFieldsUtils.getAnswerContainerDom(outter);
    const respuesta = saveFunctions.getOutstandingAnswer(type, order, containerAns, id);
    lang.setObject('respuesta', respuesta, outstandingQuestion);
    return outstandingQuestion;
  }
  function collectData(estatus, saveAction) {
    if (debug) {
      console.log('--- collectData ');
    }
    let progressStateId = +dom.byId('progressStateId').value;
    if (progressStateId === 0 || Number.isNaN(progressStateId)) {
      progressStateId = null;
    }
    const surveyRequestModeDom = dom.byId('requestMode') || { value: '' };
    const surveyRequestMode = surveyRequestModeDom.value;
    const surveyId = +dom.byId('surveyid').value;
    const code = dom.byId('outstandingCode').value;
    const outstandingSurveyId = +dom.byId('outstandingid').value;
    const data = {
      id: outstandingSurveyId,
      code: code,
      saveAction: saveAction ?? OutstandingSurveysLoggingSave.UNKNOWN_SAVE,
      outstandingSurveyId: outstandingSurveyId,
      requestId: +(dom.byId('requestId').value || 0),
      documentId: +(dom.byId('documentId').value || 0),
      businessUnitDepartmentId: +(dom.byId('businessUnitDepartmentId').value || 0),
      progressStateId: progressStateId,
      surveyId: surveyId,
      cuestionario: {
        id: surveyId
      },
      editedOutstandingidTitle: dom.byId('editedOutstandingidTitleId') ? dom.byId('editedOutstandingidTitleId').value : null,
      deleted: 0,
      surveyRequestMode: surveyRequestMode,
      conditionalValidatorCacheId: dom.byId('conditionalValidatorCacheId').value
    };
    if (Number.isNaN(data.requestId) || data.requestId === 0) {
      data.requestId = null;
    }
    if (Number.isNaN(data.documentId) || data.documentId === 0) {
      data.documentId = null;
    }
    if (Number.isNaN(data.businessUnitDepartmentId) || data.businessUnitDepartmentId === 0) {
      data.businessUnitDepartmentId = null;
    }
    if (SurveyCaptureLoad.nextUserSelector?.getSelectedUser()) {
      data.nextUserToFill = SurveyCaptureLoad.nextUserSelector.getSelectedUser().userId;
    }
    lang.setObject('estatus', estatus || 3, data);
    const preguntasRespondidas = [];
    const q = query('.form .outter');
    if (debug) {
      console.log(`--- questions\'s : ${q.length}`);
    }
    // Se obtienen todas las respuestas a guardarse
    array.forEach(q, (pregunta) => {
      loadAnswerQuestion(pregunta.id, preguntasRespondidas);
    });
    lang.setObject('preguntasRespondidas', preguntasRespondidas, data);
    if (dom.byId('currentAutorizationPoolIndex').value) {
      //parametros enviados a un callMethod
      return [data, dom.byId('currentAutorizationPoolIndex').value || 1];
    }
    return [data];
  }

  /**
   * Recorre el DOM y llena la variable `preguntasRespondidas` para enviarla a guardar.
   * @param questionId
   * @param preguntasRespondidas
   * @returns {{field: {obj: {field_id}, id: number, type: string}, daysToExpire: (number|number), id: number, daysToNotifyBeforeExpiration: (number|number), order: number}[]|Array<OutstandingAnswer>|*|null}
   */
  function loadAnswerQuestion(questionId, preguntasRespondidas) {
    const type = (dom.byId(`${questionId}_type`) || { value: 'invalid' }).value;
    if (type !== 'freeText' && type !== 'invalid' && type !== 'pageBreak') {
      const order = dom.byId(`${questionId}_order`).value;
      const question = handleAnswerType(type, order, questionId);
      const container = dom.byId(questionId);
      let filledAutorizationPoolIndex = null;
      if (domClass.contains(container, 'available-field')) {
        if (domAttr.get(container, 'data-filled-autorization-pool-index') && domAttr.get(container, 'data-filled-autorization-pool-index') !== 'null') {
          filledAutorizationPoolIndex = domAttr.get(container, 'data-filled-autorization-pool-index') || 1;
        } else {
          filledAutorizationPoolIndex = +(dom.byId('currentAutorizationPoolIndex').value || 1);
        }
      } else if (domAttr.get(container, 'data-filled-autorization-pool-index') && domAttr.get(container, 'data-filled-autorization-pool-index') !== 'null') {
        //si el campo esta deshabilidado para llenarse debe dejar lo que ya tenia
        filledAutorizationPoolIndex = domAttr.get(container, 'data-filled-autorization-pool-index');
      } else if (domAttr.get(container, 'data-fill-authorization-pool-index') && domAttr.get(container, 'data-fill-authorization-pool-index') !== 'null') {
        filledAutorizationPoolIndex = domAttr.get(container, 'data-fill-authorization-pool-index');
      }
      question.filledAutorizationPoolIndex = filledAutorizationPoolIndex;
      if (type === 'fieldArray') {
        array.forEach(question.respuesta, (r) => {
          r.filledAutorizationPoolIndex = filledAutorizationPoolIndex;
        });
        preguntasRespondidas.push.apply(preguntasRespondidas, question.respuesta);
        return question.respuesta;
      }
      question.score = {
        id: +(
          dom.byId(`select_${questionId}`) || {
            value: 0
          }
        ).value
      };
      preguntasRespondidas.push(question);
      return [question];
    }
    if (type === 'invalid') {
      console.warn(`La pregunta ${questionId} no tiene type`);
      return null;
    }
  }
  function guardadoParcial() {
    const def = new Deferred();
    if (dom.byId('justLoad').value === 'true') {
      def.reject();
      return;
    }
    if (w.misDatos.busy) {
      console.warn(i18n.alreadySaving);
      require(['core'], (core) => {
        core.warn(i18n.alreadySaving);
        def.reject(i18n.alreadySaving);
      });
      return;
    }
    require(['bnext/administrator/surveys/SurveyCaptureValidation'], (SurveyCaptureValidation) => {
      let isValid = false;
      if (dom.byId('editedOutstandingidTitleId')) {
        isValid = !SurveyCaptureValidation.existFieldsWithClass('parcialError') && dom.byId('editedOutstandingidTitleId')?.value.trim().length > 0;
      } else {
        isValid = !SurveyCaptureValidation.existFieldsWithClass('parcialError');
      }
      if (isValid) {
        if (w.misDatos) {
          loader.showLoader(i18n.savingLoader).then(() => {
            const data = collectData(4, OutstandingSurveysLoggingSave.PARTIAL_SAVE);
            w.misDatos.setData(data);
            if (debug) {
              console.log(data.preguntasRespondidas);
            }
            if (w.misDatos.busy) {
              console.warn(i18n.alreadySaving);
              def.reject(i18n.alreadySaving);
              return;
            }
            selectProgressStateId().then(
              (result) => {
                const id = +result.savedId;
                const location = window.top.document.location;
                if (!Number.isNaN(id) && id > 0 && location.search.indexOf('outstandingSurveyId') === -1 && location.search.indexOf(`id=O${id}`) === -1) {
                  const newUrl = `${location.toString()}&outstandingSurveyId=${id}`;
                  window.top.history.pushState('object or string', 'Title', newUrl);
                }
                def.resolve(result);
              },
              (e) => {
                def.reject(e);
              }
            );
          });
        } else {
          require(['core'], (core) => {
            core.fracasoMsg(i18n.errorInitialize);
            def.reject(i18n.errorInitialize);
          });
        }
      } else {
        require(['core'], (core) => {
          const messagesFail = {
            pool: i18n.verifyAnswer,
            audit: i18n.verifyAnswer,
            request: i18n.verifyRequest
          };
          core.fracasoMsg(messagesFail[dom.byId('surveyType').value] || i18n.verifyFields);
          def.reject(messagesFail[dom.byId('surveyType').value] || i18n.verifyFields);
        });
      }
    });
    return def.promise;
  }
  function getPartialProgressStatusValues() {
    const partialProgressStatusesJSON = dom.byId('partialProgressStatusesJSON').value;
    if (typeof partialProgressStatusesJSON === 'string' && partialProgressStatusesJSON !== '' && partialProgressStatusesJSON !== '[]') {
      let partialProgressStatuses = null;
      try {
        partialProgressStatuses = JSON.parse(partialProgressStatusesJSON);
      } catch (e) {
        console.error('Invalid partial progress statuses', partialProgressStatusesJSON);
        return [];
      }
      if (partialProgressStatuses && partialProgressStatuses.length > 0) {
        return partialProgressStatuses;
      }
      return [];
    }
    return [];
  }
  function selectProgressStateId() {
    const def = new Deferred();
    const partialProgressStatuses = getPartialProgressStatusValues();
    if (partialProgressStatuses.length === 0 || partialProgressStatuses.length === 1) {
      w.misDatos.busy = true;
      if (partialProgressStatuses.length === 1) {
        w.misDatos.getData()[0].progressStateId = partialProgressStatuses[0].value;
      } else {
        w.misDatos.getData()[0].progressStateId = null;
      }
      w.misDatos.saveData(i18n.saveData).then(
        (result) => {
          def.resolve(result);
        },
        (e) => {
          def.reject(e);
        }
      );
      return def.promise;
    }
    require(['core'], (core) => {
      let options = '';
      const progressStateId = w.misDatos.getData()[0].progressStateId;
      for (const item of partialProgressStatuses) {
        const selected = item.value === progressStateId ? ' selected="selected" ' : '';
        options += `<option value="${item.value}"${selected}>${item.text} </option>`;
      }
      const statusesDialog = core.eventedDialog(
        `<div data-dbox-name="partial-progress-status-container" class="grid-container grid-floating-active"><div class="cell small-12"><div class="select-component"><select data-dbox-name="" id="partialProgressStatus" name="partialProgressStatus" class="String" >${options}</select><label for="partialProgressStatus">${i18n.progressStatus}</label></div></div></div>`,
        [core.i18n.save, core.i18n.cancel_label],
        i18n.selectProgressStatus
      );
      statusesDialog.then((dialog) => {
        dialog.event('clickBtn1', () => {
          const partialProgressStatus = dialog.dBoxFormDom.partialProgressStatus.value;
          if (partialProgressStatus !== '' && partialProgressStatus !== null && typeof partialProgressStatus !== 'undefined') {
            w.misDatos.getData()[0].progressStateId = +partialProgressStatus;
            dom.byId('progressStateId').value = partialProgressStatus;
            w.misDatos.busy = true;
            w.misDatos.saveData(i18n.saveData).then(
              (result) => {
                def.resolve(result);
              },
              (e) => {
                def.reject(e);
              }
            );
            dialog.hide();
          } else {
            console.warn(i18n.missingProgressStatus);
            def.reject(i18n.missingProgressStatus);
          }
        });
        dialog.event('clickBtn2', () => {
          dialog.hide();
          def.reject();
        });
      });
    });
    return def.promise;
  }
  // noinspection UnnecessaryLocalVariableJS
  const SurveyCaptureSave = {
    collectData: collectData,
    loadAnswerQuestion: loadAnswerQuestion,
    guardadoParcial: guardadoParcial
  };
  return SurveyCaptureSave;
});
