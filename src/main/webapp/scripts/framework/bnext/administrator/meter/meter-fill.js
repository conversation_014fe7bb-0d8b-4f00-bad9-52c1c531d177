require(['core', 'dojo/dom', 'dojo/on', 'bnext/callMethod', 'bnext/filesAttacher/core', 'bnext/survey/_util/jsFailureExposure!', 'dojo/domReady!'],
function (core, dom, on, callMethod, FilesAttacher) {
    function doEverything(i18n) {
        var 
            id = +dom.byId('intCalificacionId').value,
            status = +dom.byId('status').value
        ;
        new FilesAttacher({
            serviceStore: 'MeterService.action',
            currentEntity: id,
            methodName: 'getMeterFillAttachments',
            showLoader: core.showLoader,
            hideLoader: core.hideLoader,
            hasUpload: status < 4 && dom.byId('delete').value !== "1",
            hasDelete: false,
            uploadPath: 'Upload.fileUploader',
            gridSize: -1,
            onFileAdded: function(fileId) {
                core.showLoader('Agregando archivo...');
                callMethod({
                    url:"MeterService.action",
                    method:"addMeterFillAttachment",
                    params:[id, fileId]
                }).then(core.hideLoader, core.hideLoader);
            },
            onFileRemoved: function(fileId) {
                core.showLoader('Removiendo archivo...');
                callMethod({
                    url:"MeterService.action",
                    method:"removeMeterFillAttachment",
                    params:[id, fileId]
                }).then(core.hideLoader, core.hideLoader);
            }
        }, 'fileAttacher');
        function save(){
            if ($("#mainDiv").valid()) {
                parameters();
            }
        }
        function validaNumeros(){
            if ((event.keyCode < 48 || event.keyCode > 57) && event.keyCode != 46 && event.keyCode != 43 && event.keyCode != 45){
                event.returnValue = false;
                event.preventDefault();
            }
        }
        function cancel(){
            if(dom.byId('delete').value === "1"){
                window.location.href = '../view/v.indicador.thrash.action';
            }else{
                dialog(i18n.cancelMeter, core.i18n.Validate.yes, core.i18n.Validate.no).then(
                    function(){
                        window.location.href = '../view/v.registry.list.view';
                    }
                );
            }
        }
        function setValidation() {
            $.validator.addMethod(
                'regexNumber',
                function (value, element, regexp) {
                    var re = new RegExp(regexp);
                    return this.optional(element) || re.test(value);
                },
                i18n.invalid_required.toString().replace('%specify%', 2)
            );
            $('#mainDiv').validate({
                rules: {
                    intResultado: {
                        required: true
                    }
                },
                messages: {
                    intResultado: {
                        required: core.i18n.Validate.missingField
                    }
                }
            });
            $(dom.byId('intResultado')).rules("add", {regexNumber: "^(-|\\+)?[0-9]+([.][0-9]*)?$"});
        };
        setValidation();
        on(dom.byId('intResultado'), 'keypress', validaNumeros);
        if(dom.byId('instAdd')){
            on(dom.byId('instAdd'), 'click', save);
        }
        on(dom.byId('back'), 'click', cancel);
        core.hideLoader();
    }
    core.setLang('bnext/administrator/meter/nls/meter-fill').then(doEverything, core.failure);
});