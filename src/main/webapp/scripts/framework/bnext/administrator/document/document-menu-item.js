define([
    'bnext/i18n!./nls/document-menu-item',
    'bnext/survey/_util/jsFailureExposure!'
],
function(i18n) {
    var DocumentMenuItem = {
        RE_APPROVE_DOCUMENT: {id: 'reApproveDocument'},
        MODIFY_DOCUMENT: {id: 'modifyDocument'},
        DETAIL_DOCUMENT: {id: 'detailDocument'},
        CANCEL_DOCUMENT: {id: 'cancelDocument'},
        SHARE_DOCUMENT: {id: 'shareDocument'},
        OPEN_DOCUMENT: {id: 'openDocument'},
        DOWNLOAD_DOCUMENT: {id: 'downloadDocument'},
        MOVE_DOCUMENT: {id: 'moveDocument'},
        FILL_FORM: {id: 'fillForm'},
        VIEW_FORM: {id: 'viewForm'},
        DELETE_FOLDER: {id: 'deleteFolder'},
        CONFIGURE_FOLDER: {id: 'configureFolder'},
        LINK_FOLDER: {id: 'linkFolder'},
        MOVE_FOLDER: {id: 'moveFolder'},
        GOOGLE_DRIVE_EXPORT: {id: 'googleDriveExport'},
        CHANGE_RETENTION: {id: 'changeRetention'},
        CHANGE_DISPOSITION: {id: 'changeDisposition'}
    };
    DocumentMenuItem.contains = function(item) {
        for (var key in DocumentMenuItem) {
            if (DocumentMenuItem.hasOwnProperty(key)) {
                if (DocumentMenuItem[item] === item) {
                    return true;
                }
            }
        }
        return false;
    };
    DocumentMenuItem.getLabel = function(id, multipleSelection) {
        var tag;
        if (multipleSelection === true) {
            tag = 'label_multiple_' + id;
        } else {
            tag = 'label_' + id;
        }
        return i18n[tag] || tag;
    };
    DocumentMenuItem.getTitle = function(id, multipleSelection) {
        var tag;
        if (multipleSelection === true) {
            tag = 'title_multiple_' + id;
        } else {
            tag = 'title_' + id;
        }
        return i18n[tag] || tag;
    };
    DocumentMenuItem.getIconName = function(id) {
        switch(id) {
            case 'reApproveDocument': return 'event';
            case 'modifyDocument': return 'edit';
            case 'detailDocument': return 'list_alt';
            case 'cancelDocument': return 'clear';
            case 'shareDocument': return 'share';
            case 'openDocument': return 'open_in_new';
            case 'downloadDocument': return 'vertical_align_bottom';
            case 'moveDocument': return 'library_books';
            case 'fillForm': return 'file_copy';
            case 'viewForm': return 'book';
            case 'deleteFolder': return 'delete';
            case 'configureFolder': return 'settings';
            case 'linkFolder': return 'folder_shared';
            case 'moveFolder': return 'folder';
            case 'googleDriveExport': return 'insert_drive_file';
            case 'changeRetention': return 'today';
            case 'changeDisposition': return 'file_copy';
        }
        return 'done';
    };
    return DocumentMenuItem;
});