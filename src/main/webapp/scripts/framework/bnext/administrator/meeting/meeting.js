/* global $locale */
require([ 
    "dojo/date/stamp",
    'core', 
    'dojo/dom', 
    'dojo/dom-attr', 
    'dojo/on', 
    'dojo/query', 
    'dojo/_base/lang', 
    'dojo/dom-attr', 
    "dojo/dom-class", 
    'dojo/dom-construct',
    'dijit/form/DateTextBox', 
    "dijit/form/TimeTextBox",
    'bnext/callMethod', 
    'bnext/module/data/meeting-status', 
    'bnext/periodicityPicker/core', 
    'bnext/filesAttacher/core',
    'bnext/administrator/activity/activity-grid',
    'bnext/gridComponentUtil', 
    'bnext/LinkedGridFactory', 
    'bnext/gridComponent', 
    'bnext/gridIcons',
    'bnext/formToFill/formToFill',
    'bnext/periodicityPicker/PeriodicityUtil', 
    'dojo/json',
    'bnext/administrator/meeting/meeting-grid',
    'bnext/DynamicFieldUtil',
    'bnext/FieldListUtil',
    'bnext/misc',
    'bnext/ckeditor',
    'dojo/Deferred',
    'jquery-linedtextarea',
    'xstyle/css!jquery-linedtextarea.css',
    'bnext/survey/_util/jsFailureExposure!'
],function (
        stamp, core, dom, domAttr, on, query, lang, domAttr, domClass, domConstruct, //Base Requires
        DateTextBox, TimeTextBox, //Dijit
        callMethod, meeting_status, periodicityPicker, fileAttacher, //Bnext Component Framework
        ActivityGrid, //Activity Framework
        gcUtil, LinkedGridFactory, gridComponent, gridIcons, //GridComponent Framework
        formToFill, PeriodicityUtil, json, MeetingGrid,
        DynamicFieldUtil,
        FieldListUtil,
        BnextMisc,
        ckeditor,
        Deferred
) {
    var MAIL_REGEX = /^(([a-z\sA-Z]+<)([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+>)|(([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+)$/;
    function doEverything(i18n) {
        var i18n;
        var cType = gcUtil.cType;
        var startOnDate, startOnTime, finishOnDate, finishOnTime, startOn, finishOn;
        //components
        var pp = {
            validate: function() {
                return true;
            }
        };
        var file_attacher;
        //comments
        var commentDialog;
        var commentGrid;
        //participants            
        var lgParticipants;
        //common var
        var id = dom.byId('id').value;
        var recurrent = dom.byId('recurrent').value === '1';
        var deleted = dom.byId('deleted').value === '1';
        var status = dom.byId('status').value;
        var isAdmin = dom.byId('isAdmin') ? true : false;
        var isManager = dom.byId('isManager') ? true : false;
        var isReader = dom.byId('isReader') ? true : false;
        var showPeriodicity = dom.byId('showPeriodicity');
        var typeIdDom = dom.byId('typeId');
        var isNew = +id <= 0;
        var isHost = false;
        var areParticipantsLocked = false;
        var allowToUploadFiles = false;
        var allowToDeleteFiles = false;
        var allowToAddComments = false;
        var allowToRemoveComments = false;
        var minuteWidget = null;
        var FIFTEEN = 15 * 60000;
        var bnextMisc = new BnextMisc();
        var control = {
            refresh: function (g) {
                control[g.id] = g;
                g.setCurrentPage(0);
                g.refreshData();
                core.hideLoader();
            }
        };
        var disabled = +status === meeting_status.CLOSED || +status === meeting_status.CANCELLED || (+status >= meeting_status.READY && status && !isAdmin);
        var dynamicFields = dom.byId('dynamicFields');
        var fieldListUtil = new FieldListUtil();
        var txtCodeDom = dom.byId('txtCode');
        function isValidDate(date) {
            var result = {
                date: date,
                valid: true
            };
            if (isNaN(date.getTime())) {
                result.date = new Date();
                result.valid = false;
            }
            return result;
        }
        function getTime(obj, endRange) {
            var mm = obj.date.getMinutes();
            var delta = 0;
            if (!obj.valid) {
                delta = 15 - (mm % 15);
                if (endRange) {
                    delta = delta + 15;
                }
            }
            mm = new Date(obj.date.setMinutes(mm + delta)).getMinutes();
            mm = core.pad(mm);
            var HH = obj.date.getHours();
            HH = core.pad(HH);

            return  HH + ':' + mm + ':00';
        }
        function dijitsToTime(_date, _time) {
            var month = core.pad(_date.getMonth() + 1);
            var day = core.pad(_date.getDate());
            var hour = core.pad(_time.getHours());
            var min = core.pad(_time.getMinutes());
            month = (month).substr((month).length - 2);
            day = (day).substr((day).length - 2);
            var time = _date.getFullYear() + '-' + month + '-' + day + 'T' + hour + ':' + min + ":00";
            return time;
        }
        function toDate(start) {
            var year = parseInt(start.substring(0, 4));
            var month = parseInt(start.substring(5, 7)) - 1;
            var day = parseInt(start.substring(8, 10));
            var hour = parseInt(start.substring(11, 13));
            var min = parseInt(start.substring(14, 16));
            return isValidDate(new Date(year, month, day, hour, min));
        }

        function adjustTime(base, config) {
            if (base === 'finishTime' || base === 'finishDate') {
                config.start = new Date(config.finish.getTime() - FIFTEEN);
            } else {
                config.finish = new Date(config.start.getTime() + FIFTEEN);
            }
        }

        function adjustDate(base, config) {
            if (base === 'finishTime' || base === 'finishDate') {
                config.start.setDate(config.finish.getDate());
                config.start.setMonth(config.finish.getMonth());
                config.start.setFullYear(config.finish.getFullYear());
            } else {
                config.finish.setDate(config.start.getDate());
                config.finish.setMonth(config.start.getMonth());
                config.finish.setFullYear(config.start.getFullYear());
            }
        }

        function adjustDateValues(result) {
            if (!result || !result.valid) {
                return;
            }
                var base = this.get('id');
                var startDate = startOnDate.get('value');
                var startTime = startOnTime.get('value');
                var finishDate = finishOnDate.get('value');
                var finishTime = finishOnTime.get('value');
                var config = {
                    start: new Date(startDate.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0)),
                    finish: new Date(finishDate.setHours(finishTime.getHours(), finishTime.getMinutes(), 0, 0))
                };
                if (config.start.getTime() < config.finish.getTime()) {
                    return;
                }
                if (bnextMisc.equalDate(startDate, finishDate) || bnextMisc.equalTime(startTime, finishTime)) {
                    adjustTime(base, config);
                } else {
                    adjustDate(base, config);
                    if (config.start.getTime() >= config.finish.getTime()) {
                        adjustTime(base, config);
                    }
                }

                if (!bnextMisc.equalTime(config.start, startTime)) {
                    startOnTime.set('value', config.start);
                }
                if (!bnextMisc.equalDate(config.start, startDate)) {
                    startOnDate.set('value', config.start);
                }
                if (!bnextMisc.equalTime(config.finish, finishTime)) {
                    finishOnTime.set('value', config.finish);
                }
                if (!bnextMisc.equalDate(config.finish, finishDate)) {
                    finishOnDate.set('value', config.finish);
                }

        }

        function initializeView() {
            initializeMessages();
            //initialize components
            $("#guest").linedtextarea();
            startOn = toDate(dom.byId('startOn').value);
            var time = getTime(startOn);
            domAttr.set("classified","value", dom.byId("classifiedValue").value);
            startOnDate = new DateTextBox({
                constraints: {
                    datePattern: 'dd/MM/yyyy',
                    min: startOn.date
                },
                value: startOn.date, 
                required: true,
                onChange: adjustDateValues
            }, dom.byId('startDate'));
            startOnTime = new TimeTextBox({
                constraints: {
                    datePattern: 'HH:mm:ss',
                    clickableIncrement: 'T00:15:00', 
                    visibleIncrement: 'T00:15:00',
                    visibleRange: 'T01:00:00'
                },
                value: 'T' + time, 
                required: true,
                onChange: adjustDateValues
            }, dom.byId('startTime'));
            finishOn = toDate(dom.byId('finishOn').value);
            time = getTime(finishOn, true);
            finishOnDate = new DateTextBox({
                constraints: {
                    datePattern: 'dd/MM/yyyy',
                    min: startOn.date
                },
                value: finishOn.date,
                required: true,
                onChange: adjustDateValues
            }, dom.byId('finishDate'));
            finishOnTime = new TimeTextBox({
                constraints: {
                    datePattern: 'HH:mm:ss',
                    clickableIncrement: 'T00:15:00',
                    visibleIncrement: 'T00:15:00',
                    visibleRange: 'T01:00:00'
                },
                value: 'T' + time,
                required: true,
                onChange: adjustDateValues
            }, dom.byId('finishTime'));
            if (recurrent) {
                if (deleted) {
                    core.setTitle(i18n.recurrentViewTitle);
                } else {
                    core.setTitle(i18n.recurrentTitle);
                }
            }
        }
        function validateParticipants() {
            var id = lgParticipants.getGroundGrid().id;
            var info = dom.byId(id + '_gridInfo');
            if(!info){
               return true; 
            }
            if (lgParticipants.getGroundData().length === 0) {
                domClass.add(info, 'required-error');
                domConstruct.create('label', {
                    'class': 'error',
                    'for': id,
                    'innerHTML': core.i18n.Validate.missingField
                }, info, 'after');
                return false;
            } else {
                domClass.remove(info, 'required-error');
                query('label[for="'+ id + '"]').forEach(function(domLabel){
                    domConstruct.destroy(domLabel);
                });
            }
            return true;
        }

        function validateDates() {
            var valid = startOnDate.isValid() && finishOnDate.isValid() && startOnTime.isValid() && finishOnTime.isValid();
            if (!valid) {
                if (!startOnDate.isValid()) {
                    startOnDate.focus();
                } else if (!finishOnDate.isValid()) {
                    finishOnDate.focus();
                } else if (!startOnTime.isValid()) {
                    startOnTime.focus();
                } else if (!finishOnTime.isValid()) {
                    finishOnTime.focus();
                }
                core.dialog(i18n.invalidDate, i18n.accept);
                return false;
            }
            var startOn = dijitsToTime(startOnDate.get('value'), startOnTime.get('value'));
            var finishOn = dijitsToTime(finishOnDate.get('value'), finishOnTime.get('value'));
            valid = new Date(finishOn).getTime() > new Date(startOn).getTime();
            if(!valid){
                core.dialog(i18n.invalidDateRelErrorMessage,i18n.accept);
            }
            return valid;
        }

        function save(justUpdate) {
            var valid = validateEmail();
            var bk = {
                validate: function() {
                    return true;
                }
            };
            valid = $("#validate_form").valid() && valid;
            valid = valid  && validateDates() && valid;
            valid = validateParticipants() && valid;
            valid = (pp || bk).validate() && valid;
            if (!valid) {
                return;
            }
            ckeditor.instances.body.updateElement();
            var saveMethod = getSaveMethod(justUpdate);
            core.dialog(i18n[saveMethod] + '&nbsp;'  + i18n.areYouSureToContinue, i18n.yes, i18n.no).then(function () {
                DynamicFieldUtil.save('MeetingType-DynamicFields.action', dynamicFields, typeIdDom.value)
                        .then(function (dynamicFieldInsertDTO) {
                            if (!dynamicFieldInsertDTO.tableName) {
                                saveAction(justUpdate, {});
                            } else {
                                saveAction(justUpdate, dynamicFieldInsertDTO);
                            }
            });
            });
        }
        function toggleGenerateKey() {
            toggleGenerate('#chkGenerate', '#txtCode');
        }
        function onAcceptComment() {
            commentDialog.then(function(dialog) {
                var form = dialog.dBoxFormDom;
                var commentValue = form.comment.value.toString().trim();
                if (!commentValue) {
                    core.dialog(i18n.missingComment);
                    core.hideLoader();
                    return;
                }
                if (commentValue.length > 255) {
                    core.dialog(core.specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255));
                    return;
                }
                core.showLoader();
                callMethod({
                    url: "Meeting.action",
                    params: [commentValue, fillEntity()],
                    method: 'saveComment'
                }).then(onSaveCommentSuccess, onOperationFail);
            });

        }

        function deleteComment(id) {
            commentGrid.popRowById(id);
            var criteria = commentGrid.getExtraCriteria();
            if(criteria.length === 0){
                criteria[0] = {
                    key: 'id',
                    value: id + "<!>"
                };
            }else {
                criteria[0] = {
                    key: 'id',
                    value: id + "<!>" + criteria[0].value
                };
            }
            commentGrid.setExtraCriteria(criteria);
            commentGrid.updateDataInfo();
        }
        function onHideComment() {
            core.hideLoader();
            commentDialog.then(function(dialog) {
                dialog.hide();
            });
        }
        function onSaveCommentSuccess(successResult) {
            core.hideLoader();
            if (successResult.operationEstatus || +successResult.operationEstatus === 1 || successResult.operationEstatus === '1') {
                commentGrid.refreshData();
                commentDialog.then(function(dialog) {
                    dialog.hide();
                });
            } else {
                if (successResult.operationEstatus) {
                    core.dialog(successResult.errorMessage
                            || i18n.saveFail, core.i18n.accept);
                    console.error(successResult.errorMessage + ' operationStatus' + successResult.operationEstatus);
                } else {
                    core.dialog(successResult.message, core.i18n.accept);
                    console.error(successResult.message);
                }
            }
        }
        function onOperationFail(result) {
            core.hideLoader();
            core.error(i18n.saveFail, core.i18n.accept);
            console.error(result);
        }
        function addComment() {
            commentDialog = core.eventedDialog(''
                    + '<div data-dbox-name="commentContainer">'
                    + i18n.dialogNewCommentMessage
                    + '<textarea data-dbox-name="comment" style="float: none;"></textarea>'
                    + '</div>'
                    ,
                    [i18n.accept, i18n.cancel], i18n.dialogNewCommentTitle
                    );
            commentDialog.then(function(dialog) {
                dialog.event('clickBtn1', function(){
                    onAcceptComment();
                    dialog.hide();
                });
                dialog.event('clickBtn2', onHideComment);
            });
        }
        function initializeMessages() {
            $.validator.addMethod("date_required", function (value, element) {
                var RegExPattern = /^\d{1,2}\/\d{1,2}\/\d{2,4}$/,
                nameElement = element.id,
                dateDom = dom.byId(nameElement).value;
                if(nameElement === 'startOn'){
                    dateDom = dom.byId('startDate').value;
                }else if(nameElement === 'finishOn'){
                    dateDom = dom.byId('finishDate').value;
                }
                return  (dateDom.match(RegExPattern)) && (dateDom !== '');
            });
            $.validator.addMethod('cke_required', function (value, element) {
                    var idname = $(element).attr('id');
                    var editor = ckeditor.instances[idname];
                    $(element).val(editor.getData());
                    return $(element).val().length > 0;                        
                },
                core.i18n.Validate.missingField
            );
            dom.byId('statusText').innerHTML =  i18n[dom.byId('statusLabelKey').value] || i18n.status_invalid;
            $('#validate_form').validate({
                ignore: '',
                rules: {
                    code: {
                        required: true,
                        maxlength: 30
                    },
                    description: {
                        required: true,
                        maxlength: 255
                    },
                    location: {required: true},
                    host: {required: true},
                    type: {required: true},
                    classified: {required: true},
                    startOn: {date_required: true},
                    finishOn: {date_required: true}
                },
                messages: {
                    code: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 30),
                    description: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
                    location: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
                    host: core.i18n.Validate.missingCombo,
                    type: core.i18n.Validate.missingCombo,
                    classified: core.i18n.Validate.missingCombo,
                    startOn: {date_required: i18n.invalidDate},
                    finishOn: {date_required: i18n.invalidDate}
                },
                errorPlacement: function(error, element) {
                    var elemt = element ? element[0] : {};
                    if(elemt.tagName === "TEXTAREA" && domClass.contains(elemt, 'wysiwyg')){
                        error.insertAfter("#cke_"+elemt.id);
                        return;
                    }
                    error.insertAfter(element);                    
                }
            });
            var body = dom.byId('body');
            $(body).rules('add', {cke_required: ''});
        }
        function applyBusinessRules(loadEditor) {
            if (recurrent) {
                minuteWidget = {};
            } else {
                minuteWidget = formToFill({
                    control: i18n.minuteSection,
                    baseId:id, 
                    related: 'meetingId',
                    url: '../DPMS/FormToFill.action',
                    previewAction: 'v-meeting-minute-print.action',
                    fillAction:'../DPMS/v-meeting-survey-fill.action',
                    method: 'getMeetingData',
                    disabled: +status === meeting_status.CANCELLED
                },"minuteSection");
            }
            if (+id === -1) {
                //is a new meeting
                domClass.remove('cleanBtn', 'displayNone');
                domClass.add('updateBtn', 'displayNone');
                domClass.add('comments_section', 'displayNone');
                domClass.remove('selfGeneratable', 'displayNone');
                allowToUploadFiles = true;
                allowToDeleteFiles = true;
                allowToAddComments = true;
                allowToDeleteFiles = true;
                //periodicityPicker
                var paramDate =  new Date(
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(0,4)),
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(5,7)) - 1,
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(8,10)),
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(11,13)),
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(14,16)),
                        parseInt(stamp.toISOString(startOnDate.get('value')).substring(17,19)));
                pp = new periodicityPicker({
                    startDate: paramDate,
                    startTime: stamp.toISOString(paramDate).substring(10, 19),
                    showUserListCombo: false
                }, 'periodicityPickerContainer');
            } else {
                allowToUploadFiles = true;
                allowToAddComments = true;
                if(isAdmin || isManager || isHost) {
                    allowToDeleteFiles = true;
                    allowToRemoveComments = true;
                }
                switch (+status) {
                    case (meeting_status.REPORTED):
                    case (meeting_status.READY):
                    case (meeting_status.IN_PROCESS):
                    case (meeting_status.CONCLUDED):
                    case (meeting_status.CLOSED):
                    case (meeting_status.CANCELLED):
                        domClass.remove('activitySection', 'displayNone');
                    break;
                    case (meeting_status.RECURRING):
                    default:
                    break;
                }
                if (+status === meeting_status.REPORTED) {
                    domClass.add('cleanBtn', 'displayNone');
                    domClass.remove('updateBtn', 'displayNone');
                    if(!isAdmin && isReader){
                        disabledFields(loadEditor);
                        areParticipantsLocked = true;
                        domClass.add('saveBtn', 'displayNone');
                        domClass.add('updateBtn', 'displayNone');
                    }
                } else if (+status === meeting_status.RECURRING && !deleted) {
                    domClass.add('cleanBtn', 'displayNone');
                    //Se habilitan campos de fechas en registros recurrentes por peticion de MICARE.
                    startOnDate.set('disabled', false);
                    startOnTime.set('disabled', false);
                    finishOnDate.set('disabled', false);
                    finishOnTime.set('disabled', false);
                } else if (deleted || disabled || isReader) {
                    disabledFields(loadEditor);//solo el organizador y el encargado del módulo pueden editar datos
                }
                if (deleted || +status >= meeting_status.CONCLUDED && +status !== meeting_status.RECURRING) {
                    areParticipantsLocked = true;
                }
                if (deleted || +status === meeting_status.CANCELLED) {
                    domClass.add('saveBtn', 'displayNone');
                    areParticipantsLocked = true;
                    allowToUploadFiles = false;
                    allowToDeleteFiles = false;
                    allowToAddComments = false;
                    allowToRemoveComments = false;
                } else if (+status === meeting_status.CLOSED) {
                    allowToDeleteFiles = false;
                    allowToRemoveComments = false;
                }
                parsePeriodicityDescription();
            }
        }
        function disabledFields(loadEditor){
            domAttr.set('txtCode', 'disabled', 'disabled');
            domAttr.set('description', 'disabled', 'disabled');
            domAttr.set('location', 'disabled', 'disabled');
            domAttr.set('hostId', 'disabled', 'disabled');
            domAttr.set(typeIdDom, 'disabled', 'disabled');
            loadEditor.then(function() {
                ckeditor.instances.body.setReadOnly(true);
            });
            domAttr.set(typeIdDom, 'disabled', 'disabled');
            startOnDate.set('disabled', true);
            startOnTime.set('disabled', true);
            finishOnDate.set('disabled', true);
            finishOnTime.set('disabled', true);
            domAttr.set('classified', 'disabled', 'disabled');
            domClass.add('cleanBtn', 'displayNone');
            domAttr.set('guest', 'disabled', 'disabled');
            domClass.add('saveBtn', 'displayNone');
        }
        function parsePeriodicityDescription() {
            var jsonPeriodicity = dom.byId('jsonPeriodicity');
            if (!jsonPeriodicity) {
                return;
            }
            var periodicity = json.parse(jsonPeriodicity.value);
            dom.byId('periodicityContainer').innerHTML = PeriodicityUtil.generateDescriptionEvent(periodicity);
        }
        function fillEntity() {
            var data = {};
            query('[name]', 'validate_form').forEach(function (doom) {
                if (doom.value || doom.value === '0') {
                    if (domClass.contains(doom, "subItem")) {
                        lang.setObject(domAttr.get(doom, 'name') + ".id", doom.value, data);
                    } else {
                        lang.setObject(domAttr.get(doom, 'name'), doom.value, data);
                    }
                }
            });
            data["startOn"] = dijitsToTime(startOnDate.get('value'), startOnTime.get('value'));
            data["finishOn"] = dijitsToTime(finishOnDate.get('value'), finishOnTime.get('value'));
            if (recurrent) {
                data['outstandingSurveyId'] = null;
            } else {
                data['outstandingSurveyId'] = minuteWidget.form.recordId || null;
            }
            data.body = ckeditor.instances.body.getData();
            data.bodyPlain = ckeditor.instances.body.document.getBody().getText();
            var periodicityId = dom.byId('periodicityId').value;
            if (+periodicityId > 0) {
                data["periodicity"] = {id: periodicityId};
            }
            return data;
        }
        function getSaveMethod(justUpdate) {
            var hasMinute = !recurrent && !!minuteWidget.form.hasForm;
            var filledMinute = hasMinute && +minuteWidget.form.status === 7;
            var today = new Date();
            var method = 'updateMeeting';
            if (recurrent) {
                method = 'updateMeetingRecurrence';
            } else if (justUpdate) {
                method = 'updateMeeting';
            } else {
                var startOn = toDate(dijitsToTime(startOnDate.get('value'), startOnTime.get('value'))).date;
                var finishOn = toDate(dijitsToTime(finishOnDate.get('value'), finishOnTime.get('value'))).date;
                switch (+status) {
                    case meeting_status.REPORTED:
                        if (+id === -1) {
                            method = 'reportMeeting';
                        } else {
                            if (hasMinute) {
                                if (filledMinute) {
                                    method = 'concludeMeeting';
                                } else if (today > startOn
                                        && today < finishOn) {
                                    method = 'inProcessMeeting';
                                } else if (today < startOn) {
                                    method = 'readyMeeting';
                                }
                            } else if (today > startOn
                                    && today < finishOn) {
                                method = 'inProcessMeeting';
                            } else if (today < startOn) {
                                method = 'readyMeeting';
                            } else if (today > finishOn) {
                                method = 'concludeMeeting';
                            }
                        }
                        break;
                    case meeting_status.READY:
                        if (hasMinute) {
                            if (filledMinute) {
                                method = 'concludeMeeting';
                            } else if (today > startOn
                                    && today < finishOn) {
                                method = 'inProcessMeeting';
                            } else if (today < startOn) {
                                method = 'readyMeeting';
                            }
                        } else if (today > startOn
                                && today < finishOn) {
                            method = 'inProcessMeeting';
                        } else if (today < startOn) {
                            method = 'readyMeeting';
                        }
                        break;
                    case meeting_status.IN_PROCESS:
                        if (hasMinute) {
                            if (filledMinute) {
                                method = 'concludeMeeting';
                            }
                        } else {
                            method = 'concludeMeeting';
                        }
                        break;
                    case meeting_status.CONCLUDED:
                        if (hasMinute) {
                            if (filledMinute) {
                                method = 'closeMeeting';
                            }
                        } else {
                            method = 'closeMeeting';
                        }

                }
            }
            return method;
        }
        function saveAction(justUpdate, dynamicFieldInsertDTO) {
            core.showLoader();
            var data = fillEntity();
            var generateCode = dom.byId('chkGenerate').checked;
            if (generateCode) {
                data["code"] = '';
            }
            if (+id === -1) {
                data["periodicity"] = pp.getPeriodicity();
            }   
            data.dynamicFieldInsertDTO = dynamicFieldInsertDTO || null;
            data.dynamicTableName = dynamicFieldInsertDTO.tableName || null;
            saveMethod = getSaveMethod(justUpdate);
            var comments = [];
            if (commentGrid) {
                comments = core.parseAsIdList(commentGrid.getBean().data);
            }
            callMethod({
                url: 'Meeting.action',
                method: saveMethod,
                params: [data, {
                    files: core.parseAsIdList(file_attacher.grid.getBean().data),
                    comments: comments,
                    participants: core.parseAsIdList(lgParticipants.getGroundData())
                }]
            }).then(function (gsh) {
                core.hideLoader();
                if (!gsh.validCallback || gsh.jsonEntityData.error) {
                    return core.error(gsh);
                }
                core.dialog(i18n.successfullySaved, core.i18n.Validate.btnAdd, core.i18n.Validate.btnControl).then(
                    function () {
                        window.location = "../view/v-meeting.view";
                    },
                    function () {
                        if (recurrent) {
                            window.location = "../view/v-recurring-meeting-list.view";
                        } else {
                            window.location = "../view/v-meeting-list.view";
                        }
                    }
                );
            }, core.failure);
        }
        function onFirstParticipantLoaded() {
            if (isNew && lgParticipants) {
                lgParticipants.getGroundGrid().pushRow({ 
                    id: +dom.byId("loggedUserId").value,
                    code: dom.byId("loggedUserName").value,
                    description: ''
                });
                lgParticipants.getGroundGrid().updateDataInfo();
            }
        }
        function createParticipantGrid() {
            var participantsColums = [];
            gcUtil.column(participantsColums).push('code', i18n.participants_code, cType.Text());
            gcUtil.column(participantsColums).push('description', i18n.participants_name, cType.Text());
            lgParticipants = LinkedGridFactory.create({
                id: 'meeting_participants',
                addButton: dom.byId('addParticipants'),
                onAfterAdd: validateParticipants,
                onAfterRemove: validateParticipants,
                disabled: areParticipantsLocked
            }, id, 'Meeting-Participant.action', i18n, participantsColums);
            onFirstParticipantLoaded();
        }
        function createMeetingsGrid() {
            if (!recurrent) {
                return;
            }
            domClass.remove('meetingsSection', 'displayNone');
            var columns = [];
            gcUtil.column(columns)
                .push('status', i18n.colNamesStatus, cType.FunctionImage(['id', 'status'], null, meeting_status.getSerializedStatusList(), true));      
            var config = new MeetingGrid({}, columns);
            config.prepareColumns();
            var meetingGrid = new gridComponent({
                id: 'meetings',
                container: 'meetings',
                serviceStore: 'Meeting.action?currentEntityId='+id,
                windowPath: dom.byId('windowPath').value,
                columns: config.columns,
                methodName: 'getReurrenceRows',
                size: 0,
                freezeHeader: false,
                noEyes: true,
                genericGridIsSortable: false,
                refreshOnPopRow: false,
                noSettings: true,
                noExcel: true,
                noPagination: true,
                searchContainer: 'none'
            });
            meetingGrid.refreshCurrentPage();
        }
        function createCommentsGrid() {
            if (isNew) {
                return;
            }
            commentsColumns = [];
            if (allowToRemoveComments) {
                gcUtil.column(commentsColumns)
                        .push('delete', 'i18n.columnCommentDelete', cType.Function(['id'], deleteComment, gridIcons.remove), null, null, null, null, false);
            }
            //comments
            gcUtil.column(commentsColumns)
                    .push('author_description', 'i18n.columnCommentDescription', cType.Text(), null, null, null, null, false)
                    .push('description', 'i18n.columnCommentDescription', cType.Text(), null, null, null, null, false)
                    .push('createdDate', 'i18n.columnCommentCreatedDate', cType.Timeago(), null, null, null, null, false);
            commentGrid = new gridComponent({
                id: 'meeting_comments',
                size: 0,
                container: 'meeting_comments',
                serviceStore: 'Meeting.action',
                methodName: "getGroundRows",
                columns: commentsColumns,
                freezeHeader: false,
                noEyes: true,
                genericGridIsSortable: false,
                refreshOnPopRow: false,
                noSettings: true,
                noExcel: true,
                noPagination: true,
                searchContainer: null,
                noRegMessage: i18n.comments_lang_noRegMessage
            });
            commentGrid.setMethodExtraFilter(['meeting_comments', id]);
            commentGrid.newSearch();
            if (allowToAddComments) {
                on(dom.byId('addComments'), 'click', addComment);
            } else {
                domClass.remove('addComments', 'Button');
            }
        }
        function createActivitiesGrid() {
            if (isNew) {
                return;
            }
            var parentCode = dom.byId('txtCode').value;
            new ActivityGrid({
                size: 0,
                container: 'meeting_activities',
                serviceStore: '../DPMS/Meeting.action?currentEntityId=' + id,
                methodName: 'getActivities',
                isManager: true,
                searchContainer: 'none',
                freezeHeader: false,
                resizable: false,
                noEyes: false,
                noSettings: true,
                noExcel: true,
                resultsInfo: 'none',
                paginationInfo: 'none'
            }, dom.byId('addActivity'), true, true, {
                module: 'meeting',
                urlAction: 'Meeting-Activity.action',
                source: {
                    meetingId: id,
                    parentCode: parentCode
                }
            }).startup().then(function (grid) {
                grid.refreshCurrentPage().then(function(){
                    var data = grid.getBean().data;
                    if(data.length !== 0){
                        domAttr.set('txtCode', 'disabled', 'disabled');
                    }
            });
            });

        }
        function validateEmail() {
            lines = $(".lineno");
            lines.css('background-color', '#FFFFFFF');
            lines.css('color', '#333333');
            var emailList = $("#guest").val().split('\n');
            for (var i = 0, l = emailList.length; i < l; i++) {
                if (!emailList[i]) {
                    continue;
                }
                var valid = MAIL_REGEX.test(emailList[i]);
                if (!valid) {
                    idx = $(".lineno:nth-child(" + (i + 1) + ")");
                    idx.css('background-color', '#FF3333');
                    return false;
                }
            }
            return true;
        }
        function changeType(evt, manualChange) {
            if (!manualChange) {
                generateCode();
            }
            var result;
            if (typeIdDom.value) {
                domAttr.set(typeIdDom, 'data-last-selection-value', typeIdDom.value);
                result = DynamicFieldUtil.load(id, 'MeetingType-DynamicFields.action', dynamicFields, typeIdDom.value, {
                    cacheOn: false,
                    dijits: true,
                    legacy: true
                }, 'recalculate').then(function (r) {
                    if (disabled) {
                        var dMap = r && r.domMap ? r.domMap : {};
                        fieldListUtil.handleAsDisabled(dMap);
                    }
                });
            } else {
                result = DynamicFieldUtil.clear('MeetingType-DynamicFields.action', dynamicFields, +domAttr.get(typeIdDom, 'data-last-selection-value'), {
                    cacheOn: false,
                    dijits: true
                });
            }
            return result;
        }
        function getCodePreview(data) {
            return callMethod({
                url: 'Meeting.action',
                method: 'getMeetingCodePreview',
                params: [data]
            });
        }
        function confirmCodeChange(data, codePreview) {
            var currentCode = txtCodeDom.value;
            if (!currentCode.match(new RegExp('^' + codePreview.replace(/#/g, '.') + '$'))) {
                core.info(
                        core.specifiedMessage(i18n.verifyConfirmNewCode)
                        .set('currentCode', currentCode)
                        .set('codePreview', codePreview)
                        .get(),
                        core.i18n.yes,
                        core.i18n.no
                        ).then(function () {
                    core.showLoader(i18n.reservingCodeSequence);
                    getCode(data).then(function (code) {
                        txtCodeDom.value = code;
                        core.hideLoader();
                    }, core.hideLoader);
                }, core.hideLoader);
            }
            core.hideLoader();
        }
        function getCode(data) {
            return callMethod({
                url: 'Meeting.action',
                method: 'getMeetingCode',
                params: [data]
            });
        }
        function generateCode() {
            if (core.isNull(id) || isNew) {
                return;
            }
            core.showLoader();
            var data = fillEntity();
            getCodePreview(data).then(function (codePreview) {
                confirmCodeChange(data, codePreview);
            }, core.hideLoader);
        }
        function loadCKEditor() {
            var def = new Deferred();
            var body = dom.byId('body');
            ckeditor.replace(body, {
                toolbar: [
                    ['Undo','Redo','-','Cut','Copy','Paste', 'PasteText', 'PasteFromWord','Replace','-','Outdent','Indent','-'],
                    ['Bold','Italic','Underline','Strike'],
                    ['NumberedList','BulletedList','Outdent','Indent','-','JustifyLeft','JustifyCenter','JustifyRight','JustifyBlock'],
                    ['Image','Table','-','Link','TextColor'],
                    ['Styles','Format','Font','FontSize']
                ],
                on: {
                    instanceReady: function () {
                        def.resolve();
                    },
                    language: $locale
                }
            });
            return def.promise;
        }
        function setDefaultData() {
            isHost = true;
            domClass.remove('showPeriodicityGroup', 'displayNone');
            dom.byId("hostId").value = dom.byId("loggedUserId").value;
            dom.byId('chkGenerate').checked = true;
            dom.byId("classified").value = '0';
            changeShowPeriodicity();
            toggleGenerateKey();
            var types = query('option', typeIdDom);
            if (types.length === 2) {
                types[1].selected = true;
                changeType(null, true).then(core.hideLoader);
            } else {
                core.hideLoader();
            }
        }
        var clnBtn = dom.byId('cleanBtn');
        clnBtn && on(clnBtn, 'click', function () {
            CKEDITOR.instances.body && CKEDITOR.instances.body.setData("");
            startOnDate.set("value", null);
            finishOnDate.set("value", null);
            file_attacher.clearData();
        });
        function changeShowPeriodicity() {
            if (+showPeriodicity.value === 1){
               domClass.remove('periodicityPickerGroup', 'displayNone');   
            } else {             
               domClass.add('periodicityPickerGroup', 'displayNone'); 
            }
        }
        var loadEditor = loadCKEditor();
        isHost = dom.byId("loggedUserId").value === dom.byId("hostId").value;
        initializeView();
        applyBusinessRules(loadEditor);
        //participant grid
        createParticipantGrid();
        createMeetingsGrid();
        //fileAttacher
        file_attacher = new fileAttacher({
            serviceStore: '../DPMS/Meeting.action',
            currentEntity: id,
            methodName: 'getDocuments',
            showLoader: core.showLoader,
            hideLoader: core.hideLoader,
            hasDelete: allowToDeleteFiles,
            hasUpload: allowToUploadFiles,
            uploadPath: '../DPMS/Upload.fileUploader',
            gridSize: -1
        }, 'fileAttacher');
        createCommentsGrid();
        createActivitiesGrid();
        //event binding
        on(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
        on(dom.byId('saveBtn'), 'click', function () {
            save(false);
        });
        if (!isNew) {
            on(dom.byId('updateBtn'), 'click', function () {
                save(true);
            });
        }
        if (recurrent && !deleted){
            dom.byId('onBtnControl').value = '../view/v-recurring-meeting-list.view';
        } else if (recurrent && deleted){
            dom.byId('onBtnControl').value = '../view/v-recurring-meeting-recycle-bin.view';
        } else {
            dom.byId('onBtnControl').value = '../view/v-meeting-list.view';
        }
        on(startOnDate, 'change', function () {
            if(startOnDate.get('value')){
                pp && pp.updatePeriodicity && pp.updatePeriodicity({startDate: startOnDate.get('value')});
            }
        });
        on(showPeriodicity, 'change', changeShowPeriodicity);
        if (recurrent && !deleted) {
            dom.byId('onBtnControl').value = '../view/v-recurring-meeting-list.view';
        } else if (recurrent && deleted) {
            dom.byId('onBtnControl').value = '../view/v-recurring-meeting-recycle-bin.view';
        } else {
            dom.byId('onBtnControl').value = '../view/v-meeting-list.view';
        }
        
        on(dom.byId('cancelBtn'), 'click', core.cancel);
        on(typeIdDom, 'change', changeType);
        on(dom.byId('hostId'), 'change', generateCode);
        if (isNew) {
            setDefaultData();
            core.hideLoader();
        } else {
            changeType(null, true).then(core.hideLoader);
        }
    }
    core.setLang('bnext/administrator/meeting/nls/meeting').then(doEverything).then(core.hideLoader);
});
