/* global $locale */

define([
  'bnext/gridIcons',
  'dojo/dom-construct',
  'dojo/on',
  'bnext/columnTypes',
  'bnext/angularNavigator',
  'loader',
  'dojo/dom-attr',
  'dojo/dom-style',
  'dojo/query',
  'dojo/dom-class',
  'bnext/_base/color-util',
  'bnext/_base/CurrencyUtil',
  'bnext/_base/NumberUtils',
  'dijit/Dialog',
  'dijit/registry',
  'dojo/date/locale',
  'dojo/_base/lang',
  'dojo/_base/array',
  'dojo/json',
  'dojo/dom',
  'bnext/i18n!lang/nls/main',
  'core',
  'dijit/Tooltip',
  'bnext/gridCubes'
], (
  gridIcons,
  domConstruct,
  on,
  columnTypes,
  angularNavigator,
  loader,
  domAttr,
  domStyle,
  query,
  domClass,
  colorUtil,
  CurrencyUtil,
  NumberUtils,
  Dialog,
  registry,
  locale,
  lang,
  array,
  json,
  dom,
  mainLang,
  core,
  Tooltip,
  gridCubes
) => {
  const debug = false;
  function disableSort(columns) {
    if (lang.isArray(columns)) {
      for (let i = 0; i < columns.length; i++) {
        columns[i].isSortable = false;
      }
    } else {
      console.error(`Invalid array, ${typeof columns}`);
    }
  }
  function renderImage(nodeTd, imageSrc) {
    if (!imageSrc) {
      return '-';
    }
    const viewAnchor = domConstruct.create(
      'div',
      {
        href: 'javascript:void(0)',
        class: 'grid-image-cell',
        style: {
          'background-image': `url('${imageSrc}')`
        }
      },
      nodeTd
    );
    domClass.add(nodeTd, 'avatar-td');
    domStyle.set(nodeTd, 'text-align', 'center');
    return viewAnchor;
  }

  function renderSelectEntityMultiple(args, obj) {
    if (obj[args.text] !== null && typeof obj[args.text] !== 'undefined') {
      return obj[args.text];
    }
    if (core.isNumeric(obj[args.id])) {
      return obj[args.id];
    }
    if (!core.isNull(obj[args.id])) {
      if (obj[args.id].code && !args.skipCode) {
        return `${obj[args.id].description} (${obj[args.id].code})`;
      }
      return obj[args.id].description;
    }
    return '-';
  }
  function renderSelectCatalogMultiple(args, obj) {
    if (!core.isNull(obj[args.description])) {
      return obj[args.description];
    }
    return '-';
  }

  function renderDynamicField(args, obj) {
    const value = lang.getObject(args.id, false, obj);
    if (core.isNumeric(value)) {
      return value;
    }
    return value.description;
  }

  function getParsedCriteria() {
    const filter = gcUtil.getDummyFilter(this);
    let criterio;
    let dndType;
    let referencia;
    const $GET = core.getUrlVars();
    //genera criterios de busqueda para campos normales
    for (let m = 0, lgt = this.columns.length; m < lgt; m++) {
      const column = this.columns[m];
      const widjetId = lang.exists('id', column) ? `${this.id}_${column.id}` : 0;
      filter.columns[column.id] = {
        id: column.id,
        shown: column.display === 'none' ? 0 : 1,
        escapeHtmlTitle: column.escapeHtmlTitle || 0
      };
      if (this.existsFilterForColumn(column, widjetId)) {
        if ($GET[column.id]) {
          this.setCriteriaValue(column, widjetId, $GET[column.id]);
        }
        let isTransient = false;
        if (lang.exists('isTransient', column)) {
          isTransient = column.isTransient;
        }
        if (!isTransient) {
          const refId = this.getRefId(column);
          const ctria = this.getCriteriaValue(column, widjetId);
          if (ctria.invalidField) {
            return {
              invalidSearch: true,
              invalidField: ctria.referencia
            };
          }
          criterio = ctria.criterio;
          dndType = ctria.dndType;
          referencia = ctria.referencia;
          const lower = registry.byId(`${widjetId}_lower`);
          const upper = registry.byId(`${widjetId}_upper`);
          const time_lower = registry.byId(`${widjetId}_time_lower`);
          const time_upper = registry.byId(`${widjetId}_time_upper`);
          if ((referencia || lower || upper || time_lower || time_upper) && column.searchObj && lang.exists('type', column.searchObj)) {
            const columnValue = column.value;
            this.setCriteriaFilter(column.searchObj, columnValue, filter, referencia, criterio, refId, dndType, widjetId, column);
          } else {
            log(`Ignorando criterio [${column.id} = ${refId}] Ya que no existe un objeto para leer valor`);
          }
        }
      }
    }
    //genera criterios de busqueda para campos dinamicos
    const dynWidjets = this.getDynamicSearchFields();
    let dw;
    let val;
    for (const fk in dynWidjets) {
      if (!Object.hasOwn(dynWidjets, fk)) {
        continue;
      }
      dw = dynWidjets[fk];
      val = dw.get('value');
      if (core.isNull(val)) {
        continue;
      }
      val = val.toString().trim();
      if (val || val === '0') {
        lang.setObject(`dynamicFieldCriteria.${dw.id.replace(new RegExp(`${this.getDynamicSearchFieldsScope()}-`), '')}`, val, filter);
      }
    }
    const extraCriteria = this.getExtraCriteria();
    const ignoreEmptyCriteria = {};
    if (extraCriteria && !isNull(extraCriteria.key) && !isNull(extraCriteria.value)) {
      ignoreEmptyCriteria[extraCriteria.key] = true;
      lang.setObject(`criteria.${extraCriteria.key}`, extraCriteria.value, filter);
    } else if (Array.isArray(extraCriteria)) {
      for (let r = 0; r < extraCriteria.length; r++) {
        const elemento = extraCriteria[r];
        if (lang.isObject(elemento) && !isNull(elemento.key) && !isNull(elemento.value)) {
          ignoreEmptyCriteria[elemento.key] = true;
          lang.setObject(`criteria.${elemento.key}`, typeof elemento.value === 'object' ? elemento.value : `${elemento.value}`, filter);
        } else if (lang.isObject(elemento) && (!isNull(elemento.keyMap) || !isNull(elemento.keyIn)) && !isNull(elemento.key)) {
          const sep = !isNull(elemento.keyIn) ? '<:>' : '<,>';
          let innedKeyMap = `0${sep}0`;
          const mapped = elemento.keyMap || elemento.keyIn;
          for (const k in mapped) {
            if (!Object.hasOwn(mapped, k)) {
              continue;
            }
            if (mapped[k]) {
              innedKeyMap = innedKeyMap + sep + k;
            }
          }
          lang.setObject(`criteria.${elemento.key.toString().replace(/\./g, '#')}`, innedKeyMap, filter);
        } else if (lang.isObject(elemento) && (!isNull(elemento.idMap) || !isNull(elemento.idIn)) && !isNull(elemento.key)) {
          const sep = !isNull(elemento.idIn) ? '<:>' : '<,>';
          let innerIdMap = `0${sep}0`;
          const mapped = elemento.idMap || elemento.idIn;
          for (const k in mapped) {
            if (!Object.hasOwn(mapped, k)) {
              continue;
            }
            if (mapped[k].id) {
              innerIdMap = innerIdMap + sep + mapped[k].id;
            }
          }
          lang.setObject(`criteria.${elemento.key.toString().replace(/\./g, '#')}`, innerIdMap, filter);
        }
        //<:>
      }
    }
    //Se usa para buscar por String
    if (this.getExtraLikeCriteria() && !isNull(this.getExtraLikeCriteria().key) && !isNull(this.getExtraLikeCriteria().value)) {
      lang.setObject(`likeCriteria.${this.getExtraLikeCriteria().key}`, this.getExtraLikeCriteria().value, filter);
    } else if (Array.isArray(this.getExtraLikeCriteria())) {
      for (let r = 0; r < this.getExtraLikeCriteria().length; r++) {
        const elemento = this.getExtraLikeCriteria()[r];
        if (lang.isObject(elemento) && !isNull(elemento.key) && !isNull(elemento.value)) {
          lang.setObject(`likeCriteria.${elemento.key}`, `${elemento.value}`, filter);
        }
      }
    }
    if (this.enableStatistics) {
      filter.statisticsFields = Array.prototype.slice.call(query('input[type=radio]:checked,input[type=checkbox]:checked', this.statisticsEnvelope).map((e) => e.value));
    }
    filter.emptyCriterias = areEmptyCriterias(filter, ignoreEmptyCriteria);
    return filter;
  }
  function areEmptyCriterias(filter, ignoreCriteria) {
    let ignore;
    if (ignoreCriteria) {
      ignore = ignoreCriteria;
      ignore.deleted = true;
    } else {
      ignore = {
        deleted: true
      };
    }
    const emptyCriterias =
      core.isObjectEmpty(filter.criteria, ignore) &&
      core.isObjectEmpty(filter.likeCriteria, ignore) &&
      core.isObjectEmpty(filter.dynamicFieldCriteria, ignore) &&
      core.isObjectEmpty(filter.lowerLimit, ignore) &&
      core.isObjectEmpty(filter.upperLimit, ignore);
    return emptyCriterias;
  }

  function renderRenderObjectList(
    updateDom,
    propertyPath,
    displayValueAction,
    filterItemAction,
    filterItemValue,
    hyperlinkAction,
    displayTooltipAction,
    displayTooltipExport,
    row
  ) {
    if (!row) {
      return;
    }
    const arrayItems = lang.getObject(propertyPath, false, row);
    let temp;
    const cache = {};
    if (arrayItems && arrayItems.length > 0) {
      let localSpan;
      if (updateDom) {
        localSpan = domConstruct.create('ul');
      } else {
        localSpan = '';
      }
      array.forEach(arrayItems, (item, i) => {
        temp = displayValueAction(item);
        if (!filterItemAction(item, filterItemValue)) {
          return;
        }
        if (updateDom) {
          if (temp && !cache[temp]) {
            const li = domConstruct.create('li', null, localSpan);
            const localLink = domConstruct.create(
              'a',
              {
                class: `object-list-${propertyPath}`,
                innerHTML: temp
              },
              li
            );
            if (typeof hyperlinkAction === 'function') {
              on(localLink, 'click', () => {
                hyperlinkAction(item.id);
              });
              localLink.href = 'javascript:void(0);';
            } else if (typeof hyperlinkAction === 'string') {
              localLink.href = hyperlinkAction.replace('${id}', item.id);
            }
            if (typeof displayTooltipAction === 'function') {
              new Tooltip({
                connectId: localLink,
                label: displayTooltipAction(item),
                showDelay: 5
              });
            }
          }
        } else {
          if (temp && !cache[temp]) {
            if (i === 0) {
              localSpan += displayTooltipExport(item);
            } else {
              localSpan += `\n${displayTooltipExport(item)}`;
            }
          }
        }
        cache[temp] = true;
      });
      return localSpan;
    }
  }

  function renderOwner(key, row) {
    const cache = {};
    if (!row[key]) {
      return '-';
    }
    const ownerUsers = row[key].users;
    if (ownerUsers === null) {
      return '-';
    }
    let description = null;
    array.forEach(ownerUsers, (ownerUser) => {
      const value = ownerUser.user.description;
      if (cache[value]) {
        return;
      }
      cache[value] = true;
      if (description === null) {
        description = value;
      } else {
        description += `, ${value}`;
      }
    });
    const ownerPositions = row[key].positions;
    if (ownerPositions === null) {
      return description || '-';
    }
    array.forEach(ownerPositions, (ownerPosition) => {
      if (!ownerPosition.position || !ownerPosition.position.users) {
        return;
      }
      array.forEach(ownerPosition.position.users, (user) => {
        const value = user.description;
        if (cache[value]) {
          return;
        }
        cache[value] = true;
        if (description === null) {
          description = value;
        } else {
          description += `, ${value}`;
        }
      });
    });
    return description;
  }
  const dialog = new Dialog();
  const cType = {
    Object: (objectShowedValue, idValue, includeSearchObj) => ({
      type: columnTypes.object,
      value: objectShowedValue || 'description',
      idValue: idValue || 0,
      searchObj: includeSearchObj
        ? {
            col: 1,
            type: 'text-in',
            daoHibernate: 0,
            daoData: 0,
            key: objectShowedValue || 'description'
          }
        : null
    }),
    Text: (objectShowedValue, excludeSearchObj, escapeHtmlTitle) => ({
      type: columnTypes.text,
      value: objectShowedValue || 'description',
      escapeHtmlTitle: escapeHtmlTitle ? 1 : 0,
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'texto',
            daoHibernate: 0,
            daoData: 0
          }
    }),
    CustomText: (renderText, renderTextExcel, excludeSearchObj) => ({
      type: columnTypes.text,
      value: 'description',
      renderCell: renderText,
      renderCellExcel: renderTextExcel,
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'texto',
            daoHibernate: 0,
            daoData: 0
          }
    }),
    Currency: (excludeSearchObj) => {
      function renderCurrency(row, nodeTd, nodeTr, nodeTable, column) {
        if (!column) {
          return '';
        }
        const value = row[column.id];
        if (value === null || typeof value === 'undefined' || value === '') {
          return '';
        }
        return `$${CurrencyUtil.formatCurrency(NumberUtils.redondeoTwoDecimal(value))}`;
      }
      return {
        type: columnTypes.text,
        value: 'description',
        renderCell: renderCurrency,
        renderCellExcel: (row, column) => renderCurrency(row, null, null, null, column),
        searchObj: excludeSearchObj
          ? null
          : {
              col: 1,
              type: 'double',
              daoHibernate: 0,
              daoData: 0
            }
      };
    },
    Integer: (renderText, renderTextExcel, objectShowedValue, excludeSearchObj) => ({
      type: columnTypes.text,
      value: objectShowedValue || 'description',
      renderCell: renderText,
      renderCellExcel: renderTextExcel,
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'integer',
            daoHibernate: 0,
            daoData: 0
          }
    }),
    Double: (objectShowedValue, excludeSearchObj, min, max) => ({
      type: columnTypes.double,
      value: objectShowedValue || 'description',
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'double',
            rangeMax: max,
            rangeMin: min,
            daoHibernate: 0,
            daoData: 0
          }
    }),
    Long: (excludeSearchObj) => ({
      type: columnTypes.long,
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'long',
            daoHibernate: 0,
            daoData: 0
          }
    }),
    Percentaje: () => ({
      type: columnTypes.percentaje,
      searchObj: {
        col: 1,
        type: 'percentaje',
        daoHibernate: 0,
        daoData: 0
      }
    }),
    Color: (excludeSearchObj) => ({
      type: columnTypes.color,
      value: 'description',
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'texto',
            daoHibernate: 0,
            daoData: 0
          }
    }),
    Selectable: (action) => ({
      action: action,
      type: columnTypes.selectable,
      idValue: 'selected',
      selectableHeader: true,
      isSortable: false
    }),
    Time: (includeSearchObj, onlyByTime) =>
      lang.clone({
        type: columnTypes.time,
        onlyByTime: onlyByTime,
        searchObj: includeSearchObj
          ? {
              col: 1,
              type: 'timestamp',
              daoHibernate: 0,
              daoData: 0
            }
          : null
      }),
    Timestamp: (includeSearchObj, onlyByTime, timeLabelField) =>
      lang.clone({
        type: columnTypes.timestamp,
        onlyByTime: onlyByTime,
        timeLabelField: timeLabelField,
        searchObj: includeSearchObj
          ? {
              col: 1,
              type: 'timestamp',
              daoHibernate: 0,
              daoData: 0
            }
          : null
      }),
    Date: (includeSearchObj) =>
      lang.clone({
        type: columnTypes.date,
        searchObj: includeSearchObj
          ? {
              col: 1,
              type: 'fecha',
              daoHibernate: 0,
              daoData: 0
            }
          : null
      }),
    Timeago: (includeSearchObj) =>
      lang.clone({
        type: columnTypes.timeago,
        searchObj: includeSearchObj
          ? {
              col: 1,
              type: 'fecha',
              daoHibernate: 0,
              daoData: 0
            }
          : null
      }),
    /**
     *
     * @param {type} params
     * @param {type} action
     * @param {type} options
     *     {
     *       icon: 'edit' | gridIcons.xxxx,
     *       eventContext: undefined | 'row-context',
     *       useOnce: undefined | false | true,
     *       href: undefined | string
     *     }
     * @returns {gridComponentUtilL#15.cType.FunctionByObject.gridComponentUtilAnonym$20}
     */
    FunctionByObject: (params, action, optionsParam) => {
      const options = optionsParam || {};
      return {
        icon: options.icon || gridIcons.edit,
        action: action,
        parameters: params,
        eventContext: options.eventContext || null,
        type: columnTypes.function,
        searchObj: false,
        isSortable: false,
        useOnce: options.useOnce || false,
        href: options.href || 'javascript:void(0)'
      };
    },
    /**
     * @deprecated Utilizar `FunctionByObject`
     *
     * @param {type} params
     * @param {type} action
     * @param {type} icon
     * @param {type} eventContext
     * @param {type} useOnce
     * @param {type} href
     * @returns {gridComponentUtilL#15.cType.Function.gridComponentUtilAnonym$21}
     */
    Function: (params, action, icon, eventContext, useOnce, href) => ({
      icon: icon || gridIcons.edit,
      action: action,
      parameters: params,
      eventContext: eventContext || null,
      type: columnTypes.function,
      searchObj: false,
      isSortable: false,
      useOnce: useOnce || false,
      href: href || 'javascript:void(0)'
    }),
    Select: (selectArray, excludeSearchObj) => ({
      type: columnTypes.select,
      key: 'description',
      list: selectArray,
      value: 'id',
      isSortable: false,
      searchObj: excludeSearchObj
        ? null
        : {
            col: 1,
            type: 'combo',
            daoHibernate: 0,
            daoData: 0,
            list: selectArray,
            searchName: 'description',
            idValue: 'id'
          }
    }),
    SelectMultiple: (selectArrayParam, keyDescParam, keyIdParam, multipleDataType = 'integer') => {
      const keyDesc = keyDescParam || 'description';
      const keyId = keyIdParam || 'id';
      // Sort Locale
      const selectArray = selectArrayParam.sort((a, b) => {
        const aValue = a[keyDesc];
        const bValue = b[keyDesc];
        return aValue.localeCompare(bValue) < 0 ? -1 : 1;
      });
      return {
        type: columnTypes.select,
        key: keyDesc,
        list: selectArray,
        value: keyDesc,
        searchObj: {
          col: 1,
          type: 'multiple',
          daoHibernate: 0,
          daoData: 0,
          multipleDataType: multipleDataType,
          list: selectArray,
          searchName: keyDesc,
          idValue: keyId
        }
      };
    },
    SelectEntityMultiple: (args) => {
      let id;
      if (args.argsWithFullName) {
        id = args.id;
      } else {
        id = `${args.id}.id`;
      }
      return {
        id: id,
        key: 'text',
        value: 'value',
        renderCell: (obj) => renderSelectEntityMultiple(args, obj),
        renderCellExcel: (obj) => renderSelectEntityMultiple(args, obj),
        searchObj: {
          serviceName: args.serviceName,
          methodName: args.methodName || 'getActives',
          col: 1,
          type: 'multiple-entity',
          daoHibernate: 0,
          daoData: 0,
          id: args.id
        }
      };
    },
    SelectCatalogMultiple: (args) => ({
      id: args.id,
      sortId: args.description,
      key: 'text',
      value: 'value',
      renderCell: (obj) => renderSelectCatalogMultiple(args, obj),
      renderCellExcel: (obj) => renderSelectCatalogMultiple(args, obj),
      searchObj: {
        serviceName: args.serviceName,
        methodName: args.methodName || 'getActives',
        col: 1,
        type: 'multiple-entity',
        daoHibernate: 0,
        daoData: 0,
        id: args.id
      }
    }),
    CustomSelectMultiple: (args) => ({
      id: args.id,
      key: 'text',
      value: 'value',
      type: columnTypes.text,
      searchObj: {
        serviceName: args.serviceName,
        methodName: args.methodName,
        col: 1,
        type: args.type,
        daoHibernate: 0,
        daoData: 0,
        id: args.id
      }
    }),
    /**
     * Dibuja una celda en base a todos los datos del RENGLON, el ID en el push es irrelevante
     *
     * @param {String} objectShowedValue
     * @param {Function} renderCellFunction :
     *
     *      Recibe una funcion que regresa un STRING,
     *      el string es el que se dibujará, recibe como primer parametro todo el objeto renglon
     *
     *      Ej.
     *          function(row) {
     *              return 'El estatus es ' + row.status + ' con descripcion ' + row.description;
     *          }
     *
     */
    RenderCell: (renderCellFunction, objectShowedValue, renderCellExcel, argsParam) => {
      const args = argsParam || {};
      const column = {
        type: columnTypes.text,
        value: objectShowedValue || 'description',
        renderCell: renderCellFunction,
        renderCellExcel: renderCellExcel,
        isSortable: false,
        searchObj: false
      };
      if (args.searchable) {
        column.searchObj = {
          col: 1,
          type: 'texto',
          daoHibernate: 0,
          daoData: 0,
          key: objectShowedValue || 'description'
        };
      }
      return column;
    },
    /**
     * Cuando el 'id' de la columna vale lo que dice algun 'value' del arreglo, muestra el icono correspondiente
     *
     * @Array params : arreglo de parametros Ej. ['id','status']
     * @Function action : funcion que se ejecutará y recibira los parametros
     * @Array imagesArrayObj : arreglo que contiene a que valor le corresponde a cada imagen, debe ser de esta forma
     *              [
     *                  {'icon': gridCubes.green, 'value': 1},
     *                  {'icon': gridCubes.red, 'value': 2},
     *                  {'icon': gridCubes.black, 'value': 3},
     *                  {'icon': gridCubes.orange, 'value': 4}
     *              ]
     */
    FunctionImage: (params, action, imagesArrayObj, includeSearchObj, idValue) => ({
      action: action,
      parameters: params,
      type: columnTypes.functionImage,
      list: imagesArrayObj,
      key: 'icon',
      value: 'value',
      idValue: idValue || 0,
      searchObj:
        includeSearchObj || typeof includeSearchObj === 'undefined'
          ? {
              col: 1,
              type: 'combo',
              daoHibernate: 0,
              daoData: 0,
              list: imagesArrayObj,
              default: dom.byId('status')?.value || null
            }
          : null
    }),
    /**
     *
     * @param {type} args
     *      {
     *          id: 'documentType',                             <--- Es la variable que contiene la relación con campos dinamicos
     *          serviceName: 'DocumentType.SearchFields.action' <--- El servicio con la configuracion del entity de la relación
     *      }
     * @returns {gridComponentUtil_L11.cType.DynamicField.gridComponentUtilAnonym$13}
     */
    DynamicField: (args) => ({
      id: `${args.id}.id`,
      key: 'text',
      value: 'value',
      isDynField: true,
      renderCell: (obj) => renderDynamicField(args, obj),
      renderCellExcel: (obj) => renderDynamicField(args, obj),
      searchObj: {
        serviceName: args.serviceName,
        col: 1,
        type: 'dynfield',
        daoHibernate: 0,
        daoData: 0,
        id: args.id
      }
    }),
    FunctionCube: (params, action, imagesArrayObj, includeSearchObj, idValue) => ({
      action: action,
      parameters: params,
      type: columnTypes.functionImage,
      list: imagesArrayObj,
      key: 'cubeId',
      value: 'value',
      idValue: idValue || 0,
      searchObj: includeSearchObj
        ? {
            col: 1,
            type: 'combo',
            daoHibernate: 0,
            daoData: 0,
            list: imagesArrayObj,
            default: dom.byId('status')?.value || null
          }
        : null
    }),
    /**
     * Casi igual que 'FunctionImage', solo difiere en el arreglo que va asi
     * @Array imagesArrayObj : arreglo que contiene a que valor le corresponde a cada imagen, debe ser de esta forma
     *              [
     *                  {'icon': gridCubes.green, 'Function': doNothing},
     *                  {'icon': gridCubes.red, 'Function': doNothing},
     *                  {'icon': gridCubes.black, 'Function': doNothing},
     *                  {'icon': gridCubes.orange, 'Function':
     *                                  function(row) { --aqui procesar la variable 'row'-- }
     *                                  }
     *              ]
     * Si la funcion de alguna posicion del arrgle regresa 'true' entonces mostrara ese icono
     **/
    FunctionImageEval: (params, action, imagesArrayObj, includeSearchObj, idValue) => ({
      action: action,
      parameters: params,
      type: columnTypes.evaluatedRow,
      list: imagesArrayObj,
      key: 'icon',
      value: 'value',
      idValue: idValue || 0,
      isSortable: !!includeSearchObj,
      searchObj: includeSearchObj
        ? {
            col: 1,
            type: 'combo',
            daoHibernate: 0,
            daoData: 0,
            list: imagesArrayObj
          }
        : null
    }),
    /**
     * Casi igual que 'FunctionImageEval', solo que esta pone TEXTO, difiere en el arreglo que va asi
     * @Array imagesArrayObj : arreglo que contiene a que valor le corresponde a cada imagen, debe ser de esta forma
     * @action: puede valer null si no se desea realizar ninguna accion
     *              [
     *                  {'text': 'Cuando doNothing1 regres TRUE escribe esto', 'Function': doNothing1},
     *                  {'text': 'Cuando doNothing2 regres TRUE escribe esto', 'Function': doNothing2},
     *                  {'text': 'Cuando doNothing3 regres TRUE escribe esto', 'Function': doNothing3},
     *                  {'text': 'Cuando doNothing4 regres TRUE escribe esto', 'Function': doNothing4},
     *                  {'text': gridCubes.orange, 'Function':
     *                                  function(row) { --aqui procesar la variable 'row'-- }
     *                                  }
     *              ]
     * Si la funcion de alguna posicion del arrgle regresa 'true' entonces mostrara ese texto
     **/
    FunctionTextEval: (params, action, imagesArrayObj, includeSearchObj, idValue) => ({
      action: action,
      parameters: params,
      type: columnTypes.evaluatedRow,
      list: imagesArrayObj,
      key: 'text',
      value: 'value',
      idValue: idValue || 0,
      searchObj: includeSearchObj
        ? {
            col: 1,
            type: 'combo',
            daoHibernate: 0,
            daoData: 0,
            list: imagesArrayObj
          }
        : null
    }),
    /**
     * Casi igual que 'FunctionImage', solo difiere en el arreglo que va asi
     * @Boolean includeSearchObj  : bandera que indica si se incluira o no un campo de busquda
     * @String  aGroup: si vale 0 no hce nada, si vale algo diferente los valores de esta columna se agruparan en la misma columna (TD)
     *          Ej. Listado de perfiles en alta de puestos
     * @Array   textKeyValueArray : arreglo que contiene a que valor le corresponde a cada texto, debe ser de esta forma
     *              [
     *                  {name : 'Planeado', value: 1},
     *                  {name : 'Programado', value: 2},
     *                  {name : 'En proceso', value: 3},
     *                  {name : 'Finalizado', value: 4},
     *                  {name : 'Extra', value: 5, 'td-style': {'background-color':'red'}} //<--- es un "STYLE" tal cual aplicado al TD de la columna/fila evaluada
     *              ]
     **/
    TextMap: (textKeyValueArray, includeSearchObj, idValue) => ({
      type: columnTypes.textMap,
      list: textKeyValueArray,
      key: 'name',
      value: 'value',
      idValue: idValue || 0,
      searchObj:
        includeSearchObj || typeof includeSearchObj === 'undefined'
          ? {
              col: 1,
              type: 'combo',
              daoHibernate: 0,
              daoData: 0,
              list: textKeyValueArray
            }
          : null
    }),
    /**
     * Se utiliza para mostrar varios datos tipo List o Set que esten dentro del objeto (entity)
     * maestro,
     *
     * Ej. el listado de puestos en el control de usuarios
     *
     * @param {String}  key                 :   es lo que se despliega en el grid
     * @param {Boolean} includeSearchObj    :   define si el grid inluira un campo de busqueda para este campo
     * @returns {_L3.Anonym$0.cType.Anonym$6.ObjectList.Anonym$14}
     */
    ObjectList: (includeSearchObj, key, idValue, defaultValue) => ({
      type: columnTypes.multipleSelect,
      key: key || 'description',
      idValue: idValue || 0,
      searchObj: includeSearchObj
        ? {
            col: 1,
            type: 'text-in',
            daoHibernate: 0,
            daoData: 0,
            key: key || 'description',
            defaultValue: defaultValue || null
          }
        : null
    }),
    DateOnlySearch: () =>
      lang.clone({
        type: columnTypes.hidden,
        searchObj: {
          col: 1,
          type: 'fecha',
          daoHibernate: 0,
          daoData: 0
        }
      }),
    /**
     * Se utiliza para mostrar varios datos tipo List o Set que esten dentro del objeto (entity)
     * maestro,
     *
     * Ej. el listado de puestos en el control de usuarios
     *
     * @param {String}  key                 :   es lo que se despliega en el grid
     * @param {Boolean} includeSearchObj    :   define si el grid inluira un campo de busqueda para este campo
     * @returns {_L3.Anonym$0.cType.Anonym$6.ObjectList.Anonym$14}
     */
    Patternable: (value, reference, pattern, header, separateRow, customPattern) => ({
      type: columnTypes.patternable,
      key: 'description',
      reference: reference || 'description',
      pattern: pattern || '{}',
      idValue: value || 0,
      header: header || '',
      separateRow: separateRow,
      customPattern: customPattern,
      searchObj: null
    }),
    /**
     * Se utiliza para mostrar un select con los valores de un select base
     * @param {type} keyId Id del valor en el row
     * @param {type} keyDescription Id de la descripción en el row
     * @param {type} selectClass Class del select
     * @param {type} baseDomSelect Dom del select base
     * @param {type} loadOptionsBaseAction Function to load options from select base
     * @returns {gridComponentUtil_L7.cType.RenderCell.gridComponentUtilAnonym$9}
     */
    RenderSelect: (keyId, keyDescription, selectClass, baseDomSelect, loadOptionsBaseAction) => ({
      type: columnTypes.text,
      value: 'description',
      renderCell: (row) => {
        let localValue = null;
        const localOptions = [];
        const localSelect = domConstruct.create('select', {
          name: `local_${row.id}`,
          class: selectClass
        });
        const baseDomSelectValue = row[keyId];
        if (!core.isNull(baseDomSelectValue)) {
          localValue = baseDomSelectValue;
          localOptions.push({ value: `${baseDomSelectValue}`, text: row[keyDescription] });
          domAttr.set(localSelect, 'readonly', true);
        } else if (baseDomSelect?.value) {
          loadOptionsBaseAction(localOptions);
          localValue = baseDomSelect.value;
        } else {
          loadOptionsBaseAction(localOptions);
        }
        fillCombo(localSelect, localOptions, 'text', 'value', false);
        if (localValue) {
          localSelect.value = localValue;
          localSelect.lastValue = localValue;
          row[keyId] = localValue;
        }
        on(localSelect, 'change', () => {
          row[keyId] = localSelect.value;
          localSelect.lastValue = localSelect.value;
        });
        return localSelect;
      },
      renderCellExcel: (row) => row[keyDescription],
      isSortable: false,
      searchObj: false
    }),
    RenderMenuLink: (config) => {
      const language = encodeURIComponent($locale === 'en' ? $locale : 'es');
      function getData(row) {
        let url = config.url || 'javascript: void(0)';
        let title = config.title || '';
        const icon = config.icon;
        array.forEach(config.params, (param) => {
          let paramValue = lang.getObject(`${param}`, false, row);
          if (paramValue === null || paramValue === 'undefined') {
            paramValue = '';
          }
          const paramKey = `{${param}}`;
          url = url.replace(paramKey, encodeURIComponent(paramValue));
          title = title.replace(paramKey, paramValue);
        });
        return {
          url: url,
          title: title,
          icon: icon
        };
      }
      const column = {
        type: columnTypes.text,
        value: 'description',
        renderCell: (row) => {
          const data = getData(row);
          const url = data.url;
          const title = data.title;
          const icon = data.icon;
          let hrefUrl;
          if (config.legacy) {
            hrefUrl = `./../qms/${language}/menu/legacy/${url}`;
          } else {
            hrefUrl = `./../qms/${language}/${url}`;
          }
          const r = domConstruct.create('a', {
            href: hrefUrl,
            onclick: (event) => {
              event.preventDefault();
              event.stopPropagation();
              if (config.openBlank) {
                if (config.legacy) {
                  angularNavigator.navigateBlankLegacy(url);
                } else {
                  angularNavigator.navigateBlank(url);
                }
              } else {
                if (config.legacy) {
                  angularNavigator.navigateLegacy(url);
                } else {
                  angularNavigator.navigate(url);
                }
              }
            },
            target: '_blank',
            innerHTML: icon ? '' : title,
            class: 'primary-color cursorPointer'
          });
          if (icon) {
            domConstruct.create(
              'img',
              {
                src: require.toUrl(`bnext/images/${icon}`),
                title: title
              },
              r
            );
          }
          return r;
        },
        renderCellExcel: (row) => {
          const data = getData(row);
          return data.title;
        },
        isSortable: config.sortable
      };
      const searchObjConfig = {
        col: 1,
        type: 'texto',
        daoHibernate: 0,
        daoData: 0,
        key: 'description'
      };
      if (config.icon) {
        column.searchObj = false;
      } else if (typeof config.searchObj !== 'undefined' && config.searchObj !== null) {
        column.searchObj = config.searchObj ? searchObjConfig : false;
      } else {
        column.searchObj = searchObjConfig;
      }
      return column;
    },
    RenderAvatar: (userColumName) => {
      return {
        type: columnTypes.text,
        renderCell: (row, nodeTd) => {
          const imageSrc = `./../view/v-application-avatar.view?id=${row[userColumName]}`;
          return renderImage(nodeTd, imageSrc);
        },
        renderCellExcel: () => '-',
        isSortable: false,
        searchObj: false
      };
    },
    Base64Image: () => {
      return {
        type: columnTypes.text,
        renderCell: (row, nodeTd, nodeTr, nodeTable, column) => {
          return renderImage(nodeTd, row[column.id] || '');
        },
        renderCellExcel: () => '-',
        isSortable: false,
        searchObj: false
      };
    },
    /**
     * Se utiliza para crear un input
     * @param {type} key Column key
     * @param {type} inputClass Class for input
     * @returns {gridComponentUtil_L7.cType.RenderCell.gridComponentUtilAnonym$9}
     */
    RenderInput: (key, inputClass) => ({
      type: columnTypes.text,
      value: 'description',
      renderCell: (row) => {
        let localValue = null;
        const localInput = domConstruct.create('input', {
          name: `local_${row.id}`,
          class: inputClass
        });
        const rowValue = row[key];
        if (!core.isNull(rowValue)) {
          localValue = rowValue;
          domAttr.set(localInput, 'readonly', true);
        }
        if (localValue) {
          localInput.value = localValue;
          row[key] = localValue;
        }
        on(localInput, 'change', () => {
          row[key] = localInput.value;
        });
        return localInput;
      },
      renderCellExcel: (row) => row[key],
      isSortable: false,
      searchObj: false
    }),
    /**
     * Se utiliza para mostrar varios datos tipo List o Set que esten dentro del objeto (entity)
     * maestro con filtro de datos y ligado a una acción con un hypervinculo
     *
     * @param {type} propertyPath Path de la propiedad del entity principal
     * @param {type} displayValueAction Método para generar la propiedad a desplegar
     * @param {type} filterItemAction Filtro para decidir si se incluye o no el dato
     * @param {type} filterItemValue Segundo parámetro de includeItemAction
     * @param {type} hyperlinkAction Acción a ejectuar en el link
     * @returns {gridComponentUtil_L6.cType.RenderCell.gridComponentUtilAnonym$9}
     */
    RenderObjectList: (propertyPath, displayValueAction, filterItemAction, filterItemValue, hyperlinkAction, displayTooltipAction, displayTooltipExport) => ({
      type: columnTypes.text,
      isSortable: false,
      searchObj: false,
      value: 'description',
      renderCell: (row) =>
        renderRenderObjectList(
          true,
          propertyPath,
          displayValueAction,
          filterItemAction,
          filterItemValue,
          hyperlinkAction,
          displayTooltipAction,
          displayTooltipExport,
          row
        ),
      renderCellExcel: (row) =>
        renderRenderObjectList(
          false,
          propertyPath,
          displayValueAction,
          filterItemAction,
          filterItemValue,
          hyperlinkAction,
          displayTooltipAction,
          displayTooltipExport,
          row
        )
    }),
    /**
     * Se utiliza para mostrar varios datos tipo List o Set que esten dentro del objeto (entity)
     * maestro con filtro de datos y ligado a una acción con un hypervinculo
     *
     * @param {type} key Path de la propiedad del entity principal
     * @param {type} displayValueAction Método para generar la propiedad a desplegar
     * @param {type} filterItemAction Filtro para decidir si se incluye o no el dato
     * @param {type} filterItemValue Segundo parámetro de includeItemAction
     * @param {type} hyperlinkAction Acción a ejectuar en el link
     * @returns {gridComponentUtil_L6.cType.RenderCell.gridComponentUtilAnonym$9}
     */
    Owner: (key) => ({
      type: columnTypes.text,
      value: key,
      renderCell: (row) => renderOwner(key, row),
      renderCellExcel: (row) => renderOwner(key, row),
      isSortable: false,
      searchObj: {
        col: 1,
        type: 'owner',
        daoHibernate: 0,
        daoData: 0,
        key: key
      }
    }),
    /**
     * Se utiliza para agregar la columna de selección del GridMenu
     *
     */
    RenderSelectableMenu: (renderCellFunction, includeSelectableHeader, onSelectAllRows, onDeselectAllRows) => ({
      type: columnTypes.selectable,
      parameters: ['full-row', 'grid'],
      selectableHeader: includeSelectableHeader === true,
      onSelectAllRows: onSelectAllRows,
      onDeselectAllRows: onDeselectAllRows,
      value: 'description',
      renderCell: renderCellFunction,
      renderCellExcel: () => '-',
      isSortable: false,
      searchObj: false,
      idValue: 'selected'
    }),
    /**
     * Se utiliza para mostrar iconos de gridIcons
     *
     * @param {statusList} statusList lista con los datos
     */
    ImageMap: (statusList) => ({
      list: statusList,
      type: columnTypes.imageMap,
      key: 'icon',
      value: 'value',
      name: 'name',
      searchObj: {
        type: 'combo',
        col: 1,
        list: statusList
      }
    }),
    Hidden: (objectShowedValue) =>
      lang.clone({
        type: columnTypes.hidden,
        value: objectShowedValue || 'description',
        display: 'none',
        isSortable: false,
        searchObj: false
      }),
    /**
     * Se utiliza para mostrar iconos de gridIcons
     *
     * @param {statusList} statusList lista con los datos
     */
    ImageMapMultiple: (statusList) => ({
      list: statusList,
      type: columnTypes.imageMapMultiple,
      key: 'icon',
      value: 'value',
      name: 'name',
      searchObj: {
        type: 'multiple',
        col: 1,
        list: statusList,
        searchName: 'name',
        idValue: 'value'
      }
    })
  };
  const gcUtil = {
    parseDynamicValue: (row) => {
      const parsedRow = {};
      for (const rk in row) {
        if (!Object.hasOwn(row, rk)) {
          continue;
        }
        const underscoreIndex = rk.indexOf('_');
        if (typeof rk === 'string' && underscoreIndex === -1) {
          if (!parsedRow[rk]) {
            parsedRow[rk] = row[rk];
          }
        } else {
          const mainProp = rk.substring(0, underscoreIndex);
          if (Object.hasOwn(row, `${mainProp}_id`) && core.isNull(row[`${mainProp}_id`])) {
            parsedRow[mainProp] = null;
            continue;
          }
          if (typeof parsedRow[mainProp] !== 'object') {
            parsedRow[mainProp] = {};
          }
          const newProp = rk.replace(/_/g, '.');
          const current = lang.getObject(newProp, parsedRow);
          let sett = true;
          if (typeof current === 'object') {
            for (const srk in current) {
              if (!Object.hasOwn(row, rk)) {
                continue;
              }
              sett = false;
              break;
            }
            if (!sett) {
              continue;
            }
          }
          core.setObject(newProp, row[rk], parsedRow);
        }
      }
      return parsedRow;
    },
    /**
     * Es un objeto de busqueda basico
     */
    searchObj: (col, type, daoHibernate, daoData, list, serviceValue, title) => {
      const res = {
        col: col ? col : 1,
        type: type || +type === 0 ? type : 'texto',
        daoData: daoData ? daoData : '',
        daoHibernate: daoHibernate ? daoHibernate : '',
        title: title,
        serviceValue: serviceValue ? serviceValue : [],
        list: list ? list : []
      };
      return lang.clone(res);
    },
    init: (gridsArray, size) => {
      array.forEach(gridsArray, (arr, i) => {
        arr.setPageSize(+arr.size !== 15 ? arr.size : size || 15);
      });
    },
    getWindowPath: (obj, locationParam) => {
      const location = locationParam || window.location;
      let windowPath = null;
      if (obj) {
        windowPath = obj.windowPath || null;
      }
      try {
        windowPath = windowPath || location.pathname.substr(1).substr(location.pathname.substr(1).indexOf('/')) + location.search || '#invalid';
      } catch (e) {
        windowPath = '#invalid';
      }
      return windowPath;
    },
    /**
     * Regresa un objeto basico de ARGUMENTOS gridComponent, como datos minimos requiere:
     *      obj = {
     *          serviceStore: 'AuditSample.action',   //<-- el CRUD del grid
     *          container: 'dataGrid'           //<-- es el ID de un DOM <table>
     *      }
     **/
    basic: (obj) => {
      if (!lang.exists('serviceStore', obj) || !lang.exists('container', obj)) {
        console.error('>>> FAIL! debe especificar un service-store / container');
        return {};
      }
      obj.size = core.isNull(obj.size) ? 15 : obj.size;
      let r = {
        size: obj.size,
        id: lang.exists('id', obj) ? obj.id : obj.container,
        methodName: obj.methodName || 'getRows',
        windowPath: obj.windowPath || '#invalid',
        savedWindowFilter: obj.savedWindowFilter || '#invalid',
        methodExtraFilter: obj.methodExtraFilter || [],
        serviceStore: obj.serviceStore,
        container: obj.container,
        statusList: obj.statusList || [
          { name: mainLang.firstComboElement || '--SELECCIONE--', value: '', icon: gridCubes.gray },
          { name: mainLang.STATUS[0] || 'Activo', value: 1, icon: gridCubes.green },
          { name: mainLang.STATUS[1] || 'Inactivo', value: 0, icon: gridCubes.gray }
        ],
        columns: obj.fullColumns || {
          is: '#auto',
          editRecordIcon: obj.editRecordIcon || false,
          extraColumns: lang.clone(obj.columns || []),
          editRecordActionDefault: obj.editRecordActionDefault,
          toogleEstatus: obj.toggleEstatus || null,
          logging: obj.logging || null
        },
        searchContainer: obj.searchContainer === 'none' ? null : obj.searchContainer || '#auto',
        resultsInfo: obj.resultsInfo || '#auto',
        paginationInfo: obj.paginationInfo || '#auto'
      };
      if (obj.noEdit && !obj.fullColumns) {
        r.columns.noEdit = true;
      }
      obj.columns = undefined;
      r = lang.mixin(obj, r);
      if (debug) {
        console.log('basic column', r);
      }
      return lang.clone(r);
    },
    column: function (array0, array1, array2, array3, array4) {
      let array = array0;
      if (!array) {
        array = [];
      }

      const GroupControl = [];
      const _return = {
        build: function (id, title, typeOrPreparedColumn, searchObj, width, display, group, sortable) {
          return this.push(id, title, typeOrPreparedColumn, searchObj, width, display, group, sortable, 'no-push');
        },
        /**
         * Minimo requiere 'id'
         *
         * @String id || @Column    : El ID de la columna (su nombre en el mapeo). O una columna ya construída.
         * @String title            : La etiqueta que se dibujara por 1era ves (usualmente se sustituye por algo del language)
         * @Object || @Number typeOrPreparedColumn : Puede ser un objeto tipo 'cType' o un numero tipo 'columnTypes'
         * @Object || @Boolean searchObj : Es un objeto tipo 'searchObj' o bien un boolean que indica poner una busqueda tipo 'texto'
         * @Object || @String width : Puede ser un objeto 'attributes' o un string que denote el ancho de la columna (Ej. '90px')
         * @String display          : Si vale 'none' la columna aparece oculta (desocultable por el ojito)
         * @Object group            : Sirve para agrupar varias columnas en una sola concatenando sus valores, está implementado en control de perfiles.
         * @Boolean sortable        : Si vale TRUE muestra el icono de "ordenar" la columna.
         * @String flag          : Si vale 'no-push' la columna no se agrega al arreglo de columnas.
         **/
        push: (id, title, typeOrPreparedColumn, searchObjParam, width, display, groupParam, sortable, flag, sortDirection) => {
          if (typeof id === 'object' && typeof id.id === 'string') {
            array?.push(lang.clone(id));
            array1?.push(lang.clone(id));
            array2?.push(lang.clone(id));
            array3?.push(lang.clone(id));
            array4?.push(lang.clone(id));
            return _return;
          }
          //console.log('--> push ! ' + group.group + ' - ' + GroupControl.indexOf(group.group) + ' - '+GroupControl.length)
          const group = (groupParam ? lang.clone(groupParam) : 0) || 0;
          if (group?.group && +GroupControl.indexOf(group.group) === -1) {
            GroupControl.push(group.group);
            group.index = 1;
          }
          const columna = { id: id, title: title || id };
          let type = typeOrPreparedColumn;
          if (sortDirection) {
            columna.sortDirection = sortDirection;
          }
          if (group) {
            columna.belongsToGroup = group || 0;
          }
          let searchObj = searchObjParam;
          if (searchObj !== null && typeof searchObj !== 'boolean' && !lang.isObject(searchObj)) {
            searchObj = this.searchObj(1);
          }
          if (typeOrPreparedColumn && lang.isObject(typeOrPreparedColumn)) {
            type = typeOrPreparedColumn.type || 1;
            if (typeof searchObj === 'boolean' && !searchObj) {
              //sin campos de busqueda
              searchObj = false;
            } else {
              searchObj = typeOrPreparedColumn.searchObj || searchObj;
            }
            lang.mixin(columna, typeOrPreparedColumn);
          }
          const widthConfig = lang.isObject(width)
            ? width
            : width
              ? {
                  style: {
                    width: width,
                    'max-width': width
                  }
                }
              : {};
          if (flag === 'no-export') {
            columna.skipExcelExport = true;
          }
          if (flag === 'force-export') {
            columna.forceExcelExport = true;
          }
          if (flag === 'no-push') {
            return this.pushColumn(array, columna, type || 1, widthConfig, searchObj, display, sortable, flag);
          }
          const c = this.pushColumn(array, columna, type || 1, widthConfig, searchObj, display, sortable);
          array1?.push(lang.clone(c));
          array2?.push(lang.clone(c));
          array3?.push(lang.clone(c));
          array4?.push(lang.clone(c));
          return _return;
        },
        set: (property, value) => {
          const lastColumn = array[array.length - 1];
          lastColumn[property] = value;
          return _return;
        },
        setSearchObj: (property, value) => {
          const lastColumn = array[array.length - 1];
          lastColumn.searchObj = lastColumn.searchObj || {};
          lastColumn.searchObj[property] = value;
          return _return;
        },
        hide: function (id) {
          return this.push(id, id, columnTypes.hidden, null, { value: -1 }, 'none');
        },
        searchField: function (id, label, typeOrPreparedColumnParam) {
          let typeOrPreparedColumn = typeOrPreparedColumnParam;
          if (typeof typeOrPreparedColumn === 'undefined') {
            typeOrPreparedColumn = cType.Text();
          }
          typeOrPreparedColumn.type = columnTypes.search;
          return this.push(id, label || id, typeOrPreparedColumn, null, { value: -1 }, 'none');
        },
        localizeRecord: function (urlAction, i18n, ignoreFields) {
          const localizedEntities = dom.byId('localizedEntities') ? dom.byId('localizedEntities').value === 'true' : false;
          if (!localizedEntities) {
            return this;
          }
          return this.push(
            'localizeRecord',
            core.i18n.colNameLocalizeRecord,
            cType.Function(
              ['full-row'],
              (row) => {
                loader.showLoader().then(() => {
                  setTimeout(() => {
                    require(['bnext/module/LocalizeHandler'], (LocalizeHandler) => {
                      new LocalizeHandler({
                        urlAction: urlAction,
                        record: row,
                        dialog: dialog,
                        i18nExternal: i18n,
                        ignoreFields: ignoreFields || [],
                        columns: array
                      });
                    });
                  }, 10);
                });
              },
              'translate.png'
            ),
            null,
            '25px'
          );
        },
        /**
         * Agrega una columna de tipo texto a partir de un valor boleano al grupo correspondiente
         * @param {type} name Nombre de la columna
         * @param {type} label Etiqueta de la columna
         * @param {type} group Grupo al que se agrega la columna en formato
         * @param {type} addToGroup lo muestra si tiene acceso al modulo
         * {name: 'Name of the group', label: 'Label of the group'}
         * @returns {gridComponentUtil_L6.gcUtil.column._return}
         */
        pushBoolGroup: function (name, label, group, addToGroup) {
          if (!addToGroup) {
            return _return;
          }
          if (!group) {
            console.error('Invalid group while trying to se column: ', name, label);
            return _return;
          }
          const columnType = cType.TextMap(
            [
              {
                name: label,
                value: 1,
                searchName: mainLang.STATUS[0]
              },
              {
                name: '',
                value: 0,
                searchName: mainLang.STATUS[1]
              }
            ],
            true,
            false
          );
          const groupDef = {
            group: group.name,
            label: group.label
          };
          return this.push(name, label, columnType, null, '30px', false, groupDef);
        }
      };
      return _return;
    },
    /**
     * @ToDo: Agregar las columnas faltantes si se requiere...
     */
    cType: lang.mixin(() => {
      //El mixin es backward compatibility,
      // si ya no hay instancias de cType como funcion, quitar  y dejar solo cType
      return cType;
    }, cType),
    pushColumn: (array, columna, type, attributes, searchObj, display, sortable, flag) => {
      try {
        //log('pushColumn ... ' +array.length +' - ' + type + ' - ' + columna.title + ' - ' + premadeList + ' - ' + json.stringify(searchObj)/**/);
        const column = {
          type: type,
          searchObj: searchObj ? searchObj : 0,
          display: display ? display : '',
          isSortable: sortable !== false,
          isTransient: false
        };
        lang.mixin(column, lang.clone(columna));
        const attr = attributes ? attributes : columna.attributes ? columna.attributes : 0;
        if (attr) {
          lang.setObject('attributes', attr, column);
        }
        if (flag === 'no-push') {
          //do nothing
        } else {
          array.push(lang.clone(column));
        }
        return column;
      } catch (e) {
        log(`Error! ${e}`);
      }
    },
    InCriteria: (criteriaObj) => {
      const inData = criteriaObj.array;
      const separator = criteriaObj.separator;
      const defaultValue = lang.exists('defaultValue', criteriaObj) ? criteriaObj.defaultValuej : '<zero>';
      const key = criteriaObj.key || 'id';
      let cc = '';
      const excluir = {
        key: key,
        value: defaultValue
      };
      let criterio;
      if (inData?.length) {
        for (let i = 0, l = inData.length; i < l; i++) {
          criterio = inData[i].id;
          if (criterio && criterio !== '') {
            cc = cc + (cc === '' ? criterio + separator + criterio : separator + criterio);
          }
        }
        excluir.value = `${cc}`;
      }
      return excluir;
    },
    fixDate: (date) => new Date(date.getTime() - date.getTimezoneOffset() * 60000),
    parseDateFromPattern: (/* String */ fecha, datePattern) => {
      if (fecha === null) {
        return fecha;
      }
      const res = locale.parse(fecha, {
        selector: 'date',
        datePattern: datePattern
      });
      return res;
    },
    /**
     * Recibe una cadena de fecha de la base de datos ()y la convierte al formato esperado para el dijit DateTextBox
     * @param {type} fecha
     * @returns {unresolved}
     */
    parseDateFromJson: function (/* String */ fecha) {
      try {
        return this.parseDateFromPattern(fecha, "yyyy-MM-dd'T'HH:mm:ss");
      } catch (e) {
        console.error(e.stack);
      }
    },
    parseTimeFromJson: (/* String */ time) => {
      if (time === null) {
        return time;
      }
      const res = locale.parse(time, {
        selector: 'time',
        timePattern: "yyyy-MM-dd'T'HH:mm:ss"
      });
      return res;
    },
    parseDateToJson: function (/* Date object */ fecha) {
      return this.parseDateToPattern(fecha, "yyyy-MM-dd'T'HH:mm:ss");
    },
    getLocaleDateFormat: (locale) => {
      if (locale === 'en' || (dom.byId('SYSTEM_LANGUAGE') !== null && dom.byId('SYSTEM_LANGUAGE').value === 'en')) {
        return 'MM/dd/yyyy';
      }
      return 'dd/MM/yyyy';
    },
    parseDateStrToPattern: function (/* String yyyy-MM-dd'T'HH:mm:ss */ fecha, dateFormat) {
      try {
        return this.parseDateToPattern(this.parseDateFromJson(fecha), dateFormat);
      } catch (e) {
        console.error(e.stack);
      }
    },
    parseDateToPattern: (/* Date object */ fecha, datePattern) => {
      if (fecha == null) {
        return fecha;
      }
      return locale.format(fecha, {
        selector: 'date',
        datePattern: datePattern
      });
    },
    getPropertyFromList: (listParam, key) => {
      const list = lang.clone(listParam);
      const res = [];
      for (let k = 0; k < list.length; k++) {
        const current = list[k];
        if (lang.exists(key, current)) {
          const element = {};
          lang.setObject(key, +lang.getObject(key, false, current), element);
          res.push(element);
        } else {
          console.error(`No existe ${key} en ${json.stringify(list)}`);
        }
      }
      return res;
    },
    /**
     * Cuando se hace linkGrid a dos grid en que sus columnas no son equivalentes
     *  se utiliza esta función para crear las columnas de equivalencia.
     *
     * @param {String} baseName: El nombre de la propiedad en el grid base.
     * @param {String} linkedName: El nombre de la propiedad en el grid a relacionar.
     * @param {String} isComposite: Indica si la columna es compuesta.
     * @param {String} linkedName: Indica los elementos que forman la columna compuesta. Deben
     *                             ir en el orden del constructor de la clase de la llave compuesta.
     * @returns {Object} linkColumn:
     *              Object en esta estructura
     *                {
     *                  base: '',
     *                  linked: ''
     *                }
     **/
    createLinkColumn: (baseName, linkedName, isComposite, compositeIds) => {
      const column = {
        base: baseName,
        linked: linkedName,
        isComposite: isComposite || false,
        compositeIds: compositeIds || []
      };
      return column;
    },
    domValue: (id, defaultValue) => {
      if (dom.byId('floatingGridSize')) {
        return dom.byId('floatingGridSize').value || defaultValue;
      }
      return defaultValue;
    },
    setColorsForObjectList: (color, gridId, propertyPath) => {
      let maxItems = 0;
      const baseColor = color;
      const cells = query(`table[id="${gridId}"] td.${gridId}_cell`);
      const itemsCells = {};
      array.forEach(cells, (cell) => {
        const curentItemCell = query(`a.object-list-${propertyPath}`, cell);
        itemsCells[cell.id] = curentItemCell;
        const items = curentItemCell.length;
        if (items > maxItems) {
          maxItems = items;
        }
      });
      array.forEach(cells, (cell) => {
        const curentItemCell = itemsCells[cell.id];
        const numberItems = curentItemCell.length;
        if (numberItems === 0) {
          domClass.add(cell, 'object-list-empty');
          return;
        }
        domClass.add(cell, 'object-list-not-empty');
        const dynamicColor = colorUtil.generateDynamicColor(baseColor, maxItems, numberItems);
        domStyle.set(cell, 'background-color', dynamicColor);
        const contrastColor = colorUtil.getContrastColor(dynamicColor);
        domAttr.set(cell, 'attr-color', contrastColor);
        domAttr.set(cell, 'attr-background-color', dynamicColor);
        domAttr.set(cell, 'attr-number-items', numberItems);
        array.forEach(curentItemCell, (item) => {
          domStyle.set(item, 'color', contrastColor);
        });
      });
    }
  };
  gcUtil.getParsedCriteria = getParsedCriteria;
  gcUtil.getDummyFilter = function (gridInstanceParam) {
    let gridInstance = gridInstanceParam;
    if (typeof gridInstanceParam === 'undefined') {
      gridInstance = getDummyGrid();
    }
    return {
      field: gridInstance.getField(),
      direction: gridInstance.getDirection(),
      page: gridInstance.getCurrentPage(),
      pageSize: gridInstance.getPageSize(),
      gridId: gridInstance.id,
      criteria: {},
      likeCriteria: {},
      lowerLimit: {},
      upperLimit: {},
      dynamicFieldCriteria: {},
      columns: {},
      statisticsFields: [],
      enableStatistics: this.enableStatistics
    };
  };
  function sugar(obj) {
    return () => obj;
  }
  function getDummyGrid() {
    return {
      id: `grid_${new Date().getTime()}`,
      getField: sugar({
        orderBy: null,
        type: 'HQL'
      }),
      getDirection: sugar(1),
      getCurrentPage: sugar(0),
      getPageSize: sugar(-1),
      dynamicFieldCriteria: {},
      windowPath: '',
      dynamicSearchEnabled: false
    };
  }
  gcUtil.disableSort = disableSort;
  gcUtil.areEmptyCriterias = areEmptyCriterias;
  return gcUtil;
});
