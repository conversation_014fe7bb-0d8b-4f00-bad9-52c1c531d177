<%-- 
    Document   : puesto.handle
    Created on : 6/11/2012, 04:20:57 PM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<s:set var="id" value="id" />
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <title>Perfiles</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <%@include file="../../components/requiredScripts.jsp" %>
        <script type='text/javascript' src="c.folder.access.controller?${systemVersion}"></script>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false">
        <style>
            .hidden1,.hidden2,.hidden3{
                display: none!important
            }
            .hidden1.show,.hidden2.show,.hidden3.show{
                display: block!important
            }

            .bnext .dijitDialogPaneContent {
                min-width: 500px;
            }
            ul#contenido div.gridExpandable thead, .gridFooter {
                display: none;
            }
            ul#contenido div.info {
                border-color: gray;
                font-style: italic;
                text-indent: 20px;
                width: 230px;
                float: left;
                clear: right;
                background: url('../images/common/info16.png') no-repeat 4px 3px;
                padding: 5px;
            }            
            ul#contenido div.gridExpandable td {
                border: 1px #DDD solid;
            }
            #footerComponent {
                width: 100%; 
            }
            #example_sh_grid_deps {
                display: none;
            }
            li.table_workarea.separator {
                box-shadow: none;
            }
            .bnext .dijitDialog.fixedTop div[data-dojo-attach-point="titleBar"] {
                width: 1170px !important;
                margin-right: 0.5rem !important;
            }
            .bnext .dijitDialog.fullWidthScreen + .dijitDialogUnderlayWrapper {
                background-color: rgba(0, 0, 0, 0.38) !important;
                transition-duration: 350ms;
                transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
                opacity: 0.7;
            }
        </style>
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle" style="display: none"></h3>
                            </div>
                        </div>
                        <div id="MessageBox"></div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">  
                                <div id="descriptionDiv" class="cell small-12">
                                    <div class="textarea-component">
                                        <input type="text" name="description" id="description" value="" required="required"></input>
                                        <label id="lblDescription"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" id="addBusinessUnitBtn" value="">
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div id="node_access_business_unit"></div>
                                </div>
                                <div class="cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" id="addUserBtn" value="">
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div id="node_access_users"></div>
                                </div>
                                <div class="cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" id="addDepartmentBtn" value="">
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div id="node_access_departments"></div>
                                </div>
                                <div class="cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" id="addProcessBtn" value="">
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div id="node_access_area"></div>
                                </div>
                                <div class="cell small-12 actionButtons">
                                    <%@include file="../../components/saveButtons.jsp" %>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                                <s:hidden name="id" id="id"/>
                                <s:hidden name="code" id="code"/>
                                <s:hidden name="floatingGridSize" id="floatingGridSize" />
                                <s:hidden name="windowPath" id="windowPath" />
                                <input type="hidden" name="onBtnAdd" id="onBtnAdd" value="v.folder.access.view?id=${id}" />
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>