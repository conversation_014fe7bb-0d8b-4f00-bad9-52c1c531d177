require([
  'core',
  'bnext/gridCubes',
  'bnext/extensionIcons',
  'bnext/module/DocumentRequest',
  'bnext/document/DynamicFieldsHandler',
  'bnext/administrator/solicitudes/saveRequestHandle',
  'bnext/angularNavigator',
  'bnext/angularFabMenu',
  'bnext/module/grid-menu',
  'bnext/administrator/document/document-menu-item',
  'bnext/module/grid-menu-util',
  'dijit/Dialog',
  'bnext/gridComponent',
  'bnext/gridComponentUtil',
  'dojo/_base/array',
  'dojo/_base/lang',
  'dojo/dom',
  'dojo/dom-class',
  'dojo/promise/all',
  'dojo/dom-construct',
  'dojo/string',
  'dojo/Deferred',
  'bnext/callMethod',
  'dojo/domReady!'
], (
  core,
  gridCubes,
  extensionIcons,
  DocumentRequest,
  DynamicFieldsHandler,
  saveRequestHandle,
  angularNavigator,
  angularFabMenu,
  GridMenu,
  DocumentMenuItem,
  GridMenuUtil,
  Dialog,
  gridComponent,
  gcUtil,
  array,
  lang,
  dom,
  domClass,
  all,
  domConstruct,
  string,
  Deferred,
  callMethod
) => {
  const columns = [];
  const cType = gcUtil.cType;
  let docComponent;
  let multipleReapproveComponent;
  let saveRequestHandleNEW;
  let dialog;
  const storageList = [];
  const informationClassificationList = [];
  const dispositionList = [];
  const menuItems = [];
  let grid;
  let gridMenu = null;
  const doEverything = (i18n) => {
    const statusList = [
      { name: i18n.status_active, value: 1, icon: gridCubes.green },
      { name: i18n.status_in_edition, value: 0, icon: gridCubes.red }
    ];
    const cancelConfirm = () => {
      docComponent.destroyRecursive();
      docComponent.hide();
      return [core.simpleSuccessDeferred];
    };
    const handleFormRequest = (type, documento) => {
      if (!documento.surveyId || type === 'editDetails') {
        return false;
      }
      const typeRequestLabel = {
        edit: i18n.colNameModify,
        cancel: i18n.colNameCancel
      };
      // variables utilizadas para los request
      const ev = core.eventedDialog(
        `${core.specifiedMessage(i18n.commonHandleFormRequest_sureMessage, 'type', typeRequestLabel[type])}`,
        [core.i18n.Validate.yes, core.i18n.Validate.no],
        i18n.commonHandleFormRequest_title
      );
      ev.then((dialog) => {
        dialog.event('clickBtn1', () => {
          core.showLoader().then(() => {
            angularFabMenu.displayFabMenu(false);
            dialog.hide();
            const url = `v.request.survey.mode.view?documentId=${documento.id}&id=${documento.surveyId}&viewMode=MODIFY`;
            angularNavigator.navigateLegacy(url);
          });
        });
        dialog.event('clickBtn2', () => {
          dialog.hide();
        });
      });
      return true;
    };
    function edit(type, documento) {
      if (docComponent) {
        docComponent.destroyRecursive();
      }
      if (handleFormRequest(type, documento)) {
        return;
      }
      const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
      dialog = initializeDialog(dialog);
      let requestType;
      if (type === 'edit') {
        requestType = DocumentRequest.update;
      } else if (type === 'editDetails') {
        requestType = DocumentRequest.editDetails;
      } else {
        requestType = null;
      }
      docComponent = new DocumentRequest({
        isAFormRequest: !!documento.surveyId,
        surveyId: documento.surveyId || null,
        enablePdfViewer: documento.enablePdfViewer || 0,
        type: requestType,
        doc: documento,
        externalFileSelect: core.getExternalFileIntegration(),
        onCancel: cancelConfirm,
        onSubmit: (sol, file) => {
          docComponent.showLoader(core.i18n.Validate.saving);
          return [
            sol.then(
              (sobj) => {
                save(sobj.sol, sobj.file, requestType, docComponent);
              },
              (e) => {
                core.error('No se pudo guardar la solicitud.');
                console.error('Falla al guardar valores dinamicos');
                console.error(e);
                docComponent.hideLoader();
              }
            )
          ];
        },
        requestDialog: dialog
      });
      DynamicFieldsHandler.REQUEST.MODIFY(docComponent);
      domClass.add(docComponent.unknownCodeDiv, 'displayNone');
      docComponent.set('title', i18n[`commonTitleEdit_${type}`]);
      docComponent.show();
    }
    const statusAction = (id, title, code) =>
      core.gridDialogExecutioner({
        url: 'Document.action',
        method: 'toggleStatus',
        params: [id],
        message: string.substitute(i18n.toggleMsg, { title: title || '', code: code || '' }),
        gridOrTabs: grid,
        sucessCallBack: (result) => {
          const tipo = result.jsonEntityData.tipo;
          const requestCode = result.jsonEntityData.requestCode;
          return core.dialog(string.substitute(i18n[tipo], { requestCode: requestCode }), i18n.messages.accept);
        },
        errorCallBack: () => core.dialog(i18n.messages.error, i18n.messages.accept)
      });
    const editAction = (id) => {
      core.showLoader().then(() => {
        angularNavigator.navigateLegacy(`v.documentos.view?vchState=Edit&intDocumentoId=${id}`);
      });
    };
    gridMenu = new GridMenu({
      type: DocumentMenuItem,
      multipleSelection: true
    });
    GridMenuUtil.menu(menuItems, DocumentMenuItem)
      .push(
        DocumentMenuItem.MODIFY_DOCUMENT,
        (documento) => edit('edit', documento),
        (documento) => documento.status === 1
      )
      .push(
        DocumentMenuItem.EDIT_DETAILS,
        (documento) => edit('editDetails', documento),
        (documento) => documento.status === 1
      )
      .push(DocumentMenuItem.CANCEL_DOCUMENT, cancel, (documento) => documento.status === 1)
      .push(DocumentMenuItem.DETAIL_DOCUMENT, editAction, true, ['id', 'status']);

    if (
      dom.byId('onlyDocumentManagerReapprove').value !== 'true' ||
      (dom.byId('onlyDocumentManagerReapprove').value === 'true' && dom.byId('documentRole').value === '3')
    ) {
      GridMenuUtil.menu(menuItems, DocumentMenuItem).push(
        DocumentMenuItem.RE_APPROVE_DOCUMENT,
        aprove,
        (row) => {
          if (row.status === 1) {
            return row.documentType.documentControlledType === 'controlled';
          }
        },
        null,
        null,
        multipleAprove
      );
    }

    const colTypeView = cType.RenderCell(viewDoc);
    colTypeView.isSortable = true;
    colTypeView.idValue = 'fileContent.extension';
    //Columnas especiales para tab "Todas"
    gcUtil
      .column(columns)
      .push('menu', gridMenu.cellHeader(), gridMenu.cellGridType(), null, '65px')
      .push('status', i18n.colNames.status, cType.FunctionImage(['id', 'description', 'code'], statusAction, statusList, true), null, '40px')
      .push('view', i18n.colNames.view, colTypeView, null, '35px')
      .push('code', i18n.colNames.code, cType.Text(), null, '40px')
      .set('disableStatistics', true)
      .push('description', i18n.colNames.description, cType.Text())
      .set('disableStatistics', true)
      .push('version', i18n.colNames.version, cType.Text())
      .push('tRetencion', i18n.colNames.retention, cType.RenderCell(renderRetention, null, renderRetention), null, null)
      .push('disposition', i18n.colNames.disposition, cType.SelectMultiple(dispositionList, null, null, 'long'), null, null)
      .push('storagePlaceId', i18n.colNames.storage, cType.SelectMultiple(storageList), null, null)
      .push(
        'department.description',
        i18n.colNames.department,
        cType.SelectEntityMultiple({
          id: 'department',
          serviceName: 'Documents.BusinessUnitDepartment.action',
          methodName: 'getDocumentSearchFieldValues',
          skipCode: true
        })
      )
      .push('collectingAndStoreResponsible', i18n.colNames.collectingAndStore, cType.RenderCell(renderResponsible, null, renderResponsible))
      .push('informationClassification', i18n.colNames.informationClassification, cType.SelectMultiple(informationClassificationList, null, null, 'long'), null, null);

    grid = new gridComponent(
      gcUtil.basic({
        size: dom.byId('gridSize').value,
        container: 'dataGrid_ALL',
        serviceStore: 'OptMasterList.action',
        methodName: 'getRowsRecords',
        windowPath: dom.byId('windowPath').value,
        fullColumns: columns,
        onLoaded: refreshMenu
      })
    );
    function renderRetention(obj) {
      let s = '-';
      if (obj.retentionText && obj.retentionTime) {
        s = `${obj.retentionTime} ${i18n[`span_${obj.retentionText}`]}`;
      }
      return s;
    }
    function renderResponsible(row) {
      let s = '-';
      if (!row.collectingAndStoreResponsibleDescription && row.collectingAndStoreResponsible) {
        s = row.collectingAndStoreResponsible.description;
      }
      return row.collectingAndStoreResponsibleDescription || s;
    }
    function initializeDialog(dg) {
      let d = dg;
      if (!d) {
        d = new Dialog({ autofocus: false });
      }
      return d;
    }
    function viewDoc(row, nodeTd, trDom, tableDom) {
      const fileInfo = extensionIcons.getExtensionInfo(row.fileContent, row);
      const viewAnchor = domConstruct.create('a', {
        href: 'javascript:void(0)',
        class: 'edit-rendered-cell',
        onclick: () => {
          callMethod({
            url: 'Document.action',
            method: 'getHasAccess',
            params: [row.id]
          }).then((result) => {
            if (result.operationEstatus === 1) {
              const viewerUrl = `v-document-viewer.view?id=${row.id}`;
              angularNavigator.showDocumentViewer(viewerUrl);
            } else {
              core.dialog(i18n.notAccesDocument);
            }
          });
        },
        innerHTML: `<img border=0 src="${fileInfo.iconSrc}"/>`
      });

      trDom.title = i18n.colNameDescFile + fileInfo.title;
      return viewAnchor;
    }
    function cancel(documento) {
      function go() {
        openCancel(documento);
      }
      const documentId = documento.id;
      if (documentId) {
        callMethod({
          url: '../DPMS/Document.action',
          method: 'isRequestInProcess',
          params: [documentId, 'FILL']
        }).then(
          (result) => {
            if (result) {
              core.info(i18n.fillInProcess, core.i18n.yes, core.i18n.no).then(go);
            } else {
              go();
            }
          },
          (e) => {
            core.error(e).then(go);
          }
        );
      } else {
        go();
      }
    }
    function refreshMenu() {
      gridMenu.build(this, menuItems);
    }
    function aprove(documento) {
      if (docComponent) {
        docComponent.destroyRecursive();
      }
      const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
      dialog = initializeDialog(dialog);
      docComponent = new DocumentRequest({
        isAFormRequest: !!documento.surveyId,
        surveyId: documento.surveyId || null,
        enablePdfViewer: documento.enablePdfViewer || 0,
        type: DocumentRequest.aprove,
        doc: documento,
        onCancel: cancelConfirm,
        onSubmit: (sol, file) => {
          docComponent.showLoader(core.i18n.Validate.saving);
          return [
            sol.then(
              (sobj) => {
                save(sobj.sol, sobj.file, 3, docComponent);
              },
              (e) => {
                core.error('No se pudo guardar la solicitud.');
                console.error('Falla al guardar valores dinamicos');
                console.error(e);
                docComponent.hideLoader();
              }
            )
          ];
        },
        requestDialog: dialog
      });
      DynamicFieldsHandler.REQUEST.APROVE(docComponent);
      domClass.add(docComponent.unknownCodeDiv, 'displayNone');
      docComponent.set('title', i18n.commonTitleReapr);
      docComponent.show();
    }
    function multipleAprove(documents) {
      require(['bnext/document/MultipleReapproveRequest'], (MultipleReapproveRequest) => {
        if (multipleReapproveComponent) {
          multipleReapproveComponent.destroyRecursive();
        }
        multipleReapproveComponent = new MultipleReapproveRequest({
          documents: documents,
          grid: saveRequestHandleNEW.grid
        });
      });
    }
    function openCancel(documento) {
      if (docComponent) {
        docComponent.destroyRecursive();
      }
      const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
      dialog = initializeDialog(dialog);
      docComponent = new DocumentRequest({
        isAFormRequest: !!documento.surveyId,
        surveyId: documento.surveyId || null,
        enablePdfViewer: documento.enablePdfViewer || 0,
        type: DocumentRequest.cancel,
        doc: documento,
        onCancel: cancelConfirm,
        onSubmit: (sol, file) => {
          docComponent.showLoader(core.i18n.Validate.saving);
          return [
            sol.then(
              (sobj) => {
                save(sobj.sol, sobj.file, 4, docComponent);
              },
              (e) => {
                core.error('No se pudo guardar la solicitud.');
                console.error('Falla al guardar valores dinamicos');
                console.error(e);
                docComponent.hideLoader();
              }
            )
          ];
        },
        requestDialog: dialog
      });
      DynamicFieldsHandler.REQUEST.CANCEL(docComponent);
      domClass.add(docComponent.unknownCodeDiv, 'displayNone');
      docComponent.set('title', i18n.commonTitleCancel);
      docComponent.show();
    }
    hideLoader();
    saveRequestHandleNEW = new saveRequestHandle(grid);
    return grid.setPageSize(dom.byId('gridSize').value);
  };
  function getStoragePlace() {
    const def = new Deferred();
    callMethod({
      url: '../DPMS/Document.action',
      method: 'getStoragePlaceList',
      params: []
    }).then(
      (resultado) => {
        array.forEach(resultado, (entry, i) => {
          const storage = {
            id: entry.value,
            description: entry.text
          };
          storageList.push(storage);
        });
        def.resolve();
      },
      () => {
        core.hideLoader();
        def.resolve();
      }
    );
    return def.promise;
  }
  function getInformationClassification() {
    const def = new Deferred();
    callMethod({
      url: '../DPMS/Document.action',
      method: 'getInformationClassificationCatalog',
      params: []
    }).then(
      (resultado) => {
        array.forEach(resultado, (entry, i) => {
          const inf = {
            id: entry.value,
            description: entry.text
          };
          informationClassificationList.push(inf);
        });
        def.resolve();
      },
      () => {
        core.hideLoader();
        def.resolve();
      }
    );
    return def.promise;
  }
  function getDisposition() {
    const def = new Deferred();
    callMethod({
      url: '../DPMS/Document.action',
      method: 'getDispositionCatalog',
      params: [true]
    }).then(
      (resultado) => {
        array.forEach(resultado, (entry, i) => {
          const d = {
            id: entry.value,
            description: entry.text
          };
          dispositionList.push(d);
        });
        def.resolve();
      },
      () => {
        core.hideLoader();
        def.resolve();
      }
    );
    return def.promise;
  }
  all([getStoragePlace(), getInformationClassification(), getDisposition()]).then(() => {
    core.setLang('lang/documentos/nls/document.master.records.list').then(doEverything);
  });
});
