require([
    'bnext/gridIcons', 'dojo/Deferred', 'bnext/administrator/solicitudes/saveRequestHandle',
    'bnext/gridComponentUtil', 'loader', 'i18n-util',
    'bnext/administrator/document/document-simple-list', 
    'bnext/administrator/document/document-menu-item', 'bnext/module/grid-menu-util',
    'dojo/dom', 'dojo/_base/array', 'dojo/dom-construct', 'bnext/Tabs', 'dojo/string', 'dojo/on',
    'bnext/gridCubes', 'bnext/columnTypes', 'dojo/dom-class', 'dojo/promise/all',
    'dijit/registry', 'dojo/_base/lang',
    'dijit/MenuItem', 'dijit/DropDownMenu', 'dijit/form/DropDownButton',
    'dojo/domReady!'
],
function (
    gridIcons, Deferred, saveRequestHandle, gcUtil, loader, i18nUtil,
    documentSimpleList, DocumentMenuItem, GridMenuUtil,
    dom, array, domConstruct, Tabs, string, on,
    gridCubes, columnTypes, domClass, all, registry, lang,
    MenuItem, DropDownMenu, DropDownButton
) {
function strToBool(str){return str === 'true';}
    var
        domNav = dom.byId('titleBar'),
        $GET = getUrlVars(), form = document.forms[0], tabs, dial, widget,
        grid_video,
        /* Ligas para los diferentes botones */
        media_url = 'v.document.imagenes.view?nodo=' + $GET.nodo,
        folder_access_url = 'v.folder.access.view',
        grid_media, grid_reference, ref, saveRequestHandleNEW,
        references = null,
        accessRedirect = dom.byId('redirectAccess') && dom.byId('redirectAccess').value === 'true',
        documentEditor = strToBool(dom.byId('documentEditor').value),
        documentManager = strToBool(dom.byId('documentManager').value),
        isAdmin = strToBool(dom.byId('isAdmin').value),
        folderName = '', folderPath = '';
    form.nodo.value = $GET.nodo;
    form.repositorio.value = $GET.repositorio;
    function doEverything(i18n, topLevel) {
        function setEvents() {
            registry.byId('new_folder_dialog').on('hide', function () {
                require(['bnext/angularFabMenu'], function(angularFabMenu) {
                    angularFabMenu.displayFabMenu(true);
                });
            });
            /** Agregar acceso rápido **/
            on(dom.byId('addFavorite'), 'click', function () {
                require(['bnext/angularNavigator'], function(angularNavigator) {
                    var urlParams = 'nodo=' + $GET['nodo'];
                    angularNavigator.addMenuPathAccess(
                        'menu/legacy/v.document.list.ae.view', urlParams, null, null, 'folder', folderName || 'Carpeta', 'document'
                    );
                });
            });
            /** Creación de carpetas **/
            on(dom.byId('cancel_new_folder'), 'click', function () {
                dom.byId('fld_description').value = "";
                registry.byId('new_folder_dialog').hide();
                require(['bnext/angularFabMenu'], function(angularFabMenu) {
                    angularFabMenu.displayFabMenu(true);
                });
            });
            on(dom.byId('add_new_folder'), 'click', function () {
                require(['bnext/administrator/pending/Module', 'core'], function(Module, core) {
                    var folder = {
                        code: dom.byId('fld_description').value,
                        path: dom.byId('window_title_repository').innerText,
                        parent: dom.byId('nodo').value,
                        module: Module.DOCUMENT
                    };
                    registry.byId('new_folder_dialog').hide();
                    if (folder.code.length === 0) {
                        core.dialog(i18n.label.no_empty_name);
                    } else if (folder.code.length > 200) {
                        core.dialog(i18n.label.too_long_folder_name);
                    } else {
                        require(['bnext/callMethod'], function(callMethod) {
                            callMethod({
                                url: 'Node.action',
                                params: [folder],
                                method: 'saveFolder'
                            }).then(function (value) {
                                setTimeout(function () {
                                    if (value) {
                                        core.dialog(i18n.label.add_folder_success).then(function () {
                                            loader.showLoader().then(function() {
                                                require(['bnext/angularNavigator'], function(angularNavigator) {
                                                    window.top.memoryNodes = null;
                                                    angularNavigator.navigateLegacy('v.document.list.ae.view?nodo=' + value.node.id);
                                                });
                                            });
                                        });
                                    } else {
                                        core.dialog(i18n.label.add_folder_error);
                                    }
                                }, 500);
                            }, function(e) {
                                if (e.message === 'duplicate-node') {
                                    core.dialog(i18n.label.duplicate_folder).then(function () {
                                        console.warn(i18n.label.duplicate_folder);
                                    });
                                }
                            }); 
                        });
                    }
                    dom.byId('fld_description').value = '';
                });
            });
            on(dom.byId('fld_description'),'keyup', function (evt) {
                evt.preventDefault();
                if (evt.keyCode === 13){
                    dom.byId('add_new_folder').click();
                }
            });
            on(dom.byId('goBack'), 'click', goBack);
        }
        function newDocument() {
            loader.showLoader().then(function() {
                require([
                    'core',
                    'bnext/module/DocumentRequest',  
                    'bnext/administrator/document/pre-validate-request',
                    'dijit/Dialog',
                    'dojo/aspect',
                    'bnext/DynamicFieldUtil',
                    'bnext/module/data/RequestType'
                ], function(core, DocumentRequest, preValidateRequest, Dialog, aspect, DynamicFieldUtil, RequestType) {
                    function getDialog() {
                        if (!dial) {
                            dial = new Dialog({});
                        }
                        return dial;
                    }
                    widget && widget.destroyRecursive();
                    var dialog = getDialog();
                    var save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
                    widget = new DocumentRequest({
                        type: DocumentRequest.create,
                        enablePdfViewer: 1,
                        node: {nodo: +$GET['nodo'], id: +$GET['nodo'], path: folderPath },
                        externalFileSelect: core.getExternalFileIntegration(),
                        onCancel: function() {
                            widget.hide();
                            require(['bnext/angularFabMenu'], function(angularFabMenu) {
                                angularFabMenu.displayFabMenu(true);
                            });
                        },
                        onBeforeSubmit: preValidateRequest,
                        onSubmit: function(sol, file) {
                            widget.showLoader(core.i18n.Validate.saving);
                            if(typeof sol.then === 'function') {
                                //si el 1er parametro es un PROMISE asumir que el RESOLVE es asi {sol:sol, file: file}
                                return [sol.then(
                                    function(sobj) {
                                        save(sobj.sol, sobj.file, RequestType.NEW, widget);
                                    }, 
                                    function(e) {
                                        core.error('No se pudo guardar la solicitud.');
                                        console.error('Falla al guardar valores dinamicos');
                                        console.error(e);
                                        widget.hideLoader();
                                    }
                                )];
                            } else {
                                //si el 1er parametro NO es un PROMISE continuar con el comportamiento normal
                                return [save(sol, file, RequestType.NEW, widget)];
                            }
                        },
                        requestDialog: dialog
                    });
                    //se enlaza al cambio de valor del combo de tipo de documento
                    aspect.after(widget, '_onDocTypeChange', lang.hitch(widget, function () {
                        DynamicFieldUtil.get('DocumentType.Custom.action', this.dynamicFields, this.docType.get('value'), {
                            cacheOn: false,
                            dijits: true,
                            legacy: false
                        }, 'recalculate');
                    }));
                    //se enlaza guardado de valores de campos dinamicos al guardado normal
                    aspect.before(widget, 'onSubmit', lang.hitch(widget, function (sol, file) {
                        var def = new Deferred();
                        DynamicFieldUtil.save('DocumentType.Custom.action', this.dynamicFields, this.docType.get('value'))
                            .then(
                                function(dynamicFieldInsertDTO) {
                                if (dynamicFieldInsertDTO.tableName) {
                                    sol.dynamicFieldInsertDTO = dynamicFieldInsertDTO;
                                    sol.dynamicTableName = dynamicFieldInsertDTO.tableName;
                                }
                                require(['bnext/angularFabMenu'], function(angularFabMenu) {
                                    angularFabMenu.displayFabMenu(true);
                                    def.resolve({
                                        sol: sol,
                                        file: file
                                    });
                                });
                            }, def.reject
                        );
                        return [def.promise];
                    }));
                    widget.set('title', i18n.titleNew);
                    widget.show(); 
                    loader.hideLoader();
                });
            });
        }
        function newMedia() {
            require(['bnext/angularNavigator'], function(angularNavigator) {
                angularNavigator.navigateLegacy(media_url);
            });
        }
        function newMediaLink() {
            require(['bnext/module/DocumentReference'], function(DocumentReference) {
                if (!references) {
                    references = new DocumentReference($GET['nodo']);
                    on(references, "change", function () {
                        if (typeof grid_reference.reload === 'function') {
                            grid_reference.reload();
                        }
                    });
                }
                references.show();
            });
            
        }
        function configureUserAccess() {
            require(['bnext/angularNavigator'], function(angularNavigator) {
                angularNavigator.navigate('menu/settings/document-settings/document-repository/' + $GET['nodo'])
            });
        }

        function deleteFolder() {
            var nodeId = +$GET.nodo;
            require(['core'], function(core) {
                core.dialog(i18n.messages_deleteNode, core.i18n.accept, core.i18n.cancel_label).then(function () {
                    require(['bnext/callMethod'], function(callMethod) {
                        callMethod({
                            url: 'Repositorio.action',
                            method: 'delete',
                            params: [nodeId]
                        }).then(function (result) {
                            if (result === 0) {
                                core.dialog(i18n.messages_deleteNodeSuccess).then(function () {
                                    require(['bnext/angularNavigator'], function(angularNavigator) {
                                        if (window.top.memoryNodes){
                                            window.top.memoryNodes = window.top.memoryNodes.filter( a => a.id !== nodeId);
                                        }
                                        angularNavigator.navigateLegacy('v.document.list.ae.view?nodo=' + dom.byId("nodoPadre").value);
                                    });
                                });
                            }
                            switch(result) {
                                case 1:
                                    core.dialog(i18n.messageStillFolder);
                                    break;
                                case 2:
                                    core.dialog(i18n.messageStillFiles);
                                    break;
                                case 3:
                                    core.dialog(i18n.messageStillImages);
                                    break;
                                case 4:
                                    core.dialog(i18n.messageStillHasPendingRequests);
                                    break;
                            }
                        });
                    });
                });
            });
        }
        function setColumns() {
            var statusList = [
                {name: i18n.nameStatus.none, icon: '', value: ''},
                {name: i18n.nameStatus.active, icon: gridCubes.green, value: 1},
                {name: i18n.nameStatus.active, icon: gridCubes.green, value: 2},
                {name: i18n.nameStatus.in_edition, icon: gridCubes.red, value: 0},
                {name: i18n.nameStatus.no_control, icon: gridCubes.deepGreen, value: -3},
                {name: i18n.nameStatus.no_control_eliminated, icon: gridCubes.red, value: -99}
            ];
            ref = {
                status: {
                    type: columnTypes.imageMap, list: statusList, value: 'value', name: 'name', key: 'icon', id: 'status',
                    paramters: ['id', 'description']
                },
                code: {id: 'code', type: 1, searchObj: {col: 1, type: 'texto'}},
                version: {id: 'version', type: columnTypes.text},
                title: {id: 'description', type: 1, searchObj: {col: 1, type: 'texto'}},
                author: {id: 'originador.description', type: 1, searchObj: {col: 1, type: 'texto'}},
                fechaEnt: {id: 'fechaHoraCreacion', type: columnTypes.date, searchObj: {type: 'fecha', col: 1}},
                fechaApr: {id: 'fechaUltimaModificacion', type: columnTypes.date, searchObj: {type: 'fecha', col: 1}},
                edit: {id: 'edit', type: columnTypes['function'], parameters: ['id'],
                    action: function (id) {
                        loader.showLoader().then(function() {
                            require(['bnext/angularNavigator'], function(angularNavigator) {                            
                                angularNavigator.navigateLegacy('v.documentos.view?vchState=Edit&intDocumentoId=' + id);
                            });
                        });
                    }
                }
            };
        }
        var gridSize = dom.byId('gridSize').value,
            control = {
                refresh: function(g) {
                    g.setCurrentPage(0);
                    g.refreshData();
                    loader.hideLoader();
                },
                setParam: function (g) {
                    control[g.id] = g;
                }
            }
        ;
        function openViewer (eve) {
            require(['bnext/jssor/jssorFunctions'], function(jssorFunctions) {
                var imgClicked = eve.target;
                grid_media = control['dataGrid_media'];
                var data = grid_media.getBean().data;
                // Se preparan los datos que requiere utilizar el visor
                jssorFunctions.setCountArrayTransitions(grid_media.getBean().count);
                var srcPreviews = [], srcMedia = [], selected;
                array.forEach(data, function (elem,idx) {
                    srcPreviews.push(require.toUrl('../DPMS/PreviewMedia.action?galleryId=' + elem.id));
                    srcMedia.push(require.toUrl('../DPMS/DownloadMedia.action?galleryId=' + elem.id));
                    if(elem.id === imgClicked.id) {
                        selected = idx;
                    }
                });
                jssorFunctions.getArrayImages("slider", srcPreviews, srcMedia, selected, dom.byId('slider'));
                jssorFunctions.onHide = function (eve){
                    dom.byId("dataGrid_media").style.display="";
                };
                jssorFunctions.createSlider("slider",true);
                dom.byId("dataGrid_media").style.display="none";
            });
        }
        function renderThumb (obj) {
            // Se genera la imagen de thumbnail
            var imgInnerHTML = " onerror='this.src = \"../images/galeria/default-thumbnail.jpg\"' ";
            var src = require.toUrl('../DPMS/PreviewMedia.action?galleryId=' + obj.id);
            imgInnerHTML += "src=" + src;
            imgInnerHTML += " class='thumbnail' ";
            imgInnerHTML += " id='" + obj.id + "'";
            // Se inserta en una tabla para centrar el contenido
            var tableDom = domConstruct.create('table',{
                innerHTML:"<tr><td class='image-style'><img" + imgInnerHTML + "></img></td></tr>" 
            });
            on(tableDom,'click',openViewer);
            if (hasMediaDelete()) {
                // Se añade comando de borrar medio
                var imgDel = domConstruct.create('img',{
                    src: require.toUrl("bnext/images/" + gridIcons['remove']),
                    title:i18n.colNames.delete_,
                    alt:i18n.colNames.delete_,
                    'class':"imgDelete"
                },tableDom);
                on(imgDel,"click",function (eve) {
                    require(['core'], function(core) {
                        eve.preventDefault();
                        eve.stopPropagation();
                        dialogExecutioner(string.substitute(i18n.messages.imgDelMsg, {
                            title: obj.description
                        }), core.i18n.yes, core.i18n.no, 'Gallery.action', 'delete', [obj.id], i18n.messages.imgDelSuccess, i18n.messages.error, core.i18n.Validate.accept, tabs); 
                    });
                });
            }
            // Se añade comando de descargar medio
            var imgDown = domConstruct.create('img',{
                src: require.toUrl("bnext/images/" + gridIcons.download),
                title:i18n.colNames.download,
                alt:i18n.colNames.download,
                'class':"imgDownload"
            }, tableDom);
            on(imgDown,"click",function (eve){
                eve.preventDefault();
                eve.stopPropagation();
                window.location = '../view/v-visor-pdf.view?fileId=' + obj.fileId + "&originalForce=1";
            });
            // Se añade la información del medio
            var ul = domConstruct.create('ul',{'class':"ulTexto"},tableDom);
            domConstruct.create('li',{
                innerHTML: obj.code,
                'class': "liPrincipal"
            }, ul);
            domConstruct.create('li',{
                innerHTML: obj.description,
                'class':"liSecundario"
            }, ul); 
            // Se genera una div flotante
            var div = domConstruct.create('div',{
                'class': "divOculto"
            }, tableDom);
            var ul = domConstruct.create('ul',null,div);
            domConstruct.create('li',{
                innerHTML: obj.code,
                'class':"liOculto"
            }, ul);
            domConstruct.create('li',{
                innerHTML: obj.description,
                'class':"liOculto"
            }, ul);
            
            return tableDom;
        }
        function renderThumbExcel(obj) {
            return obj.description + '.' + obj.extension;
        }
        setEvents();
        setColumns();
        setColNames();
        var 
            statusAction = function() {};
        ;
        if (accessRedirect) {
            statusAction = function (id, title, code) {
                require(['core'], function(core) {
                    return core.gridDialogExecutioner({
                        url: 'Document.action',
                        method: 'toggleStatus',
                        params: [id],
                        message: string.substitute(i18n.toggleMsg, {title: title||'', code: code||''}),
                        gridOrTabs: tabs,
                        sucessCallBack: function (result) {
                            var 
                                tipo = result.jsonEntityData.tipo,
                                requestCode = result.jsonEntityData.requestCode
                            ;
                            return core.dialog(string.substitute(i18n[tipo], {requestCode: requestCode}), i18n.messages.accept);
                        },
                        errorCallBack: function () {
                            return core.dialog(i18n.messages.error, i18n.messages.accept);
                        }
                    }); 
                });
            };
        }
        function editAction(id, status) {
            loader.showLoader().then(function() {
                require(['bnext/angularNavigator'], function(angularNavigator) {
                    angularNavigator.navigateLegacy('v.documentos.view?vchState=Edit&intDocumentoId=' + id);
                });
            });
        }

        function viewDoc(row, nodeTd, trDom) {
            var iconSrc = require.toUrl('bnext/images/' + gridIcons.play);
            var viewAnchor = domConstruct.create('a', {
                'href': 'javascript:void(0)',
                'class': 'edit-rendered-cell',
                'innerHTML': '<img border=0 src="' + iconSrc + '"/>'
            });
            domClass.add(nodeTd, 'cursorPointer');
            var contentTypeSupported = dom.byId('serializedVideoCompatibilityTypes').value.replace(/\\/g,"");
            nodeTd.onclick = function () {
                if(!contentTypeSupported.includes(row.contentType)){
                    require(['core'], function(core) {
                        core.dialog(i18n.messagePreviewVideo, i18n.download, i18n.messages.cancel).then(
                            function(){
                                window.location = '../view/v-visor-pdf.view?fileId=' + row.fileId + "&originalForce=1";
                            },
                        ); 
                    });
                    return;
                }
                require(['bnext/angularNavigator'], function(angularNavigator) {
                    var viewerUrl = 'v-document-viewer.view' 
                    + '?id=' + row.id
                    + '&playVideo=true';
                    angularNavigator.showDocumentViewer(viewerUrl);
                });
            };
            trDom.title = i18n.colNamesPlay + ': "' + row.description + '"';
            return viewAnchor;
        }
        var mediaCols=[], videoCols = [], folderCols = [], cType = gcUtil.cType;
        gcUtil.column(mediaCols)
            .push("thumb", i18n.colNames.media, cType.RenderCell(renderThumb, null, renderThumbExcel))
            .searchField("code", i18n.colNames.code, cType.Text())
            .searchField("description", i18n.colNames.title, cType.Text())
        ;
        
        function deleteMediaF(id) {
            require(['core'], function(core) {
                core.dialog(i18n.messages.deleteMediaMsg, i18n.messages.accept, i18n.messages.cancel).then(function () {
                    loader.showLoader().then(function() {
                        require(['bnext/callMethod'], function(callMethod) {
                            var args = {
                                url: 'Gallery.action',
                                method: 'delete',
                                params: [id]
                            };
                            callMethod(args).then(function (resultado) {
                                loader.hideLoader();
                                if (+resultado !== 0) {
                                    core.dialog(i18n.messages.deleteMediaSuccess, i18n.messages.accept).then(function () {
                                        var grid_video = tabs.tabs.find((element) => element.title === i18n.colNamesVideo);
                                        if (grid_video && grid_video.grid) {
                                            if (typeof grid_video.grid.refreshData === 'function') {
                                                grid_video.grid.refreshData();
                                            }
                                        }
                                    });
                                }
                            }, function (e) {
                                core.dialog(e, i18n.messages.accept);
                            });
                        });
                    });
                });
            });
        }
        
        function hasMediaDelete() {
            return isAdmin || documentManager || documentEditor;
        }
        
        var colTypeView = cType.RenderCell(viewDoc), deleteMedia = cType().Function(['id'], deleteMediaF, gridIcons['trash']);
        colTypeView.isSortable = true;
        colTypeView.idValue = 'extension';
        gcUtil.column(videoCols)
            .push('view', i18n.colNamesPlay, colTypeView, null, '96px')
            .push("code", i18n.colNames.code, cType.Text())
            .push("description", i18n.colNames.title, cType.Text());
        if (hasMediaDelete()) {
            gcUtil.column(videoCols)
                .push('delete', i18n.colNames.delete_, deleteMedia, null, '60px');
        }
        
        var viewIconSrc = require.toUrl('bnext/images/' + gridIcons.view);
        gcUtil.column(folderCols)
                .push('explore', i18n.colNameFolderExplore, cType.RenderCell(function (row, nodeTd, trDom, tableDom) {
                    var viewAnchor = domConstruct.create('a', {
                        href: 'javascript:void(0)',
                        'class' : 'edit-rendered-cell',
                        onclick: function() {
                            require(['bnext/module/util/folder-utils'], function(FolderUtils) {
                                FolderUtils.navigate(row);
                            });
                        },
                        innerHTML: '<img border=0 src="' + viewIconSrc + '"/>'
                    });
                    trDom.title = i18n.colNameFolderExplore;
                    return viewAnchor;
                }), null, '65px')
            .push("description", i18n.colNameFolderName, cType.Text())
            .push("path", i18n.colNameFolderPath, cType.Text())
        ;
        grid_reference = {
            size: dom.byId('gridSize').value, id: "dataGrid_reference", container: "dataGrid_reference", searchContainer: "#auto",
            resultsInfo: "#auto", paginationInfo: "#auto", methodName: "getRowsReferencedByNode",
            serviceStore: "Document.action?currentEntityId=" + $GET.nodo + "&currentEntityId=" + $GET.repositorio,
            windowPath: dom.byId('windowPath').value,
            columns: [
                ref.status, ref.code, ref.version, ref.title, ref.author, ref.edit
            ]
        };
            
        var defMedia = {
            size: gridSize,
            container: 'dataGrid_media',
            serviceStore: "Gallery.action?currentEntityId=" + $GET.nodo,
            methodName: "getRowsByNode",
            onClickSearch:function (){
                dom.byId("slider").style.display = "none";
            },
            fullColumns: mediaCols,
            noHeader: true,
            onLoaded:function(){
                require(['core'], function(core) {
                    core.setLang('lang/documentos/nls/document.list.ae');
                    if (!dom.byId("slider")) {
                        domConstruct.create("div",{
                            id:"slider",
                            style:{display:"none"}
                        },dom.byId("dataGrid_media").parentNode);
                    }
                });
            }
        };
        var defFolders = {
            size: gridSize,
            container: 'dataGrid_folders',
            serviceStore: "Repositorio.action?currentEntityId=" + $GET.nodo,
            methodName: "getNodes",
            fullColumns: folderCols
        };
                
        var defMediaVideos = {
            size: gridSize,
            container: 'dataGrid_mediaVideos',
            serviceStore: "Gallery.action?currentEntityId=" + $GET.nodo,
            methodName: "getRowsVideosByNode",
            fullColumns: videoCols
        };
        var configMenuItems = [];
        GridMenuUtil.menu(configMenuItems, DocumentMenuItem)
                .push(DocumentMenuItem.LINK_FOLDER, newMediaLink, true);
        if (topLevel !== true && (documentManager || isAdmin)) {
            GridMenuUtil.menu(configMenuItems, DocumentMenuItem)
                    .push(DocumentMenuItem.DELETE_FOLDER, deleteFolder, true)
                    .push(DocumentMenuItem.CONFIGURE_FOLDER, configureUserAccess, true)
                    .push(DocumentMenuItem.MOVE_FOLDER, moveFolder, true);
        }
        require(['bnext/module/grid-menu'], function(GridMenu) {
            var configMenu = new GridMenu({
                id: 'configMenu',
                type: DocumentMenuItem,
                leftClickToOpen: true
            });
            configMenu.build(dom.byId('moreOptions'), configMenuItems);
        });
        documentSimpleList({
            container: "dataGrid_control",
            serviceStore: "OptMasterList.action?currentEntityId=" + $GET.nodo + "&currentEntityId=" + $GET.repositorio,
            methodName: "getRowsByNodeAE",
            menuItems: [],
            windowPath: dom.byId('windowPath').value,
            includeStatus: true,
            reapprove: dom.byId("onlyDocumentManagerReapprove").value,
            docRole: dom.byId('documentRole').value
        }, editAction, statusAction).then(function(docList) {
            docList.prepareColumns();
            if (accessRedirect) {
                GridMenuUtil.menu(docList.menuItems, DocumentMenuItem)
                    .push(DocumentMenuItem.MOVE_DOCUMENT, function(row) {
                        redirectNode(row.id, i18n);
                    });
            }
            gcUtil.column(docList.columns);
            var defDocSimple = docList.initialize();
            function docSipmleThen (grid) {
                saveRequestHandleNEW = new saveRequestHandle(grid);
                loader.hideLoader();
            }
            
            all([defDocSimple]).then(lang.hitch(this, function (grids) {
                var configDocSimple = grids[0];
                var gridDoc = configDocSimple.grid;
                var menuDoc = configDocSimple.menu;
                var grid_media = defMedia,
                grid_video = defMediaVideos,
                grid_folder = defFolders;
               
                tabs = new Tabs({
                    tabs: [
                        {title: i18n.tabNameDocument, grid: gridDoc},
                        {title: i18n.colNameFolders, gridConf: grid_folder},
                        {title: i18n.colNames.reference, gridConf: grid_reference},
                        {title: i18n.colNames.media, gridConf: grid_media},
                        {title: i18n.colNamesVideo, gridConf: grid_video}
                    ],
                    margin: '5px',
                    onSelectTab: function (li, grid) {
                        switch (grid.getContainer()) {
                            case 'dataGrid_media':
                                if (!control['dataGrid_media']) {
                                    control.setParam(grid);
                                }
                                if (typeof grid_media.getBean !== 'function'){
                                    grid_media = grid;
                                }
                                break;
                            case 'dataGrid_mediaVideos':
                                if (typeof grid_video.getBean !== 'function'){
                                    grid_video = grid;
                                }
                                break;
                            case 'dataGrid_reference':
                                if (typeof grid_reference.getBean !== 'function'){
                                    grid_reference = grid;
                                }
                                break;
                            case 'dataGrid_folders':
                                if (typeof grid_folder.getBean !== 'function'){
                                    grid_folder = grid;
                                }
                                break;
                        }
                        if(dom.byId("slider")) {
                            dom.byId("slider").style.display = "none";
                        }
                        if (menuDoc && typeof menuDoc.reset === 'function') {
                            menuDoc.reset();
                        }
                    },
                    defaultWidth: 'auto',
                    searchNode: 'search',
                    paginationNode: 'pagination',
                    pageSize: dom.byId('gridSize').value
                }, 'tabNode');
                require(['bnext/special/handler!'], loader.hideLoader);
                docSipmleThen(gridDoc);
            }));
        });
        function setColNames() {
            ref.version.title = i18n.colNamesVersion;
            ref.author.title = i18n.colNamesAuthor;
            ref.edit.title = i18n.colNames.edit;
            ref.status.title = i18n.colNames.status;
            ref.code.title = i18n.colNames.code;
            ref.title.title = i18n.colNames.title;
        }
        
        function redirectNode(id) {
            require(['bnext/angularNotice', 'bnext/angularFolders', 'core'], function (angularNotice, angularFolders, core) {
                angularFolders.move(id).then(
                        function (result) {
                            tabs.refresh();
                            if (result === 'success') {
                                angularNotice.notice(i18n.messages.moveDocumentSuccess);
                            } else {
                                angularNotice.notice(i18n.messages.stillMoveDocument);
                            }
                        },
                        function (error) {
                            core.dialog(error, core.i18n.Validate.accept);
                        }
                );
            });
        }
       
        function moveFolder() {
            require(['bnext/angularFolders', 'core'], function (angularFolders, core) {
                angularFolders.open($GET['nodo']).then(
                    function (result) {
                        if (result === 'success') {
                            core.dialog(i18n.messages.moveFolderSuccess, core.i18n.Validate.accept).then(function () {
                                tabs.refresh();
                            });
                        } else {
                            core.dialog(i18n.messages.stillMoveFolder, core.i18n.Validate.accept).then(function () {
                                tabs.refresh();
                            });
                        }
                    },
                    function (error) {
                        core.dialog(error, core.i18n.Validate.accept);
                    }
                );
            });
        }
       
        // Mostramos la sección de botones flotantes
        var menuOptions = [{ text: i18n.newRecord, value: 'newRecord', iconName: 'description', title: i18n.newRecordTitle }];
        if (isAdmin || documentEditor || documentManager) {
           menuOptions.push({ text: i18n.newFolder, value: 'newFolder', iconName: 'folder', title: i18n.newFolderTitle });
        }
        menuOptions.push({ text: i18n.newMedia, value: 'newMedia', iconName: 'video_library', title: i18n.newMediaTitle });
        require(['bnext/angularFabMenu'], function(angularFabMenu) {
            angularFabMenu.addFabMenu(menuOptions);
            angularFabMenu.doneAction = function (result) {
                toggleAction(result);
                angularFabMenu.displayFabMenu(false);
            }; 
        });
        // Acciones de los botones flotantes
        function toggleAction(option) {
            switch (option.value) {
                case 'newRecord':
                    newDocument();;
                break;
                case 'newFolder':
                    registry.byId('new_folder_dialog').show();
                    registry.byId('new_folder_dialog').set('title', i18n.label.add_folder);
                break;
                case 'newMedia':
                    newMedia();;
                break;
            }
        }
    }
    function folderOptions() {
        var def = new Deferred();
        require(['bnext/callMethod'], lang.hitch(this, function(callMethod) {
            callMethod({
                url: 'Node.action', 
                method: 'load',
                params: [dom.byId('nodo').value]
            }).then(lang.hitch(this, function (result) {
                if (dom.byId('window_title_repository')) {
                    dom.byId('window_title_repository').innerText = "" + (result.path || '');
                }
                dom.byId("nodoPadre").value = result.parent;
                folderName = result.description;
                folderPath = result.path;
                if (result.topLevel === 1) {
                    def.resolve(true);
                } else {
                    def.resolve(false);
                }
                require(['bnext/module/util/folder-utils'], function(FolderUtils) {
                    FolderUtils.loadNodes(false).then(function (nodesModel) {
                        folderNavigation(nodesModel?.store?.data ?? [], result);
                    });
                });
            }), def.resolve);
        }));
        return def;
    }
      
    function folderNavigation(folderStore, result) {
        /* Creación del Folder container */
        var container = domConstruct.create('div', {
            'class': "folder-container"
        }, domNav, 'first');
        var node = domConstruct.create('a', {
            innerHTML: result.code || result.title,
            href: "../view/v.document.list.ae.view?nodo=" + result.id
        }, container);
        if (!result && result.parent === null) {
            return;
        }
        /* Búsqueda de los subfolders */
        var nodeSonsSearch = array.filter(folderStore, function (elem) {
                return elem.parent === result.id && elem.id.toString().match(/^[0-9]+$/);
            });
        if (nodeSonsSearch && nodeSonsSearch.length > 0) {
            domClass.add(node, 'has-childs');
            /* Creación del contenedor de los folders hijos */
            var nodeSonsContainer = domConstruct.create('div', {
                'class': "subfolder-container"
            }, node, 'after');
            /* Se crea un dropdown */
            var nodeSonsMenu = new DropDownMenu({
                'class': "subFolderMenu"
            });
            /* Se crea un botón para el dropdown y se agrega al contenedor de los folders hijos*/
            nodeSonsMenu.startup();
            var button = new DropDownButton({
                dropDown: nodeSonsMenu,
                'class': "buttonDropMenu"
            });
            button.startup();
            button.placeAt(nodeSonsContainer, 0);
            on.once(button, 'click', function () {
                /* Se agregan los subfolders al dropdown */
                array.forEach(nodeSonsSearch, function (son) {
                        var menuItem = new MenuItem({
                            label: son.title,
                            'class': "subFolderItem",
                            onClick: function () {
                                require(['bnext/angularNavigator'], function(angularNavigator) {
                                    angularNavigator.navigateLegacy('v.document.list.ae.view?nodo=' + son.id);
                                });
                            }
                        });
                        nodeSonsMenu.addChild(menuItem);
                });    
            });
        } else {
            domClass.add(node, 'has-not-childs');
        }
        /* Se realiza la búsqueda del folder padre */
        if (result.parent === null || result.readonly) {
            domClass.add(node, 'displayNone');
            return;
        }
        var nodeParentSearch = array.filter(folderStore, function (elem) {
                return elem.id === result.parent;
            });
        if (nodeParentSearch.length === 0) {
            return;
        }
        folderNavigation(folderStore, nodeParentSearch[0]);
    }
    
    function goBack() {
        require(['bnext/angularNavigator'], function(angularNavigator) {
            angularNavigator.navigate('menu/documents/my-repository');
        });
    }
    folderOptions().then(function(topLevel) {
        i18nUtil.setLang('lang/documentos/nls/document.list.ae').then(function(i18n) {
            doEverything(i18n, topLevel);
        });
    });
});