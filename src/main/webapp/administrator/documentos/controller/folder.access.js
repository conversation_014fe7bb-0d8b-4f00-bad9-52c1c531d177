require([
    'core', 'dojo/on', 'dojo/dom', 'bnext/callMethod', 'bnext/gridComponent',
    'dijit/registry', 'dojo/dom-attr',
    'bnext/saveHandle', 'entity/nodeAccess', 'dojo/_base/array', 'bnext/gridComponentUtil',
    'bnext/gridIcons', 'dojo/dom-class', 'loader',
    'bnext/administrator/pending/Module', 'bnext/angularNavigator', 'bnext/LinkedGridFactory',
    'dijit/Dialog', 'dojo/domReady!'
  ],
  function(
    core, on, dom, callMethod, gridComponent,
    registry, domAttr,
    sH, nodeAccess, array, gcUtil,
    gridIcons, domClass, loader,
    Module, angularNavigator, LinkedGridFactory
  ) {
    var module = Module.DOCUMENT;
    var windowPath = dom.byId('windowPath').value;
    var urlParams = core.getUrlVars(windowPath);
    var isSurveyAnswers = windowPath.contains('v-form-answers-access.view');
    var misDatos;
    var grid_user;
    var grid_department;
    var grid_process;
    var grid_businessUnit;


    function doEverything(i18n) {
      function sendBackToFolder() {
        angularNavigator.navigateLegacy('v.document.list.ae.view?nodo=' + dom.byId('id').value);
      }

      function cancel() {
        core.dialog(i18n.cancel_message, core.i18n.Validate.yes, core.i18n.Validate.no).then(function() {
          if (module === Module.CONFIGURATION) {
            if (urlParams['module'] !== null && typeof urlParams['module'] !== 'undefined') {
              angularNavigator.navigateLegacy('v-' + urlParams['module'] + '-report-list.view');
            } else if (windowPath.contains(Module.TIMESHEET)) {
              angularNavigator.navigate('menu/timesheet/reports');
            } else if (windowPath.contains('v-form-report-access.view')) {
              angularNavigator.navigate('menu/forms/reports');
            } else if (windowPath.contains('v-timework-report-access.view')) {
              core.navigate('menu/timework/reports');
            } else if (windowPath.contains('v-activity-report-access.view')) {
              core.navigate('menu/activities/reports');
            } else {
              core.goBackHistory();
            }
          } else {
            if (isSurveyAnswers) {
              angularNavigator.navigate('menu/forms/answers');
            } else {
              sendBackToFolder();
            }
          }
        });
      }

      function clean() {
        grid_user.getGroundGrid().clearData();
        grid_department.getGroundGrid().clearData();
        grid_process.getGroundGrid().clearData();
        grid_businessUnit.getGroundGrid().clearData();
      }

      function updateSaveFolder(gsh) {
        if (!gsh.validCallback || gsh.jsonEntityData.error) {
          return core.error(gsh);
        }
        if (!gsh.operationEstatus) {
          return core.fracasoMsg(gsh.errorMessage);
        }
        core.hideLoader();
        if (module === Module.CONFIGURATION) {
          if (urlParams['module'] !== null && typeof urlParams['module'] !== 'undefined') {
            core.dialog(i18n.confirmConfigurationSaveUpdate).then(function() {
              angularNavigator.navigateLegacy('v-' + urlParams['module'] + '-report-list.view');
            });
          } else if (windowPath.contains(Module.TIMESHEET)) {
            core.dialog(i18n.confirmTimesheetSaveUpdate).then(function() {
              angularNavigator.navigate('menu/timesheet/reports');
            });
          } else if (windowPath.contains('v-form-report-access.view')) {
            core.confirm(i18n.confirmFormularieSaveUpdate).then(function() {
              angularNavigator.navigate('menu/forms/reports');
            });
          } else if (windowPath.contains('v-timework-report-access.view')) {
            core.dialog(i18n.confirmConfigurationSaveUpdate).then(function() {
              angularNavigator.navigate('menu/timework/reports');
            });
          } else if (windowPath.contains('v-activity-report-access.view')) {
            core.dialog(i18n.confirmConfigurationSaveUpdate).then(function() {
              angularNavigator.navigate('menu/activities/reports');
            });
          } else {
            core.dialog(i18n.confirmConfigurationSaveUpdate).then(goBackHistory);
          }
        } else if (isSurveyAnswers) {
          core.confirm(i18n.confirmAnswersSaveUpdate).then(function() {
            angularNavigator.navigate('menu/forms/answers');
          });
        } else {
          core.dialog(i18n.confirmSaveUpdate).then(sendBackToFolder);
        }
      }

      misDatos = new sH({
        savedObjName: 'misDatos',
        serviceStore: 'Node.action',
        methodName: 'save',
        saveCallback: updateSaveFolder,
        noCntrlBtn: true
      });
      var data = {};

      function defineValidationRules() {
        $('#validate_form').validate({
          rules: {
            description: {
              required: true
            }
          },
          messages: {
            description: i18n.Validate.descriptionRequired
          },
          errorPlacement: function(error, element) {
            error.insertBefore(element);
          }
        });
      }

      function save() {
        if ($('#validate_form').valid()) {
          core.confirmDialog(dom.byId('id').value).then(function() {
            showLoader(core.i18n.Validate.saving);
            data = nodeAccess;
            data['id'] = dom.byId('id').value;
            data['code'] = dom.byId('description').value;
            var businessUnits = [];
            array.forEach(grid_businessUnit.getGroundData(), function(item) {
              var businessUnit = {};
              businessUnit.id = item.id;
              businessUnits.push(businessUnit);
            });
            data['businessUnits'] = businessUnits;
            var departments = [];
            array.forEach(grid_department.getGroundData(), function(item) {
              var department = {};
              department.id = item.id;
              departments.push(department);
            });
            data['departments'] = departments;
            var processes = [];
            array.forEach(grid_process.getGroundData(), function(item) {
              var process = {};
              process.id = item.id;
              processes.push(process);
            });
            data['process'] = processes;
            var users = [];
            array.forEach(grid_user.getGroundData(), function(item) {
              var user = {};
              user.id = item.id;
              users.push(user);
            });
            data['users'] = users;
            data['module'] = module;
            misDatos.setData(data);
            loader.showLoader().then(function() {
              misDatos.saveData();
            });
          }, core.hideLoader);
        }
      }

      function populate(object) {
        dom.byId('description').value = object.description;
        if (object.module === Module.CONFIGURATION) {
          module = Module.CONFIGURATION;
          dom.byId('mainTitle').innerHTML = i18n.configurationTitle;
          dom.byId('lblDescription').innerHTML = i18n.configurationName;
          domClass.add(dom.byId('cleanBtn'), 'displayNone');
          domAttr.set(dom.byId('description'), 'disabled', 'disabled');
        }
        if (isSurveyAnswers) {
          dom.byId('mainTitle').innerHTML = i18n.answersTitle;
          dom.byId('lblDescription').innerHTML = i18n.answersName;
          domAttr.set(dom.byId('description'), 'disabled', 'disabled');
        }
        //Se mantiene oculto el titulo hasta que termina de validarse en que modulo se esta mostrando la pantalla
        // y el titulo con su traduccion ya ha sido cargada, y se muestra.
        dom.byId('mainTitle').style.display = 'block';
      }

      function load(id) {
        var args;
        if (isSurveyAnswers) {
          args = {
            url: '../DPMS/Document.action',
            method: 'getDocumentAccessDetails',
            params: [id || -1]
          };
        } else {
          args = {
            url: '../DPMS/Node.action',
            method: 'getNodeAccessDetails',
            params: [id || -1]
          };
        }
        callMethod(args).then(function(result) {
          populate(result);
          core.hideLoader();
        });
      }

      var cType = gcUtil.cType;
      var lDescription = i18n.Validate.description;
      var lCode = i18n.Validate.code;
      var columnsUsers = [];
      gcUtil.column(columnsUsers)
        .push('avatarId', i18n.colNamesAvatar, cType.RenderAvatar('id'), null, '30px')
        .push('code', lCode, cType.Text())
        .push('account', i18n.account, cType.Text())
        .push('correo', i18n.mail, cType.Text())
        .push('description', i18n.fullName, cType.Text());

      var columnsDepartment = [];
      gcUtil.column(columnsDepartment)
        .push('code', lCode, cType.Text())
        .push('description', lDescription, cType.Text());

      var columnsProcess = [];
      gcUtil.column(columnsProcess)
        .push('code', lCode, cType.Text())
        .push('description', lDescription, cType.Text());

      var columnsBusinessUnit = [];
      gcUtil.column(columnsBusinessUnit)
        .push('code', lCode, cType.Text())
        .push('description', lDescription, cType.Text());

      grid_user = new LinkedGridFactory.create({
        id: 'node_access_users',
        dialogClassName: 'fullSizeScreen',
        addButton: dom.byId('addUserBtn'),
        allButton: false,
        buttons: []
      }, dom.byId('id').value, 'Node.action', i18n, columnsUsers);

      grid_department = new LinkedGridFactory.create({
        id: 'node_access_departments',
        dialogClassName: 'fullSizeScreen',
        addButton: dom.byId('addDepartmentBtn'),
        allButton: false,
        buttons: []
      }, dom.byId('id').value, 'Node.action', i18n, columnsDepartment);

      grid_process = new LinkedGridFactory.create({
        id: 'node_access_area',
        dialogClassName: 'fullSizeScreen',
        addButton: dom.byId('addProcessBtn'),
        allButton: false,
        buttons: []
      }, dom.byId('id').value, 'Node.action', i18n, columnsProcess);

      grid_businessUnit = new LinkedGridFactory.create({
        id: 'node_access_business_unit',
        dialogClassName: 'fullSizeScreen',
        addButton: dom.byId('addBusinessUnitBtn'),
        allButton: false,
        buttons: []
      }, dom.byId('id').value, 'Node.action', i18n, columnsBusinessUnit);

      load(dom.byId('id').value);
      defineValidationRules();
      core.hideLoader();

      on(dom.byId('saveBtn'), 'click', save);
      on(dom.byId('cancelBtn'), 'click', cancel);
      on(dom.byId('cleanBtn'), 'click', clean);
    }

    core.setLang('lang/documentos/nls/folder.access').then(doEverything);
  });