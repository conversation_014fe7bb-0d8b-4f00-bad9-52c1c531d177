<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import="Framework.Config.Utilities"%>
<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<s:set var="pageNum" value="pageNum" />
<s:set var="fileId" value="fileId" />
<s:set var="documentId" value="documentId" />
<s:set var="attendant" value="attendant" />
<%
    Integer showPositions = Utilities.getSettings().getShowPositionsForCopies(); 
%>
<html class="html-reset loading" translate="no">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge" >
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">

        <title>${applicationInfo} - Visor PDF</title>
        <link rel="shortcut icon" href="../images/favicon.ico?${systemVersion}" />
        <%@include file="../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" href="../styles/PDF_viewer.css?${systemVersion}">
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script type='text/javascript' src="../scripts/PDF_viewer.js?${systemVersion}"></script>
        <style>
            .container_header{
                background-color: ${systemColor};
            }
            .Button {
                color: ${systemColor};
                background-color: ${systemColorTextColor};
            }  
            <s:if test="viewFinder">
                .printTools {
                    margin-left: 103px;
                    line-height: 16px;
                }
            </s:if>
            <s:if test="bnextLogo == true">
                .page_container {
                    text-align: left;
                    background: transparent;
                    border: 0px;
                    margin-top: 15px;
                }

                .page_container.landscape,
                .page_container.landscape .page_img,
                .page_container.landscape  {
                    max-height: 97%!important;
                }

                .page_container.portait,
                .page_container.portait .page ,
                .page_container.portait .page_img {
                    max-width: 97%!important;
                }

                .page {
                    box-shadow: 0 0;
                    border: 0px;
                    background: transparent;
                }
                .bnext-viewfinder .pageTools .Button.selector,
                body.loading .pageTools .Button.slector,
                .bnext-viewfinder #controlled_copy,
                .bnext-viewfinder #uncontrolled_copy {
                    visibility: hidden;
                }
                .bnext-viewfinder  #more_vert {
                    visibility: visible;
                }
                .printTools {
                    display: none;
                }
            </s:if>
            <s:if test="hasPassword">
                .has-password-area {
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    gap: 2rem;
                }
                .has-password-form {
                    display: flex;
                    background-color: transparent;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    gap: 1rem;
                }

                .has-password-form input[type=submit] {
                    background-color: white!important;
                }
                .has-password-message {
                    font-size: 2rem;
                    color: white;
                }
            </s:if>

            button.rotate.selector .material-icons{
                position: relative;
                margin-left: 275px;
                font-size: 30px !important;
                margin-top: 0.625rem;
                margin-bottom: -0.625rem;
                color:darkorange;
            }
        </style>
        <script type="text/javascript">
            var pdfGlobalConfiguration = {
                includeTitle: false,
                viewFinder: ${viewFinder},
                floatingGridSize: ${floatingGridSize},
                fileId: ${fileId == null ? -1 : fileId},
                pdfSha512:  '${pdfSha512 == null ? "-" : pdfSha512}',
                pdfImageDpi: ${pdfImageDpi == null ? "'-'" : pdfImageDpi},
                pdfViewerFitDocumentScreen:  ${pdfViewerFitDocumentScreen == null ? true : pdfViewerFitDocumentScreen},
                documentId: ${documentId == null ? -1 : documentId},
                hasLogo: ${bnextLogo != null && bnextLogo == true && existsDefaultFileId == true},
                pages: ${pageNum != null && pageNum > 0 ? pageNum : 0},
                hasPages: ${pageNum != null && pageNum > 0},
                hasUncontrolledCopies: ${activeDocument && documentCopyAvailable && uncontrolled_copy_access},
                hasControlledCopies: ${activeDocument && attendant},
                hasNavigationPanel: ${pageNum != null && pageNum > 0 && documentCopyAvailable != false},
                printableDocument: ${activeDocument && printableDocument}
            };
            <s:if test="viewerToken">
            pdfGlobalConfiguration.targetUrl = "v-document-viewer-link.view?reading=true&token=${token}&pageNum=:num&sha512=${pdfSha512}&dpi=${pdfImageDpi}",
            pdfGlobalConfiguration.pageNum = +'${pageNum}' || 0;
            pdfGlobalConfiguration.versionMessage = ${versionMessage};
            </s:if>
            <s:else>
            pdfGlobalConfiguration.targetUrl = "v-file-pdf-page.view?pdfPageId=:id&fileId=${fileId}&pageNum=:num&sha512=${pdfSha512}&dpi=${pdfImageDpi}";
            </s:else>
            pdfGlobalConfiguration.pageNum = +'${pageNum}' || 0;
            <s:if test="pageNum != null && pageNum > 0">
            pdfGlobalConfiguration.previewUrl = 'v-pdf-page-preview.view?id=:id&fileId=${fileId}&pageNum=:num&sha512=${pdfSha512}&dpi=${pdfImageDpi}';
            pdfGlobalConfiguration.pageIds = ${pdfPagesSerialized};
            </s:if>
        </script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="bnext-viewfinder" onbeforeprint="alert('Printing is not allowed'); document.write();">
        <%@include file="../../components/loader.jsp" %>
        <div class="container_header">
            <div id="container_selector">
                <span class="pageTools">
                    <div id="rotate_left" class="Button rotate selector btn_rounded outlined-button64em"
                         tabindex="0">
                        <a class="material-icons">rotate_left</a> 
                    </div>
                    <div id="zoomin" class="Button selector btn_rounded outlined-button64em"
                         tabindex="0">
                        <a class="material-icons">zoom_in</a> 
                    </div>
                    <div id="zoomlevel" class="Button selector normal btn_rounded outlined-button64em" style="width:110px;">
                        <input type="number"  min="25" id="zlevel" value="150"
                               ondragstart="return false" onselectstart="return false" step="25" >
                        <span>%</span>
                    </div>
                    <div id="zoomout" class="Button selector btn_rounded outlined-button64em"
                         tabindex="0">
                        <a class="material-icons">zoom_out</a>
                    </div>
                    <div id="rotate_right" class="Button rotate selector btn_rounded outlined-button64em"
                         tabindex="0">
                        <a id="rotate_right_img" class="material-icons">rotate_right</a>
                    </div>
                    <div id="spinner" class="Button selector normal btn_rounded outlined-button64em" style="width:110px;">
                        <input type="number" max="${pageNum}" min="1" id="page_top" value="1"
                               ondragstart="return false" onselectstart="return false" >
                        <span id="of" style="line-height:22px;"></span><span style="line-height:22px;">${pageNum} </span>
                    </div> 
                    <s:if test="pageNum != null && pageNum > 0 && viewerToken == false && documentCopyAvailable != false">
                        <div id="navigation_preview" class="Button selector normal btn_rounded outlined-button64em">
                            <a class="material-icons">apps</a>
                            <span id="navigation_preview_btn"></span>
                        </div>   
                    </s:if> 
                    <div id="more_vert" class="Button rotate selector btn_rounded outlined-button64em"
                         tabindex="0">
                        <a id="more_vert_img" class="material-icons">more_vert</a>
                    </div>
                    <div class="dropdown-content" id="dropDownMoreOptions">
                    <div id="navigation_preview_menu" class="dropdown-more-options-item" tabindex="0" style="display:none">
                        <div>
                            <a id="navigation_preview_menu_btn">
                                <div class="material-icons">apps</div>
                                <span>
                                    Navegación
                                </span>
                            </a>
                        </div>
                    </div>  
                    <s:if test="(activeDocument && ((attendant && documentControlledCopyAvailable) || (documentCopyAvailable && uncontrolled_copy_access)))"> 
                        <s:if test="attendant && documentControlledCopyAvailable">
                        <div id="controlled_copy" class="dropdown-more-options-item" tabindex="0">
                            <div>
                                <a id="controlled_copy_btn">
                                    <div class="material-icons">print</div>
                                    <span>
                                        Copias controladas
                                    </span>
                                </a>
                            </div>
                        </div>
                        <span style="margin-bottom:2px; display:block;"></span>
                        </s:if>
                        <s:if test="uncontrolled_copy_access">
                        <div id="uncontrolled_copy" class="dropdown-more-options-item" tabindex="0">
                            <div>
                                <a id="uncontrolled_copy_btn">
                                    <div class="material-icons viewFinder_${viewFinder}">print</div>
                                    <span>
                                        Copias no controladas
                                    </span>
                                </a>
                            </div>
                        </div>
                        <span style="margin-bottom:2px; display:block;"></span>
                        </s:if>
                        <s:if test="quickPrintEnabled == 1">
                            <div id="simple_copy" class="dropdown-more-options-item" tabindex="0">
                                <div>
                                    <a id="simple_copy_btn">
                                        <div class="material-icons viewFinder_${viewFinder}">print</div>
                                        <span>
                                            Impresión rápida
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </s:if>
                    </s:if>
                        <div id="closeVisorBtn" class="dropdown-more-options-item">
                            <div class="material-icons viewFinder_${viewFinder}">reply</div>
                            <span>
                                Ir a modo Bnext QMS
                            </span>
                        </div>
                        <span style="margin-bottom:2px; display:block;"></span>
                        <div id="fullScreenBtn" class="dropdown-more-options-item"  style="display: none;">
                            <div class="material-icons viewFinder_${viewFinder}">fullscreen</div>
                            <span>
                                Pantalla completa
                            </span>
                        </div>
                        <span style="margin-bottom:2px; display:block;"></span>
                        <div id="exitFullScreenBtns"  class="dropdown-more-options-item" style="display: none;">
                            <div class="material-icons viewFinder_${viewFinder}">fullscreen_exit</div>
                            <span>
                                Salir de pantalla completa
                            </span>
                        </div>
                        <span style="margin-bottom:2px; display:block;"></span>
                        <div id="exitVisorBtn" class="dropdown-more-options-item" style="display: none;">
                            <div class="material-icons viewFinder_${viewFinder}">exit_to_app</div>
                            <span>
                                Salir del sistema
                            </span>
                        </div>
                    </div>
                </span>
            </div>
        </div>
        <div class="unselectable hideControlledCopy hideUncontrolledCopy hidden-page-navigation" id="unselectable" unselectable="on">
            <s:if test="pageNum != null && pageNum > 0 && viewerToken == false">
                <div id="page_navigation" class="page-navigation fancy-scroll leftMenu">
                    <ul class="content_area">
                        <li class="header titlePageNavigation">
                            <span id="titlePageNavigation"></span>
                        </li>
                        <li class="separator-li"></li>
                        <li class="separator-li">
                            <div class="separator-title">
                                <c:forEach var="page" begin= "0" end="${pageNum - 1}">
                                    <div class="page-preview">
                                    <img src="data:image/jpeg;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                             id="preview-${page}"
                                             loading="lazy"
                                             class="lazy-image preview pdf-page-preview pdf-page-preview-${page}"
                                             data-page="${page}"
                                             tabindex="0"
                                             alt="Page ${page + 1}"
                                             title="Page ${page + 1}"/>
                                        <label>${page + 1}</label>
                                    </div>
                                </c:forEach>
                            </div>
                        </li>
                    </ul>
                </div>
            </s:if>
            <s:if test="activeDocument && attendant">
                <div class="leftMenu fancy-scroll" id="leftMenu">
                    <!-- leyenda cerrar nueva -->
                    <s:if test="uncontrolled_copy_access">
                        <button class="button rotate selector" type="button" id="close_controlled_copy"/>
                        <a class="material-icons">close</a>
                        </button>
                    </s:if>
                    <ul class="content_area">
                        <li class="controlled_copy_title">
                            <span id="controlled_copy_title"></span>
                        </li>
                        <li class="padding-li">
                            <s:if test="attendant && printableDocument">
                                <div id="user_controlled_copy" class="user_copy selector">
                                    <span class="user_copy_btn"></span>
                                </div>
                                <% if (showPositions == 1) {%>
                                    <div id="position_controlled_copy" class="position_copy selector">
                                        <span class="position_copy_btn"></span>
                                    </div>
                                <%}%>
                            </s:if>
                        </li>
                        <li class="separator-li">
                            <div class="separator-title">
                                <div <s:if test="!attendant">class="hide"</s:if>>
                                        <h3 id="users_added" class="users_added"></h3>
                                    </div>
                                    <div class="separator" <s:if test="!attendant">class="hide"</s:if>>
                                        <div id="users_container" class="noFloat">
                                            <table id="datagrid_filtered_users_added"></table>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="separator-li">
                                <div class="separator-title">
                                    <div <s:if test="!attendant">class="displayNone"</s:if>
                                        <% if (showPositions == 0) {%> class="displayNone" <% } %>>
                                        <h3 id="positions_added" class="positions_added"></h3>
                                    </div>
                                    <div class="separator <s:if test="!attendant">displayNone</s:if>
                                        <% if (showPositions == 0) {%> displayNone <% } %> ">
                                        <div id="postions_container" class="noFloat">
                                            <table id="datagrid_filtered_positions_added"></table>
                                        </div>
                                    </div>        
                                </div>
                            </li>
                            <li class="separator-li">
                                <div class="separator-title">
                                    <div>
                                        <h3 id="history_show"></h3>
                                    </div>
                                    <div class="separator">
                                        <div class="noFloat" id="history_container">
                                            <table id="dataGridHistory" class="history"></table>
                                        </div>
                                    </div>   
                                </div>
                            </li>
                            <li>
                                <hr>
                            </li>
                            <li>
                            <s:if test="attendant">
                                <input class="button raised-button s_print_btn selector" type="button" id="print_button" value="Imprimir"/>

                            </s:if>
                        </li>
                    </ul>
                </div>
            </s:if>
            <s:if test="activeDocument && uncontrolled_copy_access">
                <div class="leftMenu fancy-scroll" id="leftMenuUncontrolled">
                    <!-- boton cerrar  nuevo -->
                   <s:if test="uncontrolled_copy_access">
                       <button class="button rotate selector" type="button" id="close_uncontrolled_copy"/>
                       <a class="material-icons">close</a>
                       </button>
                   </s:if>
                    <ul class="content_area">
                        <li class="uncontrolled_copy_title">
                            <span id="uncontrolled_copy_title"></span>
                        </li>
                        <li class="padding-li">
                            <s:if test="uncontrolled_copy_access == true && printableDocument  == true">
                                <div id="users_uncontrolled_copy_btn" class="user_copy selector">
                                    <span class="user_copy_btn"></span>
                                </div>
                                <div id="positions_uncontrolled_copy_btn" class="position_copy selector">
                                    <span class="position_copy_btn"></span>
                                </div>
                            </s:if>
                        </li>
                        <li class="separator-li">
                            <div class="separator-title">
                                <div <s:if test="!uncontrolled_copy_access">class="hide"</s:if>>
                                        <h3 id="users_added_uncontrolled" class="users_added"></h3>
                                    </div>
                                    <div class="separator" <s:if test="!attendant">class="hide"</s:if>>
                                        <div id="users_uncontrolled" class="noFloat"></div>
                                    </div>
                                </div>    
                            </li>
                            <li class="separator-li">
                                <div class="separator-title">
                                    <div <s:if test="!uncontrolled_copy_access">class="hide"</s:if>>
                                        <h3 id="positions_added_uncontrolled" class="positions_added"></h3>
                                    </div>
                                    <div class="separator" <s:if test="!attendant">class="hide"</s:if>>
                                        <div id="positions_uncontrolled" class="noFloat"></div>
                                    </div>
                                </div>
                            </li>
                            <li class="separator-li">
                                <div class="separator-title">
                                    <div>
                                        <h3 id="history_uncontrolled_show"></h3>
                                    </div>
                                    <div class="separator">
                                        <div class="noFloat">
                                            <table id="history_uncontrolled"></table>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <hr>
                            </li>
                            <li> <!--botonoces de imprimir y cerrar -->
                            <s:if test="uncontrolled_copy_access">
                                <input class="button raised-button s_print_btn selector" type="button" id="print_button_uncontrolled" value="Imprimir"/>

                            </s:if>
                        </li>
                    </ul>
                </div>
            </s:if>
            <div class="containerParent  fancy-scroll">
                <s:if test="bnextLogo">
                    <div class="page_container page print_page">       
                        <s:if test="loadDefaultImage  == true">
                            <s:if test="existsDefaultFileId == true">
                                <img 
                                    src="v-default-visor-image.view"
                                    id="page-0"
                                    loading="lazy"
                                    class="page_img pdf-page-0"
                                    data-page="0"
                                    alt="Page 1"
                                    title="Page 1"/>
                            </s:if>
                            <s:else>
                                <img 
                                    src="../images/logo/logoDPMS.png"
                                    loading="lazy"
                                    id="page-0"
                                    class="page_img pdf-page-0 bnextLogo"
                                    data-page="0"
                                    alt="Page 1"
                                    title="Page 1"/>
                            </s:else>
                        </s:if>
                        <s:else>
                            <img src="data:image/jpeg;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                loading="lazy"
                                 id="page-1"
                                 class="lazy-image page_img loading pdf-page-1"
                                 data-page="1"
                                 alt="Page 1"
                                 title="Page 1"/>

                        </s:else>
                    </div>
                </s:if>
                <s:else>
                    <s:if test="hasPassword && (pageNum == null || pageNum == 0)">
                        <div class="has-password-area">
                            <div class="has-password-message"></div>
                            <form action="" method="post" class="has-password-form">
                                <input type="password" name="filePassword" class="big-input">
                                <input type="submit" id="passwordSubmit" class="Button selector btn_rounded outlined-button64em">
                            </form>
                        </div>
                    </s:if>
                    <s:if test="pageNum != null && pageNum > 0">
                        <c:forEach var="page" begin= "0" end="${pageNum - 1}">
                            <div class="page_container page print_page">
                                <img src="data:image/jpeg;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                    loading="lazy"
                                     id="page-${page}"
                                     class="lazy-image page_img loading pdf-page-${page}"
                                     data-page="${page}"
                                     alt="Page ${page + 1}"
                                     title="Page ${page + 1}"/>
                            </div>
                        </c:forEach>
                    </s:if>
                </s:else>
            </div>
        </div>

        <s:if test="activeDocument && (attendant || uncontrolled_copy_access)">
            <div dojoType="dijit.Dialog" id="users">
                <ul class="content_area">
                    <li tabindex="0">
                        <table id="datagrid_filtered_users_to_add"></table>
                    </li>
                    <li>
                        <div class="actionButtons">
                            <input class="button right finishBtn raised-button" type="button" id="closeBtnUsers" value="Cancelar"/>
                        </div>
                    </li>
                </ul>
            </div>
            <div dojoType="dijit.Dialog" id="positions">
                <ul class="content_area">
                    <li tabindex="0">
                        <table id="datagrid_filtered_positions_to_add"></table>
                    </li>
                    <li>
                        <div class="actionButtons">
                            <input class="button right finishBtn raised-button" type="button" id="closeBtnPositions" value="Cancelar"/>
                        </div>
                    </li>
                </ul>
            </div>
            <div dojoType="dijit.Dialog" id="readers">
                <ul class="content_area">
                    <li>
                        <h3 id="users_re_print" class="users_added" tabindex="0">Usuarios para imprimir copia</h3>
                    </li>
                    <li>
                        <table id="datagrid_readers"></table>
                    </li>
                    <li <% if (showPositions == 0) {%> class="displayNoneUncontrolled" <% } %>>
                        <h3 id="positions_re_print" class="positions_added">Puestos para imprimir copia</h3>
                    </li>
                    <li <% if (showPositions == 0) {%> class="displayNoneUncontrolled" <% } %>>
                        <table id="datagrid_positions"></table>
                    </li>
                    <li>
                        <div class="actionButtons">
                            <s:if test="attendant">
                                <div id="finishBtnReaders" class="button right raised-button print_btn">
                                    <span id="s_reprint_btn"></span>
                                </div>
                            </s:if>
                            <input class="button right finishBtn " type="button" id="cancelBtnReaders" value="Cancelar"/>
                        </div>
                    </li>
                </ul>
                <input type="hidden" name="document_print_id" id="document_print_id" value="" />
                <input type="hidden" name="print_type" id="print_type" value="" />
            </div>
        </s:if>
        <s:hidden name="printId" id="printId"/>
        <s:hidden name="systemColor" id="SYSTEM_COLOR" />
        <s:hidden name="floatingGridSize" id="floatingGridSize" />
        <s:hidden name="loggedUserName" id="loggedUserName" />
        <input type="hidden" name="fileId" id="fileId" value="${fileId}" />
        <input type="hidden" name="documentId" id="documentId" value="${documentId}" />
        <input type="hidden" name="contentType" id="contentType" value="${contentType}" />
        <input type="hidden" name="pages" id="pages" value="${pageNum}" />
        <input type="hidden" id="documentDetail" value="${documentDetail}" />
        <input type="hidden" id="hasPassword" value="${hasPassword}" />
        <input type="hidden" max="${pageNum}" min="1" id="page_bot" value="1" />
    </body>
</html>
