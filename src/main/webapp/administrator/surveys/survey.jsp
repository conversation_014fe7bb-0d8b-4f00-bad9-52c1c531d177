<!DOCTYPE html>
<%@page import="isoblock.common.Properties"%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<%@ taglib prefix="s" uri="/struts-tags" %> 
<% 
    String intusuarioid = (String) session.getAttribute("intusuarioid");
    if(intusuarioid == null) {
        response.sendRedirect("../view/v-logout.view");
    }
    Properties propiedadesJsp = new Properties(Long.parseLong(intusuarioid));
    java.util.ResourceBundle tagsParam = java.util.ResourceBundle.getBundle("isoblock.common.language", propiedadesJsp.localidad);
    isoblock.common.Letreros tags = new isoblock.common.Letreros(tagsParam);
%> 
<html class="html-reset">
    <head>
        <title>Survey handle</title>
        <script type="text/javascript" src="../scripts/framework/bnext/ckeditor/ckeditor.js?${systemVersion}" charset="utf-8"  async="true"></script>
        <%@include file="../../components/requiredScripts.jsp" %>
        <s:set var="systemColor" value="systemColor" scope="systemColor"/> 
        <s:set var="surveyType" value="surveyType"/>
        <s:set var="id" value="id"/>
        <s:set var="code" value="code"/>
        <s:set var="globalId" value="globalId"/>
        <s:set var="floatingGridSize" value="floatingGridSize"/>
        <s:set var="viewMode" value="viewMode"/>
        <s:set var="sourcePage" value="sourcePage"/>
        <s:set var="documentId" value="documentId"/>
        
        <link rel='stylesheet' type='text/css' href="../styles/floating-action.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <%@include file="../../components/dijit-calendar.jsp" %>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/styles/misc.css?${systemVersion}" /> 
        <link rel='stylesheet' type='text/css' href="../scripts/surveys/surveys.css?${systemVersion}" />
        <link rel='stylesheet' type='text/css' href="../scripts/surveys/surveyCommon.css?${systemVersion}" />
        <script charset="utf-8" type="text/javascript" src="../scripts/framework/bnext/survey/surveys.js?${systemVersion}"></script>
        <style type="text/css"> 
            .selectable:hover > .main_form_obj_div,
            div.menu_tool:hover,
            .dropp,
            div.selectable .dragging,
            div.pages span.paginator:hover {
                background-color: ${systemColorLight}!important;
            }
            .dijitTabInner.dijitTabContent.dijitTab {
                background-color: ${systemColor}!important;
            }
            .bnext .dijitTab.dijitTabChecked .tabLabel,
            .itemTitle {
                color: ${systemColor}!important;
            }
            input[type=checkbox].material-icons::before,
            input[type=checkbox]:checked.material-icons::before,
            input[type=checkbox].material-icons:before,
            .cke_editor_field1_uiObject_freeText_dialog .cke_dialog_page_contents:first-child input[type="checkbox"].cke_dialog_ui_checkbox_input:before,
            input[type=checkbox]:checked.material-icons:before,
            input[type=radio].material-icons::before,
            input[type=radio]:checked.material-icons::before,
            input[type=radio].material-icons:before,
            input[type=radio]:checked.material-icons:before {
                color: ${systemSecondaryColor}
            }
            input[type=checkbox][disabled].material-icons::before,
            input[type=checkbox][disabled]:checked.material-icons::before,
            input[type=checkbox][disabled].material-icons:before,
            input[type=checkbox][disabled]:checked.material-icons:before,
            input[type=radio][disabled].material-icons::before,
            input[type=radio][disabled]:checked.material-icons::before,
            input[type=radio][disabled].material-icons:before,
            input[type=radio][disabled]:checked.material-icons:before {
                color: gray;
            }
            .show_hide_buttons {
                display: ${show_hide_buttons};
            }
            html {
                margin-top: 0px;
            }
            .bnext .dijitTabContainerTop-tabs .dijitTabChecked {
                color: ${systemSecondaryColor}
                border-bottom: 2px ${systemSecondaryColor};
            }
            .grid-container.grid-floating-active .textarea-component label {
                background: transparent;
            }
            /* Temas */
            .bnext .surveyClassic .dijitTab.dijitTabChecked .tabLabel,
            .surveyClassic .itemTitle {
                color: #004A87 !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            //
            require(['dojo/_base/array', 'dojo/json', 'dojo/dom', 'dojo/dom-construct', 'dojo/_base/lang'],
                function (array, json, dom, domConstruct, lang) {
                //cargando catalogo de "Esquema de tienda" (TipoEncuestado)
                window.preloadedFunctions = function() {
                    var tipoEncuestadoList = json.parse('${tipoEncuestadoList}') || [];
                    array.forEach(tipoEncuestadoList, function(item) { respondentArray.push({ value: item.id, label: item.descripcion});
                        domConstruct.create('div', {'class': 'w2 left p5'
                            , 'innerHTML': ''
                                    + '<input type="checkbox" name="schema" id="schema' + item.id + '" value="' + item.id + '" onchange="checkArrayValue(this,\'esquemaTienda\')">'
                                    + '<label class="bold" for="schema' + item.id + '">' + item.descripcion + '</label>'
                        }, dom.byId('propertiesSurveyArea'), 'last');
                    });
                    //cargando catalogo de "Temas" (Seccion) (Agrupador de preguntas) (Tipo de evaluación)(TEMA_ID)
                    !window.temaArray && (window.temaArray = []);
                    window.temaArray.push({value: 0 , label: '<%=tags.getString("survey.sinclausula")%>'});
                    var tipoEvaluacionList = json.parse('${tipoEvaluacionList}' || '[]') || [];;
                    array.forEach(tipoEvaluacionList, function(item) {
                        window.temaArray.push({ value: item.id, label: item.type.description + ' - ' + item.code});
                    });
                    //cargando catalogo de "Areas plaza"
                    var areaPlazaList = json.parse('${areaPlazaList}') || [];
                    array.forEach(areaPlazaList, function(item) { areaPlazaArray.push({ value: item.id, label: item.descripcion }); });
                    //cargando catalogo de "Pilares oxxo" (Tema)(TEMA_KEY)
                    var temaList = json.parse('${temaList}') || [];
                    array.forEach(temaList, function(item) { pilarArray.push({ value: item.id , label: item.descripcion }); });
                    matrixAreasPlazaArray = lang.clone(areaPlazaArray);
                };
            });
            //]]>
        </script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="${viewMode}-CLASS ${surveyType}-MODULE">
        <%@include file="../../components/loader.jsp" %>
        <input type="hidden" value="${systemColor}" id="SYSTEM_COLOR" name="systemColor" />
        <input type="hidden" value="${floatingGridSize}" id="floatingGridSize" name="floatingGridSize" />
        
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <form action="#" method="post" class="envelope container-form grid-floating-action-buttons">
                        <div class="envelope" id="stage">
                            <div class="tabContainerParent">
                                <div id="tabContainer"></div>
                            </div>
                            <div id="sidebar" data-dojo-type="dijit.Dialog" style="display:none;">
                                <div id="properties" style="padding: 10px;">
                                    <div id="propertiesArea">
                                        <div class="empty-properties">
                                            <%=tags.getString("survey.mostrarsuinformacion")%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="globalSidebar" data-dojo-type="dijit.Dialog" style="display:none;">
                                <div id="globalPropertiesArea" style="padding: 1px;"></div>
                                <div style="text-align: right">
                                    <button class="button" style="float: right;margin: 15px" id="globalSidebarCloseBtn"></button>
                                </div>
                            </div>
                            <div class="ghostMarginAtTop">
                            </div>
                            <div class="marginAtTop">
                            </div>
                            <div id="fieldArea" class="areaForma_1 cke_dialog fancy-scroll">
                                <div id="formHeader">
                                    <input id="formTitleText" class="formTitle" type="text" contenteditable="false"></div><div id="headerArea" formarea="headerArea" class="startArea"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                </div>
                                <hr id="headerHrMark">
                                <div class="selectable formArea" data-field-type="seccion">
                                    <div class="main_form_obj_div css_seccion">
                                        <div class="fieldNumber sectionId" order="1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
                                        <div class="seccion html-text-line-display" contenteditable="true" rows="1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
                                        <div class="uiObjectItems"></div>
                                    </div>
                                </div>
                                <div id="field0_down" formarea="formArea" class="dropdiv dropformArea">
                                </div>
                                <hr id="footerHrMarl">
                                <div id="footerArea" formarea="footerArea" class="startArea">
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                </div>
                            </div>
                            <ul class="content_area fixed-button-bar">
                                <li class="separator-form">
                                    <ul class="actionButtons show_hide_buttons" id="actionButtonsId">
                                        <li>
                                            <span id="guardar"> <a href="javascript: void(0);" id="saveBtn" class="button raised-button"><%=tags.getString("boton.Guardar")%></a></span>
                                        </li>
                                        <li>
                                            <span id="guardarCopia" style="display:none"> <a href="javascript: void(0);" id="guardarCopiaBtn" class="button"><%=tags.getString("boton.Guardarcopia")%></a></span>
                                        </li>
                                        <li>
                                            <span id="guardarPlantilla" style="display:none"> <a href="javascript: void(0);" id="guardarPlantillaBtn" class="button"><%=tags.getString("boton.SaveTemplate")%></a></span>
                                        </li>
                                        <li>
                                            <a id="cancelarBtn" href="javascript: void(0);" class="button"><%=tags.getString("boton.Cancelar")%></a>
                                        </li>
                                    </ul>
                                </li>            
                            </ul>
                            <input type="hidden" value="<%=tags.getString("survey.Titulodelcuestionario")%>" id="survey-title" />
                            <input type="hidden" value="<%= session.getAttribute("lang")%>" id="language" name="language" />
                            <input type="hidden" value="<%= session.getAttribute("locale")%>" id="locale" name="locale" />
                            <input type="hidden" value="" id="accountPlans" />
                            <input type="hidden" value="../view/${surveyType}.survey.view" id="onBtnAdd" />
                            <input type="hidden" value="../view/v.${surveyType}.survey.list.view" id="onBtnControl" />
                            <input type="hidden" value="${surveyType}" id="surveyType" name="surveyType" />
                            <input type="hidden" value="${requestId}" id="requestId" name="requestId" />
                            <input type="hidden" value="${documentId}" id="documentId" name="documentId" />
                            <input type="hidden" value="${id}" id="id" name="id" />
                            <input type="hidden" value="${code}" id="code" name="code" />
                            <input type="hidden" value="${globalId}" id="globalId" name="globalId" />
                            <input type="hidden" value="${templateSurveyId}" id="templateSurveyId" name="templateSurveyId" />
                            <input type="hidden" value="${surveyThemeId}" id="surveyThemeId" name="surveyThemeId" />
                            <input type="hidden" value="${viewMode}" id="viewMode" name="viewMode" />
                            <input type="hidden" value="${sourcePage}" id="sourcePage" name="sourcePage" />
                            <input type="hidden" value="${userDirectBossOptional}" id="userDirectBossOptional" name="userDirectBossOptional" />
                        </div> 
                    </form>
                </div>
            </div>
        </div>
        <%-- divs flotantes --%>
        <%-- encabezado --%>
        <div id="headerSurvey" data-dojo-type="dijit.Dialog" style="display:none;">
            <input type="hidden" name="headerSurvey" id="headerSurvey" value="-1"/>
            <input type="hidden" name="clave" id="clave" value="-1"/>
            <input type="hidden" name="descripcion" id="descripcion" value="-1"/>
            <table class="display min550" style="display:none" border="0" cellSpacing="0" cellPadding="0">
                <tr>
                    <td>
                        <div id="areaTrabajo">
                            <div class="left pb5">
                                <div class="left pb5 gris">
                                    <span class="bold" id="claveField"></span>
                                </div>
                                <!--s:hidden name="clave" id="clave"/-->
                            </div> 
                            <div class=" gris pv5" style="display:none">
                                <fieldset class="left">
                                    <legend><%=tags.getString("survey.Estatus")%></legend>
                                    <div class="w2 left p5">
                                        <div class="left" id="estatusBloq">
                                            <input type="checkbox" name="estatusCheck" id="estatusCheck" value="1" onchange="checkGetValue(this, 'estatus')"/>
                                            <span class="bold"><%=tags.getString("survey.Activo")%></span>
                                        </div>
                                        <div class="left" id="bloqueadoBloq" style="display:none">
                                            <input type="checkbox" name="espacio" id="espacio" style="visibility:hidden;" />
                                            <span class="bold"><%=tags.getString("survey.Bloqueado")%></span>
                                        </div>
                                    </div>
                                </fieldset>
                                <fieldset class="left" style="display:none">
                                    <legend>Plaza</legend>
                                    <div class="w2 left p5">
                                        <input type="checkbox" name="rol" id="rol_si" onchange="checkGetValue(this, 'rol_si')"/>
                                        <span class="bold"><%=tags.getString("survey.Sinroles")%></span>
                                    </div>
                                    <div class="w2 left p5">
                                        <input type="checkbox" name="rol" id="rol_no" onchange="checkGetValue(this, 'rol_no')"/>
                                        <span class="bold"><%=tags.getString("survey.Conroles")%></span>
                                    </div>
                                </fieldset>
                                <fieldset class="left" id="propertiesSurveyArea"  style="display:none">
                                    <legend><%=tags.getString("survey.Esquemadetienda")%></legend>
                                    <!-- los esquemas se llenan a partir de JS -->
                                </fieldset>
                            </div>
                            <div id="gridUNEsTitle" style="display:none" class="envelope bold ">
                                <h4><%=tags.getString("survey.Plantas")%></h4>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <ul class="content_area" >
                <li>
                    <table id="datagrid_business_unit_added"></table>
                </li>
                <li class="actionButtons">
                    <input class="Button raised-button" type="button" id="add_bunit_btn" value="<%=tags.getString("survey.Agregarplanta")%>"/>
                    <input class="Button finishBtn" type="button" id="close_bunit_add_btn" value="<%=tags.getString("survey.Finalizar")%>"/>
                </li>
            </ul>
        </div>
        <%-- unidades de negocio --%>

        <div id="business_unit_to_add_dialog" data-dojo-type="dijit.Dialog" style="display:none;">
            <ul class="content_area">
                <li>
                    <table id="datagrid_business_unit_to_add"></table>
                </li>
                <li class="actionButtons">
                    <input class="Button finishBtn raised-button" type="button" id="close_bunit_to_add_btn" value="<%=tags.getString("survey.Finalizar")%>"/>
                </li>
            </ul>
        </div>

        <div id="gridSearchSurveysWindow" data-dojo-type="dijit.Dialog" style="display:none;">
            <ul class="content_area">
                <li>
                    <table id="gridSearchSurveys"></table>
                </li>
                <li class="separator txt-right" style="padding-bottom: 0px !important;">
                    <a href="javascript: void(0);" id="close_search_survey_btn" class="dpms_button" ><%=tags.getString("survey.Finalizar")%></a>
                </li>
            </ul>
        </div>
        <div id="gridSearchableDataWindow" data-dojo-type="dijit.Dialog" style="display:none;">
            <ul class="content_area">
                <li>
                    <table id="gridSearchableData"></table>
                </li>
                <li class="actionButtons txt-right">
                    <a href="javascript: void(0);" id="close_searchable_survey_btn" class="Button raised-button"><%=tags.getString("survey.Finalizar")%></a>
                </li>
            </ul>
        </div>
        <div class="displayNone">
            <div id="gridProperty">
            </div>
            <div id="conditionalFieldProperty">
            </div>
        </div>
    </body>
</html>