require([
    'core', 'bnext/gridComponent', 'bnext/gridCubes', 'bnext/gridIcons', 'bnext/gridComponentUtil','dojo/Deferred','dojo/dom','dojo/domReady!'
],
function(core, gridComponent, gridCubes, gridIcons, gcUtil, Deferred, dom) {
    fillFooter();

    function editRecord(id){
        core.showLoader();
        document.edit.id.value = id;
        document.edit.action= '../DPMS/audit.survey.action';
        document.edit.submit();
    }

    function preview(id, vchTexto) {
        document.capture.windowTitle.value = "Cuestionario: " + vchTexto;
        document.capture.id.value = id;
        document.capture.action= '../DPMS/v.audit.survey.preview.action';
        document.capture.target= '_blank';
        document.capture.submit();
    }
    var doEverything = function(i18n) {
        var def = new Deferred(), c = new Array(), cType = gcUtil.cType, grid,
        statusList = [
            {name: i18n.statusLst.none, value:'', icon: gridCubes.gray},
            {name: i18n.statusLst.active, value:1, icon: gridCubes.green },
            {name: i18n.statusLst.inactive, value:0, icon: gridCubes.gray },
            {name: i18n.statusLst.locked,value:2,icon: gridCubes.black}
        ],
        toogleStatus = function(id) {
            showLoader(i18n.messages.toggling_status);
            dialogExecutioner(
                i18n.messages.changeState||i18n.messages.toggling_confirm,
                i18n.messages.yes, i18n.messages.no,
                "Audit.Survey.Status.action", "toggleStatus", [id],
                i18n.messages.succesChangeState||i18n.messages.edit_success, i18n.messages.errorStatusChange, i18n.messages.accept, grid
            );
            core.hideLoader();
        };
        
        
        var optionsBussines = [{
                Eval: function (row) {
                    if (row.businessUnitCount >= 1) {
                        return core.i18n.yes;
                    }
                },
                searchName: core.i18n.yes,
                Function: function (row) {
                    return row.businessUnitCount;
                },
                value: '<not-zero>'
            }, {
                Eval: function (row) {
                    if(row.businessUnitCount === null || typeof row.businessUnitCount === 'undefined' || row.businessUnitCount === 0){
                        return core.i18n.no;
                    }
                },
                searchName: core.i18n.no,
                Function: function (row) {
                    return !row.businessUnitCount;
                },
                value: '<zero>'
            }
        ];
        
        var optionsFieldActivities = [{
                Eval: function (row) {
                    if (row.addActivitiesEnabled === 1) {
                        return core.i18n.yes;
                    } else {
                        return core.i18n.no;
                    }
                },
                searchName: core.i18n.yes,
                Function: function (row) {
                    return row.addActivitiesEnabled;
                },
                value: 1
            }, {
                Eval: function (row) {
                    if(row.addActivitiesEnabled === null || typeof row.addActivitiesEnabled === 'undefined' || row.addActivitiesEnabled === 0){
                        return core.i18n.no;
                    }
                },
                searchName: core.i18n.no,
                Function: function (row) {
                    return !row.addActivitiesEnabled;
                },
                value: 0
            }
        ];

        gcUtil.column(c)
            .push('estatus', i18n.colNames.estatus_header, cType.FunctionImage(['id'],toogleStatus,statusList,true), null, '30px')
            .push('preview', i18n.colNames.grid_preview_header, cType.Function(['id','vchTexto'], preview, gridIcons.question), null, '80px')
            .push('edit', i18n.colNames.grid_edit_header, cType.Function(['id'], editRecord, gridIcons.edit), null, '50px')
            .push('plainText', i18n.colNames.chTexto_header)
            .push('intQuestions', i18n.colNames.intQuestions_header, cType.Integer(), null, '90px')
            .push('dteCreacion', i18n.colNames.dteCreacion_header, cType.Date(true), null, '110px')
            .push('businessUnitCount', i18n.colNames.businessUnitId, cType.FunctionTextEval(['businessUnitCount'], core.$noop, optionsBussines, true),null,'30px',null,null,null,'force-export')
            .push('addActivitiesEnabled', i18n.colNames.hasCanFieldActivity, cType.FunctionTextEval(['addActivitiesEnabled'], core.$noop, optionsFieldActivities, true),null,'30px',null,null,null,'force-export')
        ;
            
        gcUtil.init([
            grid = new gridComponent(gcUtil.basic({
                size:dom.byId('gridSize').value,
                container:"grid",
                serviceStore:"Audit.Survey.action", 
                enableStatistics:true,
                fullColumns : c,
                windowPath: dom.byId('windowPath').value,
                onLoaded: function() {
                    def.resolve();
                }
            }))
        ], dom.byId('gridSize').value);
        return def.promise;
    };
    core.setLang('lang/surveys/nls/survey.list').then(doEverything).then(core.hideLoader);
});
