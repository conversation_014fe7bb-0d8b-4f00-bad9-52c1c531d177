require([
    'core',"bnext/saveHandle",'dojo/query', "bnext/gridComponent", 'dojo/_base/lang', 'dojo/dom','dojo/dom-construct'
    , 'bnext/angularNavigator', "bnext/gridComponentUtil", 'dijit/registry', 'dojo/on', 'dijit/form/DateTextBox'
    , 'dijit/form/TimeTextBox', 'dijit/form/Form', 'dojo/Deferred', 'dojo/_base/array'
    , 'dojo/dom-class', "bnext/callMethod", 'bnext/gridIcons', 'dojo/dom-attr','bnext/filesAttacher/core','bnext/LinkedGridFactory', 'dojo/domReady!'
    , 'xstyle/css!bnext/styles/LinkedGridFactory.css'],
function(core, saveHandle, query, gridComponent, lang, dom, domConstruct, angularNavigator, gcUtil, registry, on, DateTextBox
        , TimeTextBox, Form, Deferred, array, domClass, callMethod, gridIcons, domAttr, fileAttacher, LinkedGridFactory) {
    /**
     * @ToDo: Etiquetas everywhere!
     * 
     * -----------------------------------------------------------
     * @IMPORTANTE!! 
     * No dar formato automatico a este archivo, las declaraciones 
     * de <function> se mueven y quedan en escalera
     *      
     *                  ALT + Shift + F
     *   
     * -----------------------------------------------------------     
     **/
    fillFooter();

    var grid_add_apoyo, 
    validRequire_SearchHelper = true,
    msg,
    misDatos,
    idDom = null,
    statusDom = null,
    startDateDom = null,
    startTimeDom = null,
    endDateDom = null,
    endTimeDom = null,
    auditedAccess = false,
    auditLiderAccess = false,
    auditLiderGeneralAccess = false,
    auditHelperAccess = false,
    requireSaveExternalStaff = false,
    supportStaffDom = null,
    technicalExpertsDom = null,
    auditorsInTrainingDom = null,
    requireMinimumScore = null,
    minimumScore = null,
    size = dom.byId('floatingGridSize').value,
    STATUS = {
        STATUS_PLANNED: 1,
        STATUS_PLANNED_TO_CONFIRM: 2,
        STATUS_PLANED_MAILS_SENT: 3,
        STATUS_ACTIVE_CONFIRMED: 4,
        STATUS_DONE: 5,
        STATUS_ACCEPTED: 6,
        STATUS_CANCELED: 7,
        STATUS_WAITNG_FOR_DATE_CHANGE: 8,
        STATUS_WAITNG_FOR_DATE_CHANGE_BY_AUDITED: 9
    },
    requireSave = false;
    function loadDomValues() {
        idDom = dom.byId('id');
        statusDom = dom.byId('status');
        startDateDom = dom.byId('startDate');
        startTimeDom = dom.byId('startTime');
        endDateDom = dom.byId('endDate');
        endTimeDom = dom.byId('endTime');   
        auditLiderAccess = dom.byId('auditLiderAccess').value === 'true';
        auditLiderGeneralAccess = 
        auditedAccess = dom.byId('auditedAccess').value === 'true';
        auditHelperAccess = dom.byId('auditHelperAccess').value === 'true';
        var externalStaff = dom.byId('externalStaff').value === 'true';
        requireSaveExternalStaff = externalStaff
                        && (auditLiderGeneralAccess || auditLiderAccess || auditedAccess || auditHelperAccess)
                        && (+statusDom.value < STATUS.STATUS_PLANED_MAILS_SENT 
                        || +statusDom.value === STATUS.STATUS_WAITNG_FOR_DATE_CHANGE
                        || +statusDom.value === STATUS.STATUS_WAITNG_FOR_DATE_CHANGE_BY_AUDITED);
        if (externalStaff) {
            supportStaffDom = dom.byId('supportStaff') || null;
            technicalExpertsDom = dom.byId('technicalExperts') || null;
            auditorsInTrainingDom = dom.byId('auditorsInTraining') || null;
        }
        requireMinimumScore = dom.byId('requireMinimumScore').value === 'true';
        if (requireMinimumScore) {
            minimumScore = dom.byId('minimumScore').value;
        }
    }
    function doEverything(i18n) {
        var grid_apoyo_columns = [],
        grid_comment,
        grid_audit_comment,
        grid_answer,
        def = new Deferred(),
        cType = gcUtil.cType,
        grid_comment_columns = [],
        grid_comment_audit_columns = [],
        ans_col=[];
        updateLoaderMessage(i18n.validate_loadingAudit);
        var documentsFields = [];
        
        fA = new fileAttacher({
            tableContainerId: 'fileAttacherTable',
            serviceStore: '../DPMS/audit-documents.action',
            currentEntity:  dom.byId('auditId').value,
            methodName: 'getDocuments',
            showLoader: core.showLoader,
            hideLoader: core.hideLoader,
            hasDelete: false,
            hasUpload: true,
            uploadPath: '../DPMS/Upload.fileUploader',
            files: dom.byId('idDocs').value,
            gridSize: -1
        }, 'addFiles');
        function preview(id) {
            if (id !== null) {
                angularNavigator.navigateLegacyBlank('v-document-viewer.view?id=' + id);
            }
        }
        gcUtil.column(documentsFields)
                .push('code', i18n.code, cType.Text())
                .push('description', i18n.document, cType.Text())
                .push('businessUnit_description', i18n.businnesUnit, cType.Text())
                .push('preview', i18n.preview, cType().Function(["id"], preview, gridIcons.question), null, '60px');
        linkedGridDocuments = LinkedGridFactory.create({
            id: 'auditDocuments',
            allButton: false,
            buttons: []
        }, dom.byId('auditId').value, 'audit-documents.action', i18n, documentsFields);
        
        linkedGridDocuments.hideColumn('auditDocuments_ground_colGround');
        
        function defineValidationRules() {
            $('#validate_reason').validate({//validacion para la razon
                rules: {
                    reason: {
                        required: true,
                        maxlength: 225
                    }
                },
                messages: {
                    reason: {
                        required: i18n.messages_reazonChange,
                        maxlength: i18n.messages_maxLength255
                    }
                },
                errorPlacement: function (error, element) {
                    error.insertBefore(element);
                }
            });
            $('#validate_decline_reason').validate({//validacion para la cancelacion
                rules: {
                    declineReason: {
                        required: true,
                        maxlength: 225
                    }
                },
                messages: {
                    declineReason: {
                        required: i18n.messages_reazonDenial,
                        maxlength: i18n.messages_maxLength255
                    }
                },
                errorPlacement: function (error, element) {
                    error.insertBefore(element);
                }
            });
        }
        function hideSearchHelper() {
            registry.byId('searchAuditorApoyo').hide();
        }
        function hideChangeDateWindow() {
            registry.byId('changeDateWindow').hide();
        }
        function hideDeclineDateWindow() {
            registry.byId('declineDateWindow').hide();
        }
        function require_ComentaryWindow() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            core.inputDialog('', i18n.comment, i18n.writeComment, [core.i18n.Validate.yes, core.i18n.Validate.no], i18n.messages_newComment, {
                inputLimitText: i18n.newComment,
                inputMaxLimit: 250,
                inputMinLimit: 0
            }).then(function (dialog) {
                dialog.event('clickBtn1', function (data) {
                    addComment(data.detail.inputValue).then(dialog.hide);
                });
                dialog.event('clickBtn2', function() {
                    dialog.hide();
                });
            });
        }
        function require_ChangeDateWindow() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            registry.byId('changeDateWindow').set('title', i18n.messages_changeRequest);
            registry.byId('changeDateWindow').show();
            dom.byId('changeDateWindow').style.top = '50px';
            dom.byId('reason').focus();
        }
        function require_DeclineDateWindow() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            registry.byId('declineDateWindow').set('title', i18n.messages_rejectionReason);
            registry.byId('declineDateWindow').show();
            dom.byId('declineDateWindow').style.top = '50px';
        }
        function addComment(comment) {
            var def = new Deferred();
            notice({
                message: i18n.messages_confirmComment,
                btn1Action: function() {
                    showLoader(i18n.messages_savingComment);
                    callMethod({
                        url: "../DPMS/Audit.Individual.action",
                        method: "saveComment",
                        params: [idDom.value, comment || '']
                    }).then(
                        //success
                        function(r) {
                            if (r.operationEstatus) {
                            asyncDialog(i18n.messages_successComment, i18n.messages_accept);
                            grid_comment.goToPage(0);
                            } else {
                                asyncDialog(i18n.messages_failComment, i18n.messages_accept);
                            }
                            hideLoader();
                            def.resolve();
                        },
                        //error
                        function(r) {
                            asyncDialog(i18n.messages_failComment, i18n.messages_accept);
                            hideLoader();
                            def.resolve();
                        });
                },
                btn2Action: function() {
                    def.resolve();
                }
            });
            return def.promise;
        }
        function confirmByManager() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            notice({
                message: i18n.messages_authorizeDateChange,
                btn1Action: function() {
                    showLoader(i18n.messages_savingAudit);
                    callMethod({
                        url: "../DPMS/Audit.Individual.action",
                        method: "confirmByManager",
                        params: [idDom.value]
                    }).then(
                        //success
                        function(r) {
                            if (r.successMessage === "{'tipo':'edit_success','status':'3'}") {
                                dom.byId('status').value = STATUS.STATUS_PLANED_MAILS_SENT;
                            }
                            accessHandle(1);
                            hideLoader();
                            core.dialog(i18n.messages_successDateAudit, i18n.messages_accept).then(function() {
                                showLoader(i18n.messages_loadingScreenPendings);
                                angularNavigator.navigate('pendings');
                            });
                        },
                        //error
                        function(r) {
                            hideLoader();
                            asyncDialog(i18n.messages_failDateAudit, i18n.messages_accept);
                        });
                },
                btn2Action: function() {
                }
            });
        }
        function deniedByManager() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            notice({
                message:  i18n.messages_denyDateChange,
                btn1Action: function() {
                    showLoader(i18n.messages_savingAudit);
                    callMethod({
                        url: "../DPMS/Audit.Individual.action",
                        method: "deniedByManager",
                        params: [idDom.value]
                    }).then(
                        //success
                        function(r) {
                            if (r.successMessage === "{'tipo':'edit_success','status':'3'}") {
                                dom.byId('status').value = STATUS.STATUS_PLANED_MAILS_SENT;
                            }
                            accessHandle(1);
                            hideLoader();
                            core.dialog(i18n.messages_successDenyChangeDate, i18n.messages_accept).then(function() {
                                showLoader(i18n.messages_loadingScreenPendings);
                                angularNavigator.navigate('pendings');
                            });
                        },
                        //error
                        function(r) {
                            hideLoader();
                            asyncDialog(i18n.messages_failDateAudit, i18n.messages_accept);
                        });
                    },
                btn2Action: function() {
                }
            });
        }
        function confirm() {
            var hasChanges = validatePendingChanges();
            if (hasChanges) {
                return core.asyncDialog(i18n.messages_pendingChanges, i18n.messages_accept);
            }
            notice({
                //este mensaje cambia dependiendo quien este viendo la auditoria
                message: msg,
                btn1Action: function() {
                    showLoader(i18n.messages_savingAudit);
                    callMethod({
                        url: "../DPMS/Audit.Individual.action",
                        method: "confirm",
                        params: [idDom.value]
                    }).then(
                        //success
                        function(r) {
                            if(!r.validCallback) {
                                hideLoader();
                                asyncDialog(i18n.messages_failDateAudit, i18n.messages_accept);
                                return;
                            }
                            if (r.successMessage === "{'tipo':'edit_success','status':'3'}") {
                                dom.byId('status').value = STATUS.STATUS_PLANED_MAILS_SENT;
                            }
                            if (r.successMessage === "{'tipo':'edit_success','status':'4'}") {
                                dom.byId('status').value = STATUS.STATUS_ACTIVE_CONFIRMED;
                            }
                            accessHandle(1);
                            hideLoader();
                            core.dialog(i18n.messages_successDateAudit, i18n.messages_accept).then(function() {
                                showLoader(i18n.messages_loadingScreenPendings);
                                angularNavigator.navigate('pendings');
                            });
                        },
                        //error
                        function(r) {
                            hideLoader();
                            asyncDialog(i18n.messages_failDateAudit, i18n.messages_accept);
                        }
                    );
                },
                btn2Action: function() {
                }
            });
        }
        function validateBasicData() {
            var controles = registry.byId("validate_form").validate();
            return (controles);
        }
        function getAuditData() {
            var data = {};
            data.id = +idDom.value;
            data.dteStart = gcUtil.fixDate(registry.byId("startDate").get("value"));
            data.dteEnd = gcUtil.fixDate(registry.byId("endDate").get("value"));
            data.tmpStart = gcUtil.fixDate(registry.byId("startTime").get("value"));
            data.tmpEnd = gcUtil.fixDate(registry.byId("endTime").get("value"));
            data.helpers = getAuditHelpers();
            if (requireSaveExternalStaff) {
                if (supportStaffDom) {
                    data.supportStaff = supportStaffDom.value;
                }
                if (technicalExpertsDom) {
                    data.technicalExperts = technicalExpertsDom.value;
                }
                if (auditorsInTrainingDom) {
                    data.auditorsInTraining = auditorsInTrainingDom.value;
                }
            }
            return data;
        }
        function validatePendingChanges() {
            if (requireSaveExternalStaff) {
                if (supportStaffDom) {
                    var initialValue = domAttr.get(supportStaffDom, 'data-initial-value');
                    if (initialValue !== supportStaffDom.value) {
                        return true;
                    }
                }
                if (technicalExpertsDom) {
                    var initialValue = domAttr.get(technicalExpertsDom, 'data-initial-value');
                    if (initialValue !== technicalExpertsDom.value) {
                        return true;
                    }
                }
                if (auditorsInTrainingDom) {
                    var initialValue = domAttr.get(auditorsInTrainingDom, 'data-initial-value');
                    if (initialValue !== auditorsInTrainingDom.value) {
                        return true;
                    }
                }
            }
            return false;
        }
        
        function getAuditHelpers() {
            return gcUtil.getPropertyFromList(grid_add_apoyo.getGroundDataId(), 'id');
        }
        function save() {
            defineValidationRules();
            var valid = validateBasicData();
            if (valid) {
                notice({
                    message: i18n.messages_confirmAuditProgram,
                    btn1Action: function() {
                        var data = getAuditData();
                        misDatos.setData(data);
                        misDatos.dataSave(i18n.messages_savingDataAudit);
                    }
                });
            }
            return null;
        }
        function checkDates() {
            var constraints = {};
            var date1 = new Date(registry.byId("startDate").get("value")).getTime();
            var date2 = new Date(registry.byId("endDate").get("value")).getTime();
            if (date1 === date2) {
                constraints = {
                    min: ''
                };
                constraints.min = registry.byId("startTime").get("value");
                registry.byId("endTime").set("constraints", constraints);

                constraints = {
                    max: ''
                };
                constraints.max = registry.byId("endTime").get("value");
                registry.byId("startTime").set("constraints", constraints);
            } else {
                registry.byId("startTime").set("constraints", constraints);
                registry.byId("endTime").set("constraints", constraints);
            }
        }
        function createDijits() {
            if (!startDateDom) {
                return;
            }
            var startDate = new DateTextBox(
                    {
                        value: gcUtil.parseDateFromJson(startDateDom.value),
                        required: true,
                        constraints: {
                            datePattern: 'dd/MM/yyyy',
                            min: new Date()
                        }
                    }, startDateDom);
            var endDate = new DateTextBox(
                    {
                        value: gcUtil.parseDateFromJson(endDateDom.value),
                        required: true,
                        constraints: {
                            datePattern: 'dd/MM/yyyy',
                            min: new Date()
                        }
                    }, endDateDom);
            var startHour = new TimeTextBox(
                    {
                        value: gcUtil.parseTimeFromJson(startTimeDom.value),
                        required: true
                    }, startTimeDom);
            var endHour = new TimeTextBox(
                    {
                        value: gcUtil.parseTimeFromJson(endTimeDom.value),
                        required: true
                    }, endTimeDom);
            new Form({
                'class': 'grid-x grid-padding-x'
            }, dom.byId("validate_form"));

            on(startDate, "change", function() {
                var constraints = endDate.get("constraints");
                constraints.min = startDate.get("value");
                endDate.set("constraints", constraints);
                checkDates();
            });
            on(endDate, "change", function() {
                var constraints = startDate.get("constraints");
                constraints.max = endDate.get("value");
                startDate.set("constraints", constraints);
                checkDates();
            });
            on(startHour, "change", checkDates);
            on(endHour, "change", checkDates);
        }
        function successful() {
            hideLoader();
            if (requireSaveExternalStaff) {
                updateExternalStaffData();
            }
        }
        function updateExternalStaffData() {
            if (supportStaffDom) {
                domAttr.set(supportStaffDom, 'data-initial-value', supportStaffDom.value);
            }
            if (technicalExpertsDom) {
                domAttr.set(technicalExpertsDom, 'data-initial-value', technicalExpertsDom.value);
            }
            if (auditorsInTrainingDom) {
                domAttr.set(auditorsInTrainingDom, 'data-initial-value', auditorsInTrainingDom.value);
            }
        }
        function changeDate() {
            var msgChangeDate = i18n.messages_confirmChangeDates;
            if(!validInitialDatesToChange()){
                msgChangeDate = i18n.messages_confirmChangeDatesInvalid;
                core.asyncDialog(msgChangeDate, i18n.messages_accept);
                return;
            }
            if(!validDatesToChange()){
                msgChangeDate = i18n.messages_confirmChangeDatesOutPlan;
            }
            if ($("#validate_reason").valid()
                    && registry.byId("startTime").isValid()
                    && registry.byId("endTime").isValid()
                    && registry.byId("startTime").isValid()) {
                notice({
                    message: msgChangeDate,
                    btn1Action: function() {
                        showLoader(i18n.messages_sendingChangeDates);
                        hideChangeDateWindow();
                        var params = [{
                                id: idDom.value,
                                dteStart: gcUtil.fixDate(registry.byId("startDate").get('value')),
                                dteEnd: gcUtil.fixDate(registry.byId("endDate").get('value')),
                                tmpStart: gcUtil.fixDate(registry.byId("startTime").get('value')),
                                tmpEnd: gcUtil.fixDate(registry.byId("endTime").get('value')),
                                reason: (dom.byId("reason").value)
                            }];
                        callMethod({
                            url: "../DPMS/Audit.Individual.action",
                            method: "requestDateChange",
                            params: params
                        }).then(
                            //success
                            function(r) {
                                hideLoader();
                                core.dialog(i18n.messages_successRequest, i18n.messages_accept).then(function() {
                                    angularNavigator.navigate('pendings');
                                    def.resolve();
                                });
                            },
                            //error
                            function(r) {
                                hideLoader();
                                asyncDialog(i18n.messages_failRequest, i18n.messages_accept);
                                def.resolve();
                            });
                    },
                    btn2Action: function() {
                    }
                });
            }
        }
        function validDatesToChange(){
            var planDteStart = gcUtil.parseDateFromJson(dom.byId('planDteStart').value),
            planDteEnd = gcUtil.parseDateFromJson(dom.byId('planDteEnd').value),
            planTmpStart = gcUtil.parseDateFromJson(dom.byId('planTmpStart').value),
            planTmpEnd = gcUtil.parseDateFromJson(dom.byId('planTmpEnd').value),
            dteStart = registry.byId("startDate").get('value'),
            dteEnd = registry.byId("endDate").get('value'),
            tmpStart = registry.byId("startTime").get('value'),
            tmpEnd = registry.byId("endTime").get('value');

            if ((dteStart >= planDteStart && dteStart <= planDteEnd)
                    && (dteEnd >= planDteStart && dteEnd <= planDteEnd)) {
                if (dteStart === dteEnd) {
                    if (((dteStart === planDteStart) && (tmpStart >= planTmpStart)
                            && (tmpEnd >= planTmpStart)) || ((dteEnd === planDteEnd)
                            && (tmpStart <= planTmpEnd) && (tmpEnd <= planTmpEnd))) {
                        return true;
                    }
                } else {
                    return true;
                }
            }
            return false;
        }  
        function validInitialDatesToChange() {
            var dteStart = registry.byId("startDate").get('value'),
            dteEnd = registry.byId("endDate").get('value'),
            tmpStart = registry.byId("startTime").get('value'),
            tmpEnd = registry.byId("endTime").get('value');
            const today = new Date();
            today.setHours(0,0,0,0);
            if (today > dteStart) {
                return false;
            }
            if ((dteStart <= dteEnd)) {
                if (dteStart === dteEnd) {
                    if ((tmpStart <= tmpEnd)) {
                        return true;
                    }
                } else {
                    return true;
                }
            }
            return false;
        }
        function deniedChangeDate() {
            if ($("#validate_decline_reason").valid()) {
                var def = new Deferred();
                notice({
                    //este mensaje lo ve solo el auditor :D
                    message: i18n.messages_confirmDenyChangeDate,
                    btn1Action: function() {
                        showLoader(i18n.messages_denyingChangeDate);
                        hideDeclineDateWindow();
                        var params = [
                            +idDom.value,
                            (dom.byId("declineReason").value)
                        ];
                        callMethod({
                            url: "../DPMS/Audit.Individual.action",
                            method: "deniedChangeDate",
                            params: params
                        }).then(
                            //success
                            function(r) {
                                hideLoader();
                                core.dialog(i18n.messages_successDenyChangeDate, i18n.messages_accept).then(function() {
                                    angularNavigator.navigate('pendings');
                                    def.resolve();
                                });
                            },
                            //error
                            function(r) {
                                hideLoader();
                                asyncDialog(i18n.messages_failDenyChangeDate, i18n.messages_acept);
                                def.resolve();
                            });
                    },
                    btn2Action: function() {
                        def.resolve();
                    }
                });
                return def.promise;
            }
        }
        function statusHandle() {
            var status = +dom.byId('status').value;
            dom.byId('statusText').innerHTML = i18n.statusLst[status - 1] || i18n.statusDateChange;
            accessHandle();
        }
        function accessHandle(accessJustChanged) {
            var lider_to_confirm = dom.byId('liderToConfirm').value === 'true';
            var status = +dom.byId('status').value;
            if (accessJustChanged) {
                dom.byId('confirm').style.display = 'none';
                dom.byId('changeDate').style.display = 'none';
                dom.byId('deniedChangeDate').style.display = 'none';
                dom.byId('saveBtn').style.display = 'none';
            }
            var dateDisable = function() {
                registry.byId('startDate').set('disabled', true);
                registry.byId('startTime').set('disabled', true);
                registry.byId('endDate').set('disabled', true);
                registry.byId('endTime').set('disabled', true);
            };
            var disabledExternalStaff = function(){
                if(dom.byId('supportStaff')){
                    dom.byId('supportStaff').disabled = 'true';
                }
                if(dom.byId('technicalExperts')){
                    dom.byId('technicalExperts').disabled = 'true';
                }
                if(dom.byId('auditorsInTraining')){
                    dom.byId('auditorsInTraining').disabled = 'true';
                }
            };
            var handleRules = function() {
                if (auditLiderGeneralAccess || auditLiderAccess) {
                    requireSave = true;
                    enableSupportAuditor();
                } else {
                    disableSupportAuditor();
                }
                if (auditedAccess && status < STATUS.STATUS_ACTIVE_CONFIRMED) {
                    dom.byId('confirm').style.display = '';
                    dom.byId('changeDate').style.display = '';
                    on(dom.byId('confirm'), 'click', confirm);
                    on(dom.byId('changeDate'), 'click', require_ChangeDateWindow);
                    if(dom.byId('requestDateChange')){
                        on(dom.byId('requestDateChange'), 'click', changeDate);
                    }
                    if(dom.byId('cancelDateChange')){
                        on(dom.byId('cancelDateChange'), 'click', hideChangeDateWindow);
                    }
                } else {
                    if (+status === STATUS.STATUS_WAITNG_FOR_DATE_CHANGE) {
                        dom.byId('confirmByManager').style.display = '';
                        on(dom.byId('confirmByManager'), 'click', confirmByManager); 
                        dom.byId('deniedByManager').style.display = '';
                        on(dom.byId('deniedByManager'), 'click', deniedByManager);   
                    } else {
                        if (lider_to_confirm) {
                            dom.byId('confirm').style.display = '';
                            dom.byId('deniedChangeDate').style.display = '';                       //boton de detalles
                            on(dom.byId('confirm'), 'click', confirm);
                            on(dom.byId('deniedChangeDate'), 'click', require_DeclineDateWindow);             //boton de detalles
                            if(dom.byId('declineDateChange')){
                                on(dom.byId('declineDateChange'), 'click', deniedChangeDate);                     //boton de la razon
                            }
                            if(dom.byId('cancelDeclineDateChange')){
                                on(dom.byId('cancelDeclineDateChange'), 'click', hideDeclineDateWindow);          //boton de la razon
                            }
                        } else {
                            requireSave = true;
                            dom.byId('saveBtn').style.display = '';
                        }
                    }
                }
                if (requireSaveExternalStaff) {
                    requireSave = true;
                    dom.byId('saveBtn').style.display = '';
                }
            };
            if (!accessJustChanged) {

                var timeFieldRegex = /^1970-01-01T([0-9]{2}:[0-9]{2}:[0-9]{2})$/
                function formatIfTime(row) {
                    var text = formatIfTimeExcel(row)
                    if (text == null) {
                        return document.createTextNode('-')
                    }
                    return document.createTextNode(text);
                }

                function formatIfTimeExcel(row){
                    try {
                        return row.type === 'timeSelector' && row.freeAnswer ? row.freeAnswer.replace(timeFieldRegex, "$1") : row.freeAnswer;
                    }catch (e) {
                        console.error(e);
                    }
                    return row.freeAnswer
                }
                gcUtil.column(ans_col)
                        .hide('id')
                        .push('clauseCode', i18n.clauseCode, cType.Text(), null, null, null, null, false)
                        .push('question', i18n.question, cType.Text(), null, null, null, null, false)
                        .push('questionItem', i18n.questionItem, cType.Text(), null, null, null, null, false)
                        .push('freeAnswer', i18n.freeAnswer, cType.CustomText(formatIfTime,formatIfTimeExcel), null, null, null, null, false)
                        .hide('weight', i18n.weight, cType.Text(), null, null, null, null, false)
                        .push('option', i18n.option, cType.Text(), null, null, null, null, false)
                        .hide('maxWeight', i18n.maxWeight, cType.Text(), null, null, null, null, false);
                gcUtil.column(grid_comment_columns)
                        .hide('id')
                        .push('description', i18n.comment, cType.Text(), null, null, null, null, false)
                        .push('authorName', i18n.authorName, cType.Text(), null, "200px", null, null, false)
                        .push('creationTime', i18n.messages_date, cType().Date(), null, "70px", null, null, false)
                        .push('creationTime', i18n.messages_time, cType().Time(), null, "70px", null, null, false);
                 gcUtil.column(grid_comment_audit_columns)
                        .hide('id')
                        .push('description', i18n.comment, cType.Text(), null, null, null, null, false)
                        .push('authorName', i18n.authorName, cType.Text(), null, "200px", null, null, false)
                        .push('creationTime', i18n.messages_date, cType().Date(), null, "70px", null, null, false);
                
            }

            dom.byId('statusText').innerHTML = i18n.statusLst[status - 1] || i18n.statusDateChange;
            switch (status) {
                case 1:
                case 2:
                case 3:
                case 9:
                    //Planeada
                    handleRules();
                    if (lider_to_confirm || auditLiderAccess) {
                        dateDisable();
                    }
                    if (auditedAccess) {
                        registry.byId('startDate').set('disabled', false);
                        registry.byId('startTime').set('disabled', false);
                        registry.byId('endDate').set('disabled', false);
                        registry.byId('endTime').set('disabled', false);
                    }
                    break;
                case 4://Activa
                    if (auditLiderAccess) {
                        handleRules();
                    }
                    dateDisable();
                    disabledExternalStaff();
                    break;
                case 5://Por confirmar
                    dateDisable();
                    disabledExternalStaff();
                    break;
                case 6://Cerrada
                    dom.byId('comment').style.display = 'none';
                    dateDisable();
                    disabledExternalStaff();
                    disableSupportAuditor();
                    break;
                case 7://Cancelada
                    dom.byId('comment').style.display = 'none';
                    dateDisable();
                    disableSupportAuditor();
                    disabledExternalStaff();
                    break;
                case 8:
                    handleRules();
                    break;
            }
            var found = false;
            query('input', dom.byId('actionButtonsId')).forEach(function(inputBtn) {
                if (!found && inputBtn.style.display !== 'none') {
                    domClass.add(inputBtn, 'raised-button');
                    found = true;
                }
            });
        }
        function initializeGrids() {
            var auditHelpersColumns = [];
            gcUtil.column(auditHelpersColumns)
                    .push('description', i18n.auditHelperDescription);
            grid_add_apoyo = LinkedGridFactory.create({
                id: 'auditHelpers',
                dialogClassName: 'fullSizeScreen',
                disabled: false,
                onBeforeAdd: function() {
                    
                    var excludeData = [
                        {
                            value: dom.byId('businessUnit').value,
                            key: 'businessUnit'
                        },
                        {
                            value: dom.byId('process').value,
                            key: 'process'
                        },
                        {
                            value: dom.byId('department').value,
                            key: 'department'
                        },
                        //se excluyen los auditores de apoyo ya elegidos y el auditor lider
                        gcUtil.InCriteria({
                            separator: '<!>',
                            defaultValue: null,
                            key: "id",
                            array: grid_add_apoyo.getGroundDataId()
                        })
                    ];
                    
                    grid_add_apoyo.getDialogGrid().setExtraCriteria(excludeData);
                }
            }, idDom.value, "Audit.DataSource.action?currentEntityId=" + idDom.value, i18n, auditHelpersColumns);
            
            grid_comment = new gridComponent(gcUtil.basic({
                size:0,
                refreshOnPopRow: false,
                noEyes: true,
                noPagination: true,
                container: "grid_comment",
                entityName: 'comentarios',
                serviceStore: "Audit.Individual.Comments.action?currentEntityId=" + idDom.value,
                methodName: "getAddedComments",
                searchContainer: 'none',
                fullColumns: grid_comment_columns,
                noRegMessage: i18n.messages_noAddedComments,
                resultsInfo: 'none',
                noSettings: true,
                noExcel: true,
                onLoaded: function() {
                    hideLoader();
                    def.resolve();
                }
            }));
            grid_audit_comment = new gridComponent(gcUtil.basic({
                size:0,
                refreshOnPopRow: false,
                noEyes: true,
                noPagination: true,
                container: "grid_comment_created",
                serviceStore: "AuditService.action?idAudit="+dom.byId('auditId').value,
                methodName: "getAuditComments",
                searchContainer: 'none',
                fullColumns: grid_comment_audit_columns,
                noRegMessage: i18n.messages_noAddedComments,
                resultsInfo: 'none',
                noSettings: true,
                noExcel: true,
                onLoaded: function() {
                    hideLoader();
                    def.resolve();
                }
            }));
            grid_answer = !dom.byId('grid_answer') ? null :new gridComponent(gcUtil.basic({
                size:0,
                refreshOnPopRow: false,
                noEyes: true,
                noPagination: true,
                container: "grid_answer",
                entityName: 'answer',
                serviceStore: "Audit.Individual.Answers.action?currentEntityId=" + idDom.value,
                methodName: "getAnswers",
                searchContainer: 'none',
                fullColumns: ans_col,
                noRegMessage: i18n.messages_noAnswers,
                resultsInfo: 'none',
                paginationInfo: 'none',
                noSettings: true,
                noExcel: true,
                onLoaded: function(grid) {
                    var data = grid.getBean().data,
                    total_average = 0,
                    data_count = 0,
                    total_sum = 0, 
                    total_score = 0,
                    clause_info = {};
                    array.forEach(data, function(row){
                        if (isNull(row.weight) || row.weight < 0) {
                            return;
                        }
                        var score = (row.weight / row.maxWeight) * 100;
                        total_score += score;
                        total_sum += row.weight;
                        data_count++;
                        if (row.clause) {
                            if (clause_info[row.clauseId]) {
                                clause_info[row.clauseId].count++;
                                clause_info[row.clauseId].sum += row.weight;
                                clause_info[row.clauseId].score += score;
                            } else {
                                clause_info[row.clauseId] = {
                                    count: 1,
                                    sum: row.weight,
                                    score: score,
                                    code: row.clauseCode,
                                    desc: row.clauseDescription
                                };
                            }
                        }
                    });
                    if (data_count>0) {
                        total_average = Number(total_sum/data_count).toFixed(2);
                        total_score = Number(total_score/data_count).toFixed(2);
                        total_sum = Number(total_sum).toFixed(2);                                
                        var scoreContainer = dom.byId('cDrillDown');

                        domConstruct.create('label',{innerHTML:i18n.validate_total_score, 'class':'labelAnswers'},scoreContainer);
                        domConstruct.create('label',{'class':'labelValue',innerHTML:total_score},scoreContainer);
                        if (requireMinimumScore) {
                            domConstruct.create('label',{innerHTML:i18n.minimumScore, 'class':'labelAnswers'},scoreContainer);
                            domConstruct.create('label',{'class':'labelValue',innerHTML:minimumScore},scoreContainer);
                        }
                        domConstruct.create('label',{innerHTML:i18n.validate_total_average, 'class':'labelAnswers'},scoreContainer);
                        domConstruct.create('label',{'class':'labelValue',innerHTML:total_average},scoreContainer);
                        domConstruct.create('label',{innerHTML:i18n.validate_total_sum, 'class': 'labelAnswers'},scoreContainer);
                        domConstruct.create('label',{'class':'labelValue',innerHTML:total_sum},scoreContainer);
                        for (var clause in clause_info) {
                            var clauses = domConstruct.create('ul',{},scoreContainer);
                            if (clause_info.hasOwnProperty(clause)) {
                                var liClause = domConstruct.create("li",{'class':'clause'},clauses);
                                domConstruct.create("label",{innerHTML:clause_info[clause].code + ": " ,'class':'clause_desc',style:"font-weight:bold;"},liClause);
                                domConstruct.create("label",{innerHTML:i18n.validate_sectionDescription ,'class':'clause_desc',style:"font-weight:bold;"},liClause);
                                domConstruct.create("label",{innerHTML:clause_info[clause].desc ,'class':'clause_desc'},liClause);
                                domConstruct.create("label",{innerHTML:i18n.validate_summarized,'class':'clause_title'},liClause);
                                domConstruct.create("label",{innerHTML:Number(clause_info[clause].sum).toFixed(2),'class':'clause_data',style:"font-weight:bold;"},liClause);
                                domConstruct.create("label",{innerHTML:i18n.validate_averaged,'class':'clause_title'},liClause);
                                domConstruct.create("label",{innerHTML:Number(clause_info[clause].sum/clause_info[clause].count).toFixed(2),style:"font-weight:bold;",'class':'clause_data'},liClause);
                                domConstruct.create("label",{innerHTML:i18n.validate_total_score,'class':'clause_title'},liClause);
                                domConstruct.create("label",{innerHTML:Number(clause_info[clause].score/clause_info[clause].count).toFixed(2),style:"font-weight:bold;",'class':'clause_data'},liClause);
                            }
                        }
                        hideLoader();
                        def.resolve();
                    }
                }
            }));

        };

        function scoreHandle() {
            if(!dom.byId('scoreText')){return ;}
            var scoreText = dom.byId('scoreText').innerText;
            if(scoreText === '') {
                dom.byId('scoreContainer').style.display = 'none';
                return;
            }
            if(+scoreText >= 70) {
                dom.byId('scoreText').style.color = 'green';
            } else if(+scoreText >=40 ) {
                dom.byId('scoreText').style.color = 'darkorange';
            } else {
                dom.byId('scoreText').style.color = 'red';
            }
        }
        function initialize() {
            defineValidationRules();
            misDatos = new saveHandle({
                savedObjName: 'misDatos',
                noCntrlBtn: true,
                serviceStore: '../DPMS/Audit.Individual.action',
                methodName: 'save',
                hookUpdateSuccess: successful,
                hookUpdateFailure: "void(0)"
            });
            createDijits();
            statusHandle();
            initializeGrids();
            scoreHandle();
            grid_comment.setPageSize(dom.byId('gridSize').value);
            grid_audit_comment.setPageSize(dom.byId('gridSize').value);
            grid_answer && grid_answer.setPageSize(0);
            core.autoheight('supportStaff');
            core.autoheight('technicalExperts');
            core.autoheight('auditorsInTraining');
            if (auditedAccess || auditHelperAccess || +statusDom.value === STATUS.STATUS_ACCEPTED) {
                disableSupportAuditor();
            }
            if (auditLiderAccess && +statusDom.value !== STATUS.STATUS_ACCEPTED) {
                enableSupportAuditor();
            }
        }
        function setEvents() {
            on(dom.byId('cancelBtn'), 'click', cancel);
            on(dom.byId('cancelBtnAtDeleted'), 'click', cancel);
            if (dom.byId('auditedAccess').value === "true") {
                msg = i18n.validate_msg_auditedAccess;
            } else {
                msg = i18n.validate_msg_auditor;
            }
            if (!!dom.byId('deleted').value && +dom.byId('deleted').value) {
                dom.byId('actionButtonsId').style.display = 'none';
                dom.byId('deletedActionButtonsId').style.display = '';
                disableEverything();
            }

            on(dom.byId('saveBtn'), 'click', save);
            on(dom.byId('comment'), 'click', require_ComentaryWindow);

            if (+dom.byId('scope').value === 1) {
                query('.process', 'validate_form').forEach(function (elem) {
                    domClass.remove(elem, 'hidden');
                });
                query('.areas', 'validate_form').forEach(function (elem) {
                    domClass.add(elem, 'hidden');
                });
            }
            if (+dom.byId('scope').value === 2) {
                query('.areas', 'validate_form').forEach(function (elem) {
                    domClass.remove(elem, 'hidden');
                });
                query('.process', 'validate_form').forEach(function (elem) {
                    domClass.add(elem, 'hidden');
                });
            }
        }
        function cancel() {
            var message = i18n.messages_sentToPending;
            if(!requireSave) {
                message = i18n.messageSentToPendingByCancel;
            }
            notice({
                message: message,
                btn1Action: function() {
                    showLoader(i18n.messages_loadingPendingsScreen);
                    core.goBackHistory();
                },
                btn2Action: function() {
                }
            });
        }
        /** Finalizan declaraciones **/
        /** inicia **/
        loadDomValues();
        setEvents();
        core.i18n.Validate.onBtnAdd = '../DPMS/v.my.audit.view';
        core.i18n.Validate.onBtnControl = '../DPMS/v.my.audit.view';
        core.i18n.Validate.btnAdd = i18n.messages_titleAudit;
        initialize();
        return def.promise;//<--- el resolve esta en el 'onLoaded' de 'grid_comment'
    }
    function disableSupportAuditor() {
        if (grid_add_apoyo) {
            grid_add_apoyo.disable(false, false);
            grid_add_apoyo.hideAddButton();
        }
    };
    function enableSupportAuditor() {
        if (grid_add_apoyo) {
            grid_add_apoyo.enable();
            grid_add_apoyo.showAddButton();
        }
    };
    function disableEverything() {
        query('select').attr('disabled', true);
        registry.byId('startDate') && registry.byId('startDate').set('disabled', true);
        registry.byId('startTime') && registry.byId('startTime').set('disabled', true);
        registry.byId('endDate') && registry.byId('endDate').set('disabled', true);
        registry.byId('endTime') && registry.byId('endTime').set('disabled', true);
        disableSupportAuditor();
        validRequire_SearchHelper = false;
    }
    core.setLang('lang/audit/nls/audit.individual.handle').then(doEverything);
});