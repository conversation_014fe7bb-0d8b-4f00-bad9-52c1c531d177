require([
  'core',
  'bnext/module/DocumentRequest',
  'bnext/administrator/document/document-simple-list',
  'bnext/administrator/solicitudes/saveRequestHandle',
  'bnext/document/DynamicFieldsHandler',
  'dojo/_base/lang',
  'bnext/module/data/RequestType',
  'bnext/administrator/solicitudes/fillRequestHandle',
  'dojo/dom',
  'dojo/on',
  'bnext/administrator/document/pre-validate-request',
  'bnext/angularNavigator',
  'dojo/parser',
  'dijit/registry',
  'bnext/administrator/document/document-menu-item',
  'bnext/module/grid-menu-util',
  'bnext/angularFabMenu',
  'bnext/special/handler!',
  'dojo/domReady!'
], (
  core,
  DocumentRequest,
  documentSimpleList,
  saveRequestHandle,
  DynamicFieldsHandler,
  dojoLang,
  RequestType,
  fill,
  dom,
  on,
  preValidateRequest,
  angularNavigator,
  parser,
  registry,
  DocumentMenuItem,
  GridMenuUtil,
  angularFabMenu
) => {
  let dialog;
  let docComponent;
  const clicked = dom.byId('clicked')?.value;
  parser.parse().then(() => {
    dialog = registry.byId('solicitudDialog');
    setTitleBarsColor();
  });
  const doEverything = (i18n) => {
    let saveRequestHandleNEW;
    dialog.onHide = (e) => {
      angularFabMenu.displayFabMenu(true);
    };
    addFabMenu();
    function addFabMenu() {
      // Mostramos la sección de botones flotantes
      angularFabMenu.addFabMenu([{ text: i18n.newRecord, value: 'addDocument', iconName: 'add', title: i18n.newRecord }]);
      angularFabMenu.doneAction = (result) => {
        toggleAction(result);
      };
    }
    function toggleAction(option) {
      switch (option.value) {
        case 'addDocument':
          newDocument();
          angularFabMenu.displayFabMenu(false);
          break;
      }
    }
    function preview(id) {
      core.showLoader().then(() => {
        angularFabMenu.hideFabMenu(true);
        angularNavigator.navigateLegacy(`v.documentos.view?vchState=Edit&intDocumentoId=${id}`);
      });
    }
    function cancelConfirm() {
      docComponent.hide();
      return [core.simpleSuccessDeferred];
    }
    function printForm(id, nodo, repositorio, surveyId) {
      return !core.isNull(surveyId);
    }
    function printFillForm(id, nodo, repositorio, surveyId, survey) {
      return survey && survey.isFillRequestAvailable === 1;
    }
    function printDoc(id, nodo, repositorio, surveyId) {
      return core.isNull(surveyId);
    }
    function newDocument() {
      if (docComponent) {
        docComponent.destroyRecursive();
      }
      const save = dojoLang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
      docComponent = new DocumentRequest({
        type: DocumentRequest.create,
        enablePdfViewer: 1,
        externalFileSelect: core.getExternalFileIntegration(),
        onCancel: cancelConfirm,
        onBeforeSubmit: preValidateRequest,
        onSubmit: (dynamicFieldsSavePromise) => {
          docComponent.showLoader(core.i18n.Validate.saving);
          return [
            dynamicFieldsSavePromise.then(
              (sobj) => {
                save(sobj.sol, sobj.file, RequestType.NEW, docComponent);
              },
              (e) => {
                core.error('No se pudo guardar la solicitud.');
                console.error('Falla al guardar valores dinamicos');
                console.error(e);
                docComponent.hideLoader();
              }
            )
          ];
        },
        requestDialog: dialog
      });
      DynamicFieldsHandler.REQUEST.NEW(docComponent);
      docComponent.set('title', i18n.common.titleNew);
      docComponent.show();
    }
    const menuItems = [];

    GridMenuUtil.menu(menuItems, DocumentMenuItem)
      .push(DocumentMenuItem.DETAIL_DOCUMENT, preview, printDoc, ['id', 'nodo.id', 'repositorio.id', 'surveyId'])
      .push(DocumentMenuItem.VIEW_FORM, preview, printForm, ['id', 'nodo.id', 'repositorio.id', 'surveyId'])
      .push(
        DocumentMenuItem.FILL_FORM,
        (surveyId, id, description) => {
          fill(surveyId, id, description);
        },
        printFillForm,
        ['surveyId', 'id', 'description', 'survey']
      );
    documentSimpleList({
      size: dom.byId('gridSize').value,
      container: 'dataGrid',
      methodName: 'getRowsForCurrentUser',
      serviceStore: 'OptDocumentRequest.action',
      windowPath: dom.byId('windowPath').value,
      menuItems: menuItems,
      includeStatus: false,
      reapprove: dom.byId('onlyDocumentManagerReapprove').value,
      docRole: dom.byId('documentRole').value,
      beforeShareLinkClose: () => {
        angularFabMenu.displayFabMenu(true);
      },
      beforeShareLinkOpen: () => {
        angularFabMenu.displayFabMenu(false);
      }
    }).then((docList) => {
      docList.prepareColumns();
      docList.initialize().then((configGrid) => {
        configGrid.grid.refreshCurrentPage();
        saveRequestHandleNEW = new saveRequestHandle(configGrid.grid);
        core.hideLoader();
      });
    });
    clicked && dom.byId(clicked) && dom.byId(clicked).click();
  };
  core.setLang('lang/solicitudes/nls/request').then(doEverything);
});
