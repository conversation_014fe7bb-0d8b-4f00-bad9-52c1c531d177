require([
    'core',
    'bnext/gridCubes', 'bnext/gridIcons',
    'bnext/DynamicFieldUtil',
    'dojo/_base/lang',
    'dojo/aspect',
    'bnext/module/DocumentRequestView',
    'bnext/module/data/RequestType',
    'dijit/Dialog', 'dojo/dom-class',
    'bnext/gridComponent',
    'bnext/gridComponentUtil',
    'bnext/Tabs',
    'dojo/dom',
    'bnext/callMethod',
    'bnext/i18n!bnext/administrator/document/nls/document-module',
    'dojo/domReady!'
], 
function (core, gridCubes, gridIcons, DynamicFieldUtil, dojoLang, aspect, DocumentRequestView, requestType, Dialog, domClass, gridComponent, gcUtil, Tabs, dom, callMethod,
    documentModule) {
    var
        grid_pending, tabs,
        grid_attended,
        grid_all, columns = [], columns_pending = [], columns_attended = [],
        dial = new Dialog(), widgetBK, cType = gcUtil.cType,
        doEverything = function (i18n) {
            var
                statusList = [
                    {name: i18n.statusNames.selection, icon: '', value: ''},
                    {name: i18n.statusNameStandBy, icon: gridCubes.blue, value: 9},
                    {name: i18n.statusNames.resquested, icon: gridCubes.yellow, value: 3},
                    {name: i18n.statusNames.rejected, icon: gridCubes.deepBlue, value: 5},
                    {name: i18n.statusNames.returned, icon: gridCubes.deepBlue, value: 4},
                    {name: i18n.statusNames.in_process, icon: gridCubes.orange, value: 6},
                    {name: i18n.statusNames.finished, icon: gridCubes.beige, value: 7}
                ],
                attendedStatusList = [
                    {name: i18n.statusNames.selection, icon: '', value: ''},
                    {name: i18n.statusNames.rejected, icon: gridCubes.deepBlue, value: 5},
                    {name: i18n.statusNames.in_process, icon: gridCubes.orange, value: 6},
                    {name: i18n.statusNames.finished, icon: gridCubes.beige, value: 7}
                ],
                pendingStatusList = [
                    {name: i18n.statusNames.selection, icon: '', value: ''},
                    {name: i18n.statusNames.resquested, icon: gridCubes.yellow, value: 3},
                    {name: i18n.statusNames.returned, icon: gridCubes.deepBlue, value: 4}
                ],
                busy = [
                    {name: i18n.Validate.lock, icon: gridIcons.lock, value: 1},
                    {name: i18n.Validate.unlock, icon: gridIcons.unlock, value: 0}
                ],
                edit = function (request) {
                    var promise = callMethod({
                        url: 'Request.action',
                        method: 'load',
                        params: [request.id]
                    });
                    if (widgetBK) {
                        widgetBK.destroyRecursive && widgetBK.destroyRecursive();
                        widgetBK = null;
                    }
                    promise.then(function (request) {
                        try {
                            widgetBK = new DocumentRequestView({
                                isAFormRequest: !!request.surveyId,
                                enablePdfViewer: request.enablePdfViewer || 0,
                                surveyId: request.surveyId || null,
                                sol: request,
                                sourcePage: 'requestList',
                                onCancel: function () {
                                    widgetBK.hide();
                                },
                                requestDialog: dial
                            });
                            //se carga de valores dinamicos al despuest de settear el objeto Request cargado en SOL
                            DynamicFieldUtil.load(widgetBK.sol.id, 'DocumentType.Request.Custom.action', widgetBK.dynamicFields, widgetBK.sol.documentType.id, {
                                cacheOn: false,
                                dijits: true,
                                legacy: false
                            }, widgetBK.sol.dynamicTableName).then(function(r) {
                                for(var key in r.domMap) {
                                    if(!r.domMap.hasOwnProperty(key)) {
                                        continue;
                                    }
                                    if(r.domMap[key].widjet) {
                                        r.domMap[key].widjet.set('disabled', true);
                                    } else {
                                        console.error('Debería ser un widjet!!, dynFueld: "' + key + '"');
                                    }
                                }
                            });
                        } catch (e) {
                            console.log(e, e.stack);
                        }
                        widgetBK.set('title', i18n.commonNames.titleVerify);
                        widgetBK.show();
                    });
                },
                unlock = function (id, isBusy) {
                    if(isBusy !== null && isBusy !== 0 ){
                        dialogExecutioner(
                            i18n.Validate.unlock_message,
                            i18n.Validate.yes,
                            i18n.Validate.no, "Request.action", "unlockRequest", [id],
                            i18n.Validate.unlock_success, "",
                            i18n.Validate.accept,
                            tabs
                            );
                    }
                },
                // La bandera "force-export" está encendida para columna de la sección de código que llama a este método y por temas de rendimiento, 
                // todo cambio aqui debe de validarse con una cantidad de registros > 10000.
                printNew = function (row) {
                    return row.type === requestType.NEW;
                },
                // La bandera "force-export" está encendida para columna de la sección de código que llama a este método y por temas de rendimiento, 
                // todo cambio aqui debe de validarse con una cantidad de registros > 10000.
                printModify = function (row) {
                    return row.type === requestType.MODIFY;
                },
                // La bandera "force-export" está encendida para columna de la sección de código que llama a este método y por temas de rendimiento, 
                // todo cambio aqui debe de validarse con una cantidad de registros > 10000.
                printAprove = function (row) {
                    return row.type === requestType.APROVE;
                },
                // La bandera "force-export" está encendida para columna de la sección de código que llama a este método y por temas de rendimiento, 
                // todo cambio aqui debe de validarse con una cantidad de registros > 10000.
                printCancel = function (row) {
                    return row.type === requestType.CANCEL;
                },
                printFillForm = function (row) {
                    return row.surveyId && row.type === requestType.FILL;
                }
            ;
            //Columnas especiales para tab "Todas"
            gcUtil.column(columns)
                .push('status', i18n.colNames.status, cType.FunctionImage([], core.$noop, statusList, true), null, '30px');
            //Columnas especiales para tab "Pendientes" y  "Atendidas"
            gcUtil.column(columns_pending)
                .push('status', i18n.colNames.status, cType.FunctionImage([], core.$noop, pendingStatusList, true), null, '30px');
            gcUtil.column(columns_attended)
                .push('status', i18n.colNames.status, cType.FunctionImage([], core.$noop, attendedStatusList, true), null, '30px');
            //Columnas iguales en las 3 pestañas
            gcUtil.column(columns, columns_pending, columns_attended)
                .push('edit', i18n.colNames.edit, cType.Function(['full-row'], edit));
            //Columnas especiales para tab "Todas" y  "Atendidas"
            gcUtil.column(columns, columns_attended)
                .push('rejectionDate', i18n.colNamesRejectionDate, cType.Date(true), null, null, 'none');
            //Columnas iguales en las 3 pestañas
            gcUtil.column(columns, columns_pending, columns_attended)
                .push('code', i18n.colNames.code, cType.Text())
                .push('version', i18n.colNames.version, cType.Text())
                .push('type', i18n.colNames.type, cType.FunctionTextEval(['id'], core.$noop,
                    [
                        {
                            // La bandera "force-export" está encendida para esta columna y por temas de rendimiento, 
                            // todo cambio en este método debe de validarse con una cantidad de registros > 10000.
                            Eval: printNewE = function (row) {
                                if (!row.surveyId && row.type === requestType.NEW) {
                                    return i18n.Validate._new;
                                } else if (row.surveyId && row.type === requestType.NEW){
                                    return i18n.Validate._new_form;
                                }
                            },
                            'Function': printNew, searchName: i18n.Validate._new + ' / ' + i18n.Validate._new_form, value: requestType.NEW},
                        {
                            // La bandera "force-export" está encendida para esta columna y por temas de rendimiento, 
                            // todo cambio en este método debe de validarse con una cantidad de registros > 10000.
                            Eval: printModifyE = function (row) {
                                if (!row.surveyId && row.type === requestType.MODIFY) {
                                    return i18n.Validate._modification;
                                } else if (row.surveyId && row.type === requestType.MODIFY){
                                    return i18n.Validate._modification_form;
                                }
                            },
                            'Function': printModify, searchName: i18n.Validate._modification + ' / ' + i18n.Validate._modification_form, value: requestType.MODIFY},
                        {
                            // La bandera "force-export" está encendida para esta columna y por temas de rendimiento, 
                            // todo cambio en este método debe de validarse con una cantidad de registros > 10000.
                            Eval: printAproveE = function (row) {
                                if (!row.surveyId && row.type === requestType.APROVE) {
                                    return i18n.Validate._re_approval;
                                } else if (row.surveyId && row.type === requestType.APROVE){
                                    return i18n.Validate._re_approval_form;
                                }
                            },
                            'Function': printAprove, searchName: i18n.Validate._re_approval + ' / ' + i18n.Validate._re_approval_form, value: requestType.APROVE},
                        {
                            // La bandera "force-export" está encendida para esta columna y por temas de rendimiento, 
                            // todo cambio en este método debe de validarse con una cantidad de registros > 10000.
                            Eval: printCancelE = function (row) {
                                if (!row.surveyId && row.type === requestType.CANCEL) {
                                    return i18n.Validate._cancellation;
                                } else if (row.surveyId && row.type === requestType.CANCEL){
                                    return i18n.Validate._cancellation_form;
                                }
                            },
                            'Function': printCancel, searchName: i18n.Validate._cancellation + ' / ' + i18n.Validate._cancellation_form, value: requestType.CANCEL}
                    ], true), null, '85px', null, null, null, 'force-export')
                .push('documentControlledType', i18n.colNameDocumentControlledType, 
                    cType.TextMap([
                        {name : i18n.documentControlledTypeControlled, value: 'controlled'}, 
                        {name : i18n.documentControlledTypeUncontrolled, value: 'uncontrolled'}
                    ]), null, '195px'
                )
                .push('creationDate', i18n.colNames.date, cType.Date(true))
                .push('verify', i18n.colNames.verify, cType.Date(true))
                .push('businessUnitDescription', i18n.columnBusinessUnit, cType.Text())
                .push('departmentDescription', i18n.columnDepartment, cType.Text())
                .push('authorDescription', i18n.colNames.author, cType.Text())
                .push('documentCode', i18n.colNames.doc_code, cType.Text())
                .push('description', i18n.colNames.doc_title, cType.Text())
                .push('nodoDescription', i18n.colNames.nodo, cType.Text())
                .push('enablePdfViewer', documentModule.enablePdfViewer,  cType.TextMap([
                        {name : documentModule.enabledPdfViewer, value: 1}, 
                        {name : documentModule.disabledPdfViewer, value: 0}
                    ]), null, null, 'none')
                .push('restrictRecordsByDepartment', documentModule.restrictRecordsByDepartment, cType.TextMap([
                    {name : core.i18n.yes, value: true}, 
                    {name : core.i18n.no, value: false}
                ]), null, '150px', 'none')
                .push('validateAccessFormDepartment', documentModule.validateAccessFormDepartment, cType.TextMap([
                    {name : core.i18n.yes, value: true}, 
                    {name : core.i18n.no, value: false}
                ]), null, '150px', 'none')
            ;
            gcUtil.column(columns, columns_attended)
                .push('authorizersNames', i18n.columnAuthorizersNames, cType.Text());
            dom.byId('isAdmin').value === 'true'
            && gcUtil.column(columns, columns_attended, columns_pending)
                .push('isBusy', i18n.colNames.unlock, cType.FunctionImage(['id', 'isBusy'], unlock, busy), null, '30px');
        
            grid_all = window.grid = new gridComponent(gcUtil.basic({
                size: dom.byId('gridSize').value,
                container: "dataGrid_ALL",
                serviceStore: "RequestList.action",
                windowPath: dom.byId('windowPath').value,
                fullColumns: columns
            }));
            grid_pending = new gridComponent(gcUtil.basic({
                size: dom.byId('gridSize').value,
                container: "dataGrid_pending",
                serviceStore: "RequestList.action",
                methodName: "getRowsPending",
                windowPath: dom.byId('windowPath').value,
                fullColumns: columns_pending
            }));
            grid_attended = new gridComponent(gcUtil.basic({
                size: dom.byId('gridSize').value,
                container: "dataGrid_attended",
                serviceStore: "RequestList.action",
                methodName: "getRowsAttended",
                windowPath: dom.byId('windowPath').value,
                fullColumns: columns_attended
            }));
            tabs = new Tabs({
                tabs: [
                    {title: i18n.colNames.pendingTab, grid: grid_pending},
                    {title: i18n.colNames.attendedTab, grid: grid_attended},
                    {title: i18n.colNames.ALLTab, grid: grid_all}
                ],
                margin: '5px',
                defaultWidth: 'auto',
                searchNode: 'search',
                paginationNode: 'pagination',
                pageSize: dom.byId('gridSize').value
            }, 'tabNode');
            hideLoader();
        };
    ;
    core.setLang('lang/solicitudes/nls/request.list').then(doEverything);
});