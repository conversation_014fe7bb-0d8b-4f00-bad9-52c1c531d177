/* global decodeURIComponent */
require([
    'bnext/gridComponent',
    'bnext/module/DocumentToAuthorizeRequestAttender',
    'bnext/module/data/RequestType',
    'bnext/gridComponentUtil', 'core', 'bnext/gridIcons',
    'bnext/i18n!bnext/administrator/document/nls/document-module',
    'dojo/dom',
    'dojo/_base/array',
    'bnext/Tabs',
    'bnext/callMethod',
    'dijit/registry', 
    'bnext/administrator/document/authorization-menu-item',
    'bnext/module/grid-menu',
    'bnext/module/grid-menu-util',
    'bnext/document/MultipleApprovalRequest',
    'dojo/domReady!'
],
function (
    gridComponent, DocumentToAuthorizeRequestAttender, RequestType, gcUtil, core, gridIcons, documentModule,
    dom, array, Tabs, callMethod, registry,
    AuthorizationMenuItem,
    GridMenu,
    GridMenuUtil,
    MultipleApprovalRequest
) {
    function doEverything(i18n) {
        var cType = gcUtil.cType;
        var urlVars = core.getUrlVars();
        var busy = false;
        var multipleAttender = null;
        var attender =  new DocumentToAuthorizeRequestAttender({
            grid: null,
            onSuccess: function() {
                busy = false;
            },
            onCancel: function() {
                busy = false;
            }
        });
        function openRequest(grid) {
            if (grid.getBean().count === 1) {
                authorizeAction(grid.getBean().data[0], grid);
            }
        }
        function printNew(row) {
            return !row.surveyId && row.type === RequestType.NEW;
        }
        function printModify(row) {
            return !row.surveyId && row.type === RequestType.MODIFY;
        }
        function printAprove(row) {
            return !row.surveyId && row.type === RequestType.APROVE;
        }
        function printCancel(row) {
            return !row.surveyId && row.type === RequestType.CANCEL;
        }
        function printNewForm(row) {
            return row.surveyId && row.type === RequestType.NEW;
        }
        function printModifyForm(row) {
            return row.surveyId && row.type === RequestType.MODIFY;
        }
        function printAproveForm(row) {
            return row.surveyId && row.type === RequestType.APROVE;
        }
        function printCancelForm(row) {
            return row.surveyId && row.type === RequestType.CANCEL;
        }
        function printFillForm(row) {
            return row.surveyId && row.type === RequestType.FILL;
        }
        function authorizeAction(request, grid) {
            busy = true;
            attender.grid = grid;
            attender.run(request);
        }
        function multipleAprove(requests, grid) {
            if (multipleAttender) {
                multipleAttender.destroyRecursive();
            }
            multipleAttender = new MultipleApprovalRequest({
                requests: requests,
                grid: grid
            });
        }
        function renderTabs(defaultTab, grids) {
            if (!core.isNull(urlVars) && urlVars.requestCode) {
                registry.byId(grids[defaultTab].grid.id + '_code').set('value', decodeURIComponent(urlVars.requestCode).trim());
            }
            var tabs = new Tabs({
                tabs: grids,
                margin: '5px',
                defaultWidth: 'auto',
                searchNode: 'search',
                defaultTab: defaultTab,
                paginationNode: 'pagination',
                pageSize: dom.byId('gridSize').value,
                onSelectTab:  function (li, grid) {
                    busy = false;
                }
            }, 'tabNode');
            setInterval(function () {
                if (!busy) {
                    tabs.refresh();//refresca el grid cada 5 minutos para tener informacion actualizada
                }
            }, 1000 * 60 * 5);
            core.hideLoader();
        }
        var gridMenu = new GridMenu({
            type: AuthorizationMenuItem,
            multipleSelection: true
        }, 'buttonsSection');
        var columns = []; 
        var menuItems = [];
        var reapprovalColumns = [];
        gcUtil.column(reapprovalColumns)
                .push('menu', gridMenu.cellHeader(), gridMenu.cellGridType(), null, '65px');
        GridMenuUtil.menu(menuItems, AuthorizationMenuItem)
                .push(AuthorizationMenuItem.APPROVE, authorizeAction, true, ['full-row', 'grid'], null, multipleAprove);
        gcUtil.column(columns, reapprovalColumns)
                .push('approve', i18n.colNameEdit, cType().Function(['full-row', 'grid'], authorizeAction, gridIcons.approve), null, '15px')
                .push('code', i18n.colNameCode, cType.Text())
                .push('type', i18n.colNameType, cType.FunctionTextEval(['id'], core.$noop,
                        [
                            {'text': i18n.Validate._new, 'Function': printNew},
                            {'text': i18n.Validate._modification, 'Function': printModify},
                            {'text': i18n.Validate._re_approval, 'Function': printAprove},
                            {'text': i18n.Validate._cancellation, 'Function': printCancel},
                            {'text': i18n.Validate._new_form, 'Function': printNewForm},
                            {'text': i18n.Validate._modification_form, 'Function': printModifyForm},
                            {'text': i18n.Validate._re_approval_form, 'Function': printAproveForm},
                            {'text': i18n.Validate._cancellation_form, 'Function': printCancelForm},
                            {'text': i18n.Validate._fill_form, 'Function': printFillForm}
                        ]), null, '90px')
                .push('creationDate', i18n.colNameDate, cType.Date(true))
                .push('author.description', i18n.colNameAuthor, cType.Text())
                .push('documentCode', i18n.colNameDocCode, cType.Text())
                .push('description', i18n.colNameDocDescription, cType.Text())
                .push('documentType.description', i18n.colNameDocumentType, cType.Text(), null, null, 'none')
                .push('department.description', i18n.colNameBusinessUnitDepartment, cType.Text(), null, null, 'none')
                .push('enablePdfViewer', documentModule.enablePdfViewer,  cType.TextMap([
                    {name : documentModule.enabledPdfViewer, value: 1}, 
                    {name : documentModule.disabledPdfViewer, value: 0}
                    ]), null, null, 'none')
                .push('reazon', i18n.colNameReazon, cType.Text());        

        var newGrid =  new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: "dataGrid_new",
            methodName: "getNewAutorizatorRows",
            serviceStore: "OptRequest.action",
            fullColumns: columns,
            onLoaded: function() {
                if (this.getBean().count === 1) {
                    openRequest(this);
                }
            }
        }));
        var modificationGrid =  new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: "dataGrid_modification",
            methodName: "getModificationAutorizatorRows",
            serviceStore: "OptRequest.action",
            fullColumns: columns,
            onLoaded: function() {
                openRequest(this);
            }
        }));
        var reapprovalGrid =  new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: "dataGrid_reapproval",
            methodName: "getReapprovalAutorizatorRows",
            serviceStore: "OptRequest.action",
            onBeforeRefresh: function() {
                gridMenu.reset();
                return core.resolvedPromise();
            },
            onLoaded: function() {
                openRequest(this);
                gridMenu.build(this, menuItems);
            },
            fullColumns: reapprovalColumns
        }));
        var cancellationGrid =  new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: "dataGrid_cancellation",
            methodName: "getCancellationAutorizatorRows",
            serviceStore: "OptRequest.action",
            fullColumns: columns,
            onLoaded: function() {
               openRequest(this);
            }
        }));
        busy = true;
        var grids = [
            {title: i18n.tabNameNew, grid: newGrid, type: 'NEW'},
            {title: i18n.tabNameModify, grid: modificationGrid, type: 'MODIFY'},
            {title: i18n.tabNameReapprove, grid: reapprovalGrid, type: 'APROVE'},
            {title: i18n.tabNameCancel, grid: cancellationGrid, type: 'CANCEL'}
        ];
        if (!core.isNull(urlVars) && urlVars.type) {
            var defaultTab = 0;
            array.forEach(grids, function(grid, index) {
                if (grid.type === urlVars.type) {
                    defaultTab = index;
                }
            });
            renderTabs(defaultTab, grids);
        } else {
            callMethod({
                url: 'Request.action',
                method: 'getAuthorizationPendingCount',
                params: []
            }).then(function(countPending) {
                var defaultTab = 0;
                array.some(grids,  function(grid, index) {
                    if (countPending[grid.type] > 0) {
                        defaultTab = index;
                        return true;
                    } else {
                        return false;
                    }
                });
                renderTabs(defaultTab, grids);
            });
        }

    }
    core.setLang('lang/solicitudes/nls/autorization').then(doEverything);
});