/* global decodeURIComponent */
require([
    'core', 'bnext/gridComponent', 'bnext/gridComponentUtil', 
    'bnext/module/data/RequestType', 'bnext/module/DocumentToVerifyRequestAttender',
    'bnext/gridCubes', 'bnext/gridIcons',
    'dijit/registry', 'bnext/saveHandleUtil', 
    'bnext/i18n!bnext/administrator/document/nls/document-module',
    'dojo/_base/unload', 'dojo/domReady!'
],
function(core, gridComponent, gcUtil, requestType, DocumentToVerifyRequestAttender, gridCubes, gridIcons, registry, saveHandleUtil,
    documentModule) {
    function doEverything(i18n) {
        function handleRequests() {
            if (grid.getBean().count === 1) {
                widgetHandle(grid.getBean().data[0]);
            }
            // Es necesario que el grid tenga los datos asignados 
            // antes de ejecutar este codigo para no filtrar los demas pendientes
            var tex = registry.byId('dataGrid_code').get('value');
            if (tex) {
                registry.byId('dataGrid_code').set('value', "");
            }
        }
        var grid, c = [], cType = gcUtil.cType;
        statusList = [
            {name: i18n.Cubes_color[0], icon: '', value: ''},
            {name: i18n.Cubes_color[1], icon: gridCubes.yellow, value: 3},
            {name: i18n.Cubes_color[2], icon: gridCubes.deepBlue, value: 5},
            {name: i18n.Cubes_color[3], icon: gridCubes.deepBlue, value: 4},
            {name: i18n.Cubes_color[4], icon: gridCubes.orange, value: 6},
            {name: i18n.Cubes_color[5], icon: gridCubes.beige, value: 7}
        ], 
        widgetHandle = function(request) {
            attender.run(request);
        },
        printNew = function(row) {
            return !row.surveyId && row.type === requestType.NEW;
        }, 
        printModify = function(row) {
            return !row.surveyId && row.type === requestType.MODIFY;
        }, 
        printAprove = function(row) {
            return !row.surveyId && row.type === requestType.APROVE;
        }, 
        printCancel = function(row) {
            return !row.surveyId && row.type === requestType.CANCEL;
        }, 
        printNewForm = function(row) {
            return row.surveyId && row.type === requestType.NEW;
        }, 
        printModifyForm = function(row) {
            return row.surveyId && row.type === requestType.MODIFY;
        }, 
        printAproveForm = function(row) {
            return row.surveyId && row.type === requestType.APROVE;
        }, 
        printCancelForm = function(row) {
            return row.surveyId && row.type === requestType.CANCEL;
        },
        codeColumnRender = function (row) {
            return row.documentCode || i18n.codeGenerate;
        };
        gcUtil.column(c)
            .push('approve', i18n.colNames.verify, cType().Function(["full-row"], widgetHandle, gridIcons.approve), null, '15px')
            .push('type', i18n.colNames.type, cType.FunctionTextEval(['id'], core.$noop,
                [
                    {'text': i18n.messages._new, 'Function': printNew},
                    {'text': i18n.messages._modification, 'Function': printModify},
                    {'text': i18n.messages._re_approval, 'Function': printAprove},
                    {'text': i18n.messages._cancellation, 'Function': printCancel},
                    {'text': i18n.messages._new_form, 'Function': printNewForm},
                    {'text': i18n.messages._modification_form, 'Function': printModifyForm},
                    {'text': i18n.messages._re_approval_form, 'Function': printAproveForm},
                    {'text': i18n.messages._cancellation_form, 'Function': printCancelForm}
                ]), null, '85px')
            .push('documentCode', i18n.colNames.doc_code, cType.RenderCell(codeColumnRender), true, '105px')
            .push('description', i18n.colNames.doc_desc, cType.Text(), true, '250px')
            .push('code', i18n.colNames.code, cType.Text(), true, '105px')
            .push('creationDate', i18n.colNames.date, cType.Date(true), true, '105px')
            .push('author.description', i18n.colNames.author, cType.Text(), true, '150px')
            .push('department.description', i18n.colNames.department, cType.Text(), true, '150px')
            .push('documentType.description', i18n.colNames.docType, cType.Text(), true, '160px', 'none')
            .push('documentType.documentControlledType', i18n.colNameDocumentControlledType, 
                cType.TextMap([
                    {name : i18n.documentControlledTypeControlled, value: 'controlled'}, 
                    {name : i18n.documentControlledTypeUncontrolled, value: 'uncontrolled'}
                ]), null, '195px'
            )
            .push('enablePdfViewer', documentModule.enablePdfViewer,  cType.TextMap([
                    {name : documentModule.enabledPdfViewer, value: 1}, 
                    {name : documentModule.disabledPdfViewer, value: 0}
                ]), null, null, 'none')
            .push('reazon', i18n.colNames.reason, cType.Text())
        ;
        grid = new gridComponent(gcUtil.basic({
            size: 15,
            statusList: statusList, 
            container: "dataGrid", 
            methodName: "getRowsVerificator",
            serviceStore: "OptRequest.action",
            fullColumns: c
        }));
        var urlVars = core.getUrlVars(), attender = new DocumentToVerifyRequestAttender({grid: grid});
        if(!core.isNull(urlVars) && urlVars.requestCode) {
            registry.byId('dataGrid_code').set('value', decodeURIComponent(urlVars.requestCode).trim());
        }
        grid.setPageSize(15).then(handleRequests);
        hideLoader();
        setInterval(function() {
            grid.refreshData();
        }, 1000 * 60 * 5); //<-- Refresca el grid cada 5 minutos para tener informacion actualizada
    };
    core.setLanguage('solicitudes/verification').then(doEverything);
});