<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <title>Perfiles</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../../components/requiredScripts.jsp" />
        <script type='text/javascript' src="../administrator/catalog/business_unit_department/controller/business.unit.department.js?${systemVersion}"></script>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <style type="text/css" >
            .grid-floating-action-buttons .grid-container .table-component .TemplatePicker label {
                display: none;
            }
            .formatTextMessage {
                font-weight: normal;
                border-radius: 3px;
                background-color: #DDD !important;
                text-align: justify;
                padding: 10px;
                margin-bottom: 10px;
                line-height: normal;
            }
            .dom-approver-users-container > div + label {
                position: absolute;
                top: 0;
                transform: translateY(-100%);
                width: auto;
                left: 0;
                font-size: 0.75rem;
            }
            .dom-approver-users-container {
                margin: 0;
                width: 100%;
                border-width: 0.15rem;
                border-style: groove;
                margin: 1rem;
                border-radius: 0.25rem;
                padding-bottom: 1rem;
                position: relative;
                margin-top: 2rem;
            }
            .dijitPopup.dijitMenuPopup {
                z-index: 2001!important; /* <-- Sirve para no empalmar tooltip con opciones del combo, es important para sobre escribir estilo INLINE */
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <%@include file="../../../components/loader.jsp" %>
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <div class="button-component">        
                                <span class="material-icons">add</span>
                                <input class="fixed-button" type="button" id="addFormButton"/>
                            </div>
                        </div>
                        <div class="grid-container" id="contenido">
                            <div class="grid-x grid-padding-x" id="mainDiv">
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component disabled-field ">
                                        <input type="text" id="code"></input>
                                        <label id="lblCode"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="userDefinedCode" id="userDefinedCode" required="required" />
                                        <label id="lblUserDefinedCode"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <div class="cell small-12 medium-12 large-12">
                                    <h4 id="formatMessage" class="formatTextMessage">La "Fecha vencimiento" del documento se define con el valor de este campo. Si no se ha definido en la relación, entonces se utilizará el valor del el campo "Vigencia de documentos (meses)" del tipo de documento.</h4>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="costCenter" id="costCenter" maxlength="255"></input>
                                        <label id="lblCostCenter"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="number" id="documentExpiration" name="documentExpiration"  min="1" max="9999" step="1">
                                        <label id="lblDocumentExpiration"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 struts_combo">
                                    <div class="select-component">
                                        <s:select name="businessUnitId" id="businessUnitId" 
                                                  list="businessUnits"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="businessUnitId" class="required"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="departmentId" id="departmentId" class="select" value="0">
                                            <option value="">--Seleccione--</option>
                                        </select>
                                        <label id="lblDepartment" class="required"></label>
                                    </div>
                                </div>
                                <s:if test="regionCatalogEnabled">
                                <div class="cell small-12 medium-6 struts_combo">
                                    <div class="select-component">
                                        <s:select name="regionId" id="regionId" 
                                                  list="regions"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label id="lblRegion" for="regionId"></label>
                                    </div>
                                </div>
                                </s:if>
                                <s:if test="zoneCatalogEnabled">
                                <div class="cell small-12 medium-6 struts_combo">
                                    <div class="select-component">
                                        <s:select name="zoneId" id="zoneId" 
                                                  list="zones"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label id="lblZone" for="zoneId"></label>
                                    </div>
                                </div>
                                </s:if>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="attendantId" id="attendantId" class="select" value="0">
                                            <option value="">--Seleccione--</option>
                                        </select>
                                        <label id="lblAttendant" class="required"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="documentManagerId" id="documentManagerId" class="select" value="0">
                                            <option value="">--Seleccione--</option>
                                        </select>
                                        <label id="lblDocumentManager" class="required"></label>
                                    </div>
                                </div>
  
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="complaints" id="complaints" class="select" value="0">
                                            <option value="">--Seleccione--</option>
                                            <%@include file="../../../components/comboYesNo.jsp" %>
                                        </select>
                                        <label id="lblComplaints"></label>
                                    </div>
                                </div>
                                <div class="grid-container dom-approver-users-container" id="approverUsersContainer">
                                    <div class="grid-x grid-padding-x">
                                        <div class="cell small-12 medium-6">
                                            <select
                                                data-dojo-type="bnext/module/UltraFilteringSelect"
                                                data-dojo-props="
                                                    lblClass: 'label dijit_label query', 
                                                    labelAttr: 'description',
                                                    searchAttr: 'description',
                                                    placeHolder: 'Seleccione o teclee un valor',
                                                    required: true,
                                                    v2: true
                                                "
                                                name="cancelationApproverUserId" id="cancelationApproverUserId">
                                            </select>
                                            <label for="cancelationApproverUserId" class="hidden">Cancelación*</label>
                                        </div>
                                        <div class="cell small-12 medium-6">
                                            <select
                                                data-dojo-type="bnext/module/UltraFilteringSelect"
                                                data-dojo-props="
                                                    lblClass: 'label dijit_label query', 
                                                    labelAttr: 'description',
                                                    searchAttr: 'description',
                                                    placeHolder: 'Seleccione o teclee un valor',
                                                    required: true,
                                                    v2: true
                                                "
                                                name="adjustmentApproverUserId" id="adjustmentApproverUserId">
                                            </select>
                                            <label for="adjustmentApproverUserId" class="hidden">Ajuste*</label>
                                        </div>
                                        <div class="cell small-12 medium-6">
                                            <select
                                                data-dojo-type="bnext/module/UltraFilteringSelect"
                                                data-dojo-props="
                                                    lblClass: 'label dijit_label query', 
                                                    labelAttr: 'description',
                                                    searchAttr: 'description',
                                                    placeHolder: 'Seleccione o teclee un valor',
                                                    required: true,
                                                    v2: true
                                                "
                                                name="reopenApproverUserId" id="reopenApproverUserId">
                                            </select>
                                            <label for="reopenApproverUserId" class="hidden">Reapertura*</label>
                                        </div>
                                        <div class="cell small-12 medium-6">
                                            <select multiple 
                                                    data-dojo-type="bnext/module/UltraCheckedMultiSelect"
                                                    data-dojo-props="
                                                        lblClass: 'label dijit_label query', 
                                                        labelAttr: 'description',
                                                        searchAttr: 'description',
                                                        dropDown: true,
                                                        multiple: true,
                                                        required: true
                                                    "
                                                    name="analystApproverUserId"
                                                    id="analystApproverUserId"
                                                    value="0">
                                            </select>
                                            <label for="analystApproverUserId" class="hidden">Analistas*</label>
                                        </div>
                                        <div class="cell small-12 medium-6">
                                            <select multiple
                                                    data-dojo-type="bnext/module/UltraCheckedMultiSelect"
                                                    data-dojo-props="
                                                        lblClass: 'label dijit_label query',
                                                        labelAttr: 'description',
                                                        searchAttr: 'description',
                                                        dropDown: true,
                                                        multiple: true,
                                                        required: false
                                                    "
                                                    name="docVerifierApproverUserId"
                                                    id="docVerifierApproverUserId"
                                                    value="0">
                                            </select>
                                            <label for="docVerifierApproverUserId" class="hidden">Analistas*</label>
                                        </div>
                                    </div>
                                    <label class="form-authorizers-section">Autorizadores de reapertura, ajuste y cancelación de formularios*</label>
                                </div>

                                <div class="cell small-12">
                                    <div class="table-component">
                                        <div id=formTemplate></div>
                                        <input type="hidden" id="templateId" name="templateId"></input>
                                    </div>
                                </div>        
                                <s:hidden name="id" id="id"/>
                                <s:hidden name="status" id="status"/>
                                <div class="cell small-12 actionButtons">
                                    <input class="Button raised-button" type="button" id="saveBtn" value=""/>
                                    <input class="Button" type="button" id="cancelBtn" value=""/>
                                    <input class="Button displayNone" type="button" id="updateBtn" value=""/>
                                    <input class="Button" type="button" id="clean" value=""/>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                            </div>
                        </div>
                    </form>
                    <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                    <s:hidden name="userId" id="SESSION_USER" /><%-- Mailisima practica!, este valor se puede obtener directo en el *.java con getLoggedUserId() --%>
                    <s:hidden name="configurationRole" id="SESSION_MODULE_ROLE" />
                    <input type="hidden" id="onBtnControl" value="../view/v.business.unit.department.list.view" />
                    <input type="hidden" id="onBtnAdd" value="../view/v.business.unit.department.view" />
                    <%@include file="../../../../components/footer.jsp" %>
                </div>
            </div>
        </div>
    </body>
</html>