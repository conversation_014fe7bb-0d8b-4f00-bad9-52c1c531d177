<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <title>Back End Administrator</title>
        <link rel="shortcut icon" href="../images/favicon.ico" />
        <link rel="stylesheet" href="../qms/<%=bnext.resources.DynamicCssService.CURRENT_FOUNDATION_FILE_NAME%>.css?${systemVersion}">
        <link rel="stylesheet" href="../qms/<%=bnext.resources.DynamicCssService.CURRENT_MATERIAL_FONTS_FILE_NAME%>.css?${systemVersion}">
        <script>
            dojoConfig = {
                parseOnLoad: true,
                locale: '${settingsLang}-${settingsLocale}',
                extraLocale: ["en", "es-MX"],
                deps: ['core'],
                async: true,
                cacheBust: '${systemVersion}'
            };
        </script>
        <script type='text/javascript' src="../scripts/framework/dojo/dojo.js?${systemVersion}"></script>
        <script type="text/javascript" src="../administrator/login/controller/defineDialogBox.js?${systemVersion}" ></script>
        <style>
            .section{
                float:left;
            }
            .section li:first-child{
                list-style: none;
            }
            .steps{
                font-size: 13px;
                padding: 5px;
                width: auto;
                clear: left;
                background-color: #eee;
                margin-bottom: 1em;
                font-family: Consolas,Menlo,Monaco,Lucida Console,Liberation Mono,DejaVu Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace,sans-serif;

            }

            .steps ol.csvfilesList,
            .steps ol.validationsList{
                margin-bottom: 10px;
            }

            .steps li.header
            {
                list-style-type: none;
                margin-bottom: 2px;
            }
            .steps li.content {
                list-style-type: upper-roman;
                margin-bottom: 20px;
            }
            .setps li.unidaddenegociocsvvalidation li.content {
                list-style-type: upper-roman;
            }
            .steps li.fieldRule {
                list-style-type: circle;
            }
            .setps li.content ol.csvfilesList {
                padding-left: 0px;
            }
            body {
                display: none;
            }
            strong.alert {
                color: red;
            }
            strong.info {
                color: blue;
            }
        </style>
        <script type="text/javascript">
            function bodyLoaded() {
                if (window.location.pathname.endsWith('jsp')) {
                    alert('Serás redirigido a "v.backend.manager.view", si no funciona por favor inicia sesión con "admin"');
                    window.location = '../../view/v.backend.manager.view';
                }
                window.document.body.style.display = 'block';
            }
            function showLoader() {
                document.getElementById('loader').style.display = '';
            }
            require(['core', 'dojo/domReady!'], function (core) {
                core.hideLoader();
                window.showLoader = core.showLoader;
            });
        </script>
    </head>
    <body writingsuggestions="false" textprediction="false" onload="bodyLoaded()">
        <%@include file="../../../components/loader.jsp" %>
        <div>
            <div>
                Hello there, this is the new and almost fully automated bulk insertion process.<p/>
            </div>
            <div  class="steps">   
                <ol class="stepsList" start="0">
                    <li class="header">
                        <h1>Como cargar los archivos CSV</h1>
                    </li>
                    <li class="content">    
                        <ol class="csvfilesList" start="0">
                            <li class="header">
                                <strong>Preparación de los archivos CSV:</strong>
                            </li>
                            <li>
                                Utiliza LibreOffice Calc para exportar desde formato XLS or XLSX a CSV.
                            </li>
                            <li>
                                Los archivos CSV deben estar separados por comas y con codificación UFT-8.
                            </li>
                            <li>
                                Todos los valores de los campos no deben tener espacio al inicio o al final.
                            </li>
                        </ol>
                    </li>

                    <li class="content">    
                        <ol class="fileLoadList" start="0">
                            <li class="header">
                                <strong>Orden:</strong>
                            </li>
                            <li>
                                Cargar los archivos CSV de Organization Hierarchy (1-7)
                            </li>
                            <li>
                                Cargar los archivos CSV de Roles and access (1-4)
                            </li>
                            <li>
                                Cargar los archivos CSV de Documents (1-2)
                            </li>
                            <li>
                                Cargar los archivos CSV de Devices (1-2)
                            </li>
                        </ol>
                    </li>
                    <li class="content">
                        <ol class="validationsList" start="0">
                            <li class="header">
                                <strong>Validaciones:</strong>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>UnidadDeNegocio.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Code" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Code" no debe estar duplicado.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Description" no debe estar vacío.
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="usercsvvalidation" start="0">
                                    <li class="header">
                                        <strong>Organization.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Unidad de Negocio" no debe estar vacío, pertenece a la clave o nombre en el menú /Organización/Unidad de Negocio/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave de Planta" no debe estar vacío, pertenece a la clave en el menú /Organización/Sociedad/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre de Planta" no debe estar vacío, pertenece al nombre en el menú /Organización/Sociedad/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave del Departamento" no debe estar vacío, pertenece a la clave del menú /Organización/Departamentos/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Departamento" no debe estar vacío, pertenece al nombre del menú /Organización/Departamentos/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave del Proceso" no debe estar vacío, pertenece a la clave del menú /Organización/Procesos/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Proceso" no debe estar vacío, pertenece a la clave del menú /Organización/Procesos/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave del Área" no debe estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">(En caso de usar un valor que ya existe, se modificará el registro en los campos marcados con asterisco azul: "Departamento", "Nombre del Área", "Status del Área", "Área - Campo #1", "Área - Campo #2", "Área - Campo #3", "Área - Campo #4", "Área - Campo #5" )</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Área" no debe estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Status del Área" no debe estar vacío, "1 = Activo, 0 = Inactivo". Pertenece al cubo del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Área" no debe estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave de Región" no debe estar vacío, pertenece a la clave del menú /Organización/Región/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre de Región" no debe estar vacío, pertenece al nombre del menú /Organización/Región/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave de Zona" no debe estar vacío, pertenece a la clave del menú /Organización/Zona/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre de Zona" no debe estar vacío, pertenece a la Nombre del menú /Organización/Zona/Control/.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Área - Campo #1" puede estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Área - Campo #2" puede estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Área - Campo #3" puede estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Área - Campo #4" puede estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Área - Campo #5" puede estar vacío, pertenece a la clave del menú /Organización/Área/Control/.
                                        <strong class="info">*</strong>
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="usercsvvalidation" start="0">
                                    <li class="header">
                                        <strong>User.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Unidad de Negocio" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Departamento" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Proceso" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Puesto" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Puesto" debe existir en el campo "Puesto" del archivo "Profile.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Correo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Cuenta" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Password" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Perfil" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Perfil" debe existir en el campo "Clave" del archivo "Profile.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Estado usuario" no debe estar vacío y debe ser 0 (Inactivo) o 1 (Activo).
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Estado perfil" no debe estar vacío y debe ser 0 (Inactivo) o 1 (Activo).
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Estado puesto" no debe estar vacío y debe ser 0 (Inactivo) o 1 (Activo).
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="profilecsvvalidation" start="0">
                                    <li class="header">
                                        <strong>Profile.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Puesto" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Configuración" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Documentos" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Auditorías" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Acciones" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Equipo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Reuniones" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Encuestas" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Quejas" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Indicadores" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Proyectos" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "5S" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Formularios" no debe estar vacío.
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="documentcsvvalidation" start="0">
                                    <li class="header">
                                        <strong>Documents.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Departamentos" debe existir en el campo "Departamento" del  archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "ID de Originador" debe existir en el campo "Cuenta" del archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Verificador" debe existir en el campo "Cuenta" del archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "ID de Autorizador 2" debe existir en el campo "Cuenta" del archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "ID de Autorizador 3" debe existir en el archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "ID de Autorizador 4" debe existir en el campo "Cuenta" del archivo "User.csv".
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Clave" debe ser único.
                                    </li>
                                    <li class="fieldRule">
                                        <span>Se pueden cargar modificaciones a documentos, se debe insertar los registros ordenados por versión. </span>
                                        <strong class="alert">Nota: El sistema genera automáticamente el siguiente valor de versión en los documentos publicados.</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Nombre del Documento" debe ser único.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Planta" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Departamentos" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Documento" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Revisión" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Fecha" no debe estar vacío.	
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Carpeta" no debe estar vacío.	
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre del Archivo Físico" no debe estar vacío.	
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Tipo de Documento" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "ID de Originador" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Verificador" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "ID de Autorizador 2" no debe estar vacío.
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>DynamicFields.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Catalogo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Nombre" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Tipo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El valor del campo "Tipo" es el tipo del catalogo.
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>RelationTypeDocuments.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Tipo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Campo" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El valor del  campo "Campo" debe existir en el archivo DynamicFields.csv.
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>ModifyDocuments.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Planta" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Clave" no debe estar vacío.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Tipo de documento" no debe estar vacío.
                                    </li>
                                    <strong>Los campos que continúan después del tipo de documento son dinámicos.</strong>
                                    <br>
                                    <strong>Los campos dinámicos varían.</strong>
                                </ol>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>DocumentsReaders.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo clave no debe estar vacía
                                    </li>
                                </ol>
                            </li>
                            <li>
                                <ol class="unidaddenegociocsvvalidation" start="0">
                                    <li class="header">
                                        <strong>FoldersPermissions.csv:</strong>
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Ruta de carpeta" no debe estar vacía.
                                    </li>
                                    <li class="fieldRule">
                                        El campo "Cuenta de usuario" no debe estar vacía.
                                    </li>
                                </ol>
                            </li>
                        </ol>
                    </li>
                </ol>
            </div>
            <ol class="section" start="0">
                <li>
                    <h2>Organizational Hierarchy</h2>
                </li>
                <li>
                    <h3>Insert Organizational Unit</h3>
                    Select the generated file 
                    (<a href="../files/UnidadDeNegocio.csv">UnidadDeNegocio.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=une" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="une_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Business Unit</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=bun" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="bun_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Region</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=region" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="region_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Zone</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=zone" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="zone_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Department</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=dep" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="dep_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Process</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=pro" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="pro_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Area</h3>
                    Select the generated file
                    (<a href="../files/Organization.csv">Organization.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=area" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="area_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Organizational Unit</h3>
                    Select the generated file 
                    (<a href="../files/ModifyOrganizationalUnit.csv">ModifyOrganizationalUnit.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-organizational-unit" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modifyorg_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Business Unit</h3>
                    Select the generated file
                    (<a href="../files/ModifyBusinessUnit.csv">ModifyBusinessUnit.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-business-unit" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modifyune_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Business Unit - Department</h3>
                    Select the generated file
                    (<a href="../files/ModifyBusinessUnitDepartment.csv">ModifyBusinessUnitDepartment.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-business-unit-department" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modifydep_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Department - Process</h3>
                    Select the generated file
                    (<a href="../files/ModifyDepartmentProcess.csv">ModifyDepartmentProcess.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-department-process" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modifydeppro_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Area</h3>
                    Select the generated file
                    (<a href="../files/ModifyArea.csv">ModifyArea.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-area" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modifyarea_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
            </ol>
            <ol class="section" start="0">
                <li>
                    <h2>Roles and access</h2>
                </li>
                <li>
                    <h3>Insert Profile</h3>
                    Select the generated file
                    (<a href="../rest/printing-format/download/usuariosCsv">User.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=prf" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="prf_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Position</h3>
                    Select the generated file
                    (<a href="../rest/printing-format/download/usuariosCsv">User.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=pst" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="pst_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert User</h3>
                    Select the generated file
                    (<a href="../rest/printing-format/download/usuariosCsv">User.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=usr" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="usr_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Profile</h3>
                    Select the generated file
                    (<a href="../files/Profile.csv">Profile.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=prfmod" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="prfmod_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Bosss</h3>
                    Select the generated file
                    (<a href="../files/ModifyBoss.csv">ModifyBoss.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=modify-boss" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="modify-boss_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>

            </ol>
            <ol class="section" start="0">
                <li>
                    <h2>Documents</h2>
                </li>
                <li>
                    <h3>Insert Documents from business unit</h3>
                    Select the generated file
                    (<a href="../files/Documents.csv">Documents.csv</a>)
                    <br />
                    <strong style="color: red;">Recuerde que un archivo ID = 1 debe existir antes de iniciar (tabla: files)</strong>
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=doc" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="doc_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Modify Documents Date</h3>
                    Select the generated file
                    (<a href="../files/Documents.csv">Documents.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=docsDate" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="docsdate_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Dynamic fields - Insert fields</h3>
                    Select the generated file
                    (<a href="../files/DynamicFields.csv">DynamicFields.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=dynamic" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="dynamic_load">
                        <input type="file" name="file"  accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Dynamic fields - Relation with document types</h3>
                    Select the generated file
                    (<a href="../files/RelationTypeDocuments.csv">RelationTypeDocuments.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=relation" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="relation_load">
                        <input type="file" name="file"  accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Dynamic fields - Assign to documents</h3>
                    Select the generated file
                    (<a href="../files/ModifyDocuments.csv">ModifyDocuments.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=moddoc" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="moddoc_load">
                        <input type="file" name="file"  accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Document Readers</h3>
                    Select the generated file
                    (<a href="../files/DocumentsReaders.csv">DocumentsReaders.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=reader" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="reader_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Folders Permissions</h3>
                    Select the generated file
                    (<a href="../files/FoldersPermissions.csv">FoldersPermissions.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=foldersPermisssions" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="reader_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert files</h3>
                    Select the generated file
                    (<a href="../files/Files.csv">Files.csv</a>)   
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=file" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="file_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li> 
                <li>
                    <h3>Add document references to folder</h3>
                    Select the generated file
                    (<a href="../files/DocumentsReferences.csv">DocumentsReferences.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=documentReference" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="docref_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Add related documents</h3>
                    Select the generated file
                    (<a href="../files/RelatedDocuments.csv">RelatedDocuments.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=relatedDocument" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="reldoc_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li> 
            </ol>
            <ol class="section" start="0">
                <li>
                    <h2>Devices</h2>
                </li>
                <li>
                    <h3>Insert Devices</h3>
                    Select Input File 
                    (<a href="../files/Devices.csv">Devices.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=dev" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="dev_load">
                        <input type="file" name="file" accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
                <li>
                    <h3>Insert Schedule</h3>
                    Select Input File 
                    (<a href="../files/Schedules.csv">Schedules.csv</a>)
                    <br />
                    <form action="../DPMS/BackendUpload.action?type=ser" method="post" onsubmit="showLoader()"
                          enctype="multipart/form-data" name="ser_load">
                        <input type="file" name="file"  accept=".csv,.xls,.xlsx"/>
                        <input type="submit" value="Upload File" />
                    </form>
                </li>
            </ol>
        </div>
    </body>
</html>