<%-- 
    Document   : document-type
    Created on : 10/03/2016, 10:02:45 AM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<% request.setCharacterEncoding("UTF-8"); %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="${cssHtml} html-reset">
    <head>
        <title>Tipos de documento</title>
        <link rel="stylesheet" href="../scripts/framework/bnext/administrator/document/style/document-type.css?${systemVersion}" >
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <%@include file="../../components/requiredScripts.jsp" %>
        <script src="../scripts/framework/bnext/administrator/document/document-type.js?${systemVersion}" type="text/javascript"></script>
        <style>
            .section-title {
                color: #565656;
                font-size: 1.15rem;
                font-weight: bold;
            }
            
            .divider {
                width: 100%;
                height: 2px;
                margin: 9px 1px;
                overflow: hidden;
                background-color: #d5d5d5;;
                border-bottom: 1px solid #ffffff;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <%@include file="../../components/loader.jsp" %>
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container" id="contenido">
                            <div class="grid-x grid-padding-x" id="mainDiv">  
                                <div class="cell small-12">
                                    <div class="field-display">
                                        <strong id="statusText"></strong>
                                        <label id="lblStatus" class="labelText">${statusLabelKey}</label>
                                        <input type="hidden" name="status" id="status" value="${entity.status}"/>
                                        <input type="hidden" id="statusLabelKey" value="${statusLabelKey}"/>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 placeholder">
                                    <div id="codeContainer" class="textarea-component">
                                        <input type="text" required="required" name="code" id="code" value="${entity.code}"/>
                                        <label id="lblCode"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 placeholder">
                                    <div class="textarea-component">
                                        <input type="text" required="required" name="description" id="description" value="${entity.description}"/>
                                        <label id="lblDescription"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select name="dictionaryIndexId" id="dictionaryIndexId" 
                                                  list="dictionaryIndexList"
                                                  listKey="value" listValue="text" value="entity.dictionaryIndexId">
                                        </s:select>
                                        <label id="lblDictionaryIndex"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select name="isRetainable" id="isRetainable" 
                                                  list="yesNoList" class="yesnoOption"
                                                  listKey="value" listValue="text" value="entity.isRetainable">
                                        </s:select>
                                        <label id="lblIsRetainable"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="select-component">
                                        <s:select name="documentControlledType" id="documentControlledType" 
                                                  list="controlledList" class="yesnoOption"
                                                  listKey="value" listValue="text" value="entity.documentControlledType">
                                        </s:select>
                                        <label id="lblDocumentControlledType"></label>
                                    </div>
                                </div>

                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="pdfViewerFitDocumentScreen" name="pdfViewerFitDocumentScreen" class="select yesnoOption" value="${entity.pdfViewerFitDocumentScreen}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.pdfViewerFitDocumentScreen == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.pdfViewerFitDocumentScreen != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblPdfViewerFitDocumentScreen"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="enablePdfViewer" name="enablePdfViewer" class="select yesnoOption" value="${entity.enablePdfViewer}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.enablePdfViewer == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.enablePdfViewer != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblEnablePdfViewer"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="number"  required="required" id="documentExpiration" name="documentExpiration"
                                               value="${entity.documentExpiration}">
                                        <label id="lblDocumentExpiration"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <div class="cell small-12 medium-12 large-12">
                                    <h4 id="formatMessage" class="formatTextMessage">La "Fecha vencimiento" del documento se define en la relación planta/departamento con el campo "Vigencia de documentos (meses)". Si no se ha definido en la relación, entonces se utilizará el valor del tipo.</h4>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <div class="cell small-12 medium-6 ${hasReaders == 1 ? '' : 'displayNone'}">
                                    <div class="select-component">
                                        <select id="mustRead" name="mustRead" class="select yesnoOption" value="${entity.mustRead}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.mustRead == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.mustRead != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblMustRead"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 ${hasReaders == 1 && entity.mustRead == 1 ? '' : 'displayNone'}" id="mustAssignReadersSection">
                                    <div class="select-component">
                                        <select id="mustAssignReaders" name="mustAssignReaders" class="select yesnoOption" value="${entity.mustAssignReaders}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.mustAssignReaders == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.mustAssignReaders != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblMustAssignReaders"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 readOnlyEnabledSection ${"controlled".equals(entity.documentControlledType) ? '' : 'displayNone'}" id="readOnlyEnabledSection">
                                    <div class="select-component">
                                        <select id="readOnlyEnabled" name="readOnlyEnabled" class="select yesnoOption" value="${entity.readOnlyEnabled}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.readOnlyEnabled == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.readOnlyEnabled != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblreadOnlyEnabled"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="canPrintControlledCopies" name="canPrintControlledCopies" class="select yesnoOption" value="${entity.canPrintControlledCopies}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.canPrintControlledCopies == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.canPrintControlledCopies != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblCanPrintControlledCopies"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 quickPrintEnabledSection" id="quickPrintEnabledSection">
                                    <div class="select-component">
                                        <select id="quickPrintEnabled" name="quickPrintEnabled" class="select yesnoOption" value="${entity.quickPrintEnabled}">
                                            <option value="1" ${entity.quickPrintEnabled == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.quickPrintEnabled != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                    <label id="lblquickPrintEnabled"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component downloadFilesList ${"uncontrolled".equals(entity.documentControlledType) ? '' : 'displayNone'}">
                                        <select name="downloadFiles" id="downloadFiles" class="select yesnoOption" value="${entity.downloadFiles}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.downloadFiles == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.downloadFiles != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblDownloadFiles"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <div class="cell small-12">
                                    <label id="lblRegistryCode"></label>
                                    <ul id="registryCodeContainer">
                                        <li class="topCodeContainer">
                                            <ul id="registryCodeItems" class="draggables"></ul>
                                        </li>
                                        <li>
                                            <div id="registryCode" class="draggables"></div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="cell small-12 medium-6 displayNone">
                                    <div class="textarea-component">
                                        <input type="text" name="registryCodeSequenceLength" id="registryCodeSequenceLength" value="${entity.registryCodeSequenceLength}" />
                                        <label id="lblRegistryCodeSequenceLength"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" title="Click para agregar" id="addDynamicFieldBtn">
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="table-component">
                                        <div id="document_type_dynamic_fields"></div>
                                    </div> 
                                </div>         
                                <!--Sección de compartir documentos-->
                                <div class="cell small-12 medium-6 ${shareDocumentsDisplay}">
                                    <div class="select-component">
                                        <select name="shareDocuments" id="shareDocuments" class="select yesnoOption" value="${entity.shareDocuments}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.shareDocuments == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.shareDocuments != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblShareDocuments"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 shareDocuments ${shareDocumentsDisplay}">
                                    <div class="textarea-component">
                                        <input type="text" name="shareMaxDownloads" id="shareMaxDownloads" value="${entity.shareMaxDownloads}" />
                                        <label id="lblShareMaxDownloads"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 shareDocuments ${shareDocumentsDisplay}">
                                    <div class="textarea-component">
                                        <input type="text" name="shareMaxViews" id="shareMaxViews" value="${entity.shareMaxViews}" />
                                        <label id="lblShareMaxViews"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 shareDocuments ${shareDocumentsDisplay}">
                                    <div class="textarea-component">
                                        <input type="text" name="shareLife" id="shareLife" value="${entity.shareLife}" />
                                        <label id="lblShareLife"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 shareDocuments ${shareDocumentsDisplay}">
                                    <div class="select-component">
                                        <select name="shareRestrictUserAgent" id="shareRestrictUserAgent" class="select yesnoOption" value="${entity.shareRestrictUserAgent}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.shareRestrictUserAgent == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.shareRestrictUserAgent != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                    <label id="lblShareRestrictUserAgent"></label>
                                    </div>
                                </div>        
                                <div class="rightGridContainer shareDocuments shareRestrictUserAgent ${shareDocumentsDisplay} cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" title="Click para agregar" id="addShareUserAgents">
                                    </div>
                                </div>
                                <div class="rightGridContainer shareDocuments shareRestrictUserAgent ${shareDocumentsDisplay} cell small-12">
                                    <div class="table-component">
                                        <div id="shareUserAgents"></div>
                                        </div> 
                                    </div>    
                                <div class="cell small-12 medium-6 shareDocuments ${shareDocumentsDisplay}">
                                    <div class="select-component">
                                        <select name="shareRestrictFileType" id="shareRestrictFileType" class="select yesnoOption" value="${entity.shareRestrictFileType}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.shareRestrictFileType == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.shareRestrictFileType != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                    <label id="lblShareRestrictFileType"></label>
                                    </div>
                                </div>             
                                <div class="rightGridContainer shareDocuments shareRestrictFileType ${shareDocumentsDisplay} cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" title="Click para agregar" id="addShareFileTypes">
                                    </div>
                                </div>    
                                <div class="rightGridContainer shareDocuments shareRestrictFileType ${shareDocumentsDisplay} cell small-12">
                                    <div class="table-component">
                                        <div id="shareFileTypes"></div>
                                    </div> 
                                </div>      
                                <!--Sección de liga(URL) externa-->            
                                <div class="cell small-12 medium-6  ${externalLinkDisplay}">
                                    <div class="select-component">
                                        <select name="externalLink" id="externalLink" class="select yesnoOption">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.externalLink == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.externalLink != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblExternalLink"></label>
                                    </div>
                                </div>                     
                                <div class="cell small-12 medium-6 externalLink ${externalLinkDisplay}">
                                    <div class="textarea-component">
                                        <input type="text" name="externalMaxDownloads" id="externalMaxDownloads" value="${entity.externalMaxDownloads}" />
                                        <label id="lblExternalMaxDownloads"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 externalLink ${externalLinkDisplay}">
                                    <div class="textarea-component">
                                        <input type="text" name="externalLife" id="externalLife" value="${entity.externalLife}" />
                                        <label id="lblExternalLife"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 externalLink ${externalLinkDisplay}">
                                    <div class="select-component">
                                        <select name="externalRestrictUserAgent" id="externalRestrictUserAgent" class="select yesnoOption">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.externalRestrictUserAgent == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.externalRestrictUserAgent != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblExternalRestrictUserAgent"></label>
                                    </div>
                                </div>          
                                <div class="rightGridContainer externalLink externalRestrictUserAgent ${externalLinkDisplay} cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" title="Click para agregar" id="addExternalUserAgents">
                                    </div>
                                </div>    
                                <div class="rightGridContainer externalLink externalRestrictUserAgent ${externalLinkDisplay} cell small-12">
                                    <div class="table-component">
                                        <div id="externalUserAgents"></div>
                                    </div> 
                                </div>         
                                <div class="cell small-12 medium-6 externalLink ${externalLinkDisplay}">
                                    <div class="select-component">
                                        <select name="externalRestrictFileType" id="externalRestrictFileType" class="select yesnoOption">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.externalRestrictFileType == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.externalRestrictFileType != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblExternalRestrictFileType"></label>
                                    </div>
                                </div>       
                                <div class="rightGridContainer externalLink externalRestrictFileType ${externalLinkDisplay} cell small-12">
                                    <div class="button-component">
                                        <span class="material-icons">add</span>
                                        <input type="button" title="Click para agregar" id="addExternalFileTypes">
                                    </div>
                                </div>    
                                <div class="rightGridContainer externalLink externalRestrictFileType ${externalLinkDisplay} cell small-12">
                                    <div class="table-component">
                                        <div id="externalFileTypes"></div>
                                    </div> 
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="collectingAndStoreResponsible" name="collectingAndStoreResponsible" class="select yesnoOption" value="${entity.collectingAndStoreResponsible}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.collectingAndStoreResponsible == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.collectingAndStoreResponsible != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblCollectingAndStoreResponsible"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 ${entity.isRetainable == 1 ? '' : 'displayNone'}" id="informationClassificationSection">
                                    <div class="select-component">
                                        <select id="informationClassification" name="informationClassification" class="select yesnoOption" value="${entity.informationClassification}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.informationClassification == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.informationClassification != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblInformationClassification"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6 ${entity.isRetainable == 1 ? '' : 'displayNone'}" id="dispositionSection">
                                    <div class="select-component">
                                        <select id="disposition" name="disposition" class="select yesnoOption" value="${entity.disposition}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.disposition == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.disposition != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblDisposition"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <h3 class="igx-card-header__title" id="formModule"></h3>
                                </div>
                                <div class="cell small-12 medium-12 large-12">
                                    <div class="divider"></div>
                                </div>
                                <div class="cell small-12 medium-12 large-12">
                                    <span class="section-title" id="lblFormSection"></span>
                                </div>
                                <div class="cell small-12 medium-6" id="lbl_filePdfPagesEnabled_warn">
                                    <div class="select-component">
                                        <select id="filePdfPagesEnabled" name="filePdfPagesEnabled" class="select yesnoOption Boolean" value="${entity.filePdfPagesEnabled}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.filePdfPagesEnabled ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${!entity.filePdfPagesEnabled ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lbl_filePdfPagesEnabled"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="addFindingsEnabled" name="addFindingsEnabled" class="select yesnoOption" value="${entity.addFindingsEnabled}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.addFindingsEnabled == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.addFindingsEnabled != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblAddFindingsEnabled"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="addActivitiesEnabled" name="addActivitiesEnabled" class="select yesnoOption" value="${entity.addActivitiesEnabled}">
                                            <option value="">--Seleccione--</option>
                                            <option value="1" ${entity.addActivitiesEnabled == 1 ? 'selected="selected"' : ''}></option>
                                            <option value="0" ${entity.addActivitiesEnabled != 1 ? 'selected="selected"' : ''}></option>
                                        </select>
                                        <label id="lblAddActivitiesEnabled"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 actionButtons">
                                    <%@include file="../../../components/saveButtons.jsp" %>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <textarea id="entityRegistryCode" class="displayNone">${entity.registryCode}</textarea>
                                <textarea id="serializedStatusList" class="displayNone">${serializedStatusList}</textarea>
                                <textarea id="serializedEntityKeys" class="displayNone">${serializedEntityKeys}</textarea>
                                <input type="hidden" name="id" id="id" value="${id}"/>
                                <input type="hidden" name="deleted" id="deleted" value="${entity.deleted}"/>
                                <input type="hidden" id="lastModificationDate" value="${entity.lastModifiedDate}"/>
                                <input type="hidden" id="creationDate" value="${entity.createdDate}"/>
                                <input type="hidden" id="shareDocumentsSettings" value="${shareDocuments}"/>
                                <input type="hidden" id="externalLinkSettings" value="${externalLink}"/>
                                <input type="hidden" id="auditableEntityImpl" value="${auditableEntityImpl}"/>
                                <input type="hidden" id="documentExpirationData" value="${entity.documentExpiration}">
                                <input type="hidden" id="documentTypeId" value="${entity.id}">
                                </div>
                            </div>   
                        </div> 
                    </form>
                    <%@include file="../../components/sessionVariables.jsp" %>
                    <%@include file="../../components/footer.jsp" %>
                </div>
            </div>
        </div>
    </body>
</html>