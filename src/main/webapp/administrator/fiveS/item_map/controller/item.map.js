require(['bnext/FileUploader', 'dojo/dom-attr', "bnext/callMethod", 'dojo/dom', "dojo/dom-class", 'dojo/on',
    'bnext/saveHandle', 'core', 'dojo/_base/lang', 'bnext/ImageTagger', 'dojo/dom-construct', 'dojo/domReady!'],
function (FileUploader, domAttr, callMethod, dom,domClass, on,
        sH, core, lang, ImageTagger, domConstruct) {
    var doEverything = function (i18n) {
        var imageTagger,
        misDatos = new sH({
            savedObjName: "misDatos",
            serviceStore: "ItemMap.action",
            methodName: "save"
        }),
        getItems = function () {
            var id = dom.byId("itemId").value,
            args = {
                url: "Item.action",
                method: "getItemsInItem",
                params: [id]
            };
            callMethod(args).then(function (resultado) {
                fillCombo('#childId', resultado, 'text', 'value', false);
            },core.hideLoader);
        },
        getZones = function () {
            var id = dom.byId("id").value,
            args = {
                url: "ItemMap.action",
                method: "getZones",
                params: [id]
            };
            callMethod(args).then(function(zones) {
                return lang.hitch(imageTagger,imageTagger.paintZones)(zones);
            },core.hideLoader);
        },
        save = function () {
            var valid = $("#validate_form").valid();
            if (+dom.byId('mapId').value === 0) {
                window.top.dialogBox(i18n.Validate.fileMissing, core.i18n.Validate.accept);
                return;
            }
            if (valid) {
                core.confirmDialog(dom.byId('id').value).then( function () {   
                    showLoader(core.i18n.Validate.saving);
                    var data = {
                        id: dom.byId('id').value,
                        itemId: dom.byId('itemId').value,
                        code: dom.byId('txtCode').value,
                        description: dom.byId('description').value,
                        mapId: dom.byId('mapId').value
                    },
                    generateCode = dom.byId('chkGenerate').checked,
                    zones = lang.hitch(imageTagger, imageTagger.getZones)();
                    if (generateCode) {
                        data.code = '';
                    }
                    misDatos.setData([data, zones]);
                    misDatos.saveData();
                }, core.hideLoader);
            }
        },
        toggleGenerateKey = function () {
            toggleGenerate('#chkGenerate', '#txtCode');
        },
        load = function (id) {
            var args = {
                url: "../DPMS/ItemMap.action",
                method: "load",
                params: [id]
            };
            callMethod(args).then(populate, core.hideLoader);
        },
        populate = function (object) {
            dom.byId('txtCode').value = object['code'];
            dom.byId('description').value = object['description'];
            dom.byId('itemId').value = object['itemId'];
            dom.byId('mapId').value = object['mapId'];

            var args = {
                url: "../DPMS/TempFile.action",
                method: "createTemp",
                params: [object['mapId']]
            };
            callMethod(args).then(function (scope) {
                domAttr.set("imgCanvas", "src", scope);
                domClass.remove("imgCanvas", "hidden");
                getZones();
            });
            getItems();
            hideLoader();
        },
        defineValidationRules = function () {
            $('#validate_form').validate({
                rules: {
                    code: {
                        required: true,
                        maxlength: 255
                    },
                    description: {
                        required: true
                    },
                    itemId: {
                        required: true
                    }
                },
                messages: {
                    code: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
                    description: core.i18n.Validate.missingField,
                    itemId: core.i18n.Validate.missingCombo
                }
            });

        },
        changeScope = function() {
          getItems();
          imageTagger.changeScope();  
        },

            imageTagger = new ImageTagger({
                tagItDomId: 'tagit',
                tagItTargetDomId: 'childId'
            });
            var uploader = new FileUploader({
                accept: 'image/*',
                uploadPath: '../DPMS/Upload.fileUploader',
                onChange: function (f) {
                    if(f.contentType.startsWith("image/")){
                        dom.byId("mapId").value = f.id;
                        var args = {
                            url: "../DPMS/TempFile.action",
                            method: "createTemp",
                            params: [f.id]
                        };
                        callMethod(args).then(function (scope) {
                            domClass.remove("imgCanvas", "hidden");
                            lang.hitch(imageTagger, imageTagger.changeCanvas)(scope);
                        });
                    }else{
                        core.dialog(i18n.Validate.fileType);
                    }                    
                },
                    showLoader: core.showLoader,
                    hideLoader: core.hideLoader
            });
            domConstruct.create('option', {value: 0,innerHTML: core.i18n.firstComboElement}, dom.byId('itemId'));
            uploader.placeAt('fUploader');
            /*image tagger*/
            defineValidationRules();
            on(dom.byId('itemId'), 'change', changeScope);
            on(dom.byId('imgCanvas'), 'click', lang.hitch(imageTagger, imageTagger.clickOnImage));
            on(dom.byId('tagMe'), '.taggitSave:click', lang.hitch(imageTagger, imageTagger.saveTag));
            on(dom.byId('tagMe'), '.taggitCancel:click', lang.hitch(imageTagger, imageTagger.cancelTag));
            on(dom.byId('taglist'), '.remove:click', imageTagger.removeTag);
            on(window, 'resize', lang.hitch(imageTagger, imageTagger.resizeWindow));
            on(dom.byId('leftPane'), '.tagview:click', lang.hitch(imageTagger, imageTagger.tagViewClick));
            on(dom.byId('saveBtn'), 'click', save);
            on(dom.byId('cancelBtn'), 'click', core.cancel);
            on(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
            var id = dom.byId('id').value;
            if (id && +id !== -1) {
                load(id);
            } else {
                dom.byId('status').value = 1;
                hideLoader();
            }
    };
    core.setLang('lang/fiveS/item_map/nls/item.map').then(doEverything);
    fillFooter();
});