<?xml version="1.0" encoding="UTF-8"?>
<config
        xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
        xmlns='http://www.ehcache.org/v3'
        xmlns:jsr107='http://www.ehcache.org/v3/jsr107'
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.10.xsd
                        http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.10.xsd">

    <service>
        <jsr107:defaults default-template="defaultCache" enable-management="true" enable-statistics="true"/>
    </service>

    <default-copiers>
        <copier type="java.lang.Object">org.ehcache.impl.copy.IdentityCopier</copier>
    </default-copiers>

    <persistence directory="${java.io.tmpdir}/ehcache/" />

    <cache-template name="defaultCache">
        <expiry>
            <tti unit="days">730</tti>
        </expiry>
        <resources>
            <heap unit="MB">1024</heap>
            <!--offheap unit="MB">512</offheap-->
            <!--disk persistent="true" unit="MB">5120</disk-->
        </resources>
        <heap-store-settings>
            <max-object-graph-size>**********</max-object-graph-size>
        </heap-store-settings>
    </cache-template>

    <cache alias="org.hibernate.cache.spi.QueryResultsRegion">
        <expiry>
            <tti unit="days">730</tti>
        </expiry>
        <resources>
            <heap unit="MB">2048</heap>
            <!--offheap unit="MB">3072</offheap-->
            <!--disk persistent="true" unit="MB">5120</disk-->
        </resources>
        <heap-store-settings>
            <max-object-graph-size>**********</max-object-graph-size>
        </heap-store-settings>
    </cache>

    <cache alias="org.hibernate.cache.spi.TimestampsRegion">
        <expiry>
            <tti unit="days">730</tti>
        </expiry>
        <resources>
            <heap unit="MB">512</heap>
            <!--offheap unit="MB">1024</offheap-->
            <!--disk persistent="true" unit="MB">5120</disk-->
        </resources>
        <heap-store-settings>
            <max-object-graph-size>**********</max-object-graph-size>
        </heap-store-settings>
    </cache>

    <cache alias="usersAccountIds">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.Long</value-type>
        <expiry>
            <tti unit="days">1</tti>
        </expiry>
        <resources>
            <heap unit="MB">128</heap>
            <offheap unit="MB">256</offheap>
            <disk persistent="true" unit="MB">10140</disk>
        </resources>
        <heap-store-settings>
            <max-object-graph-size>**********</max-object-graph-size>
        </heap-store-settings>
    </cache>
    <cache alias="hqlToSqlCache">
        <key-type>java.lang.String</key-type>
        <value-type>java.lang.String</value-type>
        <expiry>
            <tti unit="days">30</tti>
        </expiry>
        <resources>
            <heap unit="MB">256</heap>
            <offheap unit="MB">512</offheap>
            <disk persistent="true" unit="MB">10140</disk>
        </resources>
        <heap-store-settings>
            <max-object-graph-size>**********</max-object-graph-size>
        </heap-store-settings>
    </cache>


</config>
