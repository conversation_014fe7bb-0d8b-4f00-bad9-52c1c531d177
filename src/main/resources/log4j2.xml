<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout disableAnsi="false" pattern="%highlight{[%-5level] \\%d{yyMMdd:HH:mm:ss:SSS}\\%X{usrName}}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=magenta, DEBUG=green bold, TRACE=cyan}%style{:%c{2}:}{magenta} %highlight{%m%n}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=magenta, DEBUG=green bold, TRACE=cyan}" charset="UTF-8"/>
            <!--PatternLayout pattern="%highlight{%d{yyyy/MM/dd HH:mm:ss.SSS} [%X{version}] [%-5level] [%logger{36}] [%X{usrId}] [%X{usrName}] - %m%n}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=magenta, DEBUG=green bold, TRACE=cyan}" charset="UTF-8"/-->
        </Console>
        <Console name="JSON" target="SYSTEM_OUT">
            <JsonLayout locationInfo="false" complete="false" eventEol="true" compact="true" properties="true" />
        </Console>
        <Async name="Async" bufferSize="1000">
            <AppenderRef ref="Console"/>
        </Async>
    </Appenders>
    <Loggers>
        <!-- Struts 2 logging -->
        <Logger name="com.opensymphony" level="ERROR" />
        <Logger name="org.apache.struts2" level="ERROR" />

        <!-- Campos de formularios -->
        <Logger name="qms.form.dao" level="WARN" />

        <!-- SlimReports -->
        <Logger name="qms.framework.dao.bean" level="WARN" />
        
        <!-- Cambiar a DEBUG para visualizar los SQL ejecutados como HQL -->
        <Logger name="org.hibernate.SQL" level="DEBUG"/>
        <!-- Cambiar a TRACE para monitorear los parametros de SQL ejecutados desde Hibernate -->
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="WARN"/>
        <!-- Cambiar a DEBUG para monitorear el caché desde Hibernate -->
        <Logger name="org.hibernate.cache" level="ERROR"/>
        <!-- Cambiar a DEBUG para monitorear el caché desde Ehcache -->
        <Logger name="org.ehcache" level="ERROR"/>
        <!-- Cambiar a TRACE -->
        <Logger name="qms.ExplicitRollback" level="INFO" />
        <!-- Cambiar a TRACE para monitorear el comportamiento completo Hibernate -->
        <Logger name="org.hibernate" level="ERROR" />
        <!-- Cambiar a TRACE para monitorear el proceso de guardado de todos los MAKEPERSISTENT -->
        <Logger name="qms.MakePersistentTraking" level="WARN" />
        <!-- 
                Cambiar a TRACE para monitorear los HQL de todos los getRows con sus CRITERIA
                Cambiar a DEBUG para ver los filtros guardados (SaveWindowFilter) 
        -->
        <Logger name="qms.GridComponent_PagedFilterHQL" level="WARN" />
        <!-- Cambiar a TRACE para monitorear los HQL ejecutados para actualizar el conteo de pendientes -->
        <Logger name="qms.PendingCountTraking" level="WARN" />
        <!-- Conversión a PDF-->
        <Logger name="mx.bnext.core.daemon.ControlPoolExecutor" level="WARN"/>
        <!-- daemons-->
        <Logger name="mx.bnext.core.daemon.BnextDaemon" level="ERROR"/>
        <!-- Initializer -->
        <Logger name="Framework.Config.Initializer" level="DEBUG"/>
        <Logger name="Framework.Config.AppInitializer" level="DEBUG"/>
        <Logger name="Framework.Config.InitializerThread" level="DEBUG"/>
        <!-- CacheQueryInitializer -->
        <Logger name="qms.framework.util.CacheQueryInitializer" level="DEBUG"/>
        <Logger name="qms.framework.util.CacheQueryInitializerThread" level="DEBUG"/>
        <!-- Inicialización de Spring -->
        <Logger name="org.springframework" level="WARN" />
        <Logger name="org.springframework.messaging" level="ERROR"/>
        <Logger name="org.springframework.web.socket" level="ERROR"/>
        <Logger name="org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter" level="FATAL" />
        <Logger name="org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter" level="FATAL" />
        <Logger name="DPMS.DAO" level="WARN" />
        <Logger name="Framework" level="WARN" />
        <!-- Cambiar a TRACE para los HttpRequest del usuario configurado en `Settings.traceRequestUserIds` para la url de `traceRequestUrlRegExp` -->
        <Logger name="TRACE_REQUEST" level="WARN" />
        <!-- Cambiar a TRACE para visualizar la medición de tareas configuradas en MEASURE -->
        <Logger name="MEASURE" level="FATAL" />
        <Logger name="MEASURE.ape.mail.core.MailTask" level="FATAL" />
        <Logger name="MEASURE.qms.document.mail.DocumentMailMonitor" level="FATAL" />
        <Logger name="MEASURE.qms.framework.listeners.MailMonitor" level="FATAL" />
        <Logger name="MEASURE.qms.escalation.monitor.EscalationMailMonitor" level="FATAL" />
        <Logger name="MEASURE.qms.framework.daemon.bean.ThreadFunction" level="FATAL" />
        <Logger name="MEASURE.qms.framework.core.Mailer" level="FATAL" />
        <Logger name="MEASURE.qms.framework.pdf.rendering.PdfPageFactory" level="FATAL" />
        <Logger name="MEASURE.qms.workflow.util.WorkflowPreviewGenerator" level="FATAL" />
        <Logger name="MEASURE.qms.form.util.SurveyUtil" level="FATAL" />
        <Logger name="MEASURE.qms.form.util.SurveySetup" level="FATAL" />
        <Logger name="MEASURE.qms.form.dao.FormCaptureDAO" level="FATAL" />
        <Logger name="MEASURE.qms.framework.util.MailTokenManager" level="FATAL" />
        <Logger name="MEASURE.qms.planner.dao.IPlannerDAO" level="FATAL" />
        <Logger name="MEASURE.qms.framework.core.HibernateCacheHandler" level="FATAL" />
        <Logger name="MEASURE.qms.activity.periodicity.ActivityVerificationManager" level="FATAL" />
        <Logger name="MEASURE.qms.document.util.PdfFilesManager" level="FATAL" />
        <Logger name="ape" level="WARN" />
        <Logger name="ape.mail.core" level="WARN" />
        <Logger name="bnext" level="WARN" />
        <Logger name="bnext.ibm.maximo.api" level="WARN" />
        <Logger name="bnext.login" level="WARN" />
        <Logger name="bnext.login.Login" level="WARN" />
        <Logger name="bnext.login.logic" level="INFO" />
        <Logger name="bnext.login.rest" level="INFO" />
        <Logger name="bnext.resources.DynamicCssService" level="DEBUG" />
        <Logger name="isoblock" level="WARN" />
        <Logger name="isoblock.common.ThreadFunction" level="INFO" />
        <Logger name="javax.servlet" level="INFO" />
        <Logger name="mx.bnext.core" level="WARN" />
        <Logger name="mx.bnext.core.pdf.PdfDaemon" level="ERROR"/>  
        <Logger name="mx.bnext.core.pdf.pdfgenerator.PdfGeneratorConversor" level="WARN"/>       
        <Logger name="mx.bnext.core.pdf.pdfgenerator.PdfGeneratorExecutor" level="WARN"/>
        <Logger name="mx.bnext.core.pdf.pdfgenerator.rest.PdfGeneratorRestService" level="WARN"/>
        <Logger name="mx.bnext.core.pdf.pdfgenerator.websocket.PdfGeneratorStompHandler" level="WARN"/>
        <Logger name="qms.timework.monitor.TimeworkMonitor" level="ERROR" />
        <Logger name="net.sf.jasperreports.repo" level="WARN" />
        <Logger name="org.springframework.ws.client.MessageTracing" level="WARN" />
        <Logger name="qms.access.util" level="WARN" />
        <Logger name="qms.util" level="WARN" />
        <Logger name="qms.APE" level="WARN" />
        <Logger name="qms.APE.qms.activity.pending.imp.ToComplete" level="WARN" />
        <Logger name="qms.DAEMON" level="WARN" />
        <Logger name="qms.DynamicSearch" level="WARN" />
        <Logger name="qms.GridComponent_PagedFilterSQL" level="WARN" />
        <Logger name="qms.PING" level="INFO" />
        <Logger name="qms.STARTUP" level="INFO" />
        <Logger name="qms.TRESS" level="INFO" />
        <Logger name="qms.activity.dao" level="WARN" />
        <Logger name="qms.framework.daemon.DailyScheduler" level="INFO"/>
        <Logger name="qms.framework.daemon.HourlyScheduler" level="INFO"/>
        <Logger name="qms.framework.util.MeasureTime" level="TRACE"/>
        <Logger name="qms.framework.logic.BackendUploaderAction" level="ERROR"/>
        <Logger name="mx.bnext.cipher.HexUtil" level="FATAL"/>
        <Logger name="waffle.apache" level="WARN" />
        <Logger name="waffle.servlet" level="WARN" />
        <Logger name="waffle.windows.auth" level="WARN" />
        <Logger name="qms.framework.core.HibernateCacheUtil" level="INFO" />
        <Logger name="qms.framework.core.HibernateCacheHandler" level="ERROR" />
        <Logger name="qms.framework.util.CompressUtils" level="ERROR" />
        <Logger name="qms.form.util.SurveyUtil" level="ERROR" />
        <!-- Liquibase -->
        <Logger name="liquibase" level="ERROR" />
        <Root level="ERROR">
            <AppenderRef ref="Async"/>
        </Root>
    </Loggers>
</Configuration>
