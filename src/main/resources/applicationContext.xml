<?xml version="1.0" encoding="UTF-8"?>
<beans default-lazy-init="true"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="${spring-aop-file}"/>
    <!-- spring security and mvc configuration -->
    <context:component-scan base-package="bnext.login.bean"/>
    <bean lazy-init="true" class="bnext.login.config.SecurityConfiguration"/>

    <!-- Para la injection después de la creación de los objetos por struts -->
    <bean lazy-init="true" class="org.springframework.orm.jpa.support.PersistenceAnnotationBeanPostProcessor" />
    <!--
        PROPAGATION_REQUIRED – Support a current transaction; create a new one if none exists.
        PROPAGATION_SUPPORTS – Support a current transaction; execute non-transactionally if none exists.
        PROPAGATION_MANDATORY – Support a current transaction; throw an exception if no current transaction exists.
        PROPAGATION_REQUIRES_NEW – Create a new transaction, suspending the current transaction if one exists.
        PROPAGATION_NOT_SUPPORTED – Do not support a current transaction; rather always execute non-transactionally.
        PROPAGATION_NEVER – Do not support a current transaction; throw an exception if a current transaction exists.
        PROPAGATION_NESTED – Execute within a nested transaction if a current transaction exists, behave like PROPAGATION_REQUIRED else. 
    -->
    <!-- The Proxy parent! just to configurate the proxy -->
    <bean lazy-init="true" id="baseTransactionProxy" class="org.springframework.transaction.interceptor.TransactionProxyFactoryBean"
          abstract="true" depends-on="DAO">
        <property name="transactionManager" ref="transactionManager"/>
        <property name="transactionAttributes">
            <props>
                <prop key="*" >PROPAGATION_REQUIRED</prop>
            </props>
        </property>
    </bean>
    <bean lazy-init="true" id="buildInfo" class="mx.bnext.core.util.BuildInfo">
        <property name="projectVersion" value="${app.projectVersion}" />
        <property name="buildVersion" value="${app.buildVersion}" />
        <property name="buildProfile" value="${p.Build-Git-Info}${p.Build-MavenSite}${p.Build-Tomcat-Resources}${p.Skip-Unit-Test}${p.Skip-Compile}${p.Skip-War}${p.Build-AspectJ-CompileTime}${p.Build-Angular}${p.Run-CheckJavaRules}${p.Build-War}${p.Run-Liquibase}${p.Run-Integration-Test}" />
        <property name="appName" value="${app.name}" />
        <property name="buildNumber" value="${app.buildNumber}" />
        <property name="buildDate" value="${app.buildDate}" />
        <property name="buildDescription" value="${app.buildDescription}" />
        <property name="revisionDate" value="${app.revisionDate}" />
    </bean>
    <!-- The Proxy! intercepts bean "DAO" to set the transaction manager -->
    <bean lazy-init="true" id="daoProxy" parent="baseTransactionProxy" depends-on="transactionManager" scope="prototype" >
        <property name="target" ref="DAO" />
    </bean>
    <!-- DAO! creates the interface to access GenericDAOImpl methods -->
    <bean lazy-init="true" id="DAO" class="Framework.DAO.GenericDAOImpl" scope="prototype">
        <property name="IS_SQL_SERVER" value="true" />
    </bean>
    <bean lazy-init="true" id="DAOold" class="Framework.DAO.GenericHibernateDAO" scope="prototype">
        <property name="IS_SQL_SERVER" value="true" />
    </bean>
    <!-- The DAO factory! creates INSTANCES of brandnew DAO's -->
    <bean lazy-init="true" id="daoFactory" class="qms.framework.dao.bean.daoFactory" depends-on="daoProxy">
        <lookup-method name="getSpringBeanDao" bean="daoProxy"/>
    </bean>
    <bean lazy-init="false" class="Framework.Config.ApplicationContextProvider" id="applicationContextProvider" />
    <!-- the majestic session factory, creates session's -->
    <bean lazy-init="true" id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean" depends-on="dataSource">
        <property name="dataSource" ref="dataSource"/>
        <property name="jpaVendorAdapter">
            <bean lazy-init="true" class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter" />
        </property>
        <property name="jpaProperties">
            <props>
                <prop key="hibernate.dialect">#{dbProperties.hibDialect}</prop>
                <prop key="hibernate.max_fetch_depth">3</prop>
                <prop key="hibernate.ejb.entitymanager_factory_name">sessionFactory</prop>
                <prop key="hibernate.model.generator_name_as_sequence_name">false</prop>
                <prop key="hibernate.id.new_generator_mappings">false</prop>
                <prop key="hibernate.cache.use_second_level_cache">true</prop>
                <prop key="hibernate.cache.use_query_cache">true</prop>
                <prop key="hibernate.cache.region.factory_class">org.hibernate.cache.jcache.JCacheRegionFactory</prop>
                <!-- Parche de ehcache2 para evitar que el servidor se congele cuando se ajusta el reloj con una hora en el pasado -->
                <!-- <prop key="hibernate.cache.region.factory_class">qms.framework.util.BnextJCacheRegionFactory</prop> -->
                <prop key="hibernate.javax.cache.provider">org.ehcache.jsr107.EhcacheCachingProvider</prop>
                <prop key="hibernate.javax.cache.missing_cache_strategy">create</prop>
                <prop key="hibernate.javax.cache.uri">#{ new org.springframework.core.io.ClassPathResource("/ehcache.xml").getURI().toString()}</prop>
                <!-- hibernate hql interceptor! createQuery will be intercepted here -->
                <prop key="hibernate.query.factory_class">Framework.DAO.BnextASTQueryTranslatorFactory</prop>
                <!-- hibernate entity interceptor! makePersistece and else's entities will be intercepted here -->
                <prop key="hibernate.ejb.interceptor">Framework.DAO.EntityInterceptor</prop>
            </props>
        </property>
        <property name="packagesToScan" >
            <list>
                <value>ape.pending.entities</value>
                <value>bnext.device.master</value>
                <value>bnext.reference.device</value>
                <value>bnext.reference.document</value>
                <value>bnext.reference</value>
                <value>DPMS.Mapping</value>
                <value>isoblock.surveys.dao.hibernate</value>
                <value>qms.access.entity</value>
                <value>qms.activity.entity</value>
                <value>qms.audit.entity</value>
                <value>qms.breaks.entity</value>
                <value>qms.complaint.entity</value>
                <value>qms.configuration.entity</value>
                <value>qms.custom.entity</value>
                <value>qms.device.entity</value>
                <value>qms.document.entity</value>
                <value>qms.escalation.entity</value>
                <value>qms.finding.entity</value>
                <value>qms.finding.pending</value>
                <value>qms.form.entity</value>
                <value>qms.framework.bulk.entity</value>
                <value>qms.framework.entity</value>
                <value>qms.framework.i18n</value>
                <value>qms.happyornot.entity</value>
                <value>qms.meeting.entity</value>
                <value>qms.meter.entity</value>
                <value>qms.planner.entity</value>
                <value>qms.poll.entity</value>
                <value>qms.preferences.entity</value>
                <value>qms.survey.entity</value>
                <value>qms.template.entity</value>
                <value>qms.timesheet.entity</value>
                <value>qms.timework.entity</value>
                <value>qms.tress.entity</value>
                <value>qms.workflow.entity</value>
            </list>
        </property>
    </bean>
    <bean lazy-init="true" id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager" depends-on="entityManagerFactory">
        <property name="entityManagerFactory" ref="entityManagerFactory" />
        <property name="nestedTransactionAllowed" value="true" />
    </bean>
    <!-- The Magic! (Anotations), beans will automatically be created as 
    long as it is in a package defined in your component scan @Repository -->
    <!--
    -  ToDo:
    -       Quitar "component-scan" y colocar directamente los BEANS para mejorar performance en
    -       el arranque, revisar si se pueden especificar cuales BEANS implementan AOP.
    -->
    <context:component-scan base-package="qms.workflow.bean, mx.bnext.qms, qms.planner.dao, qms.planner.rest, bnext.login.rest, qms.template.rest, qms.template.dao, qms.happyornot.dao, qms.happyornot.rest, qms.form.rest, qms.audit.rest, qms.access.rest, qms.timework.dao, qms.timework.rest, qms.timesheet.rest, qms.timesheet.dao, qms.document.rest, qms.configuration.rest, bnext.resources, qms.framework.dao.bean, qms.framework.daemon.bean, qms.custom.dao, DPMS.DAO, bnext.device.dao, qms.form.dao, qms.finding.dao, qms.meeting.dao, qms.tress.dao, qms.activity.dao, qms.document.dao, qms.complaint.dao, qms.escalation.bean, ape.pending.dao, qms.tress.dao, qms.breaks.dao, qms.configuration.dao, qms.survey.dao, qms.activity.rest, ape.pending.rest, qms.docs.rest, qms.preferences.dao, qms.preferences.rest, qms.finding.rest, qms.complaint.rest">
    </context:component-scan>
    <context:component-scan base-package="qms.framework.security.impl"/>

    <bean lazy-init="true" class="org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor"/>
    <bean lazy-init="true" id="liquidbase" class="liquibase.integration.spring.BnextSpringLiquibase">
        <property name="dataSource" ref="dataSource" />
        <property name="buildInfo" ref="buildInfo" />
        <property name="changeLog" value="classpath:db.xml" />
    </bean>
    <bean lazy-init="true" id="dataSource"
          class="qms.framework.database.BnextDataSource"
          depends-on="hikariDataSource"
          destroy-method="close">
        <property name="dataSource" ref="hikariDataSource" />
    </bean>
    <bean lazy-init="true" id="dbProperties" class="qms.framework.util.DatabasePropertiesReader">
    </bean>
    <bean lazy-init="true" id="hikariDataSource"
          class="com.zaxxer.hikari.HikariDataSource"
          depends-on="buildInfo"
          destroy-method="close">
        <property name="driverClassName" value="#{dbProperties.driverClassName}" />
        <property name="jdbcUrl" value="#{dbProperties.jdbcUrl}" />
        <property name="username" value="#{dbProperties.username}" />
        <property name="password" value="#{dbProperties.password}" />
        <property name="transactionIsolation" value="#{dbProperties.transactionIsolation}" />
        <property name="maximumPoolSize" value="#{dbProperties.maximumPoolSize}" />
        <property name="registerMbeans" value="true" />
        <property name="connectionInitSql" value="SET LANGUAGE ENGLISH" />
    </bean>
</beans>
