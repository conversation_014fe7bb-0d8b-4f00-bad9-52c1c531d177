weekOfYearGroupName = Semana {week} de {year}
yes = S\u00ed
no = No
PROGRESS = Reportar porcentaje de avances
CHILDS = Lista de actividades
FILL_FORM = Llenar formulario
MARK_DONE = Marcar realizada o no realizada
PLANNER = Seguimiento a proyecto
PLANNER_TASK = Seguimiento a tarea
ROW_ID = ID
ROW_PARENT = Padre    
ROW_TYPE = Tipo de actividad
ROW_CODE = Clave
ROW_DESCRIPTION = Descripci\u00f3n
ROW_IMPLEMENTERS = Implementador(s)
ROW_VERIFIER = Verificador
ROW_START_DATE = Fecha de inicio
ROW_IMPLEMENTATION_DATE = Fecha de implementaci\u00f3n
ROW_VERIFICATION_DATE = Fecha de verificaci\u00f3n
ROW_DAYS_TO_VERIFY = D\u00eda(s) para verificar
ROW_FILL_TYPE = Modo de llenado
ROW_FILL_FORM = Formulario a llenar
ROW_OBJECTIVE = Objetivo
ROW_IS_PLANNED = Es planeada
ROW_BUSINESS_UNIT_DEPARTMENT = Departamento responsable
ROW_SYSTEM_LINK = Ligas a otros sistemas
ROW_CATEGORY = Categor\u00eda
ROW_SOURCE = Fuente
ROW_PRIORITY = Prioridad
ROW_CLIENT = Cliente
ROW_PLANNER = Proyecto
ROW_TASK = Tarea
ROW_PLANNED_HOURS = Horas estimadas
ROW_ACTIVITY_ORDER = Orden
ROW_DYNAMIC_FIELDS = Campos din\u00e1micos
ROW_PRE_IMPLEMENTER = Responsable(s) pre-asignado(s)
VARIABLE_NAME = Nombre
VARIABLE_VALUE = Valor
parseRowsError = Error al leer actividades del archivo
parseVariablesError = Error al leer variables del archivo
parseError = Error al leer el archivo
sheetActivitiesName = Actividades
sheetVariablesName = Variables
sheetSaveResultsName = Resultados
sheetHelpName = Ayuda
CatalogField.value = ID
CatalogField.code = Clave
CatalogField.text = Descripci\u00f3n
CatalogField.description = Descripci\u00f3n
CatalogField.version = Versi\u00f3n
CatalogField.account = Cuenta
CatalogField.mail = Correo
CatalogField.clientCode = Clave de cliente
CatalogField.plannerCode = Clave de proyecto
CatalogField.userDefinedCode = Clave de departamento
emptyFile = El archivo no tiene contenido
emptyRowsHeader = El encabezado de las actividades debe tener valor
emptyVariablesHeader = El encabezado de las variables debe tener valor
unkonowVariablesHeader =  Valor de encabezado de las variables inv\u00e1lido
hedersError = Existe al menos un encabezado inv\u00e1lido
requiredField = El {field} es un campo requerido
repeatedField = El {field} es un campo que debe tener valores \u00fanicos
invalidFieldValue = Valor inv\u00e1lido
notFoundParent = No es econtr\u00f3 un registro con el {targetField} del {sourceField}
parseDateField = No se puede convertir a fecha el  campo {field}. La fecha debe estar en formato {format}
outOfRangeDateField = El campo {field} esta fuera del rango de fechas permitido de {min} a {max}. La fecha debe estar en formato {format}
computeDaysField = No se pudo calcular los d\u00edas de {field}
parseNumberField = No se puede convertir a n\u00famero el  campo {field}
parseFillType = Valor inv\u00e1lido de {field}
catalogNotFound = No se encontr\u00f3 el valor del campo {field} en el cat\u00e1logo. Por favor revise que el valor exista en {app} y la configuraci\u00f3n del tipo de actividad
catalogNotFoundWithItems = No se encontr\u00f3 el valor {items} del campo {field} en el cat\u00e1logo. Por favor revise que el valor exista en {app} y la configuraci\u00f3n del tipo de actividad
dataMismatch = El {sourceField} de {targetField} no coincide con el campo {sourceField}
parentNotFound =  No se encuentra el valor de  {sourceField} en el campo {targetField}
maxSubtaskLevelReached =  Nivel m\u00e1ximo de subtarea de {maxLevel} alcanzado
saveException = Ocurri\u00f3 en el guardado de actividades. Detalles {error}
SAVE_SUCCESS = Guardado exitoso
SAVE_FAILED = Error en guardado
SAVE_RESULTS_CODE = Clave
SAVE_RESULTS_DESCRIPTION = Descripci\u00f3n
SAVE_RESULTS_STATUS = Estado
SAVE_RESULTS_PARENT = Padre
SAVE_RESULTS_LINK = Liga
HELP_SHEET = Hoja
HELP_FIELD = Campo
HELP_TYPE = Tipo
HELP_DESCRIPTION = Descripci\u00f3n
templateName = Plantilla de alta de actividades
notSupportedDynamicFieldType = Tipo {type} de campo din\u00e1mico no soportado
notFoundOptionDynamicFieldType = El valor {value} no es una opci\u00f3n v\u00e1lida del campo din\u00e1mico
dataValidationError = Error en validaci\u00f3n de datos
invalidDynamicFields = Valores inv\u00e1lidos de campos din\u00e1micos
maximumCharactersExceeded = Caracteres m\u00e1ximos excedidos de {maxCharacters}
HELP_TYPE_ROW_ID = N\u00famerico
HELP_DESCRIPTION_ROW_ID = Para crear subtareas se requiere asignar un {ROW_ID}
HELP_TYPE_ROW_PARENT = N\u00famerico
HELP_DESCRIPTION_ROW_PARENT = Para crear como subtarea se captura el {ROW_ID} del registro padre    
HELP_TYPE_ROW_TYPE = Cat\u00e1logo
HELP_DESCRIPTION_ROW_TYPE = Se puede capturar la clave o descripci\u00f3n del registro del Tipo
HELP_TYPE_ROW_CODE = Alfanum\u00e9rico
HELP_DESCRIPTION_ROW_CODE = Debe ser \u00fanico
HELP_TYPE_ROW_DESCRIPTION = Alfanum\u00e9rico
HELP_DESCRIPTION_ROW_DESCRIPTION = Detalle de la actividad
HELP_TYPE_ROW_IMPLEMENTERS = Cat\u00e1logo
HELP_DESCRIPTION_ROW_IMPLEMENTERS = Se puede capturar la clave, cuenta o descripci\u00f3n del usuario, permite m\u00faltiple valores separados por coma
HELP_TYPE_ROW_VERIFIER = Cat\u00e1logo
HELP_DESCRIPTION_ROW_VERIFIER = Se puede capturar la clave, cuenta o descripci\u00f3n del usuario
HELP_TYPE_ROW_START_DATE = Fecha
HELP_DESCRIPTION_ROW_START_DATE = Fecha en formato {format}
HELP_TYPE_ROW_IMPLEMENTATION_DATE = Fecha
HELP_DESCRIPTION_ROW_IMPLEMENTATION_DATE = Fecha en formato {format}
HELP_TYPE_ROW_VERIFICATION_DATE = Fecha
HELP_DESCRIPTION_ROW_VERIFICATION_DATE = Fecha en formato {format}
HELP_TYPE_ROW_DAYS_TO_VERIFY = Num\u00e9rico
HELP_DESCRIPTION_ROW_DAYS_TO_VERIFY = Se puede capturar los d\u00edas disponibles para veriificar
HELP_TYPE_ROW_FILL_TYPE = Cat\u00e1logo
HELP_DESCRIPTION_ROW_FILL_TYPE = Se puede capturar uno de los valores: {PROGRESS}, {FILL_FORM}, {MARK_DONE}.
HELP_TYPE_ROW_FILL_FORM = Cat\u00e1logo
HELP_DESCRIPTION_ROW_FILL_FORM = Se puede capturar la clave o descripci\u00f3n de un formulario, se utilizar al capturar el valor {FILL_FORM} en el campo {ROW_FILL_TYPE}
HELP_TYPE_ROW_OBJECTIVE = Cat\u00e1logo
HELP_DESCRIPTION_ROW_OBJECTIVE = Se puede capturar la clave o descripci\u00f3n del objetivo
HELP_TYPE_ROW_IS_PLANNED = Cat\u00e1logo
HELP_DESCRIPTION_ROW_IS_PLANNED = Se debe capturar el valor S\u00ed o No
HELP_TYPE_ROW_BUSINESS_UNIT_DEPARTMENT = Cat\u00e1logo
HELP_DESCRIPTION_ROW_BUSINESS_UNIT_DEPARTMENT = Se captura la descripci\u00f3n de la relaci\u00f3n entre planta y departamento.
HELP_TYPE_ROW_SYSTEM_LINK = Alfanum\u00e9rico
HELP_DESCRIPTION_ROW_SYSTEM_LINK = ID de liga a otra sistema
HELP_TYPE_ROW_CATEGORY = Cat\u00e1logo
HELP_DESCRIPTION_ROW_CATEGORY = Se puede capturar la clave o descripci\u00f3n de la categor\u00eda
HELP_TYPE_ROW_SOURCE = Cat\u00e1logo
HELP_DESCRIPTION_ROW_SOURCE = Se puede capturar la clave o descripci\u00f3n de la fuente
HELP_TYPE_ROW_PRIORITY = Cat\u00e1logo
HELP_DESCRIPTION_ROW_PRIORITY = Se puede capturar la clave o descripci\u00f3n de la prioridad
HELP_TYPE_ROW_CLIENT = Cat\u00e1logo
HELP_DESCRIPTION_ROW_CLIENT = Se puede capturar la clave o descripci\u00f3n del cliente
HELP_TYPE_ROW_PLANNER = Cat\u00e1logo
HELP_DESCRIPTION_ROW_PLANNER = Se puede capturar la clave o descripci\u00f3n del proyecto
HELP_TYPE_ROW_TASK = Cat\u00e1logo
HELP_DESCRIPTION_ROW_TASK = Se puede capturar la clave o descripci\u00f3n de la tarea
HELP_TYPE_ROW_PLANNED_HOURS = Decimal
HELP_DESCRIPTION_ROW_PLANNED_HOURS = Se debe capturar las horas estimadas
HELP_TYPE_VARIABLE_NAME = Alfanum\u00e9rico
HELP_DESCRIPTION_VARIABLE_NAME = Debe ser \u00fanico, cuando este valor coincide con el valor de una celda de {sheetActivitiesName} el valor de la celta es reemplazado por el valor de esta variable.
HELP_TYPE_VARIABLE_VALUE = Alfanum\u00e9rico
HELP_DESCRIPTION_VARIABLE_VALUE = Valor de la variable, debe cumplir con los requerimientos de la celta destino.
HELP_TYPE_ROW_ACTIVITY_ORDER = Num\u00e9rico
HELP_DESCRIPTION_ROW_ACTIVITY_ORDER = Debe ser un valor num\u00e9rico.
HELP_TYPE_ROW_PRE_IMPLEMENTER = Alfanum\u00e9rico
HELP_DESCRIPTION_ROW_PRE_IMPLEMENTER =  Se puede capturar la clave, cuenta o descripci\u00f3n del usuario, permite m\u00faltiple valores separados por coma