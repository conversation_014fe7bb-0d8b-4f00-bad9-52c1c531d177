header = Notification from DOCUMENTS module
subject = Notification from documents
label.title = REQUEST DETAILS
label.list.title = REQUESTS DETAILS
unassigned = -- Unassigned --
status.expired = Expired
status.archived = Archived
status.requested = Requested
status.return = Return
status.declined = Declined
status.inProcess = In process
status.completed = Completed
status.active = Active
label.status = Status 
label.code = ID
label.documentCode = Document ID
label.documentTitle = Document title
label.creationDate = Creation date
label.author = Document author
label.reason = Reason
label.version = Version
label.businessUnit = ${Facility}
label.department = Department
label.authorizerDescription = Authorizer
label.delayedDays = Commitment
label.link = Pending
label.allowedDate = Commitment date
delayedDaysBefore = :days day(s) ago
delayedDaysAfter = In :days day(s)
title.requestNewDocument = You have a new document request pending.
title.requestDocumentModification = You have a document modification request pending.
title.requestDocumentEditDetails = You have a document details editing request to verify.
title.requestDocumentApproval = You have a re-approval document request pending.
title.requestDocumentCancellation = You have cancellation document request pending.
title.requestNewManagerOrganization = The next request has been assigned to your organization and is pending for verification.
title.requestNewManagerBusinessUnit = The next request has been assigned to your ${facility} and is pending for verification.
title.requestNewManagerDepartment = The next request has been assigned to your department and is pending for verification.
title.requestVerified = Your request was verified, the document is pending for authorization.
subject.requestVerified = Verified request
title.requestVerifiedPublished = The request has been verified and approved.
subject.requestVerifiedPublished = Verified request
title.newDocumentPublishedAuthor = The new document request you created was authorized.
subject.newDocument = New document
title.newDocumentPublishedModuleManager = A new document request was authorized and you have authorization from the document manager.
title.newDocumentPublishedDocumentManagerInBusinessUnitWithReaders = A new document has been released, please assign readers to it. You received this message because you are the document manager of ${theFacility}.
title.newDocumentPublishedDocumentManagerInBusinessUnit = A new document has been released. You received this message because you are the document manager of ${theFacility}.
title.newDocumentPublishedDocumentManagerInDepartmentWithReaders = A new document has been released, please assign readers to it. You received this message because you are the document manager of the department.
title.newDocumentPublishedDocumentManagerInDepartment = A new document has been released. You received this message because you are the document manager of the department.
title.newDocumentPublishedReader = A new document request was authorized and you were assigned as a reader.
title.newDocumentPublishedBusinessUnitPermission = A new document request was authorized and you were granted access to the document by ${theFacility} you are assigned to.
title.newDocumentPublishedProcessPermission = A new document request was authorized and you were granted access to the document by the process you are assigned to.
title.newDocumentPublishedDepartmentPermission = A new document request was authorized and you were granted access to the document by the department you are assigned to.
title.newDocumentPublishedUserPermission = A new document request was authorized and you have access to the document because you have user authorization on the folder.
title.newDocumentPublishedAuthorizers = A new document request were you were authorizer has been fully authorized.
title.documentReapprovedAuthor = The re-approval request you created was authorized.
subject.documentReapproved = Re-approved document
title.documentReapprovedModuleManager = A re-approval request was authorized and you have authorization from the document manager.
title.documentReapprovedDocumentManagerInBusinessUnit = A re-approval request was authorized and you are the document manager of ${theFacility}.
title.documentReapprovedDocumentManagerInDepartment = A re-approval request was authorized and you are the document manager of the department.
title.documentReapprovedBusinessUnitPermission = A re-approval request was authorized and you were granted access to the document by ${theFacility} you are assigned to.
title.documentReapprovedProcessPermission = A re-approval request was authorized and you were granted access to the document by the process you are assigned to.
title.documentReapprovedDepartmentPermission = A re-approval request was authorized and you were granted access to the document by the department you are assigned to.
title.documentReapprovedUserPermission = A re-approval request was authorized and you have access to the document because you have user authorization on the folder.
title.documentReapprovedAuthorizers = A re-approval request were you were authorizer has been fully authorized.
title.documentModifiedAuthor = The modification request you created was authorized.
title.documentDetailsEditedAuthor = The request to edit details of the document you created has been authorized.
subject.documentModified = Modified document
subject.documentDetailsEdited = Edited document details
title.documentModifiedModuleManager = A modification request was authorized and you have authorization from the document manager.
title.documentDetailsEditedModuleManager = A request to edit document details has been authorized and you have manager permission on documents.
title.documentModifiedDocumentManagerInBusinessUnitWithReaders = A modification to a document has been released, please assign readers to it. You received this message because you are the document manager of ${theFacility}.
title.documentModifiedDocumentManagerInBusinessUnit = A modification to a document has been released, you received this message because you are the document manager of ${theFacility}.
title.documentModifiedDocumentManagerInDepartmentWithReaders = A modification to a document has been released, please assign readers to it. You received this message because you are the document manager of the department.
title.documentModifiedDocumentManagerInDepartment = A modification to a document has been released, you received this message because you are the document manager of the department.
title.documentDetailsEditedDocumentManagerInDepartment = A document detail edit has been published, you are being notified because you are the department's document manager.
title.documentModifiedReader = A modification request was authorized and you were assigned as a reader.
title.documentModifiedBusinessUnitPermission = A modification request was authorized and you were granted access to the document by ${theFacility} you are assigned to.
title.documentModifiedProcessPermission = A modification request was authorized and you were granted access to the document by the process you are assigned to.
title.documentModifiedDepartmentPermission = A modification request was authorized and you were granted access to the document by the department you are assigned to.
title.documentModifiedUserPermission = A modification request was authorized and you have access to the document because you have user authorization on the folder.
title.documentModifiedAuthorizers = A modification request were you were authorizer has been fully authorized.
title.documentDetailsEditedAuthorizers = A request to edit document details has been authorized and you were the authorizer.
title.documentCancelledAuthor = The cancellation request you created was authorized.
subject.documentCancelled = Cancelled document
title.documentCancelledModuleManager = A cancellation request was authorized and you had authorization from the document manager.
title.documentCancelledDocumentManagerInBusinessUnit = A cancellation request was authorized and you are the document manager of ${theFacility}.
title.documentCancelledDocumentManagerInDepartment = A cancellation request was authorized and you are the document manager of the department.
title.documentCancelledReader = A cancellation request was authorized and you were assigned as a reader.
title.documentCancelledBusinessUnitPermission = A cancellation request was authorized and you were granted access to the document by ${theFacility} you are assigned to.
title.documentCancelledProcessPermission = A cancellation request was authorized and you were granted access to the document by the process you are assigned to.
title.documentCancelledDepartmentPermission = A cancellation request was authorized and you were granted access to the document by the department you are assigned to.
title.documentCancelledUserPermission = A cancellation request was authorized and you have access to the document because you have user authorization on the folder.
title.documentCancelledAuthorizers = A cancellation request were you were authorizer has been fully authorized.
title.authorizeNewDocument = You have a new document request pending.
title.authorizeDocumentModification = You have a document modification request pending.
title.authorizeDocumentEditDetails = You have a document details editing request pending.
title.authorizeDocumentCancellation = You have a new document cancellation request  pending.
title.authorizeDocumentReapproval = You have a new document re-approval request  pending.
title.authorizationRejected = The request of the document was rejected by the user :user, reason: :reason
subject.authorizationRejected = Rejected authorization
title.rejectedVerification = The verification of the request has been rejected.
subject.rejectedVerification = Rejected verification
title.requestsInTimeAuthorizer = You have the following pending requests to authorize
title.requestsDelayedAuthorizer = You have the following request with authorization expired
title.requestsDelayedModuleManager = The time to authorize the following documents has ended. &nbsp;
title.requestsEscalate = The following request has exceeded the time permitted for authorization:
title.automaticPositionAuthorization = Your pending in the flow of the application has been automatically authorized by the system in your position :position.
title.automaticUserAuthorization = Your pending in the flow of the application has been authorized by the system automatically.
title.requestAutoApproved.verificator = Has allowed an request automatically, because the authorizer :username: is idle and you are verifier.
title.requestAutoApproved.departmentMgr = It has allowed an request automatically, because the authorizer :username: is idle and you are the department document manager.
title.requestAutoApproved.businessUnitMgr = It has allowed an request automatically, because the authorizer :username: is idle and you are the plant document manager.
title.requestAutoApproved.documentMgr = It has allowed an request automatically, because the authorizer :username: is idle and have the permission document manager.
title.cancelledFillForm = The request to fill the form was cancelled
title.toDeliverPhysicalCopy = The following modification request has been authorized where you must deliver :count controlled copies because you are the <b>department document manager</b>.
title.reminderToDeliverPhysicalCopy = There are :count controlled copies of documents to deliver where you are the <b>department document manager</b>.
subject.requestAutoApproved = Auto approval