package qms.activity.bulk.logic;

import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Locale;
import mx.bnext.core.file.FileHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkSaveResult;
import qms.activity.bulk.util.ActivityBulkConstants;
import qms.activity.bulk.util.ActivityBulkDataError;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.bulk.util.ActivityBulkDynamicFieldError;
import qms.activity.bulk.util.ActivityBulkFileError;
import qms.activity.bulk.util.ActivityBulkSchemaError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MaximumCharactersError;
import qms.framework.util.MeasureTime;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class ActivityBulkWriterTest {
    
    private ILoggedUser admin;
    private Locale locale;
    private ActivityBulkConfig config;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkManager manager = new ActivityBulkManager(config);
            final ActivityBulkSaveResult result = manager.save(true, template, admin);
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final Path file = writer.writeSaveResult("writer-result-testValidTemplate", result);
            assertNotNull(file, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkFileError thrown = assertThrows(ActivityBulkFileError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testEmpty", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testEmpty", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkDataError thrown = assertThrows(ActivityBulkDataError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testMissingFields", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testMissingFields", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }

    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final MaximumCharactersError thrown = assertThrows(MaximumCharactersError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testInvalidLength", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            final String message = config.getTags().getTag("maximumCharactersExceeded")
                    .replace("{maxCharacters}", ActivityBulkConstants.MAX_DESCRIPTION_LENGTH.toString());
            assertEquals(message, thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testInvalidLength", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkDataError thrown = assertThrows(ActivityBulkDataError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingTaskXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testMissingTask", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testMissingTask", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");

        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }

    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkSchemaError thrown = assertThrows(ActivityBulkSchemaError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidHeadersXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testInvalidHeaders", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("hedersError"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testInvalidHeaders", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidHeaders test.");
        }
    }

    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkManager manager = new ActivityBulkManager(config);
            final ActivityBulkSaveResult result = manager.save(true, template, admin);
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final Path file = writer.writeSaveResult("writer-testDynamicFields", result);
                assertNotNull(file, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }

    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkDynamicFieldError thrown = assertThrows(ActivityBulkDynamicFieldError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testInvalidDynamicFields", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testInvalidDynamicFields", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }

    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final ActivityBulkDynamicFieldError thrown = assertThrows(ActivityBulkDynamicFieldError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityBulkManager manager = new ActivityBulkManager(config);
                final ActivityBulkSaveResult result = manager.save(true, template, admin);
                final Path file = writer.writeSaveResult("writer-result-testLongDynamicFields", result);
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
            final Path errorFile = writer.writeError("writer-error-testLongDynamicFields", thrown, config, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }
    
    @Test
    public void testWriteTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkWriter writer = new ActivityBulkWriter(config);
            final Path temp = FileHandler.createTempFile("writer-template-testWriteTemplate", ".xlsx", config.getFileManager().getTempFolder());
            try (final OutputStream output = Files.newOutputStream(temp)) {
                final Long activityTypeId = 1L;
                writer.writeTemplate(activityTypeId, "writer-template-testWriteTemplate-temp", output, config, admin);
            }
            assertNotNull(temp, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }
    
}
