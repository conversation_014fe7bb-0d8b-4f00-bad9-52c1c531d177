package qms.activity.bulk.logic;

import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import java.nio.file.Path;
import java.util.Locale;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkUploadResult;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;

/**
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ActivityBulkUploaderTest {

    private ILoggedUser admin;
    private Locale locale;
    private ActivityBulkConfig config;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testValidTemplate", template, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testEmpty", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }
    
    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testMissingFields", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }
    
    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testInvalidLength", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getMissingTaskXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testMissingTask", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }

    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getInvalidHeadersXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testInvalidHeaders", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidHeaders test.");
        }
    }

    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testDynamicFields", template, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }
    
    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testInvalidDynamicFields", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }
    
    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkUploader uploader = new ActivityBulkUploader(config);
            final ActivityBulkUploadResult result = uploader.upload(true, "uploader-testLongDynamicFields", template, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }

}
