package qms.activity.bulk.util;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Locale;
import mx.bnext.access.Module;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.dto.ActivityDTO;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.dto.DisabledFields;
import qms.framework.dto.ConditionalFieldDTO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.DataExchangeParseError;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;

@ExtendWith(MockitoExtension.class)
public class ActivityConditionalHelperTest {

    private static Locale locale;
    private static ActivityBulkConfig config;

    @BeforeAll
    public static void setup() throws URISyntaxException, IOException, DataExchangeParseError {
        locale = new Locale("es", "MX");
        final LoggedUser admin = new LoggedUser(new UserRef(10L));
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testFields() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            assertNotNull(helper.getFields(), "Fields must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testFields test.");
        }
    }

    @Test
    public void testEnabledFields() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            final DisabledFields disabled = ActivityConditionalHelperUtils.getDisabledInstance(false);
            final List<ConditionalFieldDTO> fields = ActivityConditionalHelperUtils.getAllFields();
            fields.stream().forEach((field) -> {
                final Boolean enabled = helper.isFieldEnabled(field, disabled);
                assertNotNull(enabled, "Field " + field.getFieldName() + " enabled must not be null");
                assertTrue(enabled, "Field " + field.getFieldName() + " must be enabled");
            });
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEnabledFields test.");
        }
    }

    @Test
    public void testDisabledFields() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            final DisabledFields disabled = ActivityConditionalHelperUtils.getDisabledInstance(true);
            final List<ConditionalFieldDTO> fields = ActivityConditionalHelperUtils.getAllFields();
            fields.stream().forEach((field) -> {
                final Boolean enabled = helper.isFieldEnabled(field, disabled);
                assertNotNull(enabled, "Field " + field.getFieldName() + " enabled must not be null");
                assertFalse(enabled, "Field " + field.getFieldName() + " must be disabled");
            });
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDisabledFields test.");
        }
    }

    @Test
    public void testParseActivityValue() throws URISyntaxException, IOException, ActivityBulkFileError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            final List<ConditionalFieldDTO> fields = ActivityConditionalHelperUtils.getAllFields();
            final ActivityDTO activity = ActivityConditionalHelperUtils.defaultActivityInstance();
            fields.stream().forEach((field) -> {
                final Object fieldValue = ActivityConditionalHelperUtils.getActivityValue(field);
                final Object activityValue = helper.parseActivityValue(field, activity);
                assertNotNull(activityValue, "Field " + field.getFieldName() + " value must not be null");
                assertEquals(activityValue, fieldValue, "Activity field " + field.getFieldName() + " value must equals to field value");
            });
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDisabledFields test.");
        }
    }

    @Test
    public void testParseFieldValue() throws URISyntaxException, IOException, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            final List<ConditionalFieldDTO> fields = ActivityConditionalHelperUtils.getAllFields();
            final ActivityDataSourceDto typeData = config.getActivityDao().getNewActivityDataSourceByType(Module.ACTIVITY, -1L, -1L, null, null, null);
            fields.stream().forEach((field) -> {
                final Object value = helper.parseFieldValue(field, typeData, config.getTimesheetDataSource());
                assertNotNull(value, "Field " + field.getFieldName() + " value must not be null");
            });
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDisabledFields test.");
        }
    }

    @Test
    public void testUpdateValue() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityConditionalHelper helper = new ActivityConditionalHelper();
            final List<ConditionalFieldDTO> fields = ActivityConditionalHelperUtils.getAllFields();
            final ActivityDTO activity = ActivityConditionalHelperUtils.defaultActivityInstance();
            fields.stream().forEach((field) -> {
                final Object fieldValue = ActivityConditionalHelperUtils.getActivityValue(field);
                final Boolean updated = helper.updateValue(activity, field, fieldValue);
                assertNotNull(updated, "Field " + field.getFieldName() + " value must not be updated");
                final Object activityValue = helper.parseActivityValue(field, activity);
                assertEquals(fieldValue, activityValue, "Field " + field.getFieldName() + " value must be updated");
            });
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDisabledFields test.");
        }
    }

}
